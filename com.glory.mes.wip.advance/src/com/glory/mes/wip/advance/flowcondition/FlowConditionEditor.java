package com.glory.mes.wip.advance.flowcondition;

import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.consumable.model.ConsumableEqp;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.FlowCondition;
import com.glory.mes.prd.model.FlowConditionDetail;
import com.glory.framework.core.exception.ExceptionBundle;

public class FlowConditionEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.flowcondition.FlowConditionEditor";

	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected MPart mPart;

	protected SashForm sashForm;

	private QueryTableForm queryForm;
	private FlowConditionSection section;

	@PostConstruct
	public void postConstruct(Composite parent) {
		// 创建查询form
		ADTable adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		createQueryForm(parent, adTable, new FMessageManager());

		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);

		ManagedForm mform = new ManagedForm(toolkit, form);
		
		Composite body = form.getBody();
		configureBody(body);

		queryForm.getTableManager().addSelectionChangedListener(new ISelectionChangedListener() {

			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Object object = selection.getFirstElement();
				if (object instanceof FlowCondition) {
					FlowCondition flowCondition = (FlowCondition) object;
					
					//获取明细
					flowCondition = getFlowCondition(flowCondition);
					
					section.setAdObject(flowCondition);
					section.refresh();
				}
			}
		});

		GridData gd = new GridData(GridData.FILL_BOTH);
//		gd.heightHint = 440;
		queryForm.setLayoutData(gd);
		
		Composite sectionComposite = toolkit.createComposite(body, SWT.NULL);
		GridLayout layout = new GridLayout(1, false);
		sectionComposite.setLayout(layout);
		sectionComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

		section = new FlowConditionSection(adTable);
		section.createContents(mform, sectionComposite);
		section.setQueryTableForm(queryForm);

	}

	public void createQueryForm(Composite parent, ADTable adTable, IMessageManager manager) {
		FormToolkit toolkit  = new FFormToolKit(parent.getDisplay());
		Section section = toolkit.createSection(parent, Section.DESCRIPTION | Section.NO_TITLE | FFormSection.FFORM);
		section.setText(Message.getString("wip.flow_condition_query"));
		section.marginWidth = 3;
	    section.marginHeight = 4;
	    
	    Composite client = toolkit.createComposite(section);    
	    GridLayout layout = new GridLayout();    
	    layout.numColumns = 1;    
	    client.setLayout(layout);
	    
	    GridData td = new GridData(GridData.FILL_BOTH);
	    client.setLayoutData(td);
		queryForm = new QueryTableForm(client, SWT.NONE, adTable, manager);
		GridData gridData = new GridData(GridData.FILL_BOTH);
		queryForm.setLayoutData(gridData);
		queryForm.setLayout(new GridLayout(1, false));
	    createToolBar(section);

	    section.setClient(client);	
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemRefresh(ToolBar tBar) {
		ToolItem itemRefresh = new ToolItem(tBar, SWT.PUSH);
		itemRefresh.setText(Message.getString(ExceptionBundle.bundle.CommonRefresh()));
		itemRefresh.setImage(SWTResourceCache.getImage("refresh"));
		itemRefresh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				refreshAdapter();
			}

			private void refreshAdapter() {
				queryForm.refresh();
			}
		});
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;

		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	private FlowCondition getFlowCondition(FlowCondition flowCondition) {
		try {
			//PrdManager prdManager = Framework.getService(PrdManager.class);
			//flowCondition = prdManager.getFlowCondition(flowCondition);
			
			ADManager adManager = Framework.getService(ADManager.class);
			//
			List<FlowConditionDetail> details = adManager.getEntityList(Env.getOrgRrn(), FlowConditionDetail.class, 
					Integer.MAX_VALUE, " flowConditionRrn = " + flowCondition.getObjectRrn(), "seqNo");
			flowCondition.setConditionDetails(details);
			return flowCondition;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}
}
