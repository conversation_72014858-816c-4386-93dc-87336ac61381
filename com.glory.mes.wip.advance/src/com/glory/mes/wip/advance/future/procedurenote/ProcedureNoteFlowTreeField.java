package com.glory.mes.wip.advance.future.procedurenote;

import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.mes.prd.procedure.flow.ProcedureFlowTreeField;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.future.FutureNote;

public class ProcedureNoteFlowTreeField extends ProcedureFlowTreeField {

	protected ProcedureNoteSection procedureNoteSection;

	public ProcedureNoteFlowTreeField(String id, String label, TreeViewerManager manager) {
		super(id, label, manager);
	}

	@Override
	protected void doSelect(MouseEvent e) {
		if (e.button == 1) {
			// 鼠标单击左键
			// 检查是否点到了具体的节点
			TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
			// 没有点到具体结点
			if (treeItem != null) {
				// 选中了某一节点
				// selection和TreeItem是一致的，交回TreeViewer进行处理
				IStructuredSelection selection = (IStructuredSelection) viewer.getSelection();
				Object obj = selection.getFirstElement();
				if (obj.equals(treeItem.getData())) {
					if (obj instanceof StepState) {
						procedureNoteSection.itemNote.setEnabled(true);
						procedureNoteSection.initAdObject();
						procedureNoteSection.setCurrentStepState((StepState) obj);
						procedureNoteSection.refresh();
					} else if (treeItem.getData() instanceof FutureNote) {
						procedureNoteSection.itemNote.setEnabled(true);
						FutureNote fn = (FutureNote) treeItem.getData();
						Object o = treeItem.getParentItem().getData();
						if (o instanceof StepState) {
							StepState step = ((StepState) o);
							if (procedureNoteSection != null) {
								procedureNoteSection.setCurrentStepState(step);
							}
						}
						procedureNoteSection.setAdObject(fn);
						procedureNoteSection.refresh();
					} else {
						procedureNoteSection.itemNote.setEnabled(false);
						procedureNoteSection.refresh();
					}
				}
			}
		}
	}

}
