package com.glory.mes.wip.advance.future.timer;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.core.util.CollectionUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.procedure.flow.ProcedureFlowTreeField;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureMultiStepTimer;
import com.glory.mes.wip.future.FutureStepTimer;

public class StepTimerTreeField extends ProcedureFlowTreeField {

    public StepTimerSection lotSection;
    protected List<ADBase> currentFlowList;

    public StepTimerTreeField(String id, String label, TreeViewerManager manager) {
        super(id, label, manager);
    }

    /**
     * 初始化右键菜单
     */
    protected void initMenu() {
        MenuManager mgr = new MenuManager();

        Action startAction = new StartStepStateAction();
        startAction.setText("Timer");
        startAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("startstep")));

        mgr.add(startAction);

        menu = mgr.createContextMenu(tree);
    }

    @Override
    protected void doSelect(MouseEvent e) {
        if (e.button == 3) {
            // 点击右键
            TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
            if (treeItem != null) {
                IStructuredSelection selection = (IStructuredSelection) viewer.getSelection();
                Object obj = selection.getFirstElement();
                if (obj.equals(treeItem.getData())) {// 如果右击的是选中的节点
                    this.currentSelectedItem = treeItem;
                    if (obj instanceof StepState) {
                        // 如果选中的是StepState节点
                        // 显示右键菜单
                        tree.setMenu(menu);
                        lotSection.itemTimer.setEnabled(true);
                        lotSection.setAdObject(new FutureStepTimer());
                        lotSection.refresh();
                    } else {
                        // 否则取消右键菜单
                        tree.setMenu(null);
                    }
                }
            } else {
                tree.setMenu(null);
            }
        } else if (e.button == 1) {
            // 点击左键
            TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
            FutureAction currentTimer = (FutureAction)lotSection.getAdObject();
            if (treeItem != null) {
                IStructuredSelection selection = (IStructuredSelection) viewer.getSelection();
                Object obj = selection.getFirstElement();
                if (obj.equals(treeItem.getData())) {// 如果右击的是选中的节点
                    if (obj instanceof StepState) {
                        // 如果选中的是StepState节点
                        lotSection.itemTimer.setEnabled(true);
                        if (currentTimer.getObjectRrn() != null) {
							lotSection.setAdObject(new FutureStepTimer());
							lotSection.refresh();
						}
                    } else if (treeItem.getData() instanceof FutureStepTimer) {
                        // 如果选中的是SamplingAction节点
                        // 重设CurrentStepState和SamplingAction信息
                        lotSection.itemTimer.setEnabled(true);
                        FutureStepTimer timer = (FutureStepTimer) treeItem.getData();
                        Object o = treeItem.getParentItem().getData();
                        if (o instanceof StepState) {
                            StepState step = ((StepState) o);
                            if (lotSection != null) {
                                lotSection.setCurrentStepState(step);
                            }
                        }
                        lotSection.setAdObject(timer);
                        lotSection.refresh();
                    } else {
                        lotSection.itemTimer.setEnabled(false);
                        if (currentTimer.getObjectRrn() != null) {
							lotSection.setAdObject(new FutureStepTimer());
						}
                        lotSection.refresh();
                    }
                } else {
                    lotSection.itemTimer.setEnabled(false);
                    if (currentTimer.getObjectRrn() != null) {
						lotSection.setAdObject(new FutureStepTimer());
					}
                    lotSection.refresh();
                }
            }
        }
    }

	public void loadFlowTreeByProcedure(Procedure procedure, String nodeName ,List<FutureStepTimer> futureStepTimers) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);		
			if (procedure != null) {

				List<Procedure> procedures = new ArrayList<Procedure>();
				procedure = (Procedure) prdManager.getProcessDefinition(procedure);
				procedures.add(procedure);
				manager.setInput(procedures);
			
				if (nodeName != null) {
					List<ADBase> processes = new ArrayList<ADBase>();
					processes.add(procedure);
					
					PrdQueryAction queryAction = PrdQueryAction.newIntance();
					queryAction.setCopyNode(true);
					List<Node> nodes = prdManager.getProcessDefinitionChildern(procedure, queryAction);
					nodes = nodes.stream().filter(s -> s.getName().equals(nodeName)).collect(Collectors.toList());
					
					processes.addAll(nodes);
					if (CollectionUtils.isNotEmpty(futureStepTimers)) {
						processes.addAll(futureStepTimers);
					}
					currentFlowList = (List)processes;
					setCurrentFlow(currentFlowList);	
				} else {
					viewer.expandToLevel(3);
				}
			} else {
				manager.setInput(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 设置当前节点
	 */
	public void setCurrentFlow(List<ADBase> currentFlowList) {
		if (currentFlowList != null && currentFlowList.size() > 0) {
			TreeSelection section = new TreeSelection(new TreePath(currentFlowList.toArray()));
			viewer.setSelection(section);

//			TreeItem[] items = tree.getSelection();
//			for (TreeItem item : items) {
//				item.setImage(SWTResourceCache.getImage("currentstep"));
//				while (item.getParentItem() != null) {
//					item = item.getParentItem();
//				}
//			}
		}
	}
	
}
