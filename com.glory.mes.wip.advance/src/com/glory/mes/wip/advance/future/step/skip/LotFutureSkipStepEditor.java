package com.glory.mes.wip.advance.future.step.skip;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.swt.widgets.TreeItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureSkip;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotFutureSkipStepEditor extends GlcEditor{
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.step.skip.LotFutureSkipStepEditor";
	
	private QueryFormField lotFutureSkipStepQueryFormField;
	private GlcFormField lotFutureSkipStepGlcFormField;
	private CustomField flowTreeCustomField;
	private EntityFormField lotFutureSkipStepEntityFormField;
	
	private FlowCustomComposite flowCustomComposite;
	private EntityForm lotFutureSkipStepForm;
	
	private static final String FIELD_QUERYFORM = "queryForm";
	private static final String FIELD_GLCFORM = "futureSkipGlcForm";
	private static final String FIELD_CUSTOMFORM = "futureSkipSetUp";
	private static final String FIELD_ENTITYFORM = "futureSkipInfo";
	
	public static final String FIELD_LOT_ID = "lotId";
	private static final String FIELD_STEP_NAME = "stepName";	
	private static final String FIELD_PATH = "path";	
	
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_DELETE = "delete";
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotFutureSkipStepQueryFormField = form.getFieldByControlId(FIELD_QUERYFORM, QueryFormField.class);
		
		lotFutureSkipStepGlcFormField = form.getFieldByControlId(FIELD_GLCFORM, GlcFormField.class);
		
		flowTreeCustomField = lotFutureSkipStepGlcFormField.getFieldByControlId(FIELD_CUSTOMFORM, CustomField.class);
		flowCustomComposite = (FlowCustomComposite) flowTreeCustomField.getCustomComposite();
		
		lotFutureSkipStepEntityFormField = lotFutureSkipStepGlcFormField.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		lotFutureSkipStepForm = (EntityForm) lotFutureSkipStepEntityFormField.getControls()[0];
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_QUERYFORM + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		subscribeAndExecute(eventBroker, flowTreeCustomField.getFullTopic(GlcEvent.EVENT_CLICK), this::flowClickAdapter);
		
		subscribeAndExecute(eventBroker, lotFutureSkipStepGlcFormField.getFullTopic(FIELD_CUSTOMFORM + GlcEvent.NAMESPACE_SEPERATOR + "lotId"  + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::enterPressedAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		
	}
	
	/**
	 * 查询列表选择事件
	 * @param obj
	 */
	private void selectionChanged(Object obj) {
		try {
			FutureSkip futureSkip = (FutureSkip) lotFutureSkipStepQueryFormField.getSelectedObject();
			if (futureSkip == null) {
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), futureSkip.getLotId(), true);
					
			flowCustomComposite.getTxtId().setText(futureSkip.getLotId());
			flowCustomComposite.loadFlowTreeByLot(currentLot);
			
			List<ADBase> processNodes = new ArrayList<ADBase>();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			ProcessDefinition pf = prdManager.getProcessInstance(currentLot.getProcessInstanceRrn()).getProcessDefinition();
			processNodes.add(pf);		
			List<StepState> nodeList = prdManager.getStepChildren(pf);
			List<Node> nodes = nodeList.stream().filter(s -> s.getName().equals(futureSkip.getStepStateName())).collect(Collectors.toList());
			processNodes.addAll(nodes);				
			TreeSelection section = new TreeSelection(new TreePath(processNodes.toArray()));		
			flowCustomComposite.getViewer().setSelection(section);
			
			lotFutureSkipStepEntityFormField.setValue(futureSkip);
			lotFutureSkipStepEntityFormField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void flowClickAdapter(Object obj) {		
		try {		
			Event event = (Event) obj; 
			TreeItem treeItem = (TreeItem) event.getProperty(GlcEvent.PROPERTY_DATA);	
			if (treeItem.getData() instanceof StepState) {
				StepState stepState = (StepState)treeItem.getData();
						
				TextField stepTextField = lotFutureSkipStepEntityFormField.getFieldByControlId(FIELD_STEP_NAME, TextField.class);
				stepTextField.setValue(stepState.getUsedStep().getName());
				stepTextField.refresh();		
				
				TextField pathTextField = lotFutureSkipStepEntityFormField.getFieldByControlId(FIELD_PATH, TextField.class);
				pathTextField.setValue(stepState.getPath());
				pathTextField.refresh();	
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	/**
	 * 批次框回车事件
	 * @param obj
	 */
	private void enterPressedAdapter(Object obj) {
		lotFutureSkipStepForm.getMessageManager().setAutoUpdate(false);
		lotFutureSkipStepForm.getMessageManager().removeAllMessages();
		try {
			String lotId = flowCustomComposite.getTxtId().getText();
			if (StringUtil.isEmpty(lotId)) {
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, true);
			flowCustomComposite.loadFlowTreeByLot(currentLot);
			
			FutureSkip futureSkip = new FutureSkip();
			futureSkip.setLotRrn(currentLot.getObjectRrn());
			futureSkip.setLotId(currentLot.getLotId());
			futureSkip.setPartName(currentLot.getPartName());
			futureSkip.setPartVersion(currentLot.getPartVersion());
			futureSkip.setProcessName(currentLot.getProcessName());
			futureSkip.setProcessVersion(currentLot.getProcessVersion());
//			futureSkip.setProcedureName(currentLot.getProcedureName());
//			futureSkip.setProcedureVersion(currentLot.getProcedureVersion());					
//			futureSkip.setStepName(currentLot.getStepName());	
			
			lotFutureSkipStepEntityFormField.setValue(futureSkip);
			lotFutureSkipStepEntityFormField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			lotFutureSkipStepForm.getMessageManager().setAutoUpdate(true);
		}	
	}
	
	private void newAdapter(Object obj) {
		refresh();
	}
	
	/**
	 * 保存方法
	 * @param obj
	 */
	private void saveAdapter(Object obj) {
		lotFutureSkipStepForm.getMessageManager().setAutoUpdate(false);
		lotFutureSkipStepForm.getMessageManager().removeAllMessages();
		try {
			if (lotFutureSkipStepEntityFormField.getValue() != null) {
				if (lotFutureSkipStepEntityFormField.validate()) {
					FutureSkip futureSkip = (com.glory.mes.wip.future.FutureSkip) lotFutureSkipStepEntityFormField.getValue();
					if (StringUtil.isEmpty(futureSkip.getLotId())) {
						return;
					}
					
					TreeSelection section = (TreeSelection)flowCustomComposite.getViewer().getSelection();
					if (section == null) {
						
					}
					StepState stepState = (StepState)section.getFirstElement();
					
					LotManager lotManager = Framework.getService(LotManager.class);
					Lot currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), futureSkip.getLotId(), true);
					
					futureSkip.setIsActive(true);
					futureSkip.setOrgRrn(Env.getOrgRrn());				
					futureSkip.setCreatedBy(Env.getUserName());
					futureSkip.setUpdatedBy(Env.getUserName());																															
					futureSkip.setPartName(currentLot.getPartName());
					futureSkip.setPartVersion(currentLot.getPartVersion());
					futureSkip.setProcessName(currentLot.getProcessName());
					futureSkip.setProcessVersion(currentLot.getProcessVersion());
					futureSkip.setProcedureName(stepState.getProcessDefinition().getName());
					futureSkip.setProcedureVersion(stepState.getProcessDefinition().getVersion());					
					futureSkip.setStepName(stepState.getUsedStep().getName());					
					futureSkip.setStepStateName(stepState.getName());
					futureSkip.setPath(stepState.getPath());
					futureSkip.setStepPlacement(FutureAction.STEPPLACEMENT_QUEUED);
					lotManager.saveFutureAction(futureSkip, Env.getSessionContext());	
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			lotFutureSkipStepForm.getMessageManager().setAutoUpdate(true);
		}
	}
	
	/**
	 * 删除方法
	 * @param obj
	 */
	private void deleteAdapter(Object obj) {
		try {
			
			FutureSkip futureSkip = (FutureSkip) lotFutureSkipStepQueryFormField.getSelectedObject();
			if (futureSkip == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction(futureSkip, Env.getSessionContext());	
				refresh();
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void refresh() {
		flowCustomComposite.getTxtId().setText("");
		flowCustomComposite.refresh();
		lotFutureSkipStepEntityFormField.setValue(new FutureSkip());
		lotFutureSkipStepEntityFormField.refresh();	
		lotFutureSkipStepQueryFormField.refresh();
	}
}
