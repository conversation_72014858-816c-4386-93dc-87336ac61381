package com.glory.mes.wip.advance.future.timer;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.apache.log4j.Logger;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.mes.wip.advance.future.timer.step.StepTimerProperties;

/**
 * 选择流程设置单工步定时器,工步定时器只对当前流程有效
 */
public class StepTimerEditor extends EntityEditor {
	private static final Logger logger = Logger.getLogger(StepTimerEditor.class);

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.timer.StepTimerEditor";

    private StepTimerSection section;
    
    @Inject
	protected ESelectionService selectionService;
	
	@Inject
	protected MPart mPart;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		try{
			ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
			adTable = StepTimerProperties.tableHandle(adTable);
			createSection(adTable);
			
			FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
			ScrolledForm form = toolkit.createScrolledForm(parent);
			//form.setLayoutData(new GridData(GridData.FILL_BOTH));
			ManagedForm mform = new ManagedForm(toolkit, form);
			
			Composite body = form.getBody();
			configureBody(body);
			section.createContents(mform, body);
		} catch (Exception e){
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	
	protected void createSection(ADTable adTable) {
		section = new StepTimerSection(adTable);
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
