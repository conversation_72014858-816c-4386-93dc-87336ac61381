package com.glory.mes.wip.advance.future.timer.step;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.SearchMultiField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureStepTimer;
import com.glory.mes.wip.future.FutureTimer;

public class StepTimerProperties extends EntityProperties {
	
	private RefTableField timerTypeField;
	private RefTableField timerActionField;
	private RefTableField holdCodeField;
	private SearchMultiField holdOwnerField;
	private TextField holdReasonField;
	
	@Override
	public void createContents(Composite parent) {
		super.createContents(parent);
		if (super.getField("timerType") instanceof RefTableField) {
			timerTypeField = (RefTableField) super.getField("timerType");
			timerTypeField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					try {
						String timerType = newValue == null ? "" : String.valueOf(newValue);
						
						if (StringUtils.isEmpty(timerType) || StringUtils.equals(timerType, FutureTimer.TIMERTYPE_MINIMAL)) {
							timerActionField.setValue(null);
							holdCodeField.setValue(null);
							holdOwnerField.setValue(null);
							holdReasonField.setText(null);
						} 
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			});
		}
		
		if (super.getField("timerAction") instanceof RefTableField) {
			timerActionField = (RefTableField) super.getField("timerAction");
			timerActionField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					try {
						String timerAction = newValue == null ? "" : String.valueOf(newValue);
						
						if (StringUtils.isEmpty(timerAction) || StringUtils.equals(timerAction, FutureTimer.ACTION_NOTE)) {
							holdCodeField.setValue(null);
							holdOwnerField.setValue(null);
							holdReasonField.setText(null);
						} 
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			});
		}
		
		if (super.getField("holdCode") instanceof RefTableField) {
			holdCodeField = (RefTableField) super.getField("holdCode");
		}
		
		if (super.getField("holdOwner") instanceof SearchMultiField) {
			holdOwnerField = (SearchMultiField) super.getField("holdOwner");
		}
		
		if (super.getField("holdReason") instanceof TextField) {
			holdReasonField = (TextField) super.getField("holdReason");
		}
		
	}
	
	@Override
	public void saveAdapter() {
		form.getMessageManager().removeAllMessages();
		try {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				for (IForm detailForm : getDetailForms()) {
					PropertyUtil.copyProperties(getAdObject(), detailForm
							.getObject(), detailForm.getCopyProperties());
				}
				
				FutureStepTimer futureTimer = (FutureStepTimer)getAdObject();
				if (futureTimer.getTimerDuration() == null || futureTimer.getTimerDuration() <= 0) {
					 UI.showError(Message.getString("wip.timer_duration_must_valid"));
					 return;
				}
				
				if (StringUtils.equals(futureTimer.getTimerType(), FutureTimer.TIMERTYPE_MAXIMAL) && StringUtils.isEmpty(futureTimer.getTimerAction())) {
					 UI.showError(Message.getString("wip.timer_action_not_found"));
					 return;
				}
				
				if (StringUtils.isNotEmpty(futureTimer.getTimerAction()) && !StringUtils.equals(futureTimer.getTimerAction(), FutureTimer.ACTION_NOTE) && (StringUtils.isEmpty(futureTimer.getHoldCode()) || StringUtils.isEmpty(futureTimer.getHoldOwner()))) {
					 UI.showError(Message.getString("wip.timer_hold_info_not_complete"));
					 return;
				}
				
				if (futureTimer.getObjectRrn() == null) {
					futureTimer.setCreated(new Date());
					futureTimer.setCreatedBy(Env.getUserName());
				}
				futureTimer.setOrgRrn(Env.getOrgRrn());
				futureTimer.setUpdatedBy(Env.getUserName());
				
				LotManager lotManager = Framework.getService(LotManager.class);
				FutureStepTimer newFutureTimer = (FutureStepTimer) lotManager.saveFutureAction(futureTimer, Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.timer_holdsuccessed"));
				setAdObject(newFutureTimer);
				refresh();
				if (futureTimer.getObjectRrn() == null) {
					getMasterParent().refreshAdd(newFutureTimer);
				} else {
					getMasterParent().refreshUpdate(newFutureTimer);
				}
				
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public static ADTable tableHandle(ADTable table) throws Exception {
		SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
		List<ADField> fields = table.getFields();
		Iterator<ADField> it = fields.iterator();
		while(it.hasNext()){
			ADField adField = it.next();
			if ("isComponentLevel".equals(adField.getName())) {
				// 如果没有设置这个参数为True 就移除
				if (!MesCfMod.isCompUseQTime(Env.getOrgRrn(), sysParamManager)) {
					it.remove();
				};
			}
		}
		table.setFields(fields);
		return table;
	}
	
}
