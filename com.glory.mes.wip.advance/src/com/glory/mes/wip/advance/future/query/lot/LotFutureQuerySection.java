package com.glory.mes.wip.advance.future.query.lot;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureChangeFlow;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.future.FutureMerge;
import com.glory.mes.wip.future.FutureNewPart;
import com.glory.mes.wip.future.FutureNewProcedure;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.future.FutureSkip;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotFutureQuerySection extends LotSection {
	
	private static final Logger logger = Logger.getLogger(LotFutureQuerySection.class);

	public HeaderText txtCount;

	protected LotFutureQueryForm futureForm;
	protected IMessageManager mmng;

	private ToolItem itemCancelNote;
	private ToolItem itemCancelHold;
	private ToolItem itemCancelMerge;
	private ToolItem itemCancelSkip;
	
//	private ToolItem itemCancelNewPart;
	private ToolItem itemCancelNewProcedure;
	private ToolItem itemCancelChangeFlow;
	
	private Button btnNote;
	private Button btnHold;
	private Button btnTimer;
	private Button btnMerge;
	private Button btnSkip;
	
//	private Button btnNewPart;
	private Button btnNewProcedure;
	private Button btnChangeFlow;
	
	private List<String> actionTypes;
	
    public LotFutureQuerySection() {
		super();
	}

	public LotFutureQuerySection(ADTable table) {
		super(table);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(5, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));

		txtLot.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		txtLot.setTextLimit(32);
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor("Black"));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();
					String maxCount =  txtCount.getText();
					if(mmng == null) {
						mmng = (IMessageManager) new FMessageManager();
					}
					if(StringUtil.isEmpty(maxCount)) {
						mmng.addMessage(Message.getString("wip.futuer_step_count") + "common.ismandatory", 
								String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), Message.getString("wip.futuer_step_count")), null,
								MsgType.MSG_ERROR.getIndex(), txtCount);
						return;
					} else {
						mmng.removeAllMessages();
					}
						
					
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					lot = searchLot(lotId);
					tLotId.selectAll();
					if (lot == null) {
						tLotId.setForeground(SWTResourceCache.getColor("Red"));
						try {
							setAdObject(createAdObject());
						} catch (Exception en) {
							logger.error("createADObject error at searchEntity Method!");
						}
						txtLot.warning();
					} else {
						lot.setAttribute5(maxCount);
						setAdObject(lot);
						txtLot.focusing();
					}
					refresh();
					break;
				}
			}

		});

		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});

		Label labelLot = toolkit.createLabel(top, Message.getString("wip.futuer_step_count") + "*");
		labelLot.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtCount = new HeaderText(top);
		GridData gText2 = new GridData();
		gText2.widthHint = 108;
		gText2.heightHint = 26;
		txtCount.setLayoutData(gText2);
		txtCount.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		txtCount.setTextLimit(32);
		txtCount.setText("100");
		txtCount.getTextControl().addVerifyListener(new VerifyListener() {   
			public void verifyText(VerifyEvent e) {   
				// 正整数验证   
				Pattern pattern = Pattern.compile("[0-9]\\d*");   
				Matcher matcher = pattern.matcher(e.text);   
				if (matcher.matches()) // 处理数字   
					e.doit = true;   
				else if (e.text.length() > 0) // 有字符情况,包含中文、空格   
					e.doit = false;   
				else  
					// 控制键   
					e.doit = true;
			}  
		});
		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(7, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL | GridData.FILL_BOTH);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
		
		btnNote = new Button(right, SWT.CHECK);
		btnNote.setText("FutureNote");
		btnNote.setSelection(true);
		
		btnHold = new Button(right, SWT.CHECK | SWT.LEFT);
		btnHold.setSelection(true);
		btnHold.setText("FutureHold");
		
		btnMerge = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnMerge.setText("FutureMerge");
		btnMerge.setSelection(true);
		
		btnSkip = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnSkip.setText("FutureSkip");
		btnSkip.setSelection(true);
		
//		btnNewPart = new Button(right, SWT.CHECK | SWT.RIGHT);
//		btnNewPart.setText("NewPart");
//		btnNewPart.setSelection(true);
		
		btnNewProcedure = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnNewProcedure.setText("NewProcedure");
		btnNewProcedure.setSelection(true);
		
		btnChangeFlow = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnChangeFlow.setText("ChangeFlow");
		btnChangeFlow.setSelection(true);
		
		btnTimer = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnTimer.setText("QTime");
		btnTimer.setSelection(true);
	}

	public List<String> getActionTypes() {
		actionTypes = new ArrayList<String>();
		if (this.btnNote.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_NOTE);
		}
		if (this.btnHold.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_HOLD);
		}
		if (this.btnTimer.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_TIMER);
		}
		if (this.btnMerge.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_MERGE);
		}
		if (this.btnSkip.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_SKIP);
		}
		
		if (this.btnChangeFlow.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_CHANGEFLOW);
		}
		if (this.btnNewProcedure.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_NEWPRCD);
		}
//		if (this.btnNewPart.getSelection()) {
//			this.actionTypes.add(FutureAction.ACTION_NEWPART);
//		}
		return actionTypes;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNote(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemMerge(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSkip(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemNewPart(tBar);
		createToolItemNewProcedure(tBar);
		createToolItemChangeFlow(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
//	private void createToolItemNewPart(ToolBar tBar) {
//		//itemCancelNote = new AuthorityToolItem(tBar, SWT.PUSH , "wipadv.cancel_note");
//		itemCancelNewPart = new AuthorityToolItem(tBar, SWT.PUSH , "WipAdv.LotFutureActionQuery.wipadv.cancel_npart");
//		itemCancelNewPart.setText("CancelNewPart");
//		itemCancelNewPart.setImage(SWTResourceCache.getImage("future_new_part"));
//		itemCancelNewPart.setEnabled(false);
//		itemCancelNewPart.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelnpart"))){
//					cancelNewPartAdapter();
//				}
//			}
//		});
//	}
	
	protected void cancelNewPartAdapter() {
		try {
			if (futureForm.getSelectedObject() != null &&
					futureForm.getSelectedObject() instanceof FutureNewPart){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureNewPart)futureForm.getSelectedObject(), Env.getSessionContext());
				futureForm.refreshDelete(futureForm.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void createToolItemNewProcedure(ToolBar tBar) {
		//itemCancelNote = new AuthorityToolItem(tBar, SWT.PUSH , "wipadv.cancel_note");
		itemCancelNewProcedure = new AuthorityToolItem(tBar, SWT.PUSH , "WipAdv.LotFutureActionQuery.wipadv.cancel_nprocedure");
		itemCancelNewProcedure.setText("CancelNewProcedure");
		itemCancelNewProcedure.setImage(SWTResourceCache.getImage("future_new_procedure"));
		itemCancelNewProcedure.setEnabled(false);
		itemCancelNewProcedure.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelnprocedure"))){
					cancelNewProcedureAdapter();
				}
			}
		});
	}
	
	protected void cancelNewProcedureAdapter() {
		try {
			if (futureForm.getSelectedObject() != null &&
					futureForm.getSelectedObject() instanceof FutureNewProcedure){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureNewProcedure)futureForm.getSelectedObject(), Env.getSessionContext());
				futureForm.refreshDelete(futureForm.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void createToolItemChangeFlow(ToolBar tBar) {
		itemCancelChangeFlow = new AuthorityToolItem(tBar, SWT.PUSH , "WipAdv.LotFutureActionQuery.wipadv.cancel_changeflow");
		itemCancelChangeFlow.setText("CancelChangeFlow");
		itemCancelChangeFlow.setImage(SWTResourceCache.getImage("future_change_flow"));
		itemCancelChangeFlow.setEnabled(false);
		itemCancelChangeFlow.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelchangeflow"))){
					cancelChangeFlowAdapter();
				}
			}
		});
	}

	protected void cancelChangeFlowAdapter() {
		try {
			if (futureForm.getSelectedObject() != null &&
					futureForm.getSelectedObject() instanceof FutureChangeFlow){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureChangeFlow)futureForm.getSelectedObject(), Env.getSessionContext());
				futureForm.refreshDelete(futureForm.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void createToolItemNote(ToolBar tBar) {
		//itemCancelNote = new AuthorityToolItem(tBar, SWT.PUSH , "wipadv.cancel_note");
		itemCancelNote = new AuthorityToolItem(tBar, SWT.PUSH , "WipAdv.LotFutureActionQuery.wipadv.cancel_note");
		itemCancelNote.setText("CancelNote");
		itemCancelNote.setImage(SWTResourceCache.getImage("note"));
		itemCancelNote.setEnabled(false);
		itemCancelNote.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelnote"))){
					cancelNoteAdapter();
				}
			}
		});
	}

	protected void cancelNoteAdapter() {
		try {
			if (futureForm.getSelectedObject() != null &&
					futureForm.getSelectedObject() instanceof FutureNote){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureNote)futureForm.getSelectedObject(), Env.getSessionContext());
				futureForm.refreshDelete(futureForm.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * 只取消在Lot上设置的FutureHold(包括MergeHold)
	 */
	protected void createToolItemHold(ToolBar tBar) {
		//itemCancelHold = new AuthorityToolItem(tBar, SWT.PUSH, "wipadv.cancel_hold");
		itemCancelHold = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.LotFutureActionQuery.wipadv.cancel_hold");
		itemCancelHold.setText("CancelHold");
		itemCancelHold.setImage(SWTResourceCache.getImage("hold"));
		itemCancelHold.setEnabled(false);
		itemCancelHold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelhold"))){
					cancelHoldAdapter();
				}
			}
		});
	}
	
	protected void cancelHoldAdapter() {
		try {
			if (futureForm.getSelectedObject() != null &&
					futureForm.getSelectedObject() instanceof FutureHold){
				LotManager lotManager = Framework.getService(LotManager.class);
				FutureHold futureHold = (FutureHold)futureForm.getSelectedObject();
				if(futureHold.getRunCardId() != null) {
					UI.showInfo(Message.getString("wip.ein_future_hold_no_delete"));
					return;
				}
				lotManager.deleteFutureAction((FutureHold)futureForm.getSelectedObject(), Env.getSessionContext());
				futureForm.refreshDelete(futureForm.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 只取消在Lot上设置的FutureHold(包括MergeHold)
	 */
	protected void createToolItemMerge(ToolBar tBar) {
		//itemCancelMerge = new AuthorityToolItem(tBar, SWT.PUSH, "wipadv.cancel_merge");
		itemCancelMerge = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.LotFutureActionQuery.wipadv.cancel_merge");
		itemCancelMerge.setText("CancelMerge");
		itemCancelMerge.setImage(SWTResourceCache.getImage("merge"));
		itemCancelMerge.setEnabled(false);
		itemCancelMerge.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if (UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelmerge"))){
					cancelMergeAdapter();
				}
			}
		});
	}

	protected void cancelMergeAdapter() {
		try {
			if (futureForm.getSelectedObject() != null &&
					futureForm.getSelectedObject() instanceof FutureMerge){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureMerge)futureForm.getSelectedObject(), Env.getSessionContext());
				futureForm.refreshDelete(futureForm.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createToolItemSkip(ToolBar tBar) {
		itemCancelSkip = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.LotFutureActionQuery.wipadv.cancel_skip");
		itemCancelSkip.setText("CancelSkip");
		itemCancelSkip.setImage(SWTResourceCache.getImage("note"));
		itemCancelSkip.setEnabled(false);
		itemCancelSkip.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if (UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelskip"))){
					cancelSkipAdapter();
				}
			}
		});
	}

	protected void cancelSkipAdapter() {
		try {
			if (futureForm.getSelectedObject() != null &&
					futureForm.getSelectedObject() instanceof FutureSkip){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureSkip)futureForm.getSelectedObject(), Env.getSessionContext());
				futureForm.refreshDelete(futureForm.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		futureForm = new LotFutureQueryForm(this, composite, SWT.NONE, tab, mmng);
		return futureForm;
	}

	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
//			ADBase adBase = getAdObject();
//			if (adBase != null && adBase.getObjectRrn() != null) {
//				ADManager entityManager = Framework.getService(ADManager.class);
//				setAdObject(entityManager.getEntity(adBase));
//			}
			setAdObject(new Lot());
			txtLot.setText("");
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		refresh();
	}

	@Override
	public void refresh() {
		Lot lot = (Lot)this.getAdObject();
		futureForm.setLot(lot);
		super.refresh();
//		futureForm.refresh();
//		if (txtLot != null) {
//			txtLot.selectAll();
//		}
	}

	public void statusChanged(FutureAction action) {
		itemCancelNote.setEnabled(false);
		itemCancelHold.setEnabled(false);
		itemCancelMerge.setEnabled(false);
		itemCancelSkip.setEnabled(false);
//		itemCancelNewPart.setEnabled(false);
		itemCancelNewProcedure.setEnabled(false);
		itemCancelChangeFlow.setEnabled(false);
		
		if (action instanceof FutureNote){
			itemCancelNote.setEnabled(true);
		} else if(action instanceof FutureHold){
			//只针对LotFutureHold
			if (action.getLotRrn() != null) {
				itemCancelHold.setEnabled(true);
			}
		} else if(action instanceof FutureMerge){
			itemCancelMerge.setEnabled(true);
		} else if(action instanceof FutureSkip){
			itemCancelSkip.setEnabled(true);
		}/* else if (action instanceof FutureNewPart) {
			itemCancelNewPart.setEnabled(true);
		}*/
		 else if (action instanceof FutureNewProcedure) {
			itemCancelNewProcedure.setEnabled(true);
		} else if (action instanceof FutureChangeFlow) {
			itemCancelChangeFlow.setEnabled(true);
		}
	}
	
}