package com.glory.mes.wip.advance.future.timer;

import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchMultiField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.procedure.flow.ProcedureFlowSection;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureStepTimer;
import com.glory.mes.wip.future.FutureTimer;
import com.glory.framework.core.exception.ExceptionBundle;

public class StepTimerSection extends EntitySection {

    protected ToolItem itemTimer;

    // 当前选中的StepState
    private StepState currentStepState;

    private StepTimerTreeField timerField;
    private ProcedureFlowSection processSection;
    
    private RefTableField timerTypeField;
	private RefTableField timerActionField;
	private RefTableField holdCodeField;
	private SearchMultiField holdOwnerField;
	private TextField holdReasonField;

    public StepTimerSection(ADTable table) {
        super(table);
    }

    public void initAdObject() {
    	FutureStepTimer futureStepTimer = new FutureStepTimer();
    	futureStepTimer.setOrgRrn(Env.getOrgRrn());
        setAdObject(futureStepTimer);
        refresh();
    }

    protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		final IMessageManager mmng = form.getMessageManager();
		
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		
		MDSashForm sashForm = new MDSashForm(client, SWT.NULL);
		toolkit.adapt(sashForm, false, false);
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);
		StepTimerTreeManager ptm = new StepTimerTreeManager();
		timerField = new StepTimerTreeField(StepTimerTreeField.FIELD_ID, "", ptm);
		timerField.lotSection = this;
		processSection = new ProcedureFlowSection(table, timerField);
		processSection.createContents(form, sashForm);
		processSection.getProcedureIField().addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object sender, Object newValue) {
				initAdObject();
			}
		});
		
		for (ADTab tab : getTable().getTabs()) {
			EntityForm itemForm = new EntityForm(sashForm, SWT.NULL, tab, mmng);
			getDetailForms().add(itemForm);
		}		
		
		if (super.getField("timerType") instanceof RefTableField) {
			timerTypeField = (RefTableField) super.getField("timerType");
			timerTypeField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					try {
						String timerType = newValue == null ? "" : String.valueOf(newValue);
						
						if (StringUtils.isEmpty(timerType) || StringUtils.equals(timerType, FutureTimer.TIMERTYPE_MINIMAL)) {
							timerActionField.setValue(null);
							holdCodeField.setValue(null);
							holdOwnerField.setValue(null);
							holdReasonField.setText(null);
						} 
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			});
		}
		
		if (super.getField("timerAction") instanceof RefTableField) {
			timerActionField = (RefTableField) super.getField("timerAction");
			timerActionField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					try {
						String timerAction = newValue == null ? "" : String.valueOf(newValue);
						
						if (StringUtils.isEmpty(timerAction) || StringUtils.equals(timerAction, FutureTimer.ACTION_NOTE)) {
							holdCodeField.setValue(null);
							holdOwnerField.setValue(null);
							holdReasonField.setText(null);
						} 
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			});
		}
		
		if (super.getField("holdCode") instanceof RefTableField) {
			holdCodeField = (RefTableField) super.getField("holdCode");
		}
		
		if (super.getField("holdOwner") instanceof SearchMultiField) {
			holdOwnerField = (SearchMultiField) super.getField("holdOwner");
		}
		
		if (super.getField("holdReason") instanceof TextField) {
			holdReasonField = (TextField) super.getField("holdReason");
		}
		
		initAdObject();
	}

    @Override
	public void createToolBar(Section section) { 
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTimer(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
		
		toolItems.put(ADButtonDefault.BUTTON_NAME_NEW, itemNew);
		toolItems.put(ADButtonDefault.BUTTON_NAME_SAVE, itemTimer);
		toolItems.put(ADButtonDefault.BUTTON_NAME_DELETE, itemDelete);
		toolItems.put(ADButtonDefault.BUTTON_NAME_REFRESH, itemRefresh);
	}
	
	protected void createToolItemTimer(ToolBar tBar) {
		itemTimer = new ToolItem(tBar, SWT.PUSH);
		itemTimer.setText(Message.getString("wip.timer")); 
		itemTimer.setImage(SWTResourceCache.getImage("timer_end"));
		itemTimer.setEnabled(false);
		itemTimer.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				timerAdapter();
			}
		});
	}

    protected void createToolItemDelete(ToolBar tBar) {
        super.createToolItemDelete(tBar);
    }
    
    protected void timerAdapter() {
		form.getMessageManager().removeAllMessages();
		try {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				for (IForm detailForm : getDetailForms()) {
					PropertyUtil.copyProperties(getAdObject(), detailForm
							.getObject(), detailForm.getCopyProperties());
				}
				
				FutureStepTimer futureTimer = (FutureStepTimer)getAdObject();
				if (futureTimer.getTimerDuration() == null || futureTimer.getTimerDuration() <= 0) {
					 UI.showError(Message.getString("wip.timer_duration_must_valid"));
					 return;
				}
				
				if (StringUtils.equals(futureTimer.getTimerType(), FutureTimer.TIMERTYPE_MAXIMAL) && StringUtils.isEmpty(futureTimer.getTimerAction())) {
					 UI.showError(Message.getString("wip.timer_action_not_found"));
					 return;
				}
				
				if (StringUtils.isNotEmpty(futureTimer.getTimerAction()) && !StringUtils.equals(futureTimer.getTimerAction(), FutureTimer.ACTION_NOTE) && (StringUtils.isEmpty(futureTimer.getHoldCode()) || StringUtils.isEmpty(futureTimer.getHoldOwner()))) {
					 UI.showError(Message.getString("wip.timer_hold_info_not_complete"));
					 return;
				}
				
				if (futureTimer.getObjectRrn() == null) {
					if (timerField == null || timerField.getStepStart() == null) {
						UI.showError(Message.getString("wip.timer_start_must"));
						return;
					}
					
					currentStepState = timerField.getStepStart();
					
					futureTimer.setOrgRrn(Env.getOrgRrn());
					futureTimer.setCreated(new Date());
					futureTimer.setCreatedBy(Env.getUserName());
					futureTimer.setUpdatedBy(Env.getUserName());
					
					futureTimer.setStepName(currentStepState.getStepName());
					futureTimer.setStepVersion(currentStepState.getStepVersion());
					futureTimer.setStepStateName(currentStepState.getName());
					futureTimer.setPath(currentStepState.getPath());							
					futureTimer.setProcedureName(currentStepState.getProcessDefinition().getName());
//					futureTimer.setProcedureVersion(currentStepState.getProcessDefinition().getVersion());
				}
				
				LotManager lotManager = Framework.getService(LotManager.class);
				futureTimer = (FutureStepTimer)lotManager.saveFutureAction(futureTimer, Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.timer_holdsuccessed"));

				timerField.refresh();
				refresh();
				setAdObject(futureTimer);
				
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

    protected void deleteAdapter() {
		try {
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
				if (confirmDelete) {
					LotManager entityManager = Framework
							.getService(LotManager.class);
					entityManager.deleteFutureAction((FutureAction) getAdObject(), Env.getSessionContext());
					setAdObject(createAdObject());
					timerField.refresh();
					refresh();
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}

    protected void refreshAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            ADBase adBase = getAdObject();
            if (adBase != null && adBase.getObjectRrn() != null) {
                ADManager entityManager = Framework.getService(ADManager.class);
                setAdObject(entityManager.getEntity(adBase));
            }
        } catch (Exception e1) {
            ExceptionHandlerManager.asyncHandleException(e1);
            return;
        }
        refresh();
    }

    public StepState getCurrentStepState() {
        return currentStepState;
    }

    public void setCurrentStepState(StepState currentStepState) {
        this.currentStepState = currentStepState;
    }

    @Override
    public void refresh() {
        super.refresh();
    }

	public StepTimerTreeField getTimerField() {
		return timerField;
	}

	public void setTimerField(StepTimerTreeField timerField) {
		this.timerField = timerField;
	}

	public ProcedureFlowSection getProcessSection() {
		return processSection;
	}

	public void setProcessSection(ProcedureFlowSection processSection) {
		this.processSection = processSection;
	}
}