package com.glory.mes.wip.advance.future.flowaction;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseAdapter;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.IfState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.lot.flow.LotFlowSection;

public class FutureFlowActionLotFlowSection extends LotFlowSection {

	private Menu menu;
	
	private ReturnAction returnToAction;
	private StartAction selectAction;
	private StartReturnAction startReturnAction;
	
	private TreeItem returnItem;
	private TreeItem startItem;
	
	private StepState returnStep;
	private StepState startStep;
	
	private FutureFlowActionSection actionSection;

	public FutureFlowActionLotFlowSection(ADTable table, LotMediator lotMediator, TreeViewerManager treeManager,
			FutureFlowActionSection actionSection) {
		super(table, lotMediator, treeManager);
		this.actionSection = actionSection;
	}

	public void addTreeViewerMouseListener() {
		lotMediator.addTreeViewerMouseListener(new MouseAdapter() {
			@Override
			public void mouseDown(MouseEvent e) {
				if (e.button == 3) {
					initMenu();
					Tree tree = (Tree) e.widget;
					TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
					// 鼠标单击右键，3键鼠标
					// 检查是否点到了具体的节点
					// 没有点到具体结点
					if (treeItem != null) {
						// 选中了某一节点
						Object obj = treeItem.getData();
						// 如果右击的是选中的节点
						if (obj != null) {
							// 判断是否是未来工步
							boolean isFutureNode = isFutureNode();
							if (!isFutureNode) {
								tree.setMenu(null);
								return;
							}
							// 判断是否已经设置未来Flow动作
							if (isAlreadySet()) {
								tree.setMenu(null);
								return;
							}
							
							// 添加右键菜单
							if (obj instanceof StepState || obj instanceof IfState) {
								//if (actionSection.isChangeFlow() || actionSection.isNewPart()) {
									// 判断是否是当前流程、普通工步
									if (isCurrentProcedure() || isNormalNode()) {
										tree.setMenu(menu);
									} else {
										tree.setMenu(null);
									}
								//}
								
								/*if (actionSection.isNewProcedure()) {
									// 判断是否是当前流程
									if (isCurrentProcedure()) {
										tree.setMenu(menu);
									} else {
										tree.setMenu(null);
									}
								}*/
							} else {
								tree.setMenu(null);
							}
						}
					} else {
						// 没有节点被选中
						tree.setMenu(null);
					}
				}
			}
		});
	}

	private void initMenu() {
		createActions();
		MenuManager mgr = new MenuManager();
		mgr.add(selectAction);
		if (!actionSection.isNewPart()) {
			mgr.add(returnToAction);
			if (actionSection.isNewProcedure()) {
				mgr.add(startReturnAction);
			}
		}
		menu = mgr.createContextMenu(lotMediator.getLotFlowTreeField().getTreeViewer().getTree());
	}

	private void createActions() {
		if (selectAction == null) {
			selectAction = new StartAction();
			selectAction.setText("Start");
			selectAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("start")));
		}
		
		if (returnToAction == null) {
			returnToAction = new ReturnAction();
			returnToAction.setText("Return");
			returnToAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("returnto")));
		}
		
		if (startReturnAction == null) {
			startReturnAction = new StartReturnAction();
			startReturnAction.setText("StartAndReturn");
			startReturnAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("step")));
		}
	}
	
	/**
	 * 是否已经设置未来流程变更
	 * @return
	 */
	public boolean isAlreadySet() {
		try {
			TreeItem selectedItem = getTreeSelection();
			if (selectedItem != null) {
				Object select = selectedItem.getData();
				if (select instanceof StepState) {
					StepState compareToState = (StepState) select;
					LotManager lotManager = Framework.getService(LotManager.class);
					List<FutureAction> flowActions = lotManager.getLotFutureFlowAction(
							compareToState, actionSection.getCurrentLot(), null);
					return CollectionUtils.isNotEmpty(flowActions);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}

	/**
	 * 是否未来流程
	 * @return
	 */
	public boolean isFutureNode() {
		try {
			TreeItem selectedItem = getTreeSelection();
			if (selectedItem != null) {
				Object select = selectedItem.getData();
				if (select instanceof StepState) {
					StepState compareToState = (StepState) select;
					PrdManager lotManager = Framework.getService(PrdManager.class);
					int i = lotManager.compareToCurrentStep(
							actionSection.getCurrentLot().getProcessInstanceRrn(), compareToState);
					return i > 0;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}

	/**
	 * 判断选中节点是否为当前流程节点
	 */
	public boolean isCurrentProcedure() {
		TreeItem selectedItem = getTreeSelection();
		List<ADBase> flowList = lotMediator.getLotFlowTreeField().getFlowList();
		if (flowList != null && selectedItem != null) {
			Object[] nodes = flowList.toArray();
			// 倒数第二层的当前流程节点
			Node node1 = (Node) nodes[nodes.length - 2];
			if (selectedItem.getParentItem() != null) {
				Node selectedParentNode = (Node) selectedItem.getParentItem().getData();
				if (node1.equals(selectedParentNode)) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 判断选中节点是否为正常流程节点(非返工流程中)
	 */
	public boolean isNormalNode() {
		TreeItem selectedItem = getTreeSelection();
		if (selectedItem != null && selectedItem.getParentItem() != null) {
			Node selectedParentNode = (Node) selectedItem.getParentItem().getData();
			if (!(selectedParentNode instanceof ReworkState)) {
				return true;
			}
		}
		return false;
	}
	
	private TreeItem getTreeSelection() {
		TreeItem selectedItem = null;
		TreeItem[] items = lotMediator.getLotFlowTreeField().getTreeViewer().getTree().getSelection();
		if (items != null && items.length > 0) {
			selectedItem = items[0];
		}
		return selectedItem;
	}
	
	@Override
	public void excuteSearch() {
		super.excuteSearch();
		actionSection.selectionChange();
	}
	
	public TreeItem getOldSelectedItem() {
		return startItem;
	}

	public StepState getStartStep() {
		return startStep;
	}

	public StepState getReturnStep() {
		return returnStep;
	}
	
	public void setReturnStep(StepState returnStep) {
		this.returnStep = returnStep;
	}

	public void setStartStep(StepState selectedStep) {
		this.startStep = selectedStep;
		actionSection.selectionChange();
	}

	public void refreshSelection() {
		if (startItem != null) {
			lotMediator.getLotFlowTreeField().refreshViewerNode(getStartStep());
			startItem.setExpanded(true);
			lotMediator.getLotFlowTreeField().refreshViewerNode(getStartStep());
		}
		clearSelections();
	}
	
	public void clearSelections() {
		if (returnItem != null && !returnItem.isDisposed())
			returnItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		if (startItem != null && !startItem.isDisposed())
			startItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		returnItem = null;
		startItem = null;
		setStartStep(null);
		setReturnStep(null);
	}
	
	class ReturnAction extends Action {

		@Override
		public void run() {
			TreeItem currentItem = getTreeSelection();
			if (currentItem != null && currentItem.getData() != null) {
				// 未来改流程开始、返回工步不能相同
				if (actionSection.isChangeFlow()) {
					if (startItem != null && !startItem.isDisposed()) {
						if (currentItem.equals(startItem)) {
							UI.showInfo(Message.getString("wipadv.future_flow_action_same_step_error"));
							return;
						}
					}
				}
				
				// 当前是StartAndReturn，重新设置Return时，要把Start清掉
				boolean sameFlag = false;
				if (returnItem != null && !returnItem.isDisposed() && startItem != null && !startItem.isDisposed()) {
					sameFlag = returnItem.equals(startItem);
				}
				
				// 清空原有选中
				if (returnItem != null && !returnItem.isDisposed()) {
					returnItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				}
				
				// 选中选择的节点
				currentItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_YELLOW));
				
				// 更新数据
				StepState stepState = (StepState) currentItem.getData();
				setReturnStep(stepState);
				returnItem = currentItem;
				
				if (returnItem.equals(startItem)) {
					// 选中选择的节点
					currentItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_DARK_YELLOW));
				} else if (sameFlag) {
					startItem = null;
					setStartStep(null);
				}
			}
		}
	}
	
	class StartAction extends Action {

		@Override
		public void run() {
			TreeItem currentItem = getTreeSelection();
			if (currentItem != null && currentItem.getData() != null) {
				// 未来改流程开始、返回工步不能相同
				if (actionSection.isChangeFlow()) {
					if (returnItem != null && !returnItem.isDisposed()) {
						if (currentItem.equals(returnItem)) {
							UI.showInfo(Message.getString("wipadv.future_flow_action_same_step_error"));
							return;
						}
					}
				}
				
				// 当前是StartAndReturn，重新设置Start时，要把Return清掉
				boolean sameFlag = false;
				if (returnItem != null && !returnItem.isDisposed() && startItem != null && !startItem.isDisposed()) {
					sameFlag = returnItem.equals(startItem);
				}
				
				// 清空原有选中
				if (startItem != null && !startItem.isDisposed()) {
					startItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				}
				
				// 选中选择的节点
				currentItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_GREEN));
				
				// 更新数据
				StepState stepState = (StepState) currentItem.getData();
				setStartStep(stepState);
				startItem = currentItem;
				
				if (startItem.equals(returnItem)) {
					// 选中选择的节点
					currentItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_DARK_YELLOW));
				} else if (sameFlag) {
					returnItem = null;
					setReturnStep(null);
				}
			}
		}
	}
	
	class StartReturnAction extends Action {

		@Override
		public void run() {
			TreeItem currentItem = getTreeSelection();
			if (currentItem != null && currentItem.getData() != null) {
				// 清空原有选中
				if (startItem != null && !startItem.isDisposed()) {
					startItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				}
				if (returnItem != null && !returnItem.isDisposed()) {
					returnItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				}
				// 选中选择的节点
				currentItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_DARK_YELLOW));
				
				// 更新数据
				StepState stepState = (StepState) currentItem.getData();
				setStartStep(stepState);
				setReturnStep(stepState);
				startItem = currentItem;
				returnItem = currentItem;
			}
		}
	}
	
}