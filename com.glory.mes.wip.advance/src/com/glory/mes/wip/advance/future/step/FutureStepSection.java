package com.glory.mes.wip.advance.future.step;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.ui.custom.InfiniteProgressPanel;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.FutureQueryManager;
import com.glory.mes.wip.client.LotHistoryManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.lot.LotListSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;


public class FutureStepSection extends LotListSection {
	
	private static final Logger logger = Logger.getLogger(FutureStepSection.class);
	
	protected ManagedForm managedForm;
    public HeaderText txtLot;
	public ADBase adObject;
	
	private Button btnS;
	
	private ToolItem futureEqp;
	
	public FutureStepSection(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemFutureEqp(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemExport(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemFutureEqp(ToolBar tBar) {
		futureEqp = new ToolItem(tBar, SWT.PUSH);
		futureEqp.setText(Message.getString("wip.adv_available_eqp"));
		futureEqp.setImage(SWTResourceCache.getImage("eqp"));
		futureEqp.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				futureEqpAdapter();
			}
		});
	}
	
	protected void futureEqpAdapter() {
		try {
			// 获取最近的20个工步
			List<? extends Object> objects = tableManager.getInput();
			if (CollectionUtils.isNotEmpty(objects)) {
				// 过滤历史工步
				boolean isAfterCurrent = false;
				List<Lot> queryList = Lists.newArrayList();
				
				long j = 1;
				for (Object obj : objects) {
					Lot lot = (Lot) obj;
					if ("Y".equals(lot.getAttribute1())) {
						isAfterCurrent = true;
					}
					
					if (isAfterCurrent) {
						Lot queryLot = (Lot) lot.clone();
						// 给lot设置ObjectRrn，方便后面排序
						queryLot.setObjectRrn(j++);
						queryList.add(queryLot);
					}
				}
				
				
				int querySize = 20;
				if (queryList.size() < 20) {
					querySize = queryList.size();
				}
				
				List<Lot> displayList = Lists.newArrayList();
				if (querySize < queryList.size()) {
					for (int i = 0; i < querySize; i++) {
						displayList.add(queryList.get(i));
					}
				} else {
					displayList.addAll(queryList);
				}
				
				FutureQueryManager queryManager = Framework.getService(FutureQueryManager.class);
				Map<Lot, List<Equipment>> results = queryManager.getAvailableEquipmentsByFutureSteps(
						displayList, null, true, true, Env.getSessionContext());
				
				List<Lot> lots = Lists.newArrayList();
				// 按照原有的顺序显示
				for (Lot lot : displayList) {
					for (Lot disLot : results.keySet()) {
						if (lot.equals(disLot)) {
							
							List<Equipment> equipments = results.get(disLot);
							equipments = equipments.stream()
									.sorted(Comparator.comparing(Equipment::getIsAvailable).reversed())
									.collect(Collectors.toList());
									
							equipments.forEach(e -> {
								if (!StringUtil.isEmpty(e.getMessage())) {
									e.setMessage(Message.formatString(e.getMessage()));
								}
							});
							
							lot.setAttribute2(equipments);
							lots.add(lot);
							break;
						}
					}
				}
				
				FutrueEquipmentDialog dialog = new FutrueEquipmentDialog(lots);
				dialog.open();
			}
		} catch (Exception e) {
			logger.error("FutureStepSection.futureEqpAdapter() error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		top.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
		Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
		txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
		
		btnS = new Button(top, SWT.CHECK | SWT.LEFT);
		btnS.setSelection(false);
		btnS.setText(Message.getString("wip.future_show_history_process"));
		
		txtLot.setTextLimit(32);
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					lot = searchLot(lotId);
					tLotId.selectAll();
					if (lot == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
	        				setAdObject(new Lot());		        			
	        			} catch(Exception en) {
	        				logger.error("createADObject error at searchEntity Method!");
	        			}
						txtLot.warning();
					} else {
						setAdObject(lot);
						txtLot.focusing();
					}
					if (btnS.getSelection()) {						
						showAllRefresh();
					}else {
						refresh();
					}
					break;
				}
			}

		});
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});
	}		
	
	public void showAllRefresh(){
		final InfiniteProgressPanel waitPanel = InfiniteProgressPanel
				.getInfiniteProgressPanelFor(UI.getActiveShell());
		waitPanel.setText("");
		waitPanel.setTextColor(UI.getActiveShell().getDisplay().getSystemColor(SWT.COLOR_DARK_RED));
		waitPanel.start();
		final Thread thread = new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					Lot lot = (Lot) getAdObject() ;
					if (lot != null && lot.getObjectRrn() != null) {
						LotHistoryManager lotHistoryManager = Framework.getService(LotHistoryManager.class);
						List<LotHis> lotHiss = lotHistoryManager.getLotHisByLotId(Env.getOrgRrn(), lot.getLotId(), " transType = 'TRACKOUT'");
						LotManager manager = Framework.getService(LotManager.class);
						PrdManager prdManager = Framework.getService(PrdManager.class);
						Map<String, Object> lotParamMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
						List<Lot> currentList = manager.getLotFutureSteps((Lot)getAdObject(), lotParamMap, false, true, false, 0, true);
						Display.getDefault().asyncExec(new Runnable() {
							@Override
							public void run() {
								List<Lot> adList = new ArrayList<Lot>();
								Collections.reverse(lotHiss);
								for (LotHis lotHis : lotHiss) {
									Lot tempLot = new Lot();
									//添加界面显示属性栏位
									tempLot.setLotId(lotHis.getLotId());
									tempLot.setPartVersion(lotHis.getPartVersion());
									tempLot.setPartName(lotHis.getPartName());
									tempLot.setStepDesc(lotHis.getStepDesc());
									tempLot.setRecipeName(lotHis.getRecipeName());
									tempLot.setMask(lotHis.getMask());
									tempLot.setTrackInTime(lotHis.getTrackInTime());
									tempLot.setTrackOutTime(lotHis.getTrackOutTime());
									tempLot.setStepVersion(lotHis.getStepVersion());
									tempLot.setStepName(lotHis.getStepName());
									tempLot.setProcessVersion(lotHis.getProcessVersion());
									tempLot.setProcessName(lotHis.getProcessName());
									tempLot.setProcedureVersion(lotHis.getProcedureVersion());
									tempLot.setProcedureName(lotHis.getProcedureName());
									//添加已经执行过的流程
									adList.add(tempLot);						
								}
								
								for (int i= 0 ; i < currentList.size(); i++) {
									if (i == 0) {
										currentList.get(i).setAttribute1("Y");
										if (!LotStateMachine.STATE_RUN.equals(lot.getState())) {
											currentList.get(i).setTrackInTime(null);	
										}
									} else {
										currentList.get(i).setTrackInTime(null);	
									}
									currentList.get(i).setTrackInTime(null);
									currentList.get(i).setTrackOutTime(null);
									
									//添加显示设备能力
									StepState stepState = (StepState) currentList.get(i).getData("STEP");
									currentList.get(i).setAttribute2(stepState.getUsedStep().getCapabilityName());
								}
								adList.addAll(currentList);
								
								tableManager.setInput(adList);
							}
						});
					}
				} catch(Exception e) {
					logger.error("Error at showAllRefresh ", e);
					ExceptionHandlerManager.asyncHandleException(e);
				} finally {
					waitPanel.stop();
				}
			}
		});
		thread.start();
	}

	public void refresh() {
		final InfiniteProgressPanel waitPanel = InfiniteProgressPanel
				.getInfiniteProgressPanelFor(UI.getActiveShell());
		waitPanel.setText("");
		waitPanel.setTextColor(UI.getActiveShell().getDisplay().getSystemColor(SWT.COLOR_DARK_RED));
		waitPanel.start();
		final Thread thread = new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					Lot lot = (Lot) getAdObject() ;
					if (lot != null && lot.getObjectRrn() != null) {
						LotManager manager = Framework.getService(LotManager.class);
						PrdManager prdManager = Framework.getService(PrdManager.class);
						Map<String, Object> lotParamMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
						List<Lot> currentList = manager.getLotFutureSteps((Lot)getAdObject(), lotParamMap, false, true, false, 0, true);
						Display.getDefault().asyncExec(new Runnable() {
							@Override
							public void run() {
								List<Lot> adList = new ArrayList<Lot>();
								for (int i= 0 ; i < currentList.size(); i++) {
									if (i == 0) {
										currentList.get(i).setAttribute1("Y");
										if (!LotStateMachine.STATE_RUN.equals(lot.getState())) {
											currentList.get(i).setTrackInTime(null);	
										}
									} else {
										currentList.get(i).setTrackInTime(null);
									}
									currentList.get(i).setTrackInTime(null);
									currentList.get(i).setTrackOutTime(null);
									
									//添加显示设备能力
									StepState stepState = (StepState) currentList.get(i).getData("STEP");
									currentList.get(i).setAttribute2(stepState.getUsedStep().getCapabilityName());
								}
								adList.addAll(currentList);
								
								tableManager.setInput(adList);
							}
						});
					}
				} catch(Exception e) {
					logger.error("Error at showAllRefresh ", e);
					ExceptionHandlerManager.asyncHandleException(e);
				} finally {
					waitPanel.stop();
				}
			}
		});
		thread.start();
	}
	
}
