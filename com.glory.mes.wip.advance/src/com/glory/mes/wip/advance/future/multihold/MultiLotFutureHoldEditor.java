package com.glory.mes.wip.advance.future.multihold;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.LotReserved;

public class MultiLotFutureHoldEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.multihold.MultiLotFutureHoldEditor";
											
	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected MPart mPart;

	protected SashForm sashForm;

	private QueryTableForm queryForm;
	private MultiLotFutureHoldSection section;
	private static final String TABLE_NAME = "WIPNewPart";
	public CheckBoxTableViewerManager tableManager;

	@PostConstruct
	public void postConstruct(Composite parent) {
		ADTable adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
	
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);

		ManagedForm mform = new ManagedForm(toolkit, form);

		Composite body = form.getBody();
		configureBody(body);

		// 创建查询form
		tableManager = new CheckBoxTableViewerManager(adTable);
		createQueryForm(toolkit, body, adTable, new FMessageManager(), tableManager);
		queryForm.getTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Object object = selection.getFirstElement();
				if (object instanceof LotReserved) {
					LotReserved reserved = (LotReserved) object;
					section.setObject(reserved, null);
				}
			}
		});
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = 220;
		queryForm.setLayoutData(gd);
		
		Composite sectionComposite = toolkit.createComposite(body, SWT.NONE);
		GridLayout layout = new GridLayout(1, false);
		sectionComposite.setLayout(layout);
		sectionComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable futureHoldTable = adManager.getADTable(0, TABLE_NAME);
			futureHoldTable.setLabel_zh(Message.getString("wip.Lot_furure_hold"));
			section = new MultiLotFutureHoldSection(futureHoldTable,tableManager);
			section.createContents(mform, sectionComposite);
			section.setQueryTableForm(queryForm);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void createQueryForm(FormToolkit toolkit, Composite parent, ADTable adTable, IMessageManager manager,CheckBoxTableViewerManager tableManager) {
		Section section = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
		section.setText(Message.getString("wip.lot_future_hold_query"));
	    
	    Composite client = toolkit.createComposite(section);    
	    GridLayout layout = new GridLayout();    
	    layout.numColumns = 1;    
	    client.setLayout(layout);
	    section.setLayout(layout);
	    
	    GridData td = new GridData(GridData.FILL_BOTH);
	    client.setLayoutData(td);
	    section.setLayoutData(td);
	    
		queryForm = new MultiLotQueryTableForm(client, SWT.NONE, adTable, manager, tableManager);
		GridData gridData = new GridData(GridData.FILL_BOTH);
		queryForm.setLayoutData(gridData);
		queryForm.setLayout(new GridLayout(1, false));
		
		section.setClient(client);	
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;

		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
}
