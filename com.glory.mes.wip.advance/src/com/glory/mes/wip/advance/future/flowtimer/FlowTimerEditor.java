package com.glory.mes.wip.advance.future.flowtimer;

import java.util.Date;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.ExportToolItemGlc;
import org.eclipse.swt.widgets.IToolItemListener;
import org.eclipse.swt.widgets.ImportToolItemGlc;
import org.osgi.service.event.Event;

import com.glory.common.excel.upload.ExcelUpload;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchMultiField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.Process;
import com.glory.mes.wip.advance.custom.MultiProcedureTimerCustomComposite;
import com.glory.mes.wip.advance.custom.MultiTimerCustomComposite;
import com.glory.mes.wip.advance.custom.ProcedureStepTimerCustomComposite;
import com.glory.mes.wip.advance.future.timer.StepTimerSection;
import com.glory.mes.wip.advance.future.timer.multiproc.MultiProcedureSection;
import com.glory.mes.wip.advance.future.timer.multitimer.MultiTimerSection;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureMultiProcedureTimer;
import com.glory.mes.wip.future.FutureMultiStepTimer;
import com.glory.mes.wip.future.FutureStepTimer;
import com.glory.mes.wip.future.FutureTimer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class FlowTimerEditor extends GlcEditor {

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.flowtimer.FlowTimerEditor";
	
	private QueryFormField queryFormField;
	private GlcFormField glcFormField;
	private EntityFormField entityFormField;
	private CustomField customField;
	protected ImportToolItemGlc importToolItem;
	protected ExportToolItemGlc exportToolItem;
	
	private RefTableField timerTypeField;
	private RefTableField timerActionField;
	private RefTableField holdCodeField;
	private SearchMultiField holdOwnerField;
	private TextField holdReasonField;
	
	private static final String FIELD_QUERY = "qtimerQuery";
	private static final String FIELD_GLCFORM = "qtimerProcedureActionData";
	private static final String FIELD_ENTITYFORM = "stepQtime";
	private static final String FIELD_TIMER_TYPE = "timerType";
	private static final String FIELD_TIMER_ACTION = "timerAction";
	private static final String FIELD_HOLD_CODE = "holdCode";
	private static final String FIELD_HOLD_OWNER = "holdOwner";
	private static final String FIELD_HOLD_REASON = "holdReason";
	
	private static final String BUTTON_QTIME = "qTime";
	private static final String BUTTON_STEP_QTIME = "stepTime";
	private static final String BUTTON_MUILT_STEP_QTIME = "muiltStepTime";
	private static final String BUTTON_MUILT_PROC_QTIME = "QmuiltStepTime";
	
	private static final String ENTITY_QTIME_FORM = "WIPFutureQtimeBaseForm";
	private static final String ENTITY_STEP_QTIME_FORM = "WIPProcStepQtimerBaseForm";
	private static final String ENTITY_MUILT_STEP_QTIME_FORM = "WIPProcMuiltStepQtimerBaseForm";
	private static final String ENTITY_MUILT_PROC_QTIME_FORM = "WIPVProcMuiltStepQtimerBaseForm";
	
	protected NatTable natTable;
	
	private String currentEntityForm = ENTITY_QTIME_FORM;//当前entityForm界面
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		queryFormField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);
		
		glcFormField = form.getFieldByControlId(FIELD_GLCFORM, GlcFormField.class);
		
		entityFormField = glcFormField.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_QUERY + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);	
		
		subscribeAndExecute(eventBroker, glcFormField.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);
		
		subscribeAndExecute(eventBroker, glcFormField.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);
		
		subscribeAndExecute(eventBroker, glcFormField.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_QTIME), this::timeAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_STEP_QTIME), this::timeAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MUILT_STEP_QTIME), this::timeAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MUILT_PROC_QTIME), this::timeAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_REFRESH), this::refreshAdapter);
		
		importToolItem = (ImportToolItemGlc)form.getButtonByControl(null, IToolItemListener.TYPE_IMPORT);
		exportToolItem = (ExportToolItemGlc)form.getButtonByControl(null, IToolItemListener.TYPE_EXPORT);
		if (importToolItem != null) {
			importToolItem.isBackend = true;
			Consumer postImport = t -> refreshAdapter(null);
			importToolItem.postImportConsumer = postImport;
			importToolItem.cudConsumer = new Consumer<ExcelUpload>() {
				@Override
				public void accept(ExcelUpload t) {
					cudUploadEntityList(t);
				}
			};
		}
		if (exportToolItem != null) {
			exportToolItem.isBackend = true;
		}

		timerTypeField = entityFormField.getFieldByControlId(FIELD_TIMER_TYPE, RefTableField.class);
		timerActionField = entityFormField.getFieldByControlId(FIELD_TIMER_ACTION, RefTableField.class);
		holdCodeField = entityFormField.getFieldByControlId(FIELD_HOLD_CODE, RefTableField.class);
		holdOwnerField = entityFormField.getFieldByControlId(FIELD_HOLD_OWNER, SearchMultiField.class);
		holdReasonField = entityFormField.getFieldByControlId(FIELD_HOLD_REASON, TextField.class);
		
		timerTypeField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object sender, Object newValue) {
				try {
					String timerType = newValue == null ? "" : String.valueOf(newValue);
					
					if (StringUtils.isEmpty(timerType) || StringUtils.equals(timerType, FutureTimer.TIMERTYPE_MINIMAL)) {
						timerActionField.setValue(null);
						holdCodeField.setValue(null);
						holdOwnerField.setValue(null);
						holdReasonField.setText(null);
					} 
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
			}
		});
		
		timerActionField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object sender, Object newValue) {
				try {
					String timerAction = newValue == null ? "" : String.valueOf(newValue);
					
					if (StringUtils.isEmpty(timerAction) || StringUtils.equals(timerAction, FutureTimer.ACTION_NOTE)) {
						holdCodeField.setValue(null);
						holdOwnerField.setValue(null);
						holdReasonField.setText(null);
					} 
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
			}
		});
		
		
	}
	
	public void selectionChanged(Object object) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Event event = (Event) object;
			FutureTimer futureTimer = (FutureTimer) event.getProperty(GlcEvent.PROPERTY_DATA);
			//通过不同的类型动态生成下面QTime设置列表
			if(futureTimer != null) {
				if (futureTimer instanceof FutureStepTimer) {
					//单工步QTime
					timeAdapter(new Event(BUTTON_QTIME, Maps.newHashMap()));
					FutureStepTimer futureStepTimer = (FutureStepTimer) futureTimer;
					if (StringUtil.isEmpty(futureStepTimer.getProcedureName())) {
						entityFormField.setValue(futureStepTimer);
						entityFormField.refresh();
					} else {
						//模块单工步Qtime
						timeAdapter(new Event(BUTTON_STEP_QTIME, Maps.newHashMap()));
						ProcedureStepTimerCustomComposite procedureStepTimerCustomComposite = (ProcedureStepTimerCustomComposite) customField.getCustomComposite();
						StepTimerSection section = procedureStepTimerCustomComposite.getSection();
						
						Procedure procedure = new Procedure();
						procedure.setOrgRrn(Env.getOrgRrn());
						procedure.setName(futureStepTimer.getProcedureName());
						procedure = (Procedure) prdManager.getSimpleProcessDefinition(procedure);
						if (procedure != null) {
							RefTableField field = (RefTableField) section.getProcessSection().getProcedureIField();
							field.setValue(procedure.getObjectRrn());
							section.setAdObject(futureStepTimer);
							section.getTimerField().loadFlowTreeByProcedure(procedure, futureStepTimer.getStepName(),Lists.newArrayList(futureStepTimer));
						}			
						section.refresh();
					}
				} else if (futureTimer instanceof FutureMultiStepTimer){
					//模块多工步Qtime
					timeAdapter(new Event(BUTTON_MUILT_STEP_QTIME, Maps.newHashMap()));
					FutureMultiStepTimer futureMultiStepTimer = (FutureMultiStepTimer) futureTimer;
					MultiTimerCustomComposite multiTimerCustomComposite = (MultiTimerCustomComposite) customField.getCustomComposite();
					MultiTimerSection section = multiTimerCustomComposite.getSection();
					
					Procedure procedure = new Procedure();
					procedure.setOrgRrn(Env.getOrgRrn());
					procedure.setName(futureMultiStepTimer.getProcedureName());
					procedure = (Procedure) prdManager.getSimpleProcessDefinition(procedure);
					if (procedure != null) {
						RefTableField field = (RefTableField) section.getProcessSection().getProcedureIField();
						field.setValue(procedure.getObjectRrn());
						section.setAdObject(futureMultiStepTimer);
						section.getTimerField().loadFlowTreeByProcedure(procedure, futureMultiStepTimer.getStepName(),Lists.newArrayList(futureMultiStepTimer));
					}
					section.refresh();
				} else if (futureTimer instanceof FutureMultiProcedureTimer){
					//跨模块Qtime
					timeAdapter(new Event(BUTTON_MUILT_PROC_QTIME, Maps.newHashMap()));
					FutureMultiProcedureTimer futureMultiProcedureTimer = (FutureMultiProcedureTimer) futureTimer;
					MultiProcedureTimerCustomComposite multiProcedureTimerCustomComposite = (MultiProcedureTimerCustomComposite) customField.getCustomComposite();
					MultiProcedureSection section = multiProcedureTimerCustomComposite.getSection();
					
					com.glory.mes.prd.model.Process process = new Process();
					process.setOrgRrn(Env.getOrgRrn());
					process.setName(futureMultiProcedureTimer.getProcessName());
					process = (Process) prdManager.getSimpleProcessDefinition(process);
					if (process != null) {		
						RefTableField field = (RefTableField) section.getProcessSection().getiField();
						field.setValue(process.getObjectRrn());
						section.setAdObject(futureMultiProcedureTimer);
						section.getTimerField().loadFlowTreeByProcedure(process, futureMultiProcedureTimer.getProcedureName(), futureMultiProcedureTimer.getStepName(),Lists.newArrayList(futureMultiProcedureTimer));
					}	
					section.refresh();
				  } 
			   }
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
	        	return;
			}			
		}
	
	public void newAdapter(Object object) {
		refresh(null);
	}
	
	public void saveAdapter(Object object) {
		form.getMessageManager().removeAllMessages();
		try {
			if (entityFormField.validate()) {
				FutureStepTimer futureTimer = (FutureStepTimer)entityFormField.getValue();
				if (futureTimer.getTimerDuration() == null || futureTimer.getTimerDuration() <= 0) {
					 UI.showError(Message.getString("wip.timer_duration_must_valid"));
					 return;
				}
				
				if (StringUtils.equals(futureTimer.getTimerType(), FutureTimer.TIMERTYPE_MAXIMAL) && StringUtils.isEmpty(futureTimer.getTimerAction())) {
					 UI.showError(Message.getString("wip.timer_action_not_found"));
					 return;
				}
				
				if (StringUtils.isNotEmpty(futureTimer.getTimerAction()) && !StringUtils.equals(futureTimer.getTimerAction(), FutureTimer.ACTION_NOTE) && (StringUtils.isEmpty(futureTimer.getHoldCode()) || StringUtils.isEmpty(futureTimer.getHoldOwner()))) {
					 UI.showError(Message.getString("wip.timer_hold_info_not_complete"));
					 return;
				}
				
				if (futureTimer.getObjectRrn() == null) {
					futureTimer.setCreated(new Date());
					futureTimer.setCreatedBy(Env.getUserName());
				}
				futureTimer.setOrgRrn(Env.getOrgRrn());
				futureTimer.setUpdatedBy(Env.getUserName());
					
				LotManager lotManager = Framework.getService(LotManager.class);
				FutureStepTimer newFutureTimer = (FutureStepTimer) lotManager.saveFutureAction(futureTimer, Env.getSessionContext());
					
				UI.showInfo(Message.getString("wip.timer_holdsuccessed"));
				refresh(newFutureTimer);	
			}		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void deleteAdapter(Object object) {
		try {
			FutureStepTimer futureStepTimer = (FutureStepTimer) entityFormField.getValue();
			getADManger().deleteEntity(futureStepTimer, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
			refresh(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void refresh(FutureStepTimer futureTimer) {
		try {
			if(futureTimer != null && futureTimer.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				futureTimer = (FutureStepTimer) entityManager.getEntity(futureTimer);
				entityFormField.setValue(futureTimer);				
				entityFormField.refresh();
				queryFormField.refresh();
			}else {
				entityFormField.setValue(new FutureStepTimer());
				entityFormField.refresh();
				queryFormField.refresh();
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}			
	}
	
	private void refreshAdapter(Object object) {
		refresh(null);
	}
	
	private void timeAdapter(Object object) {
		try {
			Event event = (Event) object;
			String[] fullTopic = event.getTopic().split("-");
			String eventId = fullTopic[fullTopic.length - 1];
			if (!ENTITY_QTIME_FORM.equals(currentEntityForm) && BUTTON_QTIME.equals(eventId)) {
				currentEntityForm = ENTITY_QTIME_FORM;
			} else if (!ENTITY_STEP_QTIME_FORM.equals(currentEntityForm) && BUTTON_STEP_QTIME.equals(eventId)) {
				currentEntityForm = ENTITY_STEP_QTIME_FORM;
			} else if (!ENTITY_MUILT_STEP_QTIME_FORM.equals(currentEntityForm) && BUTTON_MUILT_STEP_QTIME.equals(eventId)) {
				currentEntityForm = ENTITY_MUILT_STEP_QTIME_FORM;
			} else if (!ENTITY_MUILT_PROC_QTIME_FORM.equals(currentEntityForm) && BUTTON_MUILT_PROC_QTIME.equals(eventId)) {
				currentEntityForm = ENTITY_MUILT_PROC_QTIME_FORM;
			}
			glcFormField.reflow(getADManger().getADForm(Env.getOrgRrn(), currentEntityForm));
			glcFormField = form.getFieldByControlId(FIELD_GLCFORM, GlcFormField.class);
			if (ENTITY_QTIME_FORM.equals(currentEntityForm)) {
				entityFormField = glcFormField.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
			} else {
				customField = glcFormField.getFieldByControlId(FIELD_ENTITYFORM, CustomField.class);
				changeButtonEvent();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void changeButtonEvent() {
		if (customField.getCustomComposite() instanceof MultiProcedureTimerCustomComposite) {
			MultiProcedureTimerCustomComposite customComposite = (MultiProcedureTimerCustomComposite) customField.getCustomComposite();
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_SAVE).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					queryFormField.refresh();
				}
			});
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_DELETE).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					refresh(null);
				}
			});
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_REFRESH).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					refresh(null);
				}
			});
		} else if (customField.getCustomComposite() instanceof MultiTimerCustomComposite) {
			MultiTimerCustomComposite customComposite = (MultiTimerCustomComposite) customField.getCustomComposite();
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_SAVE).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					queryFormField.refresh();
				}
			});
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_DELETE).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					refresh(null);
				}
			});
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_REFRESH).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					refresh(null);
				}
			});
		} else if (customField.getCustomComposite() instanceof ProcedureStepTimerCustomComposite) {
			ProcedureStepTimerCustomComposite customComposite = (ProcedureStepTimerCustomComposite) customField.getCustomComposite();
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_SAVE).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					queryFormField.refresh();
				}
			});
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_DELETE).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					refresh(null);
				}
			});
			customComposite.getSection().getButtonByName(ADButtonDefault.BUTTON_NAME_REFRESH).addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					refresh(null);
				}
			});
		}
	}
	
	protected void cudUploadEntityList(ExcelUpload upload) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			List<ADBase> uploadList = upload.getUploadList();
			List<FutureAction> futureActions = uploadList.stream().map(x -> (FutureAction)x).collect(Collectors.toList());
			lotManager.saveFutureActions(futureActions, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonImportSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
