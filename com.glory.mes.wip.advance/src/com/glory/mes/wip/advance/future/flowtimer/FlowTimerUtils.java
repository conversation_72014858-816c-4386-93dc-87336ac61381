package com.glory.mes.wip.advance.future.flowtimer;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;

public class FlowTimerUtils {
	
	public static ADTable tableHandle(ADTable table) throws Exception {
		List<ADTab> tabs = table.getTabs();
		ADTab adTab = tabs.get(0);
		SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
		List<ADField> fields = adTab.getFields();
		Iterator<ADField> it = fields.iterator();
		while(it.hasNext()){
			ADField adField = it.next();
			if ("isComponentLevel".equals(adField.getName())) {
				// 如果没有设置这个参数为True 就移除
				if (!MesCfMod.isCompUseQTime(Env.getOrgRrn(), sysParamManager)) {
					it.remove();
				};
			}
		}
		adTab.setFields(fields);
		ArrayList<ADTab> arrayList = new ArrayList<ADTab>();
		arrayList.add(adTab);
		table.setTabs(arrayList);
		
		return table;
	}

}
