package com.glory.mes.ras.consumable.tool.offshelf;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.mes.ras.consumable.tool.ToolSection;

public class ToolOffShelfSection extends ToolSection {

	public CheckBoxFixEditorTableManager manager;

	private ToolOffShelfFrom itemForm;

	public ToolOffShelfSection(ADTable adTable) {
		super(adTable);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemOffShelf(tBar);
		new ToolItem(tBar, 2);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemOffShelf(ToolBar tBar) {
		this.itemSave = new ToolItem(tBar, SWT.PUSH);
		this.itemSave.setText(Message.getString("mm.mlot_off"));
		this.itemSave.setImage(SWTResourceCache.getImage("mlot_offshelf"));
		this.itemSave.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				downAdapter();
			}
		});
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
         itemForm = new ToolOffShelfFrom(composite, SWT.NONE, tab, mmng);
         return itemForm;
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		if (adObject != null && adObject.getObjectRrn() != null) {
			itemForm.loadCurrent(adObject.getObjectRrn());
		}
	}

	private void downAdapter() {
		this.form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				Tool tool = (Tool) getAdObject();
				if (tool == null || tool.getObjectRrn() == null) {
					return;
				}
					
				try {
					off(tool);
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					this.refreshAdapter();
					
					// 方便继续操作
					txtTool.setFocus();
					txtTool.selectAll();
				} catch (Exception e) {
					e.printStackTrace();
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	}
	
	public Tool off(Tool tool) throws Exception {
		ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
		return consumableManager.toolOffShelf(tool, null, null, null, null, Env.getSessionContext());
	}
	
	@Override
	public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);
		ADBase adObject = getAdObject();
		if (adObject != null && adObject.getObjectRrn() != null) {
			Tool tool = (Tool) adObject;

			if (Tool.HOLDSTATE_ON.equals(tool.getHoldState())) {
				itemSave.setEnabled(false);
			} else {
				if (newStatus != null ) {
					// 判断上下架是否改变物料批状态
					boolean isChange = false;
					try {
						SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
						isChange = MesCfMod.isMLotChangeStateByShelfAction(Env.getOrgRrn(), sysParameterManager);
					} catch (Exception e) {
						e.printStackTrace();
						ExceptionHandlerManager.asyncHandleException(e);
					}
					
					if (isChange && MaterialState.STATE_INSHELF.equals(tool.getState())) {
						itemSave.setEnabled(true);
					} else if (!isChange && MaterialState.STATE_IN.equals(tool.getState())) {
						itemSave.setEnabled(true);
					} else {
						itemSave.setEnabled(false);
					}
				} else {
					itemSave.setEnabled(false);
				}
			}
		}
	}
	
}
