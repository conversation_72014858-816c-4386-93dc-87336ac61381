package com.glory.mes.ras.consumable.tool.unscrap;

import java.math.BigDecimal;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.mm.consumable.model.ConsumableAction;

public class ToolUnScrapForm extends EntityForm{
	private static final Logger logger = Logger.getLogger(ToolUnScrapForm.class);

	private IField fieldUnScrapCode;
	private static final String UNSCRAPCODE = "ActionCode";
	private static final String UNSCRAPCODE_ID = "actionCode";
//    private IField fieldUnScrapQty;
//    private static final String UNSCRAPQTY = "MainQty";
//    private static final String UNSCRAPQTY_ID = "mainQty";
	private IField fieldUnScrapReason;
	private static final String UNSCRAPREASON = "ActionReason";
	private static final String UNSCRAPREASON_ID = "actionReason";
	private IField fieldComment;
	private static final String COMMENT = "ActionComment";
	private static final String COMMENT_ID = "actionComment";

	protected ConsumableAction consumableAction = new ConsumableAction();

	public ToolUnScrapForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
		    ADField separatorADField = new ADField();
            separatorADField.setIsSameline(true);
            SeparatorField separatorField = createSeparatorField("Separator", "");
            separatorField.setADField(separatorADField);
            this.addField("Separator", separatorField);

			fieldUnScrapCode = createUserRefList(UNSCRAPCODE_ID, Message.getString("mm.unscrapcode") + "*", "ToolUnScrapCode", false);
//			fieldUnScrapQty = createText(UNSCRAPQTY_ID, Message.getString("mm.qty"), "", 32);
			fieldUnScrapReason = createText(UNSCRAPREASON_ID, Message.getString("mm.unscrapreason"), "", 32);
			fieldComment = createText(COMMENT_ID, Message.getString("mm.comment"), "", 32);

			addField(UNSCRAPCODE, fieldUnScrapCode);
//			addField(UNSCRAPQTY, fieldUnScrapQty);
			addField(UNSCRAPREASON, fieldUnScrapReason);
			addField(COMMENT, fieldComment);			
		} catch (Exception e) {
			logger.error("ToolUnScrapForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!f.equals(fieldUnScrapCode) && !f.equals(fieldUnScrapReason)
						&& !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public void setEnabled() {
		if (object != null && object instanceof ADBase) {
			ADBase base = (ADBase) object;
			for (IField f : fields.values()) {
				ADField adField = adFields.get(f.getId());
				if (adField != null && !adField.getIsEditable()) {
					if (base.getObjectRrn() == null || base.getObjectRrn() == 0) {
						f.setEnabled(true);
					} else {
						f.setEnabled(false);
					}
				}
			}
			fieldUnScrapCode.setEnabled(true);
//			fieldUnScrapQty.setEnabled(true);
			fieldUnScrapReason.setEnabled(true);
			fieldComment.setEnabled(true);
		}
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			fieldUnScrapCode.setValue("");
			fieldUnScrapCode.refresh();
//			((TextField) fieldUnScrapQty).setText("0");
			((TextField) fieldUnScrapReason).setText("");
			((TextField) fieldComment).setText("");
		} catch (Exception e) {
			logger.error("UnScrapLotForm : refresh()", e);
		}
	}

	@Override
	// 保存到数据库
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			consumableAction.setActionCode(fieldUnScrapCode.getValue().toString());
//			String qty = fieldUnScrapQty.getValue().toString();
            consumableAction.setMainQty(new BigDecimal("1"));
            consumableAction.setActionReason(fieldUnScrapReason.getValue().toString());
            consumableAction.setActionComment(fieldComment.getValue().toString());
			return true;
		}
		return false;
	}

	@Override
	public boolean validate() {
		boolean validFlag = true;
		if (fieldUnScrapCode.getValue() != null
				&& !"".equals(String.valueOf(fieldUnScrapCode.getValue()).trim())) {
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(UNSCRAPCODE_ID, String.format(Message
					.getString("common.ismandatry"), UNSCRAPCODE_ID), null,
					IMessageProvider.ERROR, fieldUnScrapCode.getControls()[1]);
		}
		return validFlag;
	}

	public ConsumableAction getConsumableAction() {
		return consumableAction;
	}

}
