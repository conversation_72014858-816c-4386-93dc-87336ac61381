package com.glory.mes.ras.consumable.tool.onshelf;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.lot.model.MLotStorage;

public class ToolOnShelfFrom extends EntityForm {

	private static final String FIELD_CURRENT_WAREHOUSE = "currentWarehouseRrn";
	private static final String FIELD_CURRENT_RACK = "currentRackId";
	private static final String FIELD_CURRENT_RACK_AREA = "currentRackAreaId";

	private static final String FIELD_TARGET_WAREHOUSE = "targetWarehouseRrn";
	private static final String FIELD_TARGET_RACK = "targetRackRrn";
	private static final String FIELD_TARGET_RACK_AREA = "targetRackAreaRrn";

	private Long warehouseRrn;
	private Long rackRrn;
	private Long rackAreaRrn;

	public ToolOnShelfFrom(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public void loadCurrent(Long mLotRrn) {
		// 1.检查批次是否已入库
		MLotStorage storage = getMLotStorage(mLotRrn);
		if (storage == null) {
			return;
		}
		// 2.设置仓库值
		RefTableField warehouseField = (RefTableField) getFields().get(FIELD_CURRENT_WAREHOUSE);
		warehouseField.setValue(storage.getWarehouseRrn(), true);
		
		RefTableField targetWarehouseField = (RefTableField) getFields().get(FIELD_TARGET_WAREHOUSE);
		targetWarehouseField.setValue(storage.getWarehouseRrn(), true);

		// 3.设置货架、库位值
		RefTableField rackField = (RefTableField) getFields().get(FIELD_CURRENT_RACK);
		RefTableField rackAreaField = (RefTableField) getFields().get(FIELD_CURRENT_RACK_AREA);

		if (StringUtil.isEmpty(storage.getStorageType())) {
			rackField.setValue(null, true);
			rackAreaField.setValue(null, true);
			return;
		}

		if (Storage.CATEGORY_RACK.equals(storage.getStorageType())) {
			// 物料只与货架做了绑定，显示货架值
			rackField.setValue(storage.getStorageId(), true);
			rackAreaField.setValue(null, true);
		} else {
			// 物料与库位做了绑定，显示库位值
			rackAreaField.setValue(storage.getStorageId(), true);

			// 显示库位所在的货架
			Storage rackStorage = getRack(storage.getStorageId());
			rackField.setValue(rackStorage.getName(), true);
		}

	}

	private Storage getRack(String rackAreaId) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);

			Storage childStorage = new Storage();
			childStorage.setName(rackAreaId);
			childStorage.setOrgRrn(Env.getOrgRrn());
			childStorage = mmManager.getStorage(childStorage);

			Storage parentStorage = new Storage();
			parentStorage.setOrgRrn(Env.getOrgRrn());
			parentStorage.setObjectRrn(childStorage.getParentRrn());
			parentStorage = mmManager.getStorage(parentStorage);
			return parentStorage;
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	private MLotStorage getMLotStorage(Long mLotRrn) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			List<MLotStorage> storages = mmManager.getLotStorages(mLotRrn);

			if (CollectionUtils.isEmpty(storages)) {
				RefTableField warehouseField = (RefTableField) getFields().get(FIELD_CURRENT_WAREHOUSE);
				RefTableField rackField = (RefTableField) getFields().get(FIELD_CURRENT_RACK);
				RefTableField rackAreaField = (RefTableField) getFields().get(FIELD_CURRENT_RACK_AREA);
				RefTableField targetWarehouseField = (RefTableField) getFields().get(FIELD_TARGET_WAREHOUSE);
				targetWarehouseField.setValue(null, true); 
				warehouseField.setValue(null, true);
				rackField.setValue(null, true);
				rackAreaField.setValue(null, true);	
				throw new ClientException("mm.mlot_must_specify_lotstorage");
			}else {
				RefTableField targetRackRrn = (RefTableField) getFields().get(FIELD_TARGET_RACK);
				RefTableField targetRackAreaRrn = (RefTableField) getFields().get(FIELD_TARGET_RACK_AREA);
				targetRackRrn.setValue(null, true);
				targetRackAreaRrn.setValue(null, true); 
			}

			if (storages.size() > 1) {
				throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
			}
			return storages.get(0);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.getId().equals(FIELD_CURRENT_WAREHOUSE)
						&& !f.getId().equals(FIELD_CURRENT_RACK) && !f.getId().equals(FIELD_CURRENT_RACK_AREA)
						&& !f.getId().equals(FIELD_TARGET_WAREHOUSE) && !f.getId().equals(FIELD_TARGET_RACK)
						&& !f.getId().equals(FIELD_TARGET_RACK_AREA)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.getId().equals(FIELD_CURRENT_WAREHOUSE)
						&& !f.getId().equals(FIELD_CURRENT_RACK) && !f.getId().equals(FIELD_CURRENT_RACK_AREA)
						&& !f.getId().equals(FIELD_TARGET_WAREHOUSE) && !f.getId().equals(FIELD_TARGET_RACK)
						&& !f.getId().equals(FIELD_TARGET_RACK_AREA)) {
					PropertyUtil.setProperty(object, f.getId(), f.getValue());
				}

				if (f.getId().equals(FIELD_TARGET_WAREHOUSE)) {
					setWarehouseRrn(DBUtil.toLong(f.getValue()));
				}

				if (f.getId().equals(FIELD_TARGET_RACK)) {
					setRackRrn(DBUtil.toLong(f.getValue()));
				}

				if (f.getId().equals(FIELD_TARGET_RACK_AREA)) {
					setRackAreaRrn(DBUtil.toLong(f.getValue()));
				}
			}
			return true;
		}
		return false;
	}

	public Long getWarehouseRrn() {
		return warehouseRrn;
	}

	public void setWarehouseRrn(Long warehouseRrn) {
		this.warehouseRrn = warehouseRrn;
	}

	public Long getRackRrn() {
		return rackRrn;
	}

	public void setRackRrn(Long rackRrn) {
		this.rackRrn = rackRrn;
	}

	public Long getRackAreaRrn() {
		return rackAreaRrn;
	}

	public void setRackAreaRrn(Long rackAreaRrn) {
		this.rackAreaRrn = rackAreaRrn;
	}

}
