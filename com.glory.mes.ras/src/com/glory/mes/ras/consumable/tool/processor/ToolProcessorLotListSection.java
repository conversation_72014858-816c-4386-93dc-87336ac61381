package com.glory.mes.ras.consumable.tool.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.state.model.StatusModel;
import com.glory.common.state.model.StatusModelDiagram;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.application.event.AppEvent;
import com.glory.framework.base.application.event.AppEventManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.svg.SvgDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.idquery.IdQueryEntityQueryListSection;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.ras.consumable.tool.action.dialog.ToolDetailDialog;
import com.glory.mes.ras.consumable.tool.action.dialog.ToolHisQueryDialog;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * 在制品查询列表,可以在此列表中对批量对批次进行处理
 * 支持导入功能
 */
public class ToolProcessorLotListSection extends IdQueryEntityQueryListSection {
	
	protected static final String KEY_IN = "In";
	protected static final String KEY_ONSHELF = "OnShelf";
	protected static final String KEY_OFFSHELF = "OffShelf";
	protected static final String KEY_OUT = "Out";
	protected static final String KEY_TRANSFER = "Transfer";
	protected static final String KEY_RETURN = "Return";
	protected static final String KEY_CHANGESHELFLIFE = "ChangeShelfLife";
	protected static final String KEY_HOLD = "Hold";
	protected static final String KEY_RELEASE = "Release";
	protected static final String KEY_SCRAP = "Scrap";
	
	protected ToolItem itemClean, itemChange, itemSave, itemScrap, itemUnscrap, itemLogEvent;
	protected ToolItem itemHold, itemRelease, itemMaterialOn, itemOffShelf, itemDetail, itemHisQuery;
	protected ToolItem itemIn;
	protected ToolItem itemOut;
	protected ToolItem itemPrint;

	private String packageLayer;
	
	protected IEventBroker eventBroker;


	public ToolProcessorLotListSection(ListTableManager tableManager, String packageLayer) {
		super(tableManager);
		this.packageLayer = packageLayer;
	}
	
	public ToolProcessorLotListSection(ListTableManager tableManager, String packageLayer,IEventBroker eventBroker) {
		super(tableManager);
		this.packageLayer = packageLayer;
		this.eventBroker = eventBroker;
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemOnShelf(tBar);
		createToolItemOffShelf(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		createToolItemRelease(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemLogEvent(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClean(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemLifeCycle(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemIn(tBar);
		createToolItemOut(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemMove(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnShip(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnscrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemPrint(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSearch(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemToolDetail(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemToolHisQuery(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		//根据系统参数判定时都需要展示该按钮
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			boolean isShowSvg = MesCfMod.isShowSvgButton(Env.getOrgRrn(), sysParamManager);
			if(isShowSvg) {
				new ToolItem(tBar, SWT.SEPARATOR);
				createToolItemSvg(tBar);
			}
		} catch (Exception e) {
			logger.error("ToolProcessorLotListSection : createToolItemSvg ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemImport(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemIn(ToolBar tBar) {
		// itemIn = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_IN);
		itemIn = new ToolItem(tBar, SWT.PUSH);
		itemIn.setText(Message.getString("mm.mlot_in"));
		itemIn.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemIn.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				inAdapter();
			}
		});
	}
	
	protected void inAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				ToolInProcessor processor = new ToolInProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : inAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void createToolItemOut(ToolBar tBar) {
		// itemOut = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_OUT);
		itemOut = new ToolItem(tBar, SWT.PUSH);
		itemOut.setText(Message.getString("mm.mlot_out"));
		itemOut.setImage(SWTResourceCache.getImage("mlot_receive"));
		itemOut.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				outAdapter();
			}
		});
	}
	
	protected void outAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				ToolOutProcessor processor = new ToolOutProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : outAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//治具清洗
	protected void createToolItemClean(ToolBar tBar) {
		itemClean = new ToolItem(tBar, SWT.PUSH);
		itemClean.setText(Message.getString("ras.Tool_clean"));
		itemClean.setImage(SWTResourceCache.getImage("sample-setup"));
		itemClean.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				cleanAdapter(event);
			}
		});
	}
	
	protected void cleanAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolCleanProcessor processor = new ToolCleanProcessor(true, false);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//治具修改生命周期
	private void createToolItemLifeCycle(ToolBar tBar) {
		itemChange = new ToolItem(tBar, SWT.PUSH);
		itemChange.setText(Message.getString("ras.lifecycle"));
		itemChange.setImage(SWTResourceCache.getImage("wip_code"));
		itemChange.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				lifeCycleAdapter(event);
			}
		});
	}
	
	protected void lifeCycleAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolLifeCycleProcessor processor = new ToolLifeCycleProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//移库
	protected void createToolItemMove(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString("mm.move_warehouse"));
		itemSave.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				moveAdapter(event);
			}
		});
	}
	
	protected void moveAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolTransferProcessor processor = new ToolTransferProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//退库
	protected void createToolItemUnShip(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString("ras.unship"));
		itemSave.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unshipAdapter(event);
			}
		});
	}
	
	protected void unshipAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolUnShipProcessor processor = new ToolUnShipProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//取消报废
    protected void createToolItemUnscrap(ToolBar tBar) {
        itemUnscrap = new ToolItem(tBar, SWT.PUSH);
        itemUnscrap.setText(Message.getString("ras.unscrap"));
        itemUnscrap.setImage(SWTResourceCache.getImage("unscrap"));
        itemUnscrap.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                unScrapAdapter(event);
            }
        });
    }
	
	protected void unScrapAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolUnScrapProcessor processor = new ToolUnScrapProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
    
	//报废
	protected void createToolItemScrap(ToolBar tBar) {
		itemScrap = new ToolItem(tBar, SWT.PUSH);
		itemScrap.setText(Message.getString("wip.scrap_lotcn"));
		itemScrap.setImage(SWTResourceCache.getImage("scrap-lot"));
		itemScrap.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				scrapAdapter(event);
			}
		});
	}
	
	protected void scrapAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolScrapProcessor processor = new ToolScrapProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//修改治具状态
	protected void createToolItemLogEvent(ToolBar tBar) {
		itemLogEvent = new ToolItem(tBar, SWT.PUSH);
		itemLogEvent.setText("LogEvent");
		itemLogEvent.setImage(SWTResourceCache.getImage("event"));
		itemLogEvent.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeEventAdapter(event);
			}
		});
	}	
	
	protected void changeEventAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				if(checkLots.size() > 1) {
					UI.showInfo(Message.getString("common.select_only_one_object"));
					return;
				}
				ToolLogEventProcessor processor = new ToolLogEventProcessor(true, checkLots);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//治具暂停
	protected void createToolItemHold(ToolBar tBar) {
		itemHold = new ToolItem(tBar, SWT.PUSH);
		itemHold.setText(Message.getString("wip.hold"));
		itemHold.setImage(SWTResourceCache.getImage("hold"));
		itemHold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				holdAdapter(event);
			}
		});
	}
	
	protected void holdAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolHoldProcessor processor = new ToolHoldProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//治具放行
	protected void createToolItemRelease(ToolBar tBar) {
		itemRelease = new ToolItem(tBar, SWT.PUSH);
		itemRelease.setText(Message.getString("ras.release"));
		itemRelease.setImage(SWTResourceCache.getImage("release"));
		itemRelease.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				releaseAdapter(event);
			}
		});
	}
	
	protected void releaseAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolReleaseProcessor processor = new ToolReleaseProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//治具上架
	protected void createToolItemOnShelf(ToolBar tBar) {
		this.itemMaterialOn = new ToolItem(tBar, SWT.PUSH);
		this.itemMaterialOn.setText(Message.getString("mm.mlot_on"));
		this.itemMaterialOn.setImage(SWTResourceCache.getImage("mlot_onshelf"));
		this.itemMaterialOn.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				onAdapter(event);
			}
		});
	}
	
	protected void onAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				
				if(checkLots.size() > 1) {
					UI.showInfo(Message.getString("common.select_only_one_object"));
					return;
				}
				ToolOnShelfProcessor processor = new ToolOnShelfProcessor(true, checkLots.get(0));
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//治具下架
	protected void createToolItemOffShelf(ToolBar tBar) {
		this.itemOffShelf = new ToolItem(tBar, SWT.PUSH);
		this.itemOffShelf.setText(Message.getString("mm.mlot_off"));
		this.itemOffShelf.setImage(SWTResourceCache.getImage("mlot_offshelf"));
		this.itemOffShelf.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				downAdapter(event);
			}
		});
	}
	
	protected void downAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolOffShelfProcessor processor = new ToolOffShelfProcessor(true, false);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void createToolItemSvg(ToolBar tBar) {
		itemSvg = new ToolItem(tBar, SWT.PUSH);;
		itemSvg.setText(Message.getString("common.svg"));
		itemSvg.setImage(SWTResourceCache.getImage("svg"));
		itemSvg.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				svgAdapter();
			}
		});
	}
	
	protected void svgAdapter() {
		try {
			Object[] elements = null;
			if (getSelectedObject() != null) {
				elements = new Object[] {getSelectedObject()};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else if(elements.length > 1){
				UI.showInfo(Message.getString("common.select_only_one_object"));
			}else {
				ADManager adManager = Framework.getService(ADManager.class);
				//需要标注的状态列表
				Tool tool = (Tool) getSelectedObject();
				List<String> actives = new ArrayList<String>();
				actives.add(tool.getState());
				//根据治具获取到治具对应的状态模型
				StatusModel statusModel = new StatusModel();
				statusModel.setObjectRrn(tool.getStatusModelRrn());
				statusModel = (StatusModel) adManager.getEntity(statusModel);
				//获取状态模型对应的事件流程图
				byte[] svgData= null;
				List<StatusModelDiagram> statusModelDiagrams = adManager.getEntityList(Env.getOrgRrn(), StatusModelDiagram.class, 1, " modelRrn = " + statusModel.getObjectRrn(), null);
				if(statusModelDiagrams != null && !statusModelDiagrams.isEmpty()) {
					svgData = statusModelDiagrams.get(0).getSvgData();
				}
				SvgDialog dialog = new SvgDialog(svgData, actives, null);
				dialog.open();
				this.refresh();
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}
	
	@Override
	public List<Object> getObjectsByInClause(List<String> ids) {
		try {
//			Set idSet = new HashSet(ids);
//			Map<String, Object> fieldMap = new HashMap<String, Object>();
//			fieldMap.put("lotIds", idSet);
//			ADManager adManager = Framework.getService(ADManager.class);
//			List<Lot> lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, 
//					Integer.MIN_VALUE, Integer.MAX_VALUE, " lotId in (:lotIds) ", "", fieldMap);
//			return (List<Object>)(List)lots;
		} catch (Exception e) {
			logger.error("LotProcessorLotListSection getObjectsByInClause error:", e);
		}
		return null;
	}

	protected void createToolItemPrint(ToolBar tBar) {
		this.itemPrint = new ToolItem(tBar, 8);
		this.itemPrint.setText(Message.getString(ExceptionBundle.bundle.CommonPrint()));
		this.itemPrint.setImage(SWTResourceCache.getImage("label"));
		this.itemPrint.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				printAdapter();
			}
		});
	}
	
	protected void printAdapter() {
        try {
        	List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				for (Object object : objects) {			
					if (object instanceof Tool) {
						Tool tool = (Tool) object;
						AppEvent appEvent = new AppEvent();
						appEvent.setEventParam1(packageLayer); //labelType
						appEvent.setEventParam2("MLot"); //objectType
						appEvent.setEventParam3(tool); //object
						AppEventManager.postEvent(AppEvent.EVENT_ID_PRINTLABEL, appEvent);
					}
				}
			} else {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	//详细信息
	protected void createToolItemToolDetail(ToolBar tBar) {
		itemDetail = new ToolItem(tBar, SWT.PUSH);
		itemDetail.setText(Message.getString("ras.tool_detail"));
		itemDetail.setImage(SWTResourceCache.getImage("lot-detail"));
		itemDetail.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				detailAdapter();
			}
		});
	}
	
	protected void detailAdapter() {
        try {
        	List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				if (objects.size() > 1) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				}
				List<Tool> checkLots = new ArrayList<Tool>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolDetailDialog detailDialog = new ToolDetailDialog("ToolDetailDialog", null, eventBroker, checkLots);
				if (Dialog.OK == detailDialog.open()) {
					refresh();
				}
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	//历史
	protected void createToolItemToolHisQuery(ToolBar tBar) {
		itemHisQuery = new ToolItem(tBar, SWT.PUSH);
		itemHisQuery.setText(Message.getString("ras.tool_his"));
		itemHisQuery.setImage(SWTResourceCache.getImage("search"));
		itemHisQuery.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				toolHisAdapter();
			}
		});
	}
	
	protected void toolHisAdapter() {
		try {
        	List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				if (objects.size() > 1) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				}
				List<Tool> checkLots = new ArrayList<Tool>();
				for (Object object : objects) {
					checkLots.add((Tool)object);
				}
				ToolHisQueryDialog hisQueryDialog = new ToolHisQueryDialog("ToolHisQueryDialog", null, eventBroker, checkLots);
				if (Dialog.OK == hisQueryDialog.open()) {
					refresh();
				}
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
}
