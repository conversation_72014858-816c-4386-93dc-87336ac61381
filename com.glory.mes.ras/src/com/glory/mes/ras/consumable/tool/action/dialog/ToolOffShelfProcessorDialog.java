package com.glory.mes.ras.consumable.tool.action.dialog;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.mes.ras.consumable.tool.action.ToolActionDialog;
import com.glory.framework.core.exception.ExceptionBundle;

public class ToolOffShelfProcessorDialog extends ToolActionDialog{

	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;

	private static final String FIELD_TOOLMULITLIST = "toolMulitList";

	protected ListTableManagerField toolMulitListField;

	protected List<Tool> tools;

	public ToolOffShelfProcessorDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	public ToolOffShelfProcessorDialog(String adFormName, String authority, IEventBroker eventBroker, List<Tool> tools) {
		super(adFormName, authority, eventBroker);
		this.tools = tools;
		setToolsList(tools);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		toolMulitListField = form.getFieldByControlId(FIELD_TOOLMULITLIST, ListTableManagerField.class);
		initLot();
	}

	@Override
	public void initLot() {
		try {
			((CheckBoxTableViewerManager)toolMulitListField.getListTableManager().getTableManager()).setCheckBoxDisableListener(new ICheckBoxDisableListener() {
    			public boolean isDisable(Object object) {
    				MLot lot = (MLot)object;
    				if (lot.getConstraintFlag()) {
    					return true;
    				}
    				return false;
    			}
    		});
			toolMulitListField.getListTableManager().setInput(tools);
      		if (tools != null && tools.size() > 0) {
      			List<MLot> validLots = new ArrayList<MLot>();
      			for (MLot lot : tools) {
      				if (!lot.getConstraintFlag()) {
      					validLots.add(lot);
    				}
          		}
      			toolMulitListField.getListTableManager().getCheckedObject().addAll(validLots);
      			toolMulitListField.getListTableManager().refresh();
      		}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected void okPressed() {
		try {
			List<Object> objects = toolMulitListField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showError(Message.getString("ras.not_select_tool"));
				return;
			}
			tools = objects.stream().map(o -> ((Tool)o)).collect(Collectors.toList());
			ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
			consumableManager.toolOffShelf((List)tools, null, null, null, null, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		super.okPressed();
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}

	@Override
	public boolean checkMLotState(Tool tool) {
		if (Tool.HOLDSTATE_ON.equals(tool.getHoldState())) {
			return false;
		}else {
			// 判断上下架是否改变物料批状态
			boolean isChange = false;
			try {
				SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
				isChange = MesCfMod.isMLotChangeStateByShelfAction(Env.getOrgRrn(), sysParameterManager);
			} catch (Exception e) {
				e.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e);
			}
			if (isChange && MaterialState.STATE_INSHELF.equals(tool.getState())) {
				return true;
			} else if (!isChange && MaterialState.STATE_IN.equals(tool.getState())) {
				return true;
			} else {
				return false;
			}
		}
	
	}

	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
