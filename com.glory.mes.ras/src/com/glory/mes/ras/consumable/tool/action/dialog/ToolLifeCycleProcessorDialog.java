package com.glory.mes.ras.consumable.tool.action.dialog;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.DateTimeField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.ras.consumable.tool.action.ToolActionDialog;
import com.glory.framework.core.exception.ExceptionBundle;

public class ToolLifeCycleProcessorDialog extends ToolActionDialog { 
	
	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;

	private static final String FIELD_TOOLACTION = "toolAction";
	private static final String FIELD_TOOLMULITLIST = "toolMulitList";
	private static final String FIELD_LIMITLIFE = "limitLife";
	private static final String FIELD_PERIODVALIDITY = "periodValidity";

	protected EntityFormField toolActionField;
	protected ListTableManagerField toolMulitListField;
	protected TextField limitLifeField;
	protected DateTimeField periodValidityField;
	
	protected List<Tool> tools;

	public ToolLifeCycleProcessorDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	public ToolLifeCycleProcessorDialog(String adFormName, String authority, IEventBroker eventBroker, List<Tool> tools) {
		super(adFormName, authority, eventBroker);
		this.tools = tools;
		setToolsList(tools);
	}
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		toolActionField = form.getFieldByControlId(FIELD_TOOLACTION, EntityFormField.class);
		toolMulitListField = form.getFieldByControlId(FIELD_TOOLMULITLIST, ListTableManagerField.class);
		limitLifeField = toolActionField.getFieldByControlId(FIELD_LIMITLIFE, TextField.class);
		periodValidityField = toolActionField.getFieldByControlId(FIELD_PERIODVALIDITY, DateTimeField.class);
		
		initLot();
	}

	@Override
	public void initLot() {
		try {
			((CheckBoxTableViewerManager)toolMulitListField.getListTableManager().getTableManager()).setCheckBoxDisableListener(new ICheckBoxDisableListener() {
    			public boolean isDisable(Object object) {
    				MLot lot = (MLot)object;
    				if (lot.getConstraintFlag()) {
    					return true;
    				}
    				return false;
    			}
    		});
			toolMulitListField.getListTableManager().setInput(tools);
      		if (tools != null && tools.size() > 0) {
      			List<MLot> validLots = new ArrayList<MLot>();
      			for (MLot lot : tools) {
      				if (!lot.getConstraintFlag()) {
      					validLots.add(lot);
    				}
          		}
      			toolMulitListField.getListTableManager().getCheckedObject().addAll(validLots);
      			toolMulitListField.getListTableManager().refresh();
      		}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	
	}
	
	@Override
	protected void okPressed() {
		try {
			List<Object> objects = toolMulitListField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showError(Message.getString("ras.not_select_tool"));
				return;
			}
			tools = objects.stream().map(o -> ((Tool)o)).collect(Collectors.toList());
			if (toolActionField.validate()) {
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				//获取失效时间
				Date date = (Date) periodValidityField.getValue();
				//获取限制使用次数值
				if (date == null && StringUtil.isEmpty(limitLifeField.getValue().toString())) {
					UI.showInfo(Message.getString("ras.please_input_change_vlaue"));
					return;
				}
				Long limitLife = null;
				if (!StringUtil.isEmpty(limitLifeField.getValue().toString())) {
					limitLife = Long.parseLong(limitLifeField.getValue().toString());
				}
				consumableManager.changeToolFloorLifeExpire((List)tools, date, limitLife, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			} else {
				return;
			}
		} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	} 
		super.okPressed();
	}

	@Override
	public boolean isSupportMulitLot() {
		return true;
	}

	@Override
	public boolean checkMLotState(Tool tool) {
		if(MLot.HOLDSTATE_ON.equals(tool.getHoldState())) {
			return false;
		} else {
			return true;
		}	
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}