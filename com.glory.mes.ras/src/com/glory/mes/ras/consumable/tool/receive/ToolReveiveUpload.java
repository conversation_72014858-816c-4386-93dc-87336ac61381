package com.glory.mes.ras.consumable.tool.receive;

import java.util.ArrayList;
import java.util.List;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.model.ToolMaterial;


public class ToolReveiveUpload extends Upload {
	protected FixEditorTableManager manager;
	protected List<Tool> upLoadLots = new ArrayList<Tool>();
	
	public ToolReveiveUpload(String name,FixEditorTableManager manager,List<Tool> upLoadLots) {
		super(name);
		this.manager = manager;
		this.upLoadLots = upLoadLots;
	}
	
	public ToolReveiveUpload(String authorityName, String buttonName, String name, FixEditorTableManager manager,List<Tool> upLoadLots) {
		super(authorityName, buttonName, name);
		this.manager = manager;
		this.upLoadLots = upLoadLots;
	}

	@Override
	protected void cudEntityList() {
		try {
			List<ADBase> uploadList = progress.getUploadList(); 
	        ADManager adManager = Framework.getService(ADManager.class);
	        // 若批次中一条数据不符合，则该批次不导入
	        List<Tool> tools = new ArrayList<Tool>();
	        if (uploadList != null && uploadList.size() > 0) {
	        	for(int i = 0; i < uploadList.size(); i++) {
	        		Tool loadTool =  (Tool) uploadList.get(i);
	        		if (loadTool.getmLotId() != null && !"".equals(loadTool.getmLotId())) {
	        			List<ToolMaterial> specList = adManager.getEntityList(Env.getOrgRrn(), ToolMaterial.class, Env.getMaxResult(), " name = '" + loadTool.getMaterialName() + "' AND status = 'Active'", "");
	        			loadTool.setMaterialVersion(specList.get(0).getVersion());
	        			loadTool.setmLotType(specList.get(0).getMaterialType());
	        			loadTool.setLimitLife(specList.get(0).getLimitLife());
	        			loadTool.setGrade1(specList.get(0).getGroup1());
	        			loadTool.setGrade2(specList.get(0).getSpec1());
	        			tools.add(loadTool);	
	        		}
	        	}
	        }
	        manager.setInput(tools);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
