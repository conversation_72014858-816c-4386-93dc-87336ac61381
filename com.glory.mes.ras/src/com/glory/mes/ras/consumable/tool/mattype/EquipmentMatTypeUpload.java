package com.glory.mes.ras.consumable.tool.mattype;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.excel.UploadErrorLog;
import com.glory.framework.base.excel.UploadException;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.EquipmentMatType;
import com.glory.framework.core.exception.ExceptionBundle;

public class EquipmentMatTypeUpload extends Upload  {

	private static final String CLEAN_REFNAME = "CleanEqpType";
	private List<EquipmentMatType> equipmentMatTypes = new ArrayList<>();
	private List<String> cleanEqp = new ArrayList<String>();
	
	public EquipmentMatTypeUpload(String name) {
		super(name);
		getCleanEqp();
	}
	
	public EquipmentMatTypeUpload(String authorityName, String buttonName, String name) {
		super(authorityName, buttonName, name);
		getCleanEqp();
	}
	
	@Override
	public void cudEntityList() {
		try {
			ADManager manager = Framework.getService(ADManager.class);
			equipmentMatTypes.forEach(eqp -> {
				manager.deleteEntityList(Env.getOrgRrn(), "EquipmentMatType", "equipmentId = '" + eqp.getEquipmentId() + "'");
			});
			manager.cudEntityList(equipmentMatTypes, null, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean preCheck(List<ADBase> uploadList, List<ADBase> deleteList) throws UploadException {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			RASManager rasManager = Framework.getService(RASManager.class);
			EquipmentMatType matType = null;
			Equipment equipment = null;
			List<ADURefList> list = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, 
					Env.getMaxResult(), "referenceName = 'MainMatType'", "");
			List<String> mainType = list.stream().map(ADURefList :: getText).collect(Collectors.toList());
			for(int i = 0; i < uploadList.size(); i++) {
				EquipmentMatType equipmentMatType = (EquipmentMatType) uploadList.get(i);
				boolean flag = true;//支持两种格式
				if (StringUtil.isEmpty(equipmentMatType.getEquipmentId())) { 
					flag = false;
				} 
				if (equipment != null && equipment.getEquipmentId().equals(equipmentMatType.getEquipmentId())) {
					flag = false;
				} 
				matType = new EquipmentMatType();
				if(!flag) {
					if(equipment != null) {
						matType.setEquipmentId(equipment.getEquipmentId());
						matType.setEquipmentRrn(equipment.getObjectRrn());
					}
				} else {
					equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentMatType.getEquipmentId(), false);
					if (equipment == null) {
						UploadErrorLog errorLog = new UploadErrorLog(new Long(i) + 2, null, "EQUIPMENT", equipmentMatType.getEquipmentId() + ":" + Message.getString("ras.equipment_no_found"));
	    				progress.getErrLogs().add(errorLog);
	    				continue;
					}
					if(!cleanEqp.contains(equipment.getEqpType())) {
						UploadErrorLog errorLog = new UploadErrorLog(new Long(i) + 2, null, "EQUIPMENT", equipmentMatType.getEquipmentId() + ": not cleaning equipment");
	    				progress.getErrLogs().add(errorLog);
	    				continue;
					}
					matType.setEquipmentId(equipmentMatType.getEquipmentId());
					matType.setEquipmentRrn(equipment.getObjectRrn());
				}
			
				if(!mainType.contains(equipmentMatType.getMainMatType())) {
					UploadErrorLog errorLog = new UploadErrorLog(new Long(i) + 2, null, "MAINMATTYPE", equipmentMatType.getMainMatType() + ":Not Carrier Main Material Type!");
    				progress.getErrLogs().add(errorLog);
    				continue;
				}
				matType.setMainMatType(equipmentMatType.getMainMatType());
				equipmentMatTypes.add(matType);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return progress.isSuccess();
	}
	
	private void getCleanEqp(){
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADURefList> refLists = adManager.getADURefList(Env.getOrgRrn(), CLEAN_REFNAME);
			List<String> cleanEqp = refLists.stream().map(ADURefList :: getKey).collect(Collectors.toList());
			if(cleanEqp != null && cleanEqp.size() > 0) {
				this.cleanEqp.addAll(cleanEqp);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
