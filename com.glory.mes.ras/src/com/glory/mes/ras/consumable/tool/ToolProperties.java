package com.glory.mes.ras.consumable.tool;

import java.math.BigDecimal;

import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.framework.core.exception.ExceptionBundle;

public class ToolProperties extends EntityProperties {
	
	public ToolProperties() {
		super();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		section.setTextClient(tBar);
	}
	
	@Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().setAutoUpdate(false);
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();			
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());		
					}
					
					Tool tool = (Tool) getAdObject();
					if (tool.getMainQty() == null || tool.getMainQty().compareTo(BigDecimal.ZERO) == 0) {
						tool.setMainQty(BigDecimal.ONE);
					}
					
					ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
					Tool obj = (Tool) consumableManager.saveConsumable(tool, Env.getSessionContext());
							
					ADManager entityManager = Framework.getService(ADManager.class);			
					ADBase newBase = entityManager.getEntity(obj);
					if (oldBase.getObjectRrn() == null) {
						getMasterParent().refreshAdd(newBase);
					} else {
						getMasterParent().refreshUpdate(newBase);
					}
					
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));				
					//设置当前选中值
					getMasterParent().getTableManager().setSelection(new StructuredSelection(new Object[] {newBase}));
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			form.getMessageManager().setAutoUpdate(true);
		}
	}

	@Override
	public boolean delete() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				if (getAdObject().getObjectRrn() != null) {
					ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
					consumableManager.deleteConsumable((Tool) getAdObject(), Env.getSessionContext());
					setAdObject(createAdObject());
					refresh();
					return true;
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		return false;
	}
	
	@Override
	public void newAdapter() {
		super.newAdapter();
	}

}
