package com.glory.mes.ras.consumable.tool.processor;

import java.math.BigDecimal;
import java.util.List;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.framework.core.exception.ExceptionBundle;

public class ToolOutProcessor extends AbstractToolProcessor {

	public ToolOutProcessor(boolean isBatch) {
		super(isBatch, false);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
			consumableManager.outConsumables((List)lots, null, new ConsumableAction(), 
					Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			return mmManager.checkMLotState(mLot, MaterialEvent.EVENT_INVENTORYOUT, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
	}
	

}
