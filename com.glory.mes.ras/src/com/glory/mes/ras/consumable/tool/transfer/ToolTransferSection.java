package com.glory.mes.ras.consumable.tool.transfer;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.mes.ras.consumable.tool.ToolSection;

/**
 * 仓库变更<br/>
 * 1.当前只支持批次只存储在一个位置的情况<br/>
 * 
 * <AUTHOR>
 *
 */
public class ToolTransferSection extends ToolSection {
	private ToolTransferFrom itemForm;

	public ToolTransferSection(ADTable adTable) {
		super(adTable);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemMove(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemMove(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString("mm.move_warehouse"));
		itemSave.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				moveAdapter();
			}
		});
	}

	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		itemForm = new ToolTransferFrom(composite, SWT.NONE, tab, mmng);
		return itemForm;
	}

	// 转库
	private void moveAdapter() {
		form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				Tool tool = (Tool) getAdObject();
				if (tool == null) {
					return;
				}
				try {
					MMManager mmManager = Framework.getService(MMManager.class);

					List<MLotStorage> storages = mmManager.getLotStorages(tool.getObjectRrn());
					if (CollectionUtils.isEmpty(storages)) {
						throw new ClientException("mm.mlot_must_specify_lotstorage");
					}

					if (storages.size() > 1) {
						throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
					}
					MLotStorage storage = storages.get(0);

					tool.setTransWarehouseRrn(storage.getWarehouseRrn());
					tool.setTransStorageType(storage.getStorageType());
					tool.setTransStorageId(storage.getStorageId());

					tool.setTransTargetWarehouseRrn(itemForm.getWarehouseRrn());

					/*
					 * if (itemForm.getRackAreaRrn() != null) {
					 * tool.setTransTargetStorageType(Storage.CATEGORY_RACKAREA);
					 * 
					 * Storage rackAeraStorage = getStorage(itemForm.getRackAreaRrn());
					 * tool.setTransTargetStorageId(rackAeraStorage.getName()); } else if
					 * (itemForm.getRackRrn() != null) {
					 * tool.setTransTargetStorageType(Storage.CATEGORY_RACK);
					 * 
					 * Storage rackStorage = getStorage(itemForm.getRackRrn());
					 * tool.setTransTargetStorageId(rackStorage.getName()); } else { // 只是修改所在仓库
					 * tool.setTransStorageType(null); tool.setTransStorageId(null); }
					 */

					String storageKey = storage.getWarehouseRrn() + storage.getStorageType() + storage.getStorageId();
					String changeKey = tool.getTransTargetWarehouseRrn() + tool.getTransTargetStorageType()
							+ tool.getTransTargetStorageId();

					// 未做改变，提示后返回
					if (storageKey.equals(changeKey)) {
						UI.showInfo(Message.getString("mm.transfer_no_change"));
						return;
					}

					ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
					tool = consumableManager.transferTool(tool, new ConsumableAction(), tool.getTransWarehouseRrn(),
							null, tool.getTransStorageType(), tool.getTransStorageId(),
							tool.getTransTargetWarehouseRrn(), null, tool.getTransTargetStorageType(),
							tool.getTransTargetStorageId(), Env.getSessionContext());
					UI.showInfo(Message.getString("wip.transfer_success"));
					setAdObject(tool);
					refresh();
				} catch (Exception e) {
					e.printStackTrace();
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	}

	/*
	 * private Storage getStorage(Long storageRrn) throws Exception { MMManager
	 * mmManager = Framework.getService(MMManager.class); Storage storage = new
	 * Storage(); storage.setObjectRrn(storageRrn); return
	 * mmManager.getStorage(storage); }
	 */

	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);

		if (adObject != null && adObject.getObjectRrn() != null) {
			itemForm.loadCurrent(adObject.getObjectRrn());
		}
	}

	@Override
	public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);

		ADBase adObject = getAdObject();
		if (adObject != null && adObject.getObjectRrn() != null) {
			Tool tool = (Tool) adObject;

			if (Tool.HOLDSTATE_ON.equals(tool.getHoldState())) {
				itemSave.setEnabled(false);
			} else if (MaterialState.STATE_IN.equals(tool.getState())) {
				itemSave.setEnabled(true);
			} else {
				itemSave.setEnabled(false);
			}
		}
	}

}
