package com.glory.mes.ras.consumable.tool.processor;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;

public class ToolUnShipProcessor extends AbstractToolProcessor {

	private static final String TABLE_NAME = "RASToolProcessorUnShip";
	private static final String TABLE_NAME_MLOT_LIST = "RASToolProcessorUnShipList";

	private static final String TRANSWAREHOUSERRN = "transWarehouseRrn";
	
	private EntityForm entityForm;
	private IMessageManager mmng;
	
	public ToolUnShipProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				for(MLot mLot : lots) {
					Tool tool = (Tool) mLot;
					Tool moveTool = (Tool) entityForm.getObject();
					tool.setTransWarehouseRrn(moveTool.getTransWarehouseRrn());
					MMManager mmManager = Framework.getService(MMManager.class);
					
					// 不支持多储位
					List<MLotStorage> storages = mmManager.getLotStorages(tool.getObjectRrn());
					
					if (CollectionUtils.isNotEmpty(storages)) {
						if (storages.size() > 1) {
							UI.showWarning(Message.getString("wms.lot_in_multi_warehouse_or_storage"));
							return false;
						}
						MLotStorage storage = storages.get(0);
						
						String storageKey = storage.getWarehouseRrn() + storage.getStorageType()
						+ storage.getStorageId();
						String returnKey = tool.getTransWarehouseRrn() + tool.getTransStorageType()
						+ tool.getTransStorageId();
						
						// 检查与当前储位是否一致
						if (!storageKey.equals(returnKey)) {
							Warehouse warehouse = new Warehouse();
							warehouse.setObjectRrn(storage.getWarehouseRrn());
							warehouse = mmManager.getWarehouse(warehouse);
							if (!StringUtil.isEmpty(storage.getStorageType())) {
								// 当前没有处理库房
								if (Storage.CATEGORY_RACK.equals(storage.getStorageType())) {
									UI.showWarning(String.format(Message.getString("mm.mlot_was_stored2"),
											warehouse.getWarehouseId(), storage.getStorageId()));
									return false;
								} else {
									UI.showWarning(String.format(Message.getString("mm.mlot_was_stored3"),
											warehouse.getWarehouseId(), storage.getStorageId()));
									return false;
								}
							} else {
								UI.showWarning(String.format(Message.getString("mm.mlot_was_stored1"),
										warehouse.getWarehouseId()));
								return false;
							}
						}
					}
				}
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				consumableManager.returnTool((List)lots, Tool.RECEIVE_TYPE_IN, new ConsumableAction(), Env.getSessionContext());
			} else {
				return false;
			}
		} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	} finally {
      		mmng.setAutoUpdate(true);
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {	
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		ManagedForm mform = new ManagedForm(toolkit, form);
		mmng = mform.getMessageManager();
		
		Composite body = form.getBody();
		configureBody(body);
		
		entityForm = new EntityForm(body, SWT.NONE, new Tool(), getADTable(), mmng);
		entityForm.setLayout(new GridLayout(1, false));
		entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	@Override
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("ToolUnShipProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("ToolUnShipProcessor getListADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}
	
	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldWoId = new ADField();
		adFieldWoId.setName(TRANSWAREHOUSERRN);
		adFieldWoId.setIsMain(true);
		adFieldWoId.setIsDisplay(true);
		adFieldWoId.setIsEditable(true);
		adFieldWoId.setDisplayLength(15l);
		adFieldWoId.setLabel(Message.getString("wms.target_warehouse"));
		adFieldWoId.setLabel_zh(Message.getString("wms.target_warehouse"));
		adFieldWoId.setDataType("string");
		adFieldWoId.setDisplayType("reftable");
		adFieldWoId.setReftableRrn(4335030L);
		adFieldWoId.setIsMandatory(true);
		adFields.add(adFieldWoId);

		adTable.setFields(adFields);
		
		return adTable;
	}
	
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> adFields = adTable.getFields();
		
		ADField adFieldWoId = new ADField();
		adFieldWoId.setName(TRANSWAREHOUSERRN);
		adFieldWoId.setIsMain(true);
		adFieldWoId.setIsDisplay(true);
		adFieldWoId.setIsEditable(true);
		adFieldWoId.setDisplayLength(15l);
		adFieldWoId.setLabel(Message.getString("wms.target_warehouse"));
		adFieldWoId.setLabel_zh(Message.getString("wms.target_warehouse"));
		adFieldWoId.setDataType("string");
		adFieldWoId.setDisplayType("reftable");
		adFieldWoId.setReftableRrn(4335030L);
		adFields.add(adFieldWoId);
		
		return adTable;
	}

	@Override
	public boolean checkMLotState(MLot lot) {
		if(Tool.HOLDSTATE_ON.equals(lot.getHoldState())) {
			return false;
		} else {
			return true;
		}
	}
}
