package com.glory.mes.ras.consumable.tool.processor;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.state.model.MaterialState;

public class ToolOnShelfProcessor extends AbstractToolProcessor{
	private static final String TABLE_NAME = "RASToolProcessorOnShelf";
	private static final String TABLE_NAME_MLOT_LIST = "RASToolProcessorOnShelfList";
	
	private ToolOnShelfProcessorFrom entityForm;
	private IMessageManager mmng;
	
	private MLot mLot;
	
	public ToolOnShelfProcessor(boolean isBatch, MLot mLot) {
		super(isBatch);
		this.mLot = mLot;
	}

	@Override
	public boolean process(List<MLot> mlots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			entityForm.setObject(mLot);
			if (entityForm.saveToObject()) {
				MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
				List<MLotStorage> storages =  mmManager.getLotStorages(mlots.get(0).getObjectRrn());
				if (CollectionUtils.isEmpty(storages)) {
					throw new ClientException("mm.mlot_must_specify_lotstorage");
				}
				
				if (storages.size() > 1) {
					throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
				}
				MLotStorage storage = storages.get(0);
				
				mlots.get(0).setTransWarehouseRrn(storage.getWarehouseRrn());
				mlots.get(0).setTransStorageType(storage.getStorageType());
				mlots.get(0).setTransStorageId(storage.getStorageId());
				
				mlots.get(0).setTransTargetWarehouseRrn(storage.getWarehouseRrn());
				
				if (entityForm.getRackAreaRrn() != null) {
					mlots.get(0).setTransTargetStorageType(Storage.CATEGORY_RACKAREA);
					
					Storage rackAeraStorage = getStorage(entityForm.getRackAreaRrn());
					mlots.get(0).setTransTargetStorageId(rackAeraStorage.getName());
				} else if (entityForm.getRackRrn() != null) {
					mlots.get(0).setTransTargetStorageType(Storage.CATEGORY_RACK);
					
					Storage rackStorage = getStorage(entityForm.getRackRrn());
					mlots.get(0).setTransTargetStorageId(rackStorage.getName());
				}
				
				on((Tool)mlots.get(0), storage.getWarehouseRrn(), mlots.get(0).getTransTargetStorageType(), mlots.get(0).getTransTargetStorageId());
			} else {
				return false;
			}
			
		} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	} finally {
      		mmng.setAutoUpdate(true);
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {	
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		ManagedForm mform = new ManagedForm(toolkit, form);
		mmng = mform.getMessageManager();
		
		Composite body = form.getBody();
		configureBody(body);
		entityForm = new ToolOnShelfProcessorFrom(body, SWT.NONE, new Tool(), getADTable(), mmng);
		entityForm.setLayout(new GridLayout(1, false));
		entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		entityForm.loadCurrent(mLot.getObjectRrn());
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	@Override
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("ToolOnShelfProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("ToolOnShelfProcessor getADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}
	
	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		//目标仓库
		ADField adField1 = new ADField();
		adField1.setName("targetWarehouseRrn");
		adField1.setIsMain(true);
		adField1.setIsDisplay(true);
		adField1.setIsEditable(true);
		adField1.setDisplayLength(15l);
		adField1.setLabel(Message.getString("wms.target_warehouse"));
		adField1.setLabel_zh(Message.getString("wms.target_warehouse"));
		adField1.setDataType("string");
		adField1.setDisplayType("reftable");
		adField1.setReftableRrn(4335030l);
//		adField1.setDefaultValue(warehouseRrn.toString());
		adField1.setIsReadonly(true);
		adField1.setIsMandatory(true);
		adFields.add(adField1);
		
		//目标货架
		ADField adField2 = new ADField();
		adField2.setName("targetRackRrn");
		adField2.setIsMain(true);
		adField2.setIsDisplay(true);
		adField2.setIsEditable(true);
		adField2.setDisplayLength(15l);
		adField2.setLabel(Message.getString("wms.target_shelf"));
		adField2.setLabel_zh(Message.getString("wms.target_shelf"));
		adField2.setDataType("string");
		adField2.setDisplayType("reftable");
		adField2.setReftableRrn(128951l);
		adField2.setIsMandatory(true);
		adFields.add(adField2);
		
		//目标储位
		ADField adField3 = new ADField();
		adField3.setName("targetRackAreaRrn");
		adField3.setIsMain(true);
		adField3.setIsDisplay(true);
		adField3.setIsEditable(true);
		adField3.setDisplayLength(15l);
		adField3.setLabel(Message.getString("wms.target_storage"));
		adField3.setLabel_zh(Message.getString("wms.target_storage"));
		adField3.setDataType("string");
		adField3.setDisplayType("reftable");
		adField3.setReftableRrn(128952l);
		adFields.add(adField3);
		
		adTable.setFields(adFields);
		return adTable;
	}
	
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		return adTable;
	}

	@Override
	public boolean checkMLotState(MLot mlot) {
		if (Tool.HOLDSTATE_ON.equals(mlot.getHoldState())) {
			return false;
		} else {
			if (MaterialState.STATE_IN.equals(mlot.getState())) {
				return true;
			} else {
				return false;
			}
		}
	}
	
	public Tool on(Tool tool, Long toWarehouseRrn, String toStorageType, String toStorageId) throws Exception {
		Warehouse warehouse = new Warehouse();
		warehouse.setObjectRrn(toWarehouseRrn);
		MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
		warehouse = mmManager.getWarehouse(warehouse);
		
		ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
		return consumableManager.toolOnShelf(tool, null, warehouse, 
				tool.getTransTargetStorageType(), tool.getTransTargetStorageId(), Env.getSessionContext());
	}
	
	private Storage getStorage(Long storageRrn) throws Exception {
		MMManager mmManager = Framework.getService(MMManager.class);
		Storage storage = new Storage();
		storage.setObjectRrn(storageRrn);
		return mmManager.getStorage(storage);
	}
}
