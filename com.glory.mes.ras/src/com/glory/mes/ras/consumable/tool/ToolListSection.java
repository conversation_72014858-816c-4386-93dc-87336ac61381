package com.glory.mes.ras.consumable.tool;

import java.util.LinkedList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.ras.consumable.IConsumableChangeListener;

public class ToolListSection extends EntityListSection {
	protected List<IConsumableChangeListener> toolChangeListeners = new LinkedList<IConsumableChangeListener>();
	private static final Logger logger = Logger.getLogger(ToolListSection.class);
	public Text txtTool;
	public ADBase adObject;

	public ToolListSection(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("ras.Tool_id") + "：");
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtTool = toolkit.createText(top, "", SWT.BORDER);
		GridData gText = new GridData();
		gText.widthHint = 216;
		txtTool.setLayoutData(gText);
		txtTool.setTextLimit(32);
		txtTool.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					MLot mLot = null;
					String lotId = tLotId.getText();
					tLotId.setText(lotId);
					mLot = searchtTool(lotId);
					tLotId.selectAll();
					if (mLot == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
							setAdObject(new MLot());
						} catch (Exception en) {
							logger.error("createADObject error at searchEntity Method!");
						}
					} else {
						setAdObject(mLot);
					}
					refresh();
					break;
				}
			}

		});
		txtTool.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				tLotId.setText(tLotId.getText());
			}
		});

		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
	}

	public void setFocus() {
		txtTool.setFocus();
	}

	@SuppressWarnings("unused")
	private Tool searchtTool(String toolId) {
		try {
			ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
			Tool tool = consumableManager.getToolById(Env.getOrgRrn(), toolId);
			notifyLotChangeListeners(this, tool);
			return tool;
		} catch (Exception e) {
			logger.error("Cannot find this Tool by this ToolId!");
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	@Override
	public void refresh() {
		super.refresh();
		if (txtTool != null) {
			txtTool.selectAll();
		}
	}

	public void notifyLotChangeListeners(Object sender, Tool newTool) {
		synchronized (toolChangeListeners) {
			for (IConsumableChangeListener listener : toolChangeListeners) {
				try {
					listener.toolChanged(sender, newTool);
				} catch (Throwable t) {
					t.printStackTrace();
				}
			}
		}
	}

	public void setAdObject(ADBase adObject) {
		this.adObject = adObject;
	}

	public ADBase getAdObject() {
		return adObject;
	}
}
