package com.glory.mes.ras.consumable.tool.unscrap;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;

public class ToolUnScrapSection extends EntitySection{
	private static final Logger logger = Logger.getLogger(EntitySection.class);

    protected ToolItem itemUnscrap;
    protected ToolUnScrapForm itemForm;
    public Text txtLot;

    public ToolUnScrapSection() {
        super();
    }

    public ToolUnScrapSection(ADTable table) {
        super(table);
    }

    @Override
    public void createContents(IManagedForm form, Composite parent) {
        super.createContents(form, parent);
        section.setText(Message.getString("wip.unscrap_sectiontitle"));
        initAdObject();
    }

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtLot = toolkit.createText(top, "", SWT.BORDER);
		GridData gText = new GridData();
		gText.widthHint = 216;
		txtLot.setLayoutData(gText);
		txtLot.setTextLimit(32);
		txtLot.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Tool tool = null;
					String mlotId = tLotId.getText();
					mlotId = mlotId.toUpperCase();
					tLotId.setText(mlotId);
					tool = (Tool) searchLot(mlotId);
					tLotId.selectAll();
					if (tool == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
							setAdObject(createAdObject());
						} catch (Exception en) {
							logger.error("createADObject error at searchEntity Method!");
						}
					} else {
						setAdObject(tool);
					}
					refresh();
					break;
				}
			}

		});
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String mlotId = tLotId.getText();
				mlotId = mlotId.toUpperCase();
				tLotId.setText(mlotId);
			}
		});

		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
	}

	@Override
	public void setFocus() {
		txtLot.setFocus();
	}
    
	public Tool searchLot(String mlotId) {
		try {
			if(mlotId == null) {
				return null;
			}
			ADManager adManager = Framework.getService(ADManager.class);
			String whereClause ="mLotId = " + "'" + mlotId + "'";
			List<Tool> tool = adManager.getEntityList(Env.getOrgRrn(), Tool.class, Integer.MAX_VALUE, whereClause, "");
			return tool.get(0);
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
    public void initAdObject() {
        setAdObject(new MLot());
        refresh();
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemHold(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    protected void createToolItemHold(ToolBar tBar) {
        itemUnscrap = new ToolItem(tBar, SWT.PUSH);
        itemUnscrap.setText(Message.getString("wip.unscrap_lot_by_scrap"));
        itemUnscrap.setImage(SWTResourceCache.getImage("unscrap"));
        itemUnscrap.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                unScrapAdapter(event);
            }
        });
    }

    protected void unScrapAdapter(SelectionEvent event) {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    for (IForm detailForm : getDetailForms()) {
                        if (detailForm instanceof ToolUnScrapForm) {
                        	ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
                            Tool tool = (Tool) this.getAdObject();
                            if (tool != null && tool.getObjectRrn() != null) {
                             // 检查批次是否有库存
//                                List<MLotStorage> storages = mmManager.getLotStorages(mlot.getObjectRrn());
//                                if ( storages== null || storages.size() == 0) {
//                                    UI.showError(Message.getString("mm.mlot_must_be_received"));
//                                    return;
//                                }

                            	ConsumableAction consumableAction = ((ToolUnScrapForm) detailForm).getConsumableAction();
                                List<ConsumableAction> consumableActions = new ArrayList<ConsumableAction>();
                                consumableActions.add(consumableAction);
                                consumableManager.unScrapConsumable(tool, consumableActions, Env.getSessionContext());
                                UI.showInfo(Message.getString("wip.unscrapLot_success"));// 弹出提示框
                                refresh();
                            }
                        }
                    }
                }
            }
            txtLot.setFocus();
        } catch (Exception e) {
            logger.error("HoldSection : holdAdapter()", e);
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
	protected IForm getForm(Composite composite, ADTab tab) {
        itemForm = new ToolUnScrapForm(composite, SWT.NONE, tab, mmng);
        return itemForm;
    }

    public void statusChanged(String newStatus) {
        if (newStatus != null) {
            itemUnscrap.setEnabled(true);
        } else {
            itemUnscrap.setEnabled(false);
        }
    }

    @Override
    public void refresh() {
        try {
            ADBase adBase = getAdObject();
            if (adBase != null && adBase.getObjectRrn() != null) {
                ADManager entityManager = Framework.getService(ADManager.class);
                setAdObject(entityManager.getEntity(adBase));
            }
            form.getMessageManager().removeAllMessages();
        } catch (Exception e1) {
            ExceptionHandlerManager.asyncHandleException(e1);
            return;
        }
        super.refresh();
    }

}
