package com.glory.mes.ras.consumable.tool.changelifecycle;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.forms.field.DateTimeField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;

public class ChangeToolLifeCycleSection extends QueryEntityListSection implements IRefresh {

	protected DateTimeField expireDateTimeField;
	protected Text limitLife;
	protected ToolItem itemChange;

	public ChangeToolLifeCycleSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChange(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	private void createToolItemChange(ToolBar tBar) {
		itemChange = new ToolItem(tBar, SWT.PUSH);
		itemChange.setText(Message.getString("wip.change"));
		itemChange.setImage(SWTResourceCache.getImage("wip_code"));
		itemChange.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeAdapter();
			}
		});
	}

	private void changeAdapter() {
		try {
			List<Object> objects = getCheckedObject();
			if (objects == null || objects.isEmpty()) {
				UI.showInfo(Message.getString("ras.please_select_change_life_cycle_tool"));
				return;
			}

			//获取失效时间
			Date date = (Date) expireDateTimeField.getValue();
			//获取限制使用次数值
			String count = limitLife.getText();
			if (date == null && count == null) {
				UI.showInfo(Message.getString("ras.please_input_change_vlaue"));
				return;
			}
			Long limitLife = null;
			if (count != null && !"".equals(count)) {
				limitLife = Long.parseLong(count);
			}
			List<Tool> tools = new ArrayList<Tool>();
			for (Object object : objects) {
				Tool tool = (Tool) object;
				tools.add(tool);
			}
			ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
			consumableManager.changeToolFloorLifeExpire(tools, date, limitLife, Env.getSessionContext());
			UI.showInfo(Message.getString("ras.change_tool_life_cycle_success"));
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public void createContents(IManagedForm form, Composite parent) {
		final FormToolkit toolkit = form.getToolkit();
		super.createContents(form, parent);
		
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		Composite fieldComposite = toolkit.createComposite(parent);
		fieldComposite.setLayout(new GridLayout(5, false));
		fieldComposite.setLayoutData(gd);
		
		GridLayout layout = new GridLayout();
	    layout.verticalSpacing = 0;
	    layout.marginHeight = 0;
		toolkit.createLabel(fieldComposite, Message.getString("ras.limitLife"), SWT.NULL);
		limitLife = toolkit.createText(fieldComposite, "", SWT.BORDER);
		limitLife.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
	    limitLife.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		int mStyle = SWT.READ_ONLY | SWT.BORDER;
		expireDateTimeField = new DateTimeField("", mStyle);
		expireDateTimeField.setLabel(Message.getString("mm_period_validity"));
		expireDateTimeField.createContent(fieldComposite, toolkit);
//		expireDateTimeField.getControls()[0].setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
}
