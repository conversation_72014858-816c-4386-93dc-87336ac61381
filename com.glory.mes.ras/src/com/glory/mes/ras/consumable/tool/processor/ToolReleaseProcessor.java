package com.glory.mes.ras.consumable.tool.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.lot.model.MLot;

public class ToolReleaseProcessor extends AbstractToolProcessor{
	private static final String TABLE_NAME = "RASToolProcessorRelease";
	private static final String TABLE_NAME_MLOT_LIST = "RASToolProcessorReleaseList";
	
	private static final String ACTIONCODE = "actionCode";
	private static final String ACTIONREASON = "actionReason";
	private static final String ACTIONCOMMENT = "actionComment";
	
	private EntityForm entityForm;
	private IMessageManager mmng;
	
	public ToolReleaseProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> mlots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				ConsumableAction consumableAction = (ConsumableAction) entityForm.getObject();
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				consumableManager.releaseConsumable((List)mlots, consumableAction, Env.getSessionContext());
			} else {
				return false;
			}
			
		} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	} finally {
      		mmng.setAutoUpdate(true);
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {	
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		ManagedForm mform = new ManagedForm(toolkit, form);
		mmng = mform.getMessageManager();
		
		Composite body = form.getBody();
		configureBody(body);
		
		entityForm = new EntityForm(body, SWT.NONE, new ConsumableAction(), getADTable(), mmng);
		entityForm.setLayout(new GridLayout(1, false));
		entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	@Override
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}
	
	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adField1 = new ADField();
		adField1.setName(ACTIONCODE);
		adField1.setIsMain(true);
		adField1.setIsDisplay(true);
		adField1.setIsEditable(true);
		adField1.setDisplayLength(15l);
		adField1.setLabel(Message.getString("wip.releasecode"));
		adField1.setLabel_zh(Message.getString("wip.releasecode"));
		adField1.setDataType("string");
		adField1.setDisplayType("userreflist");
		adField1.setReftableRrn(14479l);
		adField1.setUreflistName("ReleaseCode");
		adField1.setIsMandatory(true);
		adFields.add(adField1);
		
		ADField adField2 = new ADField();
		adField2.setName(ACTIONREASON);
		adField2.setIsMain(true);
		adField2.setIsDisplay(true);
		adField2.setIsEditable(true);
		adField2.setDisplayLength(15l);
		adField2.setLabel(Message.getString("wip.releasereason"));
		adField2.setLabel_zh(Message.getString("wip.releasereason"));
		adField2.setDataType("string");
		adField2.setDisplayType("text");
		adFields.add(adField2);
		
		ADField adField3 = new ADField();
		adField3.setName(ACTIONCOMMENT);
		adField3.setIsMain(true);
		adField3.setIsDisplay(true);
		adField3.setIsEditable(true);
		adField3.setDisplayLength(15l);
		adField3.setLabel(Message.getString("wip.comment"));
		adField3.setLabel_zh(Message.getString("wip.comment"));
		adField3.setDataType("string");
		adField3.setDisplayType("text");
		adFields.add(adField3);
		
		adTable.setFields(adFields);
		
		return adTable;
	}
	
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		return adTable;
	}


	@Override
	public boolean checkMLotState(MLot lot) {
		if(MLot.HOLDSTATE_OFF.equals(lot.getHoldState())) {
			return false;
		} else {
			return true;
		}	
	}
}
