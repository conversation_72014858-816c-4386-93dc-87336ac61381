package com.glory.mes.ras.consumable.tool.attach;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.ConsumableEqp;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.ras.eqp.Equipment;

public class ToolEqpAttachProperties extends EntityProperties {
	
	protected ToolItem itemAttach;
	protected ToolItem itemDttach;

	public ToolEqpAttachProperties() {
		super();
	}
	
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemAttach(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDetach(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemAttach(ToolBar tBar) {
		itemAttach = new ToolItem(tBar, SWT.PUSH);
		itemAttach.setText(Message.getString("ras.reticle_attach"));
		itemAttach.setImage(SWTResourceCache.getImage("mask_attach"));
		itemAttach.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				attachAdapter();
			}
		});
	}
	
	protected void createToolItemDetach(ToolBar tBar) {
		itemDttach = new ToolItem(tBar, SWT.PUSH);
		itemDttach.setText(Message.getString("ras.reticle_detach"));
		itemDttach.setImage(SWTResourceCache.getImage("mask_detach"));
		itemDttach.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				detachAdapter();
			}
		});
	}
	
	protected void attachAdapter() {
		try {
			form.getMessageManager().setAutoUpdate(false);
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());
					}
					
					Long equipmentRrn = ((ConsumableEqp)getAdObject()).getEquipmentRrn();
					Long toolRrn = ((ConsumableEqp)getAdObject()).getConsumableRrn();
					if (equipmentRrn == null || toolRrn == null) {
						return;
					}
					
					ADManager adManager = Framework.getService(ADManager.class);
					//检查治具是否已经绑定
					List<ConsumableEqp> reticleEqps = adManager.getEntityList(Env.getOrgRrn(), ConsumableEqp.class, 
							1, " consumableRrn = '" + toolRrn + "'", "");
					if (reticleEqps.size() > 0) {
						ConsumableEqp reticleEqp = reticleEqps.get(0);
						if (equipmentRrn.equals(reticleEqp.getEquipmentRrn())) {
							UI.showInfo(Message.getString("ras.tool_attach_eqp_repeat"));
							return;
						}
						UI.showInfo(Message.formatString("ras.tool_attach_other_eqp" + "#" + reticleEqp.getEquipmentId()));
						return;
					}
					
					Tool tool = new Tool();
					tool.setObjectRrn(toolRrn);
					tool = (Tool) adManager.getEntity(tool);
					
					Equipment equipment = new Equipment();
					equipment.setObjectRrn(equipmentRrn);
					equipment = (Equipment) adManager.getEntity(equipment);
					//判定设备类型绑定tool类型
					if (!equipment.getEqpType().equals(tool.getEqpType())) {
						UI.showError(Message.getString("ras.tool_equipment_type_not_match"));
						return;
					}
					
					ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);	
					consumableManager.attachEquipmentByConsumable(tool, equipment, "", "", false, Env.getSessionContext());
					UI.showInfo(Message.getString("ras.tool_attach_success"));
					getMasterParent().refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			form.getMessageManager().setAutoUpdate(true);
		}
	}
	
	protected void detachAdapter() {
		if (UI.showConfirm(Message.getString("ras.tool_detach_confirm"))) {
			try {
				form.getMessageManager().setAutoUpdate(false);
				form.getMessageManager().removeAllMessages();
				if (getAdObject() != null) {
					boolean saveFlag = true;
					for (IForm detailForm : getDetailForms()) {
						if (!detailForm.saveToObject()) {
							saveFlag = false;
						}
					}
					if (saveFlag) {
						for (IForm detailForm : getDetailForms()) {
							PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());
						}
						
						Long equipmentRrn = ((ConsumableEqp)getAdObject()).getEquipmentRrn();
						Long toolRrn = ((ConsumableEqp)getAdObject()).getConsumableRrn();
						if (equipmentRrn == null || toolRrn == null) {
							return;
						}
						
						ADManager adManager = Framework.getService(ADManager.class);
						//检查是否已经绑定
						List<ConsumableEqp> toolEqps = adManager.getEntityList(Env.getOrgRrn(), ConsumableEqp.class, 
								1, " consumableRrn = '" + toolRrn + "'", "");
						ConsumableEqp toolEqp = null;
						if (toolEqps.size() > 0) {
							toolEqp = toolEqps.get(0);
							if (equipmentRrn.equals(toolEqp.getEquipmentRrn())) {
								Tool tool = new Tool();
								tool.setObjectRrn(toolRrn);
								tool = (Tool) adManager.getEntity(tool);
								Equipment equipment = new Equipment();
								equipment.setObjectRrn(equipmentRrn);
								equipment = (Equipment) adManager.getEntity(equipment);
								
								ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);	
								consumableManager.detachEquipmentByConsumable(tool, equipment, null, "", null, Env.getSessionContext());
								
								UI.showInfo(Message.getString("ras.tool_detach_success"));
								getMasterParent().refresh();
								return;
							}
						}
						
						UI.showInfo(Message.getString("ras.tool_no_attach_eqp"));
						getMasterParent().refresh();
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			} finally {
				form.getMessageManager().setAutoUpdate(true);
			}
		}
	}
	
}
