package com.glory.mes.ras.consumable.tool;

import java.util.LinkedList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.ras.consumable.IConsumableChangeListener;

public class ToolSection extends EntitySection {
	
	private static final Logger logger = Logger.getLogger(ToolSection.class);
	
	protected List<IConsumableChangeListener> toolChangeListeners = new LinkedList<IConsumableChangeListener>();
	
	public Text txtTool;
	public ADBase prestoreObject;
	
	public ToolSection() {
		super();
	}

	public ToolSection(ADTable table) {
		this(table, null);
	}
	
	public ToolSection(ADTable table, ADBase prestoreObject){
		super(table);
		this.prestoreObject = prestoreObject;
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("ras.Tool_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtTool = toolkit.createText(top, "", SWT.BORDER);
		GridData gText = new GridData();
		gText.widthHint = 216;
		txtTool.setLayoutData(gText);
		txtTool.setTextLimit(32);
		txtTool.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		txtTool.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					MLot mLot = null;
					String mlotId = tLotId.getText();
					if (!isMLotIdCaseSensitive()) {
						mlotId = mlotId.toUpperCase();
					}
					tLotId.setText(mlotId);
					mLot = searchTool(mlotId);
					tLotId.selectAll();
					if (mLot == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
	        				setAdObject(createAdObject());
	        			} catch(Exception en) {
	        				logger.error("createADObject error at searchEntity Method!");
	        			}
					} else {
						setAdObject(mLot);
					}
					refresh();
					break;
				}
			}

		});
		txtTool.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String mlotId = tLotId.getText();
				if (!isMLotIdCaseSensitive()) {
					mlotId = mlotId.toUpperCase();
				}
				tLotId.setText(mlotId);
			}
		});
		
		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
	}
	
	@Override
	public void setFocus() {
		txtTool.setFocus();
	}

	public Tool searchTool(String toolId) {
		try {
			ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
			Tool tool = consumableManager.getToolById(Env.getOrgRrn(), toolId);
			notifyLotChangeListeners(this, tool);
			return tool;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public void refresh() {
		super.refresh();
		if(txtTool != null) {
			txtTool.selectAll();
		}
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Tool newBase = (Tool) this.getAdObject();
		if (newBase != null) {
			statusChanged(newBase.getState());
		} else {
			statusChanged("");
		}
	}
	 
	public void initAdObject() {
		setAdObject(new Tool());
		refresh();
	}
	
	public void statusChanged(String newStatus){
	}

	public void setTool(Tool lot) {
		if (lot != null) {
			txtTool.setText(lot.getmLotId());
			setAdObject(lot);
			refresh();
		}
	}
	
	public void addLotChangeListener(IConsumableChangeListener listener) {
		synchronized (toolChangeListeners) {
			toolChangeListeners.add(listener);
		}
	}

	public void removeLotChangeListener(IConsumableChangeListener listener) {
		synchronized (toolChangeListeners) {
			toolChangeListeners.remove(listener);
		}
	}

	public void notifyLotChangeListeners(Object sender, Tool newTool) {
		synchronized (toolChangeListeners) {
			for (IConsumableChangeListener listener : toolChangeListeners) {
				try {
					listener.toolChanged(sender, newTool);
				} catch (Throwable t) {
					t.printStackTrace();
				}
			}
		}
	}
	
	// 跟随MLOT设置
	private Boolean isCaseSensitive;
	public boolean isMLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isMLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
