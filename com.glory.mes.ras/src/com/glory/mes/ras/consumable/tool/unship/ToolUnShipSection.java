package com.glory.mes.ras.consumable.tool.unship;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.ras.consumable.tool.ToolSection;

/**
 * 退仓<br/>
 * 1.当前只支持批次只存储在一个位置的情况<br/>
 * 2.没有针对库房做管理，一个仓库只有一个实体库房
 * 
 * <AUTHOR>
 *
 */
public class ToolUnShipSection extends ToolSection {

	protected ToolUnShipFrom itemForm;

	public ToolUnShipSection(ADTable adTable) {
		super(adTable);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemUnShip(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemUnShip(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString("wip.ship"));
		itemSave.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unshipAdapter();
			}
		});
	}

	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		itemForm = new ToolUnShipFrom(composite, SWT.NONE, tab, mmng);
		return itemForm;
	}

	// 退库
	protected void unshipAdapter() {
		form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				try {
					Tool tool = (Tool) getAdObject();
					if (tool == null) {
						return;
					}

					tool.setTransWarehouseRrn(itemForm.getWarehouseRrn());

					/*if (itemForm.getRackAreaRrn() != null) {
						tool.setTransStorageType(Storage.CATEGORY_RACKAREA);

						Storage rackAeraStorage = getStorage(itemForm.getRackAreaRrn());
						tool.setTransStorageId(rackAeraStorage.getName());
					} else if (itemForm.getRackRrn() != null) {
						tool.setTransStorageType(Storage.CATEGORY_RACK);

						Storage rackStorage = getStorage(itemForm.getRackRrn());
						tool.setTransStorageId(rackStorage.getName());
					} else {
						// 只是修改所在仓库
						tool.setTransStorageType(null);
						tool.setTransStorageId(null);
					}*/

					MMManager mmManager = Framework.getService(MMManager.class);

					// 不支持多储位
					List<MLotStorage> storages = mmManager.getLotStorages(tool.getObjectRrn());

					if (CollectionUtils.isNotEmpty(storages)) {
						if (storages.size() > 1) {
							UI.showWarning(Message.getString("wms.lot_in_multi_warehouse_or_storage"));
							return;
						}
						MLotStorage storage = storages.get(0);

						String storageKey = storage.getWarehouseRrn() + storage.getStorageType()
								+ storage.getStorageId();
						String returnKey = tool.getTransWarehouseRrn() + tool.getTransStorageType()
								+ tool.getTransStorageId();

						// 检查与当前储位是否一致
						if (!storageKey.equals(returnKey)) {
							Warehouse warehouse = new Warehouse();
							warehouse.setObjectRrn(storage.getWarehouseRrn());
							warehouse = mmManager.getWarehouse(warehouse);
							if (!StringUtil.isEmpty(storage.getStorageType())) {
								// 当前没有处理库房
								if (Storage.CATEGORY_RACK.equals(storage.getStorageType())) {
									UI.showWarning(String.format(Message.getString("mm.mlot_was_stored2"),
											warehouse.getWarehouseId(), storage.getStorageId()));
									return;
								} else {
									UI.showWarning(String.format(Message.getString("mm.mlot_was_stored3"),
											warehouse.getWarehouseId(), storage.getStorageId()));
									return;
								}
							} else {
								UI.showWarning(String.format(Message.getString("mm.mlot_was_stored1"),
										warehouse.getWarehouseId()));
								return;
							}
						}
					}

					ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
					tool = consumableManager.returnTool(tool, Tool.RECEIVE_TYPE_IN, new ConsumableAction(),
							tool.getTransWarehouseRrn(), tool.getTransWarehouseId(), tool.getTransStorageType(),
							tool.getTransStorageId(), Env.getSessionContext());
					UI.showInfo(Message.getString("common.return.success"));
					setAdObject(getADManger().getEntity(tool));
					refresh();
				} catch (Exception e) {
					e.printStackTrace();
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	}

	protected Storage getStorage(Long storageRrn) throws Exception {
		MMManager mmManager = Framework.getService(MMManager.class);
		Storage storage = new Storage();
		storage.setObjectRrn(storageRrn);
		return mmManager.getStorage(storage);
	}

	@Override
    public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);
		ADBase adObject = getAdObject();
		if (adObject != null && adObject.getObjectRrn() != null) {
			Tool tool = (Tool) adObject;
			
			if (Tool.HOLDSTATE_ON.equals(tool.getHoldState())) {
				itemSave.setEnabled(false);
			} else {
				if (newStatus != null) {
					itemSave.setEnabled(true);
		        } else {
		        	itemSave.setEnabled(false);
		        }
			}
		}
    }
	
}
