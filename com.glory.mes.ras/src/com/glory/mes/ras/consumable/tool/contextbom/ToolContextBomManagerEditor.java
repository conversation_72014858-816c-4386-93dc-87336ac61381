package com.glory.mes.ras.consumable.tool.contextbom;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.common.context.ContextActiveDialog;
import com.glory.common.context.ContextRuleDialog;
import com.glory.common.context.client.ContextManager;
import com.glory.common.context.custom.ContextEditCustomComposite;
import com.glory.common.context.custom.ContextObjectQueryCustomComposite;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextValue;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SnowFlake;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.AbstractBomLine;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.client.MMManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class ToolContextBomManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.ras/com.glory.mes.ras.consumable.tool.contextbom.ToolContextBomManagerEditor";

	public static final String BUTTON_NAME_SHOWRULE = "showRule";
	
	private static final String FIELD_CONTEXTBOMQUERY = "contextBomQuery";
	private static final String FIELD_CONTEXTBOMEDIT = "contextBomEdit";
	private static final String FIELD_CONTEXTINFO = "contextInfo";
	private static final String FIELD_BOMINFO = "bomInfo";

	protected CustomField  contextBomQueryField;
	protected GlcFormField contextBomEditField;
	protected CustomField contextInfoField;
	protected CustomField bomInfoField;

	private static final String CONTEXT_NAME = "BOM";
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		contextBomQueryField = form.getFieldByControlId(FIELD_CONTEXTBOMQUERY, CustomField.class);
		contextBomEditField = form.getFieldByControlId(FIELD_CONTEXTBOMEDIT, GlcFormField.class);
		contextInfoField = contextBomEditField.getFieldByControlId(FIELD_CONTEXTINFO, CustomField.class);
		bomInfoField = contextBomEditField.getFieldByControlId(FIELD_BOMINFO, CustomField.class);

		//查询表格选择事件
		subscribeAndExecute(eventBroker, contextBomQueryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);
				
		//新建事件
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_SHOWRULE), this::ruleAdapter);
				
		//新建事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);
				
		//保存事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);			
				
		//激活事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ACTIVE), this::activeAdapter);		
				
		//失效事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_INACTIVE), this::inActiveAdapter);		
				
		//删除事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);		
				
		//刷新事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ENTITYREFRESH), this::refreshAdapter);		
				
		//打开页面加载新建事件
		newAdapter(null);
	}

	protected void selectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			org.osgi.service.event.Event event = (org.osgi.service.event.Event) object;		
			ContextValue toolBomContextValue = (ContextValue) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (toolBomContextValue == null) {
				return;
			}
			CustomCompsite contextInfoCustomCompsite = contextInfoField.getCustomComposite();
			contextInfoCustomCompsite.setValue(toolBomContextValue);
			contextInfoCustomCompsite.refresh();
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<BomLine> lines = adManager.getEntityList(Env.getOrgRrn(), BomLine.class, Integer.MAX_VALUE, 
					" bomRrn = '" + toolBomContextValue.getResultValue2() + "'", "");
			if (lines != null && lines.size() > 0) {
				bomInfoField.setValue(lines);
				bomInfoField.refresh();	
			}							
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	
	protected void ruleAdapter(Object object) {
        try {
        	ContextManager contextManager = Framework.getService(ContextManager.class);
            Context context = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME);
             
        	ContextRuleDialog dialog = new ContextRuleDialog(UI.getActiveShell(), context);  
            dialog.open();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void newAdapter(Object object) {
		try {
			CustomCompsite contextInfoCustomCompsite = contextInfoField.getCustomComposite();
			contextInfoCustomCompsite.setValue(new ContextValue());
			
			bomInfoField.setValue(new ArrayList<BomLine>());
			bomInfoField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void saveAdapter(Object object) {
		ContextEditCustomComposite contextInfoCustomCompsite = (ContextEditCustomComposite) contextInfoField.getCustomComposite();
		
		try {	
			contextInfoCustomCompsite.getMessageManager().setAutoUpdate(true); //设置为true时才会刷新界面出现icon
			contextInfoCustomCompsite.getMessageManager().removeAllMessages();
			
			contextInfoCustomCompsite.getContextForm().getMessageManager().setAutoUpdate(true); //设置为true时才会刷新界面出现icon
			contextInfoCustomCompsite.getContextForm().getMessageManager().removeAllMessages();
			
			if (contextInfoCustomCompsite.getContextForm().validate()) {
				boolean saveFlag = true;			
				if (!contextInfoCustomCompsite.getContextForm().saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					ContextManager contextManager = Framework.getService(ContextManager.class);
			        Context context = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME);
			           	
					ContextValue contextValue = (ContextValue) contextInfoCustomCompsite.getValue();
					contextValue.setContextRrn(context.getObjectRrn());
					contextValue.setOrgRrn(Env.getOrgRrn());
					
					ADManager adManager = Framework.getService(ADManager.class);
					Bom bom = new Bom();
					if (StringUtil.isEmpty(contextValue.getResultValue2())) {
						bom.setOrgRrn(Env.getOrgRrn());
						bom.setIsActive(true);
						bom.setBomUse(Bom.BOMUSE_TOOL);
						bom.setName("BOM" + SnowFlake.generateId());
					} else {
						bom.setObjectRrn(Long.valueOf(contextValue.getResultValue2()));
						bom = (Bom) adManager.getEntity(bom);
					}	
					
					List<BomLine> lines = (List<BomLine>)(List<? extends Object>)bomInfoField.getValue();
					if (CollectionUtils.isEmpty(lines)) {
						UI.showError(Message.getString("mm.bom_lines_not_exist"));
						return;
					}

					List<BomLine> bomLines = new ArrayList<BomLine>();
					Long seqNo = 0l;
					for (BomLine line : lines) {
						if (line.getIsCritical() == null || "".equals(line.getIsCritical()) 
								|| line.getFlushType() == null || "".equals(line.getFlushType())) {
							line.setIsCritical(true);
							line.setFlushType(AbstractBomLine.FLUSH_TYPE_BOM);
						}
						line.setIsMain(line.getIsMain());
						line.setObjectRrn(null);
						line.setOrgRrn(Env.getOrgRrn());
						line.setSeqNo(seqNo);
						bomLines.add(line);
						seqNo ++;
					}										
					PropertyUtil.setProperty(bom, "bomLines", bomLines);										
									
					MMManager mmManager = Framework.getService(MMManager.class);
					mmManager.saveContextBom(bom, contextValue, Env.getSessionContext());			
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}  finally {
			contextInfoCustomCompsite.getContextForm().getMessageManager().setAutoUpdate(false);
		}
	}
	
	protected void activeAdapter(Object object) {
		try {
			ContextActiveDialog dialog = new ContextActiveDialog(UI.getActiveShell(), CONTEXT_NAME);
            dialog.open();
            
            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void inActiveAdapter(Object object) {
		try {		
			ContextObjectQueryCustomComposite contextObjectQueryComposite = 
					(ContextObjectQueryCustomComposite) contextBomQueryField.getCustomComposite();
			ContextValue toolContextContextValue = (ContextValue)contextObjectQueryComposite.getQueryForm().getTableManager().getSelectedObject();	
            if (toolContextContextValue == null) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
            if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmInActive()))) {             
            	
                List<ContextValue> contextValues = new ArrayList<ContextValue>();
                contextValues.add(toolContextContextValue);
                	 
                ContextManager contextManager = Framework.getService(ContextManager.class);
                contextManager.activeContextValue(contextValues, Env.getSessionContext());
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
            }	        
            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void deleteAdapter(Object object) {
		try {
			ContextObjectQueryCustomComposite contextObjectQueryComposite = 
					(ContextObjectQueryCustomComposite) contextBomQueryField.getCustomComposite();
			ContextValue toolContextContextValue = (ContextValue)contextObjectQueryComposite.getQueryForm().getTableManager().getSelectedObject();		
            if (toolContextContextValue == null ) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
                List<ContextValue> contextValues = new ArrayList<ContextValue>();
                contextValues.add(toolContextContextValue);
                	
                MMManager mmManager = Framework.getService(MMManager.class);
                mmManager.deleteContextBom(contextValues, Env.getSessionContext());
			}
			
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void refreshAdapter(Object object) {
		try {
			//查询列表清空
			contextBomQueryField.refresh();
			
			//打开页面加载新建事件
			newAdapter(null);
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}

}