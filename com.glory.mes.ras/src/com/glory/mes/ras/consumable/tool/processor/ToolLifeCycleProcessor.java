package com.glory.mes.ras.consumable.tool.processor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;

public class ToolLifeCycleProcessor extends AbstractToolProcessor{

	private static final String TABLE_NAME = "RASToolProcessorLifeCycle";
	private static final String TABLE_NAME_MLOT_LIST = "RASToolProcessorLifeCycleList";
	
	private static final String FLOORLIFEEXPIRE = "floorLifeExpire";
	private static final String LIMITLIFE = "limitLife";
	
	private EntityForm entityForm;
	private IMessageManager mmng;
	
	public ToolLifeCycleProcessor(boolean isBatch) {
		super(isBatch);
	}
	
	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				Tool tool = (Tool) entityForm.getObject();

				//获取失效时间
				Date date = (Date) tool.getFloorLifeExpire();
				//获取限制使用次数值
				Long limitLife = tool.getLimitLife();
				if (date == null && limitLife == null) {
					UI.showInfo(Message.getString("ras.please_input_change_vlaue"));
					return false;
				}
				consumableManager.changeToolFloorLifeExpire((List)lots, date, limitLife, Env.getSessionContext());
			} else {
				return false;
			}
		} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	} finally {
      		mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot lot) {
		if(MLot.HOLDSTATE_ON.equals(lot.getHoldState())) {
			return false;
		} else {
			return true;
		}	
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {	
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		ManagedForm mform = new ManagedForm(toolkit, form);
		mmng = mform.getMessageManager();
		
		Composite body = form.getBody();
		configureBody(body);
		
		entityForm = new EntityForm(body, SWT.NONE, new Tool(), getADTable(), mmng);
		entityForm.setLayout(new GridLayout(1, false));
		entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	@Override
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}
	
	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldLimitLife = new ADField();
		adFieldLimitLife.setName(LIMITLIFE);
		adFieldLimitLife.setIsMain(true);
		adFieldLimitLife.setIsDisplay(true);
		adFieldLimitLife.setIsEditable(true);
		adFieldLimitLife.setDisplayLength(15l);
		adFieldLimitLife.setLabel(Message.getString("ras.limitLife"));
		adFieldLimitLife.setLabel_zh(Message.getString("ras.limitLife"));
		adFieldLimitLife.setDataType("string");
		adFieldLimitLife.setDisplayType("text");
		adFields.add(adFieldLimitLife);
		
		ADField floorLifeExpire = new ADField();
		floorLifeExpire.setName(FLOORLIFEEXPIRE);
		floorLifeExpire.setIsMain(true);
		floorLifeExpire.setIsDisplay(true);
		floorLifeExpire.setIsEditable(true);
		floorLifeExpire.setDisplayLength(15l);
		floorLifeExpire.setLabel(Message.getString("mm_period_validity"));
		floorLifeExpire.setLabel_zh(Message.getString("mm_period_validity"));
		floorLifeExpire.setDataType("date");
		floorLifeExpire.setDisplayType("datetime");
		adFields.add(floorLifeExpire);

		adTable.setFields(adFields);
		
		return adTable;
	}
	
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> adFields = adTable.getFields();
		
		ADField adFieldLimitLife = new ADField();
		adFieldLimitLife.setName(LIMITLIFE);
		adFieldLimitLife.setIsMain(true);
		adFieldLimitLife.setIsDisplay(true);
		adFieldLimitLife.setIsEditable(true);
		adFieldLimitLife.setDisplayLength(15l);
		adFieldLimitLife.setLabel(Message.getString("ras.limitLife"));
		adFieldLimitLife.setLabel_zh(Message.getString("ras.limitLife"));
		adFieldLimitLife.setDataType("string");
		adFieldLimitLife.setDisplayType("text");
		adFields.add(adFieldLimitLife);
		
		ADField floorLifeExpire = new ADField();
		floorLifeExpire.setName(FLOORLIFEEXPIRE);
		floorLifeExpire.setIsMain(true);
		floorLifeExpire.setIsDisplay(true);
		floorLifeExpire.setIsEditable(true);
		floorLifeExpire.setDisplayLength(15l);
		floorLifeExpire.setLabel(Message.getString("mm_period_validity"));
		floorLifeExpire.setLabel_zh(Message.getString("mm_period_validity"));
		floorLifeExpire.setDataType("date");
		floorLifeExpire.setDisplayType("text");
		adFields.add(floorLifeExpire);
		
		return adTable;
	}
}
