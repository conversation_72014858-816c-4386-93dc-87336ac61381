package com.glory.mes.ras.port;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.excel.UploadErrorDialog;
import com.glory.framework.base.excel.UploadErrorLog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class PortUpload extends Upload{
	
	public PortUpload(String name) {
		super(name);
	}
	
	public PortUpload(String authorityName, String buttonName, String name) {
		super(authorityName, buttonName, name);
	}
	
	public static final String ONLINE = "Online";
	public static final String AGV = "AGV";
	
	public static final String MANUAL = "Manual";
	public static final String MGV = "MGV";
	
	@Override
	protected void cudEntityList() {
		try {
			RASManager rasManager = Framework.getService(RASManager.class);
			List<ADBase> uploadList = progress.getUploadList();
			List<Port> portLists = new ArrayList<Port>();
			//获得所有port因为port + equipmentID组合只能有一个
			Map<String, Port> parentEqpMap = Maps.newHashMap();
			parentEqpMap = getPortmap(parentEqpMap);
			// 获得所有设备
			Map<Long, Equipment> equipmentmap = new HashMap<>();
			equipmentmap = getEquipmentmap(equipmentmap);
			// 首先在没有转换之前，校验格式
			int i = 1;
			for (ADBase model : uploadList) {
				i++;
				Port uploadTemp = (Port) model;
				if (uploadTemp.getPortId() == null || uploadTemp.getPortId().equals("")) {
					UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null, "PORTID", uploadTemp.getPortId() + "_" + uploadTemp.getParentEqpId()
					+ String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
					progress.getErrLogs().add(errorLog);
					continue;
				}

				if (StringUtil.isEmpty(uploadTemp.getPortNum())) {
					UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null, "PORTNUM", uploadTemp.getPortId() + "_" + uploadTemp.getParentEqpId()
					+ String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
					progress.getErrLogs().add(errorLog);
					continue;
				}

				if (uploadTemp.getParentEqpRrn() != null) {
					if (equipmentmap.containsKey(uploadTemp.getParentEqpRrn())) {
						uploadTemp.setParentEqpId((equipmentmap.get(uploadTemp.getParentEqpRrn())).getEquipmentId());
					} else {
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null,"EQUIPMENT", uploadTemp.getPortId() + "_" + uploadTemp.getParentEqpId()
						+ String.format(Message.getString("ras.equipment_no_found")));
						progress.getErrLogs().add(errorLog);
						continue;
					}
				}

				//因为默认值是MGV，所以当空时直接默认为MGV状态
				if (MANUAL.equals(uploadTemp.getAccessState()) || ONLINE.equals(uploadTemp.getAccessState()) || MGV.equals(uploadTemp.getAccessState())|| AGV.equals(uploadTemp.getAccessState())) {
					if (MANUAL.equals(uploadTemp.getAccessState())) {
						uploadTemp.setAccessState(MGV);
					} else if (ONLINE.equals(uploadTemp.getAccessState())) {
						uploadTemp.setAccessState(AGV);
					}
				}else {
					UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null, "ACCESSSTATE", uploadTemp.getPortId() + "_" + uploadTemp.getParentEqpId()
					+ String.format(Message.getString("edc.edcset_attribute_format")));
					progress.getErrLogs().add(errorLog);
					continue;
				}

				if (StringUtil.isEmpty(uploadTemp.getDurableType())) {
					UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null, "DURANLETYPE", uploadTemp.getPortId() + "_" + uploadTemp.getParentEqpId()
					+ String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
					progress.getErrLogs().add(errorLog);
					continue;
				}
				
				//判断是否又portID + equipemtID对象已存在
				if(parentEqpMap.containsKey(uploadTemp.getPortId() + uploadTemp.getParentEqpId())) {
					UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null, "PORTID", uploadTemp.getPortId() + "_" + uploadTemp.getParentEqpId()
					+ String.format(Message.getString("wip.component_is_exist")));
					progress.getErrLogs().add(errorLog);
					continue;
				}
				
				parentEqpMap.put(uploadTemp.getPortId() + uploadTemp.getParentEqpId(), uploadTemp);
				portLists.add(uploadTemp);
			}
			//如果存在不通过，则弹框显示那条数据有问题
			if (CollectionUtils.isNotEmpty(progress.getErrLogs())) {
				UploadErrorDialog dialog = new UploadErrorDialog(progress.getErrLogs());
				dialog.open();
			} else {
				rasManager.saveUploadPortList(portLists, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private Map<Long, Equipment> getEquipmentmap(Map<Long, Equipment> equipmentmap){
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<Equipment> allEquipment = adManager.getEntityList(Env.getOrgRrn(), Equipment.class);
			if(allEquipment != null && allEquipment.size() > 0) {
				for(Equipment equipment : allEquipment) {
					if(!StringUtil.isEmpty(equipment.getEquipmentId()) && equipment.getObjectRrn() != null) {
						equipmentmap.put(equipment.getObjectRrn(), equipment);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return equipmentmap;
	}
	
	private Map<String, Port> getPortmap(Map<String, Port> parentEqpMap){
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<Port> allPort = adManager.getEntityList(Env.getOrgRrn(), Port.class);
			if(CollectionUtils.isNotEmpty(allPort)) {
				for(Port port : allPort) {
					parentEqpMap.put(port.getPortId() + port.getParentEqpId(), port);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return parentEqpMap;
	}
	
}
