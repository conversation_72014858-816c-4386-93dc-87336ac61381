package com.glory.mes.ras.ipbindingeqp;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.WorkStation;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.EquipmentWorkStation;
import com.glory.framework.core.exception.ExceptionBundle;

public class IPBindingEqpProperties extends EntityProperties {

    private IPBindingEqpForm warehousePartForm;

    public IPBindingEqpProperties() {
        super();
    }

    @Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
        EntityForm itemForm;
        String tabName = tab.getName();
        if ("IPWorkStationEqpList".equalsIgnoreCase(tabName)) {
            warehousePartForm = new IPBindingEqpForm(composite, SWT.NONE, tab, mmng);
            return warehousePartForm;
        } else {
            itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
        }
        return itemForm;
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemSave(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void saveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            boolean saveFlag = true;
            for (IForm detailForm : getDetailForms()) {
                if (!detailForm.saveToObject()) {
                    saveFlag = false;
                }
            }
            if (saveFlag) {
                WorkStation workStation = (WorkStation) getAdObject();
                //检测IP是否可以ping通
                try {
                    InetAddress address = InetAddress.getByName(workStation.getIpAddress());
                    boolean isReachable = address.isReachable(3000);
                    if (!isReachable) {
                        UI.showError(Message.getString("common.ip.is.not.available"));
                        return;
                    }
                } catch (UnknownHostException e) {
                    UI.showError(Message.getString("common.ip.is.not.available"));
                    return;
                } catch (IOException e) {
                    UI.showError(Message.getString("common.ip.is.not.available"));
                    return;
                }

                List<Equipment> equipments = (List<Equipment>) getField(IPBindingEqpForm.FIELD_ID_EQP).getValue();

                List<EquipmentWorkStation> equipmentWorkStations = new ArrayList<EquipmentWorkStation>();
                for (Equipment equipment : equipments) {
                    EquipmentWorkStation equipmentWorkStation = new EquipmentWorkStation();
                    equipmentWorkStation.setEquipmentRrn(equipment.getObjectRrn());
                    equipmentWorkStation.setEquipmentId(equipment.getEquipmentId());
                    equipmentWorkStations.add(equipmentWorkStation);
                }

                RASManager rasManager = Framework.getService(RASManager.class);
                WorkStation newWorkStation = rasManager.saveIpBindingEqp(workStation,
                        equipmentWorkStations, Env.getSessionContext());
                setAdObject(newWorkStation);

                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
                refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
    public void refresh() {
        super.refresh();
        try {
            WorkStation workStation = (WorkStation) getAdObject();
            TableSelectField eqpList = (TableSelectField) getField("attributeValues");

            if (workStation != null && workStation.getObjectRrn() != null) {
            	itemSave.setEnabled(true);
            	
                ADManager adManager = Framework.getService(ADManager.class);

                List<Equipment> equipments = new ArrayList<Equipment>();

                List<EquipmentWorkStation> wareHouseParts = adManager.getEntityList(
                        Env.getOrgRrn(), EquipmentWorkStation.class, Env.getMaxResult(),
                        "stationRrn = " + workStation.getObjectRrn(), "equipmentId");
                for (EquipmentWorkStation equipmentWorkStation : wareHouseParts) {
                    Equipment equipment = new Equipment();
                    equipment.setObjectRrn(equipmentWorkStation.getEquipmentRrn());
                    equipment = (Equipment) adManager.getEntity(equipment);
                    equipments.add(equipment);
                }
                eqpList.setValue(equipments);

                eqpList.refresh();
            } else {
            	itemSave.setEnabled(false);
                eqpList.setValue(null);
                getMasterParent().refresh();
                eqpList.refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
}
