package com.glory.mes.base.idgenerator;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTextField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.core.util.PropertyUtil;

public class DSTypeForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(DSTypeForm.class);
	
	public DSTypeForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	protected void createContent() {
		try {
			mLeftPadding = 0;
			mTopPadding = 0;
//			mRightPadding = -5;
			mBottomPadding = 0;
			
			mHorizSpacing = 0;
			mVertSpacing = 5;
			super.createContent();
		} catch (Exception e) {
			logger.error("DSTypeForm : createContent()", e);
		}
	}
	
	
	/**
	 * 此方法提供另外一种loadFromObject方法，可根据用户指定来选择是否将页面栏位置为可用状态
	 * @param isForUpdate 是否刷新页面栏位
	 */
	public void loadFromObject(boolean isForUpdate) {
		if(isForUpdate){
			if (object != null && object instanceof ADBase){
				for (IField f : fields.values()){
					if (!(f instanceof SeparatorField || f instanceof RefTextField)){
						Object o = PropertyUtil.getPropertyForIField(object, f.getId());
						f.setValue(o);
					}
					ADField adField = getAdFields().get(f.getId());
					if (adField != null && !adField.getIsEditable()){
						f.setEnabled(isForUpdate);
					}
				}
				refresh();
			}
		}else{
			loadFromObject();
		}
	}
}
