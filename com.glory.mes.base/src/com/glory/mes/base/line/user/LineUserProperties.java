package com.glory.mes.base.line.user;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.Line;
import com.glory.mes.base.model.LineUser;
import com.glory.framework.core.exception.ExceptionBundle;

public class LineUserProperties extends EntityProperties {
	
	private LineUserForm parameterForm;

	public LineUserProperties() {
		super();
    }
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		EntityForm itemForm;
		String tabName = tab.getName();
		if (tabName.equalsIgnoreCase("LineUserTab")) {
			parameterForm = new LineUserForm(composite, SWT.NONE, tab, mmng);
			return parameterForm;
		} else {
			itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
		}
		return itemForm;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				Line line = (Line) getAdObject();
				if (line == null || line.getObjectRrn() == null) {
					UI.showInfo(Message.getString("ras.equipment_select_eqp"));
					return;
				}
				List<ADUser> selectEqps = (List<ADUser>) getField("reserved1").getValue();
				if (selectEqps == null) {
					selectEqps = new ArrayList<ADUser>();
				}
				
				List<LineUser> userLines = new ArrayList<LineUser>();
				for (ADUser adUser : selectEqps) {
					LineUser lineuser = new LineUser();
					lineuser.setLineId(line.getName());
					lineuser.setLineRrn(line.getObjectRrn());
					lineuser.setUserName(adUser.getUserName());
					lineuser.setUserRrn(adUser.getObjectRrn());
					userLines.add(lineuser);
				}
				MBASManager mbasManager = Framework.getService(MBASManager.class);
				mbasManager.saveLineUsers(line.getObjectRrn(), userLines, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void refresh() {
		super.refresh();
		try {
			Line line = (Line) getAdObject();
			if (line != null && line.getObjectRrn() != null) {
				MBASManager mbasManager = Framework.getService(MBASManager.class);
				//Framework.getService(RASManager.class)
				List<ADUser> adUsers = mbasManager.getLineUsers(line.getObjectRrn());
				
				TableSelectField selectField = (TableSelectField) getField("reserved1");
				selectField.setValue(adUsers);
				selectField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
