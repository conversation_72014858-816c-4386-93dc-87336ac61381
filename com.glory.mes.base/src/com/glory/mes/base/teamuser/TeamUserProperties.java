package com.glory.mes.base.teamuser;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.Team;
import com.glory.mes.base.model.TeamUser;
import com.glory.framework.core.exception.ExceptionBundle;

public class TeamUserProperties extends EntityProperties {

    private TeamUserForm teamUserForm;

    public TeamUserProperties() {
        super();
    }

    @Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
        EntityForm itemForm;
        String tabName = tab.getName();
        if ("TeamUserList".equalsIgnoreCase(tabName)) {
        	teamUserForm = new TeamUserForm(composite, SWT.NONE, tab, mmng);
            return teamUserForm;
        } else {
            itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
        }
        return itemForm;
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemSave(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    @SuppressWarnings("unchecked")
	@Override
    protected void saveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            boolean saveFlag = true;
            for (IForm detailForm : getDetailForms()) {
                if (!detailForm.saveToObject()) {
                    saveFlag = false;
                }
            }
            if (saveFlag) {
                Team team = (Team) getAdObject();
                if (team != null && team.getObjectRrn() != null) {
                	
                	List<ADUser> users = (List<ADUser>) getField(TeamUserForm.FIELD_ID_USER).getValue();
                	
                	List<TeamUser> teamUsers = new ArrayList<TeamUser>();
                	if (users != null) {
                		for (ADUser adUser : users) {
                			TeamUser teamUser = new TeamUser();
                			teamUser.setOrgRrn(Env.getOrgRrn());
                			teamUser.setTeamRrn(team.getObjectRrn());
                			teamUser.setTeamId(team.getName());
                			teamUser.setUserRrn(adUser.getObjectRrn());
                			teamUser.setUserName(adUser.getUserName());
                			teamUsers.add(teamUser);
                		}
                	}
                	
                	MBASManager mbaSManager = Framework.getService(MBASManager.class);
                	mbaSManager.saveTeamUsers(team, teamUsers, Env.getSessionContext());

                	UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
                    refresh();
                } else {
                	UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));// 弹出提示框
                }
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
    public void refresh() {
        super.refresh();
        try {
        	Team team = (Team) getAdObject();
            TableSelectField userList = (TableSelectField) getField("attributeValues");

            if (team != null && team.getObjectRrn() != null) {
                ADManager adManager = Framework.getService(ADManager.class);

                List<ADUser> adUsers = new ArrayList<ADUser>();

                List<TeamUser> teamUsers = adManager.getEntityList( Env.getOrgRrn(), TeamUser.class, Env.getMaxResult(),
                        "teamRrn = " + team.getObjectRrn(), "teamRrn");

                for (TeamUser teamUser : teamUsers) {
                	ADUser adUser = new ADUser();
                	adUser.setObjectRrn(teamUser.getUserRrn());
                	adUser = (ADUser) adManager.getEntity(adUser);
                	adUsers.add(adUser);
                }
                userList.setValue(adUsers);
                userList.refresh();
            } else {
            	userList.setValue(null);
                getMasterParent().refresh();
                userList.refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
}
