package com.glory.mes.prd.designer.part;

import org.eclipse.draw2d.ColorConstants;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.gef.EditPart;
import org.eclipse.gef.EditPartFactory;
import org.eclipse.gef.Request;
import org.eclipse.gef.RequestConstants;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.prd.designer.command.EdgeDeleteCommand;
import com.glory.mes.prd.designer.command.NodeCreateCommand;
import com.glory.mes.prd.designer.command.NodeDeleteCommand;
import com.glory.mes.prd.designer.common.command.AbstractEdgeDeleteCommand;
import com.glory.mes.prd.designer.common.command.AbstractNodeCreateCommand;
import com.glory.mes.prd.designer.common.command.AbstractNodeDeleteCommand;
import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.notation.AbstractNodeContainer;
import com.glory.mes.prd.designer.common.notation.Edge;
import com.glory.mes.prd.designer.common.notation.Label;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.common.notation.NodeContainer;
import com.glory.mes.prd.designer.common.notation.RootContainer;
import com.glory.mes.prd.designer.common.part.EdgeGraphicalEditPart;
import com.glory.mes.prd.designer.common.part.LabelGraphicalEditPart;
import com.glory.mes.prd.designer.common.part.NodeContainerGraphicalEditPart;
import com.glory.mes.prd.designer.common.part.NodeGraphicalEditPart;
import com.glory.mes.prd.designer.common.part.RootContainerGraphicalEditPart;
import com.glory.mes.prd.designer.common.policy.ComponentEditPolicy;
import com.glory.mes.prd.designer.common.policy.ConnectionEditPolicy;
import com.glory.mes.prd.designer.common.policy.GraphicalNodeEditPolicy;
import com.glory.mes.prd.designer.common.policy.XYLayoutEditPolicy;
import com.glory.mes.prd.designer.dialog.AvailablePrdDialog;
import com.glory.mes.prd.designer.dialog.ConditionTransitionDialog;
import com.glory.mes.prd.designer.dialog.EdgePropertyEditDialog;
import com.glory.mes.prd.designer.model.AbstractNode;
import com.glory.mes.prd.designer.model.Condition;
import com.glory.mes.prd.designer.model.ConditionTransition;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndIfState;
import com.glory.mes.prd.designer.model.EndState;
import com.glory.mes.prd.designer.model.IfState;
import com.glory.mes.prd.designer.model.MoveToState;
import com.glory.mes.prd.designer.model.ReworkState;
import com.glory.mes.prd.designer.model.ReworkTransition;
import com.glory.mes.prd.designer.model.StartState;
import com.glory.mes.prd.designer.model.StepState;
import com.glory.mes.prd.designer.notation.JpdlNode;
import com.glory.mes.prd.designer.policy.NodeGraphicalNodeEditPolicy;

public class JpdlGraphicalEditPartFactory implements EditPartFactory {
	
	protected IEventBroker eventBroker;
	
	public JpdlGraphicalEditPartFactory() {
		super();
	}
	
	public JpdlGraphicalEditPartFactory(IEventBroker eventBroker) {
		super();
		this.eventBroker = eventBroker;
	}
	
	public EditPart createEditPart(EditPart context, Object model) {
		if (model == null) return null;
		if (model instanceof RootContainer) {
			return createRootContainerGraphicalEditPart(model);
		} else if (model instanceof AbstractNodeContainer) {
			return createNodeContainerGraphicalEditPart(model);
		} else if (model instanceof Node){
			return createNodeGraphicalEditPart(model);
		} else if (model instanceof Edge) {
			return createEdgeGraphicalEditPart(model);
		} else if (model instanceof Label) {
			return createLabelGraphicalEditPart(context.getModel(),model);
		}
		return null;
	}
	
	private EditPart createNodeContainerGraphicalEditPart(Object model) {
		return new NodeContainerGraphicalEditPart((NodeContainer)model) {
			protected XYLayoutEditPolicy createXYLayoutEditPolicy() {
				return getLayoutEditPolicy();
			}
			protected ComponentEditPolicy createComponentEditPolicy() {
				return getComponentEditPolicy();
			}
			protected GraphicalNodeEditPolicy createGraphicalNodeEditPolicy() {
				return new NodeGraphicalNodeEditPolicy(eventBroker);
			}
		};
	}
	
	/**
	 * 设置节点背景颜色
	 * @param nodeGraphicalEditPart
	 */
	private void setNodeFigureBackgroundColor(NodeGraphicalEditPart nodeGraphicalEditPart) {
		SemanticElement semanticElement = ((JpdlNode)nodeGraphicalEditPart.getModel()).getSemanticElement();	
		if (semanticElement instanceof StartState) {
			nodeGraphicalEditPart.getFigure().setBackgroundColor(ColorConstants.green);
		} else if (semanticElement instanceof EndState) {
			nodeGraphicalEditPart.getFigure().setBackgroundColor(ColorConstants.red);	
		} else if (semanticElement instanceof StepState) {
//			nodeGraphicalEditPart.getFigure().setBackgroundColor(ColorConstants.blue);				
		} else if (semanticElement instanceof MoveToState) {
			nodeGraphicalEditPart.getFigure().setBackgroundColor(ColorConstants.cyan);				
		} else if (semanticElement instanceof ReworkState) {
			nodeGraphicalEditPart.getFigure().setBackgroundColor(ColorConstants.darkGreen);				
		} else if (semanticElement instanceof IfState
				|| semanticElement instanceof ElseState
				|| semanticElement instanceof EndIfState) {
			nodeGraphicalEditPart.getFigure().setBackgroundColor(ColorConstants.yellow);	
		}
	}
	
	/**
	 * 设置IF与Step节点鼠标停留时显示ToolTip
	 * @param nodeGraphicalEditPart
	 */
	private void setNodeFigureToolTip(NodeGraphicalEditPart nodeGraphicalEditPart) {
		SemanticElement semanticElement = ((JpdlNode)nodeGraphicalEditPart.getModel()).getSemanticElement();			
		String toolTipMsg = ((AbstractNode)semanticElement).getToolTip();
		if (StringUtils.isNotEmpty(toolTipMsg)) {
			org.eclipse.draw2d.Label toolTipLabel = new org.eclipse.draw2d.Label();	
			toolTipLabel.setText(((AbstractNode)semanticElement).getToolTip());
			nodeGraphicalEditPart.getFigure().setToolTip(toolTipLabel);
		}
	}
	
	private EditPart createNodeGraphicalEditPart(Object model) {	
		NodeGraphicalEditPart nodeGraphicalEditPart = new NodeGraphicalEditPart((Node)model) {
			protected ComponentEditPolicy createComponentEditPolicy() {
				return getComponentEditPolicy();
			}
			protected GraphicalNodeEditPolicy createGraphicalNodeEditPolicy() {
				return new NodeGraphicalNodeEditPolicy(eventBroker);
			}			 
			public void performRequest(Request request) {
				if (request.getType() == RequestConstants.REQ_DIRECT_EDIT) {
					//do nothing
				} else if (request.getType() == RequestConstants.REQ_OPEN) {
					Node model = (Node) this.getModel();
					if(! (model.getSemanticElement() instanceof StartState || model.getSemanticElement() instanceof EndState ||  
							model.getSemanticElement() instanceof ElseState ||  model.getSemanticElement() instanceof EndIfState)){
						AvailablePrdDialog pd = new AvailablePrdDialog(UI.getActiveShell(), model);
						pd.open();
					}
				} else {
					super.performRequest(request);
				}
			}
		};
		
		setNodeFigureBackgroundColor(nodeGraphicalEditPart);
		setNodeFigureToolTip(nodeGraphicalEditPart);
		
		return nodeGraphicalEditPart;
	}

	private EditPart createLabelGraphicalEditPart(Object parent, Object model) {

		class ReworkTransitionLabelGraphicalEditPart extends LabelGraphicalEditPart {
			private Edge parent;

			public ReworkTransitionLabelGraphicalEditPart(Edge parent, Label model) {
				super(model);
				this.parent = parent;
			}

			@Override
			public void performRequest(Request request) {
				if (request.getType() == RequestConstants.REQ_DIRECT_EDIT
						|| request.getType() == RequestConstants.REQ_OPEN) {
					if (parent.getSemanticElement() instanceof ReworkTransition) {
						EdgePropertyEditDialog eped = new EdgePropertyEditDialog(UI.getActiveShell(), parent);
						eped.open();
					} else if (parent.getSemanticElement() instanceof ConditionTransition) {
						ConditionTransition conditionTransition = (ConditionTransition) parent.getSemanticElement();

						String expression = "";
						if (conditionTransition.getCondition() != null) {
							expression = conditionTransition.getCondition().getExpression();
						}

						ConditionTransitionDialog propertyDialog = new ConditionTransitionDialog("ConditionTransitionDialog", expression, eventBroker);
						if (propertyDialog.open() == Dialog.OK) {
							if (conditionTransition.getCondition() == null) {
								conditionTransition.setCondition(new Condition());
							}

							conditionTransition.getCondition().setExpression(propertyDialog.getValue().getName());
							conditionTransition.setName(conditionTransition.getFromSeqNo() + ":" + conditionTransition.getCondition().getExpression());
							conditionTransition.getDescription().setDescription(propertyDialog.getValue().getDescription());
						}
					}
				}
			}
		}

		return new ReworkTransitionLabelGraphicalEditPart((Edge) parent, (Label) model);
	}

	private EditPart createRootContainerGraphicalEditPart(Object model) {
		return new RootContainerGraphicalEditPart((RootContainer)model) {
			protected XYLayoutEditPolicy createLayoutEditPolicy() {
				return getLayoutEditPolicy();
			}			
		};
	}

	private EditPart createEdgeGraphicalEditPart(Object model) {
		return new EdgeGraphicalEditPart((Edge)model) {
			protected ConnectionEditPolicy getConnectionEditPolicy() {
				return new ConnectionEditPolicy() {
					protected AbstractEdgeDeleteCommand createDeleteCommand() {
						return new EdgeDeleteCommand();
					}
				};
			}
			
			@Override
			public void performRequest(Request req) {
				if (req.getType() == RequestConstants.REQ_OPEN) {
					Edge model = (Edge) this.getModel();
					if (model.getSemanticElement() instanceof ReworkTransition) {
						EdgePropertyEditDialog eped = new EdgePropertyEditDialog(UI.getActiveShell(), model);
						eped.open();
					} else if (model.getSemanticElement() instanceof ConditionTransition) {
						ConditionTransition conditionTransition = (ConditionTransition) model.getSemanticElement();
						
						String expression = "";
						if (conditionTransition.getCondition() != null) {
							expression = conditionTransition.getCondition().getExpression();
						}
						
						ConditionTransitionDialog propertyDialog = new ConditionTransitionDialog("ConditionTransitionDialog", expression, eventBroker);
						if(propertyDialog.open() == Dialog.OK) {
							if (conditionTransition.getCondition() == null) {
								conditionTransition.setCondition(new Condition());
							}
							
							conditionTransition.getCondition().setExpression(propertyDialog.getValue().getName());
							conditionTransition.setName(conditionTransition.getFromSeqNo() + ":" + conditionTransition.getCondition().getExpression());
							conditionTransition.getDescription().setDescription(propertyDialog.getValue().getDescription());
						}
					}
				
				} else {
					super.performRequest(req);
				}
			}
		};
	}
	
	private XYLayoutEditPolicy getLayoutEditPolicy() {
		return new XYLayoutEditPolicy() {
			protected AbstractNodeCreateCommand createNodeCreateCommand() {
				return new NodeCreateCommand();
			}					
		};
	}

	private ComponentEditPolicy getComponentEditPolicy() {
		return new ComponentEditPolicy() {
			protected AbstractNodeDeleteCommand createDeleteCommand() {
				return new NodeDeleteCommand();
			}					
		};
	}
}
