package com.glory.mes.prd.designer.dialog;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.EndState;

public class SelectedPrdModel implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private static final Logger logger = Logger.getLogger(SelectedPrdModel.class);
	
	private String version;
	private String name;
	private String description;
	private String stageId;
	
	private String reworkProcedureName;//for reworkState
	private String reworkProcedureVersion;//for reworkState
	private String isDynamic;//for reworkState
	
	private String objectType;//For ifState
	private String ifParameter;//For ifState
	private String ifParameterComparison = "=";//For ifState
	private String ifParameterValue;//For ifState
	private String ifExpression;//For ifState
	
	private String target; //For moveToLocState
	
	/**
	 * 返回上级流程：上级模块名称 
	 * For redirectEndState
	 */
	private String superiorProcedureName;
	
	/**
	 * 返回上级流程：上级模块工步名称 
	 * For redirectEndState
	 */
	private String superiorProcedureStepName;
	
	/**
	 * 返回到主流程：产品名称 
	 * For redirectEndState
	 */
	private String targetPartName;
	
	/**
	 * 返回到主流程：模块名称 
	 * For redirectEndState
	 */
	private String targetProcedureName;
	
	/**
	 * 返回到主流程：返工模块 
	 * For redirectEndState
	 */
	private List<Procedure> targetReworkProcedures;
	
	/**
	 * 返回到主流程：模块工步名称 
	 * For redirectEndState
	 */
	private String targetProcedureStepName;
	
	/**
	 * Transition的顺序
	 */
	private String fromSeqNo;
	
	
	public String getStageId() {
		return stageId;
	}

	public void setStageId(String stageId) {
		this.stageId = stageId;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setReworkProcedureName(String reworkProcedureName) {
		this.reworkProcedureName = reworkProcedureName;
	}

	public String getReworkProcedureName() {
		return reworkProcedureName;
	}

	public void setReworkProcedureVersion(String reworkProcedureVersion) {
		this.reworkProcedureVersion = reworkProcedureVersion;
	}

	public String getReworkProcedureVersion() {
		return reworkProcedureVersion;
	}

	public String getObjectType() {
		return objectType;
	}

	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}
	
	public String getIfParameter() {
		return ifParameter;
	}

	public void setIfParameter(String ifParameter) {
		this.ifParameter = ifParameter;
	}

	public String getIfParameterValue() {
		return ifParameterValue;
	}

	public void setIfParameterValue(String ifParameterValue) {
		this.ifParameterValue = ifParameterValue;
	}

	public String getIfExpression() {
		return ifExpression;
	}

	public void setIfExpression(String ifExpression) {
		this.ifExpression = ifExpression;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getIfParameterComparison() {
		return ifParameterComparison;
	}

	public void setIfParameterComparison(String ifParameterComparison) {
		this.ifParameterComparison = ifParameterComparison;
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public void setIsDynamic(String isDynamic) {
		this.isDynamic = isDynamic;
	}

	public String getIsDynamic() {
		return this.isDynamic;
	}

	public String getSuperiorProcedureName() {
		return superiorProcedureName;
	}

	public void setSuperiorProcedureName(String superiorProcedureName) {
		this.superiorProcedureName = superiorProcedureName;
	}

	public String getSuperiorProcedureStepName() {
		return superiorProcedureStepName;
	}

	public void setSuperiorProcedureStepName(String superiorProcedureStepName) {
		this.superiorProcedureStepName = superiorProcedureStepName;
	}

	public String getTargetPartName() {
		return targetPartName;
	}

	public void setTargetPartName(String targetPartName) {
		this.targetPartName = targetPartName;
	}

	public String getTargetProcedureName() {
		return targetProcedureName;
	}

	public void setTargetProcedureName(String targetProcedureName) {
		this.targetProcedureName = targetProcedureName;
	}

	public List<Procedure> getTargetReworkProcedures() {
		return targetReworkProcedures;
	}

	public void setTargetReworkProcedures(List<Procedure> targetReworkProcedures) {
		this.targetReworkProcedures = targetReworkProcedures;
	}

	public String getTargetProcedureStepName() {
		return targetProcedureStepName;
	}

	public void setTargetProcedureStepName(String targetProcedureStepName) {
		this.targetProcedureStepName = targetProcedureStepName;
	}

	public String getFromSeqNo() {
		return fromSeqNo;
	}

	public void setFromSeqNo(String fromSeqNo) {
		this.fromSeqNo = fromSeqNo;
	}

	public String getRedirectPath() {
		if (!StringUtil.isEmpty(superiorProcedureName) 
				&& !StringUtil.isEmpty(superiorProcedureStepName)) {
			return EndState.SUPER_INSTRUCTION_RELATIVE + ":" + superiorProcedureName + "/" + superiorProcedureStepName;
		} else if (!StringUtil.isEmpty(targetProcedureName) 
				&& !StringUtil.isEmpty(targetProcedureStepName)) {
			String path = EndState.SUPER_INSTRUCTION_PARENT + ":" + targetProcedureName + "/";
			
			if (CollectionUtils.isNotEmpty(targetReworkProcedures)) {
				String procedures = targetReworkProcedures.stream().map(Procedure::getName).collect(Collectors.joining("/", "", "/"));
				path += procedures;
			}
			
			path += targetProcedureStepName;
			return path;
		}

		return null;
	}
	
	public void setRedirectPath(String hiSuperInstruction) {
		if (!StringUtil.isEmpty(hiSuperInstruction)) {
			String[] path = hiSuperInstruction.split(":");
			if (path.length == 2) {
				if (EndState.SUPER_INSTRUCTION_RELATIVE.equals(path[0])) {
					String[] relativePath = path[1].split("/");
					if (relativePath.length == 2) {
						this.superiorProcedureName = relativePath[0];
						this.superiorProcedureStepName = relativePath[1];
					}
				} else if (EndState.SUPER_INSTRUCTION_PARENT.equals(path[0])) {
					String[] absolutePath = path[1].split("/");
					if (absolutePath.length >= 2) {
						List<Procedure> reworks = Lists.newArrayList();
						int i = 0;
						for (String nodeName : absolutePath) {
							if (i == 0) {
								this.targetProcedureName = nodeName;
							} else if (i == (absolutePath.length - 1)) {
								this.targetProcedureStepName = nodeName;
							} else {
								try {
									Procedure procedure = new Procedure();
									procedure.setName(nodeName);
									procedure.setOrgRrn(Env.getOrgRrn());
									
									PrdManager prdManager = Framework.getService(PrdManager.class);
									procedure = (Procedure) prdManager.getProcessDefinition(procedure);
									reworks.add(procedure);
								} catch (Exception e) {
									e.printStackTrace();
									logger.error("setRedirectPath() failed !", e);
								}
							}
							i++;
						}
						
						if (CollectionUtils.isNotEmpty(reworks)) {
							this.targetReworkProcedures = reworks;
						}
					}
				}
			}
		}
	}
	
}
