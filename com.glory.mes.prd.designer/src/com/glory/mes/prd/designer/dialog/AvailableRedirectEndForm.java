package com.glory.mes.prd.designer.dialog;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.google.common.collect.Lists;

public class AvailableRedirectEndForm extends AbstractPrdDialogForm {
	
	private static final Logger logger = Logger.getLogger(AbstractPrdDialogForm.class);
	
	private static final String TABLE_NAME = "PRDDesignerRedirectEnd";
	private static final String TABLE_NAME_PROCEDURE = "PRDReworkProcedure";

	public AvailableRedirectEndForm(Composite parent, int style, Object object) {
		super(parent, style, object);
		// 添加级联事件
		addEvents();
	}
	
	@Override
	public void loadFromObject() {
		try {
			// 设置返回工步下拉列表
			SelectedPrdModel model = (SelectedPrdModel) getObject();
			if (model != null) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				
				if (!StringUtil.isEmpty(model.getSuperiorProcedureName())) {
					Procedure procedure = new Procedure();
					procedure.setOrgRrn(Env.getOrgRrn());
					procedure.setName(model.getSuperiorProcedureName());
					procedure = (Procedure) prdManager.getProcessDefinition(procedure);
					
					RefTableField refTableField = (RefTableField) fields.get("superiorProcedureStepName");
					if (CollectionUtils.isNotEmpty(getSteps(procedure))) {
						refTableField.setInput(getSteps(procedure));
					} else {
						refTableField.setInput(Lists.newArrayList());
					}
				}
				
				if (CollectionUtils.isNotEmpty(model.getTargetReworkProcedures())) {
					Procedure procedure = model.getTargetReworkProcedures().get(model.getTargetReworkProcedures().size() - 1);
					RefTableField refTableField = (RefTableField) fields.get("targetProcedureStepName");
					if (CollectionUtils.isNotEmpty(getSteps(procedure))) {
						refTableField.setInput(getSteps(procedure));
					} else {
						refTableField.setInput(Lists.newArrayList());
					}
					
				} else if (!StringUtil.isEmpty(model.getTargetProcedureName())) {
					Procedure procedure = new Procedure();
					procedure.setOrgRrn(Env.getOrgRrn());
					procedure.setName(model.getTargetProcedureName());
					procedure = (Procedure) prdManager.getProcessDefinition(procedure);
					
					RefTableField refTableField = (RefTableField) fields.get("targetProcedureStepName");
					if (CollectionUtils.isNotEmpty(getSteps(procedure))) {
						refTableField.setInput(getSteps(procedure));
					} else {
						refTableField.setInput(Lists.newArrayList());
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("loadFromObject() failed !", e);
		}
		
		
		super.loadFromObject();
		
	}
	
	private List<Step> getSteps(Procedure procedure) {
		try {
			List<Step> steps = Lists.newArrayList();
			if (procedure != null) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
                //获取Procedure下的所有StepState节点
                List<StepState> stepStates = prdManager.getStepChildren((ProcessDefinition)procedure);           
                for (StepState stepState : stepStates) {
                	Step step = stepState.getUsedStep();
                	// 使用step state name
                	step.setName(stepState.getName());
                	steps.add(step);
                }     
				return steps;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	private List<Procedure> getProcedures(Part part) {
		try {
			List<Procedure> procedures = Lists.newArrayList();
			if (part != null) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				ProcessDefinition processDefinition = prdManager.getPartProcess(part);
				List<Node> nodes = prdManager.getProcessDefinitionChildern(processDefinition);
				for (Node node : nodes) {
					if (node instanceof ProcedureState) {
						ProcedureState procedureState = (ProcedureState) node;
						Procedure procedure = (Procedure)procedureState.getUsedProcedure();
						// 使用procedure state name
						procedure.setName(procedureState.getName());
						procedures.add(procedure);
					}
				}
				return procedures;
			}
		} catch (Exception e) {
			e.printStackTrace();
		} 
		return null;
	}
	
	private List<Procedure> addValue(Procedure procedure, TableSelectField tableSelectField) {
		tableSelectField.getTableManager().add(procedure);
		tableSelectField.setValue(Lists.newArrayList(tableSelectField.getTableManager().getInput()));
		
		return (List<Procedure>) tableSelectField.getValue();
    }
	
	@Override
	public Point getDialogPoint() {
		int height = DPIUtil.autoScaleUpUsingNativeDPI(300);
		int width = DPIUtil.autoScaleUpUsingNativeDPI(400);
		return new Point(width, height);
	}
	
	private void addEvents() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			this.table = table;
			createForm();
			
			Map<String, IField> fieldMap = fields;
			RefTableField refTableField = (RefTableField) fieldMap.get("superiorProcedureName");
			
			refTableField.addValueChangeListener(new IValueChangeListener() {
				
				@Override
				public void valueChanged(Object sender, Object newValue) {
					if (newValue != null || !StringUtil.isEmpty(DBUtil.toString(newValue))) {
						Procedure procedure = (Procedure) refTableField.getData();
						
						RefTableField refTableField = (RefTableField) fieldMap.get("superiorProcedureStepName");
						if (CollectionUtils.isNotEmpty(getSteps(procedure))) {
							refTableField.setInput(getSteps(procedure));
						} else {
							refTableField.setInput(Lists.newArrayList());
						}
						refTableField.setValue(null);
					}
				}
			});
			
			RefTableField refTableField2 = (RefTableField) fieldMap.get("targetPartName");
			
			RefTableField refTableField3 = (RefTableField) fieldMap.get("targetProcedureName");
			
			TableSelectField tableSelectField = (TableSelectField) fieldMap.get("targetReworkProcedures");
			
			refTableField2.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					if (newValue != null || !StringUtil.isEmpty(DBUtil.toString(newValue))) {
						Part part = (Part) refTableField2.getData();
						if (CollectionUtils.isNotEmpty(getProcedures(part))) {
							refTableField3.setInput(getProcedures(part));
						} else {
							refTableField3.setInput(Lists.newArrayList());
						}
						refTableField3.setValue(null);
					}
				}
			});
			
			refTableField3.addValueChangeListener(new IValueChangeListener() {
				
				@Override
				public void valueChanged(Object sender, Object newValue) {
					Procedure procedure = (Procedure) refTableField3.getData();
					if (procedure != null) {
						List<Procedure> procedures = (List<Procedure>) tableSelectField.getValue();
			    		if (CollectionUtils.isEmpty(procedures)) {
			    			RefTableField refTableField = (RefTableField) fieldMap.get("targetProcedureStepName");
							if (CollectionUtils.isNotEmpty(getSteps(procedure))) {
								refTableField.setInput(getSteps(procedure));
							} else {
								refTableField.setInput(Lists.newArrayList());
							}
							refTableField.setValue(null);
			    		}
					}
				}
			});
			
			
			tableSelectField.setAddSelectionListener(new SelectionAdapter() {
		    	@Override
				public void widgetSelected(SelectionEvent event) {
					ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_PROCEDURE);
					adTable = adManager.getADTableDeep(adTable.getObjectRrn());
					EntityDialog dialog = new AvailableRedirectEndReworkProceduresDialog(adTable, new Procedure());
					if (Dialog.OK == dialog.open()) {
						try {
							PrdManager prdManager = Framework.getService(PrdManager.class);
							
							Procedure procedure = (Procedure) dialog.getAdObject();
							procedure.setOrgRrn(Env.getOrgRrn());
							procedure = (Procedure) prdManager.getProcessDefinition(procedure);
							
							addValue(procedure, tableSelectField);
							
							RefTableField refTableField = (RefTableField) fieldMap.get("targetProcedureStepName");
							if (CollectionUtils.isNotEmpty(getSteps(procedure))) {
								refTableField.setInput(getSteps(procedure));
							} else {
								refTableField.setInput(Lists.newArrayList());
							}
							refTableField.setValue(null);
						} catch (Exception e) {
							e.printStackTrace();
							logger.error("AddSelection failed !", e);
						}
						
					}
				}
		    });
			
			tableSelectField.setDeleteSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					tableSelectField.delete();
					
		    		List<Procedure> procedures = (List<Procedure>) tableSelectField.getValue();
		    		if (CollectionUtils.isNotEmpty(procedures)) {
		    			//返回工步下拉框只显示添加最后一个模块的工步
						Procedure procedure = procedures.get(procedures.size() - 1);
						List<Procedure> newProcedures = adManager.getEntityList(Env.getOrgRrn(), Procedure.class,
								Integer.MIN_VALUE, Integer.MAX_VALUE, "name = '"+ procedure.getName() +"' and status = 'Active'", null);
						
						RefTableField refTableField = (RefTableField) fieldMap.get("targetProcedureStepName");
						if (CollectionUtils.isNotEmpty(getSteps(newProcedures.get(0)))) {
							refTableField.setInput(getSteps(newProcedures.get(0)));
						} else {
							refTableField.setInput(Lists.newArrayList());
						}
						refTableField.setValue(null);
		    		} else {
		    			Procedure procedure = (Procedure) refTableField3.getData();
						if (procedure != null) {
							RefTableField refTableField = (RefTableField) fieldMap.get("targetProcedureStepName");
							if (CollectionUtils.isNotEmpty(getSteps(procedure))) {
								refTableField.setInput(getSteps(procedure));
							} else {
								refTableField.setInput(Lists.newArrayList());
							}
							refTableField.setValue(null);
						}
		    		}
				}
			});

		}  catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	String getDialogTitle() {
		return Message.getString("prd.designer_redirect_end_dialog_title");
	}

	@Override
	String getDialogMessage() {
		return Message.getString("prd.designer_redirect_end_dialog_message");
	}
	
}
