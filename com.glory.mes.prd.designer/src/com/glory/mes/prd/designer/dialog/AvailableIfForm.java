package com.glory.mes.prd.designer.dialog;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.ParameterDefinition;
import com.glory.mes.prd.workflow.bsh.JbpmExpressionEvaluator;
import com.glory.mes.prd.workflow.graph.node.IfState;
import com.glory.mes.wip.model.LotAttribute;
import com.glory.framework.core.exception.ExceptionBundle;

public class AvailableIfForm extends AbstractPrdDialogForm {
	private static final String TABLE_NAME = "PRDDesignerIf";
	protected RefTableField objectTypeField;
	protected RefTableField ifParameterField;
	protected RefTableField ifParameterComparisonField;	
	protected TextField ifParameterValueField;
	protected TextField ifExpressionField;
	protected Button radio1Btn, radio2Btn, addBtn, andBtn, orBtn, parserBtn;
	protected Composite conditionComposite, expressionComposite;
	
	public AvailableIfForm(Composite parent, int style, Object object) {
		super(parent, style, object);
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			this.table = table;
			createForm();
		}  catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void createForm() {
		createContent();
		try {
			FormToolkit toolkit = new FormToolkit(Display.getCurrent());
			
			Composite bodyComposite = form.getBody();
			bodyComposite.setLayout(new GridLayout(1, false));
			bodyComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			radio1Btn = toolkit.createButton(bodyComposite,"Condition", SWT.RADIO);	
			radio1Btn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					radio1Btn.setSelection(true);
					radio2Btn.setSelection(false);
					conditionComposite.setEnabled(true);
					expressionComposite.setEnabled(false);
					addBtn.setEnabled(false);
					andBtn.setEnabled(false);
					orBtn.setEnabled(false);
					parserBtn.setEnabled(false);
					ifExpressionField.setValue(null);
					ifExpressionField.setText("");
				}
			});	
			
			conditionComposite = new Composite(bodyComposite, SWT.NONE);
			conditionComposite.setLayout(new GridLayout(2, false));
			conditionComposite.setBackground(new Color(null, 255, 255, 255));
			conditionComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_type"));
			objectTypeField = createObjectTypeField(conditionComposite, toolkit);			
			toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_name"));
			ifParameterField = createIfParameterField(conditionComposite, toolkit);			
			toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_comparison"));
			ifParameterComparisonField = createIfParameterComparisonField(conditionComposite, toolkit);			
			toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_value"));
			ifParameterValueField = createIfParameteValueField(conditionComposite, toolkit);
			
			radio2Btn = toolkit.createButton(bodyComposite,"Expression", SWT.RADIO);				
			radio2Btn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					radio1Btn.setSelection(false);
					radio2Btn.setSelection(true);
					conditionComposite.setEnabled(false);
					expressionComposite.setEnabled(true);
					addBtn.setEnabled(true);
					andBtn.setEnabled(true);
					orBtn.setEnabled(true);
					parserBtn.setEnabled(true);
					objectTypeField.setValue(null);
					ifParameterField.setValue(null);
					ifParameterComparisonField.setValue(null);
					ifParameterValueField.setValue(null);
					ifParameterValueField.setText("");
				}
			});			
			expressionComposite = toolkit.createComposite(bodyComposite);
			expressionComposite.setLayout(new GridLayout(1, false));
			expressionComposite.setBackground(new Color(null, 255, 255, 255));
			expressionComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			Composite buttonComposite = toolkit.createComposite(expressionComposite, SWT.NONE);
			GridLayout layoutButton = new GridLayout(4, false);
			buttonComposite.setLayout(layoutButton);
			GridData buttonGridData = new GridData(GridData.FILL_BOTH);
			buttonComposite.setLayoutData(buttonGridData);			
			addBtn = new Button(buttonComposite, SWT.BUTTON3);
			addBtn.setLayoutData(buttonGridData);
			addBtn.setText(Message.getString(ExceptionBundle.bundle.CommonAdd()));
			addBtn.addSelectionListener(new SelectionListener() {
				@Override
				public void widgetDefaultSelected(SelectionEvent e) {}
				@Override
				public void widgetSelected(SelectionEvent e) {
					try {						
						AvailableIfExpressionAddDialog dialog = new AvailableIfExpressionAddDialog();
						if (dialog.open() == Dialog.OK) {
							String objectType = dialog.getObjectTypeField().getValue().toString();
							String ifParameter = dialog.getIfParameterField().getValue().toString();
							String ifParameterComparison = dialog.getIfParameterComparisonField().getValue().toString();
							String ifParameterValue = dialog.getIfParameterValueField().getValue().toString();
							String buildContition = IfState.buildContition(objectType, ifParameter, ifParameterComparison, ifParameterValue);
							ifExpressionField.setText(ifExpressionField.getValue() + buildContition);
						}
					} catch (Exception e1) {
						
					}
				}
			});
			andBtn = new Button(buttonComposite, SWT.BUTTON3);
			andBtn.setLayoutData(buttonGridData);
			andBtn.setText("&&&&");
			andBtn.addSelectionListener(new SelectionListener() {
				@Override
				public void widgetDefaultSelected(SelectionEvent e) {}
				@Override
				public void widgetSelected(SelectionEvent e) {
					ifExpressionField.setText(ifExpressionField.getValue() + "&&");
				}
			});
			orBtn = new Button(buttonComposite, SWT.BUTTON3);
			orBtn.setLayoutData(buttonGridData);
			orBtn.setText("||");
			orBtn.addSelectionListener(new SelectionListener() {
				@Override
				public void widgetDefaultSelected(SelectionEvent e) {}
				@Override
				public void widgetSelected(SelectionEvent e) {
					ifExpressionField.setText(ifExpressionField.getValue() + "||");
				}
			});
			parserBtn = new Button(buttonComposite, SWT.BUTTON3);
			parserBtn.setLayoutData(buttonGridData);
			parserBtn.setText(Message.getString("common.check"));
			parserBtn.addSelectionListener(new SelectionListener() {
				@Override
				public void widgetDefaultSelected(SelectionEvent e) {}
				@Override
				public void widgetSelected(SelectionEvent e) {
					try {
						JbpmExpressionEvaluator.parser(ifExpressionField.getText());
					}  catch (Exception e1) {
						UI.showError(Message.getString("prd.if_expression_is_not_correct"));
					}
				}
			});	
			
			ifExpressionField = createIfExpressionField(expressionComposite, toolkit);	
						
	        loadFromObject();	        
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
    public void loadFromObject() {
		if (object != null) {
			SelectedPrdModel model = (SelectedPrdModel)object;
			
			if (model.getIfExpression() != null && !"".equals(model.getIfExpression())) {
				radio2Btn.setSelection(true);
				ifExpressionField.setValue(model.getIfExpression());
				ifExpressionField.setText(model.getIfExpression());	
				
				radio1Btn.setSelection(false);
				conditionComposite.setEnabled(false);				
			} else {
				radio1Btn.setSelection(true);				
				objectTypeField.setValue(model.getObjectType());
				ifParameterField.setValue(model.getIfParameter());
				ifParameterComparisonField.setValue(model.getIfParameterComparison());
				ifParameterValueField.setValue(model.getIfParameterValue());
				ifParameterValueField.setText(model.getIfParameterValue());
				
				radio2Btn.setSelection(false);
				expressionComposite.setEnabled(false);
				addBtn.setEnabled(false);
				andBtn.setEnabled(false);
				orBtn.setEnabled(false);
				parserBtn.setEnabled(false);
				ifExpressionField.setValue(null);
				ifExpressionField.setText("");
			}
			refresh();
			setEnabled();
		}
    }
	
	@Override
    public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			PropertyUtil.setProperty(object, objectTypeField.getId(), objectTypeField.getValue());
			PropertyUtil.setProperty(object, ifParameterField.getId(), ifParameterField.getValue());
			PropertyUtil.setProperty(object, ifParameterComparisonField.getId(), ifParameterComparisonField.getValue());
			PropertyUtil.setProperty(object, ifParameterValueField.getId(), ifParameterValueField.getValue());
			PropertyUtil.setProperty(object, ifExpressionField.getId(), ifExpressionField.getValue());
			return true;
		}
		return false;
    }
	
//	@Override
//	public boolean validate() {
//		if (radio2Btn.getSelection()) {
//			try {
//				JbpmExpressionEvaluator.parser(ifExpressionField.getText());
//			}  catch (Exception e1) {
//				UI.showError("表达示不正确!");
//				return false;
//			}
//		}
//		if (mmng == null) {
//			mmng = this.getForm().getMessageManager();
//		}
//		if (radio1Btn.getSelection()) {
//			if (objectTypeField.getValue() == null || "".equals(objectTypeField.getValue())) {
//				mmng.addMessage("objectType" + "common.ismandatory", 
//						String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), "ParameterType"), null,
//						IMessageProvider.ERROR, objectTypeField.getControls()[objectTypeField.getControls().length - 1]);
//				return false;
//			}
//			if (ifParameterField.getValue() == null || "".equals(ifParameterField.getValue())) {
//				mmng.addMessage("ifParameter" + "common.ismandatory", 
//						String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), "IfParameter"), null,
//						IMessageProvider.ERROR, ifParameterField.getControls()[ifParameterField.getControls().length - 1]);
//				return false;
//			}
//			if (ifParameterComparisonField.getValue() == null || "".equals(ifParameterComparisonField.getValue())) {
//				mmng.addMessage("ifParameterComparison" + "common.ismandatory", 
//						String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), "IfParameterComparison"), null,
//						IMessageProvider.ERROR, ifParameterComparisonField.getControls()[ifParameterComparisonField.getControls().length - 1]);
//				return false;
//			}
//			if (ifParameterValueField.getValue() == null || "".equals(ifParameterValueField.getValue())) {
//				mmng.addMessage("ifParameterValue" + "common.ismandatory", 
//						String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), "IfParameterValue"), null,
//						IMessageProvider.ERROR, ifParameterValueField.getControls()[ifParameterValueField.getControls().length - 1]);
//				return false;
//			} 
//		} else {
//			if (ifExpressionField.getValue() == null || "".equals(ifExpressionField.getValue())) {
//				mmng.addMessage("ifExpression" + "common.ismandatory", 
//						String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), "IfExpression"), null,
//						IMessageProvider.ERROR, ifExpressionField.getControls()[ifExpressionField.getControls().length - 1]);
//				return false;
//			}			
//		}
//		return true;
//	}
	
	public RefTableField createObjectTypeField(Composite top, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			
			ListTableManager tableManager = new ListTableManager(adTable);
			List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(), 
					Env.getMaxResult(), "referenceName = 'IfStatusObjectType'", adTable.getOrderByClause());	
			list.add(new ADURefList());
			tableManager.setInput(list);
			
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("key");
			adRefTable.setTextField("text");
			objectTypeField = new RefTableField("objectType", tableManager, adRefTable, SWT.NONE);
			objectTypeField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
						ADManager adManager = Framework.getService(ADManager.class);
						if (objectTypeField.getValue() != null && !"".equals(objectTypeField.getValue().toString())) {		
							if (IfState.OBJECTTYPE_PARAMETERS.equals(objectTypeField.getValue())) {						
								List<ParameterDefinition> parameters = adManager.getEntityList(Env.getOrgRrn(), ParameterDefinition.class, 
										Env.getMaxResult(), "", "");	
								List<ADField> Fields = new ArrayList<ADField>();
								for (ParameterDefinition parameter : parameters) {
									ADField field = new ADField();
									field.setName(parameter.getName());
									field.setDescription(parameter.getDescription());
									field.setDataType(parameter.getType());
									Fields.add(field);
								}
								ifParameterField.setInput(Fields);								
							} else if (IfState.OBJECTTYPE_ATTRIBUTES.equals(objectTypeField.getValue())) {		
								List<LotAttribute> lotAttributes = adManager.getEntityList(Env.getOrgRrn(), LotAttribute.class, 
										Env.getMaxResult(), "", "");	
								List<ADField> Fields = new ArrayList<ADField>();
								for (LotAttribute lotAttribute : lotAttributes) {
									ADField field = new ADField();
									field.setName(lotAttribute.getName());
									field.setDescription(lotAttribute.getDescription());
									field.setDataType(lotAttribute.getDataType());
									Fields.add(field);
								}
								ifParameterField.setInput(Fields);						
							} else if (IfState.OBJECTTYPE_LOT.equals(objectTypeField.getValue())) {
								ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), "PRDIfStatusParameterLot");
								List<ADField> Fields = lotTable.getFields();
								ifParameterField.setInput(Fields);								
							} else if (IfState.OBJECTTYPE_PART.equals(objectTypeField.getValue())) {							
								ADTable partTable = adManager.getADTable(Env.getOrgRrn(), "PRDIfStatusParameterPart");	
								List<ADField> Fields = partTable.getFields();
								ifParameterField.setInput(Fields);
							}
							ifParameterField.setValue(null);
							ifParameterComparisonField.setValue(null);
							ifParameterValueField.setValue(null);
							ifParameterValueField.setText("");
						}	
					} catch (Exception e) {
						e.printStackTrace();
					}
				}				
			});			
			objectTypeField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return objectTypeField;
	}
	
	public RefTableField createIfParameterField(Composite top, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADField");
					
			ListTableManager tableManager = new ListTableManager(adTable);
			
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("name");
			adRefTable.setTextField("name");
			ifParameterField = new RefTableField("ifParameter", tableManager, adRefTable, SWT.NONE);
			ifParameterField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
//						ADManager adManager = Framework.getService(ADManager.class);
//						if (ifParameterField.getValue() != null && !"".equals(ifParameterField.getValue().toString())) {																									
//							List<ADRefList> list = adManager.getEntityList(Env.getOrgRrn(), ADRefList.class, 
//									Env.getMaxResult(), "referenceName = 'IfStatusParameterComparison'", "seqNo");	
//							String dataType = ((ADField)ifParameterField.getData()).getDataType();
//							if ("string".equals(dataType)) {
//								
//							} else {
//								
//							}
//						}	
						ifParameterComparisonField.setValue(null);
						ifParameterValueField.setValue(null);
						ifParameterValueField.setText("");
					} catch (Exception e) {
						e.printStackTrace();
					}
				}				
			});			
			ifParameterField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return ifParameterField;
	}
	
	public RefTableField createIfParameterComparisonField(Composite top, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			
			ListTableManager tableManager = new ListTableManager(adTable);
			List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(), 
					Env.getMaxResult(), "referenceName = 'IfStatusParameterComparison'", adTable.getOrderByClause());	
			list.add(new ADURefList());
			tableManager.setInput(list);
			
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("key");
			adRefTable.setTextField("text");
			ifParameterComparisonField = new RefTableField("ifParameterComparison", tableManager, adRefTable, SWT.NONE);				
			ifParameterComparisonField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return ifParameterComparisonField;
	}
	
	public TextField createIfParameteValueField(Composite top, FormToolkit toolkit) {
		try {
			ifParameterValueField = new TextField("ifParameterValue", SWT.NONE);
			ifParameterValueField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return ifParameterValueField;
	}
	
	public TextField createIfExpressionField(Composite top, FormToolkit toolkit) {
		try {
			ifExpressionField = new TextField("ifExpression", SWT.WRAP
					| SWT.MULTI | SWT.V_SCROLL);		
			ifExpressionField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return ifExpressionField;
	}

	public RefTableField getObjectTypeField() {
		return objectTypeField;
	}

	public void setObjectTypeField(RefTableField objectTypeField) {
		this.objectTypeField = objectTypeField;
	}

	public RefTableField getIfParameterComparisonField() {
		return ifParameterComparisonField;
	}

	public void setIfParameterComparisonField(
			RefTableField ifParameterComparisonField) {
		this.ifParameterComparisonField = ifParameterComparisonField;
	}

	public RefTableField getIfParameterField() {
		return ifParameterField;
	}

	public void setIfParameterField(RefTableField ifParameterField) {
		this.ifParameterField = ifParameterField;
	}

	public TextField getIfParameterValueField() {
		return ifParameterValueField;
	}

	public void setIfParameterValueField(TextField ifParameterValueField) {
		this.ifParameterValueField = ifParameterValueField;
	}

	public TextField getIfExpressionField() {
		return ifExpressionField;
	}

	public void setIfExpressionField(TextField ifExpressionField) {
		this.ifExpressionField = ifExpressionField;
	}

	public Button getRadio1Btn() {
		return radio1Btn;
	}

	public void setRadio1Btn(Button radio1Btn) {
		this.radio1Btn = radio1Btn;
	}

	public Button getRadio2Btn() {
		return radio2Btn;
	}

	public void setRadio2Btn(Button radio2Btn) {
		this.radio2Btn = radio2Btn;
	}

	@Override
	String getDialogTitle() {
		return Message.getString("prd.designer_if_dialog_title");
	}

	@Override
	String getDialogMessage() {
		return Message.getString("prd.designer_if_dialog_message");
	}
	
}
