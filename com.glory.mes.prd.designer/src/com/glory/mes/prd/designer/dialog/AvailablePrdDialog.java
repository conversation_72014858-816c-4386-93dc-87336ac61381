package com.glory.mes.prd.designer.dialog;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.FormLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.ParameterDefinition;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.common.notation.RootContainer;
import com.glory.mes.prd.designer.dialog.procedurestep.ProcedureStepDialog;
import com.glory.mes.prd.designer.dialog.procedurestep.ProcedureStepModel;
import com.glory.mes.prd.designer.model.AbstractNode;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndIfState;
import com.glory.mes.prd.designer.model.IfState;
import com.glory.mes.prd.designer.model.MoveToState;
import com.glory.mes.prd.designer.model.ProcedureState;
import com.glory.mes.prd.designer.model.ProcessDefinition;
import com.glory.mes.prd.designer.model.RedirectEndState;
import com.glory.mes.prd.designer.model.ReworkState;
import com.glory.mes.prd.designer.model.StepState;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.bsh.JbpmExpressionEvaluator;
import com.glory.mes.prd.workflow.context.def.WFParameter;
import com.glory.mes.wip.action.PrdQueryAction;

public class AvailablePrdDialog extends BaseTitleDialog {

	private SelectedPrdModel value;
    private AbstractPrdDialogForm form;
    protected ManagedForm managedForm;
     
	private Node model = null;
	private AbstractNode node = null;
	
	protected Control createOkCancelButtons(Composite parent) {
		Composite composite = new Composite(parent, SWT.NONE);
		// create a layout with spacing and margins appropriate for the font
		// size.
		GridData data = new GridData(GridData.FILL_HORIZONTAL);
		composite.setLayoutData(data);
		composite.setFont(parent.getFont());
		composite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_DIALOG_BUTTON_BAR_BG));
		
		composite.setLayout(new FormLayout());
		// Add the buttons to the button bar.
		createButtonsForButtonBar(composite);
		return composite;
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton ok = createSquareButton(parent, IDialogConstants.OK_ID, Message.getString(ExceptionBundle.bundle.CommonOk()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID, Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, -12);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
		
		if (form instanceof AvailableProcedureForm) {
			SquareButton steps = createStepsButton(parent, IDialogConstants.CANCEL_ID, Message.getString("prd.show_procedure_step_info"), null);
			fd = new FormData();
			fd.width = 120;
			fd.height = 35;
			fd.top = new FormAttachment(0, 15);
			fd.right = new FormAttachment(ok, -12, SWT.LEFT);
			fd.bottom = new FormAttachment(100, -15);
			steps.setLayoutData(fd);
		}
	}
	
	protected SquareButton createStepsButton(Composite parent, int id, String label, String type) {
		SquareButton button = UIControlsFactory.createButton(parent, type);

		button.setText(label);
		button.setFont(JFaceResources.getDialogFont());
		button.setData(new Integer(id));
		button.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if ("".equals(form.getFields().get("name").getValue()) || null == form.getFields().get("name").getValue()) {
	        		UI.showError(Message.getString("prd.process_procedure_is_required"));
	        		return;
	        	}
				String procedureName = String.valueOf(form.getFields().get("name").getValue());
				Object versionObj = form.getFields().get("version").getValue();
				try {
					ADManager adManager = Framework.getService(ADManager.class);

					StringBuffer whereClause = new StringBuffer(" name = '" + procedureName +"'");

					if (versionObj != null && !"".equals(String.valueOf(versionObj))) {
						String version = String.valueOf(versionObj);
						whereClause.append(" AND version = " + version);
					} else {
						whereClause.append(" AND status = 'Active'");
					}
					List<Procedure> procedures = adManager.getEntityList(Env.getOrgRrn(), Procedure.class, 1, whereClause.toString(), null);
					Procedure procedure = procedures.get(0);
					List<ProcedureStepModel> procedureStepModels = getProcessChildern(procedure);
					
					ProcedureStepDialog dialog = new ProcedureStepDialog(Display.getCurrent().getActiveShell(), procedureStepModels);
					dialog.open();
				} catch (Exception e) {
					e.printStackTrace();
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		});
		setButtonLayoutData(button);
		return button;
	}
	
	protected List<ProcedureStepModel> getProcessChildern(Procedure procedure) throws Exception {
		PrdManager prdManager = Framework.getService(PrdManager.class);
		
		PrdQueryAction queryAction = PrdQueryAction.newIntance();
		queryAction.setCopyNode(true);
		List<com.glory.mes.prd.workflow.graph.def.Node> nodeLevel1s = prdManager.getProcessDefinitionChildern(procedure, queryAction);				
		com.glory.mes.prd.model.ProcessDefinition procedurePf = prdManager.getProcessDefinition(procedure);

		List<ProcedureStepModel> procedureStepModels = new ArrayList<ProcedureStepModel>();
		for (com.glory.mes.prd.workflow.graph.def.Node nodeLevel1 : nodeLevel1s) {		
			if (nodeLevel1 instanceof com.glory.mes.prd.workflow.graph.node.StepState) {
				Step step = ((com.glory.mes.prd.workflow.graph.node.StepState) nodeLevel1).getUsedStep();
				com.glory.mes.prd.model.ProcessDefinition stepPf = prdManager.getProcessDefinition(step);
				List<WFParameter> stepParameters = stepPf.getWfParameters();

				ProcedureStepModel procedureStepModel = new ProcedureStepModel();
				procedureStepModel.setType("Step");
				procedureStepModel.setName(step.getName());
				procedureStepModel.setVersion(step.getVersion() != null ? step.getVersion().toString() : null);
				procedureStepModel.setDescription(step.getDescription());
				
				if (stepParameters != null) {
					for (WFParameter stepParameter : stepParameters) {
						// 显示工步上RecipeName和Mask
						if (stepParameter.getVariableName().startsWith(ParameterDefinition.PARAMETER_PPID)) {
							procedureStepModel.setRecipe(stepParameter.getDefaultValue());
						}
						if (stepParameter.getVariableName().startsWith(ParameterDefinition.PARAMETER_MASK)) {
							procedureStepModel.setMask(stepParameter.getDefaultValue());
						}
						
						// 显示模块上RecipeName和Mask
						if (procedurePf.getWfParameters() != null) {
							for (WFParameter procedureParameter : procedurePf.getWfParameters()) {
								if (stepParameter.getVariableName().equals(procedureParameter.getVariableName())) {
									if (procedureParameter.getVariableName().startsWith(ParameterDefinition.PARAMETER_PPID)) {
										procedureStepModel.setRecipe(procedureParameter.getDefaultValue());
									}
									if (procedureParameter.getVariableName().startsWith(ParameterDefinition.PARAMETER_MASK)) {
										procedureStepModel.setMask(procedureParameter.getDefaultValue());
									}
								}
							}
						}
					}
				}

				procedureStepModels.add(procedureStepModel);
			} else if (nodeLevel1 instanceof com.glory.mes.prd.workflow.graph.node.MoveToState) {
				ProcedureStepModel procedureStepModel = new ProcedureStepModel();
				procedureStepModel.setType("MoveTo");
				procedureStepModel.setName(nodeLevel1.getName());
				procedureStepModel.setDescription(nodeLevel1.getDescription());
				procedureStepModels.add(procedureStepModel);
			} else if (nodeLevel1 instanceof com.glory.mes.prd.workflow.graph.node.IfState) {
				ProcedureStepModel procedureStepModel = new ProcedureStepModel();
				procedureStepModel.setType("If");
				procedureStepModel.setName(nodeLevel1.getName());
				procedureStepModel.setDescription(nodeLevel1.getDescription());
				procedureStepModels.add(procedureStepModel);
			} else if (nodeLevel1 instanceof com.glory.mes.prd.workflow.graph.node.ElseState) {
				ProcedureStepModel procedureStepModel = new ProcedureStepModel();
				procedureStepModel.setType("Else");
				procedureStepModel.setName(nodeLevel1.getName());
				procedureStepModel.setDescription(nodeLevel1.getDescription());
				procedureStepModels.add(procedureStepModel);
			} else if (nodeLevel1 instanceof com.glory.mes.prd.workflow.graph.node.EndIfState) {
				ProcedureStepModel procedureStepModel = new ProcedureStepModel();
				procedureStepModel.setType("EndIf");
				procedureStepModel.setName(nodeLevel1.getName());
				procedureStepModel.setDescription(nodeLevel1.getDescription());
				procedureStepModels.add(procedureStepModel);
			} else if (nodeLevel1 instanceof com.glory.mes.prd.workflow.graph.node.ReworkState) {
				ProcedureStepModel procedureStepModel = new ProcedureStepModel();
				procedureStepModel.setType("Rework");
				procedureStepModel.setName(((com.glory.mes.prd.workflow.graph.node.ReworkState)nodeLevel1).getName());
				procedureStepModel.setDescription(((com.glory.mes.prd.workflow.graph.node.ReworkState)nodeLevel1).getDescription());
				procedureStepModels.add(procedureStepModel);
			}					
								
		}			
		return procedureStepModels;		
	}
	
	public AvailablePrdDialog(Shell parent, Node model) {
		super(parent);
		this.model = model;
		this.node = (AbstractNode)model.getSemanticElement();

		if (model.getSemanticElement() instanceof ProcedureState) {
			ProcedureState procedure = (ProcedureState)model.getSemanticElement();
	        value = new SelectedPrdModel();
	        value.setName(procedure.getProcedure().getName());
	        value.setVersion(procedure.getProcedure().getVersion());
		} else if (model.getSemanticElement() instanceof StepState) {
			StepState step = (StepState)model.getSemanticElement();
	        value = new SelectedPrdModel();
	        value.setName(step.getStep().getName());
	        value.setVersion(step.getStep().getVersion());
	        if (step.getStep().getStageId() == null || step.getStep().getStageId().trim().length() == 0) {
	        	if (model.getContainer() instanceof RootContainer) {
	        		RootContainer container = (RootContainer)model.getContainer();
	        		if (container.getDefaultStageId() != null) {
	        			value.setStageId(container.getDefaultStageId());
	        		}
	        	}
	        } else {
	        	value.setStageId(step.getStep().getStageId());
	        }
		} else if (model.getSemanticElement() instanceof IfState) {
			IfState ifNode = (IfState)model.getSemanticElement();
			value = new SelectedPrdModel();
			value.setName(node.getName());
			value.setObjectType(ifNode.getObjectType());
			value.setIfParameter(ifNode.getIfParameter());
			value.setIfParameterComparison(ifNode.getIfParameterComparison());
			value.setIfParameterValue(ifNode.getIfParameterValue());
			value.setIfExpression(ifNode.getIfExpression());
		} else if (model.getSemanticElement() instanceof ElseState) {
			value = new SelectedPrdModel();
			value.setName(node.getName());
		} else if (model.getSemanticElement() instanceof EndIfState) {
			value = new SelectedPrdModel();
			value.setName(node.getName());
		} else if (model.getSemanticElement() instanceof ReworkState) {
			ReworkState reworkState = (ReworkState)model.getSemanticElement();
			value = new SelectedPrdModel();
			value.setName(reworkState.getProcedure().getName());
	        value.setReworkProcedureName(reworkState.getProcedure().getName());
	        value.setReworkProcedureVersion(reworkState.getProcedure().getVersion());
	        value.setIsDynamic(reworkState.getIsDynamic());
	        value.setTarget(reworkState.getTarget());
	        value.setObjectType(reworkState.getObjectType());
		} else if (model.getSemanticElement() instanceof MoveToState) {
			MoveToState moveToLocState = (MoveToState)model.getSemanticElement();
			value = new SelectedPrdModel();
			value.setTarget(moveToLocState.getTarget());
			value.setIsDynamic(moveToLocState.getIsDynamic());
		} else if (model.getSemanticElement() instanceof RedirectEndState) {
			RedirectEndState endState = (RedirectEndState)model.getSemanticElement();
			
			value = new SelectedPrdModel();
			value.setName(node.getName());
			value.setRedirectPath(endState.getHiSuperInstruction());
		}
		
	}
	
    protected void buttonPressed(int buttonId) {
        if (buttonId == IDialogConstants.OK_ID) {
        	if (form instanceof AvailableStepForm) {
	        	if ("".equals(form.getFields().get("stageId").getValue()) || null == form.getFields().get("stageId").getValue()) {
	        		UI.showError(Message.getString("prd.procedure_stageId_input"));
	        		return;
	        	}
	        	if ("".equals(form.getFields().get("name").getValue()) || null == form.getFields().get("name").getValue()) {
	        		UI.showError(Message.getString("prd.procedure_name_input"));
	        		return;
	        	}
	        } else if (form instanceof AvailableProcedureForm) {
	        	if ("".equals(form.getFields().get("name").getValue()) || null == form.getFields().get("name").getValue()) {
	        		UI.showError(Message.getString("prd.process_procedure_is_required"));
	        		return;
	        	}
	        } else if (form instanceof AvailableIfForm) {
	        	AvailableIfForm availableIfForm = (AvailableIfForm) form;
	        	if (availableIfForm.getRadio1Btn().getSelection()) { 
		        	if (availableIfForm.getIfParameterField().getValue() == null || "".equals(availableIfForm.getIfParameterField().getValue())) {
		        		UI.showError(Message.getString("prd.if_parameter_is_required"));
		        		return;
		        	}
		        	if (availableIfForm.getIfParameterValueField().getValue() == null || "".equals(availableIfForm.getIfParameterValueField().getValue())) {
		        		UI.showError(Message.getString("prd.if_parametervalue_is_required"));
		        		return;
		        	}
	        	} else if (availableIfForm.getRadio2Btn().getSelection()) {
	        		if (availableIfForm.getIfExpressionField().getValue() == null || "".equals(availableIfForm.getIfExpressionField().getValue())) {
		        		UI.showError(Message.getString("prd.if_expression_is_required"));
		        		return;
		        	} else {
		        		try {
		        			JbpmExpressionEvaluator.parser(availableIfForm.getIfExpressionField().getValue().toString());
		        		}  catch (Exception e1) {
		        			UI.showError(Message.getString("prd.if_expression_is_not_correct"));
		        			return;
		        		}
		        	}
	        	}        	
	        } else if (form instanceof AvailableReworkForm) {
	        	Object procedureName = form.getFields().get("reworkProcedureName").getValue();
	        	if (procedureName == null || String.valueOf(procedureName).trim().length() == 0) {
	        		if (!UI.showConfirm(Message.getString("prd.designer_rework_procedure_confirm"))) {
	        			return;
	        		}
	        	}
	        	
	        	IField controlTypeField = form.getFields().get("objectType");
	        	IField controlCountField = form.getFields().get("target");
	        	if (controlTypeField != null && controlCountField != null) {
	        		Object objectType = controlTypeField.getValue();
		        	Object target = controlCountField.getValue();
		        	if (objectType != null || target != null) {
		        		if (!(objectType != null && target != null)) {
		        			UI.showError("wip.rework_control_data_error");
		        			return;
		        		}
		        	}
	        	}
	        	
	        	
	        } else if (form instanceof AvailableTargetForm) {
	        	Object location = form.getFields().get("target").getValue();
	        	Object isDynamic = form.getFields().get("isDynamic").getValue();
	        	if ((location == null || String.valueOf(location).trim().length() == 0)
	        			&& ((Boolean)isDynamic == false)) {
	        		UI.showWarning(Message.getString("prd.designer_location_two_choose_one"));
	        		return;
//	        		if (!UI.showConfirm(Message.getString("prd.designer_location_confirm"))) {
//	        			return;
//	        		}
	        	}
	        } else if (form instanceof AvailableRedirectEndForm) {
	        	String superiorProcedureName = DBUtil.toString(form.getFields().get("superiorProcedureName").getValue());
	        	String superiorProcedureStepName = DBUtil.toString(form.getFields().get("superiorProcedureStepName").getValue());
	        	String targetPartName = DBUtil.toString(form.getFields().get("targetPartName").getValue());
	        	String targetProcedureName = DBUtil.toString(form.getFields().get("targetProcedureName").getValue());
	        	String targetReworkProcedures = "";
	        	List<Procedure> procedures = (List<Procedure>)form.getFields().get("targetReworkProcedures").getValue();
	        	if (CollectionUtils.isNotEmpty(procedures)) {
		        	for (Procedure procedure : procedures) {
		        		targetReworkProcedures += procedure.getName() + "/";
		        	}
		        	targetReworkProcedures = targetReworkProcedures.substring(0, targetReworkProcedures.length() - 1);
	        	}
	        	String targetProcedureStepName = DBUtil.toString(form.getFields().get("targetProcedureStepName").getValue());
	        	
	        	if (!StringUtil.isEmpty(superiorProcedureName) || !StringUtil.isEmpty(superiorProcedureStepName)) {
	        		if (!(!StringUtil.isEmpty(superiorProcedureName) && !StringUtil.isEmpty(superiorProcedureStepName))) {
	        			UI.showInfo(Message.getString("prd.designer_redirect_to_superior"));
	        			return;
	        		}
	        	}
	        	
	        	if (!StringUtil.isEmpty(targetProcedureName) || !StringUtil.isEmpty(targetProcedureStepName)) {
	        		if (!(!StringUtil.isEmpty(targetProcedureName) && !StringUtil.isEmpty(targetProcedureStepName))) {
	        			UI.showInfo(Message.getString("prd.designer_redirect_to_main"));
	        			return;
	        		}
	        	}
	        	
				if (StringUtil.isEmpty(superiorProcedureName) && StringUtil.isEmpty(superiorProcedureStepName)
						&& StringUtil.isEmpty(targetProcedureName) && StringUtil.isEmpty(targetProcedureStepName)) {
        			UI.showInfo(Message.getString("prd.designer_redirect_end_path_null"));
        			return;
        		}
	        			
	        	
				if ((!StringUtil.isEmpty(superiorProcedureName) || !StringUtil.isEmpty(superiorProcedureStepName))
						&& (!StringUtil.isEmpty(targetPartName) || !StringUtil.isEmpty(targetProcedureName)
								|| !StringUtil.isEmpty(targetReworkProcedures)
								|| !StringUtil.isEmpty(targetProcedureStepName))) {

					if (!UI.showConfirm(Message.getString("prd.designer_redirect_priority"))) {
						return;
					}
				}
	        }
        	
            int runner = 0;
            managedForm.getMessageManager().removeAllMessages();
			if (form != null) {
				boolean saveFlag = true;
				if (!form.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					PropertyUtil.copyProperties(value, form.getObject(), form.getCopyProperties());
				}
			}
			
            if (value == null) {
            	return;
            }
            
            String name = value.getName(); 
            if (model.getSemanticElement() instanceof ReworkState) {
            	name = value.getReworkProcedureName();
            } else if (model.getSemanticElement() instanceof MoveToState) {
            	name = value.getTarget();
            } else if (model.getSemanticElement() instanceof RedirectEndState) {
            	if (!StringUtil.isEmpty(value.getSuperiorProcedureName()) 
						&& !StringUtil.isEmpty(value.getSuperiorProcedureStepName())) {
            		name = value.getSuperiorProcedureStepName();
				} else if (!StringUtil.isEmpty(value.getTargetProcedureName()) 
						&& !StringUtil.isEmpty(value.getTargetProcedureStepName())) {
					name = value.getTargetProcedureStepName();
				}
            }
            
            String prefix = name;
            //如果为指定返工流程则默认为NA
        	if (prefix == null || prefix.trim().length() == 0) {
        		prefix = "NA";
        	}
        	
			while (runner < 1000) {
    			String candidate = runner == 0 ? prefix : prefix + "_" + runner;
    			AbstractNode findNode =(AbstractNode) ((ProcessDefinition)model.getContainer().getSemanticElement()).getNodeElementByName(candidate);
    			if (findNode == null || findNode.equals( model.getSemanticElement()/*当前双击的节点*/)) {
    				if(model.getSemanticElement() instanceof ProcedureState){
	    				ProcedureState procedureState = (ProcedureState)model.getSemanticElement();
	    				procedureState.setName(candidate);
	    				procedureState.getProcedure().setName(name);
	    				if( form.getFields().get("version") != null && 
	    						form.getFields().get("version").getValue() != null) {
	    					procedureState.getProcedure().setVersion(String.valueOf(form.getFields().get("version").getValue()));
    					} else {
    						procedureState.getProcedure().setVersion("");
    					}
    				} else if (model.getSemanticElement() instanceof StepState) {
    					StepState stepState = (StepState)model.getSemanticElement();
    					stepState.setName(candidate);
    					stepState.getStep().setName(name);
    					stepState.getStep().setStageId(String.valueOf(form.getFields().get("stageId").getValue()));
    					if (form.getFields().get("version").getValue() != null) {
    						stepState.getStep().setVersion(String.valueOf(form.getFields().get("version").getValue()));
    					} else {
    						stepState.getStep().setVersion("");
    					}
    				} else if (model.getSemanticElement() instanceof IfState) {
    					IfState ifState = (IfState)model.getSemanticElement();
    					ifState.setName(candidate);
    					
    					AvailableIfForm availableIfForm = (AvailableIfForm) form;   		  
    		        	if (availableIfForm.getObjectTypeField().getValue() != null) {
    						ifState.setObjectType(String.valueOf(availableIfForm.getObjectTypeField().getValue()));
    					} else {
    						ifState.setObjectType("");
    					}		        	
    					if (availableIfForm.getIfParameterField().getValue() != null) {
    						ifState.setIfParameter(String.valueOf(availableIfForm.getIfParameterField().getValue()));
    					} else {
    						ifState.setIfParameter("");
    					}					
    					if (availableIfForm.getIfParameterComparisonField().getValue() != null) {
    						ifState.setIfParameterComparison(String.valueOf(availableIfForm.getIfParameterComparisonField().getValue()));
    					} else {
    						ifState.setIfParameterComparison("");
    					}					
    					if (availableIfForm.getIfParameterValueField().getValue() != null) {
    						ifState.setIfParameterValue(String.valueOf(availableIfForm.getIfParameterValueField().getValue()));
    					} else {
    						ifState.setIfParameterValue("");
    					}					
    					if (availableIfForm.getIfExpressionField().getValue() != null) {
    						ifState.setIfExpression(String.valueOf(availableIfForm.getIfExpressionField().getValue()));
    					} else {
    						ifState.setIfExpression("");
    					}
    				} else if (model.getSemanticElement() instanceof ReworkState) {
    					ReworkState reworkState = (ReworkState)model.getSemanticElement();
    					reworkState.setName(candidate);
    					reworkState.getProcedure().setName(name);
    					if (form.getFields().get("reworkProcedureVersion").getValue() != null) {
    						reworkState.getProcedure().setVersion(String.valueOf(form.getFields().get("reworkProcedureVersion").getValue()));
    					} else {
    						reworkState.getProcedure().setVersion("");
    					}
    					if (form.getFields().get("isDynamic").getValue() != null) {
    						reworkState.setIsDynamic(DBUtil.toString(form.getFields().get("isDynamic").getValue()));
    					}
    					if (form.getFields().get("objectType") != null && form.getFields().get("objectType").getValue() != null) {
    						reworkState.setObjectType(DBUtil.toString(form.getFields().get("objectType").getValue()));
    					} else {
    						reworkState.setObjectType(null);
    					}
    					if (form.getFields().get("target") != null && form.getFields().get("target").getValue() != null) {
    						reworkState.setTarget(DBUtil.toString(form.getFields().get("target").getValue()));
    					} else {
    						reworkState.setTarget(null);
    					}
    				} else if (model.getSemanticElement() instanceof MoveToState) {
    					MoveToState moveToLocState = (MoveToState)model.getSemanticElement();
    					moveToLocState.setName(candidate);
    					if (form.getFields().get("target").getValue() != null) {
    						moveToLocState.setTarget(String.valueOf(form.getFields().get("target").getValue()));
    					}   					
    					if (form.getFields().get("isDynamic").getValue() != null) {
    						moveToLocState.setIsDynamic(DBUtil.toString(form.getFields().get("isDynamic").getValue()));
    					}
    				} else if (model.getSemanticElement() instanceof RedirectEndState) {
    					RedirectEndState redirectEndState = (RedirectEndState)model.getSemanticElement();
    					redirectEndState.setName(candidate);
    					
    					String redirectPath = value.getRedirectPath();
    					if (!StringUtil.isEmpty(redirectPath)) {
    						redirectEndState.setHiSuperInstruction(redirectPath);
    					} else {
    						redirectEndState.setHiSuperInstruction(null);
    					}
    					
    				}
    				break;
    			}
    			runner++;
    		}
        } else {
            value = null;
        }
        super.buttonPressed(buttonId);
    }

    protected void createFormContent(Composite composite) {
		FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
		ScrolledForm sForm = toolkit.createScrolledForm(composite);
		managedForm = new ManagedForm(toolkit, sForm);
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		Composite body = sForm.getForm().getBody();
		configureBody(body);
		if (node instanceof ProcedureState) {
			form = new AvailableProcedureForm(body, SWT.NONE, value);
		} else if (node instanceof StepState) {
     		form = new AvailableStepForm(body, SWT.NONE, value);
		} else if (node instanceof IfState) {
			form = new AvailableIfForm(body, SWT.NONE, value);
		} else if (node instanceof ReworkState) {
			form = new AvailableReworkForm(body, SWT.NONE, value);
		} else if (node instanceof MoveToState) {
			form = new AvailableTargetForm(body, SWT.NONE, value);
		} else if (node instanceof RedirectEndState) {
			form = new AvailableRedirectEndForm(body, SWT.NONE, value);
		}
		form.setLayoutData(new GridData(GridData.FILL_BOTH));	

	}

	@Override
	protected Control buildView(Composite parent) {
		createFormContent(parent);
		
        setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        if (StringUtil.isEmpty(form.getDialogTitle())) {
        	 setTitle(Message.getString("prd.procedure_designer_input"));
        } else {
        	 setTitle(form.getDialogTitle());
        }
       
        if (StringUtil.isEmpty(form.getDialogMessage())) {
        	setMessage(Message.getString("prd.procedure_designer_node"));
        } else {
        	setMessage(form.getDialogMessage());
        }
        return parent;
	}
	
	@Override
	public Point getMinSize() {
		return form.getDialogPoint();
	}

}
