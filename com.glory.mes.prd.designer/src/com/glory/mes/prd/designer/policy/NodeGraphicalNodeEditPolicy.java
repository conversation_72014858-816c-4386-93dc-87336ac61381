package com.glory.mes.prd.designer.policy;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.gef.Request;
import org.eclipse.gef.commands.Command;
import org.eclipse.gef.requests.CreateConnectionRequest;
import org.eclipse.gef.requests.ReconnectRequest;

import com.glory.framework.core.util.StringUtil;
import com.glory.mes.prd.designer.command.ConditionTransitionEdgeCreateCommand;
import com.glory.mes.prd.designer.command.EdgeCreateCommand;
import com.glory.mes.prd.designer.command.EdgeMoveCommand;
import com.glory.mes.prd.designer.command.ReworkTransitionEdgeCreateCommand;
import com.glory.mes.prd.designer.common.command.AbstractEdgeCreateCommand;
import com.glory.mes.prd.designer.common.command.AbstractEdgeMoveCommand;
import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.common.notation.NotationElement;
import com.glory.mes.prd.designer.common.policy.GraphicalNodeEditPolicy;
import com.glory.mes.prd.designer.model.AbstractNode;
import com.glory.mes.prd.designer.model.ConditionTransition;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndState;
import com.glory.mes.prd.designer.model.IfState;
import com.glory.mes.prd.designer.model.NodeElement;
import com.glory.mes.prd.designer.model.ProcessDefinition;
import com.glory.mes.prd.designer.model.RedirectEndState;
import com.glory.mes.prd.designer.model.ReworkState;
import com.glory.mes.prd.designer.model.ReworkTransition;
import com.glory.mes.prd.designer.model.StartState;
import com.glory.mes.prd.designer.model.StepState;
import com.glory.mes.prd.designer.model.Transition;
import com.glory.mes.prd.designer.notation.JpdlEdge;
import com.glory.mes.prd.designer.notation.JpdlNode;

public class NodeGraphicalNodeEditPolicy extends GraphicalNodeEditPolicy {
	
	IEventBroker eventBroker;
	
	public NodeGraphicalNodeEditPolicy(IEventBroker eventBroker) {
		super();
		this.eventBroker = eventBroker;
	}

	protected boolean canStart(Request request) {
		SemanticElement semanticElement = ((NotationElement)getNode()).getSemanticElement();
		if (semanticElement instanceof EndState || semanticElement instanceof RedirectEndState) {
			return false;
		}
		
		CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
		Object transitionObject = ((JpdlEdge)ccRequest.getNewObject()).getSemanticElement();
		Transition[] transitions = ((AbstractNode)semanticElement).getTransitions();
		List<Transition> normalTransitions = new ArrayList<Transition>();
		List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
		List<ConditionTransition> branchTransitions = new ArrayList<ConditionTransition>();
		for (Transition tran : transitions) {
			if (tran instanceof ReworkTransition) {
				reworkTransitions.add((ReworkTransition) tran);
			} else if (tran instanceof ConditionTransition) {
				branchTransitions.add((ConditionTransition) tran);
			} else {
				normalTransitions.add(tran);
			}
		}
		
		if (semanticElement instanceof AbstractNode) {
			if (transitionObject instanceof ReworkTransition) {
				//如果创建的是reworkTransition
				if (semanticElement instanceof StepState 
						|| semanticElement instanceof ReworkState
						|| semanticElement instanceof IfState
						|| semanticElement instanceof ElseState) {
					// 对于stepState和reworkState可以创建reworkTransition联结，其他类型的node都不可以
					//且stepState可以发出多个reworkTransition而reworkState只能有一个
					if (semanticElement instanceof StepState
							|| semanticElement instanceof IfState
							|| semanticElement instanceof ElseState) {
						return true;
					} else {
						if (reworkTransitions.size() > 0) {
							return false;
						} else if (branchTransitions.size() > 0) {
							return false;
						} else {
							return true;
						}
					}
				} else {
					//对于非stepState、reworkState、IfState和ElseState不允许创建reworkTransition联结
					return false;
				}
			} else if (transitionObject instanceof ConditionTransition) {
				
				//如果创建的是BranchTransition
				if (semanticElement instanceof StepState || semanticElement instanceof ReworkState) {
					return true;
				} else {
					//对于非stepState不允许创建BranchTransition联结
					return false;
				}
			} else {
				//如果创建的是Transition,看从该node发出的transition是否已经存在
				if (normalTransitions.size() >= 1) {
					//如果该节点已经作为source存在过了，所以不能再作为source了
					return false;	
				} else {
					return true;
				}
			}
		} else {
			return false;
		}
	}
	
	/*
	 * 支持两种事件，一种是把已建好的连线拖动，一种是重新新建连线
	 * @see com.glory.mes.prd.designer.common.policy.GraphicalNodeEditPolicy#canStop(org.eclipse.gef.Request)
	 */
	protected boolean canStop(Request request) {
		SemanticElement semanticElement = ((NotationElement)getNode()).getSemanticElement();
		NodeElement[] nodes = ((ProcessDefinition)((JpdlNode)getNode()).getContainer().getSemanticElement()).getNodeElements();
				
		Object transitionObject = null;
		if (request instanceof CreateConnectionRequest) {
			CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
			transitionObject = ((JpdlEdge)ccRequest.getNewObject()).getSemanticElement();
		} else if (request instanceof ReconnectRequest) {
			ReconnectRequest rcRequest = (ReconnectRequest) request;			
			transitionObject = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSemanticElement();
		}		
		
		if (semanticElement instanceof StartState) {
			return false;
		} else if (semanticElement instanceof AbstractNode) {
			AbstractNode targetNode = (AbstractNode)semanticElement;//联结的target
			if (transitionObject instanceof ReworkTransition) {
				//如果创建的是reworkTransition
				Node source = null;
				if (request instanceof CreateConnectionRequest) {
					CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
					source = (Node)((ReworkTransitionEdgeCreateCommand)ccRequest.getStartCommand()).getSource();
				} else if (request instanceof ReconnectRequest) {
					ReconnectRequest rcRequest = (ReconnectRequest) request;			
					source = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSource();
				}								
				SemanticElement sourceNode = source.getSemanticElement();//联结的source
				if (sourceNode instanceof StepState) {
					if (targetNode instanceof ReworkState) {
						//因为stepState允许发出多个reworkTransition
						//需要判断一下由该stepState发出的联结到target的reworkTransition是否已经存在
						//如果存在则不允许产生联结
						boolean targetIsReworkState = (targetNode instanceof ReworkState);
						boolean transitionAlreadyExists = false;
						
						Transition[] transitions = ((StepState) sourceNode).getTransitions();
						List<Transition> normalTransitions = new ArrayList<Transition>();
						List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
						for (Transition tran : transitions) {
							if (tran instanceof ReworkTransition) {
								reworkTransitions.add((ReworkTransition) tran);
							} else {
								normalTransitions.add(tran);
							}
						}
						for (ReworkTransition rt : reworkTransitions) {
							String target = rt.getTo();
							if (!StringUtil.isEmpty(target) && target.equals(targetNode.getName())) {
								transitionAlreadyExists = true;
								break;
							}
						}
						return targetIsReworkState && !transitionAlreadyExists;
					} else {
						return false;
					}
				} 
//				else if (sourceNode instanceof IfState
//						|| sourceNode instanceof ElseState) {
//					if (targetNode instanceof ReworkState) {
//						//只允许发出一个reworkTransitiond
//						Transition[] transitions = ((AbstractNode) sourceNode).getTransitions();
//						List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
//						for (Transition tran : transitions) {
//							if (tran instanceof ReworkTransition) {
//								reworkTransitions.add((ReworkTransition) tran);
//							} 
//						}
//						if (reworkTransitions.size() > 0) {
//							return false;
//						} else {
//							return true;
//						}
//					}
//				}
				else if (sourceNode instanceof ReworkState) {
//					if (targetNode instanceof StepState 
//							|| targetNode instanceof IfState 
//							|| targetNode instanceof ElseState 
//							|| targetNode instanceof EndIfState 
//							|| targetNode instanceof EndState) {
//						//只允许发出一个reworkTransitiond
//						Transition[] transitions = ((ReworkState) sourceNode).getTransitions();
//						List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
//						for (Transition tran : transitions) {
//							if (tran instanceof ReworkTransition) {
//								reworkTransitions.add((ReworkTransition) tran);
//							} 
//						}
//						if (reworkTransitions.size() > 0) {
//							return false;
//						} else {
//							return true;
//						}
//					}
					return false;
				} 
				return true;
			} else if (transitionObject instanceof ConditionTransition) {
				//如果创建的是reworkTransition
				Node source = null;
				if (request instanceof CreateConnectionRequest) {
					CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
					source = (Node)((ConditionTransitionEdgeCreateCommand)ccRequest.getStartCommand()).getSource();
				} else if (request instanceof ReconnectRequest) {
					ReconnectRequest rcRequest = (ReconnectRequest) request;			
					source = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSource();
				}								
				SemanticElement sourceNode = source.getSemanticElement();//联结的source
				
				if (targetNode instanceof ReworkState 
						|| targetNode instanceof StepState
						|| targetNode instanceof EndState
						|| targetNode instanceof RedirectEndState) {
					// 因为stepState允许发出多个contitionTransition
					// 需要判断一下由该stepState发出的联结到target的contitionTransition是否已经存在
					// 如果存在则不允许产生联结
					boolean transitionAlreadyExists = false;
					
					Transition[] transitions = ((AbstractNode) sourceNode).getTransitions();
					List<Transition> exsitsTransitions = new ArrayList<Transition>();
					for (Transition tran : transitions) {
						if (tran instanceof ConditionTransition) {
							exsitsTransitions.add(tran);
						} else if (tran instanceof ReworkTransition) {
							// Rework不在此处理
						} else {
							exsitsTransitions.add(tran);
						}
					}
					for (Transition rt : exsitsTransitions) {
						String target = rt.getTo();
						if (!StringUtil.isEmpty(target) && target.equals(targetNode.getName())) {
							transitionAlreadyExists = true;
							break;
						}
					}
					
					return !transitionAlreadyExists;
				}
				
				return false;
			} else {
				if (targetNode instanceof EndState || targetNode instanceof RedirectEndState) {
					return true;
				}
				
				if (targetNode instanceof ReworkState) {
					//reworkState只能用reworkTransition联结
					return false;
				}
				
				Node source = null;
				if (request instanceof CreateConnectionRequest) {
					CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
					source = (Node)((EdgeCreateCommand)ccRequest.getStartCommand()).getSource();
				} else if (request instanceof ReconnectRequest) {
					ReconnectRequest rcRequest = (ReconnectRequest) request;			
					source = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSource();
				}								
				SemanticElement sourceNode = source.getSemanticElement();//联结的source
				if (sourceNode instanceof ReworkState && targetNode instanceof StepState) {
					return true;
				}
				
				for (NodeElement node : nodes) {
					if (node instanceof AbstractNode) {
						Transition[] transitions = ((AbstractNode)node).getTransitions();
						List<Transition> normalTransitions = new ArrayList<Transition>();
						for (Transition tran : transitions) {
							if (!(tran instanceof ReworkTransition)) {
								normalTransitions.add(tran);
							}
						}
						if (normalTransitions.size() >= 1) {
							if (targetNode.getName().equals(normalTransitions.get(0).getTo())) {
								//如果有一个节点的target是这个节点,那么它不可以再作为target
								return false;
							}
						}
					}
				}
				return true;
			}
		} else {
			return false;
		}
	}
	
	protected AbstractEdgeCreateCommand createEdgeCreateCommand(Request request) {
		CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
		Object createObject = ((JpdlEdge)ccRequest.getNewObject()).getSemanticElement();
		if (createObject instanceof ReworkTransition) {
			return new ReworkTransitionEdgeCreateCommand();
		} else if (createObject instanceof ConditionTransition) {
			return new ConditionTransitionEdgeCreateCommand(eventBroker);
		} else {
			return new EdgeCreateCommand();
		}
	}

	protected AbstractEdgeMoveCommand createEdgeMoveCommand() {
		return new EdgeMoveCommand();
	}
	
	@Override
	public Command getCommand(Request request) {
		//copy code from supper
		if (REQ_CONNECTION_START.equals(request.getType())) {
			return getConnectionCreateCommand((CreateConnectionRequest) request);
		} else if (REQ_CONNECTION_END.equals(request.getType())) {
			return getConnectionCompleteCommand((CreateConnectionRequest) request);
		} else if (REQ_RECONNECT_TARGET.equals(request.getType())) {
			return getReconnectTargetCommand((ReconnectRequest) request);
		} else if (REQ_RECONNECT_SOURCE.equals(request.getType())) {
			return getReconnectSourceCommand((ReconnectRequest) request);
		}
		return null;
	}
}
