package com.glory.mes.prd.designer.model;

public class IfState extends AbstractNode {
	
	protected String objectType;
	protected String ifParameter;
	protected String ifParameterComparison = "=";//比较运算符
	protected String ifParameterValue;
	protected String ifExpression;
	protected ElseState elseState;
	
	@Override
	public boolean isPossibleChildOf(NodeElementContainer nodeElementContainer) {
		int i=0;
		int j=0;
		int k=0;
		for(NodeElement element : nodeElementContainer.getNodeElements()){
			if(element instanceof IfState){
				i++;
			}else if(element instanceof ElseState){
				j++;
			}else if(element instanceof EndIfState){
				k++;
			}
		}
		return ((ProcessDefinition)nodeElementContainer).getStartState() != null && (i<=j || i<=k);
	}

	public String getObjectType() {
		return objectType;
	}

	public void setObjectType(String newObjectType) {
		String oldObjectType = objectType;
		objectType = newObjectType;
		firePropertyChange("objecttype", oldObjectType, newObjectType);
	}
	
	public String getIfParameter() {
		return ifParameter;
	}

	public void setIfParameter(String newIfParameter) {
		String oldIfParameter = ifParameter;
		ifParameter = newIfParameter;
		firePropertyChange("parameter", oldIfParameter, newIfParameter);
	}

	public String getIfParameterValue() {
		return ifParameterValue;
	}

	public void setIfParameterValue(String newIfParameterValue) {
		String oldIfParameterValue = ifParameterValue;
		ifParameterValue = newIfParameterValue;
		firePropertyChange("parametervalue", oldIfParameterValue, newIfParameterValue);
	}

	public String getIfExpression() {
		return ifExpression;
	}

	public void setIfExpression(String newIfExpression) {
		String oldIfExpression = ifExpression;
		ifExpression = newIfExpression;
		firePropertyChange("ifExpression", oldIfExpression, newIfExpression);
	}

	public ElseState getElseState() {
		if (elseState == null) {
			elseState = (ElseState)getFactory().createById("ElseState");
		}
		return elseState;
	}
	
	public void setElseState(ElseState elseState) {
		this.elseState = elseState;
	}

	public EndIfState getEndIfState(){
		if(elseState != null){
			return elseState.getEndIfState();
		}
		return null;
	}

	public String getIfParameterComparison() {
		return ifParameterComparison;
	}

	public void setIfParameterComparison(String newIfParameterComparison) {
		String oldIfParameterComparison = ifParameterComparison;
		ifParameterComparison = newIfParameterComparison;
		firePropertyChange("comparison", oldIfParameterComparison, newIfParameterComparison);
	}
	
	
}
