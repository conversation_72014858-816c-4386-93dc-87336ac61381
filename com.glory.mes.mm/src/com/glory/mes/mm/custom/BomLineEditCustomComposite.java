package com.glory.mes.mm.custom;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SnowFlake;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.BomAddAltMaterialDialog;
import com.glory.mes.mm.bom.BomLoadDialog;
import com.glory.mes.mm.bom.model.AbstractBomLine;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.bom.model.BomTemplate;
import com.glory.mes.mm.bom.model.BomTemplateLine;
import com.glory.mes.mm.bom.template.AbstractBomLineTableManager;
import com.glory.mes.mm.bom.template.BomTemplateAddMaterialDialog;
import com.glory.mes.mm.model.Material;
import com.glory.mes.mm.model.RawMaterial;
import com.glory.mes.mm.model.ToolMaterial;
import com.glory.mes.prd.model.Part;
import com.glory.framework.core.exception.ExceptionBundle;

public class BomLineEditCustomComposite extends CustomCompsite {

	public static final Logger logger = Logger.getLogger(BomLineEditCustomComposite.class);
	
	public static final String ATTRIBUTE_SHOW_ALTTERNATE_BUTTON = "ShowAlternateBtn";
	public static final String ATTRIBUTE_SHOW_TEMPLATE_BUTTON = "ShowTemplateBtn";
	public static final String ATTRIBUTE_SETUP_BOMLINE_STEP = "SetupBomLineStep";
	public static final String ATTRIBUTE_ITEM_CATEGORY = "ItemCategory";//支持多个ItemCategory
	public static final String ATTRIBUTE_TABLE_NAME = "TableName";
	
	public static final String TABLE_NAME_BOMLINE = "MMBomLine";
	
	protected String tableName;
	protected String itemCategory;
	protected boolean isShowAlternateBtn = true;
	protected boolean isShowTemplateBtn = false;
	protected boolean isSetupBomLineStep = false;
	
	public AbstractBomLineTableManager tableManager;
	
	public SquareButton saveTemplate, loadTemplate, up, down, add, delete, addAlternative;
	
	private Composite composite;
	
	private Bom bom;
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite content = toolkit.createComposite(parent, SWT.NONE);
		content = CustomCompsite.configureBody(content);
		
		try {			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = null;
			if (StringUtil.isEmpty(tableName)) {
				table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_BOMLINE);
			} else {
				table = adManager.getADTable(Env.getOrgRrn(), tableName);
			}
			tableManager = new AbstractBomLineTableManager(table);
			tableManager.setAutoSizeFlag(false);
			tableManager.newViewer(content);

			Composite btnComposite = toolkit.createComposite(content, SWT.NONE);
			this.composite = btnComposite;
			GridLayout layoutBtn = new GridLayout(7, false);
			btnComposite.setLayout(layoutBtn);					
			GridData gd1 = new GridData(GridData.END);	
			gd1.horizontalAlignment = SWT.RIGHT;		
			btnComposite.setLayoutData(gd1);			
			//上移
			up = UIControlsFactory.createButton(btnComposite, Message.getString("mm.move_up"), null);
			up.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						upAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			//下移
			down = UIControlsFactory.createButton(btnComposite, Message.getString("mm.move_down"), null);
			down.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						downAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			
			if (isShowTemplateBtn) {
				//另存为Bom模版
				saveTemplate = UIControlsFactory.createButton(btnComposite, Message.getString("mm.save_bom_template"), null);
				saveTemplate.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						try {
							saveAsTemplateAdapter();
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				});
				//导入Bom模版
				loadTemplate = UIControlsFactory.createButton(btnComposite, Message.getString("mm.import_bom_template"), null);
				loadTemplate.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						try {
							loadAdapter();
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				});
			}
			
			add = UIControlsFactory.createButton(btnComposite, "  " + Message.getString(ExceptionBundle.bundle.CommonAdd()) + "  ", null);
			add.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						addAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			
			if (isShowAlternateBtn) {
				//增加替代料
				addAlternative = UIControlsFactory.createButton(btnComposite, Message.getString("mm.increase_substitute_material"), null);
				addAlternative.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						try{
							addAlternativeAdapter(event);
						}catch(Exception e){
							e.printStackTrace();
						}
					}
				});
			}
			
			delete = UIControlsFactory.createButton(btnComposite, "  " + Message.getString(ExceptionBundle.bundle.CommonDelete()) + "  ", "Gray");
			delete.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						delAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			
		} catch (Exception e) {
			logger.error("LotTecnCustomCompsite createForm error:", e);
		}
		return content;
	}
	
	protected void saveAsTemplateAdapter() {
		try {
			List<BomLine> values = (List<BomLine>)(List<? extends Object>)tableManager.getInput();									
			
			BomTemplate bomTemplate = new BomTemplate();
			bomTemplate.setIsActive(true);
			bomTemplate.setOrgRrn(Env.getOrgRrn());
			bomTemplate.setName("BOM" + SnowFlake.generateId());
			bomTemplate.setUpdatedBy(Env.getUserName());
			bomTemplate.setStatus(VersionControl.STATUS_ACTIVE);
			
			List<BomTemplateLine> bomTemplateLines = new ArrayList<BomTemplateLine>();
			Map<String, BomLine> map = new HashMap<String, BomLine>();
			for (BomLine value : values) {	
				String key = (value.getStepName() != null ? value.getStepName() : "") + value.getMaterialName();
				if (map.containsKey(key)) {
					//物料在同一工步存在两条记录
					UI.showError(Message.formatString("mm.material_records_info"+"#"+value.getMaterialName()));
					return;
				} else {
					map.put(key, value);
				}
				if (value.getIsCritical() != null) {
					if ("Y".equals(value.getIsCritical()) && value.getFlushType() == null) {
						//为关键物料, 请维护倒扣料方式
						UI.showError( Message.formatString("mm.key_materials_info"+"#"+value.getMaterialName()));
						return;
					} 
					if ("Y".equals(value.getIsCritical()) 
							&& BomLine.FLUSH_TYPE_BOM.equals(value.getFlushType()) 
							&& value.getUnitQty() == null) {
						//物料，请输入单位用量
						UI.showError( Message.formatString("mm.material_dosage_info"+"#"+value.getMaterialName()));
						return;
					}
				} else {
					if (value.getUnitQty() == null) {
						//物料，请输入单位用量
						UI.showError(Message.formatString("mm.material_dosage_info"+"#"+value.getMaterialName()));
						return;
					}
				}
				if (value.getItemCategory() == null) {
					//物料，请输入类别
					UI.showError(Message.formatString("mm.material_kind_info"+"#"+value.getMaterialName()));
					return;
				}
				BomTemplateLine templateLine = new BomTemplateLine();
				templateLine.setOrgRrn(value.getOrgRrn());
				templateLine.setIsActive(value.getIsActive());
				templateLine.setSeqNo(value.getSeqNo());
				templateLine.setItemCategory(value.getItemCategory());
				templateLine.setMaterialRrn(value.getMaterialRrn());
				templateLine.setMaterialName(value.getMaterialName());
				templateLine.setMaterialDesc(value.getMaterialDesc());
				templateLine.setMaterialType(value.getMaterialType());
				templateLine.setMaterialVersion(value.getMaterialVersion());
				templateLine.setUnitQty(value.getUnitQty());
				templateLine.setUomId(value.getUomId());
				templateLine.setStepName(value.getStepName());
				templateLine.setStepVersion(value.getStepVersion());
				templateLine.setAlternateGroup(value.getAlternateGroup());
				templateLine.setIsAlternate(value.getIsAlternate());
				templateLine.setAlternatePercent(value.getAlternatePercent());
				templateLine.setAlternatePriority(value.getAlternatePriority());
				templateLine.setAlternateStrategy(value.getAlternateStrategy());
				templateLine.setIsSelected(value.getIsSelected());
				templateLine.setIsOptional(value.getIsOptional());
				templateLine.setIsAssembly(value.getIsAssembly());
				templateLine.setFixedQty(value.getFixedQty());
				templateLine.setLossRate(value.getLossRate());
				templateLine.setIsMain(value.getIsMain());
				templateLine.setIsCritical(value.getIsCritical());
				templateLine.setFlushType(value.getFlushType());
				templateLine.setValidFrom(value.getValidFrom());
				templateLine.setValidTo(value.getValidTo());
				templateLine.setComments(value.getComments());
				templateLine.setAttribute1(value.getAttribute1());
				templateLine.setAttribute2(value.getAttribute2());
				templateLine.setAttribute3(value.getAttribute3());
				templateLine.setAttribute4(value.getAttribute4());
				templateLine.setAttribute5(value.getAttribute5());
				templateLine.setIsProduction(value.getIsProduction());
				bomTemplateLines.add(templateLine);
			}
			bomTemplate.setBomTemplateLines(bomTemplateLines);
			
			BASManager basManager = Framework.getService(BASManager.class);
			basManager.active(bomTemplate, Env.getSessionContext());
			UI.showInfo(Message.getString("mm.save_success"));// 弹出提示框	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@SuppressWarnings("unchecked")
	protected void loadAdapter() {
		try {					
			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "MMBomTemplateLoad");
			EntityDialog dialog = new BomLoadDialog(adTable, new BomTemplate());
			if (dialog.open() == IDialogConstants.OK_ID) {				
				List<BomLine> bomLines = new ArrayList<BomLine>();
				BomTemplate bomTemplate = (BomTemplate)dialog.getAdObject();
				if (bomTemplate == null || bomTemplate.getBomTemplateLines() ==null) {
					return;
				}
				for (BomTemplateLine value : bomTemplate.getBomTemplateLines()) {						
					BomLine bomLine = new BomLine();
					bomLine.setOrgRrn(value.getOrgRrn());
					bomLine.setIsActive(value.getIsActive());
					bomLine.setSeqNo(value.getSeqNo());
					bomLine.setItemCategory(value.getItemCategory());
					bomLine.setMaterialRrn(value.getMaterialRrn());
					bomLine.setMaterialName(value.getMaterialName());
					bomLine.setMaterialDesc(value.getMaterialDesc());
					bomLine.setMaterialType(value.getMaterialType());
					bomLine.setMaterialVersion(value.getMaterialVersion());
					bomLine.setUnitQty(value.getUnitQty());
					bomLine.setUomId(value.getUomId());
					bomLine.setStepName(value.getStepName());
					bomLine.setStepVersion(value.getStepVersion());
					bomLine.setAlternateGroup(value.getAlternateGroup());
					bomLine.setIsAlternate(value.getIsAlternate());
					bomLine.setAlternatePercent(value.getAlternatePercent());
					bomLine.setAlternatePriority(value.getAlternatePriority());
					bomLine.setAlternateStrategy(value.getAlternateStrategy());
					bomLine.setIsSelected(value.getIsSelected());
					bomLine.setIsOptional(value.getIsOptional());
					bomLine.setIsAssembly(value.getIsAssembly());
					bomLine.setFixedQty(value.getFixedQty());
					bomLine.setLossRate(value.getLossRate());
					bomLine.setIsMain(value.getIsMain());
					bomLine.setIsCritical(value.getIsCritical());
					bomLine.setFlushType(value.getFlushType());
					bomLine.setValidFrom(value.getValidFrom());
					bomLine.setValidTo(value.getValidTo());
					bomLine.setComments(value.getComments());
					bomLine.setAttribute1(value.getAttribute1());
					bomLine.setAttribute2(value.getAttribute2());
					bomLine.setAttribute3(value.getAttribute3());
					bomLine.setAttribute4(value.getAttribute4());
					bomLine.setAttribute5(value.getAttribute5());
					bomLine.setIsProduction(value.getIsProduction());
					bomLines.add(bomLine);												
				}
				List<BomLine> tableLines = (List<BomLine>)(List<? extends Object>)tableManager.getInput();
				for (BomLine newbomLine : bomLines) {
					for (BomLine oldbomLine : tableLines) {
						
						if (newbomLine.getStepName() != null && !"".equals(newbomLine.getStepName())) {
							if (newbomLine.getMaterialName().equals(oldbomLine.getMaterialName()) 
									&& newbomLine.getStepName().equals(oldbomLine.getStepName())) {
								//导入的模版跟现有的BOM清单有重复
								UI.showWarning(Message.getString("mm.bom_list_repeat"));
								return;
							}
						} else {
							if (newbomLine.getMaterialName().equals(oldbomLine.getMaterialName()) 
									&& (oldbomLine.getStepName() == null || "".equals(oldbomLine.getStepName()))) {
								//导入的模版跟现有的BOM清单有重复
								UI.showWarning(Message.getString("mm.bom_list_repeat"));
								return;
							}
						}
					}
				}
				
				List<BomLine> allBomLines = new ArrayList<BomLine>();
				for (BomLine bomLine : tableLines) {
					allBomLines.add(bomLine);
				}					
				allBomLines.addAll(bomLines);
				tableManager.setInput(allBomLines);												
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	protected void upAdapter() {
		try {
			List<BomLine> bomLines = (List<BomLine>)(List<? extends Object>)tableManager.getInput();
			List<Object> os = tableManager.getCheckedObject();
			List<BomLine> sortList = new ArrayList<BomLine>();
			for(Object o : os) {
				sortList.add((BomLine) o);
			}
			//根据当前位置进行排序升序1 --> n
			for (int i = 0; i < sortList.size(); i++){  
                for (int j = 0; j < sortList.size() - i - 1; j++) {  
                    if (sortList.get(j).getSeqNo() > sortList.get(j+1).getSeqNo()) {  
                    	BomLine bomLine = sortList.get(j);
                    	sortList.set(j, sortList.get(j+1));  
                    	sortList.set(j + 1, bomLine);  
                    }  
                }  
            }  
			int tmpIndex = 1;
			if (sortList.size() != 0) {
				for (BomLine o : sortList) {
					BomLine checkBomLine = o;
				    tmpIndex = bomLines.indexOf(checkBomLine); 
					if (tmpIndex == 0) {
						break;
					}
				}
				if (tmpIndex != 0) {
					for (BomLine o : sortList) {
						BomLine bomLine = o;
						if (bomLines.contains(bomLine)) {
							int index = bomLines.indexOf(bomLine);						
							if (index > 0) {
								bomLines.remove(bomLine);
								bomLines.add(index - 1, bomLine);
							}
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	protected void downAdapter() {
		try {
			List<BomLine> bomLines = (List<BomLine>)(List<? extends Object>)tableManager.getInput();
			List<Object> os = tableManager.getCheckedObject();
			List<BomLine> sortList = new ArrayList<BomLine>();
			for (Object o : os) {
				sortList.add((BomLine) o);
			}
			//根据当前位置进行排序升序1 --> n
			for (int i=0; i<sortList.size(); i++) {  
                for (int j=0; j<sortList.size()-i-1; j++) {  
                    if (sortList.get(j).getSeqNo() > sortList.get(j+1).getSeqNo()) {  
                    	BomLine bomLine = sortList.get(j);
                    	sortList.set(j, sortList.get(j+1));  
                    	sortList.set(j+1, bomLine);  
                    }  
                }  
            }  
			if (sortList.size() != 0) {
				for (int i = sortList.size() - 1;i >= 0;i--) {
					BomLine bomLine = sortList.get(i);
					if (bomLines.contains(bomLine)) {
						int index = bomLines.indexOf(bomLine);	
						if (index == bomLines.size() - 1) {
							break;
						}
						if (index < bomLines.size() - 1) {
							bomLines.remove(bomLine);
							bomLines.add(index + 1, bomLine);
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	protected void addAdapter() {
		try {
			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "BOMMaterialSet");
			
			String baseWhereClause = null;
			if (!StringUtil.isEmpty(itemCategory)) {
				baseWhereClause = " clazz in (";
				for (String category : itemCategory.split(";")) {
					baseWhereClause = baseWhereClause + "'" + category + "',";
				}
				baseWhereClause = baseWhereClause.substring(0, baseWhereClause.length() - 1) + ")";
			}
			BomTemplateAddMaterialDialog dialog = new BomTemplateAddMaterialDialog(new ListTableManager(adTable, true), baseWhereClause, baseWhereClause, bom, isSetupBomLineStep);
			if (dialog.open() == IDialogConstants.OK_ID) {
				List<BomLine> bomLines = (List<BomLine>)(List<? extends Object>)tableManager.getInput();
				List<BomLine> lines = new ArrayList<BomLine>();
				for (BomLine bomLine : bomLines) {
					lines.add(bomLine);
				}								
				List<Material> materials = (List<Material>)(List) dialog.getSelectionList();
				for (Material material : materials) {
					boolean flag = true;
					for (BomLine line : lines) {
						if (material.getName().equals(line.getMaterialName()) 
								&&  material.getVersion().equals(line.getMaterialVersion())) {
							flag = false;
							break;
						}
					}
					if (flag) {
						BomLine line = new BomLine();
						line.setMaterialRrn(material.getObjectRrn());
						line.setMaterialName(material.getName());
						line.setMaterialDesc(material.getDescription());
						line.setMaterialType(material.getMaterialType());
						line.setMaterialVersion(material.getVersion());
						if (material instanceof ToolMaterial) {
							line.setItemCategory(AbstractBomLine.ITEMCATEGORY_TOOLS);
						} else if (material instanceof Part) {
							line.setItemCategory(AbstractBomLine.ITEMCATEGORY_COMPONENT);
						} else if (material instanceof RawMaterial) {
							line.setItemCategory(AbstractBomLine.ITEMCATEGORY_COMPONENT);
						}			
						line.setUomId(material.getUomId());
//						line.setIsCritical(material.getIsCritical());
//						line.setFlushType(material.getFlushType());
						lines.add(line);						
					}
				}	
				tableManager.setInput(lines);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	/**
	 * 移除行
	 */
	protected void delAdapter() {
		try {
			List<BomLine> bomLines = (List<BomLine>)(List<? extends Object>)tableManager.getInput();
			List<BomLine> lines = new ArrayList<BomLine>();
			for (BomLine bomLine : bomLines) {
				lines.add(bomLine);
			}				
			List<Object> os = tableManager.getCheckedObject();
			if (os.size() != 0) {
				for (Object o : os) {
					BomLine line = (BomLine) o;
					lines.remove(line);
				}
			}
			tableManager.setInput(lines);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	protected void addAlternativeAdapter(SelectionEvent event) {
		try {
			List<Object> objects = tableManager.getCheckedObject();
			if (objects == null || objects.size() != 1) {
				//请选择一行
				UI.showError(Message.getString("mm.choose_one_line"));
				return;
			} else {
				BomLine selectLine = (BomLine) objects.get(0);
				List<BomLine> bomLines = (List<BomLine>)(List<? extends Object>)tableManager.getInput();
				List<BomLine> lines = new ArrayList<BomLine>(); //所有料
				List<BomLine> alternateGroupLines = new ArrayList<BomLine>(); //已有的替代料
				boolean isHasGroup = false;//新增替代料如果点取消或者关闭弹窗没有清除选中行的组别信息，这里做额外清除逻辑
				if(selectLine.getAlternateGroup() != null) {
					isHasGroup = true;
				}
				for (BomLine bomLine : bomLines) {
					lines.add(bomLine);
					if (selectLine.getAlternateGroup() != null) {
						if (bomLine.getAlternateGroup() != null 
								&& bomLine.getAlternateGroup().equals(selectLine.getAlternateGroup())) {						
							alternateGroupLines.add(bomLine);
						}	
					} else {
						if (selectLine.getStepName() != null && !"".equals(selectLine.getStepName())) {
							if (selectLine.getStepName().equals(bomLine.getStepName()) 
									&& bomLine.getMaterialName().equals(selectLine.getMaterialName())) {
								if (bomLine.getAlternateGroup() == null || "".equals(bomLine.getAlternateGroup())) {
									//bomLine.setAlternateGroup(bomLine.getStepName() + selectLine.getMaterialRrn()); //生成替代组
									createBomLineGroup(selectLine , bomLine);
								}
							}										
							if (bomLine.getAlternateGroup() != null 
									&& bomLine.getAlternateGroup().equals(selectLine.getAlternateGroup())) {						
								alternateGroupLines.add(bomLine);
							}
						} else {
							if (bomLine.getMaterialName().equals(selectLine.getMaterialName()) 
									&& (bomLine.getStepName() == null || "".equals(bomLine.getStepName()))) {
								if (bomLine.getAlternateGroup() == null || "".equals(bomLine.getAlternateGroup())) {
									//bomLine.setAlternateGroup(DBUtil.toString(selectLine.getMaterialRrn())); //生成替代组
									createBomLineGroup(selectLine , bomLine);
								}
							}										
							if (bomLine.getAlternateGroup() != null 
									&& bomLine.getAlternateGroup().equals(selectLine.getAlternateGroup())) {						
								alternateGroupLines.add(bomLine);
							}
						}
					}
				}
				
				BomAddAltMaterialDialog dialog = new BomAddAltMaterialDialog (
													event.widget.getDisplay().getActiveShell(), alternateGroupLines);
				if (dialog.open() == IDialogConstants.OK_ID) {								
					for (BomLine oldLine : alternateGroupLines) {
						if (selectLine.getStepName() != null && !"".equals(selectLine.getStepName())) {
							if (!(selectLine.getStepName().equals(oldLine.getStepName()) 
									&& oldLine.getMaterialName().equals(selectLine.getMaterialName())
									&& oldLine.getMaterialVersion().equals(selectLine.getMaterialVersion()))) {
								lines.remove(oldLine); //先移除
							}
						} else {
							if (!(oldLine.getMaterialName().equals(selectLine.getMaterialName())
									&& oldLine.getMaterialVersion().equals(selectLine.getMaterialVersion()))) {	
								lines.remove(oldLine); //先移除
							}	
						}
					}	
					
					int index = lines.indexOf(selectLine); //从选择起始位置
					List<BomLine> newLines = (List<BomLine>)(List) dialog.getManager().getInput();	
					if (newLines.size() == 1) {
						alternateGroupLines.get(0).setIsAlternate(false);
						alternateGroupLines.get(0).setAlternateGroup(null);
						alternateGroupLines.get(0).setAlternateStrategy(null);
						alternateGroupLines.get(0).setAlternatePercent(null);
						alternateGroupLines.get(0).setAlternatePriority(null);
					}
					if (newLines != null && newLines.size() > 0) {																	
						for (int i = newLines.size() - 1; i >= 0; i--) {	
							if (selectLine.getStepName() != null && !"".equals(selectLine.getStepName())) {
								if (selectLine.getStepName().equals(newLines.get(i).getStepName()) 
										&& newLines.get(i).getMaterialName().equals(selectLine.getMaterialName())
										&& newLines.get(i).getMaterialVersion().equals(selectLine.getMaterialVersion())) {
									newLines.get(i).setIsAlternate(true);
//									newLines.get(i).setAlternateStrategy(dialog.getCombo());
								} else {
//									newLines.get(i).setAlternateStrategy(dialog.getCombo());
									newLines.get(i).setAlternateGroup(selectLine.getAlternateGroup());
									newLines.get(i).setIsAlternate(true);
									newLines.get(i).setStepName(selectLine.getStepName());																
									lines.add(index + 1, newLines.get(i));	//重新添加
								}
							} else {
								if (newLines.get(i).getMaterialName().equals(selectLine.getMaterialName())
										&& newLines.get(i).getMaterialVersion().equals(selectLine.getMaterialVersion())) {
									newLines.get(i).setIsAlternate(true);
//									newLines.get(i).setAlternateStrategy(dialog.getCombo());
								} else {
//									newLines.get(i).setAlternateStrategy(dialog.getCombo());
									newLines.get(i).setAlternateGroup(selectLine.getAlternateGroup());
									newLines.get(i).setIsAlternate(true);
									newLines.get(i).setStepName(selectLine.getStepName());																
									lines.add(index + 1, newLines.get(i));	//重新添加
								}
							}
						}
					}
					tableManager.setInput(lines);
				} else {
					if(!isHasGroup) {
						//清除选中行的组别信息  解决框点击取消组别混乱问题
						selectLine.setAlternateGroup(null);
					}
				}
			}						
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	//替代料组别生成规则
	public void createBomLineGroup(BomLine selectLine ,  BomLine bomLine) {
		List<BomLine> lines = (List<BomLine>)(List<? extends Object>)tableManager.getInput();
		List<BomLine> allBomLine = lines.stream().sorted(Comparator.comparing(BomLine::getAlternateGroup, Comparator.nullsFirst(String :: compareTo)))
				.collect(Collectors.toList());
        BomLine line = (BomLine) allBomLine.get(allBomLine.size() -1);
        if (line.getAlternateGroup() != null && !"".equals(line.getAlternateGroup())) {
			int nextGroupID =  1;
			int groupId = 0;
			groupId = Integer.parseInt(line.getAlternateGroup());
			nextGroupID = nextGroupID + groupId;
        	bomLine.setAlternateGroup(String.valueOf(nextGroupID)); //生成替代组
		} else {
			bomLine.setAlternateGroup("1"); //生成替代组
		}
	}	
	
	public void setEnable(boolean flag) {
		composite.setEnabled(flag);
	}
	
	@Override
	public void refresh() {
		tableManager.refresh();
	}

	@Override
	public void setValue(Object value) {
		tableManager.setInput((List<Object>)value);
	}

	@Override
	public Object getValue() {
		return tableManager.getInput();
	}

	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_SHOW_ALTTERNATE_BUTTON:
				isShowAlternateBtn = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_TEMPLATE_BUTTON:
				isShowTemplateBtn = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SETUP_BOMLINE_STEP:
				isSetupBomLineStep = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_ITEM_CATEGORY:
				itemCategory = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TABLE_NAME:
				tableName = formAttribute.getStringValue();
				break;
			}
		}
	}

	public Bom getBom() {
		return bom;
	}

	public void setBom(Bom bom) {
		this.bom = bom;
	}
	
}
