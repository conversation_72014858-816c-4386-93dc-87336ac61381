package com.glory.mes.mm.carrier.query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.framework.base.application.command.CommandFactory;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.mm.carrier.action.dialog.MMCarrierChangeLocationDialog;
import com.glory.mes.mm.carrier.action.dialog.MMCarrierCleanDialog;
import com.glory.mes.mm.carrier.action.dialog.MMCarrierHisQueryDialog;
import com.glory.mes.mm.carrier.action.dialog.MMCarrierHoldDialog;
import com.glory.mes.mm.carrier.action.dialog.MMCarrierReleaseDialog;
import com.glory.mes.mm.carrier.action.dialog.MMCarrierUpdateSpecDialog;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.consumable.model.Consumable;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.Durable;
import com.glory.mes.mm.durable.model.DurableAction;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

public class MMCarrierActionQueryEditor extends GlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.carrier.query.MMCarrierActionQueryEditor";
	public static final String AUHTORITY_ID = CommandFactory.getAutorityId(EDITOR_ID);
	
	public static final String TABLE_NAME = "MMCarrierUploadTable";
	
	private static final String BUTTON_HOLD = "hold";
	private static final String BUTTON_RELEASE = "release";
	private static final String BUTTON_CLEAN = "clean";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_HISQUERY = "hisquery";
	private static final String BUTTON_CHANGELOCATION = "changeLoaction";
	
	private static final String BUTTON_AVL = "avl";
	private static final String BUTTON_UVL = "uvl";
	private static final String BUTTON_SCR = "scr";
	private static final String BUTTON_UNSCR = "unscr";
	private static final String BUTTON_UNRES = "unres";
	private static final String BUTTON_RETURN = "return";
	private static final String BUTTON_UPDATESPEC = "updatespec";
	
	private static final String BUTTON_IMPORT = "import";
	
	private static final String FIELD_QUERYFORM = "carrierQuery";

	private QueryFormField queryFormField;
	
	private ToolItem itemHold;
	private ToolItem itemRelease;
	private ToolItem itemClean;
	private ToolItem itemHisQuery;
	
	private ToolItem itemAvl;
	private ToolItem itemUvl;
	private ToolItem itemScr;
	private ToolItem itemUNScr;
	private ToolItem itemUNRes;
	private ToolItem itemReturn;
	private ToolItem itemUpdateSpec;
	private ToolItem itemChangeLocation;
	
	protected NatTable natTable;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HOLD), this::holdAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RELEASE), this::releaseAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAN), this::cleanAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HISQUERY), this::hisQueryAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHANGELOCATION), this::changeLocationAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_AVL), this::avlAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UVL), this::uvlAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SCR), this::scrAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNSCR), this::unScrAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNRES), this::unResAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RETURN), this::returnAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UPDATESPEC), this::updateSpecAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_QUERYFORM, GlcEvent.EVENT_SELECTION_CHANGED),
				this::selectionChange);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_QUERYFORM, GlcEvent.EVENT_TABLE_CHECK),
				this::checkAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_IMPORT), this::importAdapter);
		
		init();
	}
	
	private void init() {
		queryFormField = form.getFieldByControlId(FIELD_QUERYFORM, QueryFormField.class);
		
		itemClean = (ToolItem) form.getButtonByControl(null, BUTTON_CLEAN);
		itemHold = (ToolItem) form.getButtonByControl(null, BUTTON_HOLD);
		itemRelease = (ToolItem) form.getButtonByControl(null, BUTTON_RELEASE);
		itemHisQuery = (ToolItem) form.getButtonByControl(null, BUTTON_HISQUERY);
		itemAvl = (ToolItem) form.getButtonByControl(null, BUTTON_AVL);
		itemUvl = (ToolItem) form.getButtonByControl(null, BUTTON_UVL);
		itemScr = (ToolItem) form.getButtonByControl(null, BUTTON_SCR);
		itemUNScr = (ToolItem) form.getButtonByControl(null, BUTTON_UNSCR);
		itemUNRes = (ToolItem) form.getButtonByControl(null, BUTTON_UNRES);
		itemReturn = (ToolItem) form.getButtonByControl(null, BUTTON_RETURN);
		itemUpdateSpec = (ToolItem) form.getButtonByControl(null, BUTTON_UPDATESPEC);
		itemChangeLocation = (ToolItem) form.getButtonByControl(null, BUTTON_CHANGELOCATION);
		
		itemClean.setEnabled(false);
		itemHold.setEnabled(false);
		itemRelease.setEnabled(false);
		itemHisQuery.setEnabled(false);
		itemAvl.setEnabled(false);
		itemUvl.setEnabled(false);
		itemScr.setEnabled(false);
		itemUNScr.setEnabled(false);
		itemUNRes.setEnabled(false);
		itemReturn.setEnabled(false);
		itemUpdateSpec.setEnabled(false);
		itemChangeLocation.setEnabled(false);
	}
	
	@Override
	public void initializeDatas(Map<String, Object> initDatas) {
		//如果有初始数据
		String carrierId = (String)initDatas.get(MesGlcEvent.PROPERTY_CARRIER_ID);
		if (!StringUtil.isEmpty(carrierId)) {
			try {
				TextField textField = (TextField) queryFormField.getQueryForm().getQueryForm().getFields().get("durableId");
				if (textField != null) {
					textField.setText(carrierId);
				}
				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, true);
				queryFormField.getQueryForm().getTableManager().setInput(Lists.newArrayList(carrier));
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	private void holdAdaptor(Object obj) {
		List<Object> objs = queryFormField.getCheckedObjects();
		List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
		MMCarrierHoldDialog baseDialog = new MMCarrierHoldDialog("MMCarrierActionHoldDialog", null, eventBroker, carriers);
		if (Dialog.OK == baseDialog.open()) {
			UI.showInfo(Message.getString("ras.carrier_hold_successed"));// 弹出提示框
			refreshAdaptor(null);
		}
	}
		
	private void releaseAdaptor(Object obj) {
		List<Object> objs = queryFormField.getCheckedObjects();
		List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
		MMCarrierReleaseDialog baseDialog = new MMCarrierReleaseDialog("MMCarrierActionReleaseDialog", null, eventBroker, carriers);
		if (Dialog.OK == baseDialog.open()) {
			UI.showInfo(Message.getString("ras.carrier_release_successed"));// 弹出提示框
			refreshAdaptor(null);
		}
	}
	
	private void cleanAdaptor(Object obj) {
		List<Object> objs = queryFormField.getCheckedObjects();	
		List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
		List<Carrier> cleanCarriers = carriers.stream().filter(
				r -> !Consumable.CLEANSTATE_DIRTY.equals(r.getCleanState())).collect(Collectors.toList());
		
		try {
			if (CollectionUtils.isNotEmpty(cleanCarriers)) {
				String reticlesStr = cleanCarriers.stream().map(Carrier::getDurableId).collect(Collectors.joining(","));
				// 提示载具是清洁状态是否还要清洁
				if (UI.showConfirm(Message.getString("mm.carrier_confirm_clean") + "<" + reticlesStr + ">")) {
					MMCarrierCleanDialog baseDialog = new MMCarrierCleanDialog("MMCarrierActionCleanDialog", null, eventBroker,carriers);
					if (Dialog.OK == baseDialog.open()) {
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
						refreshAdaptor(null);
					}
				}
			} else {
				MMCarrierCleanDialog baseDialog = new MMCarrierCleanDialog("MMCarrierActionCleanDialog", null, eventBroker,carriers);
				if (Dialog.OK == baseDialog.open()) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
					refreshAdaptor(null);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void updateSpecAdaptor(Object obj) {
		try {
			List<Object> objs = queryFormField.getCheckedObjects();
			List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
			MMCarrierUpdateSpecDialog baseDialog = new MMCarrierUpdateSpecDialog("MMCarrierActionSpecDialog", null, eventBroker,carriers);
			if (Dialog.OK == baseDialog.open()) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
				refreshAdaptor(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void hisQueryAdaptor(Object obj) {
		try {
			List<Object> objs = queryFormField.getCheckedObjects();
			List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
			MMCarrierHisQueryDialog baseDialog = new MMCarrierHisQueryDialog("MMCarrierActionHisQueryDialog", null, eventBroker,carriers);
			if (Dialog.OK == baseDialog.open()) {
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void checkAdaptor(Object obj) {
		try {			
			List<Object> objs = queryFormField.getCheckedObjects();
			boolean isChecked = CollectionUtils.isNotEmpty(objs);
			
			if (isChecked) {
				itemClean.setEnabled(true);
				itemChangeLocation.setEnabled(true);
				itemUpdateSpec.setEnabled(true);
				
				List<Durable> carriers = objs.stream().map(
						o -> ((Durable)o)).collect(Collectors.toList());
				
				// 检查是否可以暂停
				Optional<Durable> f = carriers.stream().filter(r -> Carrier.HOLDSTATE_ON.equals(r.getHoldState())).findFirst();
				if (f.isPresent()) {
					itemHold.setEnabled(false);
					// Hold后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemHold.setEnabled(true);
				}
				
				// 检查是否可以Release
				f = carriers.stream().filter(r -> Carrier.HOLDSTATE_OFF.equals(r.getHoldState())).findFirst();
				if (f.isPresent()) {
					itemRelease.setEnabled(false);
				} else {
					itemRelease.setEnabled(true);
				}
				
				DurableManager durableManager = Framework.getService(DurableManager.class);
				// 检查是否可以报废
				boolean isCanScrap = durableManager.checkDurableState(carriers, Carrier.EVENT_SCR, Env.getSessionContext());
				if (isCanScrap) {
					itemScr.setEnabled(true);
				} else {
					itemScr.setEnabled(false);
				}
				// 检查是否可以取消报废
				boolean isCanUnScrap = durableManager.checkDurableState(carriers, Carrier.EVENT_UNSCR, Env.getSessionContext());
				if (isCanUnScrap) {
					itemUNScr.setEnabled(true);
					// 报废后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemUNScr.setEnabled(false);
				}
				// 检查是否可以使可用
				boolean isCanAvl = durableManager.checkDurableState(carriers, Carrier.EVENT_AVL, Env.getSessionContext());
				if (isCanAvl) {
					itemAvl.setEnabled(true);
				} else {
					itemAvl.setEnabled(false);
				}
				// 检查是否可以使不可用
				boolean isCanUvl = durableManager.checkDurableState(carriers, Carrier.EVENT_UVL, Env.getSessionContext());
				if (isCanUvl) {
					itemUvl.setEnabled(true);
				} else {
					itemUvl.setEnabled(false);
				}
				// 检查是否可以客返
				boolean isCanReturn = true;
				for (Durable durable : carriers) {
					if (!Carrier.EVENT_SHIP.equals(durable.getState())) {
						isCanReturn = false;
						break;
					}
				}
				if (isCanReturn) {
					itemReturn.setEnabled(true);
					// 发出后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemReturn.setEnabled(false);
				}
				// 检查是否可以取消预留
				boolean isCanUnRes = durableManager.checkDurableState(carriers, Carrier.EVENT_UNRES, Env.getSessionContext());
				if (isCanUnRes) {
					itemUNRes.setEnabled(true);
					// 预留后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemUNRes.setEnabled(false);
				}
			} else {
				itemClean.setEnabled(false);
				itemHold.setEnabled(false);
				itemRelease.setEnabled(false);
				itemScr.setEnabled(false);
				itemUNScr.setEnabled(false);
				itemAvl.setEnabled(false);
				itemUvl.setEnabled(false);
				itemReturn.setEnabled(false);
				itemUNRes.setEnabled(false);
				itemChangeLocation.setEnabled(false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void changeLocationAdaptor(Object obj) {
		List<Object> objs = queryFormField.getCheckedObjects();
		List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());				
		MMCarrierChangeLocationDialog baseDialog = new MMCarrierChangeLocationDialog("MMCarrierActionChgLocaDialog", null, eventBroker, carriers);
		if (Dialog.OK == baseDialog.open()) {
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
			refreshAdaptor(null);
		}
	}
	
	private void selectionChange(Object obj) {
		try {
			boolean isSelection = queryFormField.getSelectedObject() != null;		
			if (isSelection) {
				itemHisQuery.setEnabled(true);
			} else {
				itemHisQuery.setEnabled(false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void avlAdaptor(Object obj) {
		if (UI.showConfirm(Message.getString("mm.carrier_confirm_avl"))) {
			changeCarrierState(Carrier.EVENT_AVL);
		}	
	}
	
	private void uvlAdaptor(Object obj) {
		if (UI.showConfirm(Message.getString("mm.carrier_confirm_uvl"))) {
			changeCarrierState(Carrier.EVENT_UVL);
		}	
	}
	
	private void scrAdaptor(Object obj) {
		if (UI.showConfirm(Message.getString("mm.carrier_confirm_scr"))) {
			changeCarrierState(Carrier.EVENT_SCR);
		}	
	}
	
	private void unScrAdaptor(Object obj) {
		changeCarrierState(Carrier.EVENT_UNSCR);
	}
	
	private void unResAdaptor(Object obj) {
		if (UI.showConfirm(Message.getString("mm.carrier_confirm_unres"))) {
			changeCarrierState(Carrier.EVENT_UNRES);
		}		
	}
	private void returnAdaptor(Object obj) {
		try {
			if (UI.showConfirm(Message.getString("mm.carrier_confirm_return"))) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Object> objs = queryFormField.getCheckedObjects();
				if (CollectionUtils.isNotEmpty(objs)) {
					List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
					carrierLotManager.receiveCarriers(carriers, Env.getSessionContext());
					UI.showInfo(Message.getString("ras.logevent_success"));
					queryFormField.refresh();
				}
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	private void changeCarrierState(String eventId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			DurableAction action = new DurableAction();
			List<Object> objs = queryFormField.getCheckedObjects();
			if (CollectionUtils.isNotEmpty(objs)) {
				List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
				// TODO 修改载具状态并记录相应的历史	
				List<Durable> durables = new ArrayList<Durable>();
				List<Durable> durableLots = new ArrayList<Durable>();				
				for(Carrier carrier : carriers) {
					Durable durable = carrier;
					//AVL事件需要校验载具上是否有批次，有则不允许执行AVL事件
					if (eventId.equals(Carrier.EVENT_AVL)) {
						List<Lot> lots = lotManager.getLotsByDurableId(durable.getOrgRrn(), durable.getDurableId());
						if (lots != null && lots.size() > 0) {
							durableLots.add(durable);							
						}
					}
					durables.add(durable);
				}	
				String durableIdStr = durableLots.stream().map(Durable::getDurableId).collect(Collectors.joining(","));
				if (durableLots.size() > 0) {
					UI.showInfo(Message.getString("wip.carrier_assigned_lot_forbid_avl") + "<" + durableIdStr + ">");
					return;
				}
				durableManager.logEvents(durables, eventId, action, Env.getSessionContext());
				UI.showInfo(Message.getString("ras.logevent_success"));
				queryFormField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	private void importAdapter(Object object) {
		MMCarrierActionUpload uploads = new MMCarrierActionUpload(form.getAuthority(), null, TABLE_NAME);
		if (uploads.getUploadProgress().init()) {
			if (uploads.run()) {
				refreshAdaptor(null);
			}
		}
	}

	private void refreshAdaptor(Object obj) {
		queryFormField.refresh();
	}
	
}