package com.glory.mes.mm.carrier.action.dialog;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.carrier.action.CarrierActionDialog;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;

public class MMCarrierChangeLocationDialog extends CarrierActionDialog{
	
	private static int DIALOG_WIDTH = 400;
	private static int DIALOG_HEIGHT = 180;
	
	private static final String ADFORM_NAME = "MMCarrierActionChgLocaDialog";
	
	private static final String FIELD_CHANGETYPE = "changeType";
	private static final String FIELD_CHANGEFORM = "changeForm";
	
	private static final String CHANGE_TYPE_EQUIPMENT = "Equipment";
	private static final String CHANGE_TYPE_WAREHOUSE = "Warehouse";
	
	private static final String FORM_EQP = "MMCarrierActionChgLocaEqpDialog";
	private static final String FORM_WAREHOUSE = "MMCarrierActionChgLocaHousDialog";
	
	protected RadioField typeField;
	protected GlcFormField formField;
	protected List<Carrier> carriers;
	
	public MMCarrierChangeLocationDialog(String adFormName, String authority, IEventBroker eventBroker,List<Carrier> carriers) {
		super(ADFORM_NAME, authority, eventBroker);
		this.carriers = carriers;
		setCarrierList(carriers);
	}
	
	public MMCarrierChangeLocationDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		typeField = form.getFieldByControlId(FIELD_CHANGETYPE, RadioField.class);
		formField = form.getFieldByControlId(FIELD_CHANGEFORM, GlcFormField.class);
		formField.getForm().initValue(new Carrier());
		
		typeField.addValueChangeListener(new IValueChangeListener() {
			
			@Override
			public void valueChanged(Object sender, Object newValue) {
				try {
					ADManager adManager = Framework.getService(ADManager.class);
					if (CHANGE_TYPE_EQUIPMENT.equals(DBUtil.toString(newValue))) {
						ADForm adForm = adManager.getADForm(0L, FORM_EQP);
						formField.reflow(adForm);
						formField.getForm().initValue(new Carrier());
					} else if (CHANGE_TYPE_WAREHOUSE.equals(DBUtil.toString(newValue))) {
						ADForm adForm = adManager.getADForm(0L, FORM_WAREHOUSE);
						formField.reflow(adForm);
						formField.getForm().initValue(new Carrier());
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
				
			}
		});
		initLot();
	}
	
	@Override
	protected void okPressed() {	
		try {
			Map<String, Object> valueMap = (Map<String, Object>) formField.getValue();
			if (MapUtils.isEmpty(valueMap)) {
				return;
			}
			
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = carriers.get(0);
			String equipmentId = carrier.getEquipmentId();
			String portId = carrier.getPortId();
			String locatorId = carrier.getLocatorId();
			String warehouseId = carrier.getWarehouseId();
			
			String comment = "";
			if (CHANGE_TYPE_EQUIPMENT.equals(DBUtil.toString(typeField.getValue()))) {
				equipmentId = DBUtil.toString(valueMap.get("equipmentId"));
				portId = DBUtil.toString(valueMap.get("portId"));
				
				if (StringUtil.isEmpty(equipmentId) && StringUtil.isEmpty(portId)) {
					return;
				}
				// 位置修改到设备时也要清空仓库信息
				locatorId = null;
				warehouseId = null;
				
				comment += "Carrier Change Equipment to <" + equipmentId + ">, ";
				comment += "Change Port to <" + portId + ">, ";
				
				// 检查Port上是否有载具
				DurableManager rasManager = Framework.getService(DurableManager.class);
				Carrier portCarrier = rasManager.getCarrierByPortId(Env.getOrgRrn(), equipmentId, portId);
				if (portCarrier != null) {
					UI.showInfo(Message.getString("mm.carrier_already_on_port"));
					return;
				}
				
			} else if (CHANGE_TYPE_WAREHOUSE.equals(DBUtil.toString(typeField.getValue()))) {
				locatorId = DBUtil.toString(valueMap.get("locatorId"));
				warehouseId = DBUtil.toString(valueMap.get("warehouseId"));
				
				if (StringUtil.isEmpty(locatorId) && StringUtil.isEmpty(warehouseId)) {
					return;
				}
				
				// 位置修改到仓库时也要清空设备信息
				equipmentId = null;
				portId = null;
				
				comment += "Carrier Change Warehouse to <" + warehouseId + ">, ";
				comment += "Change Locator to <" + locatorId + ">, ";
			}
			durableManager.changeCarrierLocation(carrier, equipmentId, portId, warehouseId, 
					locatorId, comment, Env.getSessionContext());
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void initLot() {
		carriers = getCarrierList();
	}

	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}
