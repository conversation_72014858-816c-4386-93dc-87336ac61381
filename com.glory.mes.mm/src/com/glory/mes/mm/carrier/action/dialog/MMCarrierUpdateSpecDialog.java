package com.glory.mes.mm.carrier.action.dialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.carrier.action.CarrierActionDialog;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableSpec;

public class MMCarrierUpdateSpecDialog extends CarrierActionDialog{
	
	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;
	
	private static final String ADFORM_NAME = "MMCarrierActionSpecDialog";

	protected ListTableManagerField ListTableManagerFieldField;
	protected List<Carrier> carriers;
	private static final String FIELD_MMDURABLESPECFROM = "MMDurableSpecFrom";// 动态页面
	
	public MMCarrierUpdateSpecDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	public MMCarrierUpdateSpecDialog(String adFormName, String authority, IEventBroker eventBroker,List<Carrier> carriers) {
		super(ADFORM_NAME, authority, eventBroker);
		this.carriers = carriers;
		setCarrierList(carriers);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		ListTableManagerFieldField = form.getFieldByControlId(FIELD_MMDURABLESPECFROM, ListTableManagerField.class);
		initLot();
		
	}
	
	@Override
	public void initLot() {
		try {
			carriers = getCarrierList();
			ADManager adManager = Framework.getService(ADManager.class);
			List<DurableSpec> DurableSpecList = new ArrayList<DurableSpec>();
			Set<String> carrierDurableSpecNames = carriers.stream().map(Carrier :: getDurableSpecName).collect(Collectors.toSet());
			Map<String, Object> fieldMap = new HashMap<String, Object>();
			fieldMap.put("carrierDurableSpecNames", carrierDurableSpecNames);
			DurableSpecList = adManager.getEntityList(Env.getOrgRrn(), DurableSpec.class, Integer.MIN_VALUE, Integer.MAX_VALUE, "status = 'Active' and name in (:carrierDurableSpecNames)", "", fieldMap);	
			ListTableManagerFieldField.setValue(DurableSpecList);
			ListTableManagerFieldField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void okPressed() {	
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Object checkObject = ListTableManagerFieldField.getListTableManager().getSelectedObject();
			if(checkObject == null) {
				UI.showInfo(Message.getString("mm.choose_one_line"));// 弹出提示框
				return;
			}		
			DurableSpec durableSpec = durableManager.getDurableSpec((DurableSpec)checkObject,  Env.getSessionContext());
			if(CollectionUtils.isNotEmpty(carriers)) {
				for(Carrier carrier : carriers) {
					if(carrier.getDurableSpecName().equals(durableSpec.getName())) {
						carrier.setDurableSpecName(durableSpec.getName());
						carrier.setDurableSpecVersion(durableSpec.getVersion());
						carrier.setDurableType(durableSpec.getDurableType());
						carrier.setMainMatType(durableSpec.getMainMatType());
						carrier.setCapacity(durableSpec.getCapacity());
						carrier.setSlotDirection(durableSpec.getSlotDirection());
						carrier.setSubMatType(durableSpec.getSubMatType());
						carrier.setLimitCount(durableSpec.getLimitCount());
						carrier.setLimitTime(durableSpec.getLimitTime());
						carrier.setLimitTimeUnit(durableSpec.getTimeUnit());
					}else {
						UI.showInfo(carrier.getDurableId() + Message.getString("mm.durable_in_other_spec"));// 弹出提示框
						return;
					}
				}
			}
			durableManager.saveDurableList(carriers, Env.getSessionContext());
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}

	@Override
	public boolean isSupportMulitLot() {		
		return true;
	}

	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}
