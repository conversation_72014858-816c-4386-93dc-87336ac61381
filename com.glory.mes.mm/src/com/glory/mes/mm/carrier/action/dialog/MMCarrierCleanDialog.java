package com.glory.mes.mm.carrier.action.dialog;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.carrier.action.CarrierActionDialog;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableAction;
import com.glory.mes.mm.durable.model.DurableSpec;
import com.glory.mes.ras.eqp.EquipmentMatType;

public class MMCarrierCleanDialog extends CarrierActionDialog{
	
	private static int DIALOG_WIDTH = 400;
	private static int DIALOG_HEIGHT = 180;
	
	private static final String ADFORM_NAME = "MMCarrierActionCleanDialog";
	
	private static final String FIELD_ENTITYFORM = "actionCode";
	protected EntityFormField entityFormField;
	protected List<Carrier> carriers;


	public MMCarrierCleanDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	public MMCarrierCleanDialog(String adFormName, String authority, IEventBroker eventBroker, List<Carrier> carriers) {
		super(ADFORM_NAME, authority, eventBroker);
		this.carriers = carriers;
		setCarrierList(carriers);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		entityFormField = form.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		entityFormField.setValue(new DurableAction());
		entityFormField.refresh();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		carriers = getCarrierList();
		if (CollectionUtils.isEmpty(carriers)) {
			return;
		}
	}
	
	@Override
	protected void okPressed() {	
		try {
			if(entityFormField.validate()) {
				DurableAction durableAction = (DurableAction) entityFormField.getValue();
				DurableManager durableManager = Framework.getService(DurableManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				List<Carrier> carrierActions = new ArrayList<Carrier>();
				List<EquipmentMatType> matTypes = adManager.getEntityList(Env.getOrgRrn(), EquipmentMatType.class, 
						Env.getMaxResult(), "equipmentId = '" + durableAction.getComment() + "'", "");
				List<String> mainType = matTypes.stream().map(EquipmentMatType :: getMainMatType).collect(Collectors.toList());
				for(Carrier carrier : carriers) {
					Carrier carrierAction = carrier;
					DurableSpec spec = durableManager.getDurableSpec(Env.getOrgRrn(), null, carrier.getDurableSpecName(), carrier.getDurableSpecVersion());
					if(spec != null && mainType.size() > 0 && !mainType.contains(spec.getMainMatType())) {
						UI.showWarning(String.format(Message.getString("mm.eqp_and_carrier_mattpe_not_match"), carrierAction.getDurableId()));
						return;
					}
					
					carrierAction.setActionCode(durableAction.getActionCode());
					carrierAction.setActionReason(durableAction.getActionReason());
					carrierAction.setActionComment(durableAction.getActionComment());
					carrierAction.setEquipmentId(durableAction.getComment());
					carrierActions.add(carrierAction);
				}		
				//清空comment
				durableAction.setComment(null);
				durableManager.cleanCarriers(carriers, durableAction, Env.getSessionContext());
				super.okPressed();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} 
		
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}
