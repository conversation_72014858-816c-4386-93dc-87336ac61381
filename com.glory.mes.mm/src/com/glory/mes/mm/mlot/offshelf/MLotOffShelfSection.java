package com.glory.mes.mm.mlot.offshelf;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.MLotSection;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.framework.core.exception.ExceptionBundle;

public class MLotOffShelfSection extends MLotSection {

	public CheckBoxFixEditorTableManager manager;

	private MLotOffShelfFrom itemForm;

	public MLotOffShelfSection(ADTable adTable) {
		super(adTable);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemOffShelf(tBar);
		new ToolItem(tBar, 2);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemOffShelf(ToolBar tBar) {
		this.itemSave = new ToolItem(tBar, SWT.PUSH);
		this.itemSave.setText(Message.getString("mm.mlot_off"));
		this.itemSave.setImage(SWTResourceCache.getImage("mlot_offshelf"));
		this.itemSave.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				downAdapter();
			}
		});
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
         itemForm = new MLotOffShelfFrom(composite, SWT.NONE, tab, mmng);
         return itemForm;
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		if (adObject != null && adObject.getObjectRrn() != null) {
			itemForm.loadCurrent(adObject.getObjectRrn());
		}
	}

	private void downAdapter() {
		this.form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				MLot mLot = (MLot) getAdObject();
				if (mLot == null || mLot.getObjectRrn() == null) {
					return;
				}
					
				try {
					off(mLot);
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					setAdObject(mLot);
					this.refreshAdapter();
					
					// 方便继续操作
					txtLot.setFocus();
					txtLot.selectAll();
				} catch (Exception e) {
					e.printStackTrace();
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	}
	
	public MLot off(MLot mLot) throws Exception {
		MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
		MLotAction lotAction = new MLotAction();
		lotAction.setMainQty(mLot.getMainQty());
		return mmManager.mLotOffShelf(mLot, lotAction, null, null, null, Env.getSessionContext());
	}
	
	@Override
	public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);
		ADBase adObject = getAdObject();
		if (adObject != null && adObject.getObjectRrn() != null) {
			MLot mLot = (MLot) adObject;

			if (MLot.HOLDSTATE_ON.equals(mLot.getHoldState())) {
				itemSave.setEnabled(false);
			} else {
				if (newStatus != null ) {
					// 判断上下架是否改变物料批状态
					boolean isChange = false;
					try {
						SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
						isChange = MesCfMod.isMLotChangeStateByShelfAction(Env.getOrgRrn(), sysParameterManager);
					} catch (Exception e) {
						e.printStackTrace();
						ExceptionHandlerManager.asyncHandleException(e);
					}
					
					if (isChange && MaterialState.STATE_INSHELF.equals(mLot.getState())) {
						itemSave.setEnabled(true);
					} else if (!isChange && MaterialState.STATE_IN.equals(mLot.getState())) {
						itemSave.setEnabled(true);
					} else {
						itemSave.setEnabled(false);
					}
				} else {
					itemSave.setEnabled(false);
				}
			}
		}
	}
	
}
