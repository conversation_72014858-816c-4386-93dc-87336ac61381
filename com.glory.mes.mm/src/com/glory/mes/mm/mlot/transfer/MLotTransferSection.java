package com.glory.mes.mm.mlot.transfer;


import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.mlot.MLotSection;
import com.glory.mes.mm.state.model.MaterialState;

/**
 * 物料仓库变更<br/>
 * 1.当前只支持批次只存储在一个位置的情况<br/>
 * <AUTHOR>
 *
 */
public class MLotTransferSection extends MLotSection {
	protected MLotTransferFrom itemForm;
	
	public MLotTransferSection(ADTable adTable) {
		super(adTable);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemMove(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemMove(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString("mm.move_warehouse"));
		itemSave.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				moveAdapter();
			}
		});
	}
	
    @Override
	protected IForm getForm(Composite composite, ADTab tab) {
        itemForm = new MLotTransferFrom(composite, SWT.NONE, tab, mmng);
        return itemForm;
    }
	
	//转库
	protected void moveAdapter() {
		form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
                if (!detailForm.saveToObject()) {
                	saveFlag = false;
                }
            }
			if (saveFlag) {
				MLot mLot = (MLot) getAdObject();
				if (mLot == null) {
					return;
				}
				try {
					MMManager mmManager = Framework.getService(MMManager.class);
					
					List<MLotStorage> storages =  mmManager.getLotStorages(mLot.getObjectRrn());
					if (CollectionUtils.isEmpty(storages)) {
						throw new ClientException("mm.mlot_must_specify_lotstorage");
					}

					if (storages.size() > 1) {
						throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
					}
					MLotStorage storage = storages.get(0);
					
					mLot.setTransWarehouseRrn(storage.getWarehouseRrn());
					mLot.setTransStorageType(storage.getStorageType());
					mLot.setTransStorageId(storage.getStorageId());
					
					mLot.setTransTargetWarehouseRrn(itemForm.getWarehouseRrn());
					
					/*if (itemForm.getRackAreaRrn() != null) {
						mLot.setTransTargetStorageType(Storage.CATEGORY_RACKAREA);
						
						Storage rackAeraStorage = getStorage(itemForm.getRackAreaRrn());
						mLot.setTransTargetStorageId(rackAeraStorage.getName());
					} else if (itemForm.getRackRrn() != null) {
						mLot.setTransTargetStorageType(Storage.CATEGORY_RACK);
						
						Storage rackStorage = getStorage(itemForm.getRackRrn());
						mLot.setTransTargetStorageId(rackStorage.getName());
					} else {
						// 只是修改所在仓库
						mLot.setTransStorageType(null);
						mLot.setTransStorageId(null);
					}*/
					
					String storageKey = storage.getWarehouseRrn() + storage.getStorageType() + storage.getStorageId();
					String changeKey = mLot.getTransTargetWarehouseRrn() + mLot.getTransTargetStorageType() + mLot.getTransTargetStorageId();
					
					// 未做改变，提示后返回
					if (storageKey.equals(changeKey)) {
						UI.showInfo(Message.getString("mm.transfer_no_change"));
						return;
					}
					
					mLot = mmManager.transferMLot(mLot, mLot.getMainQty(), new MLotAction(),
							mLot.getTransWarehouseRrn(), null, mLot.getTransStorageType(), mLot.getTransStorageId(),
							mLot.getTransTargetWarehouseRrn(), null, mLot.getTransTargetStorageType(), mLot.getTransTargetStorageId(), Env.getSessionContext());
					UI.showInfo(Message.getString("wip.transfer_success"));
					setAdObject(mLot);
					refresh();
				} catch (Exception e) {
					e.printStackTrace();
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	}
	
	/*private Storage getStorage(Long storageRrn) throws Exception {
		MMManager mmManager = Framework.getService(MMManager.class);
		Storage storage = new Storage();
		storage.setObjectRrn(storageRrn);
		return mmManager.getStorage(storage);
	}*/
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		
		if (adObject != null && adObject.getObjectRrn() != null) {
			itemForm.loadCurrent(adObject.getObjectRrn());
		}
	}
	
	@Override
	public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);
		
		ADBase adObject = getAdObject();
		if (adObject != null && adObject.getObjectRrn() != null) {
			MLot mLot = (MLot) adObject;
			
			if (MLot.HOLDSTATE_ON.equals(mLot.getHoldState())) {
				itemSave.setEnabled(false);
			} else if (MaterialState.STATE_IN.equals(mLot.getState())) {
				itemSave.setEnabled(true);
			} else {
				itemSave.setEnabled(false);
			}
		}
	}
	
}
