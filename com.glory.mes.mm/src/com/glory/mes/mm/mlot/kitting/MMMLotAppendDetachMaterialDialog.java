package com.glory.mes.mm.mlot.kitting;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.EquipmentMaterialAppend;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.ras.eqp.PositionSet;
import com.glory.mes.wip.client.MLotManager;

public class MMMLotAppendDetachMaterialDialog extends MMMLotDetachMaterialDialog{

	public MMMLotAppendDetachMaterialDialog(String adFormName, String authority, IEventBroker eventBroker,
			List<EquipmentMaterial> equipmentMaterials, Boolean isDoubleClick, String equipmentId) {
		super(adFormName, authority, eventBroker, equipmentMaterials, isDoubleClick, equipmentId);
	}
	
	@Override
	public List<EquipmentMaterial> getUnKittingData() {
		// 区分Append与Empty/replace
		List<EquipmentMaterial> list = new ArrayList<EquipmentMaterial>();
		try {
			if (getEquipmentMaterials() != null && !getEquipmentMaterials().isEmpty()) {
				MLotManager mLotManager = Framework.getService(MLotManager.class);
				for (EquipmentMaterial equipmentMaterial : getEquipmentMaterials()) {
					List<EquipmentMaterial> materials = mLotManager.getEquipmentMaterials(Env.getOrgRrn(),
							equipmentMaterial.getEquipmentId(), equipmentMaterial.getPositionName());
					//检索选择的append类型的Kitting位置数据
					if (materials != null && !materials.isEmpty()) {
						list.addAll(materials);
					}
					//根据设备，位置号，检索出append中的数据
					List<EquipmentMaterialAppend> materialAppends = mLotManager.getEquipmentMaterialAppends(Env.getOrgRrn(),
							equipmentMaterial.getEquipmentId(), equipmentMaterial.getPositionName());
					if(materialAppends != null && !materialAppends.isEmpty()) {
						for(EquipmentMaterialAppend equipmentMaterialAppend : materialAppends) {
							EquipmentMaterial material = new EquipmentMaterial();
							PropertyUtil.copyProperties(material, equipmentMaterialAppend);
							list.add(material);
						}
					}
				}
				list.forEach(p -> p.setReplenishType(PositionSet.REPLENISH_TYPE_APPEND));
				setEquipmentMaterials(list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	
	@Override
	protected void okPressed() {
		try {
			// 选择的解绑的物料批次数据列表
			List<EquipmentMaterial> deatchEquipmentMaterials = new ArrayList<EquipmentMaterial>();
			List<Object> objects = tableManager.getCheckedObject();
			if(objects != null && !objects.isEmpty()) {
				for (Object object : objects) {
					deatchEquipmentMaterials.add((EquipmentMaterial) object);
				}

				//检查解绑仓库
	        	List<EquipmentMaterial> targetWarehouse = deatchEquipmentMaterials.stream().filter(x -> x.getTargetWarehouseRrn() == null).collect(Collectors.toList());
	        	if(targetWarehouse != null && !targetWarehouse.isEmpty()) {
	        		UI.showError(Message.getString("error.no_warehouse_input"));
	        	} else {
	        		MLotManager mlotManager = Framework.getService(MLotManager.class);
	    			Map<Long, List<EquipmentMaterial>> map = deatchEquipmentMaterials.stream()
	    					.collect(Collectors.groupingBy(EquipmentMaterial::getTargetWarehouseRrn));
	    			for (Long targetWarehouseRrn : map.keySet()) {
	    				// 当前设备物料批
	    				mLots = new ArrayList<MLot>();
	    				// Append的物料批
	    				appendMLots = new ArrayList<MLot>();
	    				List<EquipmentMaterial> equipmentMaterials = map.get(targetWarehouseRrn);
	    				buildMLots(equipmentMaterials);
	    				
	    				Map<String, List<MLot>> mlotsMap = mLots.stream().collect(Collectors.groupingBy(MLot::getEquipmentId));
	    				Map<String, List<MLot>> appendMlotsMap = appendMLots.stream().collect(Collectors.groupingBy(MLot::getEquipmentId));
	    				for(String equipmentId : appendMlotsMap.keySet()) {
	    					appendMlotsMap.put(equipmentId, appendMlotsMap.get(equipmentId));
	    				}
	    				mlotManager.unKittingEquipmentMaterials(mlotsMap, appendMlotsMap, targetWarehouseRrn, null, null, Env.getSessionContext());
	    			}
	    			isCancel = false;
	    			setReturnCode(OK);
	    			close();
	        	}
			} else {
				UI.showError(Message.getString("common.please_select"));
			}
		} catch (ClientParameterException e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} catch (ClientException e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	public void buildMLots(List<EquipmentMaterial> equipmentMaterials) {
		try {
			if (equipmentMaterials != null && !equipmentMaterials.isEmpty()) {
				MLotManager mLotManager = Framework.getService(MLotManager.class);
				MMManager mmManager = Framework.getService(MMManager.class);
				for (EquipmentMaterial equipmentMaterial : equipmentMaterials) {
					List<EquipmentMaterial> materials = mLotManager.getEquipmentMaterials(Env.getOrgRrn(),
							equipmentMaterial.getEquipmentId(), equipmentMaterial.getPositionName());
					//检索选择的append类型的Kitting位置数据
					if (materials != null && !materials.isEmpty()) {
						//记录append的物料批
						for(EquipmentMaterial matModel : materials) {
							if(matModel.getmLotId().equals(equipmentMaterial.getmLotId())) {
								MLot mlot = mmManager.getMLotByMLotId(Env.getOrgRrn(), matModel.getmLotId());
								mlot.setTransMainQty(equipmentMaterial.getAttachMainQty());
								mlot.setTransPosition(matModel.getPositionName());
								mlot.setEquipmentId(matModel.getEquipmentId());
								mLots.add(mlot);
							}
						}
					}
					//根据设备，位置号，检索出append中的数据
					List<EquipmentMaterialAppend> materialAppends = mLotManager.getEquipmentMaterialAppends(Env.getOrgRrn(),
							equipmentMaterial.getEquipmentId(), equipmentMaterial.getPositionName());
					if(materialAppends != null && !materialAppends.isEmpty()) {
						for(EquipmentMaterialAppend equipmentMaterialAppend : materialAppends) {
							//记录append的物料批
							if(equipmentMaterialAppend.getmLotId().equals(equipmentMaterial.getmLotId())) {
								MLot mlot = mmManager.getMLotByMLotId(Env.getOrgRrn(), equipmentMaterialAppend.getmLotId());
								mlot.setTransMainQty(equipmentMaterial.getAttachMainQty());
								mlot.setTransPosition(equipmentMaterial.getPositionName());
								mlot.setEquipmentId(equipmentMaterial.getEquipmentId());
								appendMLots.add(mlot);
							}
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
