package com.glory.mes.mm.mlot.kitting;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.EquipmentMaterialAppend;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.Material;
import com.glory.mes.ras.eqp.PositionSet;
import com.glory.mes.wip.client.MLotManager;

public class MMMLotAppendAttachDialog extends MMMLotEmptyAttachDialog { 

	private static final String FIELD_EQUIPMENTPOSITION = "equipmentPosition";

	protected ListTableManagerField equipmentPositionField;
	protected ListTableManager kittingTableManager;
	
	public List<EquipmentMaterialAppend> appendEquipmentMaterials = new ArrayList<EquipmentMaterialAppend>();

	public MMMLotAppendAttachDialog(String adFormName, String authority, IEventBroker eventBroker, List<EquipmentMaterial> equipmentMaterials, Boolean isDoubleClick, String equipmentId) {
		super(adFormName, authority, eventBroker, equipmentMaterials, isDoubleClick, equipmentId);
        this.equipmentMaterials = equipmentMaterials;
        this.equipmentId = equipmentId;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		mlotTextField = form.getFieldByControlId(FIELD_MLOTTEXT, CustomField.class);
		kittingMaterialField = form.getFieldByControlId(FIELD_KITTINGMATERIAL, TableEditorField.class);
		sourceStorageField = form.getFieldByControlId(FIELD_SOURCESTORAGE, ListTableManagerField.class);
		equipmentPositionField = form.getFieldByControlId(FIELD_EQUIPMENTPOSITION, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(EVENT_ENTERPRESSED), this::searchAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
		
		tableManager = kittingMaterialField.getTableManager();
		storageTableManager = (CheckBoxTableViewerManager) sourceStorageField.getListTableManager().getTableManager();
		kittingTableManager = equipmentPositionField.getListTableManager();
		EnterPressComposite enterPressComposite = (EnterPressComposite) mlotTextField.getCustomComposite();
		txtMLotId = enterPressComposite.getTxtLot();
				
		storageTableManager.addICheckChangedListener(checkChangedListener);
		init();
	}
	
	@Override
	protected void searchAdaptor(Object object) {
		Event event = (Event) object;
		MLot mlot = (MLot) event.getProperty(GlcEvent.PROPERTY_DATA);
		mlot = MMMLotKittingEquipmentFormEditor.searchMLot(mlot.getmLotId());		
		txtMLotId.selectAll();
		if (mlot == null) {
			txtMLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
		} else {
			// 若存在批次，则按照顺序从上往下填入
			try {
				if (appendEquipmentMaterials.size() == 1 && CollectionUtils.isEmpty(kittingTableManager.getInput())) {
					UI.showInfo(Message.getString("mm.append_kitting_cant_muilt_mlot_operate"));
					return;
				}
				MLot mLot = mlot;
				List<EquipmentMaterialAppend> checkLots = appendEquipmentMaterials.stream().filter(x -> mLot.getmLotId().equals(x.getmLotId())).collect(Collectors.toList());
				if(checkLots == null || checkLots.isEmpty()) {
					//物料批检查
					if(MMMLotKittingEquipmentFormEditor.checkMLot(mLot)) {
						EquipmentMaterialAppend equipmentMaterialAppend = new EquipmentMaterialAppend(); 
						equipmentMaterialAppend.setPositionName(equipmentMaterials.get(0).getPositionName());
						equipmentMaterialAppend.setReplenishType(equipmentMaterials.get(0).getReplenishType());
						equipmentMaterialAppend.setEquipmentId(equipmentMaterials.get(0).getEquipmentId());
						equipmentMaterialAppend.setmLotId(mLot.getmLotId());
						equipmentMaterialAppend.setMaterialName(mLot.getMaterialName());
						equipmentMaterialAppend.setMaterialDesc(mLot.getMaterialDesc());
						equipmentMaterialAppend.setMaterialType(mLot.getMaterialType());
						if (!Material.BATCH_TYPE_BATCH.equals(mLot.getBatchType())) {
							equipmentMaterialAppend.setAttachMainQty(mLot.getMainQty());;
						} else {
							tableManager.addEditorObjects(equipmentMaterialAppend);
						}
						appendEquipmentMaterials.add(equipmentMaterialAppend);
						tableManager.setInput(appendEquipmentMaterials);
					}
					try {
						MMManager mmManager = Framework.getService(MMManager.class);
						List<MLot> storageMLots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), Arrays.asList(mLot));
						List<MLot> inputMLots = storageTableManager.getInput().stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());;
						inputMLots.addAll(storageMLots);
						for (Iterator iterator = inputMLots.iterator(); iterator.hasNext();) {
							MLot iteratorMLot = (MLot) iterator.next();
							if ("Equipment".equals(iteratorMLot.getTransStorageType())) {
								iterator.remove();
							}
						}
						storageTableManager.setInput(inputMLots);
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
					}
				} else {
					//物料批已在列表中
					UI.showError(Message.getString("bas.line_lineuser_also_exsit_in_list"));
					return;
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		tableManager.refresh();
	
	}
	
	@Override
	protected void init() {
		try {
			tableManager.refresh();
			tableManager.setInput(appendEquipmentMaterials);
			
			MLotManager mLotManager = Framework.getService(MLotManager.class);
			List<EquipmentMaterial> equipmentMaterialAppends = new ArrayList<EquipmentMaterial>();
			//获取EquipmentMaterial表中append数据
			List<EquipmentMaterial> equipmentMaterials = mLotManager.getEquipmentMaterials(Env.getOrgRrn(), this.equipmentMaterials.get(0).getEquipmentId(), this.equipmentMaterials.get(0).getPositionName());
			if(equipmentMaterials != null && !equipmentMaterials.isEmpty()) {
				EquipmentMaterial equipmentMaterial = equipmentMaterials.get(0);
				equipmentMaterial.setReplenishType(PositionSet.REPLENISH_TYPE_APPEND);
				equipmentMaterialAppends.add(equipmentMaterial);
			}
			//获取EquipmentMaterialAppend补充批次列表
			List<EquipmentMaterialAppend> appends = mLotManager.getEquipmentMaterialAppends(Env.getOrgRrn(), this.equipmentMaterials.get(0).getEquipmentId(), this.equipmentMaterials.get(0).getPositionName());
			for(EquipmentMaterialAppend equipmentMaterialAppend : appends) {
				EquipmentMaterial equipmentMaterial = new EquipmentMaterial();
				PropertyUtil.copyProperties(equipmentMaterial, equipmentMaterialAppend);
				equipmentMaterial.setReplenishType(PositionSet.REPLENISH_TYPE_APPEND);
				equipmentMaterialAppends.add(equipmentMaterial);
			}
			kittingTableManager.setInput(equipmentMaterialAppends);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected void clearAdapter(Object object) {
		try {
			//移除扫描的MLOT
			List<Object> objects = tableManager.getCheckedObject();
			if(objects != null && !objects.isEmpty()) {
				List<EquipmentMaterialAppend> removeAppends = new ArrayList<EquipmentMaterialAppend>();
				for(Object object1 : objects) {
					removeAppends.add((EquipmentMaterialAppend) object1);
					//退回扫描的库存信息
					List<MLot> mLots = storageTableManager.getInput().stream().map(o -> ((MLot)o)).collect(Collectors.toList());
					MMManager mmManager = Framework.getService(MMManager.class);
					MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), ((EquipmentMaterialAppend) object1).getmLotId());
					for (Iterator iterator = mLots.iterator(); iterator.hasNext();) {
						MLot iteratorMLot = (MLot) iterator.next();
						if (iteratorMLot.getmLotId().equals(mLot.getmLotId())) {
							iterator.remove();
						}
					}
					storageTableManager.setInput(mLots);
				}
				appendEquipmentMaterials.removeAll(removeAppends);
				tableManager.getInput().removeAll(removeAppends);
				tableManager.refresh();
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
    @Override
    public List<EquipmentMaterial> kittingData() {
    	List<EquipmentMaterial> equipmentMaterials = new ArrayList<EquipmentMaterial>();
    	List<EquipmentMaterialAppend>  equipmentMaterialAppends = appendEquipmentMaterials;
    	for(EquipmentMaterialAppend equipmentMaterialAppend : equipmentMaterialAppends) {
    		EquipmentMaterial equipmentMaterial = new EquipmentMaterial();
    		PropertyUtil.copyProperties(equipmentMaterial, equipmentMaterialAppend);
    		equipmentMaterials.add(equipmentMaterial);
    	}
    	return equipmentMaterials;
    }
    
	ICheckChangedListener checkChangedListener = new ICheckChangedListener() {
		@Override
		public void checkChanged(List<Object> eventObjects, boolean checked) {
			if (CollectionUtils.isEmpty(eventObjects)) {
				return;
			}
			List<MLot> mlots = storageTableManager.getCheckedObject().stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			for (Object object : eventObjects) {
				if (checked) {
					MLot mLot = (MLot) object;
					List<MLot> f = mlots.stream().filter(m -> mLot.getmLotId().equals(m.getmLotId())).collect(Collectors.toList());
					if (f.size() > 1) {
						storageTableManager.unCheckObject(object);
					} else {
						List<EquipmentMaterialAppend> equipmentMaterialAppends = (List<EquipmentMaterialAppend>) tableManager.getInput();
						for (EquipmentMaterialAppend equipmentMaterialAppend : equipmentMaterialAppends) {
							if (equipmentMaterialAppend.getmLotId().equals(mLot.getmLotId())) {
								equipmentMaterialAppend.setAttachMainQty(mLot.getMainQty());
								tableManager.update(equipmentMaterialAppend);
							}
						}
					}
				}
			}
		
		}
	};
	
    
    public List<EquipmentMaterialAppend> getAppendEquipmentMaterials() {
		return appendEquipmentMaterials;
	}

	public void setAppendEquipmentMaterials(List<EquipmentMaterialAppend> appendEquipmentMaterials) {
		this.appendEquipmentMaterials = appendEquipmentMaterials;
	}

}