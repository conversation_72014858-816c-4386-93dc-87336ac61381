package com.glory.mes.mm.mlot.kitting;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.nattable.editor.row.ListRowEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.mm.bom.model.BomContext;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class MMToolAttachEquipmentDialog extends GlcBaseDialog { 

	private static final String FIELD_TOOLID = "toolId";
	private static final String FIELD_TOOLATTACH = "toolAttach";

	private static final String BUTTON_CLEAR = "clear";

	protected CustomField toolIdField;
	protected TableEditorField toolAttachField;
	
	public ListRowEditorTableManager tableManager;
	public HeaderText txtToolId;
	
	protected List<EquipmentMaterial> equipmentMaterials;
	
    private static int MIN_DIALOG_WIDTH = 500;
    private static int MIN_DIALOG_HEIGHT = 500;
    
	public boolean isCancel = true;
	public boolean ischeckByRuningLot = true;
	public int addcount = 0;

	public MMToolAttachEquipmentDialog(String adFormName, String authority, IEventBroker eventBroker, List<EquipmentMaterial> equipmentMaterials, Boolean isDoubleClick, String equipmentId) {
		super(adFormName, authority, eventBroker);
		this.equipmentMaterials = equipmentMaterials;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		toolIdField = form.getFieldByControlId(FIELD_TOOLID, CustomField.class);
		toolAttachField = form.getFieldByControlId(FIELD_TOOLATTACH, TableEditorField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_ENTERPRESSED), this::searchAdaptor);
		
		tableManager = toolAttachField.getTableManager();
		EnterPressComposite enterPressComposite = (EnterPressComposite) toolIdField.getCustomComposite();
		txtToolId = enterPressComposite.getTxtLot();
		
		tableManager.setInput(equipmentMaterials);
	}
	
	private void searchAdaptor(Object object) {
		Event event = (Event) object;
		Tool toolObject = (Tool) event.getProperty(GlcEvent.PROPERTY_DATA);
		toolObject = MMMLotKittingEquipmentFormEditor.searchTool(toolObject.getmLotId());		
		txtToolId.selectAll();
		if (toolObject == null) {
			txtToolId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
		} else {
			// 若存在治具，则按照顺序从上往下填入
			try {
				Tool tool = toolObject;
				if (equipmentMaterials.size() == addcount) {
					UI.showError(Message.getString("bas.line_lineuser_also_exsit_in_list"));
					return;
				}
				List<EquipmentMaterial> checkLots = equipmentMaterials.stream()
						.filter(x -> tool.getmLotId().equals(x.getmLotId()))
						.collect(Collectors.toList());
				if (checkLots == null || checkLots.isEmpty()) {
					// 物料批检查
					if (MMMLotKittingEquipmentFormEditor.checkMLot(tool)) {
						EquipmentMaterial setEquipmentMaterial = (EquipmentMaterial) tableManager.getInput().get(addcount);
						setEquipmentMaterial.setmLotId(tool.getmLotId());
						setEquipmentMaterial.setTransMainQty(tool.getTransMainQty());
						setEquipmentMaterial.setMaterialName(tool.getMaterialName());
						setEquipmentMaterial.setMaterialDesc(tool.getMaterialDesc());
						setEquipmentMaterial.setMaterialType(tool.getMaterialType());
						setEquipmentMaterial.setAttachMainQty(tool.getMainQty());
						// 扫描成功一次，记录一次
						addcount++;
						tableManager.update(setEquipmentMaterial);
					}
				} else {
					UI.showError(Message.getString("bas.line_lineuser_also_exsit_in_list"));
					return;
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		txtToolId.selectAll();
		tableManager.refresh();
		return;
	}
	
	private void clearAdapter(Object object) {
		try {
			// 退回扫描的Tool
			EquipmentMaterial setEquipmentMaterial = (EquipmentMaterial) tableManager.getInput().get(addcount - 1 < 0 ? 0 : addcount - 1);
			if (setEquipmentMaterial != null) {
				setEquipmentMaterial.setmLotId(null);
				setEquipmentMaterial.setTransMainQty(null);
				setEquipmentMaterial.setMaterialName(null);
				setEquipmentMaterial.setMaterialDesc(null);
				setEquipmentMaterial.setMaterialType(null);
				setEquipmentMaterial.setAttachMainQty(null);
				// 点击成功一次，退回一次
				addcount--;
			}
			tableManager.update(setEquipmentMaterial);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
    @Override
    protected void okPressed() {
    	equipmentMaterials = kittingData();
		if (equipmentMaterials != null && !equipmentMaterials.isEmpty()) {
			try {
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				List<Tool> tools = new ArrayList<Tool>();
				for (EquipmentMaterial equipmentMaterial : equipmentMaterials) {
					if (equipmentMaterial.getmLotId() != null && !"".equals(equipmentMaterial.getmLotId())) {
						Tool tool = MMMLotKittingEquipmentFormEditor.searchTool(equipmentMaterial.getmLotId());
						if (tool == null) {
							UI.showError(Message.getString("ras.tool_is_not_find"));
							return;
						}
						tool.setTransPosition(equipmentMaterial.getPositionName());
						tool.setActionComment(equipmentMaterial.getActionComment());
						tool.setEquipmentId(equipmentMaterial.getEquipmentId());
						tools.add(tool);
					}
				}
				
				Map<String, List<Tool>> map = tools.stream().collect(Collectors.groupingBy(Tool::getEquipmentId));
				
				Map<Equipment, List<Tool>> toolsMap = new HashMap<Equipment, List<Tool>>();
				Map<Equipment, List<Map<String, String>>> eqpConditionMap = new HashMap<Equipment, List<Map<String, String>>>();
				
				LotManager lotManager = Framework.getService(LotManager.class);
				for(String equipmentId : map.keySet()) {
					Equipment equipment = MMMLotKittingEquipmentFormEditor.searchEquipment(equipmentId);
					//设备当前生产的产品列表,用于检查tool是否可以绑定
					List<Lot> lots = new ArrayList<Lot>();
					if(ischeckByRuningLot) {
						//取当前设备RUN的批次
						lots = lotManager.getRunningLotsByEqp(Env.getOrgRrn(), equipmentId);
					} else {
						//取当前设备的所有批次
						lots = lotManager.getLotsByEqp(Env.getOrgRrn(), equipment.getObjectRrn(), Env.getSessionContext());
					}
					
					List<Map<String, String>> conditionMaps = new ArrayList<Map<String, String>>();
					if (lots != null && lots.size() > 0) {	
						for (Lot lot : lots) {
							Map<String, String> conditionMap = new HashMap<String, String>();
							conditionMap.put(BomContext.CONTEXT_FIELD_CUSTOMER_CODE, lot.getCustomerCode());										
							conditionMap.put(BomContext.CONTEXT_FIELD_PART_NAME, lot.getPartName());											
							conditionMap.put(BomContext.CONTEXT_FIELD_STEP_NAME, lot.getStepName());
							conditionMaps.add(conditionMap);
						}
					}			
					//设备对应的批次一些信息放在MAP传过去，用于通过Context取得Tool Bom校验Tool
					eqpConditionMap.put(equipment, conditionMaps);	
					toolsMap.put(equipment, map.get(equipmentId));
				}
				//批量绑定治具
				consumableManager.attachEquipmentByTools(toolsMap, eqpConditionMap, Env.getSessionContext());
				isCancel = false;
			} catch (ClientParameterException e) {
				ExceptionHandlerManager.asyncHandleException(e);
			} catch (ClientException e) {
				ExceptionHandlerManager.asyncHandleException(e);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		setReturnCode(OK);
		close();
    }
    
	public List<EquipmentMaterial> kittingData() {
		return equipmentMaterials;
	}
	
	@Override
	protected void cancelPressed() {
		isCancel = true;
		super.cancelPressed();
	}
	
	public boolean isCancel() {
		return isCancel;
	}
	
	public void setCancel(boolean isCancel) {
		this.isCancel = isCancel;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

}