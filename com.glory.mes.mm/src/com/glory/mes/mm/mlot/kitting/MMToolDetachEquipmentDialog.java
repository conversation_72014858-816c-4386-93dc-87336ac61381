package com.glory.mes.mm.mlot.kitting;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.nattable.editor.row.ListRowEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;

public class MMToolDetachEquipmentDialog extends GlcBaseDialog { 

	private static final String FIELD_TOOLATTACH = "toolAttach";

	protected TableEditorField toolAttachField;
	protected ListRowEditorTableManager tableManager;
	
    protected String equipmentId;
    protected List<EquipmentMaterial> equipmentMaterials;
    
    protected List<Tool> tools;
    
    public boolean isCancel = true;
    
    private static int MIN_DIALOG_WIDTH = 500;
    private static int MIN_DIALOG_HEIGHT = 500;
    

	public MMToolDetachEquipmentDialog(String adFormName, String authority, IEventBroker eventBroker, List<EquipmentMaterial> equipmentMaterials, Boolean isDoubleClick, String equipmentId) {
		super(adFormName, authority, eventBroker);
		this.equipmentMaterials = equipmentMaterials;
		this.equipmentId = equipmentId;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		toolAttachField = form.getFieldByControlId(FIELD_TOOLATTACH, TableEditorField.class);
		tableManager = toolAttachField.getTableManager();
		
		List<EquipmentMaterial> unkittingMaterials = getUnKittingData();
		
		tableManager.setInput(unkittingMaterials);
	}
	
	protected void okPressed() {
        try {
        	//选择的解绑的物料批次数据列表
        	List<EquipmentMaterial> deatchEquipmentMaterials = new ArrayList<EquipmentMaterial>();
        	List<Object> objects = tableManager.getCheckedObject();
        	if(objects != null && !objects.isEmpty()) {
        		for(Object object : objects) {
            		deatchEquipmentMaterials.add((EquipmentMaterial)object);
            	}
            	ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
                buildTools(equipmentMaterials);
                Map<Equipment, List<Tool>> toolsMap = new HashMap<Equipment, List<Tool>>();
                
                Map<String, List<Tool>> map = tools.stream().collect(Collectors.groupingBy(Tool::getEquipmentId));
            	for(String equipmentId : map.keySet()) {
            		Equipment equipment = MMMLotKittingEquipmentFormEditor.searchEquipment(equipmentId);
            		toolsMap.put(equipment, map.get(equipmentId));
            	}
                consumableManager.detachEquipmentByConsumables(toolsMap, Env.getSessionContext());
                isCancel = false;
                setReturnCode(OK);
        		close();
        	} else {
        		UI.showError(Message.getString("common.please_select"));
        	}
        } catch (ClientParameterException e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} catch (ClientException e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public void buildTools(List<EquipmentMaterial> equipmentMaterials) {
    	tools = new ArrayList<Tool>();
		try {
			for(EquipmentMaterial equipmentMaterial : equipmentMaterials) {
				Tool tool = MMMLotKittingEquipmentFormEditor.searchTool(equipmentMaterial.getmLotId());
				tool.setTransPosition(equipmentMaterial.getPositionName());
				tool.setActionComment(equipmentMaterial.getActionComment());
				tool.setTransMainQty(null);//治具解绑不消耗数量
				tool.setEquipmentId(equipmentMaterial.getEquipmentId());
            	tools.add(tool);
        	}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public List<EquipmentMaterial> getUnKittingData() {
		List<EquipmentMaterial> list = new ArrayList<EquipmentMaterial>();
		try {
			if (equipmentMaterials != null && !equipmentMaterials.isEmpty()) {
				RASManager rasManager = Framework.getService(RASManager.class);
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				for (EquipmentMaterial equipmentMaterial : equipmentMaterials) {
					Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentMaterial.getEquipmentId());
					List<Tool> tools = consumableManager.getToolByEquipment(equipment.getObjectRrn(), equipmentMaterial.getPositionName());
					if (tools != null && !tools.isEmpty()) {
						for(Tool tool : tools) {
							EquipmentMaterial equipmentTool = new EquipmentMaterial();
							PropertyUtil.copyProperties(equipmentTool, tool);
							equipmentTool.setPositionName(equipmentMaterial.getPositionName());
							equipmentTool.setReplenishType(equipmentMaterial.getReplenishType());
							equipmentTool.setEquipmentId(equipmentMaterial.getEquipmentId());
							list.add(equipmentTool);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	
	@Override
	protected void cancelPressed() {
		isCancel = true;
		super.cancelPressed();
	}
	
	public boolean isCancel() {
		return isCancel;
	}
	
	public void setCancel(boolean isCancel) {
		this.isCancel = isCancel;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}