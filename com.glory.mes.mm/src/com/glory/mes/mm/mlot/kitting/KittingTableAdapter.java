package com.glory.mes.mm.mlot.kitting;

import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.swt.graphics.Color;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.EquipmentMaterialAppend;
import com.glory.mes.ras.eqp.PositionSet;
import com.glory.mes.wip.client.MLotManager;

@SuppressWarnings("rawtypes")
public class KittingTableAdapter extends ListItemAdapter {
	
	@Override
	public Color getBackground(Object element, String id) {
		try {
			/**
			 * 设备当前物料批特殊标记
			 */
			EquipmentMaterial equipmentMaterial = (EquipmentMaterial) element;
			MLotManager mLotManager = Framework.getService(MLotManager.class); 
			List<EquipmentMaterialAppend> equipmentMaterials = mLotManager.getEquipmentMaterialAppends(Env.getOrgRrn(), equipmentMaterial.getEquipmentId(), equipmentMaterial.getPositionName());
			equipmentMaterials = equipmentMaterials.stream().filter(x -> x.getmLotId().equals(equipmentMaterial.getmLotId())).collect(Collectors.toList());
			//只处理Append类型的数据
			if((equipmentMaterials == null || equipmentMaterials.isEmpty()) && PositionSet.REPLENISH_TYPE_APPEND.equals(equipmentMaterial.getReplenishType())) {
				return SWTResourceCache.getColor(SWTResourceCache.COLOR_GREEN);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return super.getBackground(element, id);
	}
	
}
