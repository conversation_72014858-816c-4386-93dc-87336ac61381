package com.glory.mes.mm.mlot.kitting;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.ConsumableEqp;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.Material;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.PositionSet;
import com.glory.mes.ras.eqp.PositionSetLine;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class MMMLotKittingEquipmentFormEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.mlot.kitting.MMMLotKittingEquipmentFormEditor";
	
	public static String ACTIONTYPE_ATTACH_MATERIAL = "AttachMaterial";// 绑定物料批动作
	public static String ACTIONTYPE_DETACH_MATERIAL = "DetachMaterial"; // 解绑物料批动作
	public static String ACTIONTYPE_ATTACH_TOOL = "AttachTool";// 绑定治具动作
	public static String ACTIONTYPE_DETACH_TOOL = "DetachTool"; // 解绑治具动作
	
	public static final String EVENT_ENTERPRESSED = "EnterPressed";

	public static final String FIELD_EQPTEXT = "eqpText";
	public static final String FIELD_EQPMATERIAL = "eqpMaterial";
	public static final String FIELD_EQPINFO = "eqpInfo";

	public static final String BUTTON_REFRESH = "refresh";
	public static final String BUTTON_BOM = "bom";
	public static final String BUTTON_ATTACHMLOT = "attachMLot";
	public static final String BUTTON_DETACHMLOT = "detachMLot";
	public static final String BUTTON_ATTACHTOOL = "attachTool";
	public static final String BUTTON_DETACHTOOL = "detachTool";

	protected CustomField eqpTextField;
	protected GlcFormField eqpMaterialField;
	protected ListTableManagerField eqpInfoField;
	protected ListTableManagerField eqpMaterialField1;
	
	protected EnterPressComposite enterPressComposite;
	
	public List<EquipmentMaterial> equipmentMaterials;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		eqpTextField = form.getFieldByControlId(FIELD_EQPTEXT, CustomField.class);
		eqpMaterialField = form.getFieldByControlId(FIELD_EQPMATERIAL, GlcFormField.class);
		eqpInfoField = eqpMaterialField.getFieldByControlId(FIELD_EQPINFO, ListTableManagerField.class);
		eqpMaterialField1 = eqpMaterialField.getFieldByControlId(FIELD_EQPMATERIAL, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(EVENT_ENTERPRESSED), this::searchAdaptor);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, eqpMaterialField.getFullTopic(BUTTON_BOM), this::bomAdapter);
		subscribeAndExecute(eventBroker, eqpMaterialField.getFullTopic(BUTTON_ATTACHMLOT), this::attachMLotAdapter);
		subscribeAndExecute(eventBroker, eqpMaterialField.getFullTopic(BUTTON_DETACHMLOT), this::detachMLotAdapter);
		subscribeAndExecute(eventBroker, eqpMaterialField.getFullTopic(BUTTON_ATTACHTOOL), this::attachToolAdapter);
		subscribeAndExecute(eventBroker, eqpMaterialField.getFullTopic(BUTTON_DETACHTOOL), this::detachToolAdapter);
		
		enterPressComposite = (EnterPressComposite) eqpTextField.getCustomComposite();
	}

	private void refreshAdapter(Object object) {
		eqpMaterialField.refresh();	
	}
	
	public void searchAdaptor (Object object) {
		Event event = (Event) object;
		ADBase adBase = (ADBase) event.getProperty(GlcEvent.PROPERTY_DATA);
		Equipment equipment = (Equipment) adBase;
		enterPressComposite.getTxtLot().selectAll();
		if (equipment == null) {
			enterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			eqpInfoField.getListTableManager().setInput(null);
			eqpInfoField.getListTableManager().refresh();
			eqpMaterialField1.getListTableManager().setInput(null);
			eqpMaterialField1.getListTableManager().refresh();
			enterPressComposite.getTxtLot().warning();
		} else {
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				// eqpLot
				List<Lot> eqpLotList = lotManager.getLotsByEqp(Env.getOrgRrn(), equipment.getObjectRrn(), Env.getSessionContext());
				eqpInfoField.getListTableManager().setInput(eqpLotList);
				
				// MMEquipmentMaterialKitting
				if (equipment.getPositionSetRrn() != null) {
					List<EquipmentMaterial> equipmentMaterials = refreshPosition(equipment);
					eqpMaterialField1.getListTableManager().setInput(equipmentMaterials);
					eqpMaterialField1.getListTableManager().refresh();
				} else {
					eqpMaterialField1.getListTableManager().setInput(new ArrayList<EquipmentMaterial>());
					eqpMaterialField1.getListTableManager().refresh();
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
			
			enterPressComposite.getTxtLot().focusing();
		}
		refreshAdapter(object);
	}

	private void bomAdapter(Object object) {
		try {
			Object obj = eqpInfoField.getListTableManager().getSelectedObject();
			if (obj == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			Lot lot = (Lot) obj;
			
			MMMLotKittingStepBomDialog baseDialog = new MMMLotKittingStepBomDialog("MMMLotKittingBom", null, eventBroker, lot);
			baseDialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void attachMLotAdapter(Object object) {
		try {
			checkEquipmentMaterial(ACTIONTYPE_ATTACH_MATERIAL);
			if(equipmentMaterials == null || equipmentMaterials.isEmpty()) {
				return;
			}
			String selectPositionType = checkEquipmentMaterialType();
			
			Equipment eqp = (Equipment) enterPressComposite.getValue();
			MMMLotEmptyAttachDialog attachDialog = null;
			if (PositionSet.REPLENISH_TYPE_EMPTY.equals(selectPositionType) || 
					PositionSet.REPLENISH_TYPE_REPLACE.equals(selectPositionType)) {
				attachDialog = new MMMLotEmptyAttachDialog("MMMLotEmptyAttachDialog", null, eventBroker, equipmentMaterials, true, eqp.getEquipmentId());
			} else if (PositionSet.REPLENISH_TYPE_APPEND.equals(selectPositionType)) {
				List<String> entitylist = equipmentMaterials.stream().map(EquipmentMaterial::getReplenishType)
						.collect(Collectors.toList()).stream().filter(x -> x.equals(PositionSet.REPLENISH_TYPE_APPEND)).collect(Collectors.toList());
				if (entitylist.size() > 1) {
					UI.showInfo(Message.getString("mm.kitting_append_edit_on_one"));
					return;
				}
				attachDialog = new MMMLotAppendAttachDialog("MMMLotAppendAttachDialog", null, eventBroker, equipmentMaterials, true, eqp.getEquipmentId());
			}
			if(attachDialog != null) {
				attachDialog.open();
				if (!attachDialog.isCancel()) {
					UI.showInfo(Message.getString("mm.attach_success"));
				}
			}
			eqpMaterialField1.getListTableManager().setInput(refreshPosition(eqp));
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void detachMLotAdapter(Object object) {
		try {
			checkEquipmentMaterial(ACTIONTYPE_DETACH_MATERIAL);
			if(equipmentMaterials == null || equipmentMaterials.isEmpty()) {
				return;
			}
			String selectPositionType = checkEquipmentMaterialType();
			
			MMMLotDetachMaterialDialog detachDialog = null;
			Equipment eqp = (Equipment) enterPressComposite.getValue();
			if (PositionSet.REPLENISH_TYPE_EMPTY.equals(selectPositionType) ||
					PositionSet.REPLENISH_TYPE_REPLACE.equals(selectPositionType)) {
				detachDialog = new MMMLotDetachMaterialDialog("MMMLotDetachMaterialDialog", null, eventBroker, equipmentMaterials, true, eqp.getEquipmentId());
			} else if (PositionSet.REPLENISH_TYPE_APPEND.equals(selectPositionType)) {
				List<String> entitylist = equipmentMaterials.stream().map(EquipmentMaterial::getReplenishType)
						.collect(Collectors.toList()).stream().filter(x -> x.equals(PositionSet.REPLENISH_TYPE_APPEND)).collect(Collectors.toList());
				if (entitylist.size() > 1) {
					UI.showInfo(Message.getString("mm.kitting_append_edit_on_one"));
					return;
				}
				detachDialog = new MMMLotAppendDetachMaterialDialog("MMMLotDetachMaterialDialog", null, eventBroker, equipmentMaterials, true, eqp.getEquipmentId());
			}
			if(detachDialog != null) {
				detachDialog.open();
				if (!detachDialog.isCancel()) {
					UI.showInfo(Message.getString("mm.detach_success"));
				}
			}
			eqpMaterialField1.getListTableManager().setInput(refreshPosition(eqp));
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void attachToolAdapter(Object object) {
		try {
			checkEquipmentMaterial(ACTIONTYPE_ATTACH_TOOL);
			if(equipmentMaterials == null || equipmentMaterials.isEmpty()) {
				return;
			}
			String selectPositionType = checkEquipmentMaterialType();
			Equipment eqp = (Equipment) enterPressComposite.getValue();
			MMToolAttachEquipmentDialog attachDialog = null;
			if (PositionSet.REPLENISH_TYPE_EMPTY.equals(selectPositionType) || 
					PositionSet.REPLENISH_TYPE_REPLACE.equals(selectPositionType)) {
				attachDialog = new MMToolAttachEquipmentDialog("MMToolAttachEquipmentDialog", null, eventBroker, equipmentMaterials, true, eqp.getEquipmentId());
			} else if (PositionSet.REPLENISH_TYPE_APPEND.equals(selectPositionType)) {
				UI.showInfo(Message.getString("mm.kitting_tool_nonsupport_append"));
				return;
			}
			if(attachDialog != null) {
				attachDialog.open();
				if (!attachDialog.isCancel()) {
					UI.showInfo(Message.getString("mm.attach_success"));
				}
			}
			eqpMaterialField1.getListTableManager().setInput(refreshPosition(eqp));
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void detachToolAdapter(Object object) {
		try {
			checkEquipmentMaterial(ACTIONTYPE_DETACH_TOOL);
			if(equipmentMaterials == null || equipmentMaterials.isEmpty()) {
				return;
			}
			String selectPositionType = checkEquipmentMaterialType();
			
			MMToolDetachEquipmentDialog detachDialog = null;
			Equipment eqp = (Equipment) enterPressComposite.getValue();
			if (PositionSet.REPLENISH_TYPE_EMPTY.equals(selectPositionType) || 
					PositionSet.REPLENISH_TYPE_REPLACE.equals(selectPositionType)) {
				detachDialog = new MMToolDetachEquipmentDialog("MMToolDetachEquipmentDialog", null, eventBroker, equipmentMaterials, false, eqp.getEquipmentId());
			} else if (PositionSet.REPLENISH_TYPE_APPEND.equals(selectPositionType)) {
				UI.showInfo(Message.getString("mm.kitting_tool_nonsupport_append"));
				return;
			}
			if(detachDialog != null) {
				detachDialog.open();
				if (!detachDialog.isCancel()) {
					UI.showInfo(Message.getString("mm.detach_success"));
				}
			}
			eqpMaterialField1.getListTableManager().setInput(refreshPosition(eqp));
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * 绑定后刷新界面数据,包含父子能力设备下的设备位置
	 * @param equipment
	 * @return
	 */
	public List<EquipmentMaterial> refreshPosition(Equipment equipment1) {
		List<EquipmentMaterial> equipmentMaterials = new ArrayList<EquipmentMaterial>();
		try {
			List<Equipment> allEquipment = new ArrayList<Equipment>();
			allEquipment.add(equipment1);
			//获取父子设备能力下的子设备的positionSet
			RASManager rasManager = Framework.getService(RASManager.class);
			List<Equipment> subEquipments = rasManager.getSubEquipmentCombineByParent(equipment1.getObjectRrn());
			allEquipment.addAll(subEquipments);
			
			//子设备位置集
			if(allEquipment != null && !allEquipment.isEmpty()) {
				ADManager adManager = Framework.getService(ADManager.class);
				for(Equipment eqp: allEquipment) {
					PositionSet positionSet = new PositionSet();
					positionSet.setObjectRrn(eqp.getPositionSetRrn());
					positionSet = (PositionSet) adManager.getEntity(positionSet);
					List<PositionSetLine> positionSetLines = adManager.getEntityList(Env.getOrgRrn(),
							PositionSetLine.class, Env.getMaxResult(), "positionSetRrn = " + eqp.getPositionSetRrn(), "positionName");
					if (positionSetLines != null && positionSetLines.size() > 0) {
						for (PositionSetLine positionSetLine : positionSetLines) {
							EquipmentMaterial equipmentMaterial = new EquipmentMaterial();
							equipmentMaterial.setEquipmentId(eqp.getEquipmentId());
							equipmentMaterial.setPositionName(positionSetLine.getPositionName());
							equipmentMaterial.setReplenishType(positionSetLine.getReplenishType());
							equipmentMaterial.setIsActive(true);
							// 若已绑定，则展示绑定的物料批次信息
							if(PositionSet.MATERIAL_CATEGORY_MATERIAL.equals(positionSetLine.getMaterialCategory())) {
								List<EquipmentMaterial> attachEquipmentMaterials = adManager.getEntityList(
										Env.getOrgRrn(), EquipmentMaterial.class, Env.getMaxResult(),
										"equipmentId = '" + eqp.getEquipmentId() + "' and positionName = '" + positionSetLine.getPositionName() + "'",null);
								if (attachEquipmentMaterials != null && !attachEquipmentMaterials.isEmpty()) {
									equipmentMaterial.setmLotId(attachEquipmentMaterials.get(0).getmLotId());
									equipmentMaterial.setMaterialName(attachEquipmentMaterials.get(0).getMaterialName());
									equipmentMaterial.setMaterialDesc(attachEquipmentMaterials.get(0).getMaterialDesc());
									equipmentMaterial.setMaterialType(attachEquipmentMaterials.get(0).getMaterialType());
									equipmentMaterial.setAttachMainQty(attachEquipmentMaterials.get(0).getAttachMainQty());
								}
							} else if(PositionSet.MATERIAL_CATEGORY_TOOL.equals(positionSetLine.getMaterialCategory())) {
								List<ConsumableEqp> attachEquipmentTools = adManager.getEntityList(
										Env.getOrgRrn(), ConsumableEqp.class, Env.getMaxResult(),
										"equipmentId = '" + eqp.getEquipmentId() + "' and positionName = '" + positionSetLine.getPositionName() + "'",null);
								if (attachEquipmentTools != null && !attachEquipmentTools.isEmpty()) {
									equipmentMaterial.setmLotId(attachEquipmentTools.get(0).getConsumableId());
									//获取治具名称、描述、类型、数量等相关信息
									List<Tool> consumableTools = adManager.getEntityList(
											Env.getOrgRrn(), Tool.class, Env.getMaxResult(),
											" objectRrn = '" + attachEquipmentTools.get(0).getConsumableRrn() + "'", null);
									if(consumableTools != null && !consumableTools.isEmpty()) {
										List<Material> materialTools = adManager.getEntityList(
												Env.getOrgRrn(), Material.class, Env.getMaxResult(),
												" objectRrn = '" + consumableTools.get(0).getMaterialRrn() + "'", null);
										equipmentMaterial.setAttachMainQty(consumableTools.get(0).getMainQty());
										if(materialTools != null && !materialTools.isEmpty()) {
											equipmentMaterial.setMaterialName(materialTools.get(0).getName());
											equipmentMaterial.setMaterialDesc(materialTools.get(0).getDescription());
											equipmentMaterial.setMaterialType(materialTools.get(0).getMaterialType());
										}
									}
								}
							}
							equipmentMaterials.add(equipmentMaterial);
						}
					}
				}
			}
				
		} catch (Exception e) {
			e.printStackTrace();
		}
		return equipmentMaterials;
	}

	//检查是否有数据
	protected void checkEquipmentMaterial(String actionType) {
		equipmentMaterials = new ArrayList<EquipmentMaterial>();
		List<Object> objects = eqpMaterialField1.getListTableManager().getCheckedObject();
		if (objects != null && objects.size() == 0) {
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			equipmentMaterials = null;
			return;
		}
		for (Object object : objects) {
			EquipmentMaterial equipmentMaterial = (EquipmentMaterial) object;
			if(ACTIONTYPE_DETACH_TOOL.equals(actionType) || ACTIONTYPE_DETACH_MATERIAL.equals(actionType)) {
				//检查解绑位置是否有物料批或Tool
				if(StringUtil.isEmpty(equipmentMaterial.getmLotId())) {
					UI.showInfo(Message.getString("mm.unkitting_not_select_null_mlot"));
					equipmentMaterials = null;
					return;
				}
			}
			//检查操作对象类型是否匹配
			Equipment equipment= searchEquipment(equipmentMaterial.getEquipmentId());
			PositionSet positionSet = searchPositionSet(equipment.getPositionSetRrn());
			for (PositionSetLine positionSetLine : positionSet.getPositionSetLines()) {
				if (positionSetLine.getPositionName().equals(equipmentMaterial.getPositionName())) {
					//检查物料类型,物料批类型只能操作物料批绑定/解绑
					if((ACTIONTYPE_ATTACH_MATERIAL.equals(actionType) || ACTIONTYPE_DETACH_MATERIAL.equals(actionType)) 
							&& PositionSet.MATERIAL_CATEGORY_TOOL.equals(positionSetLine.getMaterialCategory())) {
						UI.showInfo(Message.getString("mm.kitting_tool_category_not_support_material"));
						equipmentMaterials = null;
						return;
					}
					//检查治具类型,Tool类型只能操作Tool绑定/解绑
					if((ACTIONTYPE_ATTACH_TOOL.equals(actionType) || ACTIONTYPE_DETACH_TOOL.equals(actionType))) {
						if(PositionSet.MATERIAL_CATEGORY_MATERIAL.equals(positionSetLine.getMaterialCategory())) {
							UI.showInfo(Message.getString("mm.kitting_material_category_not_support_tool"));
							equipmentMaterials = null;
							return;
						}
					}
					//检查治具Append类型
					if(ACTIONTYPE_ATTACH_TOOL.equals(actionType) && PositionSet.REPLENISH_TYPE_APPEND.equals(positionSetLine.getReplenishType())) {
						UI.showInfo(Message.getString("mm.kitting_tool_nonsupport_append"));
						equipmentMaterials = null;
						return;
					}
					//检查Empty类型是否已绑定，已绑定则无需再绑
					if((ACTIONTYPE_ATTACH_TOOL.equals(actionType) || ACTIONTYPE_ATTACH_MATERIAL.equals(actionType))
						&& PositionSet.REPLENISH_TYPE_EMPTY.equals(positionSetLine.getReplenishType())
						&& !StringUtil.isEmpty(equipmentMaterial.getmLotId())) {
						UI.showInfo(Message.getString("mm.attach_empty_not_attach_again"));
						equipmentMaterials = null;
						return;
					}
				}
			}
			
			equipmentMaterials.add(equipmentMaterial);
			//排序位置
			equipmentMaterials.sort((a, b) -> a.getPositionName().compareTo(b.getPositionName()));
		}
	}
	
	public static Equipment searchEquipment(String equipmentId) {
		try {
			RASManager rasManager = Framework.getService(RASManager.class);
			Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentId);
			return equipment;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	public static PositionSet searchPositionSet(long positionSetRrn) {
        try {
            ADManager adManager = Framework.getService(ADManager.class);
            PositionSet positionSet = new PositionSet();
            positionSet.setObjectRrn(positionSetRrn);
			positionSet = (PositionSet)adManager.getEntity(positionSet);
            return positionSet;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
        return null;
    }
	
	//限制Append类型的只能选择一个
	protected String checkEquipmentMaterialType() {
		// 检查补充类型
		List<String> entitySet = equipmentMaterials.stream().map(EquipmentMaterial::getReplenishType)
				.collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
		if (entitySet != null && entitySet.size() > 1) {
			if(entitySet.contains(PositionSet.REPLENISH_TYPE_APPEND)) {
				UI.showInfo(Message.getString("mm.kitting_not_allow_edit_multi_type"));
				return null;
			}
		}
		return entitySet.get(0);
	}
	
	public static MLot searchMLot(String mLotId) {
        try {
            MMManager mmManager = Framework.getService(MMManager.class);
            MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
            return mLot;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
        return null;
    }
	
	/**
	 * 检查批次与position的限制
	 * 
	 * @param mlot
	 * @param equipmentMaterial
	 * @return
	 */
	public static boolean checkMLot(MLot mlot) {
		boolean flag = true;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			// 1: 检测Batch Type
			Material material = new Material();
			material.setObjectRrn(mlot.getMaterialRrn());
			material = (Material) adManager.getEntity(material);
			// TODO add the batch type of support B
//			if (Material.BATCH_TYPE_BATCH.equals(material.getBatchType())) {
//				UI.showError(Message.getString("mm.batch.type.not.support.B"));
//				return false;
//			}

			// 2: 检测 hold状态 和 检测 批次是否Avail
			if (!MLot.HOLDSTATE_OFF.equals(mlot.getHoldState())) {
				UI.showError(Message.getString("mm.lot.state.not.allow"));
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}
	
	public static Tool searchTool(String toolId) {
        try {
            ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
            Tool tool = consumableManager.getToolById(Env.getOrgRrn(), toolId);
            return tool;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
        return null;
    }
	
}