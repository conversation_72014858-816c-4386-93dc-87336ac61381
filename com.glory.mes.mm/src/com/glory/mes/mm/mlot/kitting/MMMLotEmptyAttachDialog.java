package com.glory.mes.mm.mlot.kitting;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.row.ListRowEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.model.Material;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.model.Lot;

public class MMMLotEmptyAttachDialog extends GlcBaseDialog { 

	public static final String EVENT_ENTERPRESSED = "EnterPressed";
	
	public static final String FIELD_MLOTTEXT = "mlotText";
	public static final String FIELD_KITTINGMATERIAL = "kittingMaterial";
	public static final String FIELD_SOURCESTORAGE = "sourceStorage";

	public static final String BUTTON_CLEAR = "clear";

	protected CustomField mlotTextField;
	protected TableEditorField kittingMaterialField;
	protected ListTableManagerField sourceStorageField;
	
	protected String equipmentId;
	protected boolean isCancel = true;
	protected boolean ischeckByRuningLot = true;
	
    protected List<EquipmentMaterial> equipmentMaterials;
    
    public HeaderText txtMLotId;
    public ListRowEditorTableManager tableManager;
	public CheckBoxTableViewerManager storageTableManager;
	
	public int addcount = 0;
    
    public static int MIN_DIALOG_WIDTH = 500;
    public static int MIN_DIALOG_HEIGHT = 500;

	public MMMLotEmptyAttachDialog(String adFormName, String authority, IEventBroker eventBroker, List<EquipmentMaterial> equipmentMaterials, Boolean isDoubleClick, String equipmentId) {
		super(adFormName, authority, eventBroker);
        this.equipmentMaterials = equipmentMaterials;
        this.equipmentId = equipmentId;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mlotTextField = form.getFieldByControlId(FIELD_MLOTTEXT, CustomField.class);
		kittingMaterialField = form.getFieldByControlId(FIELD_KITTINGMATERIAL, TableEditorField.class);
		sourceStorageField = form.getFieldByControlId(FIELD_SOURCESTORAGE, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(EVENT_ENTERPRESSED), this::searchAdaptor);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
		
		tableManager = kittingMaterialField.getTableManager();
		storageTableManager = (CheckBoxTableViewerManager) sourceStorageField.getListTableManager().getTableManager();
		EnterPressComposite enterPressComposite = (EnterPressComposite) mlotTextField.getCustomComposite();
		txtMLotId = enterPressComposite.getTxtLot();
				
		storageTableManager.addICheckChangedListener(checkChangedListener);
		init();
	}
	
	protected void searchAdaptor(Object object) {
		try {
			Event event = (Event) object;
			MLot mlot = (MLot) event.getProperty(GlcEvent.PROPERTY_DATA);
			mlot = MMMLotKittingEquipmentFormEditor.searchMLot(mlot.getmLotId());		
			txtMLotId.selectAll();
			if (mlot == null) {
				txtMLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			} else {
				// 若存在批次，则按照顺序从上往下填入
				try {
					MLot mLot = mlot;
					// MMEquipmentMaterialKitting
					List<EquipmentMaterial> checkLots = equipmentMaterials.stream()
							.filter(x -> mLot.getmLotId().equals(x.getmLotId()))
							.collect(Collectors.toList());
					if (checkLots == null || checkLots.isEmpty()) {
						// 物料批检查
						if (MMMLotKittingEquipmentFormEditor.checkMLot(mLot)) {
							if(addcount >= tableManager.getInput().size()) {
								UI.showError(Message.getString("mm.kitting_posotionset_is_full"));
								return;
							}
							EquipmentMaterial setEquipmentMaterial = (EquipmentMaterial) tableManager.getInput().get(addcount);
							setEquipmentMaterial.setmLotId(mLot.getmLotId());
							setEquipmentMaterial.setTransMainQty(mLot.getTransMainQty());
							setEquipmentMaterial.setMaterialName(mLot.getMaterialName());
							setEquipmentMaterial.setMaterialDesc(mLot.getMaterialDesc());
							setEquipmentMaterial.setMaterialType(mLot.getMaterialType());
							if (!Material.BATCH_TYPE_BATCH.equals(mLot.getBatchType())) {
								setEquipmentMaterial.setAttachMainQty(mLot.getMainQty());
							}
							// 扫描成功一次，记录一次
							tableManager.update(setEquipmentMaterial);
							if (Material.BATCH_TYPE_BATCH.equals(mLot.getBatchType())) {
								Object object1 = tableManager.getInput().get(addcount);
								tableManager.addEditorObjects(object1);
								List<EquipmentMaterial> equipmentMaterials = tableManager.getInput().stream().map(selectObj -> (EquipmentMaterial) selectObj).collect(Collectors.toList());
								tableManager.setInput(equipmentMaterials);
								tableManager.setCheckedObject(equipmentMaterials);
								tableManager.refresh();
							}
							addcount++;
						}
						MMManager mmManager = Framework.getService(MMManager.class);
						List<MLot> storageMLots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), Arrays.asList(mLot));
						List<MLot> inputMLots = storageTableManager.getInput().stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());;
						inputMLots.addAll(storageMLots);
						for (Iterator iterator = inputMLots.iterator(); iterator.hasNext();) {
							MLot iteratorMLot = (MLot) iterator.next();
							if ("Equipment".equals(iteratorMLot.getTransStorageType())) {
								iterator.remove();
							}
						}
						storageTableManager.setInput(inputMLots);
					} else {
						UI.showError(Message.getString("bas.line_lineuser_also_exsit_in_list"));
						return;
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
			tableManager.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	ICheckChangedListener checkChangedListener = new ICheckChangedListener() {
		@Override
		public void checkChanged(List<Object> eventObjects, boolean checked) {
			if (CollectionUtils.isEmpty(eventObjects)) {
				return;
			}
			List<MLot> mlots = storageTableManager.getCheckedObject().stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			for (Object object : eventObjects) {
				if (checked) {
					MLot mLot = (MLot) object;
					List<MLot> f = mlots.stream().filter(m -> mLot.getmLotId().equals(m.getmLotId())).collect(Collectors.toList());
					if (f.size() > 1) {
						storageTableManager.unCheckObject(object);
					} else {
						List<EquipmentMaterial> equipmentMaterials = (List<EquipmentMaterial>) tableManager.getInput();
						for (EquipmentMaterial equipmentMaterial : equipmentMaterials) {
							if (mLot.getmLotId().equals(equipmentMaterial.getmLotId())) {
								equipmentMaterial.setAttachMainQty(mLot.getMainQty());
								tableManager.update(equipmentMaterial);
							}
						}
					}
				}
			}
		
		}
	};
	
	protected void init() {
		tableManager.refresh();
		tableManager.setInput(equipmentMaterials);
	}
	

	protected void clearAdapter(Object object) {
		try {
			// 退回扫描的MLOT
			EquipmentMaterial setEquipmentMaterial = (EquipmentMaterial) tableManager.getInput().get(addcount - 1 < 0 ? 0 : addcount - 1);
			//退回扫描的库存信息
			List<MLot> mLots = storageTableManager.getInput().stream().map(o -> ((MLot)o)).collect(Collectors.toList());
			MMManager mmManager = Framework.getService(MMManager.class);
			MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), setEquipmentMaterial.getmLotId());
			for (Iterator iterator = mLots.iterator(); iterator.hasNext();) {
				MLot iteratorMLot = (MLot) iterator.next();
				if (iteratorMLot.getmLotId().equals(mLot.getmLotId())) {
					iterator.remove();
				}
			}
			storageTableManager.setInput(mLots);
			
			if (setEquipmentMaterial != null) {
				setEquipmentMaterial.setmLotId(null);
				setEquipmentMaterial.setTransMainQty(null);
				setEquipmentMaterial.setMaterialName(null);
				setEquipmentMaterial.setMaterialDesc(null);
				setEquipmentMaterial.setMaterialType(null);
				setEquipmentMaterial.setAttachMainQty(null);
				// 点击成功一次，退回一次
				addcount--;
			}
			tableManager.update(setEquipmentMaterial);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	protected void okPressed() {
    	equipmentMaterials = kittingData();
		if (equipmentMaterials != null && !equipmentMaterials.isEmpty()) {
			try {
				MMManager mmManager = Framework.getService(MMManager.class);
				MLotManager mlotManager = Framework.getService(MLotManager.class);
				List<MLot> mLots = new ArrayList<MLot>();
				for (EquipmentMaterial equipmentMaterial : equipmentMaterials) {
					if (equipmentMaterial.getmLotId() != null && !"".equals(equipmentMaterial.getmLotId())) {
						MLot mlot = mmManager.getMLotByMLotId(Env.getOrgRrn(), equipmentMaterial.getmLotId());
						if (mlot == null) {
							UI.showError(Message.getString("mm.mlot_not_found"));
							return;
						}
						List<Object> objects = storageTableManager.getCheckedObject();
						if (CollectionUtils.isEmpty(objects)) {
							UI.showInfo(Message.getString("mm.please_select_one_mlot_storage"));
							return;
						}
						List<MLot> mlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
						Map<String, List<MLot>> map = mlots.stream().collect(Collectors.groupingBy(m -> m.getmLotId()));
						if (CollectionUtils.isEmpty(map.get(mlot.getmLotId())) || map.get(mlot.getmLotId()).size() > 1) {
							UI.showError(mlot.getmLotId() + Message.getString("mm.please_select_one_mlot_storage"));
							return;
						}
						MLotStorage storage = new MLotStorage();
						if (mlot.getBatchType().equals(Material.BATCH_TYPE_BATCH)) {
							storage = mmManager.getLotStorage(mlot.getObjectRrn(), map.get(mlot.getmLotId()).get(0).getTransWarehouseRrn(),  map.get(mlot.getmLotId()).get(0).getTransStorageType(),  map.get(mlot.getmLotId()).get(0).getTransStorageId(), false);
						} else {
							List<MLotStorage> mLotStorages = mmManager.getLotStorages(mlot.getObjectRrn());
							storage = mLotStorages.get(0);
						}
						
						if (storage != null) {
							mlot.setTransWarehouseRrn(storage.getWarehouseRrn());
							mlot.setTransStorageId(storage.getStorageId());
							mlot.setTransStorageType(storage.getStorageType());
						}
						
						if (equipmentMaterial.getAttachMainQty() == null) {
							mlot.setTransMainQty(mlot.getMainQty());
						} else {
							mlot.setTransMainQty(equipmentMaterial.getAttachMainQty());
						}
						mlot.setTransPosition(equipmentMaterial.getPositionName());
						mlot.setEquipmentId(equipmentMaterial.getEquipmentId());
						mLots.add(mlot);
					}
				}
				Map<String, List<MLot>> mLotsMap = mLots.stream().collect(Collectors.groupingBy(MLot::getEquipmentId));
				if(ischeckByRuningLot) {
					//检查批次使用的BOM类型,true使用产品BOM,false使用工单BOM
					SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
					boolean isUsePartBom = MesCfMod.isCheckUseBom(Env.getOrgRrn(), sysParamManager);
					//获取当前设备RUN批次列表
					LotManager lotManager = Framework.getService(LotManager.class);
					Map<String, List<Lot>> runLotsMap = new HashMap<String, List<Lot>>();
					
					for(String equipmentId : mLotsMap.keySet()) {
						List<Lot> runningLots = lotManager.getRunningLotsByEqp(Env.getOrgRrn(), equipmentId);
						runLotsMap.put(equipmentId, runningLots);
					}
					mlotManager.kittingEquipmentMaterial(mLotsMap, runLotsMap, isUsePartBom, "ByStep", Env.getSessionContext());
				} else {
					mlotManager.kittingEquipmentMaterials(mLotsMap, Env.getSessionContext());
				}
				isCancel = false;
				super.okPressed();
			} catch (ClientParameterException e) {
				ExceptionHandlerManager.asyncHandleException(e);
			} catch (ClientException e) {
				ExceptionHandlerManager.asyncHandleException(e);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
    
	}
	
	@Override
	protected void cancelPressed() {
		isCancel = true;
		super.cancelPressed();
	}
	
	public boolean isCancel() {
		return isCancel;
	}
	
	public void setCancel(boolean isCancel) {
		this.isCancel = isCancel;
	}
	
	/**
	 * Kitting的物料批信息列表
	 * @return
	 */
	public List<EquipmentMaterial> kittingData() {
		return equipmentMaterials;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

}