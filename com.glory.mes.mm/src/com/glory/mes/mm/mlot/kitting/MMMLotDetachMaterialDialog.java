package com.glory.mes.mm.mlot.kitting;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.client.MLotManager;

public class MMMLotDetachMaterialDialog extends GlcBaseDialog { 

	private static final String FIELD_TARGETWAREHOUSE = "targetWarehouse";
	private static final String FIELD_KITTINGMATERIAL = "kittingMaterial";

	protected RefTableField targetWarehouseField;
	protected ListTableManagerField kittingMaterialField;
	
	public CheckBoxTableViewerManager tableManager;
	
    protected String equipmentId;
    protected List<EquipmentMaterial> equipmentMaterials;
    public boolean isCancel = true;
    //当前设备物料批
    public List<MLot> mLots = new ArrayList<MLot>();
  	//Append的物料批
    public List<MLot> appendMLots = new ArrayList<MLot>();
	
    private static int MIN_DIALOG_WIDTH = 500;
    private static int MIN_DIALOG_HEIGHT = 500;

	public MMMLotDetachMaterialDialog(String adFormName, String authority, IEventBroker eventBroker, List<EquipmentMaterial> equipmentMaterials, Boolean isDoubleClick, String equipmentId) {
		super(adFormName, authority, eventBroker);
		this.equipmentMaterials = equipmentMaterials;
        this.equipmentId = equipmentId;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		targetWarehouseField = form.getFieldByControlId(FIELD_TARGETWAREHOUSE, RefTableField.class);
		kittingMaterialField = form.getFieldByControlId(FIELD_KITTINGMATERIAL, ListTableManagerField.class);
		
		tableManager = (CheckBoxTableViewerManager) kittingMaterialField.getListTableManager().getTableManager();
		
		List<EquipmentMaterial> unkittingMaterials = getUnKittingData();
		tableManager.setInput(unkittingMaterials);
		
		tableManager.addICheckChangedListener(checkChangedListener);
		
		XCombo xCombo = targetWarehouseField.getComboControl();
		GridData gd = new GridData(1);
		gd.widthHint = 250;
		xCombo.setLayoutData(gd);
	}
	
    public void buildMLots(List<EquipmentMaterial> equipmentMaterials) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			for(EquipmentMaterial equipmentMaterial : equipmentMaterials) {
        		MLot mlot = mmManager.getMLotByMLotId(Env.getOrgRrn(), equipmentMaterial.getmLotId());
            	mlot.setTransMainQty(equipmentMaterial.getAttachMainQty());
            	mlot.setTransPosition(equipmentMaterial.getPositionName());
            	mlot.setEquipmentId(equipmentMaterial.getEquipmentId());
            	mLots.add(mlot);
        	}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
    
	ICheckChangedListener checkChangedListener = new ICheckChangedListener() {
		@Override
		public void checkChanged(List<Object> eventObjects, boolean checked) {
			if (checked) {
				boolean flag = true;
				Object objectRrn = targetWarehouseField.getValue();
				if (objectRrn == null) {
					flag = false;
					UI.getActiveShell().getDisplay().asyncExec(new Runnable() {
						@Override
						public void run() {
							UI.showWarning(Message.getString("ras.please_select_warehouse"));
							for (Object object : eventObjects) {
								EquipmentMaterial equipmentMaterial = (EquipmentMaterial) object;
								//tableManager.unCheckObject(object);
								tableManager.getCheckedObject().remove(object);
								equipmentMaterial.setTargetWarehouseId(null);
								equipmentMaterial.setTargetWarehouseRrn(null);
							}
						}
					});
				}
				for (Object object : eventObjects) {
					EquipmentMaterial equipmentMaterial = (EquipmentMaterial) object;
					if (flag) {
						// 根据仓库RRN获取仓库信息
						long whreHouseRrn = Long.parseLong(String.valueOf(objectRrn));
						try {
							MMManager mmManager = Framework.getService(MMManager.class);
							Warehouse warehouse = new Warehouse();
							warehouse.setObjectRrn(whreHouseRrn);
							warehouse = mmManager.getWarehouse(warehouse);

							equipmentMaterial.setTargetWarehouseId(warehouse.getWarehouseId());
							equipmentMaterial.setTargetWarehouseRrn(whreHouseRrn);
						} catch (Exception e) {
							e.printStackTrace();
						}
					} else {
						//tableManager.unCheckObject(object);
						tableManager.getCheckedObject().remove(object);
						equipmentMaterial.setTargetWarehouseId(null);
						equipmentMaterial.setTargetWarehouseRrn(null);
					}
				}
			} else {
				for (Object object : eventObjects) {
					((EquipmentMaterial) object).setTargetWarehouseId(null);
					((EquipmentMaterial) object).setTargetWarehouseRrn(null);
				}
			}
		
		}
	};
	
    @Override
    protected void okPressed() {
        try {
        	//选择的解绑的物料批次数据列表
        	List<EquipmentMaterial> deatchEquipmentMaterials = new ArrayList<EquipmentMaterial>();
        	List<Object> objects = tableManager.getCheckedObject();
        	if(objects != null && !objects.isEmpty()) {
        		for(Object object : objects) {
            		deatchEquipmentMaterials.add((EquipmentMaterial)object);
            	}
            	//检查解绑仓库
            	List<EquipmentMaterial> targetWarehouse = deatchEquipmentMaterials.stream().filter(x -> x.getTargetWarehouseRrn() == null).collect(Collectors.toList());
            	if(targetWarehouse != null && !targetWarehouse.isEmpty()) {
            		UI.showError(Message.getString("error.no_warehouse_input"));
            	} else {
            		 MLotManager mlotManager = Framework.getService(MLotManager.class);
                     Map<Long, List<EquipmentMaterial>> map = deatchEquipmentMaterials.stream()
                     		.collect(Collectors.groupingBy(EquipmentMaterial::getTargetWarehouseRrn));
                     for(Long taegetWarehouseRrn : map.keySet()) {
                     	List<EquipmentMaterial> equipmentMaterials = map.get(taegetWarehouseRrn);
                     	buildMLots(equipmentMaterials);
                     	Map<String, List<MLot>> mlotsMap = mLots.stream().collect(Collectors.groupingBy(MLot::getEquipmentId));
                     	mlotManager.unKittingEquipmentMaterials(mlotsMap, null, taegetWarehouseRrn, null, null, Env.getSessionContext());
                     }
                     isCancel = false;
                     super.okPressed();
            	}
        	} else {
        		UI.showError(Message.getString("common.please_select"));
        	}
        } catch (ClientParameterException e) {
			ExceptionHandlerManager.asyncHandleException(e);
        } catch (ClientException e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} catch (Exception e) {
            e.printStackTrace();
        }
    }
	
	@Override
	protected void cancelPressed() {
		isCancel = true;
		super.cancelPressed();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
	
	/**
	 * 不同dialog数据格式不同
	 * @param equipmentMaterials
	 * @return
	 */
	public List<EquipmentMaterial> getUnKittingData() {
		return equipmentMaterials;
	}
	
	
	public List<EquipmentMaterial> getEquipmentMaterials() {
		return equipmentMaterials;
	}
	
	public void setEquipmentMaterials(List<EquipmentMaterial> equipmentMaterials) {
		this.equipmentMaterials = equipmentMaterials;
	}
	
	public boolean isCancel() {
		return isCancel;
	}
	
	public void setCancel(boolean isCancel) {
		this.isCancel = isCancel;
	}
}