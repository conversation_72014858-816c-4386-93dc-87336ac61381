package com.glory.mes.mm.mlot.kitting;

import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.model.Lot;

public class MMMLotKittingStepBomDialog extends GlcBaseDialog{

	private static final String FIELD_ENTITYFORM = "stepBomMaterial";
	protected ListTableManagerField listTableManagerField;
	protected Lot lot;
	protected ListTableManager listTableManager;

	
	public MMMLotKittingStepBomDialog(String adFormName, String authority, IEventBroker eventBroker,Lot lot) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		listTableManagerField = form.getFieldByControlId(FIELD_ENTITYFORM, ListTableManagerField.class);		
		listTableManager = listTableManagerField.getListTableManager();
		init();
	}
	
	private void init() {
		try {
            //获取批次工单Bom信息
            PpManager ppManager = Framework.getService(PpManager.class);
            WorkOrder workOrder = new WorkOrder();
            workOrder.setDocId(lot.getWoId());
            workOrder = ppManager.getWorkOrder(workOrder, Env.getSessionContext());
            if(workOrder != null) {
            	List<WorkOrderBomLine> workOrderBomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
            	listTableManager.setInput(workOrderBomLines);
            }
		}  catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	protected void cancelPressed() {
		super.cancelPressed();
	}

	
	@Override
	 protected Point getInitialSize() {
	    return new Point(800,600);
	 }
	
}
