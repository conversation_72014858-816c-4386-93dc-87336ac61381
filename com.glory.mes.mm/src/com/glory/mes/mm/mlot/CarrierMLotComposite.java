package com.glory.mes.mm.mlot;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.DurableMLotManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.lot.model.MLot;

/**
 * 载具物料批次显示界面
 * 可以输入载具和物料批次
 * 可输入载具号,显示载具中对应的物料批次,可有多个物料批次
 * 可输入批次,根据isShowAll来决定是根据批次找到载具，并显示载具中的所有批次，还是仅选择输入的批次
 */
public class CarrierMLotComposite extends Composite {

	private static final Logger logger = Logger.getLogger(CarrierMLotComposite.class);
	
	private static final String TABLE_NAME = "MMLotByCarrier";
	
	//Table种是否显示checkBox
	protected boolean checkFlag;
	
	//是否显示批号输入栏位
	protected boolean showMLotFlag;
	
	//是否显示批号详细信息(动态表)
	protected boolean isShowAll = false;

	protected ListTableManager mlotTableManager;
	protected EntityForm mlotDetailsForm;
	protected String lblCarrier;
	protected HeaderText txtCarrierId;
	protected HeaderText txtMLotId;

	protected int tableHeigthHint = Toolkit.getDefaultToolkit().getScreenSize().height / 3;

	public CarrierMLotComposite(Composite parent, int style, boolean checkFlag) {
		this(parent, style, checkFlag, false, false);
	}
	
	public CarrierMLotComposite(Composite parent, int style, boolean checkFlag, boolean showMLotFlag, boolean isShowAll) {
		super(parent, style);
		this.checkFlag = checkFlag;
		this.showMLotFlag = showMLotFlag;
		this.isShowAll = isShowAll;
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite carrierComposite = new Composite(this, SWT.NONE);
			int gridY = 2;
			if (showMLotFlag) {
				gridY += 2;
			}
		
			carrierComposite.setLayout(new GridLayout(gridY, false));
			carrierComposite.setLayoutData(new GridData());
			
	        Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
	        if (StringUtil.isEmpty(lblCarrier)) {
				lblCarrierId.setText(Message.getString("wip.carrier_id"));
	        } else {
	        	lblCarrierId.setText(lblCarrier);
	        }
			lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

			txtCarrierId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-carrier"));
			txtCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
	
			if (showMLotFlag) {
				Label lblLotId = new Label(carrierComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
			
				txtMLotId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-lot"));
				txtMLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtMLotId.setTextLimit(32);				
				txtMLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// 回车事件
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText();
							if (!StringUtil.isEmpty(lotId)) {
								getMLotByLotId(lotId);
							}
						}
					}
				});
			}
						
			txtCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// 回车事件
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String carrierId = ((Text) event.widget).getText();
						if (!StringUtil.isEmpty(carrierId)) {
							getLotsByCarrierId(carrierId);
						}
					}
				}
			});
			
			Composite lotComposite = new Composite(this, SWT.NONE);
			lotComposite.setLayout(new GridLayout(1, false));
			
			GridData gridData = new GridData(GridData.FILL_BOTH);
			gridData.heightHint = tableHeigthHint;
			lotComposite.setLayoutData(gridData);
			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			mlotTableManager = new ListTableManager(lotTable, checkFlag);
			mlotTableManager.setAutoSizeFlag(true);
			mlotTableManager.newViewer(lotComposite);
			mlotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					StructuredSelection selection = (StructuredSelection) event.getSelection();
					MLot mlot = (MLot) selection.getFirstElement();
					if (mlotDetailsForm != null) { 
						mlotDetailsForm.setObject(mlot);
						mlotDetailsForm.loadFromObject();
					}
				}
			});
		} catch (Exception e) {
			logger.error("StepTreeView createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void getMLotByLotId(String mlotId) {
		try {
			if (!isMLotIdCaseSensitive()) {
				mlotId = mlotId.toUpperCase();
			}
			txtMLotId.setText(mlotId);
			MMManager mmManager = Framework.getService(MMManager.class);
			MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mlotId);
			if (mLot != null) {
				if (isShowAll) {
					if (!StringUtil.isEmpty(mLot.getDurable())) {
						getLotsByCarrierId(mLot.getDurable());
					}
				} else {
					List<MLot> mLots = new ArrayList<MLot>();
					List<MLot> tableMLots = (List<MLot>) mlotTableManager.getInput();
					if (tableMLots != null && tableMLots.size() > 0) {
						for (MLot tableMLot : tableMLots) {
							if (mLot.getmLotId().equals(tableMLot.getmLotId())) {
								return;
							}
						}
					}
					mLots.add(mLot);
					mLots.addAll(tableMLots);
					
					mlotTableManager.setInput(mLots);
					
					txtMLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					mlotTableManager.refresh();
					// 默认全选
					if (checkFlag) {
						mlotTableManager.setCheckedObject(mLot);
					}
					mlotTableManager.setSelection(new StructuredSelection(new Object[] {mLot}));
					
					txtMLotId.focusing();
				}
			} else {
				mlotTableManager.setInput(new ArrayList<MLot>());
				mlotTableManager.refresh();

				txtMLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				
				mlotTableManager.setSelection(new StructuredSelection(new Object[] {new MLot()}));
				
				txtMLotId.warning();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				DurableMLotManager carrierMLotManager = Framework.getService(DurableMLotManager.class);
				List<MLot> mlots = carrierMLotManager.getMLotsByDurableId(Env.getOrgRrn(), carrierId);
				mlotTableManager.setInput(mlots);
				// 默认全选
				if (checkFlag) {
					for (MLot mlot : mlots) {
						mlotTableManager.setCheckedObject(mlot);
					}
				}
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				mlotTableManager.refresh();
				txtCarrierId.focusing();
			} else {
				mlotTableManager.setInput(new ArrayList<MLot>());
				mlotTableManager.refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtCarrierId.warning();
			}
			
			if (mlotDetailsForm != null) {
				mlotDetailsForm.setObject(new MLot());
				mlotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public HeaderText getTxtCarrierId() {
		return txtCarrierId;
	}

	public void setTxtCarrierId(HeaderText txtCarrierId) {
		this.txtCarrierId = txtCarrierId;
	}

	public HeaderText getMTxtLotId() {
		return txtMLotId;
	}

	public void setMTxtLotId(HeaderText txtLotId) {
		this.txtMLotId = txtLotId;
	}

	public ListTableManager getMLotTableManager() {
		return mlotTableManager;
	}

	public void setMLotTableManager(ListTableManager lotTableManager) {
		this.mlotTableManager = lotTableManager;
	}

	public String getLblCarrier() {
		return lblCarrier;
	}

	public void setLblCarrier(String lblCarrier) {
		this.lblCarrier = lblCarrier;
	}
	
	public int getTableHeigthHint() {
		return tableHeigthHint;
	}

	public void setTableHeigthHint(int tableHeigthHint) {
		this.tableHeigthHint = tableHeigthHint;
	}
	
	private Boolean isCaseSensitive;
	public boolean isMLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isMLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
