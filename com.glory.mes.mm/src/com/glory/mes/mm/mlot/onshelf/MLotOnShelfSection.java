package com.glory.mes.mm.mlot.onshelf;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.mlot.MLotSection;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.framework.core.exception.ExceptionBundle;

public class MLotOnShelfSection extends MLotSection {

	public CheckBoxFixEditorTableManager manager;

	private MLotOnShelfFrom itemForm;

	public MLotOnShelfSection(ADTable adTable) {
		super(adTable);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemMaterialOn(tBar);
		new ToolItem(tBar, 2);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemMaterialOn(ToolBar tBar) {
		this.itemSave = new ToolItem(tBar, SWT.PUSH);
		this.itemSave.setText(Message.getString("mm.mlot_on"));
		this.itemSave.setImage(SWTResourceCache.getImage("mlot_onshelf"));
		this.itemSave.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				MLotOnShelfSection.this.onAdapter();
			}
		});
	}

	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		itemForm = new MLotOnShelfFrom(composite, SWT.NONE, tab, mmng);
		return itemForm;
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		if (adObject != null && adObject.getObjectRrn() != null) {
			itemForm.loadCurrent(adObject.getObjectRrn());
		}
	}

	private void onAdapter() {
		form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
                if (!detailForm.saveToObject()) {
                	saveFlag = false;
                }
            }
			if (saveFlag) {
				MLot mLot = (MLot) getAdObject();
				if (mLot == null) {
					return;
				}

				try {
					
					MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
					List<MLotStorage> storages =  mmManager.getLotStorages(mLot.getObjectRrn());
					if (CollectionUtils.isEmpty(storages)) {
						throw new ClientException("mm.mlot_must_specify_lotstorage");
					}

					if (storages.size() > 1) {
						throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
					}
					MLotStorage storage = storages.get(0);
					
					mLot.setTransWarehouseRrn(storage.getWarehouseRrn());
					mLot.setTransStorageType(storage.getStorageType());
					mLot.setTransStorageId(storage.getStorageId());
					
					mLot.setTransTargetWarehouseRrn(storage.getWarehouseRrn());
					
					if (itemForm.getRackAreaRrn() != null) {
						mLot.setTransTargetStorageType(Storage.CATEGORY_RACKAREA);
						
						Storage rackAeraStorage = getStorage(itemForm.getRackAreaRrn());
						mLot.setTransTargetStorageId(rackAeraStorage.getName());
					} else if (itemForm.getRackRrn() != null) {
						mLot.setTransTargetStorageType(Storage.CATEGORY_RACK);
						
						Storage rackStorage = getStorage(itemForm.getRackRrn());
						mLot.setTransTargetStorageId(rackStorage.getName());
					}
					
					mLot = on(mLot, storage.getWarehouseRrn(), mLot.getTransTargetStorageType(), mLot.getTransTargetStorageId());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					setAdObject(mLot);
					this.refreshAdapter();
					
					// 方便继续操作
					txtLot.setFocus();
					txtLot.selectAll();
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	}
	
	public MLot on(MLot mLot, Long toWarehouseRrn, String toStorageType, String toStorageId) throws Exception {
		MLotAction lotAction = new MLotAction();
		lotAction.setMainQty(mLot.getMainQty());
		
		Warehouse warehouse = new Warehouse();
		warehouse.setObjectRrn(toWarehouseRrn);
		MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
		warehouse = mmManager.getWarehouse(warehouse);
		return mmManager.mLotOnShelf(mLot, lotAction, warehouse, 
				mLot.getTransTargetStorageType(), mLot.getTransTargetStorageId(), Env.getSessionContext());
	}
	
	private Storage getStorage(Long storageRrn) throws Exception {
		MMManager mmManager = Framework.getService(MMManager.class);
		Storage storage = new Storage();
		storage.setObjectRrn(storageRrn);
		return mmManager.getStorage(storage);
	}

	@Override
	public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);
		ADBase adObject = getAdObject();
		if (adObject != null && adObject.getObjectRrn() != null) {
			MLot mLot = (MLot) adObject;

			if (MLot.HOLDSTATE_ON.equals(mLot.getHoldState())) {
				itemSave.setEnabled(false);
			} else {
				if (newStatus != null && MaterialState.STATE_IN.equals(mLot.getState())) {
					itemSave.setEnabled(true);
				} else {
					itemSave.setEnabled(false);
				}
			}
		}
	}

}
