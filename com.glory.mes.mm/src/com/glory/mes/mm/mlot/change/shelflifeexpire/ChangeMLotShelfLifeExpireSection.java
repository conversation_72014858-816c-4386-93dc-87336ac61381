package com.glory.mes.mm.mlot.change.shelflifeexpire;

import java.util.Date;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.forms.field.DateTimeField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;

public class ChangeMLotShelfLifeExpireSection extends QueryEntityListSection implements IRefresh {

	protected DateTimeField expireDateTimeField;
	protected ToolItem itemChange;

	public ChangeMLotShelfLifeExpireSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChange(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	private void createToolItemChange(ToolBar tBar) {
		itemChange = new ToolItem(tBar, SWT.PUSH);
		itemChange.setText(Message.getString("wip.change"));
		itemChange.setImage(SWTResourceCache.getImage("wip_code"));
		itemChange.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeAdapter();
			}
		});
	}

	private void changeAdapter() {
	
		List<Object> objects = getCheckedObject();
		if (objects == null || objects.isEmpty()) {
			UI.showError(Message.getString("mm_material_information"));
			return;
		}

		//获取时间
		Date date = (Date) expireDateTimeField.getValue();
		if (date == null) {
			UI.showError(Message.getString("mm_select_the_expiration_date"));
			return;
		}

		try {
			for (Object object : objects) {
				MLot mLot = (MLot) object;
				mLot.setShelfLifeExpire(date);
			}
			MMManager mmManager = Framework.getService(MMManager.class);
			mmManager.changeMLotShelfLifeExpire((List<MLot>)(List)objects, Env.getSessionContext());
			
			UI.showError(Message.getString("mm_modification_period"));
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void createContents(IManagedForm form, Composite parent) {
		final FormToolkit toolkit = form.getToolkit();
		super.createContents(form, parent);
		
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		Composite fieldComposite = toolkit.createComposite(parent);
		fieldComposite.setLayout(new GridLayout(2, false));
		fieldComposite.setLayoutData(gd);
		
		GridLayout layout = new GridLayout();
	    layout.verticalSpacing = 0;
	    layout.marginHeight = 0;
		GridData gText = new GridData();
		gText.widthHint = 300;
		int mStyle = SWT.READ_ONLY | SWT.BORDER;
		expireDateTimeField = new DateTimeField("", mStyle);
		expireDateTimeField.setLabel(Message.getString("mm_period_validity"));
		expireDateTimeField.createContent(fieldComposite, toolkit);
		expireDateTimeField.getControls()[0].setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
}
