package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.graphics.Point;

import com.glory.common.state.model.Event;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.model.Material;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.framework.core.exception.ExceptionBundle;

public class MMLotOutDialog extends MLotActionDialog {
	
	public static final String ADFORM_NAME = "MMLotOutDialog";
	public static final String AUTHORITY = "MM.MLotActionOut";
	
	private static final String FIELD_MLOTLIST = "mlotList";
	private static final String FIELD_OUT_ACTION = "outAction";
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 330;
	
	private ListTableManagerField mlotListField;
	private EntityFormField outActionEntityForm;
	protected List<MLot> mlots = new ArrayList<MLot>();
	protected Event event;
	
	public MMLotOutDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
	}
	
	public MMLotOutDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
		setmLotList(mlots);
		this.mlots = mlots;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		outActionEntityForm = form.getFieldByControlId(FIELD_OUT_ACTION, EntityFormField.class);
		outActionEntityForm.setValue(new MLot());
		outActionEntityForm.refresh();
		ListTableManager listTableManager = mlotListField.getListTableManager();
		listTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				MLot mlot = (MLot) selection.getFirstElement();
				outActionEntityForm.setValue(mlot);
				outActionEntityForm.refresh();
			}
		});
		initLot();
	}
	
	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getmLotList())) {
			mlots = getmLotList();
		}
		try {
			List<MLot> inputMlots = new ArrayList<MLot>();
			if(CollectionUtils.isNotEmpty(mlots)) {
				// 查询批次已入库信息
				MMManager mmManager = Framework.getService(MMManager.class);
				inputMlots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), mlots);
			}
			mlotListField.getListTableManager().setInput(inputMlots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	@Override
	protected void okPressed() {
		try {
			if (outActionEntityForm.validate()) {
				MLot mLotAction = (MLot) outActionEntityForm.getValue();
				MMManager mmManager = Framework.getService(MMManager.class);	
				MLot mLot =  (MLot) mlotListField.getListTableManager().getSelectedObject();
				List<MLot> mlots = Arrays.asList(mLot);
				if (mLot == null) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				} else {
					String fromInfo = mlots.get(0).getTransWarehouseRrn() + mlots.get(0).getTransStorageId() + mlots.get(0).getTransStorageType();
					for(MLot mlot : mlots) {
						if(!(mlot.getTransWarehouseRrn() + mlot.getTransStorageId() + mlot.getTransStorageType()).equals(fromInfo)) {
							UI.showError(mlot.getmLotId() + Message.getString("mm.warehouse_from_different"));
							return;
						}
					}
				}
				for (MLot mlot : mlots) {
					//物料批次库存信息
					Long mlotRrn = mmManager.getMLotByMLotId(Env.getOrgRrn(), mlot.getmLotId()).getObjectRrn();
					mlot.setObjectRrn(mlotRrn);
					List<MLotStorage> storages = mmManager.getLotStorages(mlotRrn);
					if (storages == null || storages.size() == 0) {
						UI.showError(mlot.getmLotId() + Message.getString("mm.lot_not_in_warehouse_or_storage"));
						return;
					}		
					if (Material.BATCH_TYPE_LOT.equals(this.mlots.get(0).getBatchType()) && storages.size() > 1) {
						UI.showError(mlot.getmLotId() + Message.getString("wms.lot_in_multi_warehouse_or_storage"));
						return;
					}
					List<String> storageKeys = new ArrayList<String>();
					for (MLotStorage storage : storages) {
						String storageKey = storage.getWarehouseRrn() + storage.getStorageType() + storage.getStorageId();
						storageKeys.add(storageKey);
					}
					String inputKey = mLotAction.getTransWarehouseRrn() + mLotAction.getTransStorageType() + mLotAction.getTransStorageId();
					if (!storageKeys.contains(inputKey)) {
						UI.showError(mlot.getmLotId() + Message.getString("mm.storage_no_exist"));
						return;
					}
					MLotStorage storage = mmManager.getLotStorage(mlot.getObjectRrn(), mLotAction.getTransWarehouseRrn(), mLotAction.getTransStorageType(), mLotAction.getTransStorageId(), false);
					mlot.setTransMainQty(storage.getOnhandMainQty());
				}
				if(mlots.get(0).getSubUnitType().equals(MLot.UNIT_TYPE_COMPONENT)) {
					String fromWarehouseId = mmManager.getWarehouseId(mLotAction.getTransWarehouseRrn());
					mmManager.outMLotByComponent(mlots, null, new MLotAction(), mLotAction.getTransWarehouseRrn(), fromWarehouseId,
							mLotAction.getTransStorageType(), mLotAction.getTransStorageId(), false,
							Env.getSessionContext());
				} else {
					mmManager.outMLots(mlots, new MLotAction(), mLotAction.getTransWarehouseRrn(), null,
							mLotAction.getTransStorageType(), mLotAction.getTransStorageId(), false,
							Env.getSessionContext());
				}
				
				UI.showInfo(Message.getString("mm.mlot_ship_success"));// 弹出提示框
				super.okPressed();
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean checkMLotState(List<MLot> mLots) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			return mmManager.checkMLotState(mLots, MaterialEvent.EVENT_INVENTORYOUT, true, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
	
	@Override
	public boolean preValidate() {
		boolean flag = super.preValidate();
		if (flag) {
			if (!checkMLotState(getmLotList())) {
				return false;
			}
		} 
		return flag;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}
