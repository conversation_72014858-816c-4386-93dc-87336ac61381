package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.wip.model.ComponentUnit;

public class MMLotCompScrapDialog extends MLotActionDialog{
	
	private static int DIALOG_WIDTH = 600;
	private static int DIALOG_HEIGHT = 370;
	
	private static final String ADFORM_NAME = "MMLotCompScrapDialog";
	private static final String ENTITY_FORM = "scrapCode";
	private static final String LISTTABLE_FORM = "mLotCompList";
	
	private static final String FIELD_ACTION_CODE = "actionCode";
	private static final String FIELD_ACTION_REASON = "actionReason";
	private static final String FIELD_ACTION_COMMENT = "actionComment";
	
	private ListTableManagerField listTableManagerField;
	private EntityFormField entityFormField;
	
	private CheckBoxTableViewerManager tableManager;
	
	private RefTableField actionCodeField;
	private TextField actionReasonField, actionCommentField;

	private List<MLot> mLots;
	private List<MComponentUnit> mComponentUnits;
	
	protected SquareButton ok;
	
	public MMLotCompScrapDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots) {
		super(ADFORM_NAME, authority, eventBroker);
		this.mLots = mLots;
		setmLotList(mLots);
	}
	
	public MMLotCompScrapDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots, List<MComponentUnit> mComponentUnits) {
		super(ADFORM_NAME, authority, eventBroker);
		this.mLots = mLots;
		this.mComponentUnits = mComponentUnits;
		setmLotList(mLots);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		entityFormField = form.getFieldByControlId(ENTITY_FORM, EntityFormField.class);
		listTableManagerField = form.getFieldByControlId(LISTTABLE_FORM, ListTableManagerField.class);
		
		actionCodeField = entityFormField.getFieldByControlId(FIELD_ACTION_CODE, RefTableField.class);
		actionReasonField = entityFormField.getFieldByControlId(FIELD_ACTION_REASON, TextField.class);
		actionCommentField = entityFormField.getFieldByControlId(FIELD_ACTION_COMMENT, TextField.class);
		
		ListTableManager listTableManager = listTableManagerField.getListTableManager();
		tableManager = (CheckBoxTableViewerManager) listTableManager.getTableManager();
	
		initLot();
	}

	@Override
	public void initLot() {
		try {
			tableManager.setInput(mComponentUnits);
			tableManager.addICheckChangedListener(new ICheckChangedListener() {
				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if (checked) {
						boolean flag = true;
						if(actionCodeField.getText() == null
								|| "".equals(actionCodeField.getText().trim())){
							flag = false;
							UI.getActiveShell().getDisplay().asyncExec(new Runnable() {
								@Override
								public void run() {
									UI.showWarning(String.format(Message.getString("wip.scrap_code_required"), Message.getString("wip.trackout_scrapcode")));
									for (Object object : eventObjects) {
										MComponentUnit compositeUnit = (MComponentUnit) object;
										tableManager.unCheckObject(object);
										compositeUnit.setReserved8("");
									}
								}
							});
						}
						for (Object object : eventObjects) {
							MComponentUnit compositeUnit = (MComponentUnit) object;
							if (flag) {
								compositeUnit.setReserved8(actionCodeField.getText());
							}else {
								tableManager.unCheckObject(object);
								compositeUnit.setReserved8("");
							}
						}
					}else {
						for (Object object : eventObjects) {
							((MComponentUnit)object).setReserved8("");
						}
					}
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}

	@Override
	protected void okPressed() {
		if (validate()) {
			try {
				MMManager mmManager = Framework.getService(MMManager.class);
				List<MComponentUnit> scrapUnits = new ArrayList<MComponentUnit>();
            	List<Object> elements = tableManager.getCheckedObject();
                for (int i = 0; i < elements.size(); i++) {
                	MComponentUnit comp = (MComponentUnit) elements.get(i);
                    scrapUnits.add(comp);
                }
                if (scrapUnits.size() == 0) {
                    UI.showWarning(String.format(Message.getString("common.please_select"),
                            ComponentUnit.class.getSimpleName()));
                    return;
                }
                List<MLotAction> mlotActions = new ArrayList<MLotAction>();
    	    	for (MComponentUnit mComponentUnit : scrapUnits) {
    	    		MLotAction action = new MLotAction();
    	    		action.setmLotRrn(mLots.get(0).getObjectRrn());
    	    		action.setActionCode(mComponentUnit.getReserved8());
    	    		action.setActionReason(actionReasonField.getText());
    	    		action.setActionComment(actionCommentField.getText());
    	    		if (StringUtil.isEmpty(actionCommentField.getText())) {
    	    			UI.showInfo(Message.getString("wip.abort_comments_null"));
    	    			return;
    	    		}
    	    		action.setMainQty(mComponentUnit.getMainQty());
    	    		action.setSubQty(mComponentUnit.getSubQty());
    	    		action.setmComponentUnits(Arrays.asList(mComponentUnit));
    	    		mlotActions.add(action);
    	    	}
    	    	 mmManager.scrapMLot(mLots.get(0), mlotActions, Env.getSessionContext());
                 UI.showInfo(Message.getString("wip.scrapLot_success"));// 弹出提示框
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
                return;
			}
		}
		super.okPressed();
	}
	
	public boolean validate() {
		boolean validateFlag = true;
		if (StringUtil.isEmpty(actionCodeField.getText())) {
			validateFlag = false;
		}
		return validateFlag;
	}
	
	@Override
	protected void buttonPressed(int buttonId) {
		if (IDialogConstants.OK_ID == buttonId || IDialogConstants.YES_ID == buttonId) {
			okPressed();
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed();
		}
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}
