package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.base.merge.MergeRuleResult.MergeRuleException;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.query.MLotQueryGlcEditor;

public class MMLotMergeDialog extends MLotActionDialog{ 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.mlot.action.dialog.MMLotMergeDialog";

	private static int DIALOG_WIDTH = 600;
	private static int DIALOG_HEIGHT = 320;
	
	private static final String FIELD_MERGELOT = "mergeLot";
	private static final String FIELD_MERGELOTLIST = "mergeLotList";
	private static final String FIELD_MERGECOMMENT = "mergeComment";

	private static final String BUTTON_ADD = "add";
	private static final String BUTTON_DELETE = "delete";

	protected TextField mergeLotField;
	protected ListTableManagerField mergeLotListField;
	protected TextField mergeCommentField;
	
	protected ListTableManager listTableManager;
	protected CheckBoxTableViewerManager tableManager;
	
	protected List<MLot> mLots;
	protected MLot mLot;
	protected MLotQueryGlcEditor mLotQueryGlcEditor;
	
	protected Text txtMergeMLot;
	private Boolean isCaseSensitive;

	public MMLotMergeDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots, MLotQueryGlcEditor mLotQueryGlcEditor) {
		super(adFormName, authority, eventBroker);
		this.mLots = mLots;
		this.mLotQueryGlcEditor = mLotQueryGlcEditor;
		setmLotList(mLots);
		setBlockOnOpen(false);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mergeLotField = form.getFieldByControlId(FIELD_MERGELOT, TextField.class);
		mergeLotListField = form.getFieldByControlId(FIELD_MERGELOTLIST, ListTableManagerField.class);
		mergeCommentField = form.getFieldByControlId(FIELD_MERGECOMMENT, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ADD), this::addAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		
		listTableManager = mergeLotListField.getListTableManager();
		tableManager = (CheckBoxTableViewerManager) listTableManager.getTableManager();
		
		txtMergeMLot = mergeLotField.getTextControl();
		initLot();
	}

	@Override
	public void initLot() {
		mLot = mLots.get(0);
    	txtMergeMLot.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
		GridData gText = new GridData();
		gText.widthHint = 216;
		txtMergeMLot.setLayoutData(gText);
		txtMergeMLot.setTextLimit(32);
		txtMergeMLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					searchLot(lotId);
					tLotId.selectAll();
					break;
				}
			}

		});
	}

	private void addAdapter(Object object) {
		if(mLot == null || mLot.getObjectRrn() == null) {
			UI.showError(Message.getString("wip.merge_noparent"));
			return;
		}
		MMLotChildMergeDialog dialog = new MMLotChildMergeDialog("MMLotChildMergeDialog", null, eventBroker, mLot);
		List<Object> objs = (List<Object>)tableManager.getInput();
		List<MLot> value = objs.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
		dialog.setExistMergedMLots(value);
		if (dialog.open() == Dialog.OK) {
			List<MLot> selectedMLots = dialog.getSelectionMLots();
			if(value != null) {
				for(MLot mLot : selectedMLots){
					if (!value.contains(mLot)) {
						value.add(mLot);
					}
				}
				tableManager.setInput(value);
			} else {
				tableManager.setInput(selectedMLots);
			}
		}
	
	}

	private void deleteAdapter(Object object) {
		List<Object> checkedObject = tableManager.getCheckedObject();
		List<Object> objs = (List<Object>)tableManager.getInput();
		List<MLot> removedMLots = objs.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
		if(checkedObject != null && !checkedObject.isEmpty()) {
			for(Object o : checkedObject) {
				MLot mLot = (MLot)o;
				removedMLots.remove(mLot);
			}
			tableManager.setInput(removedMLots);
		}
	
	}
	
	@Override
	protected void okPressed() {
		try {
			MLot parentMLot = mLot;
			if(parentMLot == null || parentMLot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.input_lot_first"));
			} else {
				List<Object> objects = (List<Object>) tableManager.getInput();
				List<MLot> mergedMLots = objects.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
				if(mergedMLots == null || mergedMLots.size() == 0) {
					UI.showError(Message.getString("wip.merge_nolot"));
					return;
				}
				
				String comment = mergeCommentField.getValue() + "";
				MLotAction lotAction = new MLotAction();
				lotAction.setActionComment(comment);
                
				try {
					MMManager mmManager = Framework.getService(MMManager.class);
					mmManager.mergeMLot(parentMLot, mergedMLots, lotAction, Env.getSessionContext());
					mLotQueryGlcEditor.refreshAdapter(null);
					UI.showInfo(Message.getString("wip.merge_success"));
				} catch(Exception e) {
					UI.showError(Message.getString("wip.merge_failure"));
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	
		super.okPressed();
	}
	
    public void searchLot(String mLotId) {
		try {
			if (mLot == null || mLot.getObjectRrn() == null) {
				UI.showError(Message.getString("error.no_lot_input"));
				return;
			}
			MMManager mmManager = Framework.getService(MMManager.class);
			MLot mergeMLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
			if (mergeMLot == null) {
				UI.showError(Message.getString("error.lot_not_exist"));
				return;
			}
			if (mLot.getObjectRrn().equals(mergeMLot.getObjectRrn())) {
				UI.showError(Message.getString("wip.lotid_repeat"));
				return;
			}
			
			List<MLot> cMLots = new ArrayList<MLot>();
			cMLots.add(mergeMLot);
			//检查合批规则
			MergeRuleResult result = mmManager.checkMLotMergeRule(mLot, cMLots, null, Env.getSessionContext());
			if (!result.isSuccess()) {
				MergeRuleException exception = result.getException();
				throw new ClientParameterException(exception.getExceptionName(),exception.getExceptionParams());
			}
			
			List<Object> objs = (List<Object>)tableManager.getInput();
			List<MLot> mergeMLots = objs.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
			if (mergeMLots == null) {
				mergeMLots = new ArrayList<MLot>();
			}
			if (mergeMLots.contains(mergeMLot)) {
				UI.showError(Message.getString("wip.box_already_exists"));
				return;
			}
			mergeMLots.add(mergeMLot);
			tableManager.setInput(mergeMLots);
        } catch (Exception e) {
        	ExceptionHandlerManager.asyncHandleException(e);
		}
	}
    
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}