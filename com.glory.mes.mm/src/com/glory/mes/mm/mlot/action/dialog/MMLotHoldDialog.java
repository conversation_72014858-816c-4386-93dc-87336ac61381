package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.action.MLotActionDialog;

public class MMLotHoldDialog extends MLotActionDialog {

	public static final String ADFORM_NAME = "MMLotHoldDialog";
	public static final String AUTHORITY = "MM.MLotActionHold";
	private static final String FIELD_MLOTLIST = "mlotList";
	private static final String FIELD_HOLD_ACTION = "holdAction";
	private ListTableManagerField mlotListField;
	private EntityFormField holdActionEntityForm;
	protected List<MLot> mlots = new ArrayList<MLot>();
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 365;
	
	public MMLotHoldDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
		setmLotList(mlots);
		this.mlots = mlots;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		holdActionEntityForm = form.getFieldByControlId(FIELD_HOLD_ACTION, EntityFormField.class);
		holdActionEntityForm.setValue(new MLotAction());
		holdActionEntityForm.refresh();
		initLot();
	}

	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getmLotList())) {
			mlots = getmLotList();
		}
		try {
			if(CollectionUtils.isNotEmpty(mlots)) {
				// 查询批次已入库信息
				MMManager mmManager = Framework.getService(MMManager.class);
				mlots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), mlots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		mlotListField.getListTableManager().setInput(mlots);
	}

	
	@Override
	protected void okPressed() {
		try {
			if (holdActionEntityForm.validate()) {
				MLotAction mLotAction = (MLotAction) holdActionEntityForm.getValue();
				MMManager mmManager = Framework.getService(MMManager.class);
				for (MLot mlot : mlots) {
					Long mlotRrn = mmManager.getMLotByMLotId(Env.getOrgRrn(), mlot.getmLotId()).getObjectRrn();
					mlot.setObjectRrn(mlotRrn);
				}
				mmManager.holdMLot(mlots, mLotAction, Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.hold_successed"));// 弹出提示框
				super.okPressed();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	@Override
	public boolean preValidate() {
		boolean flag = super.preValidate();
		if (flag) {
			for(MLot mLot : getmLotList()) {
				if (MLot.STATE_COM.equals(mLot.getComClass())) {
					UI.showError(mLot.getmLotId() + Message.getString("mm.mlot_state_not_allowed"));
					return false;
				}
				if (MLot.HOLDSTATE_ON.equals(mLot.getHoldState())) {
					UI.showError(mLot.getmLotId() + Message.getString("mm.mlots_already_hold"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

}
