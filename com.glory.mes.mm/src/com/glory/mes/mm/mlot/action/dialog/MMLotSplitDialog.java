package com.glory.mes.mm.mlot.action.dialog;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.query.MLotQueryGlcEditor;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class MMLotSplitDialog extends MLotActionDialog{ 
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.mlot.action.dialog.MMLotSpiltDialog";

	private static int DIALOG_WIDTH = 600;
	private static int DIALOG_HEIGHT = 350;
	
	private static final String ADFORM_NAME = "MMLotSplitDialog";
	
	private static final String FIELD_MLOTSPILTLIST = "mLotSpiltList";
	private static final String FIELD_MLOTSPLITQTY = "mLotSplitQty";
	private static final String FIELD_MLOTID = "mLotId";
	private static final String FIELD_MAINQTY = "mainQty";
	private static final String FIELD_SPLIT_TYPE = "splitType";

	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_ADD = "add";

	protected ListTableManagerField mLotSpiltListField;
	protected EntityFormField mLotSplitQtyField;
	protected TextField mLotIdField;
	protected TextField mainQtyField;
	protected RadioField splitTypeField;
	
	protected List<MLot> mLots;
	protected MLot lot;
	protected MLotQueryGlcEditor mLotQueryGlcEditor;
	
	protected ListTableManager listTableManager;
	protected CheckBoxFixEditorTableManager tableManager;
	
	public MMLotSplitDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots, MLotQueryGlcEditor mLotQueryGlcEditor) {
		super(ADFORM_NAME, authority, eventBroker);
		this.mLots = mLots;
		this.mLotQueryGlcEditor = mLotQueryGlcEditor;
		setmLotList(mLots);
		setBlockOnOpen(false);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mLotSpiltListField = form.getFieldByControlId(FIELD_MLOTSPILTLIST, ListTableManagerField.class);
		mLotSplitQtyField = form.getFieldByControlId(FIELD_MLOTSPLITQTY, EntityFormField.class);
		mLotIdField = mLotSplitQtyField.getFieldByControlId(FIELD_MLOTID, TextField.class);
		mainQtyField = mLotSplitQtyField.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		splitTypeField = mLotSplitQtyField.getFieldByControlId(FIELD_SPLIT_TYPE, RadioField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ADD), this::addAdapter);
		
		listTableManager = mLotSpiltListField.getListTableManager();
		
		tableManager = (CheckBoxFixEditorTableManager) listTableManager.getTableManager();
		
		mainQtyField.getTextControl().addModifyListener(getModifyListener("double"));
		
		initLot();
	}

	@Override
	public void initLot() {
		mLots = getmLotList();
		lot = mLots.get(0);
	}
	
	@Override
	protected void okPressed() {
		// 检查重复批号
		Set<String> mLotIds = new HashSet<String>();
		for (Object object : tableManager.getInput()) {
			MLot mLot = (MLot) object;
			if (StringUtil.isEmpty(mLot.getmLotId())) {
				continue;
			}
			
			if (mLotIds.contains(mLot.getmLotId())) {
				UI.showWarning(String.format(Message.getString("mm.lot_id_repeat"), mLot.getmLotId()));
				return;
			}
			
			mLotIds.add(mLot.getmLotId());
		}
		
		// 检查母批数量是否足够
		BigDecimal total = BigDecimal.ZERO;
		for (Object object : tableManager.getInput()) {
			MLot mLot = (MLot) object;
			if (BigDecimal.ZERO.compareTo(mLot.getMainQty()) >= 0) {
				UI.showWarning(Message.getString("mm.split_qty_greater0"));
				return;
			}
			total = total.add(mLot.getMainQty());
		}
		
		BigDecimal reservedQty = lot.getReservedMainQty() == null ? BigDecimal.ZERO : lot.getReservedMainQty();
		if (total.compareTo(lot.getMainQty().subtract(reservedQty)) > 0) {
			UI.showWarning(Message.getString("mm.split_parent_not_enough"));
			return;
		}
		
		mLotQueryGlcEditor.split(lot, getSubLots());
		super.okPressed();
	}
	
	private void deleteAdapter(Object object) {
		List<Object> checkedList = tableManager.getCheckedObject();
		tableManager.getInput().removeAll(checkedList);
		tableManager.refresh();
	}

	private void addAdapter(Object object) {
		String splitType = (String) splitTypeField.getValue();
		if (StringUtil.isEmpty(splitType)) {
			UI.showInfo(Message.getString("mm.please_select_split_type"));
			return;
		}
		if (splitType.equals("SplitQty")) {
			BigDecimal qty = getInputQty();
			if (qty == null) {
				UI.showWarning(Message.getString("mm.enter_qty"));
				return;
			}
			BigDecimal reservedQty = lot.getReservedMainQty() == null ? BigDecimal.ZERO : lot.getReservedMainQty();
			BigDecimal totalQty = lot.getMainQty().subtract(reservedQty);
			
			if (qty.compareTo(totalQty) > 0) {
				if (!UI.showConfirm(Message.getString("mm.qty_greater_than_parent"))) {
					return;
				}
			}
			BigDecimal total = BigDecimal.ZERO;
			for (Object obj : tableManager.getInput()) {
				MLot mLot = (MLot) obj;
				if (BigDecimal.ZERO.compareTo(mLot.getMainQty()) >= 0) {
					UI.showWarning(Message.getString("mm.split_qty_greater0"));
					return;
				}
				total = total.add(mLot.getMainQty());
			}
			BigDecimal getInputQty = qty.add(total);
			if (getInputQty.compareTo(totalQty) > 0) {
				if (!UI.showConfirm(Message.getString("mm.qty_greater_than_parent"))) {
					return;
				}
			}
			
			MLot mLot = new MLot();
			mLot.setMainQty(qty);
			mLot.setmLotId(getInputMLotId());
			getTableManager().add(mLot);
			getTableManager().refresh();
		} else if (splitType.equals("SplitEqual")) {
			BigDecimal qty = getInputQty();
			if (qty == null) {
				UI.showWarning(Message.getString("mm.enter_qty"));
				return;
			}
			BigDecimal reservedQty = lot.getReservedMainQty() == null ? BigDecimal.ZERO : lot.getReservedMainQty();
			BigDecimal totalQty = lot.getMainQty().subtract(reservedQty);
			
			BigDecimal total = BigDecimal.ZERO;
			for (Object obj : tableManager.getInput()) {
				MLot mLot = (MLot) obj;
				if (BigDecimal.ZERO.compareTo(mLot.getMainQty()) >= 0) {
					UI.showWarning(Message.getString("mm.split_qty_greater0"));
					return;
				}
				total = total.add(mLot.getMainQty());
			}
			
			BigDecimal getInputQty = qty.add(total);
			if (getInputQty.compareTo(totalQty) > 0) {
				if (!UI.showConfirm(Message.getString("mm.qty_greater_than_parent"))) {
					return;
				}
			}
			
			while (totalQty.compareTo(BigDecimal.ZERO) > 0) {
				if (qty.compareTo(totalQty) > 0) {
					MLot mLot = new MLot();
					mLot.setMainQty(totalQty);
					mLot.setmLotId(getInputMLotId());
					getTableManager().add(mLot);
					totalQty = BigDecimal.ZERO;
				} else {
					MLot mLot = new MLot();
					mLot.setMainQty(qty);
					mLot.setmLotId(getInputMLotId());
					getTableManager().add(mLot);
					totalQty = totalQty.subtract(qty);
				}
			}
			getTableManager().refresh();
		}
	}

	public ModifyListener getModifyListener(final String dataType) {
		return new ModifyListener() {
			public void modifyText(ModifyEvent e) {
				Text text = (Text)e.widget;
				String value = text.getText().trim();
				if ("".equalsIgnoreCase(value.trim())) {
					return;
				}
				if (!discernQty(value)) {
					text.setText("");
					text.setFocus();
				}
			}
			public boolean discernQty(String value) {
				if (!ValidatorFactory.isValid(dataType, value)) {
					UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInputError()), Message.getString("common.inputerror_title"));
					return false;
				}
				return true;
			}
		};
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	public CheckBoxFixEditorTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(CheckBoxFixEditorTableManager tableManager) {
		this.tableManager = tableManager;
	}

	@SuppressWarnings("unchecked")
	public List<MLot> getSubLots() {
		return (List<MLot>) Lists.newArrayList(tableManager.getInput());
	}

	public BigDecimal getInputQty() {
		String numStr = mainQtyField.getTextControl().getText();
		if (StringUtil.isEmpty(numStr)) {
			return null;
		}
		return new BigDecimal(numStr);
	}
	
	public String getInputMLotId() {
		String mLotIdStr = mLotIdField.getTextControl().getText();
		if (StringUtil.isEmpty(mLotIdStr)) {
			return null;
		}
		return mLotIdStr;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
}