package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.common.state.model.Event;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.framework.core.exception.ExceptionBundle;

public class MMLotInDialog extends MLotActionDialog {
	
	public static final String ADFORM_NAME = "MMLotInDialog";
	public static final String AUTHORITY = "MM.MLotActionInProcessor";
	
	private static final String FIELD_MLOTLIST = "mlotList";
	private static final String FIELD_INPROCESSOR_ACTION = "inProcessorAction";
	
	private ListTableManagerField mlotListField;
	private EntityFormField inProcessorActionEntityForm;
	protected List<MLot> mlots = new ArrayList<MLot>();
	protected Event event;
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 360;
	
	public MMLotInDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
	}
	
	public MMLotInDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
		setmLotList(mlots);
		this.mlots = mlots;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		inProcessorActionEntityForm = form.getFieldByControlId(FIELD_INPROCESSOR_ACTION, EntityFormField.class);
		inProcessorActionEntityForm.setValue(new MLot());
		inProcessorActionEntityForm.refresh();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getmLotList())) {
			mlots = getmLotList();
		}
		mlotListField.getListTableManager().setInput(mlots);
	}
	
	@Override
	protected void okPressed() {
		try {
			List<Object> objects = (List<Object>) mlotListField.getListTableManager().getInput();
			if(CollectionUtils.isNotEmpty(objects)) {
				List<MLot> checkMlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
				MMManager mmManager = Framework.getService(MMManager.class);	
				if (!inProcessorActionEntityForm.validate()) {
					return;
				}
				MLot inProcessorAction = (MLot) inProcessorActionEntityForm.getValue();
				if(checkMlots.get(0).getSubUnitType().equals(MLot.UNIT_TYPE_COMPONENT)) {
					String toWarehouseId = mmManager.getWarehouseId(inProcessorAction.getTransWarehouseRrn());
					mmManager.inMLotByComponent(checkMlots, null, 
							null, inProcessorAction.getTransWarehouseRrn(), toWarehouseId, inProcessorAction.getTransStorageType(), 
							inProcessorAction.getTransStorageId(), Env.getSessionContext());
				}else {
					mmManager.inMLots(checkMlots, null, 
							inProcessorAction.getTransWarehouseRrn(), null, inProcessorAction.getTransStorageType(), 
							inProcessorAction.getTransStorageId(), Env.getSessionContext());
				}
				
				UI.showInfo(Message.getString("wip.ship_success"));// 弹出提示框
			}else {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean checkMLotState(List<MLot> mLots) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			return mmManager.checkMLotState(mLots, MaterialEvent.EVENT_INVENTORYIN, true, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
	
	@Override
	public boolean preValidate() {
		boolean flag = super.preValidate();
		if (flag) {
			if (!checkMLotState(getmLotList())) {
				return false;
			}
		}
		return flag;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}
