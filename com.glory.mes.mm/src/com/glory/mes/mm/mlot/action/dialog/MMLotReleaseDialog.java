package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.action.MLotActionDialog;

public class MMLotReleaseDialog extends MLotActionDialog {

	public static final String ADFORM_NAME = "MMLotReleaseDialog";
	public static final String AUTHORITY = "MM.MLotActionRelease";
	
	private static final String FIELD_MLOTLIST = "mlotList";
	private static final String FIELD_RELEASE_ACTION = "releaseAction";
	private ListTableManagerField mlotListField;
	private EntityFormField releaseActionEntityForm;
	protected List<MLot> mlots = new ArrayList<MLot>();
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 365;
	
	public MMLotReleaseDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
		setmLotList(mlots);
		this.mlots = mlots;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		releaseActionEntityForm = form.getFieldByControlId(FIELD_RELEASE_ACTION, EntityFormField.class);
		releaseActionEntityForm.setValue(new MLotAction());
		releaseActionEntityForm.refresh();
		initLot();
	}

	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getmLotList())) {
			mlots = getmLotList();
		}
		try {
			if(CollectionUtils.isNotEmpty(mlots)) {
				// 查询批次已入库信息
				MMManager mmManager = Framework.getService(MMManager.class);
				mlots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), mlots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		mlotListField.getListTableManager().setInput(mlots);
	}

	@Override
	protected void okPressed() {
		try {
			if (releaseActionEntityForm.validate()) {
				MMManager mmManager = Framework.getService(MMManager.class);
				for (MLot mlot : mlots) {
					Long mlotRrn = mmManager.getMLotByMLotId(Env.getOrgRrn(), mlot.getmLotId()).getObjectRrn();
					mlot.setObjectRrn(mlotRrn);
				}
				mmManager.releaseMLot(mlots, (MLotAction)releaseActionEntityForm.getValue(), Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.release_successed"));// 弹出提示框
				super.okPressed();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public boolean checkMLotState(MLot mLot) {
		if (MLot.HOLDSTATE_ON.equals(mLot.getHoldState())) {
			return true;
		} else {
			return false;
		}
	}
	
	@Override
	public boolean preValidate() {
		boolean flag = super.preValidate();
		if (flag) {
			for(MLot mLot : getmLotList()) {
				if(!checkMLotState(mLot)) {
					UI.showError(mLot.getmLotId() + Message.getString("mm.mlot_state_not_allow"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}
