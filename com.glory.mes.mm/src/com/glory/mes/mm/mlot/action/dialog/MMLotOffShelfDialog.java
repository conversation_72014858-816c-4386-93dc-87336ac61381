package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.common.state.model.Event;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.model.Material;
import com.glory.framework.core.exception.ExceptionBundle;

public class MMLotOffShelfDialog extends MLotActionDialog {
	
	public static final String ADFORM_NAME = "MMLotOffShelfDialog";
	public static final String AUTHORITY = "MM.MLotActionOffShelf";
	
	private static final String FIELD_MLOTLIST = "mlotList";
	
	private ListTableManagerField mlotListField;
	protected List<MLot> mlots = new ArrayList<MLot>();
	protected Event event;
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 300;
	
	public MMLotOffShelfDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
	}
	
	public MMLotOffShelfDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
		setmLotList(mlots);
		this.mlots = mlots;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		initLot();
	}
	
	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getmLotList())) {
			mlots = getmLotList();
		}
		try {
			List<MLot> mLots = new ArrayList<MLot>();
			if(CollectionUtils.isNotEmpty(mlots)) {
				// 查询批次已入库信息
				MMManager mmManager = Framework.getService(MMManager.class);
				mLots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), mlots);
			}
			List<MLot> inputMLots = new ArrayList<MLot>();
			if(CollectionUtils.isNotEmpty(mLots)) {
				for(MLot mLot : mLots) {
					if(!StringUtil.isEmpty(mLot.getTransStorageId())) {
						inputMLots.add(mLot);
					}
				}
			}
			mlotListField.getListTableManager().setInput(inputMLots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		try {
			List<Object> objects = (List<Object>) mlotListField.getListTableManager().getCheckedObject();
			List<MLot> mlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
			if (mlots == null || mlots.size() == 0) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			for (MLot lot : mlots) {
				//物料批次库存信息
				Long mlotRrn = mmManager.getMLotByMLotId(Env.getOrgRrn(), lot.getmLotId()).getObjectRrn();
				MLotStorage storage = mmManager.getLotStorage(mlotRrn, lot.getTransWarehouseRrn(), lot.getTransStorageType(), lot.getTransStorageId(), false);
				
				if (storage == null) {
					UI.showError(lot.getmLotId() + Message.getString("mm.lot_not_in_warehouse_or_storage"));
					return;
				}
				lot.setObjectRrn(mlotRrn);
				lot.setTransMainQty(lot.getMainQty());
				List<MLot> mlotStroages = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), Arrays.asList(lot));
				if (Material.BATCH_TYPE_LOT.equals(this.mlots.get(0).getBatchType())  && mlotStroages.size() > 1) {
					UI.showError(lot.getmLotId() + Message.getString("wms.lot_in_multi_warehouse_or_storage"));
					return;
				}
			}
			mmManager.mLotOffShelf(mlots, new MLotAction(), null, null, null, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean checkMLotState(List<MLot> checkMLots) {
		try {
			// 1.检查批次是否已入库
			MMManager mmManager = Framework.getService(MMManager.class);
			mlots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), checkMLots);
			List<MLot> inputMLots = new ArrayList<MLot>();
			if(CollectionUtils.isNotEmpty(mlots)) {
				for(MLot mLot : mlots) {
					if(!StringUtil.isEmpty(mLot.getTransStorageId())) {
						inputMLots.add(mLot);
					}
				}
			}
			if (inputMLots.size() == 0) {
				UI.showError(Message.getString("wms.mlot_storage_not_found"));
				return false;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} 
		return true;
	}
	
	@Override
	public boolean preValidate() {
		boolean flag = super.preValidate();
		if (flag) {
			if (!checkMLotState(getmLotList())) {
				return false;
			}
		} 
		return flag;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}
