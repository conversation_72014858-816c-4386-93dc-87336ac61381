package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.common.state.model.Event;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.model.Material;
import com.glory.framework.core.exception.ExceptionBundle;

public class MMLotOnShelfDialog extends MLotActionDialog {
	
	public static final String ADFORM_NAME = "MMLotOnShelfDialog";
	public static final String AUTHORITY = "MM.MLotActionOnShelf";
	
	private static final String FIELD_MLOTLIST = "mlotList";
	private static final String FIELD_ONSHELF_ACTION = "onShelfAction";
	
	private ListTableManagerField mlotListField;
	private EntityFormField onShelfActionEntityForm;
	protected List<MLot> mlots = new ArrayList<MLot>();
	protected Event event;
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 370;
	
	public MMLotOnShelfDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
	}
	
	public MMLotOnShelfDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
		setmLotList(mlots);
		this.mlots = mlots;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		onShelfActionEntityForm = form.getFieldByControlId(FIELD_ONSHELF_ACTION, EntityFormField.class);
		onShelfActionEntityForm.setValue(new MLot());
		onShelfActionEntityForm.refresh();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getmLotList())) {
			mlots = getmLotList();
		}
		try {
			List<MLot> inputMlots = new ArrayList<MLot>();
			if(CollectionUtils.isNotEmpty(mlots)) {
				// 查询批次已入库信息
				MMManager mmManager = Framework.getService(MMManager.class);
				inputMlots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), mlots);
			}
			mlotListField.getListTableManager().setInput(inputMlots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	@Override
	protected void okPressed() {
		try {
			if (onShelfActionEntityForm.validate()) {
				MLot mLotAction = (MLot) onShelfActionEntityForm.getValue();
				MMManager mmManager = Framework.getService(MMManager.class);	
				List<Object> objects = (List<Object>) mlotListField.getListTableManager().getCheckedObject();
				List<MLot> mlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
				if (mlots == null || mlots.size() == 0) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				}
				for (MLot mlot : mlots) {
					//物料批次库存信息
					Long mlotRrn = mmManager.getMLotByMLotId(Env.getOrgRrn(), mlot.getmLotId()).getObjectRrn();
					mlot.setObjectRrn(mlotRrn);
					mlot.setTransMainQty(mlot.getMainQty());
					List<MLot> mlotStroages = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), Arrays.asList(mlot));
					if (CollectionUtils.isEmpty(mlotStroages)) {
						UI.showError(mlot.getmLotId() + Message.getString("mm.lot_not_in_warehouse_or_storage"));
						return;
					}	
					if (Material.BATCH_TYPE_LOT.equals(this.mlots.get(0).getBatchType())  && mlotStroages.size() > 1) {
						UI.showError(mlot.getmLotId() + Message.getString("wms.lot_in_multi_warehouse_or_storage"));
						return;
					}
					
					if (!mlot.getTransWarehouseRrn().equals(mLotAction.getTransTargetWarehouseRrn())) {
						UI.showError(mlot.getmLotId() + Message.getString("mm.onshelf_can_not_change_warehouse"));
						return;
					}
					
					String storageKey = mlot.getTransWarehouseRrn() + mlot.getTransStorageType() + mlot.getTransStorageId();
					String changeKey = mLotAction.getTransTargetWarehouseRrn() + mLotAction.getTransTargetStorageType() + mLotAction.getTransTargetStorageId();
					
					// 未做改变，提示后返回
					if (storageKey.equals(changeKey)) {
						UI.showError(mlot.getmLotId() + Message.getString("mm.transfer_no_change"));
						return;
					}
				}
			
				Warehouse warehouse = new Warehouse();
				warehouse.setObjectRrn(mLotAction.getTransTargetWarehouseRrn());
				warehouse = mmManager.getWarehouse(warehouse);
				mmManager.mLotOnShelf(mlots, null, warehouse, mLotAction.getTransTargetStorageType(), mLotAction.getTransTargetStorageId(), Env.getSessionContext());							
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
				super.okPressed();
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean checkMLotState(List<MLot> checkMLots) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			mlots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), checkMLots);
			if(CollectionUtils.isNotEmpty(mlots)) {
				for(MLot mlot : mlots) {
					if(StringUtil.isEmpty(mlot.getTransWarehouseId())) {
						UI.showError(mlot.getmLotId() + Message.getString("mm.mlot_must_specify_lotstorage"));
						return false;
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return true;
	}
	
	@Override
	public boolean preValidate() {
		boolean flag = super.preValidate();
		if (flag) {
			if (!checkMLotState(getmLotList())) {
				return false;
			}
		} 
		return flag;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}
