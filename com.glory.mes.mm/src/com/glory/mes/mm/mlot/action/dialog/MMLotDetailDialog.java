package com.glory.mes.mm.mlot.action.dialog;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.mlot.action.MLotActionDialog;

public class MMLotDetailDialog extends MLotActionDialog{

	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;
	
	private static final String ADFORM_NAME = "MMLotDetailDialog";
	private static final String ENTITY_FORM = "mLotInfo";
	private static final String LISTTABLE_FORM = "mLotChildList";
	
	private static final String FIELD_CURRENT_WAREHOUSE = "currentWarehouseRrn";
	private static final String FIELD_CURRENT_RACK = "currentRackId";
	private static final String FIELD_CURRENT_RACK_AREA = "currentRackAreaId";
	
	private ListTableManagerField listTableManagerField;
	private EntityFormField entityFormField;
	
	private RefTableField warehouseField, rackField, rackAreaField;
	
	private List<MLot> mLots;

	public MMLotDetailDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	public MMLotDetailDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots) {
		super(ADFORM_NAME, authority, eventBroker);
		this.mLots = mLots;
		setmLotList(mLots);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		entityFormField = form.getFieldByControlId(ENTITY_FORM, EntityFormField.class);
		listTableManagerField = form.getFieldByControlId(LISTTABLE_FORM, ListTableManagerField.class);
		
		warehouseField = entityFormField.getFieldByControlId(FIELD_CURRENT_WAREHOUSE, RefTableField.class);
		rackField = entityFormField.getFieldByControlId(FIELD_CURRENT_RACK, RefTableField.class);
		rackAreaField = entityFormField.getFieldByControlId(FIELD_CURRENT_RACK_AREA, RefTableField.class);
		initLot();
	}

	@Override
	public void initLot() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			mLots = getmLotList();
			//基本信息
			entityFormField.setValue(mLots.get(0));
			entityFormField.refresh();
			//子批列表
			List<MLot> subLots = adManager.getEntityList(Env.getOrgRrn(), MLot.class, Env.getMaxResult(),
					"parentMLotRrn = " + mLots.get(0).getObjectRrn(), "objectRrn");
			listTableManagerField.getListTableManager().setInput(subLots);
			//储位信息
			MLotStorage storage = getMLotStorage(mLots.get(0).getObjectRrn());
			if (storage == null) {
				return;
			}
			warehouseField.setValue(storage.getWarehouseRrn(), true);
			if (StringUtil.isEmpty(storage.getStorageType())) {
				rackField.setValue(null, true);
				rackAreaField.setValue(null, true);
				return;
			}

			if (Storage.CATEGORY_RACK.equals(storage.getStorageType())) {
				// 物料只与货架做了绑定，显示货架值
				rackField.setValue(storage.getStorageId(), true);
				rackAreaField.setValue(null, true);
			} else {
				// 物料与库位做了绑定，显示库位值
				rackAreaField.setValue(storage.getStorageId(), true);

				// 显示库位所在的货架
				Storage rackStorage = getRack(storage.getStorageId());
				if (rackStorage != null) {
					rackField.setValue(rackStorage.getName(), true);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private MLotStorage getMLotStorage(Long mLotRrn) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			MLot mLot = new MLot();
			mLot.setObjectRrn(mLotRrn);
			mLot = (MLot) adManager.getEntity(mLot);

			MMManager mmManager = Framework.getService(MMManager.class);
			List<MLotStorage> storages = mmManager.getLotStorages(mLotRrn);
			
			warehouseField.setValue(null, true);
			rackField.setValue(null, true);
			rackAreaField.setValue(null, true);		
			
			if (CollectionUtils.isEmpty(storages)) {		
			return null;
			}

			if (storages.size() > 1) {
				throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
			}
			return storages.get(0);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	// 显示库位所在的货架
	private Storage getRack(String rackAreaId) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);

			Storage childStorage = new Storage();
			childStorage.setName(rackAreaId);
			childStorage.setOrgRrn(Env.getOrgRrn());
			childStorage = mmManager.getStorage(childStorage);

			Storage parentStorage = new Storage();
			parentStorage.setOrgRrn(Env.getOrgRrn());
			parentStorage.setObjectRrn(childStorage.getParentRrn());
			parentStorage = mmManager.getStorage(parentStorage);
			return parentStorage;
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}
