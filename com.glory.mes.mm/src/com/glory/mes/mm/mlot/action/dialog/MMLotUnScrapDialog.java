package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotScrap;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.model.Material;
import com.google.common.collect.Maps;

public class MMLotUnScrapDialog extends MLotActionDialog{

	
	private static int DIALOG_WIDTH = 600;
	private static int DIALOG_HEIGHT = 370;
	
	private static final String ENTITY_FORM = "unScrapAction";
	private static final String LISTTABLE_FORM = "mLotList";
	
	private static final String FIELD_ACTION_CODE = "actionCode";
	private static final String FIELD_ACTION_REASON = "actionReason";
	private static final String FIELD_ACTION_COMMENT = "actionComment";
	
	private ListTableManagerField listTableManagerField;
	private EntityFormField entityFormField;
	
	private CheckBoxFixEditorTableManager tableManager;
	private ListTableManager listTableManager;
	private RefTableField actionCodeField;
	private TextField actionReasonField, actionCommentField;

	private List<MLot> mLots;
	private List<MLotScrap> mLotScraps;
	private Boolean isUnScrapComponent = true;
	
	public MMLotUnScrapDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots, List<MLotScrap> mLotScraps, Boolean isUnScrapComponent) {
		super(adFormName, authority, eventBroker);
		this.mLots = mLots;
		this.mLotScraps = mLotScraps;
		this.isUnScrapComponent = isUnScrapComponent;
		setmLotList(mLots);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		entityFormField = form.getFieldByControlId(ENTITY_FORM, EntityFormField.class);
		listTableManagerField = form.getFieldByControlId(LISTTABLE_FORM, ListTableManagerField.class);
		
		actionCodeField = entityFormField.getFieldByControlId(FIELD_ACTION_CODE, RefTableField.class);
		actionReasonField = entityFormField.getFieldByControlId(FIELD_ACTION_REASON, TextField.class);
		actionCommentField = entityFormField.getFieldByControlId(FIELD_ACTION_COMMENT, TextField.class);
		
		listTableManager = listTableManagerField.getListTableManager();
		
		tableManager = (CheckBoxFixEditorTableManager) listTableManager.getTableManager();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		try {
			tableManager.setInput(this.mLotScraps);	        
    		tableManager.addICheckChangedListener(new ICheckChangedListener() {
    			@Override
    			public void checkChanged(List<Object> eventObjects, boolean checked) {
    				if (CollectionUtils.isEmpty(eventObjects)) {
    					return;
    				}
    				boolean removeCheckFlag = false;
    				boolean removeCheckCommentFlag = false;
    				for (Object object : eventObjects) {
    					MLotScrap unit = (MLotScrap) object;
    					if (unit == null) {
    						return;
    					}
    					if (checked) {
    						String unScrapCode = actionCodeField.getText();
    						if (StringUtil.isEmpty(unScrapCode)) {
    							removeCheckFlag = true;
    							break;
    						}
    						String comments = actionCommentField.getText();
    						if (StringUtil.isEmpty(comments)) {
    							removeCheckCommentFlag = true;
    							break;
    						}
    						String reasons = actionReasonField.getText();
    						unit.setUnScrapCode(unScrapCode);
    						unit.setUnScrapReason(reasons);
    						unit.setUnScrapComment(comments);
    					} else {
    						unit.setUnScrapCode("");
    						unit.setUnScrapReason("");
    						unit.setUnScrapComment("");
    					}
    				}
    				
    				if (removeCheckFlag) {
    					tableManager.getCheckedObject().removeAll(eventObjects);
    					UI.showWarning(Message.getString("unscrap.unscrapnew_dialog_selectcodefirst"));
    				} 
     				if (removeCheckCommentFlag) {
    					tableManager.getCheckedObject().removeAll(eventObjects);
    					UI.showWarning(Message.getString("wip.abort_comments_null"));
    				} 
    			}
    		});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}
	
	@Override
	protected void okPressed() {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			if (isUnScrapComponent) {
				// 位置不能为空
				Optional<MLotScrap> f = mLotScraps.stream().filter(
						l -> l.getUnScrapMainQty() == null || l.getUnScrapSubQty() == null).findFirst();
				f = mLotScraps.stream().filter(
						l -> {
							return l.getAttribute2() == null ||
									StringUtil.isEmpty(l.getAttribute2().toString());
						}).findFirst();
				if (f.isPresent()) {
					UI.showInfo(Message.getString("wip.unscrap_position_null"));
					return;
				}

				// 检查一片是否输入了多个位置
				// 检查一个位置是否对应多片了
				Map<String, String> positionsMap = Maps.newHashMap();
				Map<String, String> unitsMap = Maps.newHashMap();
				for (MLotScrap mLotScrap : mLotScraps) {
					String position = DBUtil.toString(mLotScrap.getAttribute2());
					if (positionsMap.containsKey(mLotScrap.getmComponentId())) {
						if (!position.equals(positionsMap.get(mLotScrap.getmComponentId()))) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_multiple_position"),
									mLotScrap.getmComponentId()));
							return;
						}
					}

					if (unitsMap.containsKey(position)) {
						if (!mLotScrap.getmComponentId().equals(unitsMap.get(position))) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_position_repeat"),
									mLotScrap.getmComponentId(), unitsMap.get(position)));
							return;
						}
					}

					positionsMap.put(mLotScrap.getmComponentId(), position);
					unitsMap.put(position, mLotScrap.getmComponentId());
				}

				// 检查片位置是否已被占用
				List<MComponentUnit> mComponentUnits = mmManager.getMLotWithComponent(mLots.get(0).getObjectRrn()).getSubMComponentUnit();
				if (!CollectionUtils.isEmpty(mComponentUnits)) {
					Map<String, String> poistions = Maps.newHashMap();
					for (MComponentUnit unit : mComponentUnits) {
						MComponentUnit mComponentUnit = (MComponentUnit) unit;
						poistions.put(mComponentUnit.getmComponentId(), mComponentUnit.getPosition());
					}

					for (MLotScrap mLotScrap : mLotScraps) {
						String position = DBUtil.toString(mLotScrap.getAttribute2());
						if (poistions.containsKey(mLotScrap.getmComponentId())) {
							if (position.equals(poistions.get(mLotScrap.getmComponentId()))) {
								UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"),
										mLotScrap.getmComponentId(), poistions.get(mLotScrap.getmComponentId())));
								return;
							}
						} else {
							Boolean judge = false;
							String repeatPosition = null;
							for (Map.Entry<String, String> entry : poistions.entrySet()) {
								if (position.equals(entry.getValue())) {
									repeatPosition = entry.getKey();
									judge = true;
									break;
								}
							}
							if (judge) {
								UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"), repeatPosition,
										poistions.get(repeatPosition)));
								return;
							}
						}
					}
				}
			}
			// 检查数量
			List<Object> objects = tableManager.getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("wip.unscrap_no_units_selected"));
				return;
			}
			List<MLotScrap> mLotScraps = objects.stream()
					.map(l -> ((MLotScrap)l)).collect(Collectors.toList());
			
			List<MLotAction> mlotActions = new ArrayList<MLotAction>();
	    	// 把scrap转成action
	    	for (MLotScrap mLotScrap : mLotScraps) {
	    		MLotAction action = new MLotAction();
	    		action.setmLotRrn(mLotScrap.getmLotRrn());
	    		action.setActionCode(mLotScrap.getUnScrapCode());
	    		action.setActionReason(mLotScrap.getUnScrapReason());
	    		action.setActionComment(mLotScrap.getUnScrapComment());
	    		if (StringUtil.isEmpty(mLotScrap.getUnScrapComment())) {
	    			UI.showInfo(Message.getString("wip.abort_comments_null"));
	    			return;
	    		}
	    		action.setMainQty(mLotScrap.getUnScrapMainQty());
	    		action.setSubQty(mLotScrap.getUnScrapSubQty());
	    		action.setmLotScraps(Arrays.asList(mLotScrap));
	    		mlotActions.add(action);
	    	}
	    	if (Material.BATCH_TYPE_LOT.equals(mLots.get(0).getBatchType())) {
	    		mmManager.unScrapMLot(mLots.get(0), mlotActions, Env.getSessionContext());
			} else {
				String storageKey = mLotScraps.get(0).getWarehouseRrn() + mLotScraps.get(0).getStorageType() + mLotScraps.get(0).getStorageId();
				for (MLotScrap mLotScrap :  mLotScraps) {
					String changeKey = mLotScrap.getWarehouseRrn() + mLotScrap.getStorageType() + mLotScrap.getStorageId();
					// 返报废多个库位，不支持
					if (!storageKey.equals(changeKey)) {
						UI.showError(Message.getString("mm.unscrap_no_apply_muilt_storage_mlot"));
						return;
					}
				}
				mmManager.unScrapMLot(mLots.get(0), mlotActions, mLotScraps.get(0).getWarehouseRrn(), mLotScraps.get(0).getWarehouseId(), mLotScraps.get(0).getStorageType(), mLotScraps.get(0).getStorageId(), Env.getSessionContext());
			}
	        UI.showInfo(Message.getString("wip.unscrapLot_success"));// 弹出提示框
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton ok = createSquareButton(parent, IDialogConstants.OK_ID,
				Message.getString(ExceptionBundle.bundle.CommonOk()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, "");
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
		
		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
		
	}
	
	@Override
	protected void buttonPressed(int buttonId) {
		if (IDialogConstants.OK_ID == buttonId || IDialogConstants.YES_ID == buttonId) {
			okPressed();
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed();
		}
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}


}
