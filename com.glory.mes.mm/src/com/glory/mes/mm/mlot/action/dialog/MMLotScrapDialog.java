package com.glory.mes.mm.mlot.action.dialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.glory.mes.mm.model.Material;

import bsh.This;

public class MMLotScrapDialog extends MLotActionDialog {

	public static final String ADFORM_NAME = "MMLotScrapDialog";
	public static final String AUTHORITY = "MM.MLotActionScrap";
	
	private static final String FIELD_MLOTLIST = "mlotList";
	private static final String FIELD_SCRAP_ACTION = "scrapAction";
	private ListTableManagerField mlotListField;
	private EntityFormField scrapActionEntityForm;
	protected List<MLot> mlots = new ArrayList<MLot>();
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 410;
	
	public MMLotScrapDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots) {
		super(ADFORM_NAME, AUTHORITY, eventBroker);
		setmLotList(mlots);
		this.mlots = mlots;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		scrapActionEntityForm = form.getFieldByControlId(FIELD_SCRAP_ACTION, EntityFormField.class);
		scrapActionEntityForm.setValue(new MLotAction());
		scrapActionEntityForm.refresh();
		initLot();
	}

	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getmLotList())) {
			mlots = getmLotList();
		}
		try {
			List<MLot> inputMlots = new ArrayList<MLot>();
			if(CollectionUtils.isNotEmpty(mlots)) {
				// 查询批次已入库信息
				MMManager mmManager = Framework.getService(MMManager.class);
				inputMlots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), mlots);
			}
			mlotListField.getListTableManager().setInput(inputMlots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		try {
			if (scrapActionEntityForm.validate()) {
				MMManager mmManager = Framework.getService(MMManager.class);
				List<MLotAction> mlotActions = new ArrayList<MLotAction>();
				MLotAction mlotAction = (MLotAction)scrapActionEntityForm.getValue();
				mlotActions.add(mlotAction);
				MLot selectMLot =  (MLot) mlotListField.getListTableManager().getSelectedObject();
				if (selectMLot == null) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				}
				MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), selectMLot.getmLotId());
				selectMLot.setObjectRrn(mLot.getObjectRrn());
				List<MLot> mlots = Arrays.asList(selectMLot);
				for (MLot mlot : mlots) {
					if (mlotAction.getMainQty().compareTo(mlot.getMainQty()) == 1) {
						UI.showError(Message.getString("mm.qty_must_less_than_mainqty"));
						return;
					}
				}		
				if (Material.BATCH_TYPE_LOT.equals(mlots.get(0).getBatchType())) {
					mmManager.scrapMLot(this.mlots.get(0), mlotActions, Env.getSessionContext());
				} else {
					mmManager.scrapMLot(this.mlots.get(0), mlotActions, selectMLot.getTransWarehouseRrn(), selectMLot.getTransWarehouseId(), selectMLot.getTransStorageType(), selectMLot.getTransStorageId(), Env.getSessionContext());
				}
				UI.showInfo(Message.getString("wip.scraplot_success"));// 弹出提示框
				super.okPressed();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean checkMLotState(MLot mLot) {
		if (MLot.STATE_COM.equals(mLot.getComClass())) {
			return false;
		}
		return true;
	}
	
	@Override
	public boolean preValidate() {
		boolean flag = super.preValidate();
		if (flag) {
			for(MLot mLot : getmLotList()) {
				if(!checkMLotState(mLot)) {
					UI.showError(mLot.getmLotId() + Message.getString("mm.mlot_state_not_allow"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton ok = createSquareButton(parent, IDialogConstants.OK_ID,
				Message.getString(ExceptionBundle.bundle.CommonOk()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, "");
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
		
		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
		
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}
