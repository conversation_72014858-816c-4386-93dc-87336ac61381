package com.glory.mes.mm.mlot.carrier;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.mlot.CarrierMLotComposite;

/**
 * Attach/Detach/变更载具等处理
 * 支持多个物料批放到一个载具上，暂不支持一个物料放到多个载具上；三个动作在一个界面完成
 * 分批后，如果有载具的，父子批而在当前载具上
 * 合批后，沿用父批载具，如果子批有载具，则解除子批绑定
 * 
 * 界面显示为左右两个部分,左边为源载具(源批次)信息,右边为目标载具信息
 * 左边CarrierMLotComposite显示载具号，批号，isShowAll位false
 * 右边CarrierMLotComposite显示载具号
 */
public class MLotCarrierAssignComposite extends Composite {

	private static final Logger logger = Logger.getLogger(MLotCarrierAssignComposite.class);

	protected CarrierMLotComposite sourceCarrierMLotComposite;
	protected CarrierMLotComposite targetCarrierMLotComposite;
	
	public Button btnGo;
	public Button btnBack;

	public MLotCarrierAssignComposite(Composite parent, int style) {
		super(parent, style);
	}

	public void createForm() {
		try {
			GridLayout layout = new GridLayout(2, true);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
						
			createAssignComposite();
		} catch (Exception e) {
			logger.error("MLotCarrierAssignComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createAssignComposite() {
		createSourceComponent(this);
//		createMiddleComponent(this);
		createTargetComponent(this);
	}
	
	protected void createSourceComponent(final Composite parent) {
		Composite sourceComposite = new Composite(parent, SWT.NONE);
		sourceComposite.setLayout(new GridLayout());
		sourceComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		sourceCarrierMLotComposite = new CarrierMLotComposite(sourceComposite, SWT.NONE, true, true, false);
		sourceCarrierMLotComposite.createPartControl();
	}
	
	protected void createTargetComponent(final Composite parent) {        
		Composite targetComposite = new Composite(parent, SWT.NONE);
		targetComposite.setLayout(new GridLayout());
		targetComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		targetCarrierMLotComposite = new CarrierMLotComposite(targetComposite, SWT.NONE, true, false, false);
		targetCarrierMLotComposite.setLblCarrier(Message.getString("common.target") + Message.getString("wip.carrier_id"));
		targetCarrierMLotComposite.createPartControl();
	}
	
	protected void createMiddleComponent(final Composite parent) {
		Composite centerComposite = new Composite(parent, SWT.NONE);
	    final GridLayout buttonLayout = new GridLayout();
	    buttonLayout.marginWidth = 2;
	    buttonLayout.marginHeight = 2;
	    centerComposite.setLayout(buttonLayout);
		GridData buttonGd = new GridData(SWT.CENTER, SWT.CENTER, false, false);
		centerComposite.setLayoutData(buttonGd);
		
		btnGo = new Button(centerComposite, SWT.PUSH);
		btnGo.setText("->");
		btnGo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		btnGo.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				goAdapter();
			}
		});
		
		btnBack = new Button(centerComposite, SWT.PUSH);
		btnBack.setText("<-");
		btnBack.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		btnBack.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				backAdapter();
			}
		});
	}
	
	public void goAdapter() {
		List<MLot> sourceObjects = (List<MLot>)sourceCarrierMLotComposite.getMLotTableManager().getInput();
		List<MLot> targetObjects = (List<MLot>)targetCarrierMLotComposite.getMLotTableManager().getInput();
		
		List<Object> sourceCheckedObjects = sourceCarrierMLotComposite.getMLotTableManager().getCheckedObject();
		if (sourceCheckedObjects != null) {
			for (Object sourceCheckedObject : sourceCheckedObjects) {
				targetObjects.add((MLot)sourceCheckedObject);
				sourceObjects.remove((MLot)sourceCheckedObject);
			}
		}
		
		sourceCarrierMLotComposite.getMLotTableManager().setInput(sourceObjects);
		targetCarrierMLotComposite.getMLotTableManager().setInput(targetObjects);
	}
	
	public void backAdapter() {
		List<MLot> sourceObjects = (List<MLot>)sourceCarrierMLotComposite.getMLotTableManager().getInput();
		List<MLot> targetObjects = (List<MLot>)targetCarrierMLotComposite.getMLotTableManager().getInput();
		
		List<Object> targetCheckedObjects = targetCarrierMLotComposite.getMLotTableManager().getCheckedObject();
		if (targetCheckedObjects != null) {
			for (Object targetCheckedObject : targetCheckedObjects) {
				sourceObjects.add((MLot)targetCheckedObject);
				targetObjects.remove((MLot)targetCheckedObject);
			}
		}
		
		sourceCarrierMLotComposite.getMLotTableManager().setInput(sourceObjects);
		targetCarrierMLotComposite.getMLotTableManager().setInput(targetObjects);
	}

	public CarrierMLotComposite getSourceCarrierMLotComposite() {
		return sourceCarrierMLotComposite;
	}

	public void setSourceCarrierMLotComposite(CarrierMLotComposite sourceCarrierMLotComposite) {
		this.sourceCarrierMLotComposite = sourceCarrierMLotComposite;
	}

	public CarrierMLotComposite getTargetCarrierMLotComposite() {
		return targetCarrierMLotComposite;
	}

	public void setTargetCarrierMLotComposite(CarrierMLotComposite targetCarrierMLotComposite) {
		this.targetCarrierMLotComposite = targetCarrierMLotComposite;
	}
	
} 
