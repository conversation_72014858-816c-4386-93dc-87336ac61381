package com.glory.mes.mm.mlot.receive;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.SummaryListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.framework.core.exception.ExceptionBundle;

public class ReceiveMLotSection extends EntitySection {

    public static final String TABLE_NAME = "MMMLotReceive";

    public SummaryListTableManager manager;

    protected List<MLot> mlots = new ArrayList<MLot>();

    private ToolItem itemReceive;

    public ReceiveMLotSection() {
        super();
    }

    public ReceiveMLotSection(ADTable table) {
        super(table);
    }
    
    @Override
    public void createContents(IManagedForm form, Composite parent) {
        super.createContents(form, parent);
        initAdObject();
    }

    protected void initAdObject() {
        MLot mlot = new MLot();
        mlot.setMainQty(null);
        setAdObject(mlot);
        refresh();
    }

    @Override
    protected void createSectionContent(Composite client) {
        try {
            final FormToolkit toolkit = form.getToolkit();
            mmng = form.getMessageManager();
            super.createSectionContent(client);

            Composite btnComposite = toolkit.createComposite(client, SWT.NONE);
            GridLayout layoutBtn = new GridLayout(3, false);
            btnComposite.setLayout(layoutBtn);
            GridData gd1 = new GridData(GridData.FILL_BOTH);
            gd1.horizontalAlignment = SWT.RIGHT;
            btnComposite.setLayoutData(gd1);
            SquareButton add = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonAdd()),
            		"DEFAULT");
            add.addSelectionListener(new SelectionAdapter() {
				@Override
                public void widgetSelected(SelectionEvent e) {
                	addAdapter();
                }
            });

            SquareButton delete = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonDelete()), "DEFAULT");
            delete.addSelectionListener(new SelectionAdapter() {
            	@Override
            	public void widgetSelected(SelectionEvent e) {
            		delAdapter();
            	}
			});
            
            Composite bodyComposite = toolkit.createComposite(client, SWT.NONE);
            bodyComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
            GridLayout layoutBody = new GridLayout(1, true);
            bodyComposite.setLayout(layoutBody);
            GridData gdLeftLest = new GridData(GridData.FILL_BOTH);
            Composite parameterCompLeft = toolkit.createComposite(bodyComposite, SWT.NONE);
            gdLeftLest.heightHint = 500;
            parameterCompLeft.setLayoutData(gdLeftLest);
            GridLayout layoutLeft = new GridLayout(1, true);
            parameterCompLeft.setLayout(layoutLeft);
            ADManager adManager = (ADManager) Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
            String[] summaryColumns = {"mainQty", "subQty", "transMainQty", "transSubQty"};
            manager = new SummaryListTableManager(adTable, summaryColumns);
            manager.newViewer(parameterCompLeft);
            manager.addDoubleClickListener(new IMouseAction() {
                @Override
                public void run(NatTable natTable, MouseEvent event) {
                    MLot mlot = (MLot) manager.getSelectedObject();
                    mlots.remove(mlot);
                    manager.getInput().remove(mlot);
                    manager.refresh();
                    initAdObject();
                }
            });

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }

    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemReceive(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    protected void createToolItemReceive(ToolBar tBar) {
        itemReceive = new ToolItem(tBar, SWT.PUSH);
        itemReceive.setText(Message.getString("common.receive"));
        itemReceive.setImage(SWTResourceCache.getImage("currentstep"));
        itemReceive.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                receiveLotAdapter();
            }
        });
    }

    protected void addAdapter() {
    	 try {
    		 form.getMessageManager().removeAllMessages();
             if (getAdObject() != null) {
                 boolean saveFlag = true;
                 for (IForm detailForm : getDetailForms()) {
                     if (!detailForm.saveToObject()) {
                         saveFlag = false;
                     }
                 }
                 if (saveFlag) {
                     for (IForm detailForm : getDetailForms()) {
                         PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
                                 detailForm.getCopyProperties());
                     }
                     MLot mlot = (MLot) getAdObject();
                     if (BigDecimal.ZERO.compareTo(mlot.getTransMainQty()) == -1) {
                         mlots.add(mlot);
                         initAdObject();
                         manager.setInput(mlots);
                         manager.refresh();
                     } else {
                         UI.showInfo(Message.getString("mm.mlot_receive_must_more_than.zero"));
                         return;
                     }
                 }
             }
         } catch (Exception e1) {
             e1.printStackTrace();
             ExceptionHandlerManager.asyncHandleException(e1);
             return;
         }
    }
    
    
    protected void delAdapter() {
    	if(manager.getInput().size() > 0 ) {
        	manager.setInput(new ArrayList<>());
        	mlots.clear();
    	}
    }
    
    @SuppressWarnings("unchecked")
    protected void receiveLotAdapter() {
        try {
            List<Object> objects = (List<Object>) manager.getInput();
            if (objects == null || objects.size() == 0) {
                UI.showInfo(Message.getString("mm.please_add_mlot"));
                return;
            }
            MMManager mmManager = Framework.getService(MMManager.class);
            for (Object object : objects) {
                MLot mlot = (MLot) object;
//                mlot.setBatchType("L");   这里注释掉, 是为了根据物料名称来 动态设值
                MLotAction mLotAction = new MLotAction();
                mmManager.receiveMLot2Warehouse(mlot, mLotAction, mlot.getTransWarehouseRrn(), null, mlot.getTransStorageType(), mlot.getTransStorageId(), Env.getSessionContext());
            }
            UI.showInfo(Message.getString("common.receive.success"));
            refresh();
            manager.setInput(new ArrayList<MLot>());
            mlots.clear();
            manager.refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }
}
