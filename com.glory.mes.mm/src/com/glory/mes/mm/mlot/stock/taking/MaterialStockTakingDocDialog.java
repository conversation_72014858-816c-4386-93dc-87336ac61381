package com.glory.mes.mm.mlot.stock.taking;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.common.doc.client.WFDocManager;
import com.glory.common.doc.model.WfDoc;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotSum;
import com.glory.mes.mm.model.MaterialStockTaking;
import com.glory.mes.mm.model.MaterialStockTakingDetail;
import com.glory.mes.mm.model.MaterialSum.GROUPBY;
import com.glory.mes.wip.client.MLotManager;
import com.google.common.collect.Lists;

public class MaterialStockTakingDocDialog extends GlcBaseDialog { 

	private static final String FIELD_MATERIALSTOCKTAKING = "materialStockTaking";
	private static final String FIELD_MATERIALSTOCKTAKINGLINE = "materialStockTakingLine";

	public static final String FIELD_MATERIAL_NAME = "materialName";
	public static final String FIELD_WAREHOUSE_ID = "warehouseRrn";
	public static final String FIELD_MLOT_ID = "mLotId";
	
	protected QueryFormField materialStockTakingField;
	protected ListTableManagerField materialStorageField;
	protected EntityFormField materialStockTakingLineField;
	
	protected MaterialStockTaking materialStockTaking;
	protected List<MaterialStockTakingDetail> materialStockTakingDetails;
	protected List<Object> queryInputs;

	public MaterialStockTakingDocDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		materialStockTakingField = form.getFieldByControlId(FIELD_MATERIALSTOCKTAKING, QueryFormField.class);
		materialStockTakingLineField = form.getFieldByControlId(FIELD_MATERIALSTOCKTAKINGLINE, EntityFormField.class);

		subscribeAndExecute(eventBroker, materialStockTakingField.getFullTopic(GlcEvent.EVENT_QUERY), this::materialStockTakingQuery);
	}

	private void materialStockTakingQuery(Object object) {
		try {
			Map<String,String> whereClauseMap = new HashMap<String,String>();
			LinkedHashMap fields = materialStockTakingField.getQueryForm().getFields();
			List<ADField> listFields = materialStockTakingField.getQueryForm().getTableManager().getADTable().getFields();
			String mLotId = "";
			String materialName = "";
			String warehouseId = "";
			for (Object f : fields.values()) {//并取出输入数据,若输入值不为空，则传递到后台进行查询
				IField field= (IField) f;
				Object t = field.getValue();
				if(t != null) {
					if(!"".equals(t.toString())) {
						if (field.getId().equals(FIELD_MATERIAL_NAME)) {
							materialName = t.toString();
						} else if (field.getId().equals(FIELD_WAREHOUSE_ID)) {
							warehouseId = t.toString();
						} else if (field.getId().equals(FIELD_MLOT_ID)) {
							mLotId = t.toString();
						}
					}
				}
			}
			
			IField warehouseField = materialStockTakingField.getQueryForm().getFields().get(FIELD_WAREHOUSE_ID);
			Object warehouseRrn = warehouseField.getValue();
			if(warehouseRrn != null) {
			}else {
				UI.showWarning(Message.getString("error.no_warehouse_input"));
				return;
			}
			
			MMManager mmManager = Framework.getService(MMManager.class);
			List<MLotSum> list = mmManager.getMLotSumByLike(Env.getOrgRrn(), warehouseId, "", "", materialName, mLotId, GROUPBY.WAREHOUSE);
			materialStockTakingField.getQueryForm().getTableManager().setInput(list);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		try {
			if (materialStockTakingLineField != null) {
				if (!materialStockTakingLineField.validate()) {
					return;
				}
				materialStockTaking = (MaterialStockTaking)materialStockTakingLineField.getValue();
			}
			
			List<MaterialStockTakingDetail> details = Lists.newArrayList();
			List<Object> objects =  ((CheckBoxTableViewerManager)materialStockTakingField.getQueryForm().getTableManager()).getCheckedObject();
			for(Object object :objects) {
				if(object != null) {
					MaterialStockTakingDetail detail = new MaterialStockTakingDetail();
					MLotSum mlot = (MLotSum) object;
					detail.setMaterialName(mlot.getMaterialName());
					detail.setMaterialDesc(mlot.getMaterialDesc());
					detail.setMaterialRrn(mlot.getMaterialRrn());
					detail.setMaterialVersion(mlot.getMaterialVerison());
					detail.setMaterialType(mlot.getMaterialType());
					detail.setUomId(mlot.getUomId());
					
					detail.setTransWarehouseRrn(mlot.getWarehouseRrn());
					detail.setTransWarehouseId(mlot.getWarehouseId());
					detail.setTransStorageId(mlot.getStorageId());
					detail.setTransStorageType(mlot.getStorageType());
					detail.setmLotId(mlot.getmLotId());
					details.add(detail);
				}
			}
			
			MMManager mmManager = Framework.getService(MMManager.class);
			WFDocManager docManager = Framework.getService(WFDocManager.class);
			MaterialStockTaking stockTaking = new MaterialStockTaking();
			Warehouse warehouse = new Warehouse();
			warehouse.setOrgRrn(Env.getOrgRrn());
			warehouse.setWarehouseId(details.get(0).getTransWarehouseId());
			long wareHouseRrn = mmManager.getWarehouse(warehouse).getObjectRrn();
			stockTaking.setDocType("MLOTSTOCKTAKING");
			stockTaking.setStockTakingDate(materialStockTaking.getStockTakingDate());
			stockTaking.setStockTakingUser(materialStockTaking.getStockTakingUser());
			stockTaking.setWarehouseId(details.get(0).getTransWarehouseId());
			stockTaking.setWarehouseRrn(wareHouseRrn);
			stockTaking.setOwner(Env.getUserName());
			stockTaking.setLines(Lists.newArrayList());
			stockTaking.setDocStatus(WfDoc.STATUS_CREATED);
			stockTaking.setDocLots((List)details);
			docManager.saveDoc((WfDoc)stockTaking, false, false, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		super.okPressed();
	}

}