package com.glory.mes.mm.mlot.stock.taking;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.config.CellConfigAttributes;
import org.eclipse.nebula.widgets.nattable.config.IConfigRegistry;
import org.eclipse.nebula.widgets.nattable.config.IEditableRule;
import org.eclipse.nebula.widgets.nattable.edit.EditConfigAttributes;
import org.eclipse.nebula.widgets.nattable.layer.cell.ColumnOverrideLabelAccumulator;
import org.eclipse.nebula.widgets.nattable.layer.cell.ILayerCell;
import org.eclipse.nebula.widgets.nattable.painter.cell.TextPainter;
import org.eclipse.nebula.widgets.nattable.painter.cell.decorator.LineBorderDecorator;
import org.eclipse.nebula.widgets.nattable.painter.cell.decorator.PaddingDecorator;
import org.eclipse.nebula.widgets.nattable.style.BorderStyle;
import org.eclipse.nebula.widgets.nattable.style.CellStyleAttributes;
import org.eclipse.nebula.widgets.nattable.style.DisplayMode;
import org.eclipse.nebula.widgets.nattable.style.HorizontalAlignmentEnum;
import org.eclipse.nebula.widgets.nattable.style.Style;
import org.eclipse.nebula.widgets.nattable.style.BorderStyle.LineStyleEnum;
import org.eclipse.nebula.widgets.nattable.util.GUIHelper;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.common.doc.client.WFDocManager;
import com.glory.common.doc.model.WfDoc;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.excel.download.Download;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.nattable.editor.FixTextCellEditor;
import com.glory.framework.base.ui.nattable.editor.FixTextPaint;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.MaterialStockTaking;
import com.glory.mes.mm.model.MaterialStockTakingDetail;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class MaterialStockTakingManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.mlot.stock.taking.MaterialStockTakingManagerEditor";

	private static final String FIELD_MATERIALSTOCKTAKING = "materialStockTaking";
	private static final String FIELD_MATERIALSTOCKTAKINGDETAIL = "materialStockTakingDetail";

	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_EDIT = "edit";
	private static final String BUTTON_APPROVE = "approve";
	private static final String BUTTON_UNAPPROVE = "unapprove";
	private static final String BUTTON_COMPLETE = "complete";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_EXPORT = "export";
	private static final String BUTTON_REFRESH = "refresh";

	private ToolItem itemNew;
	private ToolItem itemEdit;
	private ToolItem itemApprove;
	private ToolItem itemUnApprove;
	private ToolItem itemComplete;
	private ToolItem itemDelete;
	private ToolItem itemExport;
	private ToolItem itemRefresh;
	
	protected QueryFormField materialStockTakingField;
	protected ListTableManagerField materialStockTakingDetailField;
	
	public BorderStyle textBorderStyle = new BorderStyle(1, GUIHelper.getColor(184, 191, 239), LineStyleEnum.SOLID);

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		materialStockTakingField = form.getFieldByControlId(FIELD_MATERIALSTOCKTAKING, QueryFormField.class);
		materialStockTakingDetailField = form.getFieldByControlId(FIELD_MATERIALSTOCKTAKINGDETAIL, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, materialStockTakingField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::materialStockTakingSelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EDIT), this::editAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_APPROVE), this::approveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNAPPROVE), this::unapproveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_COMPLETE), this::completeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EXPORT), this::exportAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		init();
	}
	
	private void init() {
		materialStockTakingDetailField.refresh();
		itemNew = (ToolItem) form.getButtonByControl(null, BUTTON_NEW);
		itemEdit = (ToolItem) form.getButtonByControl(null, BUTTON_EDIT);
		itemApprove = (ToolItem) form.getButtonByControl(null, BUTTON_APPROVE);
		itemUnApprove = (ToolItem) form.getButtonByControl(null, BUTTON_UNAPPROVE);
		itemComplete = (ToolItem) form.getButtonByControl(null, BUTTON_COMPLETE);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
		itemExport = (ToolItem) form.getButtonByControl(null, BUTTON_EXPORT);
		itemRefresh = (ToolItem) form.getButtonByControl(null, BUTTON_REFRESH);
		statusChanged(null);
	}

	private void newAdapter(Object object) {
		try {
			MaterialStockTakingDocDialog dialog = new MaterialStockTakingDocDialog("MaterialStockTakingDoc", form.getAuthority(), eventBroker);
			if(Dialog.OK == dialog.open()) {
				UI.showInfo(Message.getString("wms.stock_taking_create_secuss"));
				materialStockTakingField.refresh();
				materialStockTakingDetailField.getListTableManager().setInput(Lists.newArrayList());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void editAdapter(Object object) {
		try {
			WFDocManager docManager = Framework.getService(WFDocManager.class);
			MaterialStockTaking stockTaking = (MaterialStockTaking) materialStockTakingField.getSelectedObject();
			List<MaterialStockTakingDetail> takingDetails = (List<MaterialStockTakingDetail>) materialStockTakingDetailField.getListTableManager().getInput();
			List<MaterialStockTakingDetail> details = Lists.newArrayList();
			details.addAll(takingDetails);
			stockTaking.setDocLots((List)details);
			docManager.saveDoc(stockTaking, false, false, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
			materialStockTakingField.refresh();
			materialStockTakingDetailField.getListTableManager().setInput(Lists.newArrayList());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void approveAdapter(Object object) {
		try {
			MaterialStockTaking stockTaking = (MaterialStockTaking) materialStockTakingField.getSelectedObject();
			if (stockTaking != null && stockTaking.getObjectRrn() != null) {
				WFDocManager docManager = Framework.getService(WFDocManager.class);
				stockTaking = (MaterialStockTaking) docManager.approveDoc(stockTaking, null, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonApproveSuccessed()));// 弹出提示框
				materialStockTakingField.getQueryForm().getTableManager().update(stockTaking);
				statusChanged(stockTaking.getDocStatus());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void unapproveAdapter(Object object) {
		try {
			MaterialStockTaking stockTaking = (MaterialStockTaking) materialStockTakingField.getSelectedObject();
			if (stockTaking != null && stockTaking.getObjectRrn() != null) {
				WFDocManager docManager = Framework.getService(WFDocManager.class);
				stockTaking = (MaterialStockTaking) docManager.unApproveDoc(stockTaking, null, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonUnApproveSuccessed()));// 弹出提示框
				materialStockTakingField.getQueryForm().getTableManager().update(stockTaking);
				statusChanged(stockTaking.getDocStatus());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void completeAdapter(Object object) {
		try {
			MMManager manager = Framework.getService(MMManager.class);
			MaterialStockTaking stockTaking = (MaterialStockTaking) materialStockTakingField.getSelectedObject();
			if (stockTaking != null && stockTaking.getObjectRrn() != null) {
				List<MaterialStockTakingDetail> takingDetails = (List<MaterialStockTakingDetail>) materialStockTakingDetailField.getListTableManager().getInput();
				List<MaterialStockTakingDetail> details = Lists.newArrayList();
				details.addAll(takingDetails);
				stockTaking = manager.completeStockTaking(stockTaking, details, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonCompleteSuccessed()));
				materialStockTakingField.getQueryForm().getTableManager().update(stockTaking);
				statusChanged(stockTaking.getDocStatus());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void deleteAdapter(Object object) {
		try {
			MaterialStockTaking stockTaking = (MaterialStockTaking) materialStockTakingField.getSelectedObject();
			if (stockTaking == null) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
			WFDocManager docManager = Framework.getService(WFDocManager.class);
			if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()))) {
				docManager.deleteDoc((WfDoc) stockTaking, true, true, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				materialStockTakingField.getQueryForm().getTableManager().remove(stockTaking);
				materialStockTakingDetailField.getListTableManager().setInput(Lists.newArrayList());
				statusChanged(null);
			}
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void exportAdapter(Object object) {
		try {
			List objects = Lists.newArrayList(materialStockTakingField.getQueryForm().getTableManager().getMultiSelectedObjects());
			ADManager adManager = Framework.getService(ADManager.class);
			if (CollectionUtils.isNotEmpty(objects)) {
				for (Object o : objects) {
					MaterialStockTaking stockTaking = (MaterialStockTaking) o;
					List<MaterialStockTakingDetail> takingDetails = adManager.getEntityList(Env.getOrgRrn(), MaterialStockTakingDetail.class, Integer.MAX_VALUE, "docRrn = " + stockTaking.getObjectRrn(), "");
					stockTaking.setDocLots((List)takingDetails);
				}
				
				Download download = new Download(form.getAuthority(), BUTTON_EXPORT);
				if (download.getDownloadProgress().init()) {
					download.setIsQueryObject(false);
					download.run(objects);
				}
			} else {
				DefaultDownloadWriter.exportTemplate(form.getAuthority(), BUTTON_EXPORT);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void refreshAdapter(Object object) {
		try {
			materialStockTakingField.refresh();
			materialStockTakingDetailField.getListTableManager().setInput(Lists.newArrayList());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void materialStockTakingSelectionChanged(Object object) {
		try {
			MaterialStockTaking stockTaking = (MaterialStockTaking) materialStockTakingField.getSelectedObject();
			if (stockTaking != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				MMManager mmManager = Framework.getService(MMManager.class);
				List<MaterialStockTakingDetail> takingDetails = adManager.getEntityList(Env.getOrgRrn(), MaterialStockTakingDetail.class, Integer.MAX_VALUE, "docRrn = " + stockTaking.getObjectRrn(), "");
				takingDetails.forEach(detail -> {
					MLot mlot = mmManager.getMLotByMLotId(Env.getOrgRrn(), detail.getmLotId());
					if(mlot != null) {
						detail.setStockMainQty(mlot.getMainQty());
					}
				});
				materialStockTakingDetailField.getListTableManager().setInput(takingDetails);
				statusChanged(stockTaking.getDocStatus());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void statusChanged(String newStatus) {
		if (Documentation.STATUS_CREATED.equals(newStatus)) {
			itemNew.setEnabled(true);
			itemEdit.setEnabled(true);
			itemApprove.setEnabled(true);
			itemUnApprove.setEnabled(false);
			itemComplete.setEnabled(false);
			itemDelete.setEnabled(true);
			itemExport.setEnabled(true);
			itemRefresh.setEnabled(true);
		} else if (Documentation.STATUS_APPROVED.equals(newStatus)) {
			itemNew.setEnabled(true);
			itemEdit.setEnabled(false);
			itemApprove.setEnabled(false);
			itemUnApprove.setEnabled(true);
			itemComplete.setEnabled(true);
			itemDelete.setEnabled(false);
			itemExport.setEnabled(true);
			itemRefresh.setEnabled(true);
		} else if (Documentation.STATUS_COMPLETED.equals(newStatus)) {
			itemNew.setEnabled(true);
			itemEdit.setEnabled(false);
			itemApprove.setEnabled(false);
			itemUnApprove.setEnabled(false);
			itemComplete.setEnabled(false);
			itemDelete.setEnabled(false);
			itemExport.setEnabled(true);
			itemRefresh.setEnabled(true);
		} else {
			itemNew.setEnabled(true);
			itemEdit.setEnabled(false);
			itemApprove.setEnabled(false);
			itemUnApprove.setEnabled(false);
			itemComplete.setEnabled(false);
			itemDelete.setEnabled(false);
			itemExport.setEnabled(true);
			itemRefresh.setEnabled(true);
		} 
		
		if (Documentation.STATUS_CREATED.equals(newStatus)) {
			registerText(materialStockTakingDetailField.getListTableManager().getNatTable().getConfigRegistry(), false);
		} else {
			registerText(materialStockTakingDetailField.getListTableManager().getNatTable().getConfigRegistry(), true);
		}
		((CheckBoxFixEditorTableManager)materialStockTakingDetailField.getListTableManager().getTableManager()).registerFirstCheckBox(materialStockTakingDetailField.getListTableManager().getNatTable().getConfigRegistry());
		((CheckBoxFixEditorTableManager)materialStockTakingDetailField.getListTableManager().getTableManager()).refresh();
	}
	
	public void registerText(IConfigRegistry configRegistry, Boolean isUnregisterConfigAttribute) {
		int i = 0;
		for (final ADField field : materialStockTakingDetailField.getListTableManager().getADTable().getFields()) {
			if (isUnregisterConfigAttribute) {
				configRegistry.unregisterConfigAttribute(EditConfigAttributes.CELL_EDITABLE_RULE,DisplayMode.EDIT, "COLUMN_" + i);
				configRegistry.unregisterConfigAttribute(CellConfigAttributes.CELL_STYLE, DisplayMode.NORMAL, "COLUMN_" + i);
				configRegistry.unregisterConfigAttribute(CellConfigAttributes.CELL_PAINTER, DisplayMode.NORMAL, "COLUMN_" + i);
			} else {
        		if (field.getIsMain() && field.getIsDisplay()){
        			if (FieldType.TEXT.equals(field.getDisplayType())) {
        				if (field.getIsEditable()) {

            				configRegistry.registerConfigAttribute(CellConfigAttributes.CELL_PAINTER, 
            							new PaddingDecorator(
            								new LineBorderDecorator(
            										createTextPaint(field.getName()), textBorderStyle),1), DisplayMode.NORMAL, "COLUMN_" + i);
            				
            				((ColumnOverrideLabelAccumulator)materialStockTakingDetailField.getListTableManager().getTableManager().getLayer().getBodyDataLayer().getConfigLabelAccumulator()).registerColumnOverrides(i, "COLUMN_" + i);
            				FixTextCellEditor cellEditor = new FixTextCellEditor(materialStockTakingDetailField.getListTableManager().getNatTable(), true);
            				cellEditor.registerVerifyListener(new VerifyListener() {
								@Override
								public void verifyText(VerifyEvent e) {
									e.doit = false; 
									e.data = field;
									if (validate(e)) {
										e.doit = true;
										return;
									}
								}
		        			});
            				cellEditor.registerModifyListener(new ModifyListener() {
								@Override
								public void modifyText(ModifyEvent e) {
									textModify((Text)e.widget);
								}
		        			});
            				
            				cellEditor.registerCRListener((FixEditorTableManager)materialStockTakingDetailField.getListTableManager().getTableManager());
            				
            				Style cellStyle = new Style();
            				cellStyle.setAttributeValue(CellStyleAttributes.HORIZONTAL_ALIGNMENT, HorizontalAlignmentEnum.RIGHT);
            				configRegistry.registerConfigAttribute(CellConfigAttributes.CELL_STYLE, cellStyle, DisplayMode.NORMAL, "COLUMN_" + i);
            				
            				configRegistry.registerConfigAttribute(EditConfigAttributes.CELL_EDITOR, cellEditor, DisplayMode.EDIT, "COLUMN_" + i);
        					configRegistry.registerConfigAttribute(EditConfigAttributes.CELL_EDITABLE_RULE, IEditableRule.ALWAYS_EDITABLE, DisplayMode.EDIT, "COLUMN_" + i);
        				}
        			}
        		}
			}
			i++;
		};
	}
	
	public TextPainter createTextPaint(String fieldName) {
		return new FixTextPaint();
	}
	
	public boolean validate(VerifyEvent event) {
    	char myChar = event.character;
    	if (Character.isDigit(myChar) || myChar < 32 || myChar == 127) {
			return true;
		}
    	Text text = (Text) event.widget;
    	ADField adField = (ADField)event.data;
    	String dataType = adField.getDataType();
    	if (dataType != null && dataType.trim().length() > 0){
    		if (DataType.INTEGER.equalsIgnoreCase(dataType)) {
    			if (adField.getMinValue() != null && adField.getMinValue().trim().length() > 0) {
    				try {
    					BigDecimal minValue = new BigDecimal(adField.getMinValue().trim());
    					if (minValue.compareTo(BigDecimal.ZERO) >= 0) {
    						return false;
    					}
    				} catch (Exception e) {
    				}
    			}
    			//非数字或者负号
    			if (myChar == '-') {
    				if (text.getText().indexOf('-') == -1 && text.getCaretPosition() == 0) {
    					return true;
    				}
    			}
			} else if (DataType.DOUBLE.equalsIgnoreCase(dataType)) {
				//非数字或者负号或小数点
				if (myChar == '.') {
					if (text.getText().indexOf('.') == -1) {
						return true;
					}
				}
				if (adField.getMinValue() != null && adField.getMinValue().trim().length() > 0) {
    				try {
    					BigDecimal minValue = new BigDecimal(adField.getMinValue().trim());
    					if (minValue.compareTo(BigDecimal.ZERO) >= 0) {
    						return false;
    					}
    				} catch (Exception e) {
    				}
    			}
				
				if (myChar == '-') {
					if (text.getText().indexOf('-') == -1 && text.getCaretPosition() == 0) {
						return true;
					}
				}
			} else {
				return true;
			}
    	} else {
    		return true;
    	}
    	return false;
    }
	
	public void textModify(Text text) {
		if (text.getData(FixTextCellEditor.TEXTDATA_CELL) != null) {
			ILayerCell layerCell = (ILayerCell)text.getData(FixTextCellEditor.TEXTDATA_CELL);
			
			int columnIndex = layerCell.getColumnIndex();
			int rowIndex = layerCell.getRowIndex();
			
			materialStockTakingDetailField.getListTableManager().getTableManager().getLayer().getBodyDataProvider().setDataValue(columnIndex, rowIndex, text.getText().trim());
		}
	}
}