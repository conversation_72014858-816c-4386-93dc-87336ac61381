package com.glory.mes.mm.mlot.unscrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotScrap;
import com.glory.mes.mm.mlot.MLotSection;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.ProcessUnit;

public class MLotUnScrapSection extends MLotSection {
    private static final Logger logger = Logger.getLogger(EntitySection.class);

    protected ToolItem itemUnscrap;
    protected MLotUnScrapForm itemForm;

    public MLotUnScrapSection() {
        super();
    }

    public MLotUnScrapSection(ADTable table) {
        super(table);
    }

    @Override
    public void createContents(IManagedForm form, Composite parent) {
        super.createContents(form, parent);
        section.setText(Message.getString("wip.unscrap_sectiontitle"));
        initAdObject();
    }

    public void initAdObject() {
        setAdObject(new MLot());
        refresh();
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemUnScrap(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    protected void createToolItemUnScrap(ToolBar tBar) {
        itemUnscrap = new ToolItem(tBar, SWT.PUSH);
        itemUnscrap.setText(Message.getString("wip.unscrap_lot_by_scrap"));
        itemUnscrap.setImage(SWTResourceCache.getImage("unscrap"));
        itemUnscrap.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                unScrapAdapter(event);
            }
        });
    }

    protected void unScrapAdapter(SelectionEvent event) {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    for (IForm detailForm : getDetailForms()) {
                        if (detailForm instanceof EntityForm) {
                            ADManager adManager = Framework.getService(ADManager.class);
                            MLot mlot = (MLot) this.getAdObject();
                            Boolean isUnScrapComponent = true;
                        	if (ProcessUnit.UNIT_TYPE_QTY.equals(mlot.getSubUnitType())) {
                        		isUnScrapComponent = false;
							}
                            if (mlot != null && mlot.getObjectRrn() != null) {
                             // 检查批次是否有库存
//                                List<MLotStorage> storages = mmManager.getLotStorages(mlot.getObjectRrn());
//                                if ( storages== null || storages.size() == 0) {
//                                    UI.showError(Message.getString("mm.mlot_must_be_received"));
//                                    return;
//                                }
                            	
                            	//查询报废数据
                            	List<MLotScrap> mLotScraps = adManager.getEntityList(Env.getOrgRrn(), MLotScrap.class, Integer.MAX_VALUE, 
                            			"mLotRrn = '"+mlot.getObjectRrn()+"'", ""); 
                            	if (CollectionUtils.isEmpty(mLotScraps)) {
									UI.showInfo(Message.getString("mm.mlot_scarp_record_not_found"));
									return;
								}
                            	for (MLotScrap mLotScrap : mLotScraps) {
                            		if (isUnScrapComponent) {
            							MComponentUnit componentUnit = new MComponentUnit();
            							componentUnit.setObjectRrn(mLotScrap.getmComponentRrn());
            							componentUnit = (MComponentUnit) adManager.getEntity(componentUnit);
            							// 片当前物料批RRN
            							mLotScrap.setAttribute1(componentUnit.getParentMLotRrn());
            							// 带入原position
            							mLotScrap.setAttribute2(componentUnit.getPosition());
            						} else {
            							// 物料批RRN
            							mLotScrap.setAttribute1(mLotScrap.getmLotRrn());
            						}
                            		mLotScrap.setSubQty(mLotScrap.getSubQty() == null ? BigDecimal.ZERO : mLotScrap.getSubQty());
            						// 反报废数量
                            		mLotScrap.setUnScrapMainQty(mLotScrap.getMainQty());
                            		mLotScrap.setUnScrapSubQty(mLotScrap.getSubQty());
            						// 反报废代码
                            		mLotScrap.setUnScrapCode("");
            						// 备注
                            		mLotScrap.setUnScrapComment("");
                            	}
                            	
                            	MLotUnScrapDialog dialog = new MLotUnScrapDialog(UI.getActiveShell(), mlot, mLotScraps,isUnScrapComponent, getADManger());
                                if (Dialog.OK == dialog.open()) {
                                }
                                refresh();
                            }
                        }
                    }
                }
            }
            txtLot.setFocus();
        } catch (Exception e) {
            logger.error("HoldSection : holdAdapter()", e);
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

//    @Override
//	protected IForm getForm(Composite composite, ADTab tab) {
//        itemForm = new MLotUnScrapForm(composite, SWT.NONE, tab, mmng);
//        return itemForm;
//    }

    @Override
    public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);
		ADBase adObject = getAdObject();
		if (adObject != null && adObject.getObjectRrn() != null) {
			MLot mLot = (MLot) adObject;
			
			if (MLot.HOLDSTATE_ON.equals(mLot.getHoldState())) {
				itemUnscrap.setEnabled(false);
			} else {
				if (newStatus != null) {
					itemUnscrap.setEnabled(true);
		        } else {
		        	itemUnscrap.setEnabled(false);
		        }
			}
		}
    }

    @Override
    public void refresh() {
        try {
            ADBase adBase = getAdObject();
            if (adBase != null && adBase.getObjectRrn() != null) {
                ADManager entityManager = Framework.getService(ADManager.class);
                setAdObject(entityManager.getEntity(adBase));
            }
            form.getMessageManager().removeAllMessages();
        } catch (Exception e1) {
            ExceptionHandlerManager.asyncHandleException(e1);
            return;
        }
        super.refresh();
    }

}
