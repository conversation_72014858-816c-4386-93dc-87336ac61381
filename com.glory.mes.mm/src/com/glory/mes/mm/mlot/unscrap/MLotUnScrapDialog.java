package com.glory.mes.mm.mlot.unscrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotScrap;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Maps;

public class MLotUnScrapDialog extends BaseTitleDialog {
	
	public static final String TABLE_NAME_LOT = "MMMLotUnScrapEdit";
	public static final String TABLE_NAME_COMP = "MMMComponentUnScrapEdit";
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 420;

	private ADManager adManager;
	private List<MLotScrap> mLotScraps;
	private CheckBoxFixEditorTableManager tableManager;
	
	protected XCombo combo;
	protected Text commentText,reasonTest;
	protected MLot mLot;
	protected boolean isExternal;
	protected SquareButton externalSplit;
	protected SquareButton ok;
	protected boolean isUnScrapComponent;
    
    public MLotUnScrapDialog(Shell parentShell, MLot mLot, List<MLotScrap> mLotScraps,boolean isUnScrapComponent, ADManager adManager) {
		this(parentShell, mLot, mLotScraps,isUnScrapComponent, adManager, false);
	}

	public MLotUnScrapDialog(Shell parentShell, MLot mLot, List<MLotScrap> mLotScraps, boolean isUnScrapComponent, ADManager adManager, boolean isExternal) {
		super(parentShell);
		this.mLot = mLot;
		this.mLotScraps = mLotScraps;
		this.adManager = adManager;
		this.isExternal = isExternal;
		this.isUnScrapComponent = isUnScrapComponent;
	}

	@Override
	protected Control buildView(Composite parent) {
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		setTitleImage(SWTResourceCache.getImage("common-dialog"));
		
		setTitle(Message.getString("wip.lotHis_UnScrap"));
		setMessage(Message.getString("wip.unscrap_qty_input"));
		

		Composite content = toolkit.createComposite(parent);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setLayout(new GridLayout(1, false));
		
		tableManager = new CheckBoxFixEditorTableManager(getTable());
		tableManager.newViewer(content);
		tableManager.setInput(mLotScraps);
		
		tableManager.addICheckChangedListener(new ICheckChangedListener() {
			
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (CollectionUtils.isEmpty(eventObjects)) {
					return;
				}
				
				boolean removeCheckFlag = false;
				for (Object object : eventObjects) {
					MLotScrap unit = (MLotScrap) object;
					if (unit == null) {
						return;
					}
					
					if (checked) {
						String unScrapCode = combo.getText();
						if (StringUtil.isEmpty(unScrapCode)) {
							removeCheckFlag = true;
							break;
						}
						String comments = commentText.getText();
						String reasons = reasonTest.getText();
						unit.setUnScrapCode(unScrapCode);
						unit.setUnScrapReason(reasons);
						unit.setUnScrapComment(comments);
					} else {
						unit.setUnScrapCode("");
						unit.setUnScrapReason("");
						unit.setUnScrapComment("");
					}
				}
				
				if (removeCheckFlag) {
					tableManager.getCheckedObject().removeAll(eventObjects);
					UI.showWarning(Message.getString("unscrap.unscrapnew_dialog_selectcodefirst"));
				} else {
					if (isExternal) {
						List<Object> elements = tableManager.getCheckedObject();
						BigDecimal unScrapQty = BigDecimal.ZERO;
						for (Object element : elements) {
							MLotScrap unit = (MLotScrap) element;
							unScrapQty = unScrapQty.add(unit.getMainQty());
						}
					
						if (unScrapQty.compareTo(mLot.getMainQty()) == 0) {
							externalSplit.setEnabled(false);
							ok.setEnabled(true);
						} else {
							externalSplit.setEnabled(true);
							ok.setEnabled(false);
						}
					}
				}
			}
		});
		
		Composite unScrapComp = toolkit.createComposite(parent, SWT.NONE);
		unScrapComp.setLayout(new GridLayout(2, false));
		unScrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		unScrapComp.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));

		Label lab1 = toolkit.createLabel(unScrapComp, Message
				.getString("wip.unscrapcode_lot"), SWT.NULL);
		lab1.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		combo = RCPUtil.getUserRefListCombo(unScrapComp, "UnScrapCode", Env.getOrgRrn());
		
		combo.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
		
		Label lab3 = toolkit.createLabel(unScrapComp, Message
				.getString("mm.unscrapreason") + "*", SWT.NULL);
		lab3.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		reasonTest = toolkit.createText(unScrapComp, "", SWT.BORDER);
		reasonTest.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true,
				false));

		Label lab2 = toolkit.createLabel(unScrapComp, Message
				.getString("common.comment") + "*", SWT.NULL);
		lab2.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		commentText = toolkit.createText(unScrapComp, "", SWT.BORDER);
		commentText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true,
				false));
		return parent;
	}
	
	@Override
	protected void okPressed() {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			if (isUnScrapComponent) {
				// 位置不能为空
				Optional<MLotScrap> f = mLotScraps.stream().filter(
						l -> l.getUnScrapMainQty() == null || l.getUnScrapSubQty() == null).findFirst();
				f = mLotScraps.stream().filter(
						l -> {
							return l.getAttribute2() == null ||
									StringUtil.isEmpty(l.getAttribute2().toString());
						}).findFirst();
				if (f.isPresent()) {
					UI.showInfo(Message.getString("wip.unscrap_position_null"));
					return;
				}

				// 检查一片是否输入了多个位置
				// 检查一个位置是否对应多片了
				Map<String, String> positionsMap = Maps.newHashMap();
				Map<String, String> unitsMap = Maps.newHashMap();
				for (MLotScrap mLotScrap : mLotScraps) {
					String position = DBUtil.toString(mLotScrap.getAttribute2());
					if (positionsMap.containsKey(mLotScrap.getmComponentId())) {
						if (!position.equals(positionsMap.get(mLotScrap.getmComponentId()))) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_multiple_position"),
									mLotScrap.getmComponentId()));
							return;
						}
					}

					if (unitsMap.containsKey(position)) {
						if (!mLotScrap.getmComponentId().equals(unitsMap.get(position))) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_position_repeat"),
									mLotScrap.getmComponentId(), unitsMap.get(position)));
							return;
						}
					}

					positionsMap.put(mLotScrap.getmComponentId(), position);
					unitsMap.put(position, mLotScrap.getmComponentId());
				}

				// 检查片位置是否已被占用
				List<MComponentUnit> mComponentUnits = mmManager.getMLotWithComponent(mLot.getObjectRrn()).getSubMComponentUnit();
				if (!CollectionUtils.isEmpty(mComponentUnits)) {
					Map<String, String> poistions = Maps.newHashMap();
					for (MComponentUnit unit : mComponentUnits) {
						MComponentUnit mComponentUnit = (MComponentUnit) unit;
						poistions.put(mComponentUnit.getmComponentId(), mComponentUnit.getPosition());
					}

					for (MLotScrap mLotScrap : mLotScraps) {
						String position = DBUtil.toString(mLotScrap.getAttribute2());
						if (poistions.containsKey(mLotScrap.getmComponentId())) {
							if (!position.equals(poistions.get(mLotScrap.getmComponentId()))) {
								UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"),
										mLotScrap.getmComponentId(), poistions.get(mLotScrap.getmComponentId())));
								return;
							}
						} else {
							Boolean judge = false;
							String repeatPosition = null;
							for (Map.Entry<String, String> entry : poistions.entrySet()) {
								if (position.equals(entry.getValue())) {
									repeatPosition = entry.getKey();
									judge = true;
									break;
								}
							}
							if (judge) {
								UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"), repeatPosition,
										poistions.get(repeatPosition)));
								return;
							}
						}
					}
				}
			}
			// 检查数量
			List<Object> objects = tableManager.getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("wip.unscrap_no_units_selected"));
				return;
			}
			List<MLotScrap> mLotScraps = objects.stream()
					.map(l -> ((MLotScrap)l)).collect(Collectors.toList());
			
			List<MLotAction> mlotActions = new ArrayList<MLotAction>();
	    	// 把scrap转成action
	    	for (MLotScrap mLotScrap : mLotScraps) {
	    		MLotAction action = new MLotAction();
	    		action.setmLotRrn(mLotScrap.getmLotRrn());
	    		action.setActionCode(mLotScrap.getUnScrapCode());
	    		action.setActionReason(mLotScrap.getUnScrapReason());
	    		action.setActionComment(mLotScrap.getUnScrapComment());
	    		if (StringUtil.isEmpty(mLotScrap.getUnScrapComment())) {
	    			UI.showInfo(Message.getString("wip.abort_comments_null"));
	    			return;
	    		}
	    		action.setMainQty(mLotScrap.getUnScrapMainQty());
	    		action.setSubQty(mLotScrap.getUnScrapSubQty());
	    		action.setmLotScraps(Arrays.asList(mLotScrap));
	    		mlotActions.add(action);
	    	}
	    	mmManager.unScrapMLot(mLot, mlotActions, Env.getSessionContext());
	        UI.showInfo(Message.getString("wip.unscrapLot_success"));// 弹出提示框
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}
	
	private ADTable getTable() {
		ADTable adTable = null;
    	try {
    		if (!isUnScrapComponent) {
    			adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOT);
			} else {
				adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_COMP);
			}
    		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	return adTable;
    }

	public List<MLotScrap> getMLotscraps() {
		return mLotScraps;
	}

	public void setMLotScraps(List<MLotScrap> mLotScraps) {
		this.mLotScraps = mLotScraps;
	}
	
	@Override
	protected Point getInitialSize() {
		super.getShellStyle();
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.NONE);
	}

	public CheckBoxFixEditorTableManager getTableManager() {
		return tableManager;
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
    	ok = createSquareButton(parent, IDialogConstants.OK_ID,
				Message.getString(ExceptionBundle.bundle.CommonOk()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
		
		if (isExternal) {
			externalSplit = createSquareButton(parent, IDialogConstants.YES_ID,
					Message.getString("common.external_split"), false, UIControlsFactory.BUTTON_DEFAULT);
			
			fd = new FormData();
			fd.width = 90;
			fd.height = 35;
			fd.top = new FormAttachment(0, 15);
			fd.right = new FormAttachment(ok, -12, SWT.LEFT);
			fd.bottom = new FormAttachment(100, -15);
			externalSplit.setLayoutData(fd);
    	}
			
    }
	
	@Override
    protected void buttonPressed(int buttonId) {
		if (IDialogConstants.OK_ID == buttonId || IDialogConstants.YES_ID == buttonId) {
			okPressed();
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed();
		}
	}

	public SquareButton getExternalSplit() {
		return externalSplit;
	}
}
