package com.glory.mes.mm.mlot.create;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.entitymanager.interceptor.SectionInterceptor;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.framework.core.exception.ExceptionBundle;

public class CreateMLotProperties extends EntityProperties {

    public CreateMLotProperties() {
        super();
    }
    
    public void createToolBar(Section section) {
		if (interceptor != null) {
			if (interceptor.process(SectionInterceptor.ITEM_TOOLBAR, this)) {
				return;
			}
		}
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

    @Override
    protected void saveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    MMManager mmManager = Framework.getService(MMManager.class);
                    ADManager adManager = Framework.getService(ADManager.class);
                    MLot mlot = (MLot) getAdObject();
                    mlot = mmManager.createMLot(mlot, Env.getSessionContext());
                    UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框

                    masterParent.refreshAdd(mlot);
                    setAdObject(adManager.getEntity(mlot));
                    refresh();
                }
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
    
    @Override
    protected void deleteAdapter() {
    	try {
    		boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				if (getAdObject().getObjectRrn() != null) {
                    MMManager mmManager = Framework.getService(MMManager.class);
                    MLot mlot = (MLot) getAdObject();
                    mmManager.deleteMLot(mlot, Env.getSessionContext());
					setAdObject(createAdObject());
					refresh();
				}
			}
    	 } catch (Exception e) {
             ExceptionHandlerManager.asyncHandleException(e);
             return;
         }
    }

}
