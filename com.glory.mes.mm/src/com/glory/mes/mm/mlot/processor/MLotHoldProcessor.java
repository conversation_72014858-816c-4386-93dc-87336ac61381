package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;

public class MLotHoldProcessor extends AbstractMLotProcessor {

	private static final String TABLE_NAME = "MMLotHoldActionProcessor";
	
	private static final String TABLE_NAME_MLOT_LIST = "MMLotListHoldProcessor";
	
	private static final String MLOT_RELEASE_CODE = "MLotHoldCode";
	
	private IMessageManager mmng;
	private EntityForm entityForm;

	public MLotHoldProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				MLotAction mLotAction = (MLotAction) entityForm.getObject();
				
				MMManager mmManager = Framework.getService(MMManager.class);
				mmManager.holdMLot(lots, mLotAction, Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.hold_successed"));// 弹出提示框
			} else {
				return false;
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		if (MLot.STATE_COM.equals(mLot.getComClass())) {
			return false;
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
		try {
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			ManagedForm mform = new ManagedForm(toolkit, form);
			mmng = mform.getMessageManager();

			Composite body = form.getBody();
			configureBody(body);
			entityForm = new EntityForm(body, SWT.NONE, new MLotAction(), getADTable(), mmng);
			entityForm.setLayout(new GridLayout(1, false));
			entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("MLotHoldProcessor getADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}

	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldActionCode = new ADField();
		adFieldActionCode.setName("actionCode");
		adFieldActionCode.setIsMain(true);
		adFieldActionCode.setIsDisplay(true);
		adFieldActionCode.setIsEditable(true);
		adFieldActionCode.setDisplayLength(15l);
		adFieldActionCode.setLabel(Message.getString("wip.holdcode"));
		adFieldActionCode.setLabel_zh(Message.getString("wip.holdcode"));
		adFieldActionCode.setDataType("string");
		adFieldActionCode.setDisplayType("userreflist");
		adFieldActionCode.setReftableRrn(14479l);
		adFieldActionCode.setUreflistName(MLOT_RELEASE_CODE);
		adFieldActionCode.setIsMandatory(true);
		adFields.add(adFieldActionCode);

		ADField adFieldActionReason = new ADField();
		adFieldActionReason.setName("actionReason");
		adFieldActionReason.setIsMain(true);
		adFieldActionReason.setIsDisplay(true);
		adFieldActionReason.setIsEditable(true);
		adFieldActionReason.setLabel(Message.getString("wip.holdreason"));
		adFieldActionReason.setLabel_zh(Message.getString("wip.holdreason"));
		adFieldActionReason.setDataType("string");
		adFieldActionReason.setDisplayType("text");
		adFieldActionReason.setIsMandatory(false);
		adFields.add(adFieldActionReason);
		
		ADField adFieldActionComment = new ADField();
		adFieldActionComment.setName("actionComment");
		adFieldActionComment.setIsMain(true);
		adFieldActionComment.setIsDisplay(true);
		adFieldActionComment.setIsEditable(true);
		adFieldActionComment.setLabel(Message.getString("wip.holdcomment"));
		adFieldActionComment.setLabel_zh(Message.getString("wip.holdcomment"));
		adFieldActionComment.setDataType("string");
		adFieldActionComment.setDisplayType("textarea");
		adFieldActionComment.setIsMandatory(false);
		adFields.add(adFieldActionComment);
		
		adTable.setFields(adFields);

		return adTable;
	}
	
	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> fields = adTable.getFields();
		
		ADField fieldTransWarehouseId = new ADField();
		fieldTransWarehouseId.setName("transWarehouseId");
		fieldTransWarehouseId.setIsMain(true);
		fieldTransWarehouseId.setIsDisplay(true);
		fieldTransWarehouseId.setSeqNo(75l);
		fieldTransWarehouseId.setLabel(Message.getString("mm.warehouse.list"));
		fieldTransWarehouseId.setLabel_zh(Message.getString("mm.warehouse.list"));
		fields.add(fieldTransWarehouseId);
	
		ADField fieldStorageId = new ADField();
		fieldStorageId.setName("transStorageId");
		fieldStorageId.setIsMain(true);
		fieldStorageId.setIsDisplay(true);
		fieldStorageId.setIsEditable(true);
		fieldStorageId.setLabel(Message.getString("wip.position"));
		fieldStorageId.setLabel_zh(Message.getString("wip.position"));
		fieldStorageId.setDataType("string");
		fieldStorageId.setIsMandatory(false);
		fields.add(fieldStorageId);
		return adTable;
	}

}
