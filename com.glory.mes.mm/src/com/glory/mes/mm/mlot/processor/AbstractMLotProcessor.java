package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.model.LotStateModel;

public abstract class AbstractMLotProcessor implements IMLotProcessor {
	
	public static final Logger logger = Logger.getLogger(AbstractMLotProcessor.class);

	private static final String TABLE_NAME_MLOT_LIST = "WIPMLotProcessorLotList";

	private boolean isBatch;
	private boolean isFalse = true;
	
	public AbstractMLotProcessor(boolean isBatch) {
		super();
		this.isBatch = isBatch;
	}

	public AbstractMLotProcessor(boolean isBatch, boolean isFalse) {
		super();
		this.isBatch = isBatch;
		this.isFalse = isFalse;
	}
	/**
	 * 只有所有批次校验都通过才执行(显示LotProcessorDialog)
	 * 否则显示
	 */
	protected boolean isAllPass = false;
	
	protected String eventId;
	
	public abstract boolean checkMLotState(MLot lot);
	
	public abstract void buildProcessForm(Composite parent, FormToolkit toolkit);
	
	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	public boolean preValidate(MLot lot) {
		Boolean isValid = checkMLotState(lot);
		if (!isValid) {
			lot.setConstraintFlag(true);
			lot.clearMessage();
            lot.addMessage(Message.getString(ExceptionBundle.bundle.ErrorStateIsNotAllow()));
		}
		return isValid;
	}
	
	/**
	 * 打开处理Dialog
	 * 
	 * @param lots 待处理的批次
	 */
	public void open(List<MLot> lots) {
		try {
			List<MLot> inVaildLots = new ArrayList<MLot>();
			
			for (MLot lot : lots) {
				if (!preValidate(lot)) {
					inVaildLots.add(lot);
				}
			}
			if (isBatch) {
				if (isAllPass && !inVaildLots.isEmpty()) {
					//显示无效批次,并返回
					openInValidDialog(inVaildLots);
					return;
				}
				openLotProcessorDialog(lots);
			} else {
				if (!inVaildLots.isEmpty()) {
					//显示无效批次,并返回
					openInValidDialog(inVaildLots);
					return;
				}
				openLotProcessorDialog(lots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void openInValidDialog(List<MLot> lots) {
		MLotProcessorInValidDialog dialog = new MLotProcessorInValidDialog(this, lots);
		if (dialog.open() == Dialog.OK) {}
	}
	
	public void openLotProcessorDialog(List<MLot> lots) {	
		MLotProcessorDialog dialog = new MLotProcessorDialog(this, lots);
		if (dialog.open() == Dialog.OK) {}
	}
	
	/**
	 * 可以为事件设置LotStateModel,如果没有设置则返回null,这时需要检查事件的默认规则
	 * 可以为每个事件设置多条LotStateModel,只要符合一条规则就可以执行此事件(Button可用)
	 * LotStateModel检查方式为批次当前状态与Hold状态,与LotStateModel一致
	 */
	public Boolean checkLotStateModel(MLot lot) {
		try {
			if (!StringUtil.isEmpty(eventId)) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotStateModel> models = adManager.getEntityList(Env.getOrgRrn(), LotStateModel.class, Integer.MAX_VALUE, " eventId = '" + eventId + "'", "");
				if (models.isEmpty()) {
					return null;
				}
				
				boolean isAllFail = true;
				for (LotStateModel model : models) {
					if (!StringUtil.isEmpty(model.getHoldState()) && !model.getHoldState().equals(lot.getHoldState())) {
						//本次检查不通过,继续检查下一规则
						continue;
					}
					if (!StringUtil.isEmpty(model.getLotState()) && !model.getLotState().equals(lot.getState())) {
						//本次检查不通过,继续检查下一规则
						continue;
					}
					isAllFail = false;
					break;
				}
				if(!isAllFail) {
					return true;
				} else {
					return false;
				}
			} else {
				return null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}

	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();

		ADField adField1 = new ADField();
		adField1.setName("mLotId");
		adField1.setIsMain(true);
		adField1.setIsDisplay(true);
		adField1.setSeqNo(10l);
		adField1.setLabel(Message.getString("mm.mlot_id"));
		adField1.setLabel_zh(Message.getString("mm.mlot_id"));
		adFields.add(adField1);

		ADField adField2 = new ADField();
		adField2.setName("materialName");
		adField2.setIsMain(true);
		adField2.setIsDisplay(true);
		adField2.setSeqNo(20l);
		adField2.setLabel(Message.getString("mm.material_name"));
		adField2.setLabel_zh(Message.getString("mm.material_name"));
		adFields.add(adField2);

		ADField adField3 = new ADField();
		adField3.setName("materialDesc");
		adField3.setIsMain(true);
		adField3.setIsDisplay(true);
		adField3.setSeqNo(30l);
		adField3.setLabel(Message.getString("mm.material_desc"));
		adField3.setLabel_zh(Message.getString("mm.material_desc"));
		adFields.add(adField3);
		
		ADField adField4 = new ADField();
		adField4.setName("mainQty");
		adField4.setIsMain(true);
		adField4.setIsDisplay(true);
		adField4.setIsEditable(true);
		adField4.setSeqNo(40l);
		adField4.setLabel(Message.getString("mm.mlot_qty"));
		adField4.setLabel_zh(Message.getString("mm.mlot_qty"));
		adField4.setDataType("integer");
		adField4.setDisplayType("text");
		adFields.add(adField4);
		
		ADField adField5 = new ADField();
		adField5.setName("uomId");
		adField5.setIsMain(true);
		adField5.setIsDisplay(true);
		adField5.setSeqNo(50l);
		adField5.setLabel(Message.getString("mm.uom_id"));
		adField5.setLabel_zh(Message.getString("mm.uom_id"));
		adFields.add(adField5);
		
		ADField adField6 = new ADField();
		adField6.setName("state");
		adField6.setIsMain(true);
		adField6.setIsDisplay(true);
		adField6.setIsEditable(true);
		adField6.setSeqNo(60l);
		adField6.setLabel(Message.getString("mm.lot_state"));
		adField6.setLabel_zh(Message.getString("mm.lot_state"));
		adField6.setDisplayType("text");
		adFields.add(adField6);
		
		ADField adField7 = new ADField();
		adField7.setName("holdState");
		adField7.setIsMain(true);
		adField7.setIsDisplay(true);
		adField7.setIsEditable(true);
		adField7.setSeqNo(70l);
		adField7.setLabel(Message.getString("mm.hold_state"));
		adField7.setLabel_zh(Message.getString("mm.hold_state"));
		adField7.setDisplayType("text");
		adFields.add(adField7);
		
		ADField adField8 = new ADField();
		adField8.setName("messageString");
		adField8.setIsMain(true);
		adField8.setIsDisplay(true);
		adField8.setIsEditable(true);
		adField8.setSeqNo(80l);
		adField8.setLabel(Message.getString("mm.lot_message"));
		adField8.setLabel_zh(Message.getString("mm.lot_message"));
		adField8.setDisplayType("text");
		adFields.add(adField8);

		adTable.setFields(adFields);

		return adTable;
	}

	public boolean isBatch() {
		return isBatch;
	}

	public void setBatch(boolean isBatch) {
		this.isBatch = isBatch;
	}

	public boolean isFalse() {
		return isFalse;
	}

	public void setFalse(boolean isFalse) {
		this.isFalse = isFalse;
	}
	
}
