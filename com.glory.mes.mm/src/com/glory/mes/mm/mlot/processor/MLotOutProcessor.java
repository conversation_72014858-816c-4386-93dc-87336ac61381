package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.state.model.MaterialEvent;

public class MLotOutProcessor extends AbstractMLotProcessor {

private static final String TABLE_NAME = "MMLotOutProcessor";
	
	private static final String TABLE_NAME_MLOT_LIST = "MMLotListOutProcessor";
	
	private IMessageManager mmng;
	private EntityForm entityForm;

	private MLot mLot;
	
	public MLotOutProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				MLot mLot = (MLot) entityForm.getObject();
				
				MMManager mmManager = Framework.getService(MMManager.class);
				for (MLot lot : lots) {
					lot.setTransMainQty(lot.getMainQty());
					//物料批次库存信息
					List<MLotStorage> storages = mmManager.getLotStorages(lot.getObjectRrn());
					if (storages == null || storages.size() == 0) {
						UI.showError(Message.getString("mm.lot_not_in_warehouse_or_storage"));
						return false;
					}		
					if (storages.size() > 1) {
						UI.showError(Message.getString("wms.lot_in_multi_warehouse_or_storage"));
						return false;
					}
					MLotStorage storage = storages.get(0);
					String storageKey = storage.getWarehouseRrn() + storage.getStorageType() + storage.getStorageId();
					String inputKey = mLot.getTransWarehouseRrn() + mLot.getTransStorageType() + mLot.getTransStorageId();
					
					if (!storageKey.equals(inputKey)) {
						UI.showInfo(Message.getString("mm.storage_no_exist"));
						return false;
					}
				}
						
				mmManager.outMLots(lots, new MLotAction(), 
						mLot.getTransWarehouseRrn(), null, mLot.getTransStorageType(), mLot.getTransStorageId(), false, Env.getSessionContext());
				
				UI.showInfo(Message.getString("mm.mlot_ship_success"));// 弹出提示框
			} else {
				return false;
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			return mmManager.checkMLotState(mLot, MaterialEvent.EVENT_INVENTORYOUT, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
		try {
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			ManagedForm mform = new ManagedForm(toolkit, form);
			mmng = mform.getMessageManager();

			Composite body = form.getBody();
			configureBody(body);
			
			MLot newMLot = new MLot();
			
			if (mLot != null) {
				newMLot.setTransWarehouseRrn(mLot.getTransWarehouseRrn());
				newMLot.setTransStorageId(mLot.getTransStorageId());
				newMLot.setTransStorageType(mLot.getTransStorageType());
			}
			
			entityForm = new EntityForm(body, SWT.NONE, newMLot, getADTable(), mmng);
			entityForm.setLayout(new GridLayout(1, false));
			entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void open(List<MLot> lots) {
		for (MLot lot : lots) {
			if (lot.getTransWarehouseRrn() != null) {
				this.mLot = lot;
				break;
			}
		}		
		super.open(lots);
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("MLotOutProcessor getADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}

	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldWarehouse = new ADField();
		adFieldWarehouse.setName("transWarehouseRrn");
		adFieldWarehouse.setIsMain(true);
		adFieldWarehouse.setIsDisplay(true);
		adFieldWarehouse.setIsEditable(true);
		adFieldWarehouse.setLabel(Message.getString("mm.source_warehouse"));
		adFieldWarehouse.setLabel_zh(Message.getString("mm.source_warehouse"));
		adFieldWarehouse.setDataType("integer");
		adFieldWarehouse.setDisplayType("reftable");	
		adFieldWarehouse.setReftableRrn(4350820l);
		adFieldWarehouse.setIsMandatory(true);
		adFields.add(adFieldWarehouse);

		ADField adFieldStorageId = new ADField();
		adFieldStorageId.setName("transStorageId");
		adFieldStorageId.setIsMain(true);
		adFieldStorageId.setIsDisplay(true);
		adFieldStorageId.setIsEditable(true);
		adFieldStorageId.setLabel(Message.getString("mm.source_storage"));
		adFieldStorageId.setLabel_zh(Message.getString("mm.source_storage"));
		adFieldStorageId.setDataType("string");
		adFieldStorageId.setDisplayType("reftable");	
		adFieldStorageId.setReftableRrn(93547900l);
		adFieldStorageId.setIsMandatory(false);
		adFields.add(adFieldStorageId);
		
		ADField adFieldStorageType = new ADField();
		adFieldStorageType.setName("transStorageType");
		adFieldStorageType.setIsMain(true);
		adFieldStorageType.setIsDisplay(true);
		adFieldStorageType.setIsEditable(true);
		adFieldStorageType.setLabel(Message.getString("mm.source_storage_type"));
		adFieldStorageType.setLabel_zh(Message.getString("mm.source_storage_type"));
		adFieldStorageType.setDataType("string");
		adFieldStorageType.setDisplayType("hidden");
		adFieldStorageType.setReferenceRule("transStorageId.category");
		adFields.add(adFieldStorageType);
		adTable.setFields(adFields);
		
//		ADField adFieldQty = new ADField();
//		adFieldQty.setName("mainQty");
//		adFieldQty.setIsMain(true);
//		adFieldQty.setIsDisplay(true);
//		adFieldQty.setIsEditable(true);
//		adFieldQty.setLabel(Message.getString("mm.mlot_qty"));
//		adFieldQty.setLabel_zh(Message.getString("mm.mlot_qty"));
//		adFieldQty.setDataType("integer");
//		adFieldQty.setDisplayType("text");
//		adFieldQty.setIsMandatory(true);
//		adFields.add(adFieldQty);
		
		adTable.setFields(adFields);

		return adTable;
	}
	
	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("MLotOutProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> fields = adTable.getFields();
		
		ADField fieldTransWarehouseId = new ADField();
		fieldTransWarehouseId.setName("transWarehouseId");
		fieldTransWarehouseId.setIsMain(true);
		fieldTransWarehouseId.setIsDisplay(true);
		fieldTransWarehouseId.setSeqNo(75l);
		fieldTransWarehouseId.setLabel(Message.getString("mm.warehouse.list"));
		fieldTransWarehouseId.setLabel_zh(Message.getString("mm.warehouse.list"));
		fields.add(fieldTransWarehouseId);
	
		ADField fieldStorageId = new ADField();
		fieldStorageId.setName("transStorageId");
		fieldStorageId.setIsMain(true);
		fieldStorageId.setIsDisplay(true);
		fieldStorageId.setIsEditable(true);
		fieldStorageId.setLabel(Message.getString("wip.position"));
		fieldStorageId.setLabel_zh(Message.getString("wip.position"));
		fieldStorageId.setDataType("string");
		fieldStorageId.setIsMandatory(false);
		fields.add(fieldStorageId);
		return adTable;
	}

}
