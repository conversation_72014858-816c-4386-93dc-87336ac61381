package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.framework.core.exception.ExceptionBundle;

public class MLotOnShelfProcessor extends AbstractMLotProcessor {

	private static final String TABLE_NAME = "MMLotOnShelfProcessor";
	
	private static final String TABLE_NAME_MLOT_LIST = "MMLotListOnShelfProcessor";
	
	private IMessageManager mmng;
	private EntityForm entityForm;
	
	public MLotOnShelfProcessor(boolean isBatch) {
		super(isBatch);
	}
	
	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				MLot mLot = (MLot) entityForm.getObject();
				
				MMManager mmManager = Framework.getService(MMManager.class);	
				
				Map<String, MLotStorage> mLotStorageMap = new HashMap<String, MLotStorage>();	
				for (MLot lot : lots) {
					//物料批次库存信息
					List<MLotStorage> storages = mmManager.getLotStorages(lot.getObjectRrn());
					if (storages == null || storages.size() == 0) {
						UI.showError(Message.getString("mm.lot_not_in_warehouse_or_storage"));
						return false;
					}		
					if (storages.size() > 1) {
						UI.showError(Message.getString("wms.lot_in_multi_warehouse_or_storage"));
						return false;
					}
					MLotStorage storage = storages.get(0);
					
					if (!storage.getWarehouseRrn().equals(mLot.getTransTargetWarehouseRrn())) {
						UI.showInfo(Message.getString("mm.onshelf_can_not_change_warehouse"));
						return false;
					}
					
					String storageKey = storage.getWarehouseRrn() + storage.getStorageType() + storage.getStorageId();
					String changeKey = mLot.getTransTargetWarehouseRrn() + mLot.getTransTargetStorageType() + mLot.getTransTargetStorageId();
					
					// 未做改变，提示后返回
					if (storageKey.equals(changeKey)) {
						UI.showInfo(Message.getString("mm.transfer_no_change"));
						return false;
					}
				
					mLotStorageMap.put(lot.getmLotId(), storages.get(0));
				}
			
				Warehouse warehouse = new Warehouse();
				warehouse.setObjectRrn(mLot.getTransTargetWarehouseRrn());
				warehouse = mmManager.getWarehouse(warehouse);
				mmManager.mLotOnShelf(lots, null, warehouse, mLot.getTransTargetStorageType(), mLot.getTransTargetStorageId(), Env.getSessionContext());							
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
			} else {
				return false;
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		try {
			// 1.检查批次是否已入库
			MMManager mmManager = Framework.getService(MMManager.class);
			List<MLotStorage> storages = mmManager.getLotStorages(mLot.getObjectRrn());
			if (CollectionUtils.isEmpty(storages)) {
				return false;
			}		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} 
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
		try {
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			ManagedForm mform = new ManagedForm(toolkit, form);
			mmng = mform.getMessageManager();

			Composite body = form.getBody();
			configureBody(body);
				
			entityForm = new EntityForm(body, SWT.NONE, new MLot(), getADTable(), mmng);
			entityForm.setLayout(new GridLayout(1, false));
			entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("MLotOnShelfProcessor getADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}

	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldWarehouse = new ADField();
		adFieldWarehouse.setName("transTargetWarehouseRrn");
		adFieldWarehouse.setIsMain(true);
		adFieldWarehouse.setIsDisplay(true);
		adFieldWarehouse.setIsEditable(true);
		adFieldWarehouse.setLabel(Message.getString("mm.warehouse.list"));
		adFieldWarehouse.setLabel_zh(Message.getString("mm.warehouse.list"));
		adFieldWarehouse.setDataType("integer");
		adFieldWarehouse.setDisplayType("reftable");	
		adFieldWarehouse.setReftableRrn(4350820l);
		adFieldWarehouse.setIsMandatory(true);
		adFields.add(adFieldWarehouse);

		ADField adFieldParentStorageId = new ADField();
		adFieldParentStorageId.setName("attribute1");
		adFieldParentStorageId.setIsMain(true);
		adFieldParentStorageId.setIsDisplay(true);
		adFieldParentStorageId.setIsEditable(true);
		adFieldParentStorageId.setLabel(Message.getString("mm.target_rack"));
		adFieldParentStorageId.setLabel_zh(Message.getString("mm.target_rack"));
		adFieldParentStorageId.setDataType("string");
		adFieldParentStorageId.setDisplayType("reftable");	
		adFieldParentStorageId.setReftableRrn(574574l);
		adFieldParentStorageId.setIsMandatory(true);
		adFields.add(adFieldParentStorageId);
		
		ADField adFieldStorageId = new ADField();
		adFieldStorageId.setName("transTargetStorageId");
		adFieldStorageId.setIsMain(true);
		adFieldStorageId.setIsDisplay(true);
		adFieldStorageId.setIsEditable(true);
		adFieldStorageId.setLabel(Message.getString("mm.target_rack_area"));
		adFieldStorageId.setLabel_zh(Message.getString("mm.target_rack_area"));
		adFieldStorageId.setDataType("string");
		adFieldStorageId.setDisplayType("reftable");	
		adFieldStorageId.setReftableRrn(567465l);
		adFieldStorageId.setIsMandatory(true);
		adFields.add(adFieldStorageId);
		
		ADField adFieldStorageType = new ADField();
		adFieldStorageType.setName("transTargetStorageType");
		adFieldStorageType.setIsMain(true);
		adFieldStorageType.setIsDisplay(true);
		adFieldStorageType.setIsEditable(true);
		adFieldStorageType.setLabel(Message.getString("mm.target_storage_type"));
		adFieldStorageType.setLabel_zh(Message.getString("mm.target_storage_type"));
		adFieldStorageType.setDataType("string");
		adFieldStorageType.setDisplayType("hidden");
		adFieldStorageType.setReferenceRule("transTargetStorageId.category");
		adFields.add(adFieldStorageType);
		adTable.setFields(adFields);
		
		adTable.setFields(adFields);

		return adTable;
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("MLotOnShelfProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> fields = adTable.getFields();
		
		ADField fieldTransWarehouseId = new ADField();
		fieldTransWarehouseId.setName("transWarehouseId");
		fieldTransWarehouseId.setIsMain(true);
		fieldTransWarehouseId.setIsDisplay(true);
		fieldTransWarehouseId.setSeqNo(75l);
		fieldTransWarehouseId.setLabel(Message.getString("mm.warehouse.list"));
		fieldTransWarehouseId.setLabel_zh(Message.getString("mm.warehouse.list"));
		fields.add(fieldTransWarehouseId);
	
		ADField fieldStorageId = new ADField();
		fieldStorageId.setName("transStorageId");
		fieldStorageId.setIsMain(true);
		fieldStorageId.setIsDisplay(true);
		fieldStorageId.setIsEditable(true);
		fieldStorageId.setLabel(Message.getString("wip.position"));
		fieldStorageId.setLabel_zh(Message.getString("wip.position"));
		fieldStorageId.setDataType("string");
		fieldStorageId.setIsMandatory(false);
		fields.add(fieldStorageId);
		
		return adTable;
	}

}
