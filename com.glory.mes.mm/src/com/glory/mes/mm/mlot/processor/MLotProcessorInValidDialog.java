package com.glory.mes.mm.mlot.processor;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;

/**
 * 显示未通过校验的批次列表
 */
public class MLotProcessorInValidDialog extends BaseTitleDialog {

	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;
	
	private IMLotProcessor processor;
	private List<MLot> inValidLots;
	
	private ListTableManager tableManager;
	
	public MLotProcessorInValidDialog(IMLotProcessor processor, List<MLot> inValidLots) {
		super();
		this.inValidLots = inValidLots;
		this.processor = processor;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		setTitle(Message.getString("mm.action_is_not_allowed"));
		setMessage(Message.getString("mm.action_mlot_is_not_allowed"));
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));

		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
	
		FormToolkit toolkit = new FFormToolKit(Display.getCurrent());
		
		Section section = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
		section.setLayout(new GridLayout(1, true));
		section.setLayoutData(new GridData(GridData.FILL_BOTH));
		section.setText(Message.getString("wip.lot_processor_invalid_list_title"));
		
		Composite content = toolkit.createComposite(section);
      	content.setLayout(new GridLayout(1, true));
      	content.setLayoutData(new GridData(GridData.FILL_BOTH));
        
      	AbstractMLotProcessor abstractLotProcessor = (AbstractMLotProcessor)processor;
      	
      	try {
      		tableManager = new MLotProcessorTableManager(abstractLotProcessor.getListADTable());
      		tableManager.newViewer(content);
      		tableManager.setInput(inValidLots);
      	} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	}
      
      	section.setClient(content);
		return parent;
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
