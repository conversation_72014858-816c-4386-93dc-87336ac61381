package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.lot.model.MLot;

/**
 * 显示处理批次信息,有上下两个部分组成
 * 上部分根据不同的动作显示不同的输入表单
 * 下部分为选中的批次信息,包含不能处理的批次,不能处理的批次也显示,但显示红色且不能选中
 */
public class MLotProcessorDialog extends BaseTitleDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;
	
	public IMLotProcessor processor;
	public List<MLot> lots;
	
	public ListTableManager tableManager;
	
	public MLotProcessorDialog(IMLotProcessor processor, List<MLot> lots) {
		super();
		this.processor = processor;
		this.lots = lots;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		FFormToolKit toolkit = new FFormToolKit(parent.getDisplay()); 
		
		AbstractMLotProcessor abstractLotProcessor = (AbstractMLotProcessor)processor;   	
		if(abstractLotProcessor.isFalse()) {
			Section upSection = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
			upSection.setLayout(new GridLayout(1, true));
			upSection.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			upSection.setText(Message.getString("wip.select_action_info"));
			
			Composite upComposite = toolkit.createComposite(upSection);
			upComposite.setLayout(new GridLayout(1, true));
			upComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			abstractLotProcessor.buildProcessForm(upComposite, toolkit);    	
			upSection.setClient(upComposite);
		}
      	
      	if (abstractLotProcessor.isBatch()) {
      		Section downSection = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
      		downSection.setLayout(new GridLayout(1, true));
      		downSection.setLayoutData(new GridData(GridData.FILL_BOTH));
      		if(lots != null && !lots.isEmpty() && lots.get(0) instanceof Tool) {
      			downSection.setText(Message.getString("ras.tool_processor_list_title"));
      			setTitle(Message.getString("mm.tool_action"));
      			setMessage(Message.getString("mm.please_input_tool_action_info"));
      		} else {
      			downSection.setText(Message.getString("wip.lot_processor_list_title"));
      			setTitle(Message.getString("mm.mlot_action"));
      			setMessage(Message.getString("mm.please_input_mlot_action_info"));
      		}
    		
          	Composite downComposite = toolkit.createComposite(downSection);
          	downComposite.setLayout(new GridLayout(1, true));
          	downComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
    		
          	try {
          		tableManager = new MLotProcessorTableManager(abstractLotProcessor.getListADTable(), true);
          		tableManager.newViewer(downComposite);
          		((CheckBoxTableViewerManager)tableManager.getTableManager()).setCheckBoxDisableListener(new ICheckBoxDisableListener() {
        			public boolean isDisable(Object object) {
        				MLot lot = (MLot)object;
        				if (lot.getConstraintFlag()) {
        					return true;
        				}
        				return false;
        			}
        		});
          		tableManager.setInput(lots);
          		if (lots != null && lots.size() > 0) {
          			List<MLot> validLots = new ArrayList<MLot>();
          			for (MLot lot : lots) {
          				if (!lot.getConstraintFlag()) {
          					validLots.add(lot);
        				}
              		}
              		tableManager.getCheckedObject().addAll(validLots);
              		tableManager.refresh();
          		}
          		
          	} catch (Exception e) {
          		ExceptionHandlerManager.asyncHandleException(e);
          	}
          	downSection.setClient(downComposite);
      	} else {
      		setTitle(Message.getString("mm.action_info"));
  			setMessage(Message.getString("mm.please_input_action_info"));
      	}
      	
		return parent;
	}
	
	@Override
	protected void okPressed() {
		AbstractMLotProcessor abstractLotProcessor = (AbstractMLotProcessor)processor;   	
		if (abstractLotProcessor.isBatch()) {
			List<Object> checkedObjects = tableManager.getCheckedObject();
			if (checkedObjects != null && checkedObjects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object checkedObject : checkedObjects) {
					MLot lot = (MLot) checkedObject;
					checkLots.add(lot);
				}
				if (processor.process(checkLots)) {
					super.okPressed();
				}
			}
		} else {
			if (processor.process(lots)) {
				super.okPressed();
			}
		}
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		AbstractMLotProcessor abstractLotProcessor = (AbstractMLotProcessor)processor; 
		
		int width = MIN_DIALOG_WIDTH;
		int height = MIN_DIALOG_HEIGHT;
		if (!abstractLotProcessor.isBatch()) {
			width = MIN_DIALOG_WIDTH - 50;
			height = MIN_DIALOG_HEIGHT - 50;
		}
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(width), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(height),
						shellSize.y));
	}
}
