package com.glory.mes.mm.mlot.processor;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.framework.core.exception.ExceptionBundle;

public class MLotOffShelfProcessor extends AbstractMLotProcessor {

	private static final String TABLE_NAME_MLOT_LIST = "MMLotListOffShelfProcessor";
	
	public MLotOffShelfProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			
			MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
			for (MLot lot : lots) {
				//物料批次库存信息
				List<MLotStorage> storages = mmManager.getLotStorages(lot.getObjectRrn());
				if (storages == null || storages.size() == 0) {
					UI.showError(Message.getString("mm.lot_not_in_warehouse_or_storage"));
					return false;
				}		
				if (storages.size() > 1) {
					UI.showError(Message.getString("wms.lot_in_multi_warehouse_or_storage"));
					return false;
				}
			}
			mmManager.mLotOffShelf(lots, new MLotAction(), null, null, null, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		try {
			// 1.检查批次是否已入库
			MMManager mmManager = Framework.getService(MMManager.class);
			List<MLotStorage> storages = mmManager.getLotStorages(mLot.getObjectRrn());
			if (CollectionUtils.isEmpty(storages)) {
				return false;
			}	
			if (StringUtil.isEmpty(storages.get(0).getStorageId())) {
				return false;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} 
		return true;
	}

	public void openLotProcessorDialog(List<MLot> lots) {	
		MLotOffShelfProcessorDialog dialog = new MLotOffShelfProcessorDialog(this, lots);
		if (dialog.open() == Dialog.OK) {}
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {}
	
	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("MLotOffShelfProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> fields = adTable.getFields();
		
		ADField fieldTransWarehouseId = new ADField();
		fieldTransWarehouseId.setName("transWarehouseId");
		fieldTransWarehouseId.setIsMain(true);
		fieldTransWarehouseId.setIsDisplay(true);
		fieldTransWarehouseId.setSeqNo(75l);
		fieldTransWarehouseId.setLabel(Message.getString("mm.warehouse.list"));
		fieldTransWarehouseId.setLabel_zh(Message.getString("mm.warehouse.list"));
		fields.add(fieldTransWarehouseId);
	
		ADField fieldStorageId = new ADField();
		fieldStorageId.setName("transStorageId");
		fieldStorageId.setIsMain(true);
		fieldStorageId.setIsDisplay(true);
		fieldStorageId.setIsEditable(true);
		fieldStorageId.setLabel(Message.getString("wip.position"));
		fieldStorageId.setLabel_zh(Message.getString("wip.position"));
		fieldStorageId.setDataType("string");
		fieldStorageId.setIsMandatory(false);
		fields.add(fieldStorageId);
		
		return adTable;
	}

}
