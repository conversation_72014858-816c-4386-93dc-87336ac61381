package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.state.model.StatusModel;
import com.glory.common.state.model.StatusModelDiagram;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityQueryProgress;
import com.glory.framework.base.entitymanager.forms.ViewQueryProgress;
import com.glory.framework.base.ui.dialog.QueryProgressMonitorDialog;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.svg.SvgDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.idquery.IdQueryEntityQueryListSection;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * 物料查询列表,可以在此列表中对批量对物料批次进行处理
 * 支持导入功能
 */
public class MLotProcessorLotListSection extends IdQueryEntityQueryListSection {
	
	
	public static final String KEY_IN = "In";
	public static final String KEY_ONSHELF = "OnShelf";
	public static final String KEY_OFFSHELF = "OffShelf";
	public static final String KEY_OUT = "Out";
	public static final String KEY_TRANSFER = "Transfer";
	public static final String KEY_RETURN = "Return";
	public static final String KEY_CHANGESHELFLIFE = "ChangeShelfLife";
	public static final String KEY_HOLD = "Hold";
	public static final String KEY_RELEASE = "Release";
	public static final String KEY_SCRAP = "Scrap";
	
	protected ToolItem itemIn;
	protected ToolItem itemOnShelf;
	protected ToolItem itemOffShelf;
	protected ToolItem itemOut;
	protected ToolItem itemTransfer;
	protected ToolItem itemReturn;
	protected ToolItem itemChangeShelfLife;
	protected ToolItem itemHold;
	protected ToolItem itemRelease;
	protected ToolItem itemScrap;
	
	protected IField transWarehouseRrnIField;
	protected IField transStorageIdIField;
	
	public static final String FIELD_NAME_WAREHOUSE = "transWarehouseRrn";
	public static final String FIELD_NAME_STORAGE = "transStorageId";
	
	public MLotProcessorLotListSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemIn(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemOnShelf(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemOffShelf(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTransfer(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemOut(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);	
		createToolItemReturn(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemChangeShelfLife(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRelease(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSearch(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		//根据系统参数判定时都需要展示该按钮
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			boolean isShowSvg = MesCfMod.isShowSvgButton(Env.getOrgRrn(), sysParamManager);
			if(isShowSvg) {
				new ToolItem(tBar, SWT.SEPARATOR);
				createToolItemSvg(tBar);
			}
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : createToolItemSvg ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemSvg(ToolBar tBar) {
		itemSvg = new ToolItem(tBar, SWT.PUSH);;
		itemSvg.setText(Message.getString("common.svg"));
		itemSvg.setImage(SWTResourceCache.getImage("svg"));
		itemSvg.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				svgAdapter();
			}
		});
	}
	
	protected void createToolItemIn(ToolBar tBar) {
		itemIn = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_IN);
		itemIn.setText(Message.getString("mm.mlot_in"));
		itemIn.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemIn.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				inAdapter();
			}
		});
	}
	
	protected void createToolItemOnShelf(ToolBar tBar) {
		itemOnShelf = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_ONSHELF);
		itemOnShelf.setText(Message.getString("mm.mlot_on"));
		itemOnShelf.setImage(SWTResourceCache.getImage("mlot_onshelf"));
		itemOnShelf.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				onShelfAdapter();
			}
		});
	}
	
	protected void createToolItemOffShelf(ToolBar tBar) {
		itemOffShelf = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_OFFSHELF);
		itemOffShelf.setText(Message.getString("mm.mlot_off"));
		itemOffShelf.setImage(SWTResourceCache.getImage("mlot_offshelf"));
		itemOffShelf.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				offShelfAdapter();
			}
		});
	}
	
	protected void createToolItemOut(ToolBar tBar) {
		itemOut = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_OUT);
		itemOut.setText(Message.getString("mm.mlot_out"));
		itemOut.setImage(SWTResourceCache.getImage("mlot_receive"));
		itemOut.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				outAdapter();
			}
		});
	}
	
	protected void createToolItemTransfer(ToolBar tBar) {
		itemTransfer = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_TRANSFER);
		itemTransfer.setText(Message.getString("mm.mlot_transfer"));
		itemTransfer.setImage(SWTResourceCache.getImage("move_to_loc"));
		itemTransfer.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				transferAdapter();
			}
		});
	}
	
	protected void createToolItemReturn(ToolBar tBar) {
		itemReturn = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_RETURN);
		itemReturn.setText(Message.getString("mm.mlot_return"));
		itemReturn.setImage(SWTResourceCache.getImage("returnto"));
		itemReturn.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				returnAdapter();
			}
		});
	}
	
	protected void createToolItemChangeShelfLife(ToolBar tBar) {
		itemChangeShelfLife = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_CHANGESHELFLIFE);
		itemChangeShelfLife.setText(Message.getString("mm.mlot_change_shelf_life"));
		itemChangeShelfLife.setImage(SWTResourceCache.getImage("wip_code"));
		itemChangeShelfLife.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeShelfLifeAdapter();
			}
		});
	}
	
	protected void createToolItemHold(ToolBar tBar) {
		itemHold = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_HOLD);
		itemHold.setText(Message.getString("wip.hold"));
		itemHold.setImage(SWTResourceCache.getImage("hold-lot"));
		itemHold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				holdAdapter();
			}
		});
	}
	
	protected void createToolItemRelease(ToolBar tBar) {
		itemRelease = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_RELEASE);
		itemRelease.setText(Message.getString("mm.mlot_release"));
		itemRelease.setImage(SWTResourceCache.getImage("release-lot"));
		itemRelease.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				releaseAdapter();
			}
		});
	}
	
	protected void createToolItemScrap(ToolBar tBar) {
		itemScrap = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_SCRAP);
		itemScrap.setText(Message.getString("wip.scrap_lotcn"));
	    itemScrap.setImage(SWTResourceCache.getImage("scrap-lot"));
		itemScrap.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				scrapAdapter();
			}
		});
	}
	
	protected void inAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				MLotInProcessor processor = new MLotInProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : inAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void onShelfAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				MLotOnShelfProcessor processor = new MLotOnShelfProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : onShelfAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void offShelfAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				MLotOffShelfProcessor processor = new MLotOffShelfProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : offShelfAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void transferAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				MLotTransferProcessor processor = new MLotTransferProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : transferAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void outAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				MLotOutProcessor processor = new MLotOutProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : outAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void returnAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				if (checkLots.size() > 1) {
					UI.showInfo(Message.getString("common.only_allow_select_one_object"));
					return;
				}
				
				MLotReturnProcessor processor = new MLotReturnProcessor(false);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : returnAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void changeShelfLifeAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				MLotChangeShelfLifeProcessor processor = new MLotChangeShelfLifeProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : changeShelfLifeAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void holdAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					MLot mlot = (MLot)object;
					if (MLot.HOLDSTATE_ON.equals(mlot.getHoldState())) {
						UI.showInfo(Message.getString("mm.mlot_already_hold"));
						return;
					}
					checkLots.add((MLot)object);
				}
				MLotHoldProcessor processor = new MLotHoldProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : holdAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void releaseAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				MLotReleaseProcessor processor = new MLotReleaseProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : releaseAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void scrapAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot)object);
				}
				if (checkLots.size() > 1) {
					UI.showInfo(Message.getString("common.only_allow_select_one_object"));
					return;
				}
				
				MLotScrapProcessor processor = new MLotScrapProcessor(false);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : releaseAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void svgAdapter() {
		try {
			Object[] elements = null;
			if (getSelectedObject() != null) {
				elements = new Object[] {getCheckedObject()};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else if(elements.length > 1){
				UI.showInfo(Message.getString("common.select_only_one_object"));
			}else {
				ADManager adManager = Framework.getService(ADManager.class);
				//需要标注的状态列表
				MLot mLot = (MLot) getSelectedObject();
				List<String> actives = new ArrayList<String>();
				actives.add(mLot.getState());
				//根据治具获取到治具对应的状态模型
				StatusModel statusModel = new StatusModel();
				statusModel.setObjectRrn(mLot.getStatusModelRrn());
				statusModel = (StatusModel) adManager.getEntity(statusModel);
				//获取状态模型对应的事件流程图
				byte[] svgData= null;
				List<StatusModelDiagram> statusModelDiagrams = adManager.getEntityList(Env.getOrgRrn(), StatusModelDiagram.class, 1, " modelRrn = " + statusModel.getObjectRrn(), null);
				if(statusModelDiagrams != null && !statusModelDiagrams.isEmpty()) {
					svgData = statusModelDiagrams.get(0).getSvgData();
				}
				SvgDialog dialog = new SvgDialog(svgData, actives, null);
				dialog.open();
				this.refresh();
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}
	
	protected void queryAdapter() {
		managedForm.getMessageManager().removeAllMessages();
		if (!getQueryForm().validate()){
			return;
		}
		
		LinkedHashMap<String, IField> fields = getQueryForm().getFields();
		if (transWarehouseRrnIField == null) {
			transWarehouseRrnIField = fields.get(FIELD_NAME_WAREHOUSE);
		}
		if (transStorageIdIField == null) {
			transStorageIdIField = fields.get(FIELD_NAME_STORAGE);
		}	
		
		getQueryForm().getFields().remove(FIELD_NAME_WAREHOUSE);
		getQueryForm().getFields().remove(FIELD_NAME_STORAGE);
		
		String whereClause = " 1 = 1 " + getQueryForm().createWhereClause(true);
		whereClause = StringUtil.relpaceWildcardCondition(whereClause);
		setWhereClause(whereClause);
		refresh();
	}
	
	public void refresh() {
		try {		
			long count = getEntityNumber();
			List<Object> adList = new ArrayList<Object>();
			if (count > getMonitorThreshold()) {
				EntityQueryProgress progress;
				if (tableManager.getADTable().getIsView()) {
					progress = new ViewQueryProgress(getADManger(), count, getProcessThreshold(), tableManager.getADTable(), getWhereClause(), "", getParameterMap());
				} else {
					progress = new EntityQueryProgress(getADManger(), count, getProcessThreshold(), tableManager.getADTable(), getWhereClause(), "", getParameterMap());
				}
				QueryProgressMonitorDialog progressDiglog = new QueryProgressMonitorDialog(UI.getActiveShell(), "");
				progressDiglog.run(true, true, progress);
				adList = progress.getAdList();
			} else {
				ADManager manager = getADManger();
				if (tableManager.getADTable().getIsView()) {
					List<Map> currentList = manager.getEntityMapListByColumn(Env.getOrgRrn(), tableManager.getADTable().getObjectRrn(), 
		            		0, Env.getMaxResult(), getWhereClause(), "", false);
					adList.addAll(currentList);
				} else {
					if (tableManager.getADTable().isContainMainAttribute()) {
						List<ADBase> currentList = manager.getEntityList(Env.getOrgRrn(), tableManager.getADTable().getObjectRrn(), 
			            		0, Env.getMaxResult(), getWhereClause(), "", true, tableManager.getADTable().getMainAttributes(), getParameterMap());
						adList.addAll(currentList);
					} else {
						List<ADBase> currentList = manager.getEntityList(Env.getOrgRrn(), tableManager.getADTable().getObjectRrn(), 0, 
			            		Env.getMaxResult(), getWhereClause(), "", getParameterMap());
						adList.addAll(currentList);
					}
				}
		    }
			showNumber = adList.size();		
			if (adList != null && adList.size() > 0) {
				MMManager mmManager = Framework.getService(MMManager.class);
				List<MLot> mLotList = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), (List<MLot>)(List)adList);
				List<MLot> newMLots = new ArrayList<MLot>(); 
				for (MLot mLot : mLotList) {
					boolean flag = true;
					if (transWarehouseRrnIField != null && !StringUtil.isEmpty((String)transWarehouseRrnIField.getValue())) {
						if (!transWarehouseRrnIField.getValue().equals(String.valueOf(mLot.getTransWarehouseRrn()))) {
							flag = false;
						}
					}
					if (transStorageIdIField != null && !StringUtil.isEmpty((String)transStorageIdIField.getValue())) {
						if (!transStorageIdIField.getValue().equals(mLot.getTransStorageId())) {
							flag = false;
						}
					}
					if (flag) {
						newMLots.add(mLot);
					}
				}
				showNumber = newMLots.size();
				tableManager.setInput(newMLots);
			} else {
				tableManager.setInput(adList);
			}		
			
			createSectionDesc(section);
		} catch(Exception e) {
			logger.error("Error at Refresh ", e);
		}
	}
	
	@Override
	public List<Object> getObjectsByInClause(List<String> ids) {
		try {
			Set idSet = new HashSet(ids);
			Map<String, Object> fieldMap = new HashMap<String, Object>();
			fieldMap.put("mLotIds", idSet);
			ADManager adManager = Framework.getService(ADManager.class);
			List<MLot> lots = adManager.getEntityList(Env.getOrgRrn(), MLot.class, 
					Integer.MIN_VALUE, Integer.MAX_VALUE, " mLotId in (:mLotIds) ", "", fieldMap);
			
			MMManager mmManager = Framework.getService(MMManager.class);
			lots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), lots);
			return (List<Object>)(List)lots;
		} catch (Exception e) {
			logger.error("LotProcessorLotListSection getObjectsByInClause error:", e);
		}
		return null;
	}

}
