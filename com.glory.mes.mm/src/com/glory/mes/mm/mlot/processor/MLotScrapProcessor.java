package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;

public class MLotScrapProcessor extends AbstractMLotProcessor {

	private static final String TABLE_NAME = "MMLotScrapActionProcessor";
	
	private static final String TABLE_NAME_MLOT_LIST = "MMLotListScrapProcessor";
	
	private static final String MLOT_SCRAP_CODE = "MLotScrapCode";
	
	private IMessageManager mmng;
	private EntityForm entityForm;

	public MLotScrapProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				MLotAction mLotAction = (MLotAction) entityForm.getObject();
				List<MLotAction> mlotActions = new ArrayList<MLotAction>();
				mlotActions.add(mLotAction);
				
				for (MLot mlot : lots) {
					if (mLotAction.getMainQty().compareTo(mlot.getMainQty()) == 1) {
						UI.showError(Message.getString("mm.qty_must_less_than_mainqty"));
						return false;
					}
					
					MMManager mmManager = Framework.getService(MMManager.class);
					mmManager.scrapMLot(mlot, mlotActions, Env.getSessionContext());
				}		
				UI.showInfo(Message.getString("wip.scrapLot_success"));// 弹出提示框
			} else {
				return false;
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		if (MLot.STATE_COM.equals(mLot.getComClass())) {
			return false;
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
		try {
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			ManagedForm mform = new ManagedForm(toolkit, form);
			mmng = mform.getMessageManager();

			Composite body = form.getBody();
			configureBody(body);
			entityForm = new EntityForm(body, SWT.NONE, new MLotAction(), getADTable(), mmng);
			entityForm.setLayout(new GridLayout(1, false));
			entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("MLotScrapProcessor getADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}

	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldQty = new ADField();
		adFieldQty.setName("mainQty");
		adFieldQty.setIsMain(true);
		adFieldQty.setIsDisplay(true);
		adFieldQty.setIsEditable(true);
		adFieldQty.setLabel(Message.getString("mm.mlot_qty"));
		adFieldQty.setLabel_zh(Message.getString("mm.mlot_qty"));
		adFieldQty.setDataType("integer");
		adFieldQty.setDisplayType("text");
		adFieldQty.setIsMandatory(true);
		adFields.add(adFieldQty);
		
		ADField adFieldActionCode = new ADField();
		adFieldActionCode.setName("actionCode");
		adFieldActionCode.setIsMain(true);
		adFieldActionCode.setIsDisplay(true);
		adFieldActionCode.setIsEditable(true);
		adFieldActionCode.setDisplayLength(15l);
		adFieldActionCode.setLabel(Message.getString("mm.scrapcode"));
		adFieldActionCode.setLabel_zh(Message.getString("mm.scrapcode"));
		adFieldActionCode.setDataType("string");
		adFieldActionCode.setDisplayType("userreflist");
		adFieldActionCode.setReftableRrn(14479l);
		adFieldActionCode.setUreflistName(MLOT_SCRAP_CODE);
		adFieldActionCode.setIsMandatory(true);
		adFields.add(adFieldActionCode);

		ADField adFieldActionReason = new ADField();
		adFieldActionReason.setName("actionReason");
		adFieldActionReason.setIsMain(true);
		adFieldActionReason.setIsDisplay(true);
		adFieldActionReason.setIsEditable(true);
		adFieldActionReason.setLabel(Message.getString("mm.scrapreason"));
		adFieldActionReason.setLabel_zh(Message.getString("mm.scrapreason"));
		adFieldActionReason.setDataType("string");
		adFieldActionReason.setDisplayType("text");
		adFieldActionReason.setIsMandatory(false);
		adFields.add(adFieldActionReason);
		
		ADField adFieldActionComment = new ADField();
		adFieldActionComment.setName("actionComment");
		adFieldActionComment.setIsMain(true);
		adFieldActionComment.setIsDisplay(true);
		adFieldActionComment.setIsEditable(true);
		adFieldActionComment.setLabel(Message.getString("mm.comment"));
		adFieldActionComment.setLabel_zh(Message.getString("mm.comment"));
		adFieldActionComment.setDataType("string");
		adFieldActionComment.setDisplayType("textarea");
		adFieldActionComment.setIsMandatory(false);
		adFields.add(adFieldActionComment);
			
		adTable.setFields(adFields);

		return adTable;
	}
	
	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("MLotScrapProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		return adTable;
	}

}
