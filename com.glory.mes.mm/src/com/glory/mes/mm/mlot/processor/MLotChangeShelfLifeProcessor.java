package com.glory.mes.mm.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;

public class MLotChangeShelfLifeProcessor extends AbstractMLotProcessor {

private static final String TABLE_NAME = "MMLotChangeShelfLifeProcessor";
	
	private static final String TABLE_NAME_MLOT_LIST = "MMLotListChangeShelfLifeProcessor";
	
	private IMessageManager mmng;
	private EntityForm entityForm;

	public MLotChangeShelfLifeProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				MLot mLot = (MLot) entityForm.getObject();
				if (mLot.getShelfLifeExpire() == null) {
					UI.showError(Message.getString("mm_select_the_expiration_date"));
					return false;
				}
				for (MLot lot : lots) {
					lot.setShelfLifeExpire(mLot.getShelfLifeExpire());
				}
				MMManager mmManager = Framework.getService(MMManager.class);
				mmManager.changeMLotShelfLifeExpire(lots, Env.getSessionContext());
				
				UI.showError(Message.getString("mm_modification_period"));// 弹出提示框
			} else {
				return false;
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		if (MLot.STATE_COM.equals(mLot.getComClass())) {
			return false;
		}
		return true;
	}
	
	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
		try {
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			ManagedForm mform = new ManagedForm(toolkit, form);
			mmng = mform.getMessageManager();

			Composite body = form.getBody();
			configureBody(body);
			entityForm = new EntityForm(body, SWT.NONE, new MLot(), getADTable(), mmng);
			entityForm.setLayout(new GridLayout(1, false));
			entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("MLotChangeShelfLifeProcessor getADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}

	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldActionCode = new ADField();
		adFieldActionCode.setName("shelfLifeExpire");
		adFieldActionCode.setIsMain(true);
		adFieldActionCode.setIsDisplay(true);
		adFieldActionCode.setIsEditable(true);
		adFieldActionCode.setDisplayLength(15l);
		adFieldActionCode.setLabel(Message.getString("mm_period_validity"));
		adFieldActionCode.setLabel_zh(Message.getString("mm_period_validity"));
		adFieldActionCode.setDataType("string");
		adFieldActionCode.setDisplayType("datetime");
		adFieldActionCode.setIsMandatory(true);
		adFields.add(adFieldActionCode);
		
		adTable.setFields(adFields);

		return adTable;
	}
	
	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("MLotChangeShelfLifeProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> fields = adTable.getFields();
		
		ADField fieldTransWarehouseId = new ADField();
		fieldTransWarehouseId.setName("transWarehouseId");
		fieldTransWarehouseId.setIsMain(true);
		fieldTransWarehouseId.setIsDisplay(true);
		fieldTransWarehouseId.setSeqNo(75l);
		fieldTransWarehouseId.setLabel(Message.getString("mm.warehouse.list"));
		fieldTransWarehouseId.setLabel_zh(Message.getString("mm.warehouse.list"));
		fields.add(fieldTransWarehouseId);
	
		ADField fieldStorageId = new ADField();
		fieldStorageId.setName("transStorageId");
		fieldStorageId.setIsMain(true);
		fieldStorageId.setIsDisplay(true);
		fieldStorageId.setIsEditable(true);
		fieldStorageId.setLabel(Message.getString("wip.position"));
		fieldStorageId.setLabel_zh(Message.getString("wip.position"));
		fieldStorageId.setDataType("string");
		fieldStorageId.setIsMandatory(false);
		fields.add(fieldStorageId);
		return adTable;
	}
}
