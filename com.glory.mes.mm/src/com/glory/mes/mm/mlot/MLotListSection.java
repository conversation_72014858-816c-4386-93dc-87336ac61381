package com.glory.mes.mm.mlot;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityListSection;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;

public class MLotListSection extends EntityListSection {
	private static final Logger logger = Logger.getLogger(MLotListSection.class);
	public HeaderText txtMLot;
	public ADBase adObject;

	public MLotListSection(ListTableManager tableManager) {
		super(tableManager);
	}
	
	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("mm.mlot_id") + "：");
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtMLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
		txtMLot.setTextLimit(32);
		txtMLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					MLot mLot = null;
					String lotId = tLotId.getText();
					tLotId.setText(lotId);
					mLot = searchMLot(lotId);
					tLotId.selectAll();
					if (mLot == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
							setAdObject(new MLot());
	        			} catch(Exception en) {
	        				logger.error("createADObject error at searchEntity Method!");
	        			}
						txtMLot.warning();
					} else {
						setAdObject(mLot);
						txtMLot.focusing();
					}
					refresh();
					break;
				}
			}

		});
		txtMLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				tLotId.setText(tLotId.getText());
			}
		});
		
		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
	}
	
	public void setFocus() {
		txtMLot.setFocus();
	}
	
	private MLot searchMLot(String mLotId) {
		try {
			MMManager manager = Framework.getService(MMManager.class);
			return manager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
		} catch (Exception e) {
            logger.error("Cannot find this MLot by this mLotId!");
            ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	@Override
	public void refresh() {
		super.refresh();
		if(txtMLot != null) {
			txtMLot.selectAll();
		}
	}
	
	public void setAdObject(ADBase adObject) {
		this.adObject = adObject;
	}
	public ADBase getAdObject() {
		return adObject;
	}
}
