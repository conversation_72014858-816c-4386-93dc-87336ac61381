package com.glory.mes.mm.material.statusmodel;


import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import com.glory.common.state.model.StatusModelDiagram;
import com.glory.common.state.model.StatusModelEvent;
import com.glory.common.state.statusmodel.StatuaModelProperties;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.model.Material;
import com.glory.mes.mm.state.model.MaterialStatusModel;
import com.glory.framework.core.exception.ExceptionBundle;

public class MaterialStatusModelProperties extends StatuaModelProperties {
	
	private MaterialStatusModelForm parameterForm;
	    
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		EntityForm itemForm;
        String tabName = tab.getName();
        if (tabName.equalsIgnoreCase("MaterialStatusModelBasicInfo")) {
            parameterForm = new MaterialStatusModelForm(composite, SWT.NONE, tab, mmng, this);
            return parameterForm;
        } else {
            itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
        }

		return itemForm;
	}
	
	public void saveAdapter() {
		try {
			form.getMessageManager().setAutoUpdate(false);
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();
				
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					ADManager entityManager = Framework.getService(ADManager.class);
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					MaterialStatusModel model = (MaterialStatusModel)getAdObject();
					//处理事件
					for (StatusModelEvent modelEvent : model.getModelEvents()) {
						if (modelEvent.getObjectRrn() == null) {
							modelEvent.setCreatedBy(Env.getUserName());
						}
						modelEvent.setEvent(null);
						modelEvent.setUpdatedBy(Env.getUserName());
					}
					//处理svg图片
					StatusModelDiagram diagram = new StatusModelDiagram();
					//删除旧数据
					if(model.getStatusModelDiagram() != null) {
						//当上传新的diagram对象，删除旧的diagram
						List<StatusModelDiagram> diagrams = entityManager.getEntityList(Env.getOrgRrn(), StatusModelDiagram.class, 1, " modelRrn = " + model.getObjectRrn(), null);
						if(diagrams != null && !diagrams.isEmpty()) {
							entityManager.deleteEntity(diagrams.get(0), Env.getSessionContext());
						}
					}
					diagram.setOrgRrn(Env.getOrgRrn());
					diagram.setCreatedBy(Env.getUserName());
					diagram.setUpdatedBy(Env.getUserName());
					diagram.setModelRrn(model.getObjectRrn());
					diagram.setSvgData(model.getSvgData());
					model.setStatusModelDiagram(diagram);
					
					//保存
					MMManager mmManager = Framework.getService(MMManager.class);
					ADBase obj = mmManager.saveStatusModel(getTable().getObjectRrn(), model, Env.getSessionContext());
					
					//刷新
					ADBase newBase = entityManager.getEntity(obj);
					if (getTable().isContainAttribute()) {
						newBase.setAttributeValues(entityManager.getEntityAttributeValues(getTable().getModelName(), newBase.getObjectRrn()));
					}
					if (oldBase.getObjectRrn() == null) {
						getMasterParent().refreshAdd(newBase);
					} else {
						getMasterParent().refreshUpdate(newBase);
					}
					
					setAdObject(newBase);
					
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					refresh();
					return;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			form.getMessageManager().setAutoUpdate(true);
		}
		return;
	}
	
	
	//物料状态模型删除方法
		protected void deleteAdapter() {
			ADBase oldBase = getAdObject();
			boolean deleteFlag = delete();
			if (deleteFlag) {
				getMasterParent().refreshDelete(oldBase);
			}
		}
		
		
		public boolean delete() {
			try {
				boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
				if ((confirmDelete) && (getAdObject().getObjectRrn() != null)) {
					ADManager adManager = Framework.getService(ADManager.class);
					List<Material> materials = adManager.getEntityList(Env.getOrgRrn(), Material.class, Env.getMaxResult(), " statusModelRrn = " + getAdObject().getObjectRrn(), null);
					if(materials != null && materials.size() > 0) {
						UI.showInfo(Message.getString("mm.model_is_used"));// 弹出提示框
						return false;
					}
					ADManager entityManager = getADManger();
					entityManager.deleteEntity(getAdObject(), Env.getSessionContext());
					setAdObject(createAdObject());
					UI.showInfo(Message.getString("wip.pilot_delete_successed"));// 弹出提示框
					refresh();
					return true;
				}
			} catch (Exception e1) {
				ExceptionHandlerManager.asyncHandleException(e1);
			}
			return false;
		}
}
