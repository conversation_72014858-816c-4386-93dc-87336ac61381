package com.glory.mes.mm.material.statusmodel.simple;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

/**
 * 简单状态模型管理功能
 * 暂停使用该功能
 * 该功能不能选择和自定义事件，导致MES功能和新增的状态模型事件不能对应，原状态不能转变为目标状态
 * <AUTHOR>
 *
 */
public class MaterialSimpleStatusModelEditor extends EntityEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.material.statusmodel.simple.MaterialSimpleStatusModelEditor";
	
	@Override
	protected void createBlock(ADTable adTable) {
		block = new MaterialSimpleStatusModelBlock(new ListTableManager(adTable));
	}
}
