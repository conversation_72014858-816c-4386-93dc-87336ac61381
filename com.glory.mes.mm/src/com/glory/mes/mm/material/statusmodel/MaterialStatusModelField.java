package com.glory.mes.mm.material.statusmodel;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.common.state.model.StatusModel;
import com.glory.common.state.model.StatusModelEvent;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.core.exception.ExceptionBundle;

public class MaterialStatusModelField extends AbstractField {
	protected List<Object> values;
	protected SquareButton delete, up, down, append;
	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
    protected List<Object> mItems;
    protected ADTable adTable;
    protected ListTableManager listTableManager;
	
	public MaterialStatusModelField(String id, ADTable adTable, Composite parent, int style, EntityForm parentForm) {
		super(id);
        this.adTable = adTable;
        this.mStyle = this.mStyle | style;
		this.setParent(parentForm);
    }

    @Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		String labelStr = getLabel();
		
        composite.setLayout(new GridLayout());
        composite.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        Composite top = toolkit.createComposite(composite, SWT.NULL);
        top.setLayoutData(new GridData(GridData.FILL_BOTH));  
        if (!StringUtil.isEmpty(labelStr)) {
        	mControls = new Control[2];
        	Label label = toolkit.createLabel(top, labelStr);
            mControls[0] = label;
            top.setLayout(new GridLayout(2, false));
        } else {
        	mControls = new Control[1];
        	top.setLayout(new GridLayout(1, false));
        }
        
        
        createNewViewer(top, toolkit);
        
        createButtons(toolkit, composite);
    	append.addSelectionListener(new SelectionListener() {
            public void widgetSelected(SelectionEvent e) {
            	boolean saveFlag = true;
            	for (IForm detailForm : ((MaterialStatusModelForm)getParent()).parentForm.getDetailForms()) {
					if (detailForm instanceof MaterialStatusModelForm) {
						break;
					} else {
	            		if (!detailForm.saveToObject()) {
							saveFlag = false;
							break;
						}
					}
				}
            	if (!saveFlag) {
            		return;
            	}
            	
            	StatusModel statusModel = (StatusModel)getParent().getObject();
            	if (StringUtil.isEmpty(statusModel.getObjectType())) {
            		//UI.showWarning(Message.getString("mm.select_object_type"));
					return;
            	}
            	
            	int seqNo = 0;
            	values = (List<Object>)getValue();
            	if(values != null) {
            		seqNo = values.size(); 
            	} 
            	AddMaterialEventDialog od = new AddMaterialEventDialog(UI.getActiveShell(), null, statusModel.getObjectType());
            	od.setEvents(values);
            	if(od.open() == IDialogConstants.OK_ID) {
            		List<StatusModelEvent> events = od.getEvents();
            		for (StatusModelEvent event : events) {
            			addEvent(event, seqNo);
            		}
            	}
            }
            public void widgetDefaultSelected(SelectionEvent e) {
                widgetSelected(e);
            }
    	});
    	delete.addSelectionListener(new SelectionListener() {
			public void widgetSelected(SelectionEvent e) {
				List<Object> list = (List<Object>)getValue();
				List<Object> objectListEvent = listTableManager.getCheckedObject();
				if(objectListEvent.size() != 0) {
					for(Object o : objectListEvent) {
						StatusModelEvent pe = (StatusModelEvent)o;
						if(list.contains(pe)) {
							list.remove(pe);
						}
					}
				}
				filter(list);
			}			
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}
		});
	}

    public void filter(List<Object> list) {
		setValue(list);
		refresh();
	}
    
    protected void addEvent(StatusModelEvent event, int seqNo) {
    	if(event == null) return ;
    	if(values == null) {
			values = new ArrayList<Object>();
			values.add(event);
		} else if(!values.contains(event)) {
			values.add(event);
		}
		filter(values);
    }
    
    public void createButtons(FormToolkit toolkit, Composite composite) {
    	Composite bn = toolkit.createComposite(composite, SWT.NULL);
    	bn.setLayout( new GridLayout(5, false));
    	GridData g = new GridData();
    	g.horizontalAlignment = GridData.END;
    	bn.setLayoutData(g);
    	append = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonAdd()), "DEFAULT");
    	delete = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonDelete()), "DEFAULT");
    	decorateButton(append);
    	decorateButton(delete);
    }
	  
    public void decorateButton(SquareButton button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		int widthHint = 88;  //IDialogConstants.BUTTON_WIDTH
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}

	protected void createNewViewer(Composite client, FormToolkit toolkit){
		//非空时,显示行信息
		String labelStr = getLabel();
		if (adTable == null) {
			return;
		}
		
		Composite tableContainer = toolkit.createComposite(client, SWT.NULL);
        GridData gd = new GridData(GridData.FILL_BOTH);
        gd.grabExcessHorizontalSpace = true;
        gd.horizontalAlignment = SWT.FILL;
        gd.heightHint = 220;
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(gd);
        
    	if (!StringUtil.isEmpty(labelStr)) {
    		mControls[1] = tableContainer;
        } else {
        	mControls[0] = tableContainer;
        }
		
		listTableManager = new ListTableManager(adTable, true);
		listTableManager.setAutoSizeFlag(true);		
		listTableManager.setIndexFlag(true);
		listTableManager.newViewer(tableContainer);
		
		listTableManager.addDoubleClickListener(new AbstractMouseSelectionAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				boolean saveFlag = true;
            	for (IForm detailForm : ((MaterialStatusModelForm)getParent()).parentForm.getDetailForms()) {
            		if (detailForm instanceof MaterialStatusModelForm) {
						break;
					} else {
	            		if (!detailForm.saveToObject()) {
							saveFlag = false;
							break;
						}
					}
				}
            	if (!saveFlag) {
            		return;
            	}
            	
            	StatusModel statusModel = (StatusModel)getParent().getObject();
            	if (StringUtil.isEmpty(statusModel.getObjectType())) {
					return;
            	}
            	
            	StatusModelEvent modelEvent = (StatusModelEvent)listTableManager.getSelectedObject();
				AddMaterialEventDialog od = new AddMaterialEventDialog(UI.getActiveShell(), modelEvent, statusModel.getObjectType());
				if(od.open() == IDialogConstants.OK_ID) {
					modelEvent = od.getEditModelEvent();
				}
			}
    	});
	}
	  
	@Override
	public void refresh() {
		List<Object> val = (List<Object>)getValue();
        if (val != null) {
        	listTableManager.setInput(val);
        } else {
        	listTableManager.setInput(new ArrayList<Object>());
        }
	}
	
	public List<? extends Object> getInput() {
		return listTableManager.getInput();
	}
}
