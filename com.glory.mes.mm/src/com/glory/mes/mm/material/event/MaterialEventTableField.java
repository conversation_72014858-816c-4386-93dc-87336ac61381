package com.glory.mes.mm.material.event;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.common.state.model.Event;
import com.glory.common.state.model.EventStatus;
import com.glory.common.state.model.StatusModel;
import com.glory.common.state.model.StatusModelEvent;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.material.statusmodel.AddMaterialEventDialog;
import com.glory.mes.mm.material.statusmodel.MaterialStatusModelForm;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.framework.core.exception.ExceptionBundle;

public class MaterialEventTableField extends AbstractField {

	protected List<Object> values;
	protected SquareButton delete, up, down, append;
	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
	protected List<Object> mItems;
	protected ADTable adTable;
	protected ListTableManager listTableManager;

	public MaterialEventTableField(String id, ADTable adTable, int style, EntityForm parentForm) {
		super(id);
		this.adTable = adTable;
		this.mStyle = this.mStyle | style;
		this.setParent(parentForm);
	}

	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		String labelStr = getLabel();
        composite.setLayout(new GridLayout());
        composite.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        Composite top = toolkit.createComposite(composite, SWT.NULL);
        top.setLayoutData(new GridData(GridData.FILL_BOTH));  
        if (!StringUtil.isEmpty(labelStr)) {
        	mControls = new Control[2];
        	Label label = toolkit.createLabel(top, labelStr);
            mControls[0] = label;
            top.setLayout(new GridLayout(2, false));
        } else {
        	mControls = new Control[1];
        	top.setLayout(new GridLayout(1, false));
        }
    	
    	createNewViewer(top, toolkit);
    	
    	createButtons(toolkit, composite);
    	append.addSelectionListener(new SelectionListener() {
            public void widgetSelected(SelectionEvent e) {
            	boolean saveFlag = true;
            	for (IForm detailForm : ((MaterialEventForm)getParent()).parentForm.getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
						break;
					}
				}
            	if (!saveFlag) {
            		return;
            	}
            	
            	Event event = (Event)getParent().getObject();
            	if (StringUtil.isEmpty(event.getObjectType())) {
            		//UI.showWarning(Message.getString("mm.select_object_type"));
					return;
            	}
            	
            	int seqNo = 0;
            	values = (List<Object>)getValue();
            	if(values != null) {
            		seqNo = values.size(); 
            	} 
            	AddMMEventStatusDialog od = new AddMMEventStatusDialog(e.widget.getDisplay().getActiveShell(), event.getObjectType());
            	if(od.open() == IDialogConstants.OK_ID) {
            		EventStatus operation = od.getOperation();
            		createPRDOperation(operation, seqNo);
            	}
            }
            public void widgetDefaultSelected(SelectionEvent e) {
                widgetSelected(e);
            }
    	});
      	up.addSelectionListener(new SelectionListener() {
            public void widgetSelected(SelectionEvent e) {
            	List<Object> objectListState = listTableManager.getCheckedObject();
				if(objectListState.size() > 0) {
            		for(int i = 0; i < objectListState.size(); i++) {
            			int seqNo = ((EventStatus)objectListState.get(i)).getSeqNo().intValue();
            			if(seqNo > 1){
            				EventStatus op = (EventStatus)objectListState.get(i);
            				searchLastOperation(seqNo);
            				op.setSeqNo(new Long(seqNo - 1));
            			} else break;            				
            		}
            	}
				
				List<Object> checkedObjectList = new ArrayList<Object>();
				for (Object obj : objectListState) {
					checkedObjectList.add(obj);
				}
				
				List<EventStatus> stateList = (List<EventStatus>)listTableManager.getInput();
				List<EventStatus> sortStateList = new ArrayList<EventStatus>();
				for (int i = 0; i < stateList.size(); i++) {
					int index = i + 1;
					List<EventStatus> lst = stateList.stream().filter(p -> p.getSeqNo() == index).collect(Collectors.toList());
					if (lst.size() > 0) {
						sortStateList.add(lst.get(0));
					}
				}
				
				listTableManager.setInput(sortStateList);
				
				//选中
				for (Object obj : checkedObjectList) {
					listTableManager.setCheckedObject(obj);
				}
            }
            
            public void widgetDefaultSelected(SelectionEvent e) {
                widgetSelected(e);
            }
            
            public void searchLastOperation(int seqNo) {
            	List<Object> objectListState = (List<Object>)listTableManager.getInput();
    			for(Object ti : objectListState) {
    				EventStatus op = (EventStatus)ti;
    				if (op.getSeqNo() == seqNo - 1) {
    					op.setSeqNo(new Long(seqNo));
    					break;
    				}
    			}
    		}
    	});
    	down.addSelectionListener(new SelectionListener() {
            public void widgetSelected(SelectionEvent e) {
            	List<Object> objectListState = listTableManager.getCheckedObject();
				int count = listTableManager.getInput().size();
            	if(objectListState.size() > 0) {
            		int checkedLength = objectListState.size();
            			for(int i = checkedLength -1; i >= 0; i--) {
            				int seqNo = ((EventStatus)objectListState.get(i)).getSeqNo().intValue();
            				if(seqNo < count){
            					EventStatus op = (EventStatus)objectListState.get(i);
            					searchNextOperation(seqNo);
            					op.setSeqNo(new Long(seqNo + 1));
            				} else break;
            				
            			}
            	}
            	
            	List<Object> checkedObjectList = new ArrayList<Object>();
				for (Object obj : objectListState) {
					checkedObjectList.add(obj);
				}
            	
            	List<EventStatus> stateList = (List<EventStatus>)listTableManager.getInput();
				List<EventStatus> sortStateList = new ArrayList<EventStatus>();
				for (int i = 0; i < stateList.size(); i++) {
					int index = i + 1;
					List<EventStatus> lst = stateList.stream().filter(p -> p.getSeqNo() == index).collect(Collectors.toList());
					if (lst.size() > 0) {
						sortStateList.add(lst.get(0));
					}
				}
				
				listTableManager.setInput(sortStateList);
				
				//选中
				for (Object obj : checkedObjectList) {
					listTableManager.setCheckedObject(obj);
				}
            }
            
            public void widgetDefaultSelected(SelectionEvent e) {
                widgetSelected(e);
            }
            
            public void searchNextOperation(int seqNo) {
            	List<Object> objectListState = (List<Object>)listTableManager.getInput();
    			for(Object ti : objectListState) {
    				EventStatus op = (EventStatus)ti;
    				if (op.getSeqNo() == seqNo + 1) {
    					op.setSeqNo(new Long(seqNo));
    					break;
    				}
    			}
    		}
    	});
    	delete.addSelectionListener(new SelectionListener() {
			public void widgetSelected(SelectionEvent e) {
				List<Object> list = (List<Object>)getValue();
				List<Object> objectListState = listTableManager.getCheckedObject();
				if(objectListState.size() != 0) {
					for(Object o : objectListState) {
						EventStatus pe = (EventStatus)o;
						changeAfterOperationSeqNo(pe);
						if(list.contains(pe)) {
							list.remove(pe);
						}
					}
				}
				filter(list);
			}			
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}
			
			public void changeAfterOperationSeqNo(EventStatus pe) {
				List<Object> objectListState = (List<Object>)listTableManager.getInput();
				int deleteOperationSeqNo = pe.getSeqNo().intValue();
				for(Object ti : objectListState) {
					EventStatus op = (EventStatus)ti;
    				int afterSeqNo = op.getSeqNo().intValue();
    				if (afterSeqNo > deleteOperationSeqNo) {
    					op.setSeqNo(new Long(afterSeqNo -1));
    				}
    			}
			}
		});
    	
	}
	
	protected void createNewViewer(Composite client, FormToolkit toolkit){
		String labelStr = getLabel();
		//非空时,显示行信息
		if (adTable == null) {
			return;
		}
		
		Composite tableContainer = toolkit.createComposite(client, SWT.NULL);
        GridData gd = new GridData(GridData.FILL_BOTH);
        gd.grabExcessHorizontalSpace = true;
        gd.horizontalAlignment = SWT.FILL;
        gd.heightHint = 220;
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(gd);
        
    	if (!StringUtil.isEmpty(labelStr)) {
    		mControls[1] = tableContainer;
        } else {
        	mControls[0] = tableContainer;
        }
		
		listTableManager = new ListTableManager(adTable, true);
		listTableManager.setAutoSizeFlag(true);		
		listTableManager.setIndexFlag(true);
		listTableManager.newViewer(tableContainer);
		
		listTableManager.addDoubleClickListener(new AbstractMouseSelectionAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
//				boolean saveFlag = true;
//				MaterialEventForm parentForm = (MaterialEventForm)getParent();
//            	for (IForm detailForm : parentForm.parentForm.getDetailForms()) {
//            		if (detailForm instanceof MaterialStatusModelForm) {
//						break;
//					} else {
//	            		if (!detailForm.saveToObject()) {
//							saveFlag = false;
//							break;
//						}
//					}
//				}
//            	
//            	if (!saveFlag) {
//            		return;
//            	}
//            	
//            	MaterialEvent materialEvent = (MaterialEvent)getParent().getObject();
//            	if (StringUtil.isEmpty(materialEvent.getObjectType())) {
//					return;
//            	}
//            	
//            	EventStatus eventStatus = (EventStatus)listTableManager.getSelectedObject();
//            	AddMMEventStatusDialog od = new AddMMEventStatusDialog(UI.getActiveShell(), eventStatus);
//				if(od.open() == IDialogConstants.OK_ID) {
//					EventStatus operation = od.getOperation();
//            		createPRDOperation(operation, eventStatus.getSeqNo().intValue());
//				}
			}
    	});
	}

	public void filter(List<Object> list) {
		setValue(list);
		refresh();
	}

	protected void createPRDOperation(EventStatus operation, int seqNo) {
		if (operation == null)
			return;
		try {
			if (seqNo != -1) {
				operation.setSeqNo(new Long(seqNo + 1));
			}
			if (values == null) {
				values = new ArrayList<Object>();
				values.add(operation);
			} else if (!values.contains(operation)) {
				values.add(operation);
			}
			filter(values);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void createPRDOperationContect(String operationContent, int seqNo) {
		EventStatus o = new EventStatus();
		o.setSeqNo(new Long(seqNo + 1));
	}

	public void createButtons(FormToolkit toolkit, Composite composite) {
		Composite bn = toolkit.createComposite(composite, SWT.NULL);
		bn.setLayout(new GridLayout(5, false));
		GridData g = new GridData();
		g.horizontalAlignment = GridData.END;
		bn.setLayoutData(g);
		append = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonAdd()), UIControlsFactory.BUTTON_DEFAULT);
		delete = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonDelete()),
				UIControlsFactory.BUTTON_DEFAULT);
		up = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonUp()), UIControlsFactory.BUTTON_DEFAULT);
		down = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonDown()), UIControlsFactory.BUTTON_DEFAULT);
		decorateButton(append);
		decorateButton(delete);
		decorateButton(up);
		decorateButton(down);
	}

	public void decorateButton(SquareButton button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		int widthHint = 88; // IDialogConstants.BUTTON_WIDTH
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}

	@Override
	public void refresh() {
		List<Object> val = (List<Object>) getValue();
		if (val != null) {
			listTableManager.setInput(val);
		} else {
			listTableManager.setInput(new ArrayList<Object>());
		}
	}
	
	public List<? extends Object> getInput() {
		return listTableManager.getInput();
	}
}
