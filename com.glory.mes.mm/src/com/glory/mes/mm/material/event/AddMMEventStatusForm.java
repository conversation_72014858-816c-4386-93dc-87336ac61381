package com.glory.mes.mm.material.event;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;

import com.glory.common.state.model.EventStatus;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.state.model.MaterialComClass;
import com.glory.mes.mm.state.model.MaterialState;

public class AddMMEventStatusForm extends EntityForm {

	private static final Logger logger = Logger.getLogger(AddMMEventStatusForm.class);
	
	private static final String TABLE_NAME = "MMEventStatus";
	
	private static String FIELDNAME_SOUCECOMCLASS = "sourceComClass";
	private static String FIELDNAME_SOUCESTATE = "sourceState";
	private static String FIELDNAME_TARGETCOMCLASS = "targetComClass";
	private static String FIELDNAME_TARGETSTATE = "targetState";
	
	private RefTableField fSourceComClass;
	private RefTableField fSourceState;
	private RefTableField fTargetComClass;
	private RefTableField fTargetState;
	
	private String objectType;
	
	public AddMMEventStatusForm(Composite parent, int style, Object object, String objectType) {
		super(parent, style, object, null);
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			this.table = table;
			this.objectType = objectType;
			createForm();
		}  catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
    public void createForm() {
		try {
			super.createForm();
			
			fSourceComClass = (RefTableField)this.getFields().get(FIELDNAME_SOUCECOMCLASS);
			fSourceState = (RefTableField)this.getFields().get(FIELDNAME_SOUCESTATE);
			fTargetComClass = (RefTableField)this.getFields().get(FIELDNAME_TARGETCOMCLASS);
			fTargetState = (RefTableField)this.getFields().get(FIELDNAME_TARGETSTATE);
			
			String whereClause = " objectType = '" + objectType + "'";
			ADManager adManager = Framework.getService(ADManager.class);
			List<MaterialComClass> comClassList = adManager.getEntityList(Env.getOrgRrn(), MaterialComClass.class, Env.getMaxResult(), whereClause, "");
			
			//拷贝作为目标大类
			List<MaterialComClass> targetComClassList = new ArrayList<MaterialComClass>();
			for (MaterialComClass comClass : comClassList) {
				targetComClassList.add((MaterialComClass)comClass.clone());
			}
			fTargetComClass.setInput(targetComClassList);
			
			//添加 all 选项
			MaterialComClass newComClass = new MaterialComClass();
			newComClass.setComClass(EventStatus.ALL_FLAG);
			newComClass.setDescription("All ComClass");
			comClassList.add(0, newComClass);
			fSourceComClass.setInput(comClassList);
			
			registeSourceChangeListener();
		
		}  catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void registeSourceChangeListener(){
		fSourceComClass.addValueChangeListener(new SourceComClassListener());
		fTargetComClass.addValueChangeListener(new TargetComClassListener());
	}
	
	private class SourceComClassListener implements IValueChangeListener {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			try {
				if (EventStatus.ALL_FLAG.equalsIgnoreCase((String)newValue)) {
					List<MaterialState> stateList = fSourceState.getInput();
					stateList.clear();
					MaterialState newState = new MaterialState();
					newState.setState(EventStatus.ALL_FLAG);
					newState.setDescription("All State");
					stateList.add(0, newState);
				} else {
					String whereClause = " comClass = '" + newValue + "' AND objectType = '" + objectType + "'";
					ADManager adManager = Framework.getService(ADManager.class);
					List<MaterialState> stateList = adManager.getEntityList(Env.getOrgRrn(), MaterialState.class, Env.getMaxResult(), whereClause, "");
					MaterialState newState = new MaterialState();
					newState.setState(EventStatus.ALL_FLAG);
					newState.setDescription("All State");
					stateList.add(0, newState);
					fSourceState.setInput(stateList);
				}
			} catch (Exception e){
				logger.error("RefTableField : valueChanged", e);
			}
		}
	}
	
	private class TargetComClassListener implements IValueChangeListener {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			try {
				if (EventStatus.ALL_FLAG.equalsIgnoreCase((String)newValue)) {
					List<MaterialState> stateList = fTargetState.getInput();
					stateList.clear();
					MaterialState newState = new MaterialState();
					newState.setState(EventStatus.ALL_FLAG);
					newState.setDescription("All State");
					stateList.add(0, newState);
				} else {
					String whereClause = " comClass = '" + newValue + "' AND objectType = '" + objectType + "'";
					ADManager adManager = Framework.getService(ADManager.class);
					List<MaterialState> stateList = adManager.getEntityList(Env.getOrgRrn(), MaterialState.class, Env.getMaxResult(), whereClause, "");
					fTargetState.setInput(stateList);
				}
			} catch (Exception e){
				logger.error("RefTableField : valueChanged", e);
			}
		}
	}
}
