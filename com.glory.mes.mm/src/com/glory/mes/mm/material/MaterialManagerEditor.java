package com.glory.mes.mm.material;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.IToolItemListener;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADAttributeValue;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.excel.download.Download;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.exception.MMExceptionBundle;
import com.glory.mes.mm.model.Material;
import com.glory.mes.mm.model.RawMaterial;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class MaterialManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.material.MaterialManagerEditor";

	private static final String FIELD_ATTRIBUTEVALUES = "attributeValues";
	
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_FROZEN = "frozen";
	private static final String BUTTON_ACTIVE = "active";
	private static final String BUTTON_INACTIVE = "inactive";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_EXPORT = "export";
	private static final String BUTTON_ENTITY_REFRESH = "entityRefresh";
	
	protected TableEditorField attributeValuesField;
	
	private EntityBlock block;
	private EntityForm entityForm;
	
	protected ToolItem itemSave;
	protected ToolItem itemDelete;
	protected ToolItem itemFrozen;
	protected ToolItem itemActive;
	protected ToolItem itemInActive;
	protected ToolItem itemImport;
	
	public RawMaterial material = new RawMaterial();

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_FROZEN), this::frozenAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ACTIVE), this::activeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_INACTIVE), this::inactiveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(IToolItemListener.TYPE_IMPORT), this::importAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EXPORT), this::exportAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ENTITY_REFRESH), this::entityRefesh);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChangedAdapter);
		
		attributeValuesField = form.getFieldByControlId(FIELD_ATTRIBUTEVALUES, TableEditorField.class);
		
		block = (EntityBlock) form.getBlock();
		entityForm = (EntityForm) form.getSubForms().get(0);
		
		init();
	}

	private void init() {
		itemSave = (ToolItem) form.getButtonByControl(null, BUTTON_SAVE);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
		itemFrozen = (ToolItem) form.getButtonByControl(null, BUTTON_FROZEN);
		itemActive = (ToolItem) form.getButtonByControl(null, BUTTON_ACTIVE);
		itemInActive = (ToolItem) form.getButtonByControl(null, BUTTON_INACTIVE);
		
		setAdObject(createAdObject());
	}
	
	private void selectionChangedAdapter(Object object) {
		try { 
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			RawMaterial material = (RawMaterial) selection.getFirstElement();
			setAdObject(material);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void newAdapter(Object object) {
		try { 
			entityForm.getMessageManager().removeAllMessages();
			setAdObject(createAdObject());
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings("unchecked")
	private void saveAdapter(Object object) {
		try { 
			entityForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : form.getSubForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : form.getSubForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					RawMaterial oldMaterial = getAdObject();
 					MMManager mmManager = Framework.getService(MMManager.class);
 					List<ADAttributeValue> attributes = Lists.newArrayList();
 					List<ADAttributeValue> inputAttributes = (List<ADAttributeValue>) attributeValuesField.getValue();
 					attributes.addAll(inputAttributes);
 					//M类型物料不需要状态模型，B和L类型需要
 					if (Material.BATCH_TYPE_MATERIAL.equals(oldMaterial.getBatchType()) && oldMaterial.getStatusModelRrn() != null) {
 						UI.showInfo(Message.getString(MMExceptionBundle.bundle.MTypeMaterialNoNeedStatusModel()));
 						return;
 					} else if (!Material.BATCH_TYPE_MATERIAL.equals(oldMaterial.getBatchType()) && oldMaterial.getStatusModelRrn() == null) {
 						UI.showInfo(Message.getString(MMExceptionBundle.bundle.PleaseSelectStatusModel()));
 						return;
					}
 					Material newMaterial = mmManager.saveMaterialAndAttributes(oldMaterial, VersionControl.STATUS_UNFROZNE, attributes, Env.getSessionContext());		
 					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					
 					ADManager adManager = Framework.getService(ADManager.class);			
					if (oldMaterial.getObjectRrn() == null)
						block.refreshAdd(newMaterial);
					else {
						block.refreshUpdate(newMaterial);
					}
					setAdObject((RawMaterial) adManager.getEntity(newMaterial));
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void frozenAdapter(Object object) {
		try { 
			entityForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : form.getSubForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					Material material = (RawMaterial) getAdObject();	
					MMManager mmManager = Framework.getService(MMManager.class);
					if (VersionControl.STATUS_UNFROZNE.equalsIgnoreCase(material.getStatus())) {						
						material = mmManager.saveMaterial(material, VersionControl.STATUS_FROZNE, Env.getSessionContext());						
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonFrozenSuccess()));
					} else if (VersionControl.STATUS_ACTIVE.equalsIgnoreCase(material.getStatus())) {
						material = mmManager.saveMaterial(material, VersionControl.STATUS_FROZNE, Env.getSessionContext());						
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonFrozenSuccess()));
					} else {						
						material = mmManager.saveMaterial(material, VersionControl.STATUS_UNFROZNE, Env.getSessionContext());					
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonUnFrozenSuccess()));
					}
 					ADManager adManager = Framework.getService(ADManager.class);	
 					setAdObject((RawMaterial) adManager.getEntity(material));
 					refresh();
					block.refreshUpdate(material);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void activeAdapter(Object object) {
		try { 
			entityForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : form.getSubForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					Material material = (RawMaterial) getAdObject();
					MMManager mmManager = Framework.getService(MMManager.class);
					material = mmManager.saveMaterial(material, VersionControl.STATUS_ACTIVE, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonActiveSuccess()));
 					ADManager adManager = Framework.getService(ADManager.class);			
 					setAdObject((RawMaterial) adManager.getEntity(material));
 					refresh();
//					block.refreshUpdate(material);
					block.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void inactiveAdapter(Object object) {
		try { 
			entityForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				Material material = (RawMaterial) getAdObject();
				
				MMManager mmManager = Framework.getService(MMManager.class);
				material = mmManager.saveMaterial(material, VersionControl.STATUS_INACTIVE, Env.getSessionContext());

				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
				
				ADManager adManager = Framework.getService(ADManager.class);			
				setAdObject((RawMaterial) adManager.getEntity(material));
				refresh();
				
				block.refreshUpdate(material);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void deleteAdapter(Object object) {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				RawMaterial material = getAdObject();
				if (material.getObjectRrn() != null) {
					MMManager mmManager = Framework.getService(MMManager.class);
					mmManager.deleteMaterial(material.getObjectRrn(), Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
					setAdObject(createAdObject());
					refresh();
					block.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void importAdapter(Object object) {
		//1.继承Upload类并重写cudEntityList方法 2.提供导出模板动态表名称替换IMPORT_TABLE
		MaterialUpload upload = new MaterialUpload(form.getAuthority(), null, "TABLE NAME");
		if (upload.getUploadProgress().init()) {
			if (upload.run()) {
				block.refresh();
			}
		}
	}

	private void exportAdapter(Object object) {
		//objects：需要导出的数据
		List<RawMaterial> objects = new ArrayList<>();
		if (((EntityBlock)form.getBlock()).getTableManager().getTableManager().getMultiSelectedObjects() != null) {
			objects = Lists.newArrayList(((EntityBlock)form.getBlock()).getTableManager().getTableManager().getMultiSelectedObjects())
					.stream().map(o -> ((RawMaterial)o)).collect(Collectors.toList());
		}
		if (CollectionUtils.isNotEmpty(objects)) {
			Download download = new Download(form.getAuthority(), null);
			if (download.getDownloadProgress().init()) {
				download.run(objects);
			}
		} else {
			DefaultDownloadWriter.exportTemplate(form.getAuthority(), null);
		}
	}
	private void entityRefesh(Object object) {
		refresh();
	}

	public void statusChanged(String newStatus) {
		if (VersionControl.STATUS_UNFROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(true);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(false);
		} else if (VersionControl.STATUS_FROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(true);
			itemInActive.setEnabled(false);
		} else if (VersionControl.STATUS_ACTIVE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(true);
		} else if (VersionControl.STATUS_INACTIVE.equals(newStatus)) {
		    itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
            itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
            itemFrozen.setEnabled(true);
            itemSave.setEnabled(false);
            itemDelete.setEnabled(false);
            itemActive.setEnabled(true);
            itemInActive.setEnabled(false);
        } else {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(true);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(false);
		}
	}
	
	public RawMaterial getAdObject() {
		return material;
	}
	
	public void setAdObject(RawMaterial newObject) {
		try {
			this.material = newObject;
			if (newObject != null) {
				statusChanged(newObject.getStatus());
				if (newObject.getObjectRrn() != null) {
					ADManager adManager = Framework.getService(ADManager.class);
					List<ADAttributeValue> adAttributeValues = adManager.getEntityAttributeValues(RawMaterial.OBJECTTYPE_RAW_MATERIAL, newObject.getObjectRrn());
					newObject.setAttributeValues(adAttributeValues);
					this.material = newObject;
					attributeValuesField.setValue(adAttributeValues);
					attributeValuesField.refresh();
				}
			} else {
				statusChanged("");
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public RawMaterial createAdObject() {
		RawMaterial material = new RawMaterial();
		material.setOrgRrn(Env.getOrgRrn());
		return material;
	}
	
	public void refresh() {
		for (IForm detailForm : form.getSubForms()) {
			detailForm.setObject(getAdObject());
			detailForm.loadFromObject();
		}
		entityForm.setObject(getAdObject());
		entityForm.loadFromObject();
		entityForm.getMessageManager().removeAllMessages();
	}
	
}