package com.glory.mes.mm.material;

import java.util.List;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.model.Material;

public class MaterialUpload extends Upload {

	public MaterialUpload(String name) {
		super(name);
	}
	
	public MaterialUpload(String authorityName, String buttonName, String name) {
		super(authorityName, buttonName, name);
	}

	@Override
	protected void cudEntityList() {
		try {
			List<ADBase> uploadList = progress.getUploadList(); 
	        List<ADBase> deleteList = progress.getDeleteList(); 
	        
			MMManager mmManager = Framework.getService(MMManager.class);
	        // 当前只支持一个记录一个事务，后续会增加要么全部成功要么全部失败的事务处理
	        if (uploadList != null && uploadList.size() > 0) {
	        	for (int i = 0; i < uploadList.size(); i++) {
	        		Material material  = (Material) uploadList.get(i);
	        		mmManager.saveMaterial(material, VersionControl.STATUS_UNFROZNE, Env.getSessionContext());		
				}
	        }
	        
	        if (deleteList != null && deleteList.size() > 0) {
	        	ADManager adManager = Framework.getService(ADManager.class);
	        	adManager.deleteEntityList(deleteList, Env.getSessionContext());
	        }
	        UI.showInfo(Message.getString("com.import_success"));
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
}
