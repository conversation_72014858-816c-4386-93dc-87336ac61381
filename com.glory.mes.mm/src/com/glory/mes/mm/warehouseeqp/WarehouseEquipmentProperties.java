package com.glory.mes.mm.warehouseeqp;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.mm.WarehouseEquipment;
import com.glory.framework.core.exception.ExceptionBundle;

public class WarehouseEquipmentProperties extends EntityProperties {

    private WarehouseEquipmentForm warehouseLineForm;

    public WarehouseEquipmentProperties() {
        super();
    }

    @Override
    protected EntityForm getForm(ADTab tab) {
        EntityForm itemForm;
        String tabName = tab.getName();
        if ("EquipmentWarehoseManagement".equalsIgnoreCase(tabName)) {
            warehouseLineForm = new WarehouseEquipmentForm(getTabs(), SWT.NONE, tab, mmng);
            return warehouseLineForm;
        } else {
            itemForm = new EntityForm(getTabs(), SWT.NONE, tab, mmng);
        }
        return itemForm;
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemSave(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void saveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            boolean saveFlag = true;
            for (IForm detailForm : getDetailForms()) {
                if (!detailForm.saveToObject()) {
                    saveFlag = false;
                }
            }
            if (saveFlag) {
                Equipment equipment = (Equipment) getAdObject();
                if (equipment == null || equipment.getObjectRrn() == null) {
                    UI.showInfo(Message.getString("mm.select_equipment"));
                    return;
                }
                List<Warehouse> selectWareHouseTargets = (List<Warehouse>) getField(
                        WarehouseEquipmentForm.FIELD_ID_TARGET).getValue();
     
                List<Warehouse> selectWareHouseSources = (List<Warehouse>) getField(
                        WarehouseEquipmentForm.FIELD_ID_SOURCE).getValue();
                
                Boolean warehouseEquipment = true;
                
                List<WarehouseEquipment> targets = new ArrayList<WarehouseEquipment>();
                if (selectWareHouseTargets != null && selectWareHouseTargets.size() > 0) {
                    WarehouseEquipment resource = null;
                    for (Warehouse warehouse : selectWareHouseTargets) {
                    	resource = new WarehouseEquipment();
                    	resource.setWarehouseRrn(warehouse.getObjectRrn());
                    	resource.setWarehouseId(warehouse.getWarehouseId());
                    	resource.setWarehouseType(warehouse.getWarehouseType());
                    	resource.setSeqNo(Long.parseLong(warehouse.getReserved5()));
                    	targets.add(resource);
                    }
                }else {
                	warehouseEquipment = false;
				}
                 
                List<WarehouseEquipment> resources = new ArrayList<WarehouseEquipment>();
                if (selectWareHouseSources != null && selectWareHouseSources.size() > 0) {
                    WarehouseEquipment resource = null;
                    for (Warehouse warehouse : selectWareHouseSources) {
                        resource = new WarehouseEquipment();
                        resource.setWarehouseRrn(warehouse.getObjectRrn());
                        resource.setWarehouseId(warehouse.getWarehouseId());
                        resource.setWarehouseType(warehouse.getWarehouseType());
                        resource.setSeqNo(Long.parseLong(warehouse.getReserved5()));
                        resources.add(resource);
                    }
                }else {
                	warehouseEquipment = false;
				}
                
                if(!warehouseEquipment) {
                	 UI.showInfo(Message.getString("mm.please_add_equiment_source_and_target_warehouse"));
                     return;
                }

                MLotManager mlotManager = Framework.getService(MLotManager.class);
                mlotManager.saveEquipmentWarehouse(equipment, targets, resources,
                        Env.getSessionContext());

                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
                refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
    public void refresh() {
        super.refresh();
        try {
            Equipment equipment = (Equipment) getAdObject();
            TableSelectField target = (TableSelectField) getField("warehouseId");
            TableSelectField source = (TableSelectField) getField("warehouseType");

            if (equipment != null && equipment.getObjectRrn() != null) {
                ADManager adManager = Framework.getService(ADManager.class);
                List<WarehouseEquipment> warehouseEquipments = adManager.getEntityList(
                        Env.getOrgRrn(), WarehouseEquipment.class, Integer.MAX_VALUE, " eqpId = '" + equipment.getEquipmentId() + "'", " seqNo asc ");

                List<Warehouse> targets = new ArrayList<Warehouse>();
                List<Warehouse> sources = new ArrayList<Warehouse>();
                
                for (WarehouseEquipment warehouseEquipment : warehouseEquipments) {
                    Warehouse warehouse = new Warehouse();
                    warehouse.setObjectRrn(warehouseEquipment.getWarehouseRrn());
                    warehouse = (Warehouse) adManager.getEntity(warehouse);
                    if(warehouseEquipment.getSeqNo()!=null)
                    	warehouse.setReserved5(warehouseEquipment.getSeqNo().toString());
                    else
                    	warehouse.setReserved5("1");
                    		
                    if (WarehouseEquipment.IN_OUT_TYPE_TARGET.equals(warehouseEquipment.getType())) {
                        targets.add(warehouse);
                    } else {
                        sources.add(warehouse);
                    }
                }

                target.setValue(targets);
                source.setValue(sources);

                target.refresh();
                source.refresh();
            } else {
                target.setValue(null);
                source.setValue(null);
                target.refresh();
                source.refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
       
}
