package com.glory.mes.mm.materialwarehouse;

import java.util.List;

import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.WarehouseMaterial;
import com.glory.framework.core.exception.ExceptionBundle;

public class MaterialWarehouseEditor extends GlcEditor{
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.materialwarehouse.MaterialWarehouseEditor";
	
	private static final String FIELD_QUERT_FROM = "queryForm";
	private static final String FIELD_ENTITY_FROM = "entityForm";
	
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_REFRESH = "refresh";
	
	private QueryFormField queryFormField;
	
	private EntityFormField entityFormField;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		queryFormField = form.getFieldByControlId(FIELD_QUERT_FROM, QueryFormField.class);
		
		entityFormField = form.getFieldByControlId(FIELD_ENTITY_FROM, EntityFormField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_QUERT_FROM + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChange);	
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::newAdaptor);
		
		entityFormField.setValue(new WarehouseMaterial());
		entityFormField.refresh();
	}
	
	public void selectionChange (Object object) {     
		try {
			Event event = (Event) object;
			WarehouseMaterial warehouseMaterial = (WarehouseMaterial) event.getProperty(GlcEvent.PROPERTY_DATA);
			entityFormField.setValue(warehouseMaterial);
			entityFormField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    }
	
	public void newAdaptor (Object object) {     
		try {
			refersh(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    }
	
	public void saveAdaptor (Object object) {     
		try {
			form.getMessageManager().removeAllMessages();
			ADManager adManager = Framework.getService(ADManager.class);
			if (entityFormField.validate()) {
				WarehouseMaterial warehouseMaterial = (WarehouseMaterial) entityFormField.getValue();
				if (warehouseMaterial.getObjectRrn() == null) {
					List<WarehouseMaterial> warehouseMaterials = adManager.getEntityList(Env.getOrgRrn(), WarehouseMaterial.class, Integer.MAX_VALUE,
							"warehouseId = '"+warehouseMaterial.getWarehouseId()+"' and materialName = '"+warehouseMaterial.getMaterialName()+"'", "");
					if (warehouseMaterials != null && warehouseMaterials.size() > 0) {
						UI.showInfo(Message.getString("mm.this_material_alm_rule_is_exist"));
						return;
					}
				}
				warehouseMaterial.setOrgRrn(Env.getOrgRrn());
				warehouseMaterial = (WarehouseMaterial) adManager.saveEntity(warehouseMaterial, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				refersh(warehouseMaterial);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    }
	
	public void deleteAdaptor (Object object) {     
		try {
			form.getMessageManager().removeAllMessages();
			ADManager adManager = Framework.getService(ADManager.class);
			WarehouseMaterial warehouseMaterial = (WarehouseMaterial) entityFormField.getValue();
			if (warehouseMaterial != null && warehouseMaterial.getObjectRrn() != null) {
				adManager.deleteEntity(warehouseMaterial, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));// 弹出提示框
				refersh(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    }
	
	public void refersh (WarehouseMaterial warehouseMaterial) {     
		try {
			if (warehouseMaterial != null) {
				queryFormField.refresh();
			} else {
				entityFormField.setValue(new WarehouseMaterial());
				entityFormField.refresh();
				queryFormField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    }
	
}

