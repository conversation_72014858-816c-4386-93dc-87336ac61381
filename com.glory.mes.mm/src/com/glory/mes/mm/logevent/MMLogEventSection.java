package com.glory.mes.mm.logevent;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.state.model.Event;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;

public class MMLogEventSection extends EntitySection {
	private static final Logger logger = Logger.getLogger(EntitySection.class);

	protected ToolItem itemLogEvent;
	protected LogEventForm itemForm;
	protected MMManager mmManager;
	protected IField mLotRefTableField;
	protected ADField mLotIdField;
	private static final String FIELD_ID_MLOT_ID = "mLotId";
	private static final String FIELD_DISPLAY_TYPE = "reftable";

	public MMLogEventSection(ADTable table) {
		super(table);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(new GridData(400, 30));
		Label label = toolkit.createLabel(top, Message.getString("mm.mlot_id") + ":");
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			ADRefTable refTable = new ADRefTable();
			for (ADTab tab : table.getTabs()) {
				for (ADField adField : tab.getFields()) {
					if (FIELD_ID_MLOT_ID.equals(adField.getName())
							&& FIELD_DISPLAY_TYPE.equals(adField.getDisplayType())) {
						mLotIdField = adField;
					}
				}
			}
			refTable.setObjectRrn(mLotIdField.getReftableRrn());
			refTable = (ADRefTable) entityManager.getEntity(refTable);
			ADTable adTable = entityManager.getADTable(refTable.getTableRrn());
			mLotRefTableField = new SearchField("", adTable, refTable, "", SWT.NULL);

			mLotRefTableField.setLabel(null);
			mLotRefTableField.addValueChangeListener(mLotRefTableListener);
			mLotRefTableField.createContent(top, toolkit);
		} catch (Exception e1) {
			logger.error("LogEventSection : creatSectionTitle", e1);
		}
	}

	public MLot searchMLot(String mLotId) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			return mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
		} catch (Exception e) {
			logger.error("LogEventSection searchLotEntity(): MLot isn' t exsited!");
		}
		return null;
	}

	public void initAdObject() {
		setAdObject(new MLot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void saveAdapter() {
		MLot mLot = (MLot) getAdObject();
		if (null == getField(LogEventForm.FIELDID_EVENT).getData()) {
			UI.showInfo(Message.getString("mm.logevent_mustInput"));
			return;
		}
		if (null != mLot.getObjectRrn()) {
			if (mLot.getStatusModelRrn() == null) {
				return;
			}

			Event mmEvent = (Event) getField(LogEventForm.FIELDID_EVENT).getData();
			String comment = (String) getField(LogEventForm.FIELDID_COMMENT).getValue();
			try {
				mmManager = Framework.getService(MMManager.class);
				mmManager.logEvent(mLot, mmEvent.getEventId(), comment, Env.getSessionContext());
				UI.showInfo(Message.getString("mm.logevent_success"));
				refresh();
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}

		}
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		itemForm = new LogEventForm(composite, SWT.NONE, new MLot(), tab, mmng);
		return itemForm;
	}

	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
	}
	
	//当MLot改变时
	IValueChangeListener mLotRefTableListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			try {
				MLot mLot = searchMLot(newValue.toString());
				setAdObject(mLot);
				refresh();
			} catch (Exception e) {
				logger.error("LogEventSection : IValueChangeListener", e);
			}
		}
	};

	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
				MLot mLot = (MLot)getAdObject();
				itemForm.setObject(mLot);
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		super.refresh();
	}

}
