package com.glory.mes.mm.query;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.export.NatExporter;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotScrap;
import com.glory.mes.mm.mlot.action.dialog.MMLotChangeShelfLifeDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotCompScrapDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotCompUnScrapDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotDetailDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotHisQueryDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotHoldDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotInDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotMergeDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotOffShelfDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotOnShelfDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotOutDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotReleaseDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotReturnDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotScrapDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotSplitDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotTransferDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotUnScrapDialog;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.framework.core.exception.ExceptionBundle;

public class MLotQueryGlcEditor extends GlcEditor{
	
	public static final Logger logger = Logger.getLogger(MLotQueryGlcEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.query.WIPQueryGlcEditor";
	
	private static final String FIELD_QUERYFORM = "mlotQuery";
	
	private QueryFormField queryField;
	protected NatTable natTable;
	
	private static final String BUTTON_IN = "in";
	private static final String BUTTON_ONSHELF = "onShelf";
	private static final String BUTTON_OFFSHELF = "offShelf";
	private static final String BUTTON_OUT = "out";
	private static final String BUTTON_TRANSFER = "transfer";
	private static final String BUTTON_RETURN = "return";
	private static final String BUTTON_CHANGESHELFLIFE = "changeShelfLife";
	private static final String BUTTON_HOLD = "hold";
	private static final String BUTTON_RELEASE = "release";
	private static final String BUTTON_SCRAP = "scrap";
	private static final String BUTTON_QUERY = "query";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_HISQUERY = "hisQuery";
	private static final String BUTTON_UNSCRAP = "unScrap";
	private static final String BUTTON_SPLIT = "split";
	private static final String BUTTON_MERGE = "merge";
	private static final String BUTTON_DETAIL = "detail";
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		queryField = form.getFieldByControlId(FIELD_QUERYFORM, QueryFormField.class);
		queryField.getQueryForm().getTableManager().setAutoSizeFlag(true);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_IN), this::inAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ONSHELF), this::onShelfAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_OFFSHELF), this::offShelfAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_OUT), this::outAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_TRANSFER), this::transferAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RETURN), this::returnAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHANGESHELFLIFE), this::changeShelfLifeAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HOLD), this::holdAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RELEASE), this::releaseAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SCRAP), this::scrapAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_QUERY), this::queryAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HISQUERY), this::hisQueryAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNSCRAP), this::unScrapAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SPLIT), this::splitAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MERGE), this::mergeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DETAIL), this::detailAdapter);
		queryField.getQueryForm().getTableManager().refresh();	
	}
	
	private void hisQueryAdapter(Object obj) {
		try {
			List<Object> objs = queryField.getCheckedObjects();
			List<MLot> mLots = objs.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
			MMLotHisQueryDialog baseDialog = new MMLotHisQueryDialog("MMLotHisQueryDialog", null, eventBroker, mLots);
			if (Dialog.OK == baseDialog.open()) {
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void unScrapAdapter(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = objects.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
			ADManager adManager = Framework.getService(ADManager.class);
			MLot mlot = checkMlots.get(0);
			Boolean isUnScrapComponent = true;
			if (ProcessUnit.UNIT_TYPE_QTY.equals(mlot.getSubUnitType())) {
				isUnScrapComponent = false;
			}
			List<MLotScrap> mLotScraps = new ArrayList<MLotScrap>();
			if (mlot != null && mlot.getObjectRrn() != null) {
				//查询报废数据
				mLotScraps = adManager.getEntityList(Env.getOrgRrn(), MLotScrap.class, Integer.MAX_VALUE, 
						"mLotRrn = '"+mlot.getObjectRrn()+"'", ""); 
				if (CollectionUtils.isEmpty(mLotScraps)) {
					UI.showInfo(Message.getString("mm.mlot_scarp_record_not_found"));
					return;
				}
				for (MLotScrap mLotScrap : mLotScraps) {
					if (isUnScrapComponent) {
						MComponentUnit componentUnit = new MComponentUnit();
						componentUnit.setObjectRrn(mLotScrap.getmComponentRrn());
						componentUnit = (MComponentUnit) adManager.getEntity(componentUnit);
						// 片当前物料批RRN
						mLotScrap.setAttribute1(componentUnit.getParentMLotRrn());
						// 带入原position
						mLotScrap.setAttribute2(componentUnit.getPosition());
					} else {
						// 物料批RRN
						mLotScrap.setAttribute1(mLotScrap.getmLotRrn());
					}
					mLotScrap.setSubQty(mLotScrap.getSubQty() == null ? BigDecimal.ZERO : mLotScrap.getSubQty());
					// 反报废数量
					mLotScrap.setUnScrapMainQty(mLotScrap.getMainQty());
					mLotScrap.setUnScrapSubQty(mLotScrap.getSubQty());
					// 反报废代码
					mLotScrap.setUnScrapCode("");
					// 备注
					mLotScrap.setUnScrapComment("");
				}
			}
			if (!isUnScrapComponent) {
				MMLotUnScrapDialog baseDialog = new MMLotUnScrapDialog("MMLotUnScrapDialog", null, eventBroker, checkMlots, mLotScraps, isUnScrapComponent);
				if (Dialog.OK == baseDialog.open()) {
					refreshAdapter(object);
				}
			} else {
				MMLotCompUnScrapDialog baseDialog = new MMLotCompUnScrapDialog("MMLotCompUnScrapDialog", null, eventBroker, checkMlots, mLotScraps, isUnScrapComponent);
				if (Dialog.OK == baseDialog.open()) {
					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void splitAdapter(Object obj) {
		try {
			List<Object> objs = queryField.getCheckedObjects();
			if(CollectionUtils.isEmpty(objs)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> mLots = objs.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
			if (mLots.get(0) == null || BigDecimal.ZERO.compareTo(mLots.get(0).getMainQty()) >= 0) {
				UI.showInfo(Message.getString("mm.mlot_qty_less_than_zero"));
				return;
			}
			MMLotSplitDialog baseDialog = new MMLotSplitDialog("MMLotSplitDialog", null, eventBroker, mLots, this);
			if (Dialog.OK == baseDialog.open()) {
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void mergeAdapter(Object obj) {
		try {
			List<Object> objs = queryField.getCheckedObjects();
			if(CollectionUtils.isEmpty(objs)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> mLots = objs.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
			MMLotMergeDialog baseDialog = new MMLotMergeDialog("MMLotMergeDialog", null, eventBroker, mLots, this);
			if (Dialog.OK == baseDialog.open()) {
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void detailAdapter(Object obj) {
		try {
			List<Object> objs = queryField.getCheckedObjects();
			List<MLot> mLots = objs.stream().map(o -> ((MLot)o)).collect(Collectors.toList());
			MMLotDetailDialog baseDialog = new MMLotDetailDialog("MMLotDetailDialog", null, eventBroker, mLots);
			if (Dialog.OK == baseDialog.open()) {
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	// 入库按钮
	protected void inAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			if (!checkMLotState(checkMlots, MaterialEvent.EVENT_INVENTORYIN)) {
				return;
			}

			MMLotInDialog baseDialog = new MMLotInDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 上架按钮
	protected void onShelfAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			MMLotOnShelfDialog baseDialog = new MMLotOnShelfDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 下架按钮
	protected void offShelfAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			MMLotOffShelfDialog baseDialog = new MMLotOffShelfDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 出库按钮
	protected void outAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			if (!checkMLotState(checkMlots, MaterialEvent.EVENT_INVENTORYOUT)) {
				return;
			}
			MMLotOutDialog baseDialog = new MMLotOutDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 移库按钮
	protected void transferAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			MMLotTransferDialog baseDialog = new MMLotTransferDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 返库按钮
	protected void returnAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
			if (!checkMLotState(checkMlots, MaterialEvent.EVENT_INVENTORYRETURN)) {
				return;
			}
			
			MMLotReturnDialog baseDialog = new MMLotReturnDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 修改有效期按钮
	protected void changeShelfLifeAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
			List<MLot> checkMlots = new ArrayList<MLot>();
			for(Object obj : objects) {
				MLot mlot = (MLot)obj;
				if (MLot.STATE_COM.equals(mlot.getComClass())) {
					UI.showError(mlot.getmLotId() + Message.getString("mm.mlot_state_not_allowed"));
					return;
				}
				checkMlots.add(mlot);
			}

			MMLotChangeShelfLifeDialog baseDialog = new MMLotChangeShelfLifeDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	// 暂停按钮
	protected void holdAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = new ArrayList<MLot>();
			for(Object obj : objects) {
				MLot mlot = (MLot)obj;
				if (MLot.STATE_COM.equals(mlot.getComClass())) {
					UI.showError(mlot.getmLotId() + Message.getString("mm.mlot_state_not_allowed"));
					return;
				}
				if (MLot.HOLDSTATE_ON.equals(mlot.getHoldState())) {
					UI.showError(mlot.getmLotId() + Message.getString("mm.mlots_already_hold"));
					return;
				}
				checkMlots.add(mlot);
			}
			
			MMLotHoldDialog baseDialog = new MMLotHoldDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	// 报废按钮
	protected void scrapAdaptor(Object object) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			List<MLot> checkMlots = new ArrayList<MLot>();
			for(Object obj : objects) {
				MLot mlot = (MLot)obj;
				if (MLot.STATE_COM.equals(mlot.getComClass())) {
					UI.showError(mlot.getmLotId() + Message.getString("mm.mlot_state_not_allowed"));
					return;
				}
				checkMlots.add(mlot);
			}
			if (ProcessUnit.UNIT_TYPE_QTY.equals(checkMlots.get(0).getSubUnitType())) {
				MMLotScrapDialog baseDialog = new MMLotScrapDialog(null, null, eventBroker, checkMlots);
				if (!baseDialog.preValidate()) {
					return;
				}
				if (Dialog.OK == baseDialog.open()) {
					refreshAdapter(object);
				}
			} else {
				MLot mLotComponent = mmManager.getMLotWithComponent(checkMlots.get(0).getObjectRrn());
            	MMLotCompScrapDialog dialog = new MMLotCompScrapDialog("MMLotCompScrapDialog", null, eventBroker, checkMlots, mLotComponent.getSubMComponentUnit());
            	if (dialog != null && dialog.open() == Dialog.OK) {
            		refreshAdapter(object);
            	}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	// 放行按钮
	protected void releaseAdaptor(Object object) {
		try {
			List<Object> objects = queryField.getQueryForm().getCheckedObject();
			if(CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
			List<MLot> checkMlots = new ArrayList<MLot>();
			for(Object obj : objects) {
				MLot mlot = (MLot)obj;
				if (!MLot.HOLDSTATE_ON.equals(mlot.getHoldState())) {
					UI.showError(mlot.getmLotId() + Message.getString("mm.mlot_state_not_allowed"));
					return;
				}
				checkMlots.add(mlot);
			}
			
			MMLotReleaseDialog baseDialog = new MMLotReleaseDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	// 刷新按钮
	public void refreshAdapter(Object object) {
		queryField.getQueryForm().refresh();
		queryField.refresh();
	}
	
	// 查询按钮
	private void queryAdapter(Object object) {
		try {
			queryField.getQueryForm().refresh();
			queryField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	// 状态效验
	public boolean checkMLotState(List<MLot> mLots, String event) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			return mmManager.checkMLotState(mLots, event, true, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
	
	public void split(MLot mLot, List<MLot> subLots) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			mmManager.splitMLot(mLot, subLots, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
}
