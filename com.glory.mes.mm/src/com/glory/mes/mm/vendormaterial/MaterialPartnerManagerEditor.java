package com.glory.mes.mm.vendormaterial;

import java.util.List;

import org.eclipse.jface.viewers.StructuredSelection;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.model.MaterialPartner;
import com.glory.framework.core.exception.ExceptionBundle;

public class MaterialPartnerManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.vendormaterial.MaterialPartnerManagerEditor";

	public static final String BUTTON_SAVE = "save";
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_DELETE = "delete";

	public MaterialPartner partner = new MaterialPartner();
	
	private EntityBlock block;
	private EntityForm entityForm;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		form.unsubscribeDefaultEvent(form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED));
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChangedAdapter);
		
		block = (EntityBlock) form.getBlock();
		entityForm = (EntityForm) form.getSubForms().get(0);
	}

	private void selectionChangedAdapter(Object object) {
		try { 
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			MaterialPartner partner = (MaterialPartner) selection.getFirstElement();
			setAdObject(partner);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void newAdapter(Object object) {
		try { 
			entityForm.getMessageManager().removeAllMessages();
			setAdObject(createAdObject());
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void saveAdapter(Object object) {
		try {
			if (getAdObject() != null) {	
				ADBase oAdBase = getAdObject();
				boolean saveFlag = true;
				for (IForm detailForm : form.getSubForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				
				if (saveFlag) {
					for (IForm detailForm : form.getSubForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					MaterialPartner mp = (MaterialPartner) getAdObject();
					ADManager adManager = Framework.getService(ADManager.class);
					if (mp.getObjectRrn() == null) {
						List<MaterialPartner> bPartnerMaterials = adManager.getEntityList(Env.getOrgRrn(), MaterialPartner.class, Integer.MAX_VALUE, 
								"materialRrn='" + mp.getMaterialRrn() + "' and partnerRrn='" + mp.getPartnerRrn() + "'","");
						if (bPartnerMaterials.size() > 0){
							UI.showError(Message.getString("error.material_has_vendor_already"));
							return;
						}
						List<MaterialPartner> bMaterials = adManager.getEntityList(Env.getOrgRrn(), MaterialPartner.class, Integer.MAX_VALUE, 
								" materialName='" + mp.getMaterialName() + "'  and isMain = 'Y' ", "");
						if (bMaterials.size() >= 1 && mp.getIsMain()) {
							UI.showError(Message.getString("material_is_main_suppliers"));// 弹出提示框
							return;
						}
					}
					
					MaterialPartner bPartnerMaterial = (MaterialPartner) adManager.saveEntity(mp, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框	
					if (oAdBase.getObjectRrn() == null){
						block.refreshAdd(bPartnerMaterial);
					} else {
						block.refreshUpdate(bPartnerMaterial);
					}
					setAdObject(bPartnerMaterial);
					refresh();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void deleteAdapter(Object object) {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				if (getAdObject().getObjectRrn() != null) {
					ADManager entityManager = getADManger();
					entityManager.deleteEntity(getAdObject(), Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
					setAdObject(createAdObject());
					refresh();
					block.refresh();
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}
	
	public void refresh() {
		for (IForm detailForm : form.getSubForms()) {
			detailForm.setObject(getAdObject());
			detailForm.loadFromObject();
		}
		entityForm.setObject(getAdObject());
		entityForm.loadFromObject();
		entityForm.getMessageManager().removeAllMessages();
	}
	
	public MaterialPartner createAdObject() {
		MaterialPartner materialPartner = new MaterialPartner();
		materialPartner.setOrgRrn(Env.getOrgRrn());
		return materialPartner;
	}
	
	public MaterialPartner getAdObject() {
		return partner;
	}
	
	public void setAdObject(MaterialPartner partner) {
		this.partner = partner;
	}

}