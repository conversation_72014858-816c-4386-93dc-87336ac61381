package com.glory.mes.mm.durable.spec;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.base.entitymanager.forms.VersionControlProperties;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableSpec;

public class DurableSpecProperties extends VersionControlProperties {
	
	public DurableSpecProperties () {
		super();
	}
	
	@Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemNew(tBar);
        createToolItemCopyFrom(tBar);
        createToolItemSave(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemFrozen(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemActive(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemDelete(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }
	
	@Override
	public void saveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if ( getAdObject() != null ) {	
				DurableSpec old = (DurableSpec) getAdObject();
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if ( saveFlag ) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					DurableSpec spec = (DurableSpec) getAdObject();
					ADManager adManager = Framework.getService(ADManager.class);
					if (spec.getVersion() == null) {
						List<DurableSpec> specList = adManager.getEntityList(Env.getOrgRrn(), DurableSpec.class, Integer.MAX_VALUE, 
								" name = '" + spec.getName() + "'", "version desc");
						if (specList.size() > 0) {
							boolean flag = UI.showConfirm(spec.getName() 
									+ Message.getString("prd.version_infor") 
									+ String.valueOf(specList.get(0).getVersion() + 1));
							if (!flag) {
								return;
							}
						}			
					}
					DurableManager durableManager = Framework.getService(DurableManager.class);
					spec = durableManager.saveDurableSpec(old, Env.getSessionContext());
					spec = (DurableSpec) adManager.getEntity(spec);		
					if (old.getObjectRrn() == null) {
						getMasterParent().refreshAdd(spec);
					} else {
						getMasterParent().refreshUpdate(spec);
					}
					
					setAdObject(spec);				
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					refresh();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void activeAdapter() {
		try {
			if (getAdObject() != null) {
				DurableSpec spec = (DurableSpec) getAdObject();
				//如果是激活状态，此动作为失效动作
				if (VersionControl.STATUS_ACTIVE.equals(spec.getStatus())) {
					ADManager adManager = Framework.getService(ADManager.class);
					List<Carrier> carriers = adManager.getEntityList(Env.getOrgRrn(), Carrier.class, 1,
							"durableSpecName = '" + spec.getName() + "' and durableSpecVersion = " + spec.getVersion(), "");
					if (carriers != null && carriers.size() > 0) {
						UI.showError(Message.getString(ExceptionBundle.bundle.ConstraintViolation(spec.getName()).getErrorCode()));
						return;
					}
					BASManager basManager = Framework.getService(BASManager.class);
					spec = (DurableSpec) basManager.inActive(spec, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
					setAdObject(spec);
					getMasterParent().refreshUpdate(getAdObject());
					refresh();
				} else {
					super.activeAdapter();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void deleteAdapter() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				form.getMessageManager().setAutoUpdate(false);
				form.getMessageManager().removeAllMessages();
				if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
					DurableSpec spec = (DurableSpec) getAdObject();
					DurableManager durableManager = Framework.getService(DurableManager.class);
					durableManager.deleteDurableSpec(spec, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
					setAdObject(createAdObject());
					refresh();
					getMasterParent().refresh();					
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void statusChanged(String newStatus) {
		buildTitle(newStatus);
		if (VersionControl.STATUS_UNFROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(true);
			itemActive.setEnabled(false);
			setEnable(true);
		} else if (VersionControl.STATUS_FROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(true);
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonActive()));
			
			setEnable(false);
		} else if (VersionControl.STATUS_ACTIVE.equals(newStatus)) {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			
			itemActive.setEnabled(true);
			itemActive.setImage(SWTResourceCache.getImage("inactive"));
			itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonInActive()));
			
			setEnable(false);
		}  else if (VersionControl.STATUS_INACTIVE.equals(newStatus)) {
			itemFrozen.setEnabled(true);
			itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
			
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(true);
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonActive()));
			setEnable(false);
		} else {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(false);
			setEnable(true);
		}
	}
}
