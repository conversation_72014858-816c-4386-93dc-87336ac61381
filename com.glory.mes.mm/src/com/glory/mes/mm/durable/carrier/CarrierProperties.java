package com.glory.mes.mm.durable.carrier;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ExportToolItemGlc;
import org.eclipse.swt.widgets.ImportToolItemGlc;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableSpec;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class CarrierProperties extends EntityProperties {
	
	public ToolItem itemChangeSpec;
	public static String TABLE_NAME = "RASDurableSpecChange";
	public static String KEY_CHENGESPEC = "ChangeSpec";
	public static final String TEMPLATE_FILE_NAME = "carrier_template.xlsx";
	protected ToolItem itemImport;
	protected ExportToolItemGlc itemExport;
	
	public CarrierProperties() {
		super();	
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
//		createToolItemChangSpec(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemNew(tBar);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	

	private void createToolItemImport(ToolBar tBar) {
		itemImport = new ImportToolItemGlc(tBar, null, getTable().getAuthorityKey(), null, null);
		itemImport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				CarrierUpload uploads = new CarrierUpload(getTable().getAuthorityKey(), null, getTable().getName());
				if (uploads.getUploadProgress().init()) {
					if (uploads.run()) {
						refresh();
						getMasterParent().refresh();
					}
				}
			}
		});
	}
	
	protected void createToolItemExport(ToolBar tBar) {
		itemExport = new ExportToolItemGlc(tBar, null, getTable().getAuthorityKey(), null, null);
		itemExport.setImage(SWTResourceCache.getImage("export"));
		itemExport.setText(Message.getString(ExceptionBundle.bundle.CommonExport()));
		itemExport.setEnabled(true);
		itemExport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				exportAdapter();
			}
		});
	}
	
	protected void exportAdapter() {
		try {
			itemExport.exportAdapter(getMasterParent().getTableManager().getTableManager());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void createToolItemChangSpec(ToolBar tBar){
		itemChangeSpec = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_CHENGESPEC);
		itemChangeSpec.setImage(SWTResourceCache.getImage("change_spec"));
		itemChangeSpec.setText(Message.getString("mm.durable_change_spec"));
		itemChangeSpec.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeSpecAdapter();
			}
		});
	}
	
	protected void createSectionContent(Composite client) {
		super.createSectionContent(client);
		final FormToolkit toolkit = form.getToolkit();
		mmng = form.getMessageManager();
		Composite btnComposite = toolkit.createComposite(client, SWT.NONE);
		GridLayout layoutBtn = new GridLayout(1, false);
		btnComposite.setLayout(layoutBtn);					
		GridData gd1 = new GridData(GridData.FILL_BOTH);		
		gd1.horizontalAlignment = SWT.RIGHT;		
		btnComposite.setLayoutData(gd1);	
		RefTableField refTableField = (RefTableField) getField("durableSpecName");
		refTableField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object arg0, Object arg1) {
				RefTableField tempFiled = (RefTableField) arg0;
				DurableSpec durableSpec = (DurableSpec)tempFiled.getData();
				if (durableSpec != null) {
					BooleanField booleanField = (BooleanField) getField("isGlobal");
					if (booleanField != null) {
						if (durableSpec.getIsGlobal()) {
							booleanField.setData(true);
							booleanField.setValue(true);
							booleanField.refresh();
						} else {
							booleanField.setData(false);
							booleanField.setValue(false);
							booleanField.refresh();
						}
					}
					RefTableField statusModelField = (RefTableField) getField("statusModel");
					if (statusModelField != null) {
						if (durableSpec.getStatusModelRrn() != null) {
							statusModelField.setValue(String.valueOf(durableSpec.getStatusModelRrn()));
						} else {
							statusModelField.setValue("");
						}
					}
					
				}
			}
		});
	}
	
	@Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().setAutoUpdate(false);
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();			
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());		
					}
					DurableManager durableManager = Framework.getService(DurableManager.class);
					Carrier obj = (Carrier) durableManager.saveDurable((Carrier) getAdObject(), Env.getSessionContext());
					
					ADManager entityManager = Framework.getService(ADManager.class);
					ADBase newBase = entityManager.getEntity(obj);
					if (oldBase.getObjectRrn() == null) {
						getMasterParent().refreshAdd(newBase);
					} else {
						getMasterParent().refreshUpdate(newBase);
					}
					
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));				
					//设置当前选中值
					getMasterParent().getTableManager().setSelection(new StructuredSelection(new Object[] {newBase}));
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			form.getMessageManager().setAutoUpdate(true);
		}
	}

	@Override
	public boolean delete() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				if (getAdObject().getObjectRrn() != null) {
					Carrier carrier =(Carrier) getAdObject();
					if(Carrier.STATE_AVAILABLE.equals(carrier.getState())) {
						List<Lot> lots = getADManger().getEntityList(Env.getOrgRrn(), Lot.class, 1,
								"durable ='" + carrier.getDurableId() + "'", "");
						if(CollectionUtils.isNotEmpty(lots)) {
							UI.showError(Message.getString("mm.is_carrier_mandatory_to_delete"));
							return false;
						}
					}
					
					DurableManager durableManager = Framework.getService(DurableManager.class);
					durableManager.deleteDurable(carrier, Env.getSessionContext());
					setAdObject(createAdObject());
					refresh();
					return true;
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		return false;
	}
	
	public void changeSpecAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			form.getMessageManager().setAutoUpdate(true);
			boolean saveFlag = true;
			for(IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				for (IForm detailForm : getDetailForms()) {
					PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());		
				}
				
				ADManager adManager = Framework.getService(ADManager.class);
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
				Carrier carrier = (Carrier) getAdObject();
				ChangeDurableSpecDialog dialog = new ChangeDurableSpecDialog(adTable, carrier);
				if (dialog.open() == Dialog.OK) {
					DurableManager durableManager = Framework.getService(DurableManager.class);
					Carrier newCarrier = (Carrier) dialog.getAdObject();
					
					newCarrier = (Carrier) durableManager.saveDurable(newCarrier, Env.getSessionContext());
					setAdObject(adManager.getEntity(newCarrier));
					refresh();
					getMasterParent().refreshUpdate(newCarrier);					
				}
			}					
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public ADBase createAdObject() throws Exception {
		Carrier base = (Carrier) getAdObject().getClass().newInstance();
		base.setOrgRrn(Env.getOrgRrn());
		ADManager adManager = Framework.getService(ADManager.class);
		base.setCleanDate(adManager.getSysDate());
		return base;
	}
	
	@Override
	public void newAdapter() {
		super.newAdapter();
	}
	
	public void refresh() {				
		if (getAdObject() != null) { 
			for (IForm detailForm : getDetailForms()) {
				detailForm.setObject(getAdObject());
				detailForm.loadFromObject();
			}
			
			Carrier carrier = (Carrier)getAdObject();	
			BooleanField booleanField = (BooleanField) getField("isGlobal");
			if (booleanField != null) {
				if (carrier.getOrgRrn() == 0l) {
					booleanField.setData(true);
					booleanField.setValue(true);
					booleanField.refresh();
				} else {
					booleanField.setData(false);
					booleanField.setValue(false);
					booleanField.refresh();
				}
			}
		}
	}

}
