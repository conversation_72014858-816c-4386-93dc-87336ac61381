package com.glory.mes.mm.durable.carrier;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.excel.UploadErrorDialog;
import com.glory.framework.base.excel.UploadErrorLog;
import com.glory.framework.base.excel.UploadException;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableSpec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class CarrierUpload extends Upload {

	public CarrierUpload(String name) {
		super(name);
	}
	
	public CarrierUpload(String authorityName, String buttonName, String name) {
		super(authorityName, buttonName, name);
	}
	
	public static final String DURABLEID = "durableId";
	public static final String DURABLESPECNAME = "durableSpecName";
	public static final String HOLDSTATE = "holdState";
	public static final String TRANSFERSTATE = "transferState";
	public static final String CLEANSTATE = "cleanState";
	
	

	@Override
	protected void cudEntityList() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Map<String, Carrier> carrierMap = Maps.newHashMap(); //存放数据库已存在的所有Control
			Map<String, Carrier> uploadListMap = Maps.newHashMap(); //存放已存导入的所有Control
			Map<String, DurableSpec> durableSpecMap = Maps.newHashMap(); //Spec信息
			
			List<UploadErrorLog> errorLoglist = Lists.newArrayList();
			List<ADBase> uploadList = progress.getUploadList(); 
			
			//获取所有Active状态的Spec信息
			List<DurableSpec> durableSpecs = adManager.getEntityList(Env.getOrgRrn(), DurableSpec.class, Env.getMaxResult()," status = 'Active'", "");
			if(CollectionUtils.isNotEmpty(durableSpecs)) {
				for(DurableSpec durableSpec : durableSpecs) {
					durableSpecMap.put(durableSpec.getName(), durableSpec);
	        	}
			}
			
	        //获取数据库已存在的Carrier信息
	        List<Carrier> carrierList = adManager.getEntityList(Env.getOrgRrn(), Carrier.class);
	        if (carrierList != null) {
	        	for(Carrier carrier : carrierList) {
	        		carrierMap.put(carrier.getDurableId(), carrier);
	        	}
	        }
	        
			if (uploadList != null && uploadList.size() > 0) {
				for (int i = 0; i < uploadList.size(); i++) {
					Carrier carrier = (Carrier) uploadList.get(i);
					
					if (StringUtil.isEmpty(carrier.getDurableId())) {
						// ID为空
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null,
								DURABLEID, String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
						errorLoglist.add(errorLog);
						continue;
					}
					if (StringUtil.isEmpty(carrier.getHoldState())) {
						// ID为空
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null,
								HOLDSTATE, String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
						errorLoglist.add(errorLog);
						continue;
					}
					if (StringUtil.isEmpty(carrier.getTransferState())) {
						// ID为空
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null,
								TRANSFERSTATE, String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
						errorLoglist.add(errorLog);
						continue;
					}
					if (StringUtil.isEmpty(carrier.getCleanState())) {
						// ID为空
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null,
								CLEANSTATE, String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
						errorLoglist.add(errorLog);
						continue;
					}
					
					//效验durableSpecName是否为空，及是否存在
					if (!StringUtil.isEmpty(carrier.getDurableSpecName())) {
						if(durableSpecMap.containsKey(carrier.getDurableSpecName())) {
							carrier.setDurableSpecVersion(durableSpecMap.get(carrier.getDurableSpecName()).getVersion());
							carrier.setDurableType(durableSpecMap.get(carrier.getDurableSpecName()).getDurableType());
							carrier.setMainMatType(durableSpecMap.get(carrier.getDurableSpecName()).getMainMatType());
							carrier.setCapacity(durableSpecMap.get(carrier.getDurableSpecName()).getCapacity());
							carrier.setSlotDirection(durableSpecMap.get(carrier.getDurableSpecName()).getSlotDirection());
							carrier.setSubMatType(durableSpecMap.get(carrier.getDurableSpecName()).getSubMatType());
							carrier.setLimitCount(durableSpecMap.get(carrier.getDurableSpecName()).getLimitCount());
							carrier.setLimitTime(durableSpecMap.get(carrier.getDurableSpecName()).getLimitTime());
							carrier.setLimitTimeUnit(durableSpecMap.get(carrier.getDurableSpecName()).getTimeUnit());
						}else {
							UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null,
									DURABLESPECNAME, String.format(carrier.getDurableSpecName() + " " + Message.getString(ExceptionBundle.bundle.CommonIsNotExist())));
							errorLoglist.add(errorLog);
							continue;
						}
					}else {
						// ID为空
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null,
								DURABLESPECNAME, String.format(Message.getString(ExceptionBundle.bundle.CommonIsNull())));
						errorLoglist.add(errorLog);
						continue;
					}
					
					
                    
					//判断是否已导入
					if (uploadListMap.containsKey(carrier.getDurableId())) {
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null, 
								DURABLEID, String.format(carrier.getDurableId() + Message.getString("mm.object_ishave_template")));
						errorLoglist.add(errorLog);
						continue;
					}
					//判断是数据库是否已存在
					if (carrierMap.containsKey(carrier.getDurableId())) {
						UploadErrorLog errorLog = new UploadErrorLog(Long.parseLong(String.valueOf(i + 1)), null, 
								DURABLEID, String.format(carrier.getDurableId() + Message.getString("mm.object_ishave_input")));
						errorLoglist.add(errorLog);
						continue;
					}
					
					durableManager.saveDurable(carrier, Env.getSessionContext());
					uploadListMap.put(carrier.getDurableId(), carrier);
				}
			}
			
	        UploadErrorDialog dialog = new UploadErrorDialog(errorLoglist);
	        if(errorLoglist != null && errorLoglist.size() > 0) {
	        	dialog.open();
        	}else {
        		UI.showInfo(Message.getString("com.import_success"));
			}
		} catch (Exception e) {
			UI.showError(Message.getString(e.toString()));
			e.printStackTrace();
		}
		
	}
	
	@Override
	public boolean preCheck(List<ADBase> uploadList, List<ADBase> deleteList) throws UploadException {
		super.preCheck(uploadList, deleteList);
		return progress.isSuccess();
	}
	
}
