package com.glory.mes.mm.durable.carrier;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableSpec;

public class CarrierUpdateSpecDialog extends GlcBaseDialog{

	protected ListTableManagerField ListTableManagerFieldField;
	protected List<Carrier> carriers;
	private static final String FIELD_MMDURABLESPECFROM = "MMDurableSpecFrom";// 动态页面
	

	
	public CarrierUpdateSpecDialog(String adFormName, String authority, IEventBroker eventBroker,List<Carrier> carriers) {
		super(adFormName, authority, eventBroker);
		this.carriers = carriers;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		ListTableManagerFieldField = form.getFieldByControlId(FIELD_MMDURABLESPECFROM, ListTableManagerField.class);
		init();
		
	}
	
	protected void init() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<DurableSpec> DurableSpecList = new ArrayList<DurableSpec>();
			DurableSpecList = adManager.getEntityList(Env.getOrgRrn(), DurableSpec.class, Env.getMaxResult(), "status = 'Active'", "");	
			ListTableManagerFieldField.setValue(DurableSpecList);
			ListTableManagerFieldField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void okPressed() {	
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			List<Object> checkObject = ListTableManagerFieldField.getListTableManager().getCheckedObject();
			if(checkObject.size() < 1) {
				UI.showInfo(Message.getString("mm.choose_one_line"));// 弹出提示框
				return;
			}else if (checkObject.size() > 1) {
				UI.showInfo(Message.getString("common.only_allow_select_one_object"));// 弹出提示框
				return;
			}
			DurableSpec durableSpec = durableManager.getDurableSpec((DurableSpec)checkObject.get(0),  Env.getSessionContext());
			if(CollectionUtils.isNotEmpty(carriers)) {
				for(Carrier carrier : carriers) {
					if(carrier.getDurableSpecName().equals(durableSpec.getName())) {
						carrier.setDurableSpecName(durableSpec.getName());
						carrier.setDurableSpecVersion(durableSpec.getVersion());
						carrier.setDurableType(durableSpec.getDurableType());
						carrier.setMainMatType(durableSpec.getMainMatType());
						carrier.setCapacity(durableSpec.getCapacity());
						carrier.setSlotDirection(durableSpec.getSlotDirection());
						carrier.setSubMatType(durableSpec.getSubMatType());
						carrier.setLimitCount(durableSpec.getLimitCount());
						carrier.setLimitTime(durableSpec.getLimitTime());
						carrier.setLimitTimeUnit(durableSpec.getTimeUnit());
					}else {
						UI.showInfo(Message.getString("mm.durable_in_other_spec"));// 弹出提示框
						return;
					}
				}
			}
			durableManager.saveDurableList(carriers, Env.getSessionContext());
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	
	
	protected void cancelPressed() {
		super.cancelPressed();
	}

	
	@Override
	 protected Point getInitialSize() {
	    return new Point(700,800);
	 }
}
