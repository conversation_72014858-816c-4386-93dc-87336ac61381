package com.glory.mes.mm.durable.carrier;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableAction;

public class CarrierReturnDialog extends GlcBaseDialog{

	private static final String FIELD_ENTITYFORM = "carrierReturn";
	protected ListTableManagerField listTableManagerField;
	protected List<Carrier> carriers;
	
	public CarrierReturnDialog(String adFormName, String authority, IEventBroker eventBroker,List<Carrier> carriers) {
		super(adFormName, authority, eventBroker);
		this.carriers = carriers;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		List<Carrier> inVaildLots = new ArrayList<Carrier>();
		listTableManagerField = form.getFieldByControlId(FIELD_ENTITYFORM, ListTableManagerField.class);
		if(carriers != null && carriers.size() > 0) {
			for(Carrier carrier :carriers) {
				if (!preValidate(carrier)) {
					inVaildLots.add(carrier);
				}
				if (!inVaildLots.isEmpty()) {
					// 显示无效载具,并返回
					return;
				}
			}
		}
		listTableManagerField.setValue(carriers);
		listTableManagerField.refresh();
	}
	
	@Override
	protected void okPressed() {	
		try {
			DurableAction durableAction = (DurableAction) listTableManagerField.getValue();
			DurableManager durableManager = Framework.getService(DurableManager.class);
			List<Carrier> carrierActions = new ArrayList<Carrier>();
			for(Carrier carrier : carriers) {
				Carrier carrierAction = carrier;
				carrierAction.setActionCode(durableAction.getActionCode());
				carrierAction.setActionReason(durableAction.getActionReason());
				carrierAction.setActionComment(durableAction.getActionComment());
				carrierActions.add(carrierAction);
			}			
			durableManager.holdCarrier(carrierActions, durableAction, Env.getSessionContext());
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	public boolean preValidate(Carrier carrier) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			boolean isValid = true;
			Boolean result = durableManager.checkDurableState(carrier, Carrier.EVENT_RETURN, Env.getSessionContext());
			if (result == null) {
				if (!checkCarrierState(carrier)) {
					isValid = false;
				}

			} else if (!result) {
				isValid = false;
			}
			if (!isValid) {
				carrier.setConstraintFlag(true);
				carrier.clearMessage();
				carrier.setMessage(Message.getString("error.model_event_not_found"));
			}
			return isValid;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
	
	public boolean checkCarrierState(Carrier carrier) {
		if (Carrier.STATE_AVAILABLE.equals(carrier.getComClass())) {
			return false;
		}
		return true;
	}
	
	protected void cancelPressed() {
		super.cancelPressed();
	}

	
	@Override
	 protected Point getInitialSize() {
	    return new Point(600,700);
	 }
	
}
