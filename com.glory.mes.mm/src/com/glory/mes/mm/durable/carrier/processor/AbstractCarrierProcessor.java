package com.glory.mes.mm.durable.carrier.processor;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.common.state.client.StateManager;
import com.glory.common.state.model.Event;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.durable.model.Carrier;

public abstract class AbstractCarrierProcessor implements ICarrierProcessor {

	public static final Logger logger = Logger.getLogger(AbstractCarrierProcessor.class);

	private static final String TABLE_NAME_CARRIER_LIST = "MMCarrierProcessorLotList";

	private boolean isBatch;

	protected String eventId;

	public AbstractCarrierProcessor(boolean isBatch) {
		super();
		this.isBatch = isBatch;
	}

	/**
	 * 只有所有载具校验都通过才执行(显示CarrierProcessorDialog) 否则显示
	 */
	protected boolean isAllPass = false;

	public abstract boolean checkCarrierState(Carrier carrier);

	public abstract void buildProcessForm(Composite parent, FormToolkit toolkit);

	/**
	 * 获得显示选中的载具信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_CARRIER_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}

	public boolean preValidate(Carrier carrier) {
		boolean isValid = true;
		Boolean result = checkCarrierEventState(carrier, eventId);
		if (result == null) {
			if (!checkCarrierState(carrier)) {
				isValid = false;
			}

		} else if (!result) {
			isValid = false;
		}
		if (!isValid) {
			carrier.setConstraintFlag(true);
			carrier.clearMessage();
			carrier.addMessage("error.state_is_not_allow");
		}
		return isValid;
	}

	/**
	 * 打开处理Dialog
	 * 
	 * @param carriers
	 *            待处理的载具
	 */
	public void open(List<Carrier> carriers) {
		try {
			List<Carrier> inVaildLots = new ArrayList<Carrier>();

			for (Carrier carrier : carriers) {
				if (!preValidate(carrier)) {
					inVaildLots.add(carrier);
				}
			}
			if (isBatch) {
				if (isAllPass && !inVaildLots.isEmpty()) {
					// 显示无效载具,并返回
					openInValidDialog(inVaildLots);
					return;
				}
				openCarrierProcessorDialog(carriers);
			} else {
				if (!inVaildLots.isEmpty()) {
					// 显示无效载具,并返回
					openInValidDialog(inVaildLots);
					return;
				}
				openCarrierProcessorDialog(carriers);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void openInValidDialog(List<Carrier> carriers) {
		CarrierProcessorInValidDialog dialog = new CarrierProcessorInValidDialog(this, carriers);
		if (dialog.open() == Dialog.OK) {
		}
	}

	public void openCarrierProcessorDialog(List<Carrier> carriers) {
		CarrierProcessorDialog dialog = new CarrierProcessorDialog(this, carriers);
		if (dialog.open() == Dialog.OK) {
		}
	}

	/**
	 * 检查批次是否允许执行此事件
	 * 
	 * @param carrier
	 *            载具批
	 * @param eventId
	 *            待执行的事件
	 * @param sc
	 */
	public Boolean checkCarrierEventState(Carrier carrier, String eventId) {
		try {
			if (carrier.getStatusModelRrn() != null) {
				List<Event> events = null;
				StateManager stateManager = Framework.getService(StateManager.class);
				events = stateManager.getAllowEvents(carrier, false, Env.getSessionContext());
				boolean checkFlag = false;
				for (Event event : events) {
					if (eventId.equals(event.getEventId())) {
						checkFlag = true;
						break;
					}
				}
				return checkFlag;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
		return true;
	}

	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {

		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();

		ADField adField1 = new ADField();
		adField1.setName("durableId");
		adField1.setIsMain(true);
		adField1.setIsDisplay(true);
		adField1.setLabel(Message.getString("ras.carrier_id"));
		adField1.setLabel_zh(Message.getString("ras.carrier_id"));
		adFields.add(adField1);

		ADField adField2 = new ADField();
		adField2.setName("durableType");
		adField2.setIsMain(true);
		adField2.setIsDisplay(true);
		adField2.setDisplayLength(15l);
		adField2.setLabel(Message.getString("ras.carrier_type"));
		adField2.setLabel_zh(Message.getString("ras.carrier_type"));
		adFields.add(adField2);

		ADField adField3 = new ADField();
		adField3.setName("comClass");
		adField3.setIsMain(true);
		adField3.setIsDisplay(true);
		adField3.setIsEditable(true);
		adField3.setLabel(Message.getString("ras.com_class"));
		adField3.setLabel_zh(Message.getString("ras.com_class"));
		adField3.setDisplayType("text");
		adFields.add(adField3);

		ADField adField4 = new ADField();
		adField4.setName("state");
		adField4.setIsMain(true);
		adField4.setIsDisplay(true);
		adField4.setIsEditable(true);
		adField4.setLabel(Message.getString("wip.state"));
		adField4.setLabel_zh(Message.getString("wip.state"));
		adField4.setDisplayType("text");
		adFields.add(adField4);

		ADField adField5 = new ADField();
		adField5.setName("holdState");
		adField5.setIsMain(true);
		adField5.setIsDisplay(true);
		adField5.setIsEditable(true);
		adField5.setLabel(Message.getString("wip.hold_state"));
		adField5.setLabel_zh(Message.getString("wip.hold_state"));
		adField5.setDisplayType("text");
		adFields.add(adField5);

		// ADField adField6 = new ADField();
		// adField6.setName("messageString");
		// adField6.setIsMain(true);
		// adField6.setIsDisplay(true);
		// adField6.setIsEditable(true);
		// adField6.setLabel(Message.getString("wip.lot_message"));
		// adField6.setLabel_zh(Message.getString("wip.lot_message"));
		// adField6.setDisplayType("text");
		// adFields.add(adField6);

		adTable.setFields(adFields);

		return adTable;
	}

	public boolean isBatch() {
		return isBatch;
	}

	public void setBatch(boolean isBatch) {
		this.isBatch = isBatch;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

}
