package com.glory.mes.mm.durable.carrier.processor;

import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;

public class CarrierReturnProcessor extends AbstractCarrierProcessor {

	private static final String TABLE_NAME_CARRIER_LIST = "MMCarrierListReturnProcessor";

	public CarrierReturnProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<Carrier> checkCarriers) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			List<Carrier> newCarriers = durableManager.returnCarrier(checkCarriers, Env.getSessionContext());
			UI.showConfirm(Message.getString("mm.carrier_return_sussess"));
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}

	@Override
	public boolean checkCarrierState(Carrier carrier) {
		if (Carrier.STATE_AVAILABLE.equals(carrier.getComClass())) {
			return false;
		}
		return true;

	}

	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
	}

	@Override
	public void openCarrierProcessorDialog(List<Carrier> carriers) {
		CarrierReturnProcessorDialog dialog = new CarrierReturnProcessorDialog(this, carriers);
		if (dialog.open() == Dialog.OK) {
		}
	}

	/**
	 * 获得显示选中的载具信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_CARRIER_LIST);
		} catch (Exception e) {
			logger.error("CarrierReturnProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}

	@Override
	public boolean preValidate(Carrier carrier) {
		setEventId(Carrier.EVENT_RETURN);
		return super.preValidate(carrier);
	}
}
