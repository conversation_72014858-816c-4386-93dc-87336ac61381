package com.glory.mes.mm.durable.carrier.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.durable.model.Carrier;

/**
 * 显示处理载具信息,有上下两个部分组成 上部分根据不同的动作显示不同的输入表单
 * 下部分为选中的载具信息,包含不能处理的载具,不能处理的载具也显示,但显示红色且不能选中
 */
public class CarrierProcessorDialog extends BaseDialog {

	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;

	public ICarrierProcessor processor;
	public List<Carrier> carriers;

	public CarrierProcessorTableManager tableManager;

	public CarrierProcessorDialog(ICarrierProcessor processor, List<Carrier> carriers) {
		super();
		this.processor = processor;
		this.carriers = carriers;
	}

	@Override
	protected Control buildView(Composite parent) {
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));

		FormToolkit toolkit = new FormToolkit(parent.getDisplay());

		Section upSection = toolkit.createSection(parent, Section.TITLE_BAR | Section.EXPANDED);
		upSection.setLayout(new GridLayout(1, true));
		upSection.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		upSection.setText(Message.getString("wip.select_action_info"));

		Composite upComposite = toolkit.createComposite(upSection, SWT.NONE);
		upComposite.setLayout(new GridLayout(1, true));
		upComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
		AbstractCarrierProcessor abstractCarrierProcessor = (AbstractCarrierProcessor) processor;
		abstractCarrierProcessor.buildProcessForm(null, toolkit);
		upSection.setClient(upComposite);

		if (abstractCarrierProcessor.isBatch()) {
			Section downSection = toolkit.createSection(parent, Section.TITLE_BAR | Section.EXPANDED);
			downSection.setLayout(new GridLayout(1, true));
			downSection.setLayoutData(new GridData(GridData.FILL_BOTH));
			downSection.setText(Message.getString("mm.carrier_processor_list_title"));

			Composite downComposite = toolkit.createComposite(downSection, SWT.NONE);
			downComposite.setLayout(new GridLayout(1, true));
			downComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

			try {
				tableManager = new CarrierProcessorTableManager(abstractCarrierProcessor.getListADTable(), true);
				tableManager.newViewer(downComposite);
				((CheckBoxTableViewerManager) tableManager.getTableManager())
						.setCheckBoxDisableListener(new ICheckBoxDisableListener() {
							public boolean isDisable(Object object) {
								Carrier carrier = (Carrier) object;
								if (carrier.getConstraintFlag()) {
									return true;
								}
								return false;
							}
						});
				tableManager.setInput(carriers);
				if (carriers != null && carriers.size() > 0) {
					List<Carrier> validCarriers = new ArrayList<Carrier>();
					for (Carrier carrier : carriers) {
						if (!carrier.getConstraintFlag()) {
							validCarriers.add(carrier);
						}
					}
					tableManager.getCheckedObject().addAll(validCarriers);
					tableManager.refresh();
				}

			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
			downSection.setClient(downComposite);
		}

		return parent;
	}

	@Override
	protected void okPressed() {
		AbstractCarrierProcessor abstractCarrierProcessor = (AbstractCarrierProcessor) processor;
		if (abstractCarrierProcessor.isBatch()) {
			List<Object> checkedObjects = tableManager.getCheckedObject();
			if (checkedObjects != null && checkedObjects.size() > 0) {
				List<Carrier> checkCarriers = new ArrayList<Carrier>();
				for (Object checkedObject : checkedObjects) {
					Carrier carrier = (Carrier) checkedObject;
					checkCarriers.add(carrier);
				}
				if (processor.process(checkCarriers)) {
					super.okPressed();
				}
			}
		} else {
			if (processor.process(carriers)) {
				super.okPressed();
			}
		}
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		AbstractCarrierProcessor abstractCarrierProcessor = (AbstractCarrierProcessor) processor;

		int width = MIN_DIALOG_WIDTH;
		int height = MIN_DIALOG_HEIGHT;
		if (!abstractCarrierProcessor.isBatch()) {
			width = MIN_DIALOG_WIDTH - 200;
			height = MIN_DIALOG_HEIGHT - 200;
		}
		return new Point(Math.max(convertHorizontalDLUsToPixels(width), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(height), shellSize.y));
	}
}
