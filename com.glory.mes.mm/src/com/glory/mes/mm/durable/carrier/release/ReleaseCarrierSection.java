package com.glory.mes.mm.durable.carrier.release;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableAction;

public class ReleaseCarrierSection extends EntitySection {
	
	private static final Logger logger = Logger.getLogger(ReleaseCarrierSection.class);
	
	protected ToolItem itemRelease;
	protected ReleaseCarrierForm itemForm;
	
	public Text txtCarrier;
	
	public ReleaseCarrierSection() {
		super();
	}

	public ReleaseCarrierSection(ADTable table) {
		this(table, null);
	}
	
	public ReleaseCarrierSection(ADTable table, ADBase prestoreObject) {
		super(table);
	}
	
	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("ras.carrier_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtCarrier = toolkit.createText(top, "", SWT.BORDER);
		GridData gText = new GridData();
		gText.widthHint = 216;
		Color background = new Color(Display.getCurrent(), 255, 255, 255);
		txtCarrier.setBackground(background);
		txtCarrier.setLayoutData(gText);
		txtCarrier.setTextLimit(32);
		txtCarrier.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tCarrierId = ((Text) event.widget);
				tCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Carrier carrier = null;
					String carrierId = tCarrierId.getText();
					carrier = searchCarrier(carrierId);
					tCarrierId.selectAll();
					if (carrier == null) {
						tCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
	        				setAdObject(createAdObject());		        			
	        			} catch(Exception en) {
	        				logger.error("createADObject error at searchEntity Method!");
	        			}
					} else {
						setAdObject(carrier);
					}
					refresh();
					break;
				}
			}

		});
		txtCarrier.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				tLotId.setText(tLotId.getText());
			}
		});
		
		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
	}
	
	public Carrier searchCarrier(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			return (Carrier) durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("ras.carrier_release_sectiontitle"));
		initAdObject();
	}

	public void initAdObject() {
		setAdObject(new Carrier());
		refresh();
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		itemForm = new ReleaseCarrierForm(composite, SWT.NONE, tab, mmng);
		return itemForm;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemRelease(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemRelease(ToolBar tBar) {
		itemRelease = new ToolItem(tBar, SWT.PUSH);
		itemRelease.setText(Message.getString("ras.release"));
		itemRelease.setImage(SWTResourceCache.getImage("release-lot"));
		itemRelease.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				releaseAdapter(event);
			}
		});
	}

	protected void releaseAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						DurableAction durableAction = ((ReleaseCarrierForm) detailForm).getDurableAction();
						DurableManager durableManager = Framework.getService(DurableManager.class);
						Carrier carrier = (Carrier) this.getAdObject();
						carrier.setActionCode(durableAction.getActionCode());
						carrier.setActionReason(durableAction.getActionReason());
						carrier.setActionComment(durableAction.getActionComment());
						durableManager.releaseCarrier(carrier, durableAction, Env.getSessionContext());
					}
					UI.showInfo(Message.getString("ras.carrier_release_successed"));// 弹出提示框
					refresh();
				}
			}
			txtCarrier.setFocus();
		} catch (Exception e) {
			logger.error("ReleaseSection : releaseAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));				
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
		if(txtCarrier != null) {
			txtCarrier.selectAll();
		}
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Carrier newBase = (Carrier) this.getAdObject();
		if (newBase != null) {
			statusChanged(newBase.getHoldState());
		} else {
			statusChanged("");
		}
	}
	 
	public void statusChanged(String newStatus) {
		if (newStatus != null) {
			if (Carrier.HOLDSTATE_ON.equalsIgnoreCase(newStatus)) {
				itemRelease.setEnabled(true);
			} else {
				itemRelease.setEnabled(false);
			}
		} else {
			itemRelease.setEnabled(false);
		}
	}
	
	@Override
	public void setFocus() {
		txtCarrier.setFocus();
	}
}
