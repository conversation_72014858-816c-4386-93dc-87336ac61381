package com.glory.mes.mm.warehouse;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.framework.core.exception.ExceptionBundle;

public class WarehouseProperties extends EntityProperties {
	
	public WarehouseProperties() {
		super();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);	
		section.setTextClient(tBar);
	}
	
	@Override
	public void saveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if ( getAdObject() != null ) {	
				
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {				
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					ADManager adManager = Framework.getService(ADManager.class);
					Warehouse base = (Warehouse) getAdObject();
					Warehouse old = (Warehouse) getAdObject();
					if (base.getIsGlobal()) {
						base.setOrgRrn(0L);
					} else {
						base.setOrgRrn(Env.getOrgRrn());
					}
					base = (Warehouse) adManager.saveEntity(getTable().getObjectRrn(), base, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
					setAdObject(adManager.getEntity(base));
					refresh();
					if (old.getObjectRrn() == null) {
						getMasterParent().refreshAdd(base);
					}else{
						getMasterParent().refreshUpdate(base);
					}
					
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void newAdapter(){
		super.newAdapter();
		getMasterParent().refresh();
	}

	@Override
	public boolean delete() {
        try {
            boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
            if (confirmDelete) {
                if (getAdObject().getObjectRrn() != null) {
                	ADManager entityManager = getADManger();
                	// 查询仓库
                	String queryParam = String.format(" objectRrn = '%s'", getAdObject().getObjectRrn());
                    List<Warehouse> listWarehouse = entityManager.getEntityList(getAdObject().getOrgRrn(), Warehouse.class,
    						Integer.MAX_VALUE, queryParam, null);
                    // 删除库房、货架、储位
                    if (listWarehouse.size() > 0) {
                    	queryParam = String.format(" warehouseId = '%s'", listWarehouse.get(0).getWarehouseId());
                        ADManager adManager = Framework.getService(ADManager.class);
                        List<Storage> listStorage = adManager.getEntityList(getAdObject().getOrgRrn(), Storage.class, Integer.MAX_VALUE, queryParam, null);
                        adManager.deleteEntityList(listStorage, Env.getSessionContext());
                    }
                	// 删除仓库
                    entityManager.deleteEntity(getAdObject(), Env.getSessionContext());
                    setAdObject(createAdObject());
                    refresh();
                    return true;
                }
            }
        } catch (Exception e1) {
            ExceptionHandlerManager.asyncHandleException(e1);
        }
        return false;
	}
	
}
