package com.glory.mes.mm.packagerule;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.common.context.ContextActiveDialog;
import com.glory.common.context.client.ContextManager;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextValue;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.merge.MergeRule;
import com.glory.mes.base.merge.MergeRuleLine;
import com.glory.mes.mm.cdi.LabelCdiAction;
import com.glory.mes.mm.client.PackManager;
import com.glory.mes.mm.model.PackageRule;
import com.glory.mes.mm.model.PackageType;
import com.glory.mes.wip.client.MergeManager;
import com.google.common.base.Objects;
import com.google.common.collect.Maps;

public class PackageRuleEditor extends GlcEditor { 
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.packagerule.PackageRuleEditor";

	private static final String CONTEXT_NAME = "PACKAGERULE";
	private static final String CONTEXT_NAME_LABEL = "LABEL";
	
	private static final String DIALOG_FORM_PREVIEW_LABEL = "PackageRulePreviewLabelDialog";
	
	public static final String FIELD_CONTEXTINFOGLC = "contextInfoGlc";
	public static final String FIELD_PACKAGERULESETUP = "packageRuleSetup";
	public static final String FIELD_PACKAGERULELIST = "packageRuleList";
	public static final String FIELD_PACKAGERULECONTEXTSETUP = "packageRuleContextSetup";
	public static final String FIELD_PACKAGELABELCONTEXTINFO = "packageLabelContextInfo";
	public static final String FIELD_PACKAGEMERGEEQUALRULETABLE = "packageMergeEqualRuleTable";
	public static final String FIELD_PACKAGEMERGEEQUALRULEINFO = "packageMergeEqualRuleInfo";
	public static final String FIELD_PACKAGEMERGERULECDI = "packageMergeRuleCdi";

	public static final String FIELD_PACKAGETYPENAME = "packageTypeName";
	
	public static final String BUTTON_NEW = "new";
	public static final String BUTTON_SAVE = "save";
	public static final String BUTTON_ACTIVE = "active";
	public static final String BUTTON_INACTIVE = "inActive";
	public static final String BUTTON_DELETE = "delete";
	public static final String BUTTON_REFRESH = "refresh";
	public static final String BUTTON_PREVIEW_LABEL = "previewLabel";
	
	public static final String BUTTON_ADDEQUALLINE = "addEqualLine";
	public static final String BUTTON_DELETEEQUALLINE = "deleteEqualLine";

	protected GlcFormField contextInfoGlcField;
	protected GlcFormField packageRuleSetupField;
	protected QueryFormField packageRuleListField;
	protected ListTableManagerField packageRuleContextSetupField;
	protected ListTableManagerField packageLabelContextInfoField;
	protected EntityFormField packageRuleSetupField1;
	
	protected ListTableManagerField packageMergeEqualRuleTableField;
	protected EntityFormField packageMergeEqualRuleInfoField;
	protected ListTableManagerField packageMergeRuleCdiField;
	
	public PackageRule currentPackageRule;
	
	protected PackManager packManager;
	protected MergeManager mergeManager;
	protected ContextManager contextManager;
	protected MBASManager mbasManager;
	
	protected List<MergeRuleLine> cdiRuleLines = null;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		contextInfoGlcField = form.getFieldByControlId(FIELD_CONTEXTINFOGLC, GlcFormField.class);
		packageRuleSetupField = form.getFieldByControlId(FIELD_PACKAGERULESETUP, GlcFormField.class);
		
		packageRuleListField = contextInfoGlcField.getFieldByControlId(FIELD_PACKAGERULELIST, QueryFormField.class);
		packageRuleContextSetupField = contextInfoGlcField.getFieldByControlId(FIELD_PACKAGERULECONTEXTSETUP, ListTableManagerField.class);
		packageLabelContextInfoField = contextInfoGlcField.getFieldByControlId(FIELD_PACKAGELABELCONTEXTINFO, ListTableManagerField.class);
		
		packageRuleSetupField1 = packageRuleSetupField.getFieldByControlId(FIELD_PACKAGERULESETUP, EntityFormField.class);
		packageMergeEqualRuleTableField = packageRuleSetupField.getFieldByControlId(FIELD_PACKAGEMERGEEQUALRULETABLE, ListTableManagerField.class);
		packageMergeEqualRuleInfoField = packageRuleSetupField.getFieldByControlId(FIELD_PACKAGEMERGEEQUALRULEINFO, EntityFormField.class);
		packageMergeRuleCdiField = packageRuleSetupField.getFieldByControlId(FIELD_PACKAGEMERGERULECDI, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, packageRuleListField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		subscribeAndExecute(eventBroker, packageRuleListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		subscribeAndExecute(eventBroker, packageMergeEqualRuleTableField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::packageMergeEqualRuleTableSelectionChanged);
		
		subscribeAndExecute(eventBroker, contextInfoGlcField.getFullTopic(BUTTON_NEW), this::contextNewAdapter);
		subscribeAndExecute(eventBroker, contextInfoGlcField.getFullTopic(BUTTON_ACTIVE), this::contextActiveAdapter);
		subscribeAndExecute(eventBroker, contextInfoGlcField.getFullTopic(BUTTON_INACTIVE), this::contextInActiveAdapter);
		subscribeAndExecute(eventBroker, contextInfoGlcField.getFullTopic(BUTTON_DELETE), this::contextDeleteAdapter);
		subscribeAndExecute(eventBroker, contextInfoGlcField.getFullTopic(BUTTON_REFRESH), this::contextRefreshAdapter);
		
		subscribeAndExecute(eventBroker, contextInfoGlcField.getFullTopic(BUTTON_PREVIEW_LABEL), this::previewLabelAdapter);
		
		subscribeAndExecute(eventBroker, packageRuleSetupField.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, packageRuleSetupField.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, packageRuleSetupField.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, packageRuleSetupField.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);

		subscribeAndExecute(eventBroker, packageRuleSetupField.getFullTopic(BUTTON_ADDEQUALLINE), this::addEqualLineAdapter);
		subscribeAndExecute(eventBroker, packageRuleSetupField.getFullTopic(BUTTON_DELETEEQUALLINE), this::deleteEqualLineAdapter);

		try {
			packManager = Framework.getService(PackManager.class);
			mergeManager = Framework.getService(MergeManager.class);
			contextManager = Framework.getService(ContextManager.class);
			mbasManager = Framework.getService(MBASManager.class);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		init();
	}
	
	private void init() {
		packageRuleSetupField1.setValue(new PackageRule());
		packageRuleSetupField1.refresh();
		
		packageMergeEqualRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_EQUAL));
		packageMergeEqualRuleInfoField.refresh();
		
		packageMergeEqualRuleTableField.getListTableManager().setInput(Lists.newArrayList());
		try {
			if (cdiRuleLines == null) {
				cdiRuleLines = mergeManager.getCdiMergeRuleLine();			
			}
			packageMergeRuleCdiField.getListTableManager().setInput(cdiRuleLines);
			packageMergeRuleCdiField.getListTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}
	
	private MergeRuleLine createMergeRuleLine(String ruleType) {
		MergeRuleLine ruleLine = new MergeRuleLine();
		ruleLine.setOrgRrn(Env.getOrgRrn());
		ruleLine.setRuleType(ruleType);
		return ruleLine;
	}
	
	private void queryAdapter(Object object) {
		EntityForm packageRuleSetupEntityForm = (EntityForm) packageRuleListField.getQueryForm().getQueryForm();
		try {
			packageRuleSetupEntityForm.getMessageManager().setAutoUpdate(true); //设置为true时才会刷新界面出现icon
			packageRuleSetupEntityForm.getMessageManager().removeAllMessages();
			
			boolean saveFlag = true;			
			if (!packageRuleSetupEntityForm.validate()) {
				saveFlag = false;
			}
			if (saveFlag) {			
				Context labelContext = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME_LABEL);
				
				RefTableField packageTypeField = packageRuleListField.getQueryForm().getFieldByControlId(FIELD_PACKAGETYPENAME, RefTableField.class);
				if (packageTypeField.getValue() != null && !StringUtil.isEmpty(packageTypeField.getValue().toString())) {
					List<PackageRule> packageRules = getADManger().getEntityList(Env.getOrgRrn(), PackageRule.class, Env.getMaxResult(), 
							"packageTypeName = '" + packageTypeField.getValue().toString() + "'", "");
					packageRuleListField.getQueryForm().getTableManager().setInput(packageRules);
					
					List<PackageType> packageTypes = getADManger().getEntityList(Env.getOrgRrn(), PackageType.class, Env.getMaxResult(), 
							"name = '" + packageTypeField.getValue().toString() + "'", "");
					
					List<ContextValue> labelContextValues = getADManger().getEntityList(Env.getOrgRrn(), ContextValue.class, Env.getMaxResult(), 
							" contextRrn = " + labelContext.getObjectRrn() + " AND status = 'Active' AND contextFieldValue1 = '" + packageTypes.get(0).getPackMainMatType() + "'", "");
					packageLabelContextInfoField.getListTableManager().setInput(labelContextValues);
				} else {
					List<PackageRule> packageRules = getADManger().getEntityList(Env.getOrgRrn(), PackageRule.class);
					packageRuleListField.getQueryForm().getTableManager().setInput(packageRules);
					
					List<ContextValue> labelContextValues = getADManger().getEntityList(Env.getOrgRrn(), ContextValue.class, Env.getMaxResult(), 
							" contextRrn = " + labelContext.getObjectRrn() + " AND status = 'Active'", "");
					packageLabelContextInfoField.getListTableManager().setInput(labelContextValues);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			packageRuleSetupEntityForm.getMessageManager().setAutoUpdate(false);
		}
	}
	
	/**
	 * 包装规则列表选择事件
	 * @param object
	 */
	private void selectionChanged(Object object) {
		try {					
			PackageRule packageRule = (PackageRule) packageRuleListField.getSelectedObject();	
			setCurrentPackageRule(packageRule);
			refresh();			
			
			contextRefreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void contextNewAdapter(Object object) {
		try {
			PackageRule packageRule = (PackageRule) packageRuleListField.getSelectedObject();	
			if (packageRule == null) {
				  UI.showInfo(Message.getString("mm.please_select_package_rule"));
	              return;
			}
            Context context = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME);
            
			 ContextValue contextValue = new ContextValue();
            contextValue.setOrgRrn(Env.getOrgRrn()); 
            contextValue.setContextRrn(context.getObjectRrn());
            contextValue.setStatus(ContextValue.STATUS_ACTIVE);
            contextValue.setContextFieldValue1(packageRule.getPackageTypeName());
            contextValue.setResultValue1(packageRule.getName());
            
            EntityDialog addDialog = new PackageRuleContextAddDialog(packageRuleContextSetupField.getListTableManager().getADTable(), contextValue);
            if (Dialog.OK == addDialog.open()) {
            	contextRefreshAdapter(null);
            }
		 } catch (Exception e) {
	         ExceptionHandlerManager.asyncHandleException(e);
	         return;
	     }	 
	}

	private void contextActiveAdapter(Object object) {
		try {
			ContextActiveDialog dialog = new ContextActiveDialog(UI.getActiveShell(), CONTEXT_NAME);
            dialog.open();
            
            contextRefreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}

	private void contextInActiveAdapter(Object object) {
		try {		
			ContextValue contextValue = (ContextValue) packageRuleContextSetupField.getListTableManager().getSelectedObject();	
            if (contextValue == null) {
                UI.showInfo(Message.getString("common.select_object"));
                return;
            }
            if (UI.showConfirm(Message.getString("common.confirm_inactive"))) {             
            	
                List<ContextValue> contextValues = new ArrayList<ContextValue>();
                contextValues.add(contextValue);
                	 
                contextManager.activeContextValue(contextValues, Env.getSessionContext());
                UI.showInfo(Message.getString("common.inactive_success"));   
                contextRefreshAdapter(null);
            }	               
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}

	private void contextDeleteAdapter(Object object) {
		try {
			ContextValue contextValue = (ContextValue) packageRuleContextSetupField.getListTableManager().getSelectedObject();	
            if (contextValue == null ) {
                UI.showInfo(Message.getString("common.select_object"));
                return;
            }
			boolean confirmDelete = UI.showConfirm(Message.getString("common.confirm_delete"));
			if (confirmDelete) {
                List<ContextValue> contextValues = new ArrayList<ContextValue>();
                contextValues.add(contextValue);
                	
                contextManager.deleteContextValue(contextValues, Env.getSessionContext());
                contextRefreshAdapter(null);
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}

	private void contextRefreshAdapter(Object object) {
		try {
			if (getCurrentPackageRule() != null) {
				PackageRule packageRule = getCurrentPackageRule();
				Map<String, String> resultMap = Maps.newHashMap();
				resultMap.put(PackageRule.CONTEXT_RESULT_PACKAGE_RULE_NAME, packageRule.getName());
				List<ContextValue> contextValues = contextManager.getActiveContextValuesByResult(Env.getOrgRrn(), CONTEXT_NAME, resultMap, false);
				packageRuleContextSetupField.getListTableManager().setInput(contextValues);
			} else {
				packageRuleContextSetupField.getListTableManager().setInput(new ArrayList<ContextValue>());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}

	/**
	 * 预览标签
	 * @param object
	 */
	private void previewLabelAdapter(Object object) {
		try {
		ContextValue contextValue = (ContextValue) packageLabelContextInfoField.getListTableManager().getSelectedObject();
		if (contextValue == null ) {
            UI.showInfo(Message.getString("common.select_object"));
            return;
        }
		
		PackManager packManager = Framework.getService(PackManager.class);
		Map<String, Object> map = packManager.getLabelByName(Env.getOrgRrn(), contextValue.getResultValue1());
		if (map != null && map.get(LabelCdiAction.PROPERTY_TEMPLATE_CONTENT_BINARY) != null) {
			PreviewLabelDialog baseDialog = new PreviewLabelDialog(DIALOG_FORM_PREVIEW_LABEL, null, eventBroker, map);
			baseDialog.open();
		} else {
			UI.showError(Message.getString("error.data_exception"));
			return;
		}
	} catch (Exception e) {
		ExceptionHandlerManager.asyncHandleException(e);
		return;
	}	
	
		
	}

	/**
	 * 新建
	 * @param object
	 */
	private void newAdapter(Object object) {
		init();
	}

	/**
	 * 保存包装规则
	 * @param object
	 */
	private void saveAdapter(Object object) {
		EntityForm packageRuleSetupEntityForm = (EntityForm) packageRuleSetupField1.getControls()[0];
		try {				
			packageRuleSetupEntityForm.getMessageManager().setAutoUpdate(true); //设置为true时才会刷新界面出现icon
			packageRuleSetupEntityForm.getMessageManager().removeAllMessages();
			
			if (packageRuleSetupField1.getValue() != null) {
				boolean saveFlag = true;			
				if (!packageRuleSetupEntityForm.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {		
					boolean isAdd = true;
					PackageRule packageRule = (PackageRule) packageRuleSetupField1.getValue();
					if (packageRule.getObjectRrn() != null) {
						isAdd = false;
					}
					packageRule.setOrgRrn(Env.getOrgRrn());
								
					List<MergeRuleLine> equalRuleLines = (List<MergeRuleLine>) packageMergeEqualRuleTableField.getListTableManager().getInput();
					List<Object> cdiRuleLines = packageMergeRuleCdiField.getListTableManager().getCheckedObject();
					List<MergeRuleLine> allRuleLine = Lists.newArrayList();
					allRuleLine.addAll(equalRuleLines);
					for (Object line : cdiRuleLines) {
						allRuleLine.add((MergeRuleLine) line);
					}
					if (!CollectionUtils.isEmpty(allRuleLine)) {
						//判断变量不能重复
						List<String> ruleLineName = Lists.newArrayList();
						for (MergeRuleLine ruleLine : allRuleLine) {
							ruleLine.setObjectRrn(null);
							ruleLine.setOrgRrn(Env.getOrgRrn());
							ruleLine.setIsActive(true);
							if (ruleLineName.contains(ruleLine.getRuleName())) {
								UI.showWarning(Message.getString(ruleLine.getRuleName() + Message.getString("common.merge_rule_line_repeat")));
								return;
							} else {
								ruleLineName.add(ruleLine.getRuleName());
							}
						}
						
						MergeRule mergeRule = null;
						if (packageRule.getObjectRrn() != null && !StringUtil.isEmpty(packageRule.getMergeRuleName())) {
							mergeRule = mbasManager.getMergeRule(packageRule.getMergeRuleName(), MergeRule.CATEGORY_PACKAGE, false, Env.getSessionContext());					
						}
						if (mergeRule == null) {
							mergeRule = new MergeRule();
							mergeRule.setIsActive(true);
							mergeRule.setOrgRrn(Env.getOrgRrn());
							mergeRule.setName("PackageMergeRule-" + packageRule.getName());
							mergeRule.setStatus(MergeRule.STATUS_ACTIVE);
							packageRule.setMergeRuleName(mergeRule.getName());
						}	
						mergeRule.setMergeRuleLines(allRuleLine);
						packageRule.setMergeRule(mergeRule);
					} else {
						packageRule.setMergeRuleName(null);
						packageRule.setMergeRule(null);
						
//						UI.showError(Message.getString("wip.please_set_detail_merge_rule"));		
//						return;
					}					
					
					PackageRule oldDefaultPackageRule = null;
					if (packageRule.getIsDefault()) {
						//确认是否还需要默认的包装规则
						oldDefaultPackageRule = packManager.getPackageRuleByName(Env.getOrgRrn(), null, packageRule.getPackageTypeName(), false);
						if (oldDefaultPackageRule != null && !packageRule.getName().equals(oldDefaultPackageRule.getName())) {
							boolean confirm = UI.showConfirm(Message.getString("mm.confirm_default_packagerule_already_exist"));
							if (!confirm) {
								return;			
							}	
						}
					} 
					
					packageRule = packManager.savePackageRule(packageRule, Env.getSessionContext());				
					setCurrentPackageRule(packageRule);				
					UI.showInfo(Message.getString("common.save_successed"));// 弹出提示框					
					refreshAdapter(object);
					if (isAdd) {
						packageRuleListField.getQueryForm().getTableManager().insert(0, packageRule);
					} else {
						packageRuleListField.getQueryForm().getTableManager().update(packageRule);
					}	
					if (oldDefaultPackageRule != null) {
						oldDefaultPackageRule = (PackageRule) getADManger().getEntity(oldDefaultPackageRule);
						packageRuleListField.getQueryForm().getTableManager().update(oldDefaultPackageRule);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}  finally {
			packageRuleSetupEntityForm.getMessageManager().setAutoUpdate(false);
		}
	}

	/**
	 * 删除包装规则
	 * @param object
	 */
	private void deleteAdapter(Object object) {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString("common.confirm_delete"));
			if (confirmDelete) {
				if (packageRuleSetupField1.getValue() != null) {		
					PackageRule packageRule = (PackageRule) packageRuleSetupField1.getValue();
					packageRule = (PackageRule) getADManger().getEntity(packageRule);
					
					if (packageRule.getObjectRrn() != null && !StringUtil.isEmpty(packageRule.getMergeRuleName())) {
						MergeRule mergeRule = mbasManager.getMergeRule(packageRule.getMergeRuleName(), MergeRule.CATEGORY_PACKAGE, false, Env.getSessionContext());					
						if (mergeRule != null) {
							packageRule.setMergeRule(mergeRule);
						}
					}
					
					PackManager packManager = Framework.getService(PackManager.class);
					packManager.deletePackageRule(packageRule, Env.getSessionContext());
					
					newAdapter(null);
					packageRuleListField.refresh();
				}	
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	/**
	 * 刷新方法
	 * @param object
	 */
	private void refreshAdapter(Object object) {
		try {
			PackageRule packageRule = getCurrentPackageRule();
			if (packageRule != null && packageRule.getObjectRrn() != null) {
				ADManager entityManager = getADManger();
				setCurrentPackageRule((PackageRule)entityManager.getEntity(packageRule));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		refresh();
	}
	
	private void addEqualLineAdapter(Object object) {
		try {
			MergeRuleLine mergeRule = (MergeRuleLine) packageMergeEqualRuleInfoField.getValue();
			if (packageMergeEqualRuleInfoField.validate()) {
				mergeRule = createExpression(mergeRule);
				if (mergeRule.getSeqNo() != null) {
					packageMergeEqualRuleTableField.getListTableManager().update(mergeRule);
				} else {
					int seqNo = packageMergeEqualRuleTableField.getListTableManager().getInput().size();
					mergeRule.setSeqNo(Long.valueOf(seqNo));
					List<MergeRuleLine> mergeRuleLines = (List<MergeRuleLine>) packageMergeEqualRuleTableField.getListTableManager().getInput();
					List<MergeRuleLine> allMergeRuleLines = Lists.newArrayList();
					allMergeRuleLines.addAll(mergeRuleLines);
					allMergeRuleLines.add(mergeRule);
					packageMergeEqualRuleTableField.getListTableManager().setInput(allMergeRuleLines);
				}
				packageMergeEqualRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_EQUAL));
				packageMergeEqualRuleInfoField.refresh();
				packageMergeEqualRuleInfoField.getFormControl().removeAllMessages();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void deleteEqualLineAdapter(Object object) {
		Object selectedMergeRuleLine = packageMergeEqualRuleTableField.getListTableManager().getSelectedObject();
		List<MergeRuleLine> mergeRuleLines = (List<MergeRuleLine>) packageMergeEqualRuleTableField.getListTableManager().getInput();
		mergeRuleLines.remove(selectedMergeRuleLine);
		
		List<MergeRuleLine> mergeRuleList = Lists.newArrayList();
		int i = 0;
		for (MergeRuleLine ruleLine : mergeRuleLines) {
			ruleLine.setSeqNo(Long.valueOf(i));
			mergeRuleList.add(ruleLine);
			i++;
		}
		packageMergeEqualRuleTableField.getListTableManager().setInput(mergeRuleList);
		packageMergeEqualRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_EQUAL));
		packageMergeEqualRuleInfoField.refresh();
	}
	
	/*
	 * 相等规则：${变量} LIKE 'A%'     
	 * 计量规则：${Count} > 1  
	 * 组合规则：${A1} and ${A2}
	 */
	private MergeRuleLine createExpression(MergeRuleLine ruleLine) {
		String expression = null;
		if (MergeRuleLine.RULE_TYPE_EQUAL.equals(ruleLine.getRuleType())) {
			expression = "${" + ruleLine.getVariableName() + "} " + ruleLine.getOperator() + " '" + ruleLine.getValue() + "%'";
		} else if (MergeRuleLine.RULE_TYPE_COUNT.equals(ruleLine.getRuleType())) {
			expression = String.format("${%s} %s %s", ruleLine.getCountType(), ruleLine.getOperator(), ruleLine.getValue());
		}
		ruleLine.setExpression(expression);
		return ruleLine;
	}

	private void packageMergeEqualRuleTableSelectionChanged(Object object) {
		try {
			MergeRuleLine mergeRuleLine = (MergeRuleLine) packageMergeEqualRuleTableField.getListTableManager().getSelectedObject();
			if (mergeRuleLine != null) {
				packageMergeEqualRuleInfoField.setValue(mergeRuleLine);
				packageMergeEqualRuleInfoField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public PackageRule getCurrentPackageRule() {
		return currentPackageRule;
	}

	public void setCurrentPackageRule(PackageRule currentPackageRule) {
		this.currentPackageRule = currentPackageRule;
	}

	public void refresh() {
		try {
			packageMergeEqualRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_EQUAL));
			packageMergeEqualRuleInfoField.refresh();
			
			PackageRule packageRule = getCurrentPackageRule();	
			if (packageRule != null) {
				packageRuleSetupField1.setValue(packageRule);
				packageRuleSetupField1.refresh();
				
				if (!StringUtil.isEmpty(packageRule.getMergeRuleName())) {
					MergeRule mergeRule = mbasManager.getMergeRule(packageRule.getMergeRuleName(), MergeRule.CATEGORY_PACKAGE, true, Env.getSessionContext());	
					if (mergeRule != null) {
						List<MergeRuleLine> equalMergeRuleLines = mergeRule.getMergeRuleLines().stream().filter(p -> MergeRuleLine.RULE_TYPE_EQUAL.equals(p.getRuleType())).toList();
						List<MergeRuleLine> cdiMergeRuleLines = mergeRule.getMergeRuleLines().stream().filter(p -> MergeRuleLine.RULE_TYPE_CDI.equals(p.getRuleType())).toList();
						packageMergeEqualRuleTableField.getListTableManager().setInput(equalMergeRuleLines);
						packageMergeEqualRuleTableField.getListTableManager().refresh();
						
						if (cdiRuleLines != null) {
							for (MergeRuleLine cdiRuleLine : cdiRuleLines) {
								boolean checkFlag = false;
								if (cdiMergeRuleLines != null) {
									for (MergeRuleLine mergeRuleLine : cdiMergeRuleLines) {
										if (Objects.equal(cdiRuleLine.getRuleName(), mergeRuleLine.getRuleName())) {
											checkFlag = true;					
											break;
										}
									}
								}
								if (checkFlag) {
									((CheckBoxTableViewerManager)packageMergeRuleCdiField.getListTableManager().getTableManager()).checkObject(cdiRuleLine);
								} else {
									((CheckBoxTableViewerManager)packageMergeRuleCdiField.getListTableManager().getTableManager()).unCheckObject(cdiRuleLine);
								}
							}
						}
					} else {
						packageMergeEqualRuleTableField.getListTableManager().setInput(null);
						if (cdiRuleLines != null) {
							for (MergeRuleLine cdiRuleLine : cdiRuleLines) {
								((CheckBoxTableViewerManager)packageMergeRuleCdiField.getListTableManager().getTableManager()).unCheckObject(cdiRuleLine);
							}
						}
					}
				} else {
					packageMergeEqualRuleTableField.getListTableManager().setInput(null);
					if (cdiRuleLines != null) {
						for (MergeRuleLine cdiRuleLine : cdiRuleLines) {
							((CheckBoxTableViewerManager)packageMergeRuleCdiField.getListTableManager().getTableManager()).unCheckObject(cdiRuleLine);
						}
					}
				}
			} else {
				packageRuleSetupField1.setValue(new PackageRule());
				packageRuleSetupField1.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

}