package com.glory.mes.wip.wafersource.query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.eclipse.nebula.widgets.nattable.export.NatExporter;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.EntityQueryProgress;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.entitymanager.forms.ViewQueryProgress;
import com.glory.framework.base.ui.dialog.QueryProgressMonitorDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;

public class QueryWaferSourceSection extends QueryEntityListSection implements IRefresh {

	public static final String TABLE_NAME = "MMQueryMComponentUnit";
	public ListTableManager manager;

	public QueryWaferSourceSection(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void exportAdapter() {
		try {
			new NatExporter(natTable.getShell()).exportSingleLayer(manager.getLayer(), natTable.getConfigRegistry());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void createNewViewer(Composite client, final IManagedForm form) {
		try {
			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			manager = new ListTableManager(adTable, false);
			manager.setIndexFlag(true);
			manager.newViewer(client);
			natTable = manager.getNatTable();
			manager.addDoubleClickListener(getDoubleClickListener());
			manager.addSelectionChangedListener(getSelectChangeListener());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void refresh() {
		try {
			long count = getEntityNumber();
			List<Object> adList = new ArrayList<Object>();
			if (count > getMonitorThreshold()) {
				EntityQueryProgress progress;
				if (tableManager.getADTable().getIsView()) {
					progress = new ViewQueryProgress(this.getADManger(), count, getProcessThreshold(),
							tableManager.getADTable(), getWhereClause(), "");
				} else {
					progress = new EntityQueryProgress(this.getADManger(), count, getProcessThreshold(),
							tableManager.getADTable(), getWhereClause(), "");
				}
				QueryProgressMonitorDialog progressDiglog = new QueryProgressMonitorDialog(UI.getActiveShell(), "");
				progressDiglog.run(true, true, progress);
				adList = progress.getAdList();
			} else {
				ADManager manager = Framework.getService(ADManager.class);
				if (tableManager.getADTable().getIsView()) {
					List<Map> currentList = manager.getEntityMapListByColumn(Env.getOrgRrn(),
							tableManager.getADTable().getObjectRrn(), 0, Env.getMaxResult(), getWhereClause(), "",
							false);
					adList.addAll(currentList);
				} else {
					List<ADBase> currentList = manager.getEntityList(Env.getOrgRrn(),
							tableManager.getADTable().getObjectRrn(), Env.getMaxResult(), getWhereClause(), "");
					adList.addAll(currentList);
				}
			}

			MMManager mmManager = Framework.getService(MMManager.class);
			List<MComponentUnit> units = new ArrayList<MComponentUnit>();
			if (adList.size() > 0) {
				units = mmManager.getMComponentUnitByMLots((List<MLot>) (List) adList, "AVAIL");
				// 将仓库位置号显示在芯片库存查询界面
				if (units != null && units.size() > 0) {
					ADManager adManager = Framework.getService(ADManager.class);
					for (MComponentUnit mComponentUnit : units) {
						List<MLotStorage> mLotStorageList = adManager.getEntityList(Env.getOrgRrn(), MLotStorage.class,
								Integer.MAX_VALUE, " mLotRrn ='" + mComponentUnit.getParentMLotRrn() + "'", "");
						if (mLotStorageList != null && mLotStorageList.size() > 0) {
							mComponentUnit.setReserved1(mLotStorageList.get(0).getStorageId());
						}
					}
				}

				manager.setInput(units);
			} else {
				manager.setInput(units);
			}
			showNumber = units.size();
			totalNumber = units.size();
			createSectionDesc(section);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
