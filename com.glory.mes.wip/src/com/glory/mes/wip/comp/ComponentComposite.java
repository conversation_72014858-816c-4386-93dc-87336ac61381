package com.glory.mes.wip.comp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.extensionpoints.TableItemAdapterExtensionPoint;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapter;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class ComponentComposite {
	
	protected final Color ColorBackground = SWTResourceCache.getColor(SWTResourceCache.COLOR_TABLE_HEADER_BG);
	
	private ComponentUnit selectedObj;
	private String currentPositon;

	public static enum DiffType {
		ADD, REMOVE, CHANGE
	}
	
	public static enum OccupationPolicy{
		REPLACE, IGNORE, REJECT
	}
	
	private Composite parent;
	private ADTable adTable;
	private ListTableManager tableManager;
	
	private int count;
	private String[] itemAdapters;

	private boolean ascFlag;
	private boolean checkFlag;
	

	//载具初始的ComponentUnit位置,只记录真实的Component,Key为ComponentId,Value未Position
	private Map<String, String> initCompIdPositionMap = new LinkedHashMap<String, String>();
	
	//载具初始的ComponentUnit信息
	private Map<String, ComponentUnit> initCompIdMap = new LinkedHashMap<String, ComponentUnit>();
	
	//如果是Target列表记录选片的原始位置信息
	private Map<String, ComponentUnit> initCarrierMap = new LinkedHashMap<String, ComponentUnit>();

	//载具位置当前的ComponentUnit位置
	private Map<String, ComponentUnit> carrierMap = new LinkedHashMap<String, ComponentUnit>();
		
	public ComponentComposite(Composite parent, int count, boolean ascFlag) {
		this.parent = parent;
		this.count = count;
		this.ascFlag = ascFlag;
	}
	
	public ComponentComposite(Composite parent, int count, boolean ascFlag, boolean checkFlag) {
		this.parent = parent;
		this.count = count;
		this.ascFlag = ascFlag;
		this.checkFlag = checkFlag;
	}
	
	public ComponentComposite(Composite parent, ADTable adTable, int count, boolean ascFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.count = count;
		this.ascFlag = ascFlag;
	}
	
	public ComponentComposite(Composite parent, ADTable adTable, int count, boolean ascFlag, boolean checkFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.count = count;
		this.ascFlag = ascFlag;
		this.checkFlag = checkFlag;
	}
	
	public void init() {
		if (adTable == null) {
			adTable = getDefaultADTable();
		}		
		tableManager = new ListTableManager(adTable, checkFlag);
		tableManager.setSortFlag(false);
		tableManager.getTableManager().setAdapterFactory(createAdapterFactory());
		tableManager.newViewer(parent);
		
		tableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection structuredSelection = (StructuredSelection) event.getSelection();
				selectedObj = (ComponentUnit) structuredSelection.getFirstElement();
				if (selectedObj != null) {
					currentPositon = selectedObj.getPosition();
				}
			}
		});
		tableManager.addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if(checked) {
					if(!CollectionUtils.isEmpty(eventObjects) && eventObjects.size() == count) {
						currentPositon = ((ComponentUnit)eventObjects.get(0)).getPosition();
					}
				}
			}			
		});
		initComponents(new ArrayList<ComponentUnit>());
	}
	
	public ItemAdapterFactory createAdapterFactory() {
		if (itemAdapters != null) {
			ItemAdapterFactory factory = new ItemAdapterFactory();
			for (String name : itemAdapters) {
				Object object = TableItemAdapterExtensionPoint.getObjectClass(name);
				ItemAdapter adapter = TableItemAdapterExtensionPoint.getAdapter(name);
				if (object != null && adapter != null) {
					factory.registerAdapter(object.getClass(), adapter);
				}
			}
			return factory;
		}
		return null;
	}
	
	public void initComponents(List<ComponentUnit> components) {
		initComponents(components, count);
	}
	
	public void initComponents(List<ComponentUnit> components, int count) {
		carrierMap.clear();
		initCompIdPositionMap.clear();
		initCompIdMap.clear();
		this.count = count;
		if (ascFlag) {
			for (int i = 1; i <= count; i++) {
				ComponentUnit component = new ComponentUnit();
				component.setPosition(String.valueOf(i));
				carrierMap.put(String.valueOf(i), component);
			}
		} else {
			for (int i = count; i >= 1; i--) {
				ComponentUnit component = new ComponentUnit();
				component.setPosition(String.valueOf(i));
				carrierMap.put(String.valueOf(i), component);
			}
		}
		
		for (ComponentUnit component : components) {
			carrierMap.put(component.getPosition(), component);
			initCompIdMap.put(component.getComponentId(), component);
			initCompIdPositionMap.put(component.getComponentId(), component.getPosition());
		}
		if (MapUtils.isNotEmpty(initCarrierMap)) {
			carrierMap.putAll(initCarrierMap);
		}
		tableManager.setInput(getCarrierComponents());
		selectedObj = null;
	}
	
	public boolean addComponent(ComponentUnit component, String position, OccupationPolicy occupationPolicy) {
		ComponentUnit curComponent = carrierMap.get(position);
		if (curComponent != null) {
			// 先考虑是否已经 加到了右边列表
			List<? extends Object> input = tableManager.getInput();
			for (Object object : input) {
				ComponentUnit curUnit = (ComponentUnit) object;
				if (StringUtil.isEmpty(component.getComponentId())) {
					return false;
				}
				
				if (component.getComponentId().equals(curUnit.getComponentId())) {
					UI.showError(Message.getString("wip.durable_assign_component_repeat")  + ":" + curUnit.getComponentId());
					return false;
				}
			}
			
			if (!StringUtil.isEmpty(curComponent.getComponentId())) {
				//如果当前位置已经有Component
				switch (occupationPolicy) {
				case REPLACE:
					component.setPosition(position);
					carrierMap.put(position, component);
					break;
				case IGNORE:
					break;
				case REJECT:
					UI.showError(Message.getString("wip.wafer_transfer_fail")  + ":" + curComponent.getComponentId());
					return false;
				}
			} else {
				component.setPosition(position);
				carrierMap.put(position, component);
			}
			tableManager.setInput(getCarrierComponents());
			selectedObj = null;
			return true;
		}
		return false;
	}
	
	public boolean checkAddComponent(ComponentUnit component, String position, OccupationPolicy occupationPolicy) {
		ComponentUnit curComponent = carrierMap.get(position);
		if (curComponent != null) {
			// 先考虑是否已经 加到了右边列表
			List<? extends Object> input = tableManager.getInput();
			for (Object object : input) {
				ComponentUnit curUnit = (ComponentUnit) object;
				if (StringUtil.isEmpty(component.getComponentId())) {
					return false;
				}
				
				if (component.getComponentId().equals(curUnit.getComponentId())) {
					UI.showError(Message.getString("wip.durable_assign_component_repeat")  + ":" + curUnit.getComponentId());
					return false;
				}
			}
			
			if (!StringUtil.isEmpty(curComponent.getComponentId())) {
				//如果当前位置已经有Component
				switch (occupationPolicy) {
				case REPLACE:
					component.setPosition(position);
					carrierMap.put(position, component);
					break;
				case IGNORE:
					break;
				case REJECT:
					UI.showError(Message.getString("wip.wafer_transfer_fail")  + ":" + curComponent.getComponentId());
					return false;
				}
			} 
		
			selectedObj = null;
			return true;
		}
		return false;
	}
	
	public boolean removeComponent(String position) {
		ComponentUnit curComponent = carrierMap.get(position);
		if (!StringUtil.isEmpty(curComponent.getComponentId())) {
			ComponentUnit component = new ComponentUnit();
			component.setPosition(position);
			carrierMap.put(position, component);
		} 
		tableManager.setInput(getCarrierComponents());
		selectedObj = null;
		return true;
	}
	
	public boolean removeComponent(ComponentUnit component) {
		for (String position : carrierMap.keySet()) {
			ComponentUnit curComponent = carrierMap.get(position);
			if (!StringUtil.isEmpty(curComponent.getComponentId()) 
					&& curComponent.getComponentId().equals(component.getComponentId())) {
				ComponentUnit newComponent = new ComponentUnit();
				newComponent.setPosition(position);
				carrierMap.put(position, component);
				tableManager.setInput(getCarrierComponents());
				break;
			}
		}
		selectedObj = null;
		return true;
	}
	
	/**
	 * 按照顺序放置Component
	 * 从指定的位置开始
	 * @throws CloneNotSupportedException 
	 */
	public List<ComponentUnit> addComponentList(List<ComponentUnit> componentUnits, String position, OccupationPolicy occupationPolicy) throws CloneNotSupportedException {
		boolean startFlag = false;
		int i = 0;
		
		List<ComponentUnit> addedComponentUnits = new ArrayList<ComponentUnit>();
		for (String key : carrierMap.keySet()) {
			if (key.equals(position)) {
				startFlag = true;
			}
			if (startFlag) {
				if (i == componentUnits.size()) {
					break;
				}
				ComponentUnit component = componentUnits.get(i);
				if (!addComponent((ComponentUnit)component.clone(), key, occupationPolicy)) {
					return addedComponentUnits;
				}
				addedComponentUnits.add(component);
				i++;
			}
		}
		
		return addedComponentUnits;
	}
	
	/**
	 * 按照顺序放置Component
	 * 从第一个空的位置开始
	 */
	public List<ComponentUnit> addComponentAppend(List<ComponentUnit> componentUnits) {
		boolean startFlag = false;
		int i = 0;
		
		List<ComponentUnit> addedComponentUnits = new ArrayList<ComponentUnit>();
		for (String position : carrierMap.keySet()) {
			if (StringUtil.isEmpty(carrierMap.get(position).getComponentId())) {
				startFlag = true;
			}
			if (startFlag) {
				if (i == componentUnits.size()) {
					break;
				}
				ComponentUnit component = componentUnits.get(i);
				if (!addComponent(component, position, OccupationPolicy.REJECT)) {
					return addedComponentUnits;
				}
				addedComponentUnits.add(component);
				i++;
			}
		}
		
		return addedComponentUnits;
	}
	
	public Map<DiffType, List<ComponentUnit>> getComponentsDiff() {
		List<ComponentUnit> addComponents = new ArrayList<ComponentUnit>();
		List<ComponentUnit> removeComponents = new ArrayList<ComponentUnit>();
		List<ComponentUnit> changeComponents = new ArrayList<ComponentUnit>();
		
		for (String curPosition : carrierMap.keySet()) {
			String componentId = carrierMap.get(curPosition).getComponentId();
			if (!StringUtil.isEmpty(componentId)) {
				if (!initCompIdPositionMap.keySet().contains(componentId)) {
					//如果不在初始Carrier
					addComponents.add(carrierMap.get(curPosition));
				} else {
					//如果在初始Carrier,比较位置是否相同
					if (!curPosition.equals(initCompIdPositionMap.get(componentId))) {
						changeComponents.add(carrierMap.get(curPosition));
					}
				}
			}
		}
		
		for (String initCompId : initCompIdPositionMap.keySet()) {
			boolean existFlag = false;
			for (String curPosition : carrierMap.keySet()) {
				String componentId = carrierMap.get(curPosition).getComponentId();
				if (initCompId.equals(componentId)) {
					existFlag = true;
					continue;
				}
			}
			if (!existFlag) {
				ComponentUnit removeComponent = initCompIdMap.get(initCompId);
				removeComponent.setFromPosition(initCompIdPositionMap.get(initCompId));
				removeComponents.add(removeComponent);
			}
		}
		
		Map<DiffType, List<ComponentUnit>> diffMap = new HashMap<DiffType, List<ComponentUnit>>();
		if (addComponents.size() > 0) {
			diffMap.put(DiffType.ADD, addComponents);
		}
		if (removeComponents.size() > 0) {
			diffMap.put(DiffType.REMOVE, removeComponents);
		}
		if (changeComponents.size() > 0) {
			diffMap.put(DiffType.CHANGE, changeComponents);
		}
		return diffMap;
	}
	
	/**
	 * 获取组件列表，包含空为
	 * @return
	 */
	public List<ComponentUnit> getCarrierComponents() {
		List<ComponentUnit> compList = new ArrayList<ComponentUnit>();
		for (ComponentUnit comp : carrierMap.values()) {
			compList.add(comp);
		}
		return compList;
	}
	
	/**
	 * 获取选片组件列表
	 * @return
	 */
	public List<ComponentUnit> getInitCarrierComponents() {
		List<ComponentUnit> compList = new ArrayList<ComponentUnit>();
		if (MapUtils.isNotEmpty(initCarrierMap)) {
			for (ComponentUnit comp : initCarrierMap.values()) {
				compList.add(comp);
			}
			carrierMap.putAll(initCarrierMap);
		}
		return compList;
	}
	
	/**
	 * 获取组件列表，去除空位
	 * 判断组件号是否为空，来断定是否为空位
	 * @return
	 */
	public List<ComponentUnit> getRealComponents() {
		List<ComponentUnit> compList = new ArrayList<ComponentUnit>();
		for (ComponentUnit comp : carrierMap.values()) {
			if (!StringUtil.isEmpty(comp.getComponentId())) {
				compList.add(comp);
			}
		}
		return compList;
	}
	
	public static ADTable getDefaultADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		ADField adFieldPosition = new ADField();
		adFieldPosition.setName("position");
		adFieldPosition.setIsMain(true);
		adFieldPosition.setIsDisplay(true);
		adFieldPosition.setDataType(DataType.SEQUENCE);
		adFieldPosition.setDisplayLength(15l);
		adFieldPosition.setLabel(Message.getString("wip.position"));
		adFieldPosition.setLabel_zh(Message.getString("wip.position"));
		adFields.add(adFieldPosition);
		
		ADField adFieldLotId = new ADField();
		adFieldLotId.setName("lotId");
		adFieldLotId.setIsMain(true);
		adFieldLotId.setIsDisplay(true);
		adFieldLotId.setLabel(Message.getString("wip.lot_id"));
		adFieldLotId.setLabel_zh(Message.getString("wip.lot_id"));
		adFields.add(adFieldLotId);
		
		ADField adFieldWaferId = new ADField();
		adFieldWaferId.setName("componentId");
		adFieldWaferId.setIsMain(true);
		adFieldWaferId.setIsDisplay(true);
		adFieldWaferId.setLabel(Message.getString("wip.component_id"));
		adFieldWaferId.setLabel_zh(Message.getString("wip.component_id"));
		adFields.add(adFieldWaferId);
		
		adTable.setFields(adFields);
		
		return adTable;
	}

	public ComponentUnit getSelectedObj() {
		return selectedObj;
	}

	public void setSelectedObj(ComponentUnit selectedObj) {
		this.selectedObj = selectedObj;
	}

	public ListTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(ListTableManager tableManager) {
		this.tableManager = tableManager;
	}

	public Map<String, String> getInitCompIdPositionMap() {
		return initCompIdPositionMap;
	}

	public Map<String, ComponentUnit> getCarrierMap() {
		return carrierMap;
	}

	public void checkedAll() {
		if (checkFlag) {
			List<ComponentUnit> componentList = (List<ComponentUnit>) tableManager.getInput();
			List<ComponentUnit> units = componentList.stream().filter(comp -> !StringUtil.isEmpty(comp.getLotId())).collect(Collectors.toList());
			for (ComponentUnit componentUnit : units) {
				tableManager.setCheckedObject(componentUnit);
			}
		}
	}
	
	public void checkedAll(List<Lot> lots) {
		if (checkFlag) {
			if (CollectionUtils.isEmpty(lots)) {
				uncheckedAll();
			} else {
				List<ComponentUnit> componentList = (List<ComponentUnit>) tableManager.getInput();
				List<ComponentUnit> units = componentList.stream().filter(comp -> !StringUtil.isEmpty(comp.getLotId())).collect(Collectors.toList());
				List<String> lotIds = lots.stream().map(Lot::getLotId).collect(Collectors.toList());
				for (ComponentUnit componentUnit : units) {
					if (lotIds.contains(componentUnit.getLotId())) {
						tableManager.setCheckedObject(componentUnit);						
					}	
				}
			}
		}
	}
	
	public void uncheckedAll() {
		if (checkFlag) {
			List<Object> checkedComponentList = tableManager.getCheckedObject();
			checkedComponentList.clear();
			tableManager.refresh();
		}
	}
	
	public void checkedObjectByLot(Long parentUnitRrn) {
		if (checkFlag) {
			List<ComponentUnit> componentList = (List<ComponentUnit>) tableManager.getInput();
			List<ComponentUnit> units = componentList.stream().filter(comp -> !StringUtil.isEmpty(comp.getLotId()) && comp.getParentUnitRrn().equals(parentUnitRrn)).collect(Collectors.toList());

			List<Object> checkedComponentList = tableManager.getCheckedObject();
			if (checkedComponentList.containsAll(units)) {
				checkedComponentList.removeAll(units);
			} else {
				checkedComponentList.addAll(units);
			}
			tableManager.refresh();
		}
	}
	
	public String getCurrentPositon() {
		return currentPositon;
	}

	public void setCurrentPositon(String currentPositon) {
		this.currentPositon = currentPositon;
	}
	
	public void addCurrentPosition() {
		if (!StringUtil.isEmpty(currentPositon)) {
			int index = 0;
			if(ascFlag) {
				index = Integer.parseInt(currentPositon) + 1;
			}else {
				index = Integer.parseInt(currentPositon) - 1;
			}
			currentPositon = index + "";
			if(ascFlag) {
				if (index > count) {
					currentPositon = String.valueOf(1);
				}
			}else {
				if (index < 1) {
					currentPositon = String.valueOf(count);
				}
			}
		}
	}
	
	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public boolean isAscFlag() {
		return ascFlag;
	}

	public void setAscFlag(boolean ascFlag) {
		this.ascFlag = ascFlag;
	}

	public String[] getItemAdapters() {
		return itemAdapters;
	}

	public void setItemAdapters(String[] itemAdapters) {
		this.itemAdapters = itemAdapters;
	}

	public Map<String, ComponentUnit> getInitCompIdMap() {
		return initCompIdMap;
	}

	public void setInitCompIdMap(Map<String, ComponentUnit> initCompIdMap) {
		this.initCompIdMap = initCompIdMap;
	}

	public void setInitCompIdPositionMap(Map<String, String> initCompIdPositionMap) {
		this.initCompIdPositionMap = initCompIdPositionMap;
	}
	
	public Map<String, ComponentUnit> getInitCarrierMap() {
		return initCarrierMap;
	}

	public void setInitCarrierMap(Map<String, ComponentUnit> initCarrierMap) {
		this.initCarrierMap = initCarrierMap;
	}
	
}
