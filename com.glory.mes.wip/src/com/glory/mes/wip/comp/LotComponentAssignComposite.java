package com.glory.mes.wip.comp;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.comp.ComponentComposite.OccupationPolicy;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;

/**
 * 应用于将每个Component作为一个Lot管理的情况
 * 需要单个跟踪,同时又需要进行载具管理的情况
 * 
 * 左边可以输入载具，也可以输入批次(一个批次就是一个Component)
 */
public class LotComponentAssignComposite extends Composite {

	private static final Logger logger = Logger.getLogger(LotComponentAssignComposite.class);

	/**
	 * 指定Source是否以Carrier形式显示,默认为true
	 * 否则将以Table的形式显示
	 */
	public boolean sourceIsCarrier = true;
	
	/**
	 * 显示源载具输入栏位
	 */
	public boolean showSourceCarrierFlag;

	/**
	 * 显示目标载具输入栏位
	 */
	public boolean showTargetCarrierFlag;
	
	public Carrier sourceCarrier;
	public Carrier targetCarrier;

	public ListTableManager sourceTableManager;
	public LotComponentComposite sourceComponentComposite;
	public LotComponentComposite targetComponentComposite;
	
	public Text txtSourceLotId;
	public Text txtSourceCarrierId;
	public Text txtTargetCarrierId;
	
	public Button btnGo;
	public Button btnBack;
	
	public LotComponentAssignComposite(Composite parent, int style, boolean sourceIsCarrier,
			boolean showSourceCarrierFlag, boolean showTargetCarrierFlag) {
		super(parent, style);
		this.sourceIsCarrier = sourceIsCarrier;
		this.showSourceCarrierFlag = showSourceCarrierFlag;
		this.showTargetCarrierFlag = showTargetCarrierFlag;
		createForm();
	}

	public void createForm() {
		try {
			GridLayout layout = new GridLayout(3, false);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(GridData.FILL_BOTH));
						
			createSourceComponent(this);
			createMiddleComponent(this);
			createTargetComponent(this);
			
		} catch (Exception e) {
			logger.error("LotComponentAssignComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createSourceComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = 720;
		targetComp.setLayoutData(gd);

		if (sourceIsCarrier) {
			if (showSourceCarrierFlag) {
				Composite labelCompsite = new Composite(targetComp, SWT.NONE);
				labelCompsite.setLayout(new GridLayout(3, false));
				
				Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
				lblCarrierId.setText(Message.getString("wip.source_carrier_id"));
				
				txtSourceCarrierId = new Text(labelCompsite, SWT.BORDER);
				GridData gText = new GridData();
				gText.widthHint = 216;
				txtSourceCarrierId.setLayoutData(gText);
				txtSourceCarrierId.setTextLimit(32);
				txtSourceCarrierId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						Text tLotId = ((Text) event.widget);
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
						switch (event.keyCode) {
						case SWT.CR:
						case SWT.KEYPAD_CR:
							String carrierId = txtSourceCarrierId.getText().toUpperCase();
							sourceCarrier = searchCarrier(carrierId, sourceComponentComposite, true);
						}
					}
				});
			}
			
			sourceComponentComposite = new LotComponentComposite(targetComp, 0, false);
			sourceComponentComposite.init();
			
			sourceComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter();
				}
			});
		} else {
			if (showSourceCarrierFlag) {
				Composite labelCompsite = new Composite(targetComp, SWT.NONE);
				labelCompsite.setLayout(new GridLayout(3, false));
				
				Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
				lblCarrierId.setText(Message.getString("wip.lot_id"));
				
				txtSourceLotId = new Text(labelCompsite, SWT.BORDER);
				GridData gText = new GridData();
				gText.widthHint = 216;
				txtSourceLotId.setLayoutData(gText);
				txtSourceLotId.setTextLimit(32);
				txtSourceLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						Text tLotId = ((Text) event.widget);
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
						switch (event.keyCode) {
						case SWT.CR:
						case SWT.KEYPAD_CR:
							String lotId = txtSourceLotId.getText().toUpperCase();
							searchLot(lotId);
						}
					}
				});
			}
			
			sourceTableManager = new ListTableManager(LotComponentComposite.getDefaultADTable(), true);
			sourceTableManager.setSortFlag(true);
			sourceTableManager.newViewer(targetComp);
			sourceTableManager.addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter();
				}
			});
		}
	}
	
	protected void createMiddleComponent(final Composite parent) {
		Composite centerComposite = new Composite(parent, SWT.NONE);
	    final GridLayout buttonLayout = new GridLayout();
	    buttonLayout.marginWidth = 2;
	    buttonLayout.marginHeight = 2;
	    centerComposite.setLayout(buttonLayout);
		GridData buttonGd = new GridData(SWT.CENTER, SWT.CENTER, false, false);
		centerComposite.setLayoutData(buttonGd);
		
		btnGo = new Button(centerComposite, SWT.PUSH);
		btnGo.setText("->");
		btnGo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));

		btnGo.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				goAdapter();
			}
		});
		
		btnBack = new Button(centerComposite, SWT.PUSH);
		btnBack.setText("<-");
		btnBack.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		btnBack.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				backAdapter();
			}
		});
	}
	
	protected void createTargetComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = 720;
		targetComp.setLayoutData(gd);

		if (showTargetCarrierFlag) {
			Composite labelCompsite = new Composite(targetComp, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(3, false));
			
			Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
			lblCarrierId.setText(Message.getString("wip.target_carrier_id"));
			
			txtTargetCarrierId = new Text(labelCompsite, SWT.BORDER);
			txtTargetCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			GridData gText = new GridData();
			gText.widthHint = 216;
			txtTargetCarrierId.setLayoutData(gText);
			txtTargetCarrierId.setTextLimit(32);
			txtTargetCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					Text tLotId = ((Text) event.widget);
					tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
					case SWT.KEYPAD_CR:
						String carrierId = txtTargetCarrierId.getText().toUpperCase();
						targetCarrier = searchCarrier(carrierId, targetComponentComposite, true);
					}
				}
			});
		}
		
		targetComponentComposite = new LotComponentComposite(targetComp, 0, false);
		targetComponentComposite.init();
		targetComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				backAdapter();
			}
		});
	}
	
	public Carrier searchCarrier(String carrierId, LotComponentComposite componentComposite, boolean isValid) {
		try {
			if (!StringUtil.isEmpty(carrierId)) {
				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
				if (carrier == null) {
					UI.showWarning(Message.getString("mm.carrier_is_not_exist"));
					componentComposite.initLots(new ArrayList<Lot>());
					return null;
				}
				if (isValid) {
					//进行有效性检查
					durableManager.checkCarrierAvailable(Env.getSessionContext(), carrier);
					if (!carrier.getIsAvailable()) {
						UI.showWarning(Message.getString(carrier.getMessage()));
						return null;
					}
				}
				
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				
				componentComposite.setCount(carrier.getCapacity().intValue());
				componentComposite.setAscFlag(carrier.getSlotDirectionAsc());
				componentComposite.initLots(lots);
				return carrier;
			} else {
				componentComposite.initLots(new ArrayList<Lot>());
			}
		} catch(Exception e) {
			logger.error("ComponentAssignComposite searchCarrier:", e);
		}
		return null;
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			sourceTableManager.add(lot);
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	protected void goAdapter() {
		try {
			if (sourceComponentComposite != null) {
				Lot sourceLot = sourceComponentComposite.getSelectedLot();
				if (sourceLot == null) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}
				String targetPosition = targetComponentComposite.getCurrentPositon();
				if (StringUtil.isEmpty(targetPosition)) {
					UI.showWarning(Message.getString("wip.target_position_is_not_select"));
					return;
				}
				String sourcePosition = sourceLot.getPosition();
				if (targetComponentComposite.addLot(sourceLot, targetPosition, OccupationPolicy.REJECT)) {
					sourceComponentComposite.removeLot(sourcePosition);
				}
				targetComponentComposite.addCurrentPosition();
			} else {
				List<Object> objects = sourceTableManager.getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}

				List<Lot> sourceLots = new ArrayList<Lot>();
				for (Object obj : objects) {
					sourceLots.add((Lot)obj);
				}
				
				String targetPosition = targetComponentComposite.getCurrentPositon();
				if (StringUtil.isEmpty(targetPosition)) {
					List<Lot> addedLots = targetComponentComposite.addLotAppend(sourceLots);
					sourceTableManager.removeList(addedLots);
				} else {
					List<Lot> addedLots = targetComponentComposite.addLotList(sourceLots, targetPosition, OccupationPolicy.REJECT);
					sourceTableManager.removeList(addedLots);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void backAdapter() {
		try {
			Lot targetLot = targetComponentComposite.getSelectedLot();
			if (targetLot == null) {
				UI.showWarning(Message.getString("wip.target_component_is_not_select"));
				return;
			}
			if (sourceComponentComposite != null) {
				//检查选中的Component是否都是sourceComponentUnits
				if (!sourceComponentComposite.getInitCompIdPositionMap().keySet().contains(targetLot.getLotId())) {
					UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
					return;
				}
				
				//必须放回原来的位置
				String sourcePosition = sourceComponentComposite.getInitCompIdPositionMap().get(targetLot.getLotId());
				String targetPosition = targetLot.getPosition();
				if (sourceComponentComposite.addLot(targetLot, sourcePosition, OccupationPolicy.REJECT)) {
					targetComponentComposite.removeLot(targetPosition);
				}
			} else {
				//检查选中的Component是否都是sourceComponentUnits
				if (targetComponentComposite.getInitCompIdPositionMap().keySet().contains(targetLot.getLotId())) {
					UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
					return;
				}
				
				String targetPosition = targetLot.getPosition();
				targetComponentComposite.removeLot(targetPosition);
				
				//to source
				if (sourceTableManager.getInput().size() == 0) {
					sourceTableManager.add(targetLot);
				} else {
					int index = sourceTableManager.getInput().size();
					sourceTableManager.insert(index, targetLot);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

}
