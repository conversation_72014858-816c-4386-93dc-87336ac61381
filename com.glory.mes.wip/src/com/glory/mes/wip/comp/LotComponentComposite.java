package com.glory.mes.wip.comp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

/**
 * 应用于将每个Component作为一个Lot管理的情况
 * 需要单个跟踪,同时又需要进行载具管理的情况
 */
public class LotComponentComposite extends ComponentComposite {

	public Map<String, Lot> lotMap = new HashMap<String, Lot>();
	
	public LotComponentComposite(Composite parent, int count, boolean ascFlag) {
		super(parent, count, ascFlag);
	}
	
	public LotComponentComposite(Composite parent, ADTable adTable, int count, boolean ascFlag, boolean checkFlag) {
		super(parent, adTable, count, ascFlag, checkFlag);
	}

	public void initLots(List<Lot> lots) {
		List<ComponentUnit> components = new ArrayList<ComponentUnit>();
		for (Lot lot : lots) {
			lotMap.put(lot.getLotId(), lot);
			components.add(ComponentUnit.createComponentByLot(lot));
		}
		super.initComponents(components);
	}
	
	public boolean addLot(Lot lot, String position, OccupationPolicy occupationPolicy) {
		ComponentUnit component = ComponentUnit.createComponentByLot(lot);
		lotMap.put(lot.getLotId(), lot);
		return super.addComponent(component, position, occupationPolicy);
	}

	public List<Lot> addLotList(List<Lot> lots, String position, OccupationPolicy occupationPolicy) throws CloneNotSupportedException {
		List<ComponentUnit> components = new ArrayList<ComponentUnit>();
		for (Lot lot : lots) {
			lotMap.put(lot.getLotId(), lot);
			components.add(ComponentUnit.createComponentByLot(lot));
		}
		components = super.addComponentList(components, position, occupationPolicy);
		
		List<Lot> addLots = new ArrayList<Lot>();
		for (ComponentUnit component : components) {
			Lot lot = lotMap.get(component.getComponentId());
			lot.setPosition(component.getPosition());
			addLots.add(lot);
		}
		return addLots;
	}
	
	public List<Lot> addLotAppend(List<Lot> lots) {
		List<ComponentUnit> components = new ArrayList<ComponentUnit>();
		for (Lot lot : lots) {
			lotMap.put(lot.getLotId(), lot);
			components.add(ComponentUnit.createComponentByLot(lot));
		}
		components = super.addComponentAppend(components);
		
		List<Lot> addLots = new ArrayList<Lot>();
		for (ComponentUnit component : components) {
			Lot lot = lotMap.get(component.getComponentId());
			lot.setPosition(component.getPosition());
			addLots.add(lot);
		}
		return addLots;
	}
	
	public boolean removeLot(String position) {
		return super.removeComponent(position);
	}
	
	public boolean removeLot(Lot lot) {
		ComponentUnit component = ComponentUnit.createComponentByLot(lot);
		return super.removeComponent(component);
	}
	
	public Lot getSelectedLot() {
		ComponentUnit component = getSelectedObj();
		if (component != null) {
			return lotMap.get(component.getComponentId());
		}
		return null;
	}
	
	
	public Map<DiffType, List<Lot>> getLotDiff() {
		Map<DiffType, List<ComponentUnit>> diffMap = super.getComponentsDiff();
		
		Map<DiffType, List<Lot>> lotDiffMap = new HashMap<DiffType, List<Lot>>();
		if (diffMap.containsKey(DiffType.ADD)) {
			List<Lot> addLots = new ArrayList<Lot>();
			for (ComponentUnit component : diffMap.get(DiffType.ADD)) {
				if (!lotMap.containsKey(component.getComponentId())) {
					UI.showError("");
					return null;
				} 
				Lot lot = lotMap.get(component.getComponentId());
				lot.setPosition(component.getPosition());
				addLots.add(lot);
			}
			lotDiffMap.put(DiffType.ADD, addLots);
		}
		if (diffMap.containsKey(DiffType.REMOVE)) {
			List<Lot> removeLots = new ArrayList<Lot>();
			for (ComponentUnit component : diffMap.get(DiffType.REMOVE)) {
				if (!lotMap.containsKey(component.getComponentId())) {
					UI.showError("");
					return null;
				} 
				Lot lot = lotMap.get(component.getComponentId());
				lot.setPosition(component.getPosition());
				removeLots.add(lot);
			}
			lotDiffMap.put(DiffType.REMOVE, removeLots);
		}
		if (diffMap.containsKey(DiffType.CHANGE)) {
			List<Lot> changeLots = new ArrayList<Lot>();
			for (ComponentUnit component : diffMap.get(DiffType.CHANGE)) {
				if (!lotMap.containsKey(component.getComponentId())) {
					UI.showError("");
					return null;
				} 
				Lot lot = lotMap.get(component.getComponentId());
				lot.setPosition(component.getPosition());
				changeLots.add(lot);
			}
			lotDiffMap.put(DiffType.CHANGE, changeLots);
		}
		return lotDiffMap;
	}
	
	public static ADTable getDefaultADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		ADField adFieldPosition = new ADField();
		adFieldPosition.setName("position");
		adFieldPosition.setIsMain(true);
		adFieldPosition.setIsDisplay(true);
		adFieldPosition.setDataType(DataType.SEQUENCE);
		adFieldPosition.setDisplayLength(15l);
		adFieldPosition.setLabel(Message.getString("wip.position"));
		adFieldPosition.setLabel_zh(Message.getString("wip.position"));
		adFields.add(adFieldPosition);
		
		ADField adFieldWaferId = new ADField();
		adFieldWaferId.setName("componentId");
		adFieldWaferId.setIsMain(true);
		adFieldWaferId.setIsDisplay(true);
		adFieldWaferId.setLabel(Message.getString("wip.lot_id"));
		adFieldWaferId.setLabel_zh(Message.getString("wip.lot_id"));
		adFields.add(adFieldWaferId);
		
		adTable.setFields(adFields);
		
		return adTable;
	}
}
