package com.glory.mes.wip.comp.hold;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.LotComponentQueryComposite;
import com.glory.mes.wip.lot.multihold.MultiHoldForm;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;

/**
 * 
 */
public class ComponentHoldSection extends EntitySection {

	public static final String KEY_MULTIHOLD = "multiHold";
	
	protected LotComponentQueryComposite queryComponent;
	
	protected AuthorityToolItem itemMultiHold;
	protected ADManager manager;
	protected ADTable muHold;
	protected EntityForm holdform;
	protected ListTableManager componentHoldForm;
	protected ListTableManager componentTableManager;
	protected ListTableManager lotTableManager;

	protected LotHold multihold = new LotHold();
	protected IField fieldHoldCode;
	protected Text comment, holdReasonText;
	protected String holdCode = new String("holdCode");
	protected String holdReason = new String("holdReason");
	protected String holdComment = new String("holdComment");
	protected String holdOwner = new String("holdOwner");
	TextField commentField;
	ADField reasonField, codeField;
	ADRefTable refTable;
	protected LotAction lotAction = new LotAction();
	protected LotComponentQueryComposite LotComponentQueryComposite;
	//ListTableManager tableManager;

	public ComponentHoldSection(ADTable adTable) {
		super(adTable);
	}


	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemMultiHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void createSectionContent(Composite parent) {
		try {
			ADTable leftTable = getADManger().getADTable(Env.getOrgRrn(), "WIPLotToGetComponent");//左边
			ADTable rightTable = getADManger().getADTable(Env.getOrgRrn(), "WIPComponentUnitHold");//右边
			lotTableManager = new ListTableManager(leftTable, true);
			componentTableManager = new ListTableManager(rightTable, true);
			LotComponentQueryComposite = new LotComponentQueryComposite(parent, SWT.NONE, leftTable, rightTable, lotTableManager, componentTableManager);
			
			section.setText(Message.getString("wip.comp_multihold_section"));
			//componentTableManager = LotComponentQueryComposite.getComponentTableManager();
			componentTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					ISelection selection = event.getSelection();
					ComponentUnit componentUnit = (ComponentUnit) ((StructuredSelection) selection).getFirstElement();
					if (componentUnit != null) {
						try {
							ADManager adManager = Framework.getService(ADManager.class);
							List<LotHold> lotHold = adManager.getEntityList(Env.getOrgRrn(), LotHold.class ,Integer.MAX_VALUE ,"componentRrn = " + componentUnit.getObjectRrn() ,"");
							componentHoldForm.setInput(lotHold);
						} catch (Exception e) {
							e.printStackTrace();
						}
						
					}
				}
			});
			//中间已Hold信息
			Composite queryholdComp = new Composite(parent, SWT.NONE);
			GridLayout querylayout = new GridLayout(1, false);
			queryholdComp.setLayout(querylayout);	        
			queryholdComp.setLayoutData(new GridData(GridData.FILL_BOTH));
			componentHoldForm = new ListTableManager(getADTable());
			componentHoldForm.newViewer(queryholdComp);
			
			
			//底部HoldCold
			Composite holdComp = new Composite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.verticalSpacing = 0;
			layout.marginHeight = 0;
			holdComp.setLayout(layout);
			holdComp.setLayoutData(new GridData(GridData.FILL_BOTH));

			holdform = new EntityForm(queryholdComp, SWT.NONE, new LotAction(), getADTable1(), form.getMessageManager());
			holdform.setLayout(new GridLayout());
			GridData gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 100;
			holdform.setLayoutData(gd);
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
		
	protected void createToolItemMultiHold(ToolBar tBar) {
		itemMultiHold = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_MULTIHOLD);
		itemMultiHold.setAuthEventAdaptor(this::multiHoldAdapter);
		itemMultiHold.setText(Message.getString("wip.multihold"));
		itemMultiHold.setImage(SWTResourceCache.getImage("hold-lot"));
//		itemMultiHold.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				multiHoldAdapter(event);
//			}
//		});
	}

	protected void multiHoldAdapter(SelectionEvent event) {
		if (holdform.validate()) {
			holdform.saveToObject();
			lotAction = (LotAction) holdform.getObject();
			multihold.setHoldCode(lotAction.getActionCode());
			multihold.setHoldOwner(lotAction.getActionOperator());
			multihold.setHoldReason(lotAction.getActionReason());
			multihold.setHoldComment(lotAction.getActionComment());
			
			List<Object> objects = componentTableManager.getCheckedObject();
			
			if (objects.size() != 0) {
				try {
					SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
					boolean sysParameter = MesCfMod.isHoldCompCascadeLot(Env.getOrgRrn(), sysParamManager);
					if(!sysParameter) {
						for (Object object : objects) {
							ComponentUnit componentUnit = (ComponentUnit) object;
							try {
								ComponentManager componentManager = Framework.getService(ComponentManager.class);
								componentManager.holdComponent(componentUnit, multihold, true, true, Env.getSessionContext());
							} catch (Exception e) {
								UI.showError(Message.getString("wip.component_hold_fail_same_code"));
								return;
							}
						}
					} else {
						try {
							Map<Long,List<ComponentUnit>> maps = getLotComponent1(objects);
							for(Long parterLotRrn :maps.keySet()) {
								LotManager lotManager = Framework.getService(LotManager.class);
								Lot lot = lotManager.getLot(parterLotRrn);
								SessionContext sc = Env.getSessionContext();
								if (itemMultiHold.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
									sc.setUserName((String) itemMultiHold.getData(LotAction.ACTION_TYPE_OPERATOR));
								}
								
								lotManager.holdLotComponent(lot, multihold, "ComponentHold", maps.get(parterLotRrn), sc);
							}
						} catch (Exception e) {
							UI.showError(Message.getString("wip.component_hold_fail"));
							return;
						}
					}
					UI.showInfo(Message.getString("common.multihold_successed"));
					if(holdform != null) {
						lotAction= new LotAction();
						holdform.setObject(lotAction);
						holdform.loadFromObject();
					}
					lotTableManager.setInput(new ArrayList<Lot>());
					componentTableManager.setInput(new ArrayList<ComponentUnit>());
					lotTableManager.refresh();
					componentTableManager.refresh();
				} catch (Exception e1) {
					e1.printStackTrace();
				}
			}
		} else {
			UI.showWarning(Message.getString("warn.required_entry"));
			return;
		}
	}
	
	//按照Lot分组Component
	public static Map<Long, List<ComponentUnit>> getLotComponent1(List<Object> objects) {
		Map<Long, List<ComponentUnit>> lotTimerInstanceMap = new HashMap<Long, List<ComponentUnit>>();
		List<ComponentUnit> componentunits = new ArrayList<ComponentUnit>();
		for(Object object : objects) {
			ComponentUnit componentUnit = (ComponentUnit) object;
			componentunits.add(componentUnit);
		}
		for (ComponentUnit componentTimerInstance : componentunits) {
			if (!lotTimerInstanceMap.containsKey(componentTimerInstance.getParentUnitRrn())) {
				lotTimerInstanceMap.put(componentTimerInstance.getParentUnitRrn(), new ArrayList<ComponentUnit>());
			}
			lotTimerInstanceMap.get(componentTimerInstance.getParentUnitRrn()).add(componentTimerInstance);
		}
		return lotTimerInstanceMap;
	}
	
	protected ADTable getADTable() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPComponentUnitHoldList");//中间
		return midTable;
	}
	
	//暂停码
	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "ComponentHoldAction");
		return midTable;
	}
}
