package com.glory.mes.wip.comp.scrap;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class ScrapByComponentDialog extends GlcBaseDialog {
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 500;
	
	private static final String CONTROL_ENTITY_FORM = "scrapCode";
	private static final String CONTROL_SCRAP_CODE = "actionCode";
	
	private static final String CONTROL_QUERY_FORM = "scrapCarrierList";
	private static final String CONTROL_COMPONENT_ID = "componentId";
	
	//TODO To append other states
	private static final List<String> NotAllowScrapState = Lists.newArrayList(ComponentUnit.STATE_SCRAP); // 已经报废的不再允许报废
	
	private EntityFormField entityForm;
	private RefTableField scrapCodeField;
	
	private QueryFormField queryForm;
	private TextField componentIdField;
	
	Lot parentLot;
	
	ADManager adManager;
	ComponentManager componentManager;

	public ScrapByComponentDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot) {
		super(adFormName, authority, eventBroker);
		
		this.parentLot = lot;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		try {
			super.createFormAction(form);
			
			subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);
	
			entityForm = form.getFieldByControlId(CONTROL_ENTITY_FORM, EntityFormField.class);
			scrapCodeField = entityForm.getFieldByControlId(CONTROL_SCRAP_CODE, RefTableField.class);
			
			queryForm = form.getFieldByControlId(CONTROL_QUERY_FORM, QueryFormField.class);
			componentIdField = queryForm.getQueryForm().getFieldByControlId(CONTROL_COMPONENT_ID, TextField.class);
			componentIdField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {	
					if (StringUtils.isNotBlank((String)newValue)) {
						newAdapter(null);
					}
				}
			});		
		
			adManager = Framework.getService(ADManager.class);
			componentManager = Framework.getService(ComponentManager.class);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	@Override
	protected void okPressed() {
		saveAdapter(null);
		
		super.okPressed();
	}
	
	protected void newAdapter(Object object) {
		try {
			// 校验报废码有没有设定
			if (scrapCodeField.getValue() == null) {
				UI.showError(Message.getString("wip.scrap_code_required"));
				return;
			}
			
			String componentId = (String)componentIdField.getValue();
			if (StringUtils.isBlank(componentId)) {
				UI.showError(Message.getString("wip.component_id_cannot_be_blank"));
				return;
			}
			// 判断ComponentUnit是否重复（当前列表中）
			List<ComponentUnit> componentUnitList = (List<ComponentUnit>) queryForm.getQueryForm().getTableManager().getInput();
			List<String> componentIds = componentUnitList.stream().map(ComponentUnit::getComponentId).collect(Collectors.toList());
			if (componentIds.contains(componentId)) {
				UI.showError(Message.getString("wip.lot.identify_component_repeat"));
				return;
			}
			// 判断ComponentUnit是否在批次中
			List<ComponentUnit> existComps = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, 0, 1,
					" parentUnitRrn = " + parentLot.getObjectRrn() + " AND componentId = '" + componentId + "'", "");
			if (existComps == null || existComps.isEmpty()) {
				UI.showError(Message.getString("wip.component_not_found_lot"));
				return;
			}
			// 校验ComponentUnit状态是否允许报废
			ComponentUnit compUnit = existComps.get(0);
			if (NotAllowScrapState.contains(compUnit.getState())) {
				UI.showError(Message.getString("error.state_not_allow"));
				return;
			}

			// 根据用户输入生成ComponentUnit记录
			compUnit.setActionCode((String) scrapCodeField.getValue());

			queryForm.getQueryForm().getTableManager().add(compUnit);			
			queryForm.getQueryForm().getTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void deleteAdapter(Object object) {
		try {
			List<ComponentUnit> selectComps = new ArrayList<ComponentUnit>();
			selectComps = (List) queryForm.getCheckedObjects();
			queryForm.getQueryForm().getTableManager().getInput().removeAll(selectComps);
			queryForm.getQueryForm().getTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void saveAdapter(Object object) {
		try {
			List<ComponentUnit> componentUnitList = (List<ComponentUnit>) queryForm.getQueryForm().getTableManager().getInput();
			if (componentUnitList == null || componentUnitList.isEmpty()) {
				return;
			}
			// 根据用户输入生成ScrapAction
			List<LotAction> scrapActions = new ArrayList<LotAction>();
			componentUnitList.forEach(unit->{
				if (Objects.nonNull(unit.getActionCode())) {
					LotAction lotAction = new LotAction();
					lotAction.setActionCode(unit.getActionCode());

					scrapActions.add(lotAction);
				}			
			});
			
			// 调用后端方法保存数据
			componentManager.scrapComponent(componentUnitList, scrapActions, null, Env.getSessionContext());
			
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected Point getInitialSize() {
		super.getShellStyle();
		Point shellSize = super.getInitialSize();
		if (minWidth == -1) {
			minWidth = MIN_DIALOG_WIDTH;
		} 
		if (minHeight == -1) {
			minHeight = MIN_DIALOG_HEIGHT;
		} 
		return new Point(Math.max(convertHorizontalDLUsToPixels(minWidth), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(minHeight), shellSize.y));
	}

}
