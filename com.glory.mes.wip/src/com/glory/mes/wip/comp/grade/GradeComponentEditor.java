package com.glory.mes.wip.comp.grade;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.extensionpoints.WizardPageExtensionPoint;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.abort.AbortDialog;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.glory.framework.core.exception.ExceptionBundle;

public class GradeComponentEditor extends GlcEditor {
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.comp.grade.GradeComponentEditor";
	
	private static final Logger logger = Logger.getLogger(GradeComponentEditor.class);
	
	// Source
	private static final String CTRL_SRC_FORM = "SrcForm";
	private static final String CTRL_SRC_LOT_FORM = "SrcLot";
	private static final String CTRL_SRC_COMP_LIST_TABLE = "SrcComp";
	// Judge
	private static final String CTRL_JUDGE_FORM = "JudgeForm";
	private static final String CTRL_JUDGE_JUDGE_FORM = "Judge";	
    private static final String   CTRL_JUDGE_OPERATION_FORM  = "OperationList";
	private static final String CTRL_JUDGE_GRADE = "grade1";
	private static final String CTRL_JUDGE_DEFECT = "grade2";
	
	private static final String CTRL_JUDGE_OP_COMPONENT_ID = "componentId";
	private static final String CTRL_JUDGE_OP_AUTO_COMMIT = "actionCode";
	// Result
	private static final String CTRL_RESULT_PARENT_FORM = "ResultParentForm";
	private static final String CTRL_RESULT_SUB_FORM[] = {"ResultAForm", "ResultBForm", "ResultCForm"};
	private static final String CTRL_RESULT_DURABLE = "durable";
	private static final String CTRL_RESULT_MAIN_QTY = "mainQty";
    private static final String       CTRL_RESULT_COMPONENT      = "ResultFormComponent";
	
	
	public static final String BUTTON_TRACK_IN_ID = "trackin";
    public static final String BUTTON_ABORT_ID = "abort";
    public static final String    BUTTON_TRACK_OUT_ID        = "trackout";
    public static final String        BUTTON_NAME_SAVE           = "save";
    public static final String        BUTTON_NAME_DELETE         = "delete";
	
    public static final String    REFNAME_GRADE_CODE         = "GradeCode";
	// Source
    private QueryFormField        srcLotQueryFormField;
	private ListTableManagerField srcCompLstTblManagerField;
	// Judge
    private EntityFormField       judgeJudgeForm;
	private RefTableField gradeField;
	private RefTableField defectField;
    private ListTableManagerField judgeOpListFiled;
	private TextField componentIdField;
	private BooleanField autoCommitField;
	// Result
	private GlcFormField resultForm;
    private Map<String, GlcFormField> gradeFormMap               = new ConcurrentHashMap<>();

    private List<Lot>                 gradeLots                  = new ArrayList<>();
	
    private ComponentManager          componentManager;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		try {
            componentManager = Framework.getService(ComponentManager.class);
			// Source Form
			GlcFormField srcForm = form.getFieldByControlId(CTRL_SRC_FORM, GlcFormField.class);
            subscribeAndExecute(eventBroker, srcForm.getFullTopic(BUTTON_TRACK_IN_ID),
					this::lotTrackInAdapter);
            subscribeAndExecute(eventBroker, srcForm.getFullTopic(BUTTON_ABORT_ID),
					this::lotAbortAdapter);

            srcLotQueryFormField = srcForm.getFieldByControlId(CTRL_SRC_LOT_FORM, QueryFormField.class);

			srcCompLstTblManagerField = srcForm.getFieldByControlId(CTRL_SRC_COMP_LIST_TABLE,
                                                                    ListTableManagerField.class);

            subscribeAndExecute(eventBroker, srcLotQueryFormField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED),
                                this::srcLotDoubleClickAdapter);

            subscribeAndExecute(eventBroker, srcCompLstTblManagerField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED),
                                this::srcCompDoubleClickAdapter);

			// Judge Form
			GlcFormField judgeForm = form.getFieldByControlId(CTRL_JUDGE_FORM, GlcFormField.class);
            subscribeAndExecute(eventBroker, judgeForm.getFullTopic(BUTTON_NAME_SAVE),
					this::saveAdapter);
            subscribeAndExecute(eventBroker, judgeForm.getFullTopic(BUTTON_NAME_DELETE),
					this::deleteAdapter);

            judgeJudgeForm = judgeForm.getFieldByControlId(CTRL_JUDGE_JUDGE_FORM,
					EntityFormField.class);
            componentIdField = judgeForm.getFieldByControlId(CTRL_JUDGE_OP_COMPONENT_ID, TextField.class);
            autoCommitField = judgeForm.getFieldByControlId(CTRL_JUDGE_OP_AUTO_COMMIT, BooleanField.class);
            judgeOpListFiled = judgeForm.getFieldByControlId(CTRL_JUDGE_OPERATION_FORM, ListTableManagerField.class);

			gradeField = judgeJudgeForm.getFieldByControlId(CTRL_JUDGE_GRADE, RefTableField.class);
			defectField = judgeJudgeForm.getFieldByControlId(CTRL_JUDGE_DEFECT, RefTableField.class);

            componentIdField.getTextControl().addKeyListener(new KeyAdapter() {
                @Override
                public void keyPressed(KeyEvent event) {
                    Text tComponentId = ((Text) event.widget);
                    tComponentId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
                    switch (event.keyCode) {
                        case SWT.CR:
                        case SWT.KEYPAD_CR:
                            judgeAdapter();
                    }
                }
            });

			// Result Form
			GlcFormField resultPForm = form.getFieldByControlId(CTRL_RESULT_PARENT_FORM, GlcFormField.class);
            subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_TRACK_OUT_ID), this::lotTrackOutAdapter);

            ADManager adManager = Framework.getService(ADManager.class);
            List<ADRefList> gradeList = adManager.getADRefList(Env.getOrgRrn(), REFNAME_GRADE_CODE);
            if (CollectionUtils.isNotEmpty(gradeList)) {
                for (int i = 0; i < gradeList.size(); i++) {
                    if (i < CTRL_RESULT_SUB_FORM.length) {

                        GlcFormField resultForm = resultPForm.getFieldByControlId(CTRL_RESULT_SUB_FORM[i],
                                                                                  GlcFormField.class);

                        gradeFormMap.put(gradeList.get(i).getText(), resultForm);

                        TextField durableTextField = resultForm.getFieldByControlId(CTRL_RESULT_DURABLE,
                                                                                    TextField.class);

                        durableTextField.getTextControl().addKeyListener(new KeyAdapter() {
                            @Override
                            public void keyPressed(KeyEvent event) {
                                Text tComponentId = ((Text) event.widget);
                                tComponentId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
                                switch (event.keyCode) {
                                    case SWT.CR:
                                    case SWT.KEYPAD_CR:
                                        durableChangeAdapter(resultForm, durableTextField.getText());
                                }
                            }
                        });

                    }
                }
            }
			

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
    public void durableChangeAdapter(GlcFormField resultForm, String durableId) {
        try {
            TextField qtyField = resultForm.getFieldByControlId(CTRL_RESULT_MAIN_QTY, TextField.class);

            ListTableManagerField resultTableField = resultForm.getFieldByControlId(CTRL_RESULT_COMPONENT,
                                                                                    ListTableManagerField.class);

            List<ComponentUnit> resultCompList = (List<ComponentUnit>) resultTableField.getListTableManager().getInput();

            long qty = resultCompList.stream().filter(resultComp -> StringUtils.equals(durableId,
                                                                                         resultComp.getDurable())).count();
            qtyField.setText(String.valueOf(qty));

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    public void srcLotDoubleClickAdapter(Object object) {
        try {
            Event event = (Event) object;
            Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
            if (lot != null && StringUtils.equals(LotStateMachine.STATE_RUN, lot.getState())) {
                addSrcCompLst(lot);
            }

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    public void srcCompDoubleClickAdapter(Object object) {
        try {
            Event event = (Event) object;
            ComponentUnit componentUnit = (ComponentUnit) event.getProperty(GlcEvent.PROPERTY_DATA);
            
            if (componentUnit != null) {
                componentIdField.setText(componentUnit.getComponentId());
            } else {
                componentIdField.setText(null);
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    public void judgeAdapter() {
        try {
            String componentId = componentIdField.getText();

            if (StringUtils.isNotEmpty(componentId)) {
                ComponentUnit component = componentManager.getComponentByComponentId(Env.getOrgRrn(), componentId);

                if (CollectionUtils.isEmpty(gradeLots)
                    || component.getParentUnitRrn().longValue() != gradeLots.get(0).getObjectRrn().longValue()) {
                    UI.showInfo(Message.getString("wip.components_not_in_selected_lot"));
                    return;
                }
                component.setLotId(gradeLots.get(0).getLotId());

                String grade = gradeField.getText();
                if (StringUtils.isEmpty(grade)) {
                    UI.showInfo(Message.getString("wip.component_set_grade"));
                    return;
                }

                GlcFormField glcFormField = gradeFormMap.get(grade);
                if (glcFormField == null) {
                    UI.showInfo(Message.getString("wip.unsupport_grade"));
                    return;
                }

                String durable = glcFormField.getFieldByControlId(CTRL_RESULT_DURABLE, TextField.class).getText();
                if (StringUtils.isEmpty(durable)) {
                    UI.showInfo(Message.getString("mm.durable_not_found"));
                    return;
                }

                String defectCode = defectField.getText();

                component.setGrade1(grade);
                component.setJudge2(defectCode);
                component.setDurable(durable);

                boolean autoCommit = autoCommitField.isChecked();
                
                // 更新judge未提交列表
                List<ComponentUnit> judgeOpList = (List<ComponentUnit>) judgeOpListFiled.getListTableManager().getInput();
                judgeOpList = judgeOpList.stream().filter(judgeComp -> !StringUtils.equals(judgeComp.getComponentId(),
                                                                                          component.getComponentId())).collect(Collectors.toList());
                judgeOpList.add(component);
                judgeOpListFiled.getListTableManager().setInput(judgeOpList);

                componentIdField.setText(null);

                if (autoCommit) {
                    saveAdapter(component);
                } 
            }

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }
	
    public void saveAdapter(Object object) {
        try {
            List<ComponentUnit> saveList = (List<ComponentUnit>) judgeOpListFiled.getListTableManager().getInput();

            if (saveList == null || saveList.isEmpty()) {
                return;
            }
            
            List<ComponentUnit> compList = new ArrayList<>();
            compList.addAll(saveList);

            componentManager.gradeComponent(compList, true, Env.getSessionContext());

            removeSrcCompLst(compList);

            removeJudgeFormData(compList);

            addResultFormData(compList);

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }
	
    public void deleteAdapter(Object object) {
		try {
            ComponentUnit componentUnit = (ComponentUnit) judgeOpListFiled.getListTableManager().getTableManager().getSelectedObject();
            List<ComponentUnit> judgeOpList = (List<ComponentUnit>) judgeOpListFiled.getListTableManager().getInput();
            judgeOpList.remove(componentUnit);
            judgeOpListFiled.getListTableManager().setInput(judgeOpList);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	public void lotTrackInAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYLOT);

            List<Object> objectList = srcLotQueryFormField.getQueryForm().getCheckedObject();
            if (CollectionUtils.isEmpty(objectList) || objectList.size() > 1) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }

            Lot lot = (Lot) objectList.get(0);

            List<Lot> lots = new ArrayList<>();
            lots.add(lot);

            context.setLots(lots);
            Step step = loadCurrentStep(lot);
			context.setStep(step);
			
			FlowWizard wizard = getTrackInWizard(context, step.getTrackInFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);			
			ChainContext wipContext = lotManager.checkTrackInConstraint(inContext
					, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			TrackInDialog dialog = new TrackInDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if ((result == Dialog.OK || result == TrackInDialog.FIN) 
					&& context.getReturnCode() == TrackInContext.OK_ID) {
                refreshSrcForm(lot);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void lotAbortAdapter(Object object) {
        try {
			InContext context = new InContext();

            List<Object> objectList = srcLotQueryFormField.getQueryForm().getCheckedObject();
            if (CollectionUtils.isEmpty(objectList) || objectList.size() > 1) {
                UI.showInfo(Message.getString("wip.lot_does_not_exist"));
                return;
            }

            Lot lot = (Lot) objectList.get(0);

            List<Lot> lots = new ArrayList<>();
            lots.addAll(getRunningLotList(lot));

			context.setLots(lots);

            Step step = loadCurrentStep(lot);
			context.setCurrentStep(step);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkAbortConstraint(
					context, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			FlowWizard wizard = getAbortWizard(context, step.getAbortFlow());

			AbortDialog dialog = new AbortDialog(UI.getActiveShell(), wizard);
            int result = dialog.open();
            if (result == Dialog.OK || result == AbortDialog.FIN) {
                refreshSrcForm(lot);
                
                List<ComponentUnit> componentUnits = componentManager.getComponentsByParentUnitRrn(lot.getObjectRrn());
                removeJudgeFormData(componentUnits);
                removeResultFormData(componentUnits);
            }
		} catch (Exception e) {
			logger.error("Error at RunningLotSection : abortAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
    public void lotTrackOutAdapter(Object object) {
        try {
            form.getMessageManager().removeAllMessages();
            TrackOutContext context = new TrackOutContext();
            context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);

            if (CollectionUtils.isEmpty(gradeLots) || gradeLots.size() > 1) {
                UI.showInfo(Message.getString("wip.lot_does_not_exist"));
                return;
            }
            
            Lot currentLot = gradeLots.get(0);

            Step step = loadCurrentStep(currentLot);
            context.setStep(step);

            FlowWizard wizard = getTrackOutWizard(context, step.getTrackOutFlow());

            InContext inContext = new InContext();
            inContext.setLots(getRunningLotList(currentLot));
            inContext.setOperator1(context.getOperator1());
            inContext.setCurrentStep(step);
            inContext.setMultiEqp(step.getIsMultiEqp());

            LotManager lotManager = Framework.getService(LotManager.class);
            ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
            if (wipContext.getReturnMessage() != null && wipContext.getReturnMessage().trim().length() > 0) {
                UI.showError(wipContext.getReturnMessage());
            }
            if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
                return;
            }
            
            List<List<ComponentUnit>> reCombineComponents = Lists.newArrayList();
            List<ComponentUnit> allComponents = Lists.newArrayList();
            gradeFormMap.forEach((grade, glcFormFiled) ->{
                ListTableManagerField resultCompListTableManagerField = glcFormFiled.getFieldByControlId(CTRL_RESULT_COMPONENT, ListTableManagerField.class);
                List<ComponentUnit> outList = (List<ComponentUnit>) resultCompListTableManagerField.getListTableManager().getInput();

                List<ComponentUnit> componentUnits = Lists.newArrayList();
                componentUnits.addAll(outList);

                allComponents.addAll(outList);

                reCombineComponents.add(componentUnits);
            });

            if (currentLot.getMainQty().intValue() != allComponents.size()) {
                UI.showInfo(Message.getString("wip.lot_not_grade_finish"));
                return;
            }

            List<String> lotIds = new ArrayList<>();
            lotIds.add(currentLot.getLotId());
            
            lotManager.trackOutReCombineComp(reCombineComponents, lotIds,
                                             InContext.COMBINE_SOURCE_TYPE_ONELOT, InContext.COMBINE_CARRIER_TYPE_NONE,
                                             inContext, Env.getSessionContext());
            UI.showInfo(Message.getString("wip.trackout_success"));

            removeResultFormData(allComponents);
            gradeLots.removeAll(gradeLots);
            srcLotQueryFormField.getQueryForm().refresh();
        } catch (Exception e) {
            logger.error("Error at RunningLotSection : trackOutAdapter() ", e);
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    private AbortWizard getAbortWizard(InContext context, String wizardName) {
		AbortWizard wizard = (AbortWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
    private TrackInWizard getTrackInWizard(TrackInContext context, String wizardName) {
        context.setOperator1(Env.getUserName());
        TrackInWizard wizard = (TrackInWizard) WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
        wizard.setContext(context);
        return wizard;
    }

    private TrackOutWizard getTrackOutWizard(TrackOutContext context, String wizardName) {
        context.setOperator1(Env.getUserName());
        TrackOutWizard wizard = (TrackOutWizard) WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
        wizard.setContext(context);
        return wizard;
    }

    private List<Lot> getRunningLotList(Lot lot) {
        List<Lot> lotList = new ArrayList<Lot>();
        try {
            LotManager lotManager = Framework.getService(LotManager.class);
            if (lot.getBatchId() != null) {
                List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
                for (Lot clot : clotList) {
                    clot = lotManager.getRunningLot(clot.getObjectRrn());

                    if (LotStateMachine.STATE_RUN.equals(clot.getState())) {
                        lotList.add(clot);
                    }
                }
            } else {
                lot = lotManager.getRunningLot(lot.getObjectRrn());
                lotList.add(lot);
            }
        } catch (Exception e) {
            logger.error("Error at RunningLotSection : getRunningLotList ", e);
            ExceptionHandlerManager.asyncHandleException(e);
        }
        return lotList;
	}
	
    private Step loadCurrentStep(Lot lot) {
        Step step = new Step();
        try {
            if (lot != null && lot.getObjectRrn() != null) {
                PrdManager prdManager = Framework.getService(PrdManager.class);
                step.setObjectRrn(lot.getStepRrn());
                step = (Step) prdManager.getSimpleProcessDefinition(step);
            }
        } catch (Exception e) {
            logger.warn("LotSection loadCurrentStep(): Step isn't exsited!");
            ExceptionHandlerManager.asyncHandleException(e);
        }

        return step;
	}
	
    private boolean checkMuiltLotGradeAvalible(Lot lot) {
        // 暂定只支持同一批次分档

        if (gradeLots.size() > 0) {
            Lot gradeLot = gradeLots.get(0);

            // 如果之前批次已经在分档，暂不允许替换批次
            if (gradeLot.getObjectRrn() != lot.getObjectRrn()) {
                // List<? extends Object> srcCompLst = srcCompLstTblManagerField.getListTableManager().getInput();
                // if (lot.getMainQty().intValue() > srcCompLst.size()) {
                // UI.showInfo("Lot has grade Component, Can't Change Lot!");
                // return false;
                // }
            }

        }
        gradeLots.removeAll(gradeLots);
        gradeLots.add(lot);
        return true;

    }

    private void addSrcCompLst(Lot lot) {
        try {
            if (CollectionUtils.isNotEmpty(gradeLots)) {
                List<ComponentUnit> oldCompList = componentManager.getComponentsByParentUnitRrn(gradeLots.get(0).getObjectRrn());
                removeResultFormData(oldCompList);
            }

            if (!checkMuiltLotGradeAvalible(lot)) {
                return;
            }

            List<ComponentUnit> components = componentManager.getComponentsByParentUnitRrn(lot.getObjectRrn());
            components = components.stream().map(component -> {
                component.setLotId(lot.getLotId());
                return component;
            }).collect(Collectors.toList());

            ListTableManager srcCompLstTableManager = srcCompLstTblManagerField.getListTableManager();

            List<ComponentUnit> srcComponents = components.stream().filter(component -> StringUtils.isEmpty(component.getGrade1())).collect(Collectors.toList());
            srcCompLstTableManager.setInput(srcComponents);

            addResultFormData(components);

            removeJudgeFormData((List<ComponentUnit>) judgeOpListFiled.getListTableManager().getInput());

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    private void removeSrcCompLst(List<ComponentUnit> components) {
        try {
            ListTableManager srcCompLstTableManager = srcCompLstTblManagerField.getListTableManager();

            srcCompLstTableManager.removeList(components);

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    private void refreshSrcForm(Lot lot) {
        try {
            srcLotQueryFormField.getQueryForm().refresh();

            addSrcCompLst(lot);
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    private void removeJudgeFormData(List<ComponentUnit> componentUnits) {
        try {
            ListTableManager judgeOpListTableManager = judgeOpListFiled.getListTableManager();

            judgeOpListTableManager.removeList(componentUnits);

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    private void addResultFormData(List<ComponentUnit> componentUnits) {
        try {
            Map<String, List<ComponentUnit>> gradeCompMap = componentUnits.stream().filter(componentUnit -> StringUtils.isNotEmpty(componentUnit.getGrade1())).collect(Collectors.groupingBy(ComponentUnit::getGrade1));

            gradeFormMap.forEach((grade, glcFormField) -> {
                List<ComponentUnit> gradeCompList = gradeCompMap.get(grade);

                ListTableManagerField resultComponentField = glcFormField.getFieldByControlId(CTRL_RESULT_COMPONENT,
                                                                                              ListTableManagerField.class);

                TextField durableField = glcFormField.getFieldByControlId(CTRL_RESULT_DURABLE, TextField.class);

                TextField qtyField = glcFormField.getFieldByControlId(CTRL_RESULT_MAIN_QTY, TextField.class);

                resultComponentField.getListTableManager().removeList(componentUnits);
                
                if (CollectionUtils.isNotEmpty(gradeCompList)) {
                    resultComponentField.getListTableManager().addList(gradeCompList);
                }

                List<ComponentUnit> resultCompList = (List<ComponentUnit>) resultComponentField.getListTableManager().getInput();

                long qty = resultCompList.stream().filter(resultComp -> StringUtils.equals(durableField.getText(),
                                                                                           resultComp.getDurable())).count();
                qtyField.setText(String.valueOf(qty));

            });

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    private void removeResultFormData(List<ComponentUnit> componentUnits) {
        try {
            gradeFormMap.forEach((grade, glcFormField) -> {
                ListTableManagerField resultComponentField = glcFormField.getFieldByControlId(CTRL_RESULT_COMPONENT,
                                                                                              ListTableManagerField.class);
                TextField qtyField = glcFormField.getFieldByControlId(CTRL_RESULT_MAIN_QTY, TextField.class);
                TextField durableField = glcFormField.getFieldByControlId(CTRL_RESULT_DURABLE, TextField.class);
                List<ComponentUnit> resultCompList = (List<ComponentUnit>) resultComponentField.getListTableManager().getInput();
                resultCompList.removeAll(componentUnits);
                resultComponentField.getListTableManager().setInput(resultCompList);

                qtyField.setText(String.valueOf(resultComponentField.getListTableManager().getInput().size()));

                durableField.setText(null);
            });

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

}
