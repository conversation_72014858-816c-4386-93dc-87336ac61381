package com.glory.mes.wip.comp;

import java.awt.Toolkit;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.FormLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * Lot/Component查询控件
 * 左边为Lot查询,右边为Component查询
 * 选择左边Lot,将Lot对应的Component加入到右边Component中
 */
public class LotComponentQueryComposite extends Composite {

	private static final Logger logger = Logger.getLogger(ComponentAssignComposite.class);
	
	protected ADManager adManager;

	protected QueryForm lotQueryForm;
	protected ADTable lotAdTable;
	protected ListTableManager lotTableManager;

	protected QueryForm componentQueryForm;
	protected ADTable componentAdTable;
	protected ListTableManager componentTableManager;

	public LotComponentQueryComposite(Composite parent, int style, ADTable lotAdTable, ADTable componentAdTable,ListTableManager lotTableManager ,ListTableManager componentTableManager) {
		super(parent, style);
		this.lotAdTable = lotAdTable;
		this.componentAdTable = componentAdTable;
		this.lotTableManager = lotTableManager;
		this.componentTableManager = componentTableManager;
		createForm();
	}
	
	public void createForm() {
		try {
			GridLayout layout = new GridLayout(2, false);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(GridData.FILL_BOTH));
	
			createLotComposite(this);
			createComponentComposite(this);
			
		} catch (Exception e) {
			logger.error("ComponentAssignComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createLotComposite(final Composite parent) {
		FormToolkit toolkit = new FFormToolKit(getShell().getDisplay());
	
		Composite parentComp = toolkit.createComposite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.widthHint = ((Toolkit.getDefaultToolkit().getScreenSize().width) / 2) - 32;
		GridLayout layout = new GridLayout();
	    layout.horizontalSpacing = 0;
	    layout.verticalSpacing = 0;
	    layout.marginHeight = 0;
	    layout.marginWidth = 0;
	    layout.marginLeft = 0;
	    layout.marginRight = 0;
	    layout.marginTop = 0;
	    layout.marginBottom = 0;
		parentComp.setLayout(layout);
		parentComp.setLayoutData(gd);
				
		Composite queryComp = toolkit.createComposite(parentComp, SWT.NONE);
        gd = new GridData(GridData.FILL_HORIZONTAL);
        queryComp.setLayoutData(gd);
        
        FormLayout formLayout = new FormLayout();
        formLayout.marginHeight = 1;
        formLayout.marginWidth = 10;
        queryComp.setLayout(formLayout);
        
        queryComp.getBackground();
        lotQueryForm = new QueryForm(getADManger(), queryComp, SWT.NONE, lotAdTable, null);
        
        SquareButton btnQuery = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
        btnQuery.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));
        
        FormData fd = new FormData();
        fd.bottom = new FormAttachment(lotQueryForm, -20, SWT.BOTTOM);
        fd.left = new FormAttachment(lotQueryForm, 20, SWT.RIGHT);
		btnQuery.setLayoutData(fd);
		btnQuery.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				lotQueryAdapter();
			}
		});
		
		Composite resultComp = toolkit.createComposite(parentComp, SWT.NONE);
		layout = new GridLayout(); 
        layout.verticalSpacing = 0;
        layout.marginHeight = 0;
        resultComp.setLayout(layout);
        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        
       //lotTableManager = new ListTableManager(lotAdTable, true);
        lotTableManager.setIndexFlag(true);
        lotTableManager.newViewer(resultComp);
        lotTableManager.addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (checked) {
					if(null == eventObjects || eventObjects.isEmpty()) {
						return;
					}
					Lot lot = (Lot) eventObjects.get(0);
					lotCheckedAdapter(lot, checked);
				}
			}			
		});	
    }
	
	protected void lotQueryAdapter() {
		try {
			String whereClause = lotQueryForm.createWhereClause();
			adManager = getADManger();
			List<Lot> lotlist = adManager.getEntityList(Env.getOrgRrn(), Lot.class,Integer.MAX_VALUE,lotAdTable.getWhereClause() +whereClause,"");
			lotTableManager.setInput(lotlist);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void createComponentComposite(final Composite parent) {
		FormToolkit toolkit = new FFormToolKit(getShell().getDisplay());
		
		Composite parentComp = toolkit.createComposite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.widthHint = ((Toolkit.getDefaultToolkit().getScreenSize().width) / 2) - 32;
		GridLayout layout = new GridLayout();
	    layout.horizontalSpacing = 0;
	    layout.verticalSpacing = 0;
	    layout.marginHeight = 0;
	    layout.marginWidth = 0;
	    layout.marginLeft = 0;
	    layout.marginRight = 0;
	    layout.marginTop = 0;
	    layout.marginBottom = 0;
		parentComp.setLayout(layout);
		parentComp.setLayoutData(gd);
						
		Composite queryComp = toolkit.createComposite(parentComp, SWT.NONE);
        gd = new GridData(GridData.FILL_HORIZONTAL);
        queryComp.setLayoutData(gd);
        
        FormLayout formLayout = new FormLayout();
        formLayout.marginHeight = 0;
        formLayout.marginWidth = 10;
        queryComp.setLayout(formLayout);
                
        componentQueryForm = new QueryForm(getADManger(), queryComp, SWT.NONE, componentAdTable, null);
      
        SquareButton btnQuery = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
        btnQuery.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));
        
        FormData fd = new FormData();
        fd.bottom = new FormAttachment(componentQueryForm, -20, SWT.BOTTOM);
        fd.left = new FormAttachment(componentQueryForm, 20, SWT.RIGHT);
		btnQuery.setLayoutData(fd);
		btnQuery.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				componentQueryAdapter();
			}
		});
		
		SquareButton clearButton = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
		clearButton.setText(Message.getString(ExceptionBundle.bundle.CommonClear()));
        
        FormData fd1 = new FormData();
        fd1.bottom = new FormAttachment(componentQueryForm, -20, SWT.BOTTOM);
        fd1.left = new FormAttachment(componentQueryForm, 125, SWT.RIGHT);
        clearButton.setLayoutData(fd1);
        clearButton.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				componentTableManager.setInput(null);
				componentTableManager.refresh();
				lotQueryAdapter();
			}
		});
				
		Composite resultComp = toolkit.createComposite(parentComp, SWT.NONE);
		layout = new GridLayout(); 
        layout.verticalSpacing = 0;
        layout.marginHeight = 0;
        resultComp.setLayout(layout);
        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        componentTableManager.setIndexFlag(true);
        componentTableManager.newViewer(resultComp);
	}
	
	protected void componentQueryAdapter() {
		try {
			String whereClause = componentQueryForm.createWhereClause();
			adManager = getADManger();
			List<ComponentUnit> lotlist = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class,Integer.MAX_VALUE,componentAdTable.getWhereClause() +whereClause,"");
			componentTableManager.setInput(lotlist);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void lotCheckedAdapter(Lot lot, boolean checked) {
		try {
			adManager = getADManger();
			List<ComponentUnit> lotComponentList = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class,Integer.MAX_VALUE,
					componentAdTable.getWhereClause() + " AND parentUnitRrn = " + lot.getObjectRrn(),"");
//			List<ComponentUnit> currInput = (List<ComponentUnit>) componentTableManager.getInput();
//			//去重校验
//			for(int i =lotComponentList.size() - 1;i >= 0 ;i--) {
//				if(currInput.contains(lotComponentList.get(i))) {
//					lotComponentList.remove(lotComponentList.get(i));
//				}
//			}
//			currInput.addAll(lotComponentList);
			componentTableManager.setInput(null);
			componentTableManager.setInput(lotComponentList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void addLotSelectionChangedListener(ISelectionChangedListener listener) {
		lotTableManager.addSelectionChangedListener(listener);
	}
	
	public void addComponentSelectionChangedListener(ISelectionChangedListener listener) {
		componentTableManager.addSelectionChangedListener(listener);
	}
	
	public ADManager getADManger() {
		if (adManager == null) {
			try {
				this.adManager = Framework.getService(ADManager.class);
			} catch (Exception e) {
				logger.error(e);
			}
		}
		return adManager;
	}
}
