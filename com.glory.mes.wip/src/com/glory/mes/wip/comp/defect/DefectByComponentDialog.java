package com.glory.mes.wip.comp.defect;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.WipDefect;
import com.glory.framework.core.exception.ExceptionBundle;

public class DefectByComponentDialog extends GlcBaseDialog {
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 500;
	
	private static final int DEFECT_COUNT = 5;
	
	private static final String CONTROL_ENTITY_FORM = "defectsGatherForm";
	private static final String CONTROL_DEFECT_CODE_1 = "defectCode";
	private static final String CONTROL_DEFECT_CODE_2 = "reserved01";
	private static final String CONTROL_DEFECT_CODE_3 = "reserved02";
	private static final String CONTROL_DEFECT_CODE_4 = "reserved03";
	private static final String CONTROL_DEFECT_CODE_5 = "reserved04";
	
	private static final String CONTROL_QUERY_FORM = "componentUnitForm";
	private static final String CONTROL_COMPONENT_ID = "componentId";
	
	private EntityFormField entityForm;
	private RefTableField[] defectCodeField = new RefTableField[DEFECT_COUNT];
	
	private QueryFormField queryForm;
	private TextField componentIdField;
	
	Lot parentLot;
	
	ADManager adManager;
	LotManager lotManager;

	public DefectByComponentDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot) {
		super(adFormName, authority, eventBroker);
		
		this.parentLot = lot;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		try {
			super.createFormAction(form);
			
			subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);
			
			entityForm = form.getFieldByControlId(CONTROL_ENTITY_FORM, EntityFormField.class);
			defectCodeField[0] = entityForm.getFieldByControlId(CONTROL_DEFECT_CODE_1, RefTableField.class);
			defectCodeField[1] = entityForm.getFieldByControlId(CONTROL_DEFECT_CODE_2, RefTableField.class);
			defectCodeField[2] = entityForm.getFieldByControlId(CONTROL_DEFECT_CODE_3, RefTableField.class);
			defectCodeField[3] = entityForm.getFieldByControlId(CONTROL_DEFECT_CODE_4, RefTableField.class);
			defectCodeField[4] = entityForm.getFieldByControlId(CONTROL_DEFECT_CODE_5, RefTableField.class);
			
			queryForm = form.getFieldByControlId(CONTROL_QUERY_FORM, QueryFormField.class);
			componentIdField = queryForm.getQueryForm().getFieldByControlId(CONTROL_COMPONENT_ID, TextField.class);
			componentIdField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {	
					if (StringUtils.isNotBlank((String)newValue)) {
						newAdapter(null);
					}
				}
			});		
		
			adManager = Framework.getService(ADManager.class);
			lotManager = Framework.getService(LotManager.class);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		saveAdapter(null);
		
		super.okPressed();
	}
	
	protected void newAdapter(Object object) {
		try {
			// 校验缺陷码有没有设定
			int defectSetUp = 0;
			for (int i = 0; i < DEFECT_COUNT; i++) {
				defectSetUp |= (defectCodeField[i].getValue() == null ? 0 : 1);
			}
			if (defectSetUp == 0) {
				UI.showError(Message.getString("wip.lot_select_defect_code"));
				return;
			}
			
			String componentId = (String)componentIdField.getValue();
			if (StringUtils.isBlank(componentId)) {
				UI.showError(Message.getString("wip.component_id_cannot_be_blank"));
				return;
			}
			// 判断ComponentUnit是否重复（当前列表中，与数据库中允许多次缺陷登记）
			List<ComponentUnit> componentUnitList = (List<ComponentUnit>) queryForm.getQueryForm().getTableManager().getInput();
			List<String> componentIds = componentUnitList.stream().map(ComponentUnit::getComponentId).collect(Collectors.toList());
			if (componentIds.contains(componentId)) {
				UI.showError(Message.getString("wip.lot.identify_component_repeat"));
				return;
			}
			// 判断ComponentUnit是否在批次中
			List<ComponentUnit> existComps = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, 0, 1,
					" parentUnitRrn = " + parentLot.getObjectRrn() + " AND componentId = '" + componentId + "'", "");
			if (existComps == null || existComps.isEmpty()) {
				UI.showError(Message.getString("wip.component_not_found_lot"));
				return;
			}
			
			// 根据用户输入生成ComponentUnit记录
			ComponentUnit compUnit = existComps.get(0);//new ComponentUnit(); 
			compUnit.setAttribute1(defectCodeField[0].getValue());
			compUnit.setAttribute2(defectCodeField[1].getValue());
			compUnit.setAttribute3(defectCodeField[2].getValue());
			compUnit.setAttribute4(defectCodeField[3].getValue());
			compUnit.setAttribute5(defectCodeField[4].getValue());

			queryForm.getQueryForm().getTableManager().add(compUnit);			
			queryForm.getQueryForm().getTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected void deleteAdapter(Object object) {
		try {
			List<ComponentUnit> selectComps = new ArrayList<ComponentUnit>();
			selectComps = (List) queryForm.getCheckedObjects();
			queryForm.getQueryForm().getTableManager().getInput().removeAll(selectComps);
			queryForm.getQueryForm().getTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void saveAdapter(Object object) {
		try {
			List<ComponentUnit> componentUnitList = (List<ComponentUnit>) queryForm.getQueryForm().getTableManager().getInput();
			if (componentUnitList == null || componentUnitList.isEmpty()) {
				return;
			}
			// 根据用户输入生成WipDefect
			componentUnitList.forEach(unit->{
				List<WipDefect> wipDefects = new ArrayList<WipDefect>();
				WipDefect wipDefect;
				if (Objects.nonNull(unit.getAttribute1())) {
					wipDefect = new WipDefect();
					wipDefect.setComponentRrn(unit.getObjectRrn());
					wipDefect.setComponentId(unit.getComponentId());
					wipDefect.setDefectCode((String) unit.getAttribute1());
					wipDefect.setCreatedBy(Env.getUserName());
					wipDefect.setUpdatedBy(Env.getUserName());
					wipDefects.add(wipDefect);
				}
				if (Objects.nonNull(unit.getAttribute2())) {
					wipDefect = new WipDefect();
					wipDefect.setComponentRrn(unit.getObjectRrn());
					wipDefect.setComponentId(unit.getComponentId());
					wipDefect.setDefectCode((String) unit.getAttribute2());
					wipDefect.setCreatedBy(Env.getUserName());
					wipDefect.setUpdatedBy(Env.getUserName());
					wipDefects.add(wipDefect);
				}
				if (Objects.nonNull(unit.getAttribute3())) {
					wipDefect = new WipDefect();
					wipDefect.setComponentRrn(unit.getObjectRrn());
					wipDefect.setComponentId(unit.getComponentId());
					wipDefect.setDefectCode((String) unit.getAttribute3());
					wipDefect.setCreatedBy(Env.getUserName());
					wipDefect.setUpdatedBy(Env.getUserName());
					wipDefects.add(wipDefect);
				}
				if (Objects.nonNull(unit.getAttribute4())) {
					wipDefect = new WipDefect();
					wipDefect.setComponentRrn(unit.getObjectRrn());
					wipDefect.setComponentId(unit.getComponentId());
					wipDefect.setDefectCode((String) unit.getAttribute4());
					wipDefect.setCreatedBy(Env.getUserName());
					wipDefect.setUpdatedBy(Env.getUserName());
					wipDefects.add(wipDefect);
				}
				if (Objects.nonNull(unit.getAttribute5())) {
					wipDefect = new WipDefect();
					wipDefect.setComponentRrn(unit.getObjectRrn());
					wipDefect.setComponentId(unit.getComponentId());
					wipDefect.setDefectCode((String) unit.getAttribute5());
					wipDefect.setCreatedBy(Env.getUserName());
					wipDefect.setUpdatedBy(Env.getUserName());
					wipDefects.add(wipDefect);
				}
				// 调用后端方法保存数据
				lotManager.saveWipDefect(unit, true, wipDefects, Env.getSessionContext());
			});
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected Point getInitialSize() {
		super.getShellStyle();
		Point shellSize = super.getInitialSize();
		if (minWidth == -1) {
			minWidth = MIN_DIALOG_WIDTH;
		} 
		if (minHeight == -1) {
			minHeight = MIN_DIALOG_HEIGHT;
		} 
		return new Point(Math.max(convertHorizontalDLUsToPixels(minWidth), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(minHeight), shellSize.y));
	}

}
