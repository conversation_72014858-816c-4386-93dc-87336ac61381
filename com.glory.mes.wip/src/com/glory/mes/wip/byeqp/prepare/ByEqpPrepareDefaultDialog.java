package com.glory.mes.wip.byeqp.prepare;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.widgets.SquareButton;
import org.osgi.service.event.Event;

import com.glory.common.fel.common.StringUtils;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcTecn;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class ByEqpPrepareDefaultDialog extends ByEqpPrepareDialog {

	private static final Logger logger = Logger.getLogger(ByEqpPrepareDefaultDialog.class);
	
	public static final String CHANGE_EDC_TECN_DIALOG_FORM_NAME = "WIPLotByEqpChangeEdcTecnDialog";
	
	public static final String FIELD_LEFTFORM = "leftFrom";
	public static final String FIELD_LOTLIST = "lotList";
	public static final String FIELD_PREPARELIST = "prepareList";
	
	public static final String FIELD_JOBID = "jobId";
	public static final String FIELD_COMPONENTID = "componentId";
	
	public static final String BUTTON_CREATE_JOB = "createJob";
	public static final String BUTTON_CANCEL_JOB = "cancelJob";
	public static final String BUTTON_CHANGE_JOBID = "changeJobId";
	public static final String BUTTON_CHOOSE_WAFER = "chooseWafer";
	
	protected CustomField lotListField;
	protected ListTableManagerField prepareListField;
	protected TextField leftComponentIdField;
	protected TextField rightComponentIdField;
	
	protected TextField leftJobIdField;
	protected TextField rightJobIdField;
	
	protected SquareButton createJob;	
	protected SquareButton leftChooseWafer;
	protected SquareButton rightChooseWafer;
	
	private boolean isRTDAvailable = false;
	
	private Map<String, String> lotMap = Maps.newHashMap();
	public static final String KEY_LOTRRIN = "lotRrn";
	public static final String KEY_EDCNAME = "edcName";
	public static final String KEY_SAMPLINGPLAN = "samplingPlan";
	public static final String KEY_SAMPLINGSIZE = "samplingSize";
	
	public ByEqpPrepareDefaultDialog() {
		super();
	}
	
	public ByEqpPrepareDefaultDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LEFTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_CHOOSE_WAFER), this::leftChooseWaferAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LEFTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_CREATE_JOB), this::createJobAdapter);
			
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CANCEL_JOB), this::cancelJobAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHANGE_JOBID), this::changeJobIdAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHOOSE_WAFER), this::rightChooseWaferAdapter);
		
		lotListField = form.getFieldByControlId(FIELD_LEFTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_LOTLIST, CustomField.class);
		prepareListField = form.getFieldByControlId(FIELD_PREPARELIST, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, lotListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::lotSelectionChangeAdapter);
		subscribeAndExecute(eventBroker, prepareListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::lotPrepareSelectionChangeAdapter);
		
		leftJobIdField = form.getFieldByControlId(FIELD_LEFTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_JOBID, TextField.class);
		rightJobIdField = form.getFieldByControlId(FIELD_JOBID, TextField.class);
		
		leftComponentIdField = form.getFieldByControlId(FIELD_LEFTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_COMPONENTID, TextField.class);
		rightComponentIdField = form.getFieldByControlId(FIELD_COMPONENTID, TextField.class);
				
		leftChooseWafer = (SquareButton) form.getButtonByControl(FIELD_LEFTFORM, BUTTON_CHOOSE_WAFER);
		createJob = (SquareButton) form.getButtonByControl(FIELD_LEFTFORM, BUTTON_CREATE_JOB);
		rightChooseWafer = (SquareButton) form.getButtonByControl(null, BUTTON_CHOOSE_WAFER);
		
		leftChooseWafer.setEnabled(false);
		createJob.setEnabled(true);
		rightChooseWafer.setEnabled(false);
		
		this.currentEqp = (Equipment) propValues.get("leftFrom-equipmentInfo");
	}
	
	/**
	 * 批次选中事件
	 * @param obj
	 */
	protected void lotSelectionChangeAdapter(Object object) {
		leftChooseWafer.setEnabled(false);
		Event event = (Event) object;
		Lot lot = (Lot)event.getProperty(GlcEvent.PROPERTY_DATA);
		if (lot != null) {
			getLotEdcInfo(lot);
			if (StringUtils.isNotEmpty(lotMap.get(KEY_EDCNAME))) {
				leftChooseWafer.setEnabled(true);
				leftComponentIdField.setValue(getLastSamplingComponentIds(lot));
			} else {
				leftComponentIdField.setValue(null);
			}
		} else {
			leftComponentIdField.setValue(null);
		}
		leftComponentIdField.refresh();		
	}
	
	/**
	 * LotPrepare选中事件
	 * @param obj
	 */
	protected void lotPrepareSelectionChangeAdapter(Object object) {
		rightChooseWafer.setEnabled(false);
		Event event = (Event) object;
		LotPrepare lotPrepare = (LotPrepare)event.getProperty(GlcEvent.PROPERTY_DATA);
		if (lotPrepare != null) {
			getLotEdcInfo(lotPrepare.getLot());
			if (StringUtils.isNotEmpty(lotMap.get(KEY_EDCNAME))) {
				rightChooseWafer.setEnabled(true);
				rightComponentIdField.setValue(getLastSamplingComponentIds(lotPrepare.getLot()));
			} else {
				rightComponentIdField.setValue(null);
			}
		} else {
			rightComponentIdField.setValue(null);
		}
		rightComponentIdField.refresh();
	}
	
	/**
	 * 获取EDC相关信息
	 * @param lot
	 */
	protected void getLotEdcInfo(Lot lot) {
		try {
			lotMap.clear();
			if (ProcessUnit.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType()) && 
					(LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_DISP.equals(lot.getState()))) {
				List<AbstractEdcSet> edcSets = lotManager.getEdcSets(lot, null, true, Env.getSessionContext());
				if (CollectionUtils.isNotEmpty(edcSets)) {
					lotMap.put(KEY_EDCNAME, edcSets.get(0).getName());
					lotMap.put(KEY_LOTRRIN, String.valueOf(lot.getObjectRrn()));
					for (AbstractEdcSet edcSet : edcSets) {
	    				if (edcSet instanceof EdcItemSet) {
	    					if (!StringUtil.isEmpty(edcSet.getComponentSamplingPlan())) {
	    						lotMap.put(KEY_SAMPLINGPLAN, edcSet.getComponentSamplingPlan());
	    					}
	    					List<EdcItemSetLine> edcSetLines = ((EdcItemSet)edcSet).getItemSetLines();
							for (EdcItemSetLine edcSetLine : edcSetLines) {
								if (edcSetLine.getComp() != null) {
									lotMap.put(KEY_SAMPLINGSIZE, String.valueOf(edcSetLine.getSampleSize()));
	    							return;
								}
							}
	    				}
	    			}
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDefaultDialog : edit() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	protected String getLastSamplingComponentIds(Lot lot) {
		try {
			List<EdcTecn> edcTecns = edcManager.getTecnByLotCurrentStep(Env.getOrgRrn(), lot.getLotId(), lot.getStepName());
			if (CollectionUtils.isNotEmpty(edcTecns)) {
				return edcTecns.get(0).getComponentList();
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDefaultDialog : getLastSamplingComponentIds() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
		return null;
	}
	
	/**
	 * 左边选择Wafer
	 * @param object
	 */
	protected void leftChooseWaferAdapter(Object object) {
		chooseWaferAdapter(1);
	}
	
	/**
	 * 右边选择Wafer
	 * @param object
	 */
	protected void rightChooseWaferAdapter(Object object) {
		chooseWaferAdapter(2);
	}
	
	/**
	 * 抽样选择Wafer,修改EdcTecn
	 * @param refreshPart
	 */
	protected void chooseWaferAdapter(int refreshPart) {
		try {
			ByEqpChangeEdcTecnDialog dialog = new ByEqpChangeEdcTecnDialog(CHANGE_EDC_TECN_DIALOG_FORM_NAME, null, eventBroker);
			Lot lot = lotManager.getLotWithComponentOrderByPosition(Long.valueOf(lotMap.get(KEY_LOTRRIN)), true);
			List<EdcTecn> edcTecns = edcManager.getTecnByLotCurrentStep(Env.getOrgRrn(), lot.getLotId(), lot.getStepName());
			
			Map<String, Object> propValues = Maps.newHashMap();
			propValues.put(ByEqpChangeEdcTecnDialog.FIELD_COMPONENT_LIST, (List)lot.getSubProcessUnit());
			BigDecimal sampleSize = BigDecimal.valueOf(Long.valueOf(lotMap.get(KEY_SAMPLINGSIZE)));
			if (sampleSize.compareTo(lot.getMainQty()) != -1) {
				sampleSize = lot.getMainQty();
			}
			propValues.put(ByEqpChangeEdcTecnDialog.FIELD_SAMPLE_SIZE, sampleSize);
			dialog.setPropValues(propValues);
			
			if (CollectionUtils.isNotEmpty(edcTecns)) {
				dialog.setSamplingComponentIds(edcTecns.get(0).getComponentIdList());
			}
			lot.setEdcSetName(DBUtil.toString(lotMap.get(KEY_EDCNAME)));
			lot.setAttribute1(DBUtil.toString(lotMap.get(KEY_SAMPLINGPLAN)));
			dialog.setLot(lot);
			
			dialog.setCloseAdaptor(new Consumer<ByEqpChangeEdcTecnDialog>() {
				
				@Override
				public void accept(ByEqpChangeEdcTecnDialog t) {
					if (refreshPart == 1) {
						leftComponentIdField.setValue(getLastSamplingComponentIds(lot));
						leftComponentIdField.refresh();
					} else {
						rightComponentIdField.setValue(getLastSamplingComponentIds(lot));
						rightComponentIdField.refresh();
					}
				}
			});
			dialog.open();
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDefaultDialog : chooseWaferAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	/**
	 * 创建Job
	 */
	protected boolean createJobAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			
			String jobId = leftJobIdField.getText();
			// 检查JobID是否重复
			if (!StringUtil.isEmpty(jobId)) {
				if (!ValidatorFactory.isValid(DataType.INTEGER, leftJobIdField.getText())) {
					ADField adField = (ADField) leftJobIdField.getADField();
					form.getMessageManager().addMessage(adField.getName() + "common.isvalid", 
							String.format(Message.getString("common.isvalid"), I18nUtil.getI18nMessage(adField, "label"), DataType.INTEGER), null,
							MsgType.MSG_ERROR.getIndex(), leftJobIdField.getControls()[leftJobIdField.getControls().length - 1]);
					return false;
				}
			}
			
			boolean flag = super.createJobAdapter(object);
			if (flag) {
				leftJobIdField.setValue(null);
				leftJobIdField.refresh();
				return true;
			}			
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDefaultDialog : createAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
		return false;
	}
	
	/**
	 * 获取BatchLot
	 */
	@Override
	protected List<Lot> getBatchLotList() {
		ListTableManager tableManager = ((LotListComposite)lotListField.getCustomComposite()).getTableManager();
		List<Object> checkedObjects = tableManager.getCheckedObject();
		
		if (CollectionUtils.isNotEmpty(checkedObjects)) {
			// 批次重新查询最新的PPID、Reticle
			List<Lot> batchLots = checkedObjects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
			
			List<Lot> allLots = (List<Lot>) tableManager.getInput();
			List<Lot> sameBatchLots = Lists.newArrayList();
			Optional<Lot> optBatchLot = allLots.stream().filter(lot -> StringUtils.isNotEmpty(lot.getBatchId())).findFirst();
			if (optBatchLot.isPresent() && currentEqp.getIsBatch()) {
				Lot source = optBatchLot.get();
				sameBatchLots = batchLots.stream().filter(lot -> StringUtils.isNotEmpty(lot.getBatchId()) && source.getBatchId().equals(lot.getBatchId())).collect(Collectors.toList());
			}
			batchLots.addAll(sameBatchLots);
			batchLots = batchLots.stream().distinct().collect(Collectors.toList());
			return batchLots;
		}
		return null;
	}

	@Override
	protected String getJobId() {
		return leftJobIdField.getText();
	}

	/**
	 * 修改JobID号
	 */
	protected void changeJobIdAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
		
			if (!ValidatorFactory.isValid(DataType.INTEGER, rightJobIdField.getText())) {
				ADField adField = (ADField) rightJobIdField.getADField();
				form.getMessageManager().addMessage(adField.getName() + "common.isvalid", 
						String.format(Message.getString("common.isvalid"), I18nUtil.getI18nMessage(adField, "label"), DataType.INTEGER), null,
						MsgType.MSG_ERROR.getIndex(), rightJobIdField.getControls()[rightJobIdField.getControls().length - 1]);
				return;
			}
			
			super.changeJobIdAdapter(object);
			
			rightJobIdField.setValue(null);
			rightJobIdField.refresh();
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDefaultDialog : changeJobIdAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	@Override
	protected List<Object> getCheckedLotPrepareList() {
		return prepareListField.getListTableManager().getCheckedObject();
	}

	@Override
	protected String getChangeJobId() {
		return rightJobIdField.getText();
	}
	
	/**
	 * 刷新
	 * @throws Exception
	 */
	protected void refreshAdapter(Object object) {
		try {
			List<LotPrepare> lotPrepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), null, true);
			prepareListField.setValue(lotPrepares);
			prepareListField.refresh();
			
			// 刷新批次列表，尽量保证列表中的数据是最新的		
			List<Lot> waittingLots = Lists.newArrayList();
			if (isRTDAvailable) {
				// RTD的查询
				waittingLots = ByEqpUtil.getRtdLots(currentEqp, false);
			} else {
				waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(), LotStateMachine.STATE_WAIT, Env.getSessionContext());
			}
			
			waittingLots = ByEqpUtil.filterWaitingLots(waittingLots, currentEqp);
			ListTableManager tableManager = ((LotListComposite)lotListField.getCustomComposite()).getTableManager();
			tableManager.setInput(waittingLots);
		} catch (Exception e) {
			ExceptionHandlerManager.syncHandleException(e);
		}
	}

	public boolean isRTDAvailable() {
		return isRTDAvailable;
	}

	public void setRTDAvailable(boolean isRTDAvailable) {
		this.isRTDAvailable = isRTDAvailable;
	}
	
}
