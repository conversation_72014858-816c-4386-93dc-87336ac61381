package com.glory.mes.wip.byeqp.buffer;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.edc.client.EDCManager;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.byeqp.ByEqpDefaultPage;
import com.glory.mes.wip.byeqp.extensionpoint.IByEqpPage;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotPrepareManager;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.custom.depend.MsgConsoleView;
import com.glory.mes.wip.lot.action.LotActionFactory;
import com.glory.mes.wip.lot.action.LotMenuAction;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotEquipmentUnit;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Maps;

public class ByEqpBufferPage extends ByEqpDefaultPage implements IByEqpPage, IRefresh {
	
	private static final Logger logger = Logger.getLogger(ByEqpBufferPage.class);
	
	private static final String BUTTON_FURANCE_TRACKIN = "furanceTrackIn";
	public static final String BUTTON_DUMMYTRACKIN = "dummyTrackIn";
	public static final String BUTTON_INCOMINGLOT = "inComingLot";
	public static final String BUTTON_BUFFERINFO = "bufferInfo";
	
	private ToolItem itemTrackIn;
	private ToolItem itemDummyTrackIn;
	private ToolItem itemInComingLot;
	private ToolItem itemBuffer;
	
	protected void createFormAction(GlcForm form) {
		//获取控制台
		console = (ByEqpConsole) MsgConsoleView.getInstance();
		
		//获取左边的树形设备点击事件
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::eqpSelectionChanged);	
		
		try {
			adManager = Framework.getService(ADManager.class);
			rasManager = Framework.getService(RASManager.class);
			prdManager = Framework.getService(PrdManager.class);
			edcManager = Framework.getService(EDCManager.class);
			durableManager = Framework.getService(DurableManager.class);
			carrierLotManager = Framework.getService(CarrierLotManager.class);
			lotManager = Framework.getService(LotManager.class);
			prepareManager = Framework.getService(LotPrepareManager.class);				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		//获取左边的树形设备列表
		eqpTreeField = rootForm.getFieldByControlId(FIELD_EQPTREE, CustomField.class);	
				
		dispatchField = form.getFieldByControlId(FIELD_DISPATCHFLAG, BooleanField.class);
		if (dispatchField != null) {
			dispatchField.addValueChangeListener(new IValueChangeListener() {			
				@Override
				public void valueChanged(Object sender, Object newValue) {
					waitRefreshAdapter(null);
				}
			});
		}
		
		fieldEqp = form.getFieldByControlId(FIELD_EQUIPMENT_ID, TextField.class);
		fieldLot = form.getFieldByControlId(FIELD_LOT_ID, TextField.class);
		fieldCarrier = form.getFieldByControlId(FIELD_CARRIER_ID, TextField.class);			
		fieldRunning = form.getFieldByControlId(FIELD_RUNNINGLOTS, CustomField.class);
		fieldWaitting = form.getFieldByControlId(FIELD_WAITTINGLOTS, CustomField.class);		
		
		LotListComposite fieldRunningListComposite = (LotListComposite) fieldRunning.getCustomComposite();
		LotListComposite fieldWaittingListComposite = (LotListComposite) fieldWaitting.getCustomComposite();
		//重载右键菜单
		List<LotMenuAction> actions = LotActionFactory.getLotMenuActionByAuthName(this.authority);
		for (LotMenuAction action : actions) {
			if (!action.getAuthorityKey().endsWith(LotActionFactory.ACTION_SHIP) && !action.getAuthorityKey().endsWith(LotActionFactory.ACTION_SHIP_CANCEL)) {
				fieldWaittingListComposite.addCustomMenuAction(action);
			}
			if (action.getAuthorityKey().endsWith(LotActionFactory.ACTION_RUN_HOLD) || action.getAuthorityKey().endsWith(LotActionFactory.ACTION_RUN_RELEASE)) {
				fieldRunningListComposite.addCustomMenuAction(action);
			}
		}
		fieldWaittingListComposite.initMenu();
		fieldWaittingListComposite.setRefreshObject(this);
		fieldRunningListComposite.initMenu();
		fieldRunningListComposite.setRefreshObject(this);
			
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_EQUIPMENT_ID, GlcEvent.EVENT_ENTERPRESSED), this::equipmentEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOT_ID, GlcEvent.EVENT_ENTERPRESSED), this::lotEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_CARRIER_ID, GlcEvent.EVENT_ENTERPRESSED), this::carrierEnterPressed);
		subscribeAndExecute(eventBroker, fieldWaitting.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::waitingLotSelectionAdaptor);	
		subscribeAndExecute(eventBroker, fieldRunning.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::runningLotSelectionAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PREPARE), this::prepareAdapter);						
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_TRACKIN), this::trackInAdapter);					
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_WAITREFRESH), this::waitRefreshAdapter);				
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DOCP), this::docpAdapter);				
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_TRACKOUT), this::trackOutAdapter);		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ABORT), this::abortAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RUNREFRESH), this::runRefreshAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DUMMYTRACKIN), this::dummyTrackInAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_INCOMINGLOT), this::inComingLotAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_BUFFERINFO), this::bufferAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_FURANCE_TRACKIN), this::furanceTrackInAdapter);
		
		itemTrackIn = (ToolItem) form.getButtonByControl(null, BUTTON_FURANCE_TRACKIN);
		itemDummyTrackIn = (ToolItem) form.getButtonByControl(null, BUTTON_DUMMYTRACKIN);
		itemInComingLot = (ToolItem) form.getButtonByControl(null, BUTTON_INCOMINGLOT);
		itemBuffer = (ToolItem) form.getButtonByControl(null, BUTTON_BUFFERINFO);

		itemTrackIn.setEnabled(false);
		itemDummyTrackIn.setEnabled(false);
		
		itemPrepare = (ToolItem) form.getButtonByControl(null, BUTTON_PREPARE);
		itemDcop = (ToolItem) form.getButtonByControl(null, BUTTON_DOCP);
		itemTrackOut = (ToolItem) form.getButtonByControl(null, BUTTON_TRACKOUT);
		itemAbort = (ToolItem) form.getButtonByControl(null, BUTTON_ABORT);
		itemRunRefresh = (ToolItem) form.getButtonByControl(null, BUTTON_RUNREFRESH);
		
		itemPrepare.setEnabled(false);
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);	
		itemAbort.setEnabled(false);	
		
		addKeyListener();
	}
	
	/**
	 * Waiting批次列表的选中事件
	 * @param obj
	 */
	public void waitingLotSelectionAdaptor(Object obj) {
		try {			
			Event event = (Event) obj;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lot != null) {
				lotStatusChanged(lot.getState(), lot.getHoldState());				
				if (!currentEqp.getIsAvailable()) {
					itemDummyTrackIn.setEnabled(false);
					itemTrackIn.setEnabled(false);		
				} else {
					if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
						itemDummyTrackIn.setEnabled(false);
						itemTrackIn.setEnabled(false);
					} else if (LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_DISP.equals(lot.getState())) {
						itemDummyTrackIn.setEnabled(true);
						itemTrackIn.setEnabled(true);
					} else if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
						itemDummyTrackIn.setEnabled(false);
						itemTrackIn.setEnabled(false);
					} else {
						itemDummyTrackIn.setEnabled(false);
						itemTrackIn.setEnabled(false);
					}
				}	
							
				if (console != null && currentEqp != null) {
					ByEqpUtil.noticeEquipmentRecipeAndReticle(lot, currentEqp, console);
				}
			} else {
				itemDummyTrackIn.setEnabled(false);
				itemTrackIn.setEnabled(false);
			}
			
			ListTableManager tableManager = 
					((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
			List<Object> objects = tableManager.getCheckedObject();
			List<Lot> lots = objects.stream().map(o -> (Lot)o).collect(Collectors.toList());
			Optional<Lot> f = lots.stream().filter(r -> Lot.HOLDSTATE_ON.equals(r.getHoldState())).findFirst();
			if (f.isPresent()) {
				itemDummyTrackIn.setEnabled(false);
				itemTrackIn.setEnabled(false);
			} else {
				itemDummyTrackIn.setEnabled(true);
				itemTrackIn.setEnabled(true);
			}	
			
			List<String> dummyLotTypes = lotManager.getDummyLotTypes(Env.getOrgRrn());
			List<Lot> dummyLots = lots.stream().filter(r -> dummyLotTypes.contains(r.getLotType())).collect(Collectors.toList());
			if (lots.size() > 0) {
				if (CollectionUtils.isEmpty(dummyLots)) {		
					itemTrackIn.setEnabled(true);
					itemDummyTrackIn.setEnabled(false);
				} else if (dummyLots.size() == lots.size()) {
					itemTrackIn.setEnabled(false);
					itemDummyTrackIn.setEnabled(true);		
				} else {
					itemDummyTrackIn.setEnabled(false);
					itemTrackIn.setEnabled(false);
				}
			} else {
				itemDummyTrackIn.setEnabled(false);
				itemTrackIn.setEnabled(false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	
	/**
	 * Running批次列表的选中事件
	 * @param obj
	 */
	protected void runningLotSelectionAdaptor(Object obj) {
		super.runningLotSelectionAdaptor(obj);
		itemTrackIn.setEnabled(false);
		itemDummyTrackIn.setEnabled(false);
	}
	
	/**
	 * 标准进站方法，不允话Dummy用此进站方法
	 * @param obj
	 */
	private void furanceTrackInAdapter(Object obj) {
		try {
			trackInAdapter(obj);
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : trackInAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	private void inComingLotAdapter(Object obj) {
		try {
			if (currentEqp == null) {
				return;
			}
			InComingLotDialog dialog = new InComingLotDialog("WIPInComingLotInfoDialog", null, eventBroker);
			Map<String, Object> propValues = Maps.newHashMap();
			propValues.put("mainEqp", currentEqp);
			dialog.setPropValues(propValues);
			dialog.open();
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	private void bufferAdapter(Object obj) {
		try {
			if (currentEqp == null) {
				return;
			}
			List<Equipment> internalBuffers = rasManager.getInternalBufferByParentEqp(
					Env.getOrgRrn(), currentEqp.getObjectRrn(), null);
			
			if (CollectionUtils.isEmpty(internalBuffers)) {
				UI.showInfo(Message.getString("wip.main_eqp_buffer_not_exist"));
				return;
			}
			
			InternalBufferDialog dialog = new InternalBufferDialog("WIPByEqpBufferDialog", null, eventBroker);
			Map<String, Object> propValues = Maps.newHashMap();
			List<LotEquipmentUnit> eqpUnits = lotManager.getLotEquipmentUnits(Env.getOrgRrn(), null, currentEqp.getEquipmentId(), true);
			
			propValues.put("rackList", eqpUnits);
			propValues.put("mainEqp", currentEqp);
			propValues.put("buffer", internalBuffers.get(0));
			dialog.setPropValues(propValues);
			dialog.open();
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 基本检查
	 */
	public boolean checkBasicConstraint(List<Lot> lots) {
		if (super.checkBasicConstraint(lots)) {
			List<String> dummyLotTypes = lotManager.getDummyLotTypes(Env.getOrgRrn());
			for (Lot lot : lots) {
				if ((CollectionUtils.isNotEmpty(dummyLotTypes) && dummyLotTypes.contains(lot.getLotType()))) {
					if (console != null) {
						console.error(Message.getString("wip.lot_byeqp_furance_trackin_no_dummy_lot"));
					} else {
						UI.showError(Message.getString("wip.lot_byeqp_furance_trackin_no_dummy_lot"));
					}
					return false;
				}
			}
		} else {
			return false;
		}
		return true;
	}
	
	public boolean checkDummyBasicConstraint(List<Lot> lots) {
		if (super.checkBasicConstraint(lots)) {
			List<String> dummyLotTypes = lotManager.getDummyLotTypes(Env.getOrgRrn());
			for (Lot lot : lots) {
				if (!(CollectionUtils.isNotEmpty(dummyLotTypes) && dummyLotTypes.contains(lot.getLotType()))) {
					if (console != null) {
						console.error(Message.getString("wip.lot_byeqp_furance_trackin_no_dummy_lot"));
					} else {
						UI.showError(Message.getString("wip.lot_byeqp_furance_trackin_no_dummy_lot"));
					}
					return false;
				}
			}
		} else {
			return false;
		}
		return true;
	}
	
	/**
	 * Dummy进站
	 * @param obj
	 */
	private void dummyTrackInAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			
			List<Lot> lots = getTrackInLotList();
			//基本检查
			if (!checkDummyBasicConstraint(lots)) {
				return;
			}
			
			for (Lot lot : lots) {
				lot.setEquipmentId(currentEqp.getEquipmentId());
			}

			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYEQP);
			List<Equipment> equipments = new ArrayList<Equipment>();
			equipments.add(currentEqp);
			context.setSelectEquipments(equipments);
			context.setLots(lots);
			context.setOperator1(operator1);

			Step step = new Step();
			step.setObjectRrn(lots.get(0).getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
				
			//直接进站
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setEquipments(context.getSelectEquipments());
			inContext.setCurrentStep(step);	
			inContext.setOperator1(context.getOperator1());
			inContext.setAttributeValues(context.getLotAttributeValues());
			inContext.setCheckEquipmentCapa(false);
			inContext.setCheckMaterial(false);
			inContext.setCheckTool(false);
			
			lotManager.trackIn(inContext, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.trackin_success"));
			refreshAdapter();
			
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : trackInAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void refresh() {
		waitRefreshAdapter(null);
		runRefreshAdapter(null);
	}
	
	@Override
	public void setWhereClause(String whereClause) {}

	@Override
	public String getWhereClause() {
		return null;
	}

	@Override
	public boolean isUseParam() {
		return false;
	}

	@Override
	public Map<String, Object> getParameterMap() {
		return null;
	}
}
