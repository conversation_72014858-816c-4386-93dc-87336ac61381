package com.glory.mes.wip.byeqp.buffer;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.byeqp.glc.CarrierOutPortDialog;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotEquipmentUnit;
import com.glory.framework.core.exception.ExceptionBundle;

public class InternalBufferDialog extends GlcBaseDialog {
	
	private static final Logger logger = Logger.getLogger(InternalBufferDialog.class);
	
	private static final String FIELD_RACKLIST = "rackList";
	private static final String FIELD_COMPONENTLIST = "componentList";
	
	public static final String PARAMETER_NPW_USE_COUNT = "$NPW_USECOUNT";
	
	private ListTableManagerField rackListField;
	private ListTableManagerField componentListField;
	
	private static final String BUTTON_BUFFEROUTREQUEST = "dummyOutRequest";
	
	public Equipment mianEquipment;
	public Equipment buffer;
	public Port port;
	
	public InternalBufferDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
	}
	
	protected void createFormAction(GlcForm form) {
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_BUFFEROUTREQUEST), this::dummyOutRequestAdapter);
		
		rackListField = form.getFieldByControlId(FIELD_RACKLIST, ListTableManagerField.class);
		rackListField.setValue(propValues.get("rackList"));
		rackListField.refresh();
		rackListField.getListTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection)event.getSelection();
				if (selection != null) {
					LotEquipmentUnit selectLotEquipmentUnit = (LotEquipmentUnit) selection.getFirstElement();
					if (selectLotEquipmentUnit != null) {
						getComponentByCarrierId(selectLotEquipmentUnit.getDurableId());
					}
				}			
			}			
		});
		componentListField = form.getFieldByControlId(FIELD_COMPONENTLIST, ListTableManagerField.class);
		
		this.mianEquipment = (Equipment) propValues.get("mainEqp");
		this.buffer = (Equipment) propValues.get("buffer");
	}
	
	protected void getComponentByCarrierId(String durableId) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), durableId);
			Long maxUseCount = null;
			if (CollectionUtils.isNotEmpty(lots)) {
				Map<String, Object> paramMap = prdManager.getCurrentParameter(lots.get(0).getProcessInstanceRrn());
				if (paramMap.get(PARAMETER_NPW_USE_COUNT) != null) {
					maxUseCount = DBUtil.toLong(paramMap.get(PARAMETER_NPW_USE_COUNT).toString());
				}
			}		
			
			List<ComponentUnit> units = carrierLotManager.getComponentByCarrierId(Env.getOrgRrn(), durableId);	
			if (CollectionUtils.isNotEmpty(units)) {
				for (ComponentUnit unit : units) {
					if (unit.getParentUnitRrn() != null && CollectionUtils.isNotEmpty(lots)) {
						for (Lot lot : lots) {
							if (unit.getParentUnitRrn().equals(lot.getObjectRrn())) {
								unit.setLotId(lot.getLotId());
								break;
							}
						}
					}
					unit.setAttribute1(maxUseCount);
				}
				componentListField.setValue(units);
				componentListField.refresh();
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	private LotEquipmentUnit getTarget() {
		return (LotEquipmentUnit) rackListField.getListTableManager().getSelectedObject();
	}
	
	private void dummyOutRequestAdapter(Object obj) {
		try {
			LotEquipmentUnit target = getTarget();
			if (target == null) {
				UI.showInfo(Message.getString("wip.lot_byeqp_buffer_select_rack"));
				return;
			}
			
			if (StringUtil.isEmpty(target.getDurableId())) {
    			UI.showError(Message.getString("wip.lot_byeqp_buffer_rack_no_durable"));
    			return;
    		}
			
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), target.getDurableId());
			if (lots == null || lots.size() == 0) {
				UI.showError(Message.getString("wip.lot_is_not_in_durable"));
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
//			List<String> dummyLotTypes = lotManager.getDummyLotTypes(Env.getOrgRrn());
//			if (CollectionUtils.isNotEmpty(dummyLotTypes) && dummyLotTypes.contains(lots.get(0).getLotType())) {
//				// 弹框选择Port ID
//				CarrierOutPortDialog carrierOutPortDialog = new CarrierOutPortDialog("ChjsCarrierOutPort", null, this.eventBroker);
//				carrierOutPortDialog.setParentEqp(this.mianEquipment);
//				carrierOutPortDialog.setOkAdaptor(this::okAdaptor);
//				if (Dialog.OK == carrierOutPortDialog.open()) {
//					lotManager.carrierOutRequest(target, port, Env.getSessionContext());
//				}
//			} else {
//				UI.showInfo(Message.getString("wip.lot_byeqp_buffer_rack_dummy_only"));
//				return;
//			}
			// 弹框选择Port ID
			CarrierOutPortDialog carrierOutPortDialog = new CarrierOutPortDialog("ChjsCarrierOutPort", null, this.eventBroker);
			carrierOutPortDialog.setParentEqp(this.mianEquipment);
			carrierOutPortDialog.setOkAdaptor(this::okAdaptor);
			if (Dialog.OK == carrierOutPortDialog.open()) {
//				lotManager.carrierOutRequest(target, port, Env.getSessionContext());
			}	
		} catch (Exception e) {
			logger.error("Error at InternalBufferDialog : dummyOutRequestAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}

	protected void createButtonsForButtonBar(Composite parent) {	
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
	}
	
	
	private void okAdaptor(Object obj) {
		CarrierOutPortDialog dialog = (CarrierOutPortDialog) obj;
		if (dialog.getPort() == null) {
			dialog.setCloseFlag(false);
			UI.showInfo(Message.getString("chjs.please_select_portid"));
		} else {
			setPort(dialog.getPort());
			dialog.setCloseFlag(true);
		}
	}
	
	public void setPort(Port port) {
		this.port = port;
	}
	
}
