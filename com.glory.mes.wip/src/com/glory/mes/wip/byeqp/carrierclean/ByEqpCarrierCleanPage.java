package com.glory.mes.wip.byeqp.carrierclean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.inject.Inject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.application.command.OpenEditorCommand;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.GlcPage;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.CarrierPrepare;
import com.glory.mes.mm.durable.model.DurableSpec;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.EquipmentMatType;
import com.glory.mes.ras.model.state.RasState;
import com.glory.mes.wip.byeqp.extensionpoint.IByEqpPage;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.custom.depend.MsgConsoleView;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class ByEqpCarrierCleanPage extends GlcPage implements IByEqpPage {

	private static final Logger logger = Logger.getLogger(ByEqpCarrierCleanPage.class);
	
	@Inject
	EPartService partService;
	
	@Inject
	EModelService modelService;
	
	private static final String FIELD_DURABLEID = "durableId";
	private static final String FIELD_WAITTINGCARRIERS = "waittingCarriers";
	private static final String FIELD_RUNNINGCARRIERS = "runningCarriers";

	private static final String BUTTON_PREPARE = "prepare";
	private static final String BUTTON_START = "start";
	private static final String BUTTON_INREFRESH = "inrefresh";
	private static final String BUTTON_END = "end";
	private static final String BUTTON_RUNREFRESH = "runrefresh";

	private boolean eqpAvailable = false;
	/**
	 * 显示控制台
	 */
	public ByEqpConsole console;
	
	/**
	 * 当前设备
	 */
	public Equipment currentEqp;
	
	/**
	 * Editor中的GlcForm
	 */
	public GlcForm rootForm;
	
	private ToolItem itemPrepare;
	private ToolItem itemStart;
	
	private ToolItem itemEnd;
	
	protected TextField fieldCarrier;
	protected ListTableManagerField fieldWaitting;
	protected ListTableManagerField fieldRunning;
	
	public ADManager adManager;
	public RASManager rasManager;
	public DurableManager durableManager;
	
	protected List<Lot> holdLots;
	
	protected void createFormAction(GlcForm form) {	
		try {
			adManager = Framework.getService(ADManager.class);
			rasManager = Framework.getService(RASManager.class);	
			durableManager = Framework.getService(DurableManager.class);		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		//获取控制台
		console = (ByEqpConsole) MsgConsoleView.getInstance();
		
		//获取左边的树形设备点击事件
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::eqpSelectionChanged);	
		
		fieldCarrier = form.getFieldByControlId(FIELD_DURABLEID, TextField.class);
		fieldWaitting = form.getFieldByControlId(FIELD_WAITTINGCARRIERS, ListTableManagerField.class);
		fieldRunning = form.getFieldByControlId(FIELD_RUNNINGCARRIERS, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, fieldWaitting.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::waittingCarriersSelectionChanged);
		subscribeAndExecute(eventBroker, fieldRunning.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::runningCarriersSelectionChanged);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_DURABLEID, GlcEvent.EVENT_ENTERPRESSED), this::carrierEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_RUNNINGCARRIERS, GlcEvent.EVENT_DOUBLE_CLICK), this::doubleClickAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_WAITTINGCARRIERS, GlcEvent.EVENT_DOUBLE_CLICK), this::doubleClickAdaptor);
		
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PREPARE), this::prepareAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_START), this::cleanAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_INREFRESH), this::inrefreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_END), this::endAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RUNREFRESH), this::runrefreshAdapter);
		
		Text fieldCarriertText = fieldCarrier.getTextControl();
		GridData gText = new GridData();
		gText.widthHint = 216;
		fieldCarriertText.setLayoutData(gText);

		init();
	}
	
	/**
	 * 左边设备选择响应事件
	 * @param object
	 */
	protected void eqpSelectionChanged(Object object) {
		try {
			Event event = (Event) object;		
			//获取选中的设备
			Equipment equipment = (Equipment) event.getProperty(GlcEvent.PROPERTY_DATA);	
			if (equipment != null) {
				RasState state = rasManager.getState(Env.getOrgRrn(), equipment.getState());
				equipment.setIsAvailable(state.getIsAvailable());
				currentEqp = equipment;			
				
				fieldCarrier.setText(null);
				equipmentChanged();
				
				//控制台显示不可用的设备
				if (!state.getIsAvailable()) {
					if (console != null && !console.isDisposed()) {
						String message = "";
						message += "[" + currentEqp.getEquipmentId() + "];";
						message += Message.getString("byeqp.runninglot_eqpisnotavailable") + currentEqp.getState();
						console.error(message);
					}
				}	
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void equipmentChanged() {
		try {
			// 设备切换时，根据设备类型切换到对应的动态页面
			if (currentEqp == null) {
				itemPrepare.setEnabled(false);
				return;
			}
			
			itemPrepare.setEnabled(true);
		
			RasState state = rasManager.getState(Env.getOrgRrn(), currentEqp.getState());
			this.eqpAvailable = state.getIsAvailable();
			if (!state.getIsAvailable()) {
				if (console != null) {
					String message = "";
					message += "[" + currentEqp.getEquipmentId() + "];";
					message += Message.getString("byeqp.runninglot_eqpisnotavailable") + currentEqp.getState();
					console.error(message);
				}
			}
			
			refresh(1);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 作业准备
	 * @param object
	 */
	protected void prepareAdapter(Object object) {
		if (currentEqp != null && eqpAvailable) {
			try {
				// 作业准备Dialog
				ByEqpCarrierPrepareDialog dialog = new ByEqpCarrierPrepareDialog("ByEQPCarrierPrepare", null, eventBroker, this);
				Map<String, Object> propValues = Maps.newHashMap();
				// 作业准备记录
				List<CarrierPrepare> carrierPrepares = durableManager.getCarrierPrepareJobs(
						Env.getOrgRrn(), currentEqp.getEquipmentId(), null, null, true);
				propValues.put("prepareList", carrierPrepares);
				// 设备信息
				propValues.put("leftFrom-equipmentInfo", currentEqp);
				
				// 可设置作业准备的批次
				List<Carrier> waittingCarriers = getCarrierSpecMainMatType(currentEqp);
				
				waittingCarriers = getCarrierState(waittingCarriers, 2);
				propValues.put("leftFrom-carrierList", waittingCarriers);
				dialog.setPropValues(propValues);
				dialog.setCloseAdaptor(new Consumer<ByEqpCarrierPrepareDialog>() {
					
					@Override
					public void accept(ByEqpCarrierPrepareDialog t) {
						refresh(2);
					}
				});
				dialog.open();
			} catch (Exception e) {
				logger.error("Error at ByEqpEditor : prepareAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	/**
	 * 清洗
	 * @param object
	 */
	protected void cleanAdapter(Object object) {
		if (currentEqp != null && eqpAvailable) {
			try {
				Carrier carrier = (Carrier)fieldWaitting.getListTableManager().getSelectedObject();
				if(carrier == null || StringUtil.isEmpty(carrier.getDurableId())) {
					UI.showError(Message.getString("common.select_durable"));
					return;
				}
				
				durableManager.carrierJobPrepareAction(currentEqp.getEquipmentId(), Lists.newArrayList(carrier), Carrier.EVENT_CLN, Env.getSessionContext());
				UI.showInfo(Message.getString("ras.durable_clean_start_succeed"));
				refresh(1);
				if (console != null) {
					String message = Message.getString("wip.durable");
					message += "[" + carrier.getDurableId() + "]" + Message.getString("ras.durable_clean_start_succeed" + ";");
					console.info(message);
				}
			} catch (Exception e) {
				logger.error("Error at ByEqpEditor : prepareAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	
	}

	private void inrefreshAdapter(Object object) {
		refresh(2);
	}
	
	/**
	 * 结束清洗
	 * @param object
	 */
	private void endAdapter(Object object) {
		if (currentEqp != null && eqpAvailable) {
			try {
				Carrier carrier = (Carrier)fieldRunning.getListTableManager().getSelectedObject();
				if(carrier == null || StringUtil.isEmpty(carrier.getDurableId())) {
					UI.showError(Message.getString("common.select_durable"));
					return;
				}
				
				durableManager.carrierJobPrepareAction(currentEqp.getEquipmentId(), Lists.newArrayList(carrier), Carrier.EVENT_AVL, Env.getSessionContext());
				UI.showInfo(Message.getString("ras.durable_clean_end_succeed"));
				if (console != null) {
					String message = Message.getString("wip.durable");
					message += "[" + carrier.getDurableId() + "]" + Message.getString("ras.durable_clean_end_succeed" + ";");
					console.info(message);
				}
				refresh(1);
			} catch (Exception e) {
				logger.error("Error at ByEqpEditor : prepareAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	
	}
	
	/**
	 * 载具控件回车事件
	 * @param obj
	 */
	private void carrierEnterPressed(Object obj) {
		try {
			if (currentEqp == null) {
				if (console != null) {
					console.error(Message.getString("ras.equipment_select_eqp"));
				}
				return;
			}
			
			Event event = (Event) obj;
			String carrierId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			
		    refresh(2);
		    //如果输入了载具回车则在能查询到的设备绑定的主物料载具中查询数据，如果未输入则全部查询
		    if(!StringUtil.isEmpty(carrierId)) {
		    	Boolean equals = false;
		    	List<Carrier> waitingCarriers = (List<Carrier>)fieldWaitting.getListTableManager().getInput();
				if(CollectionUtils.isNotEmpty(waitingCarriers)) {
					Carrier carrier = new Carrier();
					for(Carrier idCarrier : waitingCarriers) {
						if(idCarrier.getDurableId().equals(carrierId)) {
							carrier = idCarrier;
							equals = true;
						}
					}
					if(equals) {
						fieldWaitting.getListTableManager().setInput(Lists.newArrayList(carrier));
					}else {
						fieldWaitting.getListTableManager().setInput(Lists.newArrayList());
					}
				}
				if (console != null) {
					String message = "Select ";
					message += Message.getString("wip.durable") +  "[" + carrierId + "];";
					console.info(message);
				}
		    }
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : carrierEnterPressed() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void doubleClickAdaptor(Object obj) {
		try {
			Event event = (Event) obj;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			
			List<ADAuthority> authority = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + "Wip.LotDetail" + "'", "");
			if (authority.size() != 1) {
				return;
			}
			authority.get(0).setAttribute1(lot.getLotId());
			OpenEditorCommand.open(authority.get(0), partService, modelService);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void runrefreshAdapter(Object object) {
		refresh(3);
	}

	private void waittingCarriersSelectionChanged(Object object) {
		try {
			Event event = (Event) object;
			Carrier carrier = (Carrier) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (carrier != null) {
				if(!StringUtil.isEmpty(carrier.getState()) && carrier.getState().equals(Carrier.EVENT_DISP)) {
					itemStart.setEnabled(true);
				}else if (!StringUtil.isEmpty(carrier.getState()) && carrier.getState().equals(Carrier.EVENT_AVL)) {
					itemStart.setEnabled(false);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	
	}

	private void runningCarriersSelectionChanged(Object object) {
		try {
			Event event = (Event) object;
			Carrier carrier = (Carrier) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (carrier != null) {
				if(!StringUtil.isEmpty(carrier.getState()) && carrier.getState().equals(Carrier.EVENT_CLN)) {
					itemEnd.setEnabled(true);
					itemStart.setEnabled(false);
					itemPrepare.setEnabled(false);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	
	}
	
	private void init() {
		itemStart = (ToolItem) form.getButtonByControl(null, BUTTON_START);
		itemEnd = (ToolItem) form.getButtonByControl(null, BUTTON_END);
		itemPrepare = (ToolItem) form.getButtonByControl(null, BUTTON_PREPARE);

		itemStart.setEnabled(false);
		itemEnd.setEnabled(false);
		itemPrepare.setEnabled(false);

		console = (ByEqpConsole) MsgConsoleView.getInstance();
	}
	
	private void clear() {
		fieldWaitting.setValue(Lists.newArrayList());
		fieldWaitting.refresh();
		setHoldLots(Lists.newArrayList());
		fieldRunning.setValue(Lists.newArrayList());
		fieldRunning.refresh();
		
		itemStart.setEnabled(false);
		itemEnd.setEnabled(false);
		itemPrepare.setEnabled(false);
		
		fieldCarrier.setText(null);
	}
	
	/**
	 * 根据设备获得绑定的相同主物料类型的载具
	 * @param state
	 * @param holdState
	 */
	public List<Carrier> carrierSetPrepareId(List<Carrier> dispCarriers) {
		try {
			//获得设备绑定的主物料类型
			if(CollectionUtils.isNotEmpty(dispCarriers)) {
				String where = dispCarriers.stream().map(Carrier :: getDurableId).collect(Collectors.joining("','"));
				List<CarrierPrepare> carrierPrepares = adManager.getEntityList(Env.getOrgRrn(), CarrierPrepare.class , Env.getMaxResult(), 
						" durableId in ('" + where + "')", null);
				if(CollectionUtils.isNotEmpty(carrierPrepares)) {
					Map<String, CarrierPrepare> carrierPrepareMap = new HashMap<>(); 
					for(CarrierPrepare carrierPrepare : carrierPrepares) {
						if(!carrierPrepareMap.containsKey(carrierPrepare.getDurableId())) {
							carrierPrepareMap.put(carrierPrepare.getDurableId(), carrierPrepare);
						}
					}
					for(Carrier carrier : dispCarriers) {
						if(carrierPrepareMap.containsKey(carrier.getDurableId())) {
							//carrier.setJobId((carrierPrepareMap.get(carrier.getDurableId()).getJobId()));
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return dispCarriers;
	}
	
	
	/**
	 * 根据设备获得绑定的相同主物料类型的载具
	 * @param state
	 * @param holdState
	 */
	public List<Carrier> getCarrierSpecMainMatType(Equipment currentEqp) {
		List<Carrier> carriers = new ArrayList<Carrier>();
		try {
			//获得设备绑定的主物料类型
			List<EquipmentMatType> matTypes = adManager.getEntityList(Env.getOrgRrn(), EquipmentMatType.class, 
					Env.getMaxResult(), "equipmentId = '" + currentEqp.getEquipmentId() + "'", "");
			//循环组成条件
			String where = matTypes.stream().map(EquipmentMatType :: getMainMatType).collect(Collectors.joining("','"));
			
			//如果不为空则说明有绑定
			if(!StringUtil.isEmpty(where)) {
				List<DurableSpec> durableSpecCarriers = adManager.getEntityList(Env.getOrgRrn(), DurableSpec.class , Env.getMaxResult(), 
						" mainMatType in ('" + where + "')", null);
				if(CollectionUtils.isNotEmpty(durableSpecCarriers)) {
					//循环组成条件
					String whereCarrier = durableSpecCarriers.stream().map(DurableSpec :: getName).collect(Collectors.joining("','"));
					
					carriers =  adManager.getEntityList(Env.getOrgRrn(), Carrier.class , Env.getMaxResult(), 
							" durableSpecName in ('" + whereCarrier + "')", null);
					return carriers;
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return carriers;
	}
	
	/**
	 * 根据设备获得绑定的相同主物料类型的载具
	 * index 1，获得DISP类型已清洗作业准备载具
	 * index 2，获得未进行清洗作业AVL载具
	 * index 3，获得未清洗已准备的DISP载具
	 */
	public List<Carrier> getCarrierState(List<Carrier> carriers, int i) {
		List<Carrier> CarrierList = new ArrayList<Carrier>();
		try {
			if (CollectionUtils.isNotEmpty(carriers)) {
				for(Carrier carrier : carriers) {
					if(i == 1) {
						if(carrier.getState() != null && carrier.getState().equals(Carrier.EVENT_CLN)) {
							CarrierList.add(carrier);
						}	
					}else if (i == 2) {
						if(carrier.getState() != null && !carrier.getState().equals(Carrier.EVENT_CLN) && !carrier.getState().equals(Carrier.EVENT_DISP)) {
							CarrierList.add(carrier);
						}	
					}else if (i == 3) {
						if(carrier.getState() != null && carrier.getState().equals(Carrier.EVENT_DISP)) {
							CarrierList.add(carrier);
						}
					}
				}
				return CarrierList;
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return CarrierList;
	}
	
	/**
	 * 刷新批次列表
	 * index 1，都刷新
	 * index 2，只刷新waiting
	 * index 3，只刷新running
	 * @param index
	 */
	public void refresh(int index) {
		try {
			if (this.currentEqp != null && this.currentEqp.getObjectRrn() != null) {
				List<Carrier> waitingCarriers = getCarrierSpecMainMatType(currentEqp);
				
				if (1 == index || 2 == index) {
					List<Carrier> fieldWaittings = new ArrayList<Carrier>();
					if(CollectionUtils.isNotEmpty(waitingCarriers)) {
						List<Carrier> dispCarriers = getCarrierState(waitingCarriers, 3);
						List<CarrierPrepare> carrierPrepare = durableManager.getCarrierPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), null, null, false);
						if(CollectionUtils.isNotEmpty(carrierPrepare) && CollectionUtils.isNotEmpty(dispCarriers)) {
							List<String> durableId = carrierPrepare.stream().map(CarrierPrepare::getDurableId).distinct().collect(Collectors.toList());
							List<Carrier> equipmentDisps = new ArrayList<Carrier>();
							for(Carrier carrier : dispCarriers) {
								if(durableId.contains(carrier.getDurableId())) {
									equipmentDisps.add(carrier);
								}
							}
							fieldWaittings.addAll(equipmentDisps);
						}
						
						fieldWaittings.addAll(getCarrierState(waitingCarriers, 2));
						fieldWaitting.setValue(fieldWaittings);
					}else {
						fieldWaitting.setValue(Lists.newArrayList());
					}
					fieldWaitting.refresh();
				} 
				if (1 == index || 3 == index) {
					if(CollectionUtils.isNotEmpty(waitingCarriers)) {
						List<Carrier> fieldRunnings = new ArrayList<Carrier>();
						List<Carrier> clnCarriers = getCarrierState(waitingCarriers, 1);
						if(CollectionUtils.isNotEmpty(clnCarriers)) {
							List<CarrierPrepare> carrierPrepare = durableManager.getCarrierPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), null, null, false);
							if(CollectionUtils.isNotEmpty(carrierPrepare) && CollectionUtils.isNotEmpty(clnCarriers)) {
								List<String> durableId = carrierPrepare.stream().map(CarrierPrepare::getDurableId).distinct().collect(Collectors.toList());
								List<Carrier> equipmentClns = new ArrayList<Carrier>();
								for(Carrier carrier : clnCarriers) {
									if(durableId.contains(carrier.getDurableId())) {
										equipmentClns.add(carrier);
									}
								}
								fieldRunnings.addAll(equipmentClns);
								fieldRunning.setValue(fieldRunnings);
							}
						}else {
							fieldRunning.setValue(Lists.newArrayList());
						}
						
					}
					fieldRunning.refresh();
				}
			} else {
				clear();
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : refresh() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected List<Lot> filterHoldLots(List<Lot> lots) {
		List<Lot> holdLots = new ArrayList<Lot>();
		if (this.currentEqp != null && this.currentEqp.getObjectRrn() != null) {
			for (Lot lot : lots) {
				if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
				    holdLots.add(lot);
				}
			}
		}
		return holdLots;
	}
	
	
	public Equipment getCurrentEqp() {
		return currentEqp;
	}

	public void setCurrentEqp(Equipment currentEqp) {
		this.currentEqp = currentEqp;
	}

	public List<Lot> getHoldLots() {
		return holdLots;
	}

	public void setHoldLots(List<Lot> holdLots) {
		this.holdLots = holdLots;
	}

	
	public IEventBroker getEventBroker() {
		return eventBroker;
	}

	public boolean isEqpAvailable() {
		return eqpAvailable;
	}

	public void setEqpAvailable(boolean eqpAvailable) {
		this.eqpAvailable = eqpAvailable;
	}

	public ListTableManagerField getFieldWaitting() {
		return fieldWaitting;
	}

	public void setFieldWaitting(ListTableManagerField fieldWaitting) {
		this.fieldWaitting = fieldWaitting;
	}
	
	public GlcForm getRootForm() {
		return rootForm;
	}

	@Override
	public void setRootForm(GlcForm rootForm) {
		this.rootForm = rootForm;
	}

}
