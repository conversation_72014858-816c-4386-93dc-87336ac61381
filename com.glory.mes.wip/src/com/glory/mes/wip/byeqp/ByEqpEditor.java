package com.glory.mes.wip.byeqp;

import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.GlcUtil;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.port.Port;
import com.google.common.collect.Maps;

public class ByEqpEditor extends GlcEditor {

	private static final Logger logger = Logger.getLogger(ByEqpEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.byeqp.ByEqpEditor";
	
	public static final String DIALOG_EQPINFO_ADFORM_NAME = "RASEquipmentInfo";
	
	public static final String FIELD_EQPTREE = "eqpTree";
	
	public static final String BUTTON_EQPINFO = "eqpinfo";
	public static final String BUTTON_REFRESH = "refresh";
	
	protected CustomField eqpTreeField;
	
	protected ToolItem itemEqpInfo;
	protected ToolItem itemRefresh;
	
	public Equipment currentEqp;
	
	public ADManager adManager;
	public RASManager rasManager;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		partService.addPartListener(this);
		
		configureBody(parent);
		parent.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		
		ADAuthority authority = (ADAuthority)mPart.getTransientData().get(CommandParameter.PARAM_ADAUTHORIY);
		String adFormName = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR)).getPARAM2();
		if (!StringUtil.isEmpty(adFormName)) {
			try {
				ADForm adForm = GlcUtil.getADForm(getClass(), adFormName, getADManger());	
				ADTable adTable = adForm.getAdTable();
				adTable.setAuthorityKey(authority.getName());
				form = new ByEqpForm(adForm, adTable);
				form.setAuthority(authority.getName());
				form.setEventBroker(eventBroker);
				//AuthorityKey后面加上UUID,是为了避免当页面关闭后,重新打开时Event可能被重新订阅问题(页面销毁时未及时取消)
				form.setTopicPrefix(authority.getName().replaceAll("\\.", GlcEvent.NAMESPACE_SEPERATOR) + UUIDUtil.base58Uuid());
				form.setTopicId(form.getTopicPrefix());
				form.setFormAttributes(adForm.getFormAttributes());
				form.setPostAddFields(this::postAddFields);
				form.createForm(parent, toolkit);
				createFormAction(form);
				
				focusControl = form.getFocusControl();
				registerAccelerator();
			} catch (Exception e) {
				logger.error(e);
			}
		}
		
		try {
			adManager = Framework.getService(ADManager.class);
			rasManager = Framework.getService(RASManager.class);			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createFormAction(GlcForm form) {
		eqpTreeField = form.getFieldByControlId(FIELD_EQPTREE, CustomField.class);		
		subscribeAndPost(eventBroker, eqpTreeField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), form.getTopicId());
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EQPINFO), this::eqpInfoAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
	
		itemEqpInfo = (ToolItem) form.getButtonByControl(null, BUTTON_EQPINFO);
		itemRefresh = (ToolItem) form.getButtonByControl(null, BUTTON_REFRESH);
		itemEqpInfo.setEnabled(false);	
	}
	
	public EventHandler subscribeAndPost(IEventBroker eventBroker, String fullEventId, String postFullEventId) {
		EventHandler handler = subscribeForwardPost(eventBroker, fullEventId, postFullEventId);
		subscribleHandlers.add(handler);
		return handler;
	}
	
	/**
	 * 执行消息转发,注意要在preDestroy中销返回的EventHandler
	 * @param eventBroker
	 * @param fullEventId 订阅事件号
	 * @param postFullEventId 转发
	 * 
	 */
	public EventHandler subscribeForwardPost(IEventBroker eventBroker, String fullEventId, String postFullEventId) {
		EventHandler subscribeHandler = new EventHandler() {
			@Override
			public void handleEvent(Event event) {
				//点击设备切换右边Page
				String postFull = postFullEventId + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_GLC_MDFIRESECTION;;
				Map<String, Object> map = Maps.newHashMap();
				for (String name : event.getPropertyNames()) {
					map.put(name, event.getProperty(name));
					if (GlcEvent.PROPERTY_DATA.equals(name)) {
						Equipment equipment = (Equipment) map.get(name);
						if (equipment != null) {
							currentEqp = equipment;
							itemEqpInfo.setEnabled(true);
							
							//不同的设备ProcessMode对应相应的Page，设备没有维护ProcessMode，打开默认的Page
							if (StringUtil.isEmpty(equipment.getProcessCategory())) {
								map.put(GlcEvent.PROPERTY_PAGE_KEY, ByEqpPage.DEFAULT_PROCESS_MODE);
							} else {
								map.put(GlcEvent.PROPERTY_PAGE_KEY, equipment.getProcessCategory());
							}
						} else {
							currentEqp = null;
							itemEqpInfo.setEnabled(false);
						}
					}
				}
				eventBroker.post(postFull, map);		
			}
		};
		eventBroker.subscribe(fullEventId, subscribeHandler);
		
		return subscribeHandler;
	}
	
	/**
	 * 设备信息
	 * @param object
	 */
	protected void eqpInfoAdapter(Object object) {
		if (currentEqp != null) {
			try {
				// 设备详细新Dialog
				GlcBaseDialog dialog = new GlcBaseDialog(DIALOG_EQPINFO_ADFORM_NAME, null, eventBroker);
				Map<String, Object> propValues = Maps.newHashMap();
				currentEqp = (Equipment)adManager.getEntity(currentEqp);
				propValues.put("equipmentInfo", currentEqp);
				
				// 子设备信息
				List<Equipment> subEqps = rasManager.getSubEquipments(Env.getOrgRrn(), currentEqp.getEquipmentId());
				propValues.put("otherInfo-subEquipments", subEqps);
				
				// 端口信息
				List<Port> ports = rasManager.getPortsByEquipment(Env.getOrgRrn(), currentEqp.getEquipmentId(), false);
				propValues.put("otherInfo-ports", ports);
				dialog.setPropValues(propValues);
				dialog.open();
			} catch (Exception e) {
				logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	/**
	 * EqpTree刷新
	 * @param object
	 */
	protected void refreshAdapter(Object object) {
		eqpTreeField.refresh();
		currentEqp = null;
		itemEqpInfo.setEnabled(false);

		//点击刷新打开默认的Page
		String postFull = form.getTopicId() + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_GLC_MDFIRESECTION;;
		Map<String, Object> map = Maps.newHashMap();
		map.put(GlcEvent.PROPERTY_PAGE_KEY, ByEqpPage.DEFAULT_PROCESS_MODE);	
		eventBroker.post(postFull, map);				
	}
	
}
