package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.common.context.client.ContextManager;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextValue;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotTecn;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotTecnCustomComposite extends CustomCompsite {

	public static final Logger logger = Logger.getLogger(LotTecnCustomComposite.class);

	public static final String TABLE_NAME_LOTTECN = "WIPLotTecn";
	public static final String TABLE_NAME_LOTTECN_EDIT = "WIPLotTecnEdit";
	
	public static final String ATTRIBUTE_TABLE_NAME_LOTTECN = "LotTecnTableName";
	public static final String ATTRIBUTE_TABLE_NAME_LOTTECN_EDIT = "LotTecnEditTableName";
	public static final String ATTRIBUTE_IS_PERSITE = "IsPersist";
	
	protected String lotTecnTableName;
	protected String lotTecnEditTableName;
	protected boolean isPersist = false;
	
	public ListTableManager tableManager;
	
	public List<Lot> lots;
	
	public String ecnSource;
	
	public String ecnSourceName;
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite content = toolkit.createComposite(parent, SWT.NONE);
		content = CustomCompsite.configureBody(content);
		
		try {			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOTTECN);
			if (!StringUtil.isEmpty(lotTecnTableName)) {
				table = adManager.getADTable(Env.getOrgRrn(), lotTecnTableName);
			} 
			tableManager = new ListTableManager(table, true);
			tableManager.setAutoSizeFlag(false);
			tableManager.newViewer(content);

			Composite btnComposite = toolkit.createComposite(content, SWT.NONE);
			GridLayout btnLayout = new GridLayout(3, false);
			btnComposite.setLayout(btnLayout);
			GridData btnGridData = new GridData(GridData.END);
			btnGridData.horizontalAlignment = SWT.RIGHT;
			btnComposite.setLayoutData(btnGridData);
			SquareButton addTecn = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonAdd()), null);
			addTecn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						addTecnAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			SquareButton editTecn = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonEdit()), null);
			editTecn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						editTecnAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			SquareButton deleteTecn = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonDelete()), null);
			deleteTecn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						delTecnAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
		} catch (Exception e) {
			logger.error("LotTecnCustomCompsite createForm error:", e);
		}
		return content;
	}
	
	/**
	 * 增加Tecn
	 */
	protected void addTecnAdapter() {
		try {
			List<LotTecn> alllotTecns = Lists.newArrayList();
			List<LotTecn> existlotTecns = (List<LotTecn>)(List)tableManager.getInput();
			alllotTecns.addAll(existlotTecns);
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable editTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOTTECN_EDIT);
			if (!StringUtil.isEmpty(lotTecnEditTableName)) {
				editTable = adManager.getADTable(Env.getOrgRrn(), lotTecnEditTableName);
			}
			LotTecnAddDialog dialog = new LotTecnAddDialog(editTable, existlotTecns, lots);
			if (dialog.open() == Dialog.OK) {
				alllotTecns.addAll(dialog.getCheckedObject());
				tableManager.setInput(alllotTecns);
				tableManager.refresh();
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void editTecnAdapter() {
		try {
			LotTecn lotTecn = (LotTecn) tableManager.getSelectedObject();
			if (lotTecn == null) {
				UI.showError(Message.getString("common.select_record"));
				return;
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOTTECN_EDIT);
			
			if (lotTecn.getObjectRrn() != null) {		
				LotTecn newLotTecn = (LotTecn) adManager.getEntity(lotTecn);
				newLotTecn.setEquipmentId(lotTecn.getEquipmentId());
				newLotTecn.setRecipeName(lotTecn.getRecipeName());
				newLotTecn.setReticleName(lotTecn.getReticleName());
				newLotTecn.setEdcName(lotTecn.getEdcName());
				newLotTecn.setEquipmentRecipeName(lotTecn.getEquipmentRecipeName());
				
				LotTecnEditDialog dialog = new LotTecnEditDialog(adTable, newLotTecn);
				if (dialog.open() == Dialog.OK) {	
					newLotTecn = (LotTecn) dialog.getAdObject();
					lotTecn.setEquipmentId(newLotTecn.getEquipmentId());
					lotTecn.setRecipeName(newLotTecn.getRecipeName());
					lotTecn.setReticleName(newLotTecn.getReticleName());
					lotTecn.setEdcName(newLotTecn.getEdcName());
					lotTecn.setEquipmentRecipeName(newLotTecn.getEquipmentRecipeName());
					tableManager.refresh();
				}
			} else {		
				LotTecnEditDialog dialog = new LotTecnEditDialog(adTable, lotTecn);
				if (dialog.open() == Dialog.OK) {
					LotTecn newLotTecn = (LotTecn) dialog.getAdObject();
					lotTecn.setEquipmentId(newLotTecn.getEquipmentId());
					lotTecn.setRecipeName(newLotTecn.getRecipeName());
					lotTecn.setReticleName(newLotTecn.getReticleName());
					lotTecn.setEdcName(newLotTecn.getEdcName());
					lotTecn.setEquipmentRecipeName(newLotTecn.getEquipmentRecipeName());
					tableManager.refresh();
				} 
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 删除Tecn
	 */
	protected void delTecnAdapter() {	
		try {
			List<LotTecn> checkedLotTecns = (List<LotTecn>)(List)tableManager.getCheckedObject();
			if (checkedLotTecns != null) {
				List<LotTecn> allLotTecns = Lists.newArrayList();
				List<LotTecn> existLotTecns = (List<LotTecn>)(List) tableManager.getInput();
				allLotTecns.addAll(existLotTecns);
				
				if (isPersist) {
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.deleteLotTecn(checkedLotTecns, Env.getSessionContext());	
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				} 
				allLotTecns.removeAll(checkedLotTecns);
				tableManager.setInput(allLotTecns);
				tableManager.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void refresh() {
		tableManager.refresh();
	}

	@Override
	public void setValue(Object value) {
		tableManager.setInput((List<Object>)value);
	}

	@Override
	public Object getValue() {
		return tableManager.getInput();
	}

	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_TABLE_NAME_LOTTECN:
				lotTecnTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TABLE_NAME_LOTTECN_EDIT:
				lotTecnEditTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_IS_PERSITE:
				isPersist = formAttribute.getBooleanValue();
				break;
			}
		}
	}

	public List<Lot> getLots() {
		return lots;
	}

	public void setLots(List<Lot> lots) {
		this.lots = lots;
	}

	
	/**
	 * 增加LotTecn弹出框
	 * <AUTHOR>
	 *
	 */
	public class LotTecnAddDialog extends BaseTitleDialog {

		protected ListTableManager tableManager;
		
		private ADTable table;
		private List<LotTecn> existLotTecns;
		private List<Lot> lots;
		private List<LotTecn> checkedLotTecns;
		
		public LotTecnAddDialog(ADTable table, List<LotTecn> existLotTecns, List<Lot> lots) {
			super();
			this.table = table;	
			this.existLotTecns = existLotTecns;
			this.lots = lots;		
		}
		
		@Override
		protected Control buildView(Composite parent) {
			setTitleImage(SWTResourceCache.getImage("operation-dialog"));
	        setTitle(Message.getString("wip.lot_tecn_title"));
	        setMessage(Message.getString("wip.lot_tecn_info"));     
	    	try {		
	 			tableManager = new ListTableManager(table, true);
	 			tableManager.setAutoSizeFlag(false);
	 			tableManager.newViewer(parent);
	 			tableManager.addDoubleClickListener(new IMouseAction() {
					@Override
					public void run(NatTable natTable, MouseEvent event) {
						editAdapter();
					} 				
	 			}); 			
	 			tableManager.setInput(initLotTecns());
	 			
	 			Composite btnComposite = new Composite(parent, SWT.NONE);
				GridLayout btnLayout = new GridLayout(1, false);
				btnComposite.setLayout(btnLayout);
				GridData btnGridData = new GridData(GridData.END);
				btnGridData.horizontalAlignment = SWT.RIGHT;
				btnComposite.setLayoutData(btnGridData);
				SquareButton edit = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonEdit()), null);
				edit.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						try {
							editAdapter();
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				});
	    	} catch (Exception e) {
	 			e.printStackTrace();
	 		}
	        return parent;
		}

		protected void editAdapter() {
			try {
				LotTecn lotTecn = (LotTecn) tableManager.getSelectedObject();
				if (lotTecn != null) {
					LotTecnEditDialog dialog = new LotTecnEditDialog(table, lotTecn, false);
					if (dialog.open() == Dialog.OK) {
						lotTecn = (LotTecn) dialog.getAdObject();
					}
					tableManager.refresh();
				}	
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
	 		}
		}
		
		protected void okPressed() {
			try {
				List<LotTecn> lotTecns = (List<LotTecn>) (List)tableManager.getCheckedObject();	
				if (lotTecns != null && lotTecns.size() > 0) {		
					setCheckedLotTecns(lotTecns);
					LotManager lotManager = Framework.getService(LotManager.class);	
					for (LotTecn lotTecn : lotTecns) {
						lotTecn.setEcnSource(ecnSource);
						lotTecn.setEcnSourceName(ecnSourceName);
						if (StringUtil.isEmpty(lotTecn.getEquipmentId())
								&& StringUtil.isEmpty(lotTecn.getRecipeName())
								&& StringUtil.isEmpty(lotTecn.getEquipmentRecipeName())
								&& StringUtil.isEmpty(lotTecn.getReticleName())
								&& StringUtil.isEmpty(lotTecn.getEdcName())) {
							UI.showError(Message.getString("edc.data_save_is_null"));
							return;
						}	
						
						if (!StringUtil.isEmpty(lotTecn.getRecipeName())) {
							//判断Recipe是否存在
							lotManager.validLogicRecipe(Env.getOrgRrn(), lotTecn.getRecipeName());
						}
							
						if (!StringUtil.isEmpty(lotTecn.getEquipmentRecipeName())) {			
							if (StringUtil.isEmpty(lotTecn.getRecipeName())) {
								//设置PPID，LogicRecipe不能为空。
								UI.showError(Message.getString("wip.lot_tecn_setup_ppid_logicrecipe_not_is_null"));
								return;
							} else {
								if (!StringUtil.isEmpty(lotTecn.getEquipmentId())) {
									//设备，LogicRecipe，PPID都不为空的情况检查。
									lotManager.validRecipeEquipment(Env.getOrgRrn(), lotTecn.getEquipmentId(), lotTecn.getRecipeName(), lotTecn.getEquipmentRecipeName());
								} else {
									//LogicRecipe，PPID都不为空的情况。
									lotManager.validRecipeEquipment(Env.getOrgRrn(), null, lotTecn.getRecipeName(), lotTecn.getEquipmentRecipeName());
								}
							}
						}	
					}	
					
					if (isPersist) {
						List<ContextValue> contextValues = new ArrayList<ContextValue>();
						for (LotTecn lotTecn : lotTecns) {
							lotTecn.setEcnSource(LotTecn.ECN_SOURCE_PILOT);
							ADManager entityManager = Framework.getService(ADManager.class);			
							ContextValue contextValue = new ContextValue();
							if (lotTecn.getContextValueRrn() != null) {
								contextValue.setObjectRrn(lotTecn.getContextValueRrn());
								contextValue = (ContextValue) entityManager.getEntity(contextValue);
							} else {
								ContextManager contextManager = Framework.getService(ContextManager.class);
								Context context = contextManager.getContextByName(Env.getOrgRrn(), LotTecn.CONTEXT_LOTTECN);				
								contextValue.setContextRrn(context.getObjectRrn());
							}
							contextValue.setResultValue1(lotTecn.getEquipmentId());
							contextValue.setResultValue2(lotTecn.getRecipeName());
							contextValue.setResultValue3(lotTecn.getReticleName());
							contextValue.setResultValue4(lotTecn.getEdcName());
							contextValue.setResultValue5(lotTecn.getEquipmentRecipeName());
							contextValues.add(contextValue);
						}
								
						lotTecns = lotManager.saveLotTecn(lotTecns, contextValues, Env.getSessionContext());
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));	
						setCheckedLotTecns(lotTecns);
					}					
				}
				
				super.okPressed();
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
	 		}
		}
		
		protected List<LotTecn> getCheckedObject() {
			List<LotTecn> lotTecns = getCheckedLotTecns();;
			if (lotTecns != null && lotTecns.size() > 0) {		
				return lotTecns;
			}
			return new ArrayList<LotTecn>();
		}
		
		/**
		 * 初始化LotTecn,剔除已添加的
		 * @return
		 */
		protected List<LotTecn> initLotTecns() {
			List<LotTecn> lotTecns = Lists.newArrayList();
			try {			
				if (lots != null && lots.size() > 0) {
					for (Lot lot : lots) {
						PrdManager prdManager = Framework.getService(PrdManager.class);
						Procedure procedure = new Procedure();
						procedure.setOrgRrn(Env.getOrgRrn());
						procedure.setName(lot.getProcedureName());
						procedure.setVersion(lot.getProcedureVersion());	
						List<StepState> stepStates = prdManager.getStepChildren(procedure);
						
						if (stepStates != null && stepStates.size() > 0) {
							int startIndex = 0;
							int endIndex = stepStates.size() - 1;
							for (int i = 0; i < stepStates.size(); i++) {
								if (!StringUtil.isEmpty(String.valueOf(lot.getAttribute1())) 
										&& String.valueOf(lot.getAttribute1()).equals(stepStates.get(i).getUsedStep().getName())) {
									startIndex = i;
								}
								
								if (!StringUtil.isEmpty(String.valueOf(lot.getAttribute2())) 
										&& String.valueOf(lot.getAttribute2()).equals(stepStates.get(i).getUsedStep().getName())) {
									endIndex = i;
								}
							}
							if (existLotTecns != null && existLotTecns.size() > 0) {
								for (int index = startIndex; index <= endIndex; index++) {	
									boolean flag = true;
									for (LotTecn existLotTecn : existLotTecns) {
										if (existLotTecn.getLotId().equals(lot.getLotId())
												&& existLotTecn.getStepName().equals(stepStates.get(index).getUsedStep().getName())) {
											flag = false;
											break;				
										}
									}
									if (flag) {
										LotTecn lotTecn = new LotTecn();
			 							lotTecn.setLotId(lot.getLotId());
			 							lotTecn.setStepName(stepStates.get(index).getUsedStep().getName());
			 							lotTecn.setStepVersion(stepStates.get(index).getUsedStep().getVersion());
			 							lotTecns.add(lotTecn);
									}
								}
							} else {
								for (int index = startIndex; index <= endIndex; index++) {	 										 						
									LotTecn lotTecn = new LotTecn();
			 						lotTecn.setLotId(lot.getLotId());
			 						lotTecn.setStepName(stepStates.get(index).getUsedStep().getName());
			 						lotTecn.setStepVersion(stepStates.get(index).getUsedStep().getVersion());
			 						lotTecns.add(lotTecn); 	 										 								
								}
							}
						}
					}	
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
	 		}
			return lotTecns;	
		}

		public List<LotTecn> getCheckedLotTecns() {
			return checkedLotTecns;
		}

		public void setCheckedLotTecns(List<LotTecn> checkedLotTecns) {
			this.checkedLotTecns = checkedLotTecns;
		}

		public ADTable getTable() {
			return table;
		}

		public void setTable(ADTable table) {
			this.table = table;
		}
		
		@Override
		protected Point getInitialSize() {
			return new Point(1000, 800);
		}		
	}
	
	/**
	 * 编辑LotTecn弹出框
	 * <AUTHOR>
	 *
	 */
	public class LotTecnEditDialog extends EntityDialog {
		
		private Boolean isEdit;
		
		public LotTecnEditDialog(ADTable table, ADBase adObject, Boolean isEdit) {
			super(table, adObject);
			this.isEdit = isEdit;
		}
		
		public LotTecnEditDialog(ADTable table, ADBase adObject) {
			this(table, adObject, true);
		}
		
		protected boolean saveAdapter() {
			try {
				managedForm.getMessageManager().removeAllMessages();
				if (getAdObject() != null) {
					boolean saveFlag = true;
					for (Form detailForm : getDetailForms()) {
						if (!detailForm.saveToObject()) {
							saveFlag = false;
						}
					}
					if (saveFlag) {
						LotManager lotManager = Framework.getService(LotManager.class);
						
						LotTecn lotTecn = (LotTecn)getAdObject();	
						lotTecn.setEcnSource(ecnSource);
						lotTecn.setEcnSourceName(ecnSourceName);
						if (StringUtil.isEmpty(lotTecn.getEquipmentId())
								&& StringUtil.isEmpty(lotTecn.getRecipeName())
								&& StringUtil.isEmpty(lotTecn.getEquipmentRecipeName())
								&& StringUtil.isEmpty(lotTecn.getReticleName())
								&& StringUtil.isEmpty(lotTecn.getEdcName())) {
							UI.showError(Message.getString("edc.data_save_is_null"));
							return false;
						}
						
						if (!StringUtil.isEmpty(lotTecn.getRecipeName())) {
							//判断Recipe是否存在
							lotManager.validLogicRecipe(Env.getOrgRrn(), lotTecn.getRecipeName());
						}
							
						if (!StringUtil.isEmpty(lotTecn.getEquipmentRecipeName())) {			
							if (StringUtil.isEmpty(lotTecn.getRecipeName())) {
								//设置PPID，LogicRecipe不能为空。
								UI.showError(Message.getString("wip.lot_tecn_setup_ppid_logicrecipe_not_is_null"));
								return false;
							} else {
								if (!StringUtil.isEmpty(lotTecn.getEquipmentId())) {
									//设备，LogicRecipe，PPID都不为空的情况检查。
									lotManager.validRecipeEquipment(Env.getOrgRrn(), lotTecn.getEquipmentId(), lotTecn.getRecipeName(), lotTecn.getEquipmentRecipeName());
								} else {
									//LogicRecipe，PPID都不为空的情况。
									lotManager.validRecipeEquipment(Env.getOrgRrn(), null, lotTecn.getRecipeName(), lotTecn.getEquipmentRecipeName());
								}
							}
						}	
												
						if (isPersist && isEdit) {
							List<LotTecn> lotTecns = new ArrayList<LotTecn>();
							lotTecns.add(lotTecn);
							
							List<ContextValue> contextValues = new ArrayList<ContextValue>();
							ADManager entityManager = Framework.getService(ADManager.class);			
							ContextValue contextValue = new ContextValue();
							if(lotTecn.getContextValueRrn() != null) {
								contextValue.setObjectRrn(lotTecn.getContextValueRrn());
								contextValue = (ContextValue) entityManager.getEntity(contextValue);
							}else {
								ContextManager contextManager = Framework.getService(ContextManager.class);
								Context context = contextManager.getContextByName(Env.getOrgRrn(), LotTecn.CONTEXT_LOTTECN);				
								contextValue.setContextRrn(context.getObjectRrn());
							}
							contextValue.setResultValue1(lotTecn.getEquipmentId());
							contextValue.setResultValue2(lotTecn.getRecipeName());
							contextValue.setResultValue3(lotTecn.getReticleName());
							contextValue.setResultValue4(lotTecn.getEdcName());
							contextValue.setResultValue5(lotTecn.getEquipmentRecipeName());
							contextValues.add(contextValue);
							
							
							List<LotTecn> saveLotTecns = lotManager.saveLotTecn(lotTecns, contextValues, Env.getSessionContext());
							UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
							setAdObject(saveLotTecns.get(0));
							return true;
						} else {
							setAdObject(getAdObject());
							return true;
						}
						
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return false;
			}
			return false;
		}
	}

	public String getEcnSource() {
		return ecnSource;
	}

	public void setEcnSource(String ecnSource) {
		this.ecnSource = ecnSource;
	}

	public String getEcnSourceName() {
		return ecnSourceName;
	}

	public void setEcnSourceName(String ecnSourceName) {
		this.ecnSourceName = ecnSourceName;
	}
	
}
