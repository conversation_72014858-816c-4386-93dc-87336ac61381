package com.glory.mes.wip.custom;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.lot.run.bylot.RunByLotQueryTableManager;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

/**
 * 用于管理载具批次界面，分为上下两个部分
 * 上部分为源载具列表：
 *  输入载具号显示载具中所有的批次信息 
 *  选择批次,显示批次的详细信息
 * 下半部分为目标载具列表：
 *  针对性的显示目标载具，输入目标载具显示下面的批次
 */
public class CarrierAssignCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(CarrierAssignCustomComposite.class);
	
	public static final String ID_CARRIER_LOT = "carrierLot";
	public static final String ATTRIBUTE_SHOW_TARGET_CARRIER = "ShowTargetCarrier";
	public static final String ATTRIBUTE_TARGET_LOTTABLENAME = "TargetLotTableName";
	public static final String ATTRIBUTE_TARGET_TABLE_HEIGT_HHINT = "TargetTableHeigthHint";
	public static final String ATTRIBUTE_TARGET_LOTLISTAUTOSIZE = "TargetLotListAutoSize";
	
	private static final String TABLE_NAME = "WIPLotByCarrier";
	
	protected String lotTableName;
	protected boolean showTargetCarrier = false;
	protected boolean lotListAutoSize = true;
	
	private CarrierLotCustomComposite carrierLotComposite;
	private List<ADFormAttribute> fieldFormAttributes;
	protected ListTableManager lotTableManager;
	protected HeaderText txtCarrierId;
	
	protected Integer tableHeigthHint;
	
	public CarrierAssignCustomComposite() {
		super();
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		try {
			parent.setLayout(new GridLayout(1, false));
			parent.setLayoutData(new GridData(GridData.FILL_BOTH));
			parent.setBackgroundMode(SWT.INHERIT_FORCE);
			
			carrierLotComposite = new CarrierLotCustomComposite();
			carrierLotComposite.setAttributes(fieldFormAttributes);
			carrierLotComposite.setField(this.getField());
			carrierLotComposite.setId(ID_CARRIER_LOT);
			carrierLotComposite.setLblCarrier(Message.getString("wip.source_carrier_id"));
			carrierLotComposite.createForm(toolkit, parent);
			
			if (showTargetCarrier) {
				Composite targetCarrierComposite = new Composite(parent, SWT.NONE);
				targetCarrierComposite.setLayout(new GridLayout(2, false));
				
				Label lblCarrierId = new Label(targetCarrierComposite, SWT.NONE);
				lblCarrierId.setText(Message.getString("wip.target_carrier_id"));
				lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtCarrierId = new HeaderText(targetCarrierComposite, SWTResourceCache.getImage("header-text-carrier"));
				txtCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtCarrierId.setTextLimit(32);
				
				final String targetCarrierIdEvent = "carrierId";
				EventHandler defaultCarrierEvent = new EventHandler() {
		             public void handleEvent(Event event) {
		            	 getLotsByCarrierId((String)event.getProperty(GlcEvent.PROPERTY_DATA));
		             }
		        }; 
		        subscribeDefaultEvent(targetCarrierIdEvent, GlcEvent.EVENT_ENTERPRESSED, defaultCarrierEvent);
				txtCarrierId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// 回车事件
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String carrierId = ((Text) event.widget).getText();
							if (!StringUtil.isEmpty(carrierId)) {
								postEvent(targetCarrierIdEvent, GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(carrierId));	
							}
						}
					}
				});
				
				Composite composite = new Composite(parent, SWT.NONE);
				GridLayout layout = new GridLayout(1, true);
				layout.horizontalSpacing = 0;
				layout.verticalSpacing = 0;
				layout.marginHeight = 0;
				layout.marginWidth = 0;
				layout.marginLeft = 0;
				layout.marginRight = 0;
				layout.marginTop = 0;
				layout.marginBottom = 0;
				composite.setLayout(layout);
				
				GridData gridData = new GridData(GridData.FILL_BOTH);
				if (tableHeigthHint != null) {
					gridData = new GridData(GridData.FILL_HORIZONTAL);
					int minHeightHint = Toolkit.getDefaultToolkit().getScreenSize().height / 6;
					if (minHeightHint > tableHeigthHint) {
						tableHeigthHint = minHeightHint;
					}
					gridData.heightHint = tableHeigthHint;
				}
				composite.setLayoutData(gridData);
				composite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
				
				Composite lotComposite = new Composite(composite, SWT.NONE);
				configureBody(lotComposite);

				final String lotTableId = "targetCarrierlotTable";
				ADManager adManager = Framework.getService(ADManager.class);
				ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), getLotTableName());
				lotTable.setIsFilter(false);
				lotTableManager = new RunByLotQueryTableManager(lotTable);
				lotTableManager.setAutoSizeFlag(lotListAutoSize);
				lotTableManager.newViewer(lotComposite);
				lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
					@Override
					public void selectionChanged(SelectionChangedEvent event) {
						StructuredSelection selection = (StructuredSelection) event.getSelection();
						Lot lot = (Lot) selection.getFirstElement();
						postEvent(lotTableId, GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(lot));
					}
				});
			}
		} catch (Exception e) {
			logger.error("LotCarrierInfoCustomComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return parent;
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isNotEmpty(lots)) {
					lotTableManager.setInput(lots);
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					lotTableManager.refresh();
				}
			} else {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
		} catch (Exception e) {
			logger.error("LotCarrierInfoCustomComposite getLotByLotId error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void refresh() {
	}
	
	@Override
	public Object getValue() {
		return null;
	}
	
	public HeaderText getTxtCarrierId() {
		return txtCarrierId;
	}

	public void setTxtCarrierId(HeaderText txtCarrierId) {
		this.txtCarrierId = txtCarrierId;
	}
	
	public String getLotTableName() {
		if (StringUtil.isEmpty(lotTableName)) {
			return TABLE_NAME;
		}
		return lotTableName;
	}

	public void setLotTableName(String lotTableName) {
		this.lotTableName = lotTableName;
	}
	
	public CarrierLotCustomComposite getCarrierLotComposite() {
		return carrierLotComposite;
	}

	public void setCarrierLotComposite(CarrierLotCustomComposite carrierLotComposite) {
		this.carrierLotComposite = carrierLotComposite;
	}

	@Override
	public void setValue(Object value) {
	}
	
	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_SHOW_TARGET_CARRIER:
				showTargetCarrier = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TARGET_LOTTABLENAME:
				lotTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TARGET_TABLE_HEIGT_HHINT:
				tableHeigthHint = formAttribute.getIntValue();
				break;
			case ATTRIBUTE_TARGET_LOTLISTAUTOSIZE:
				lotListAutoSize = formAttribute.getBooleanValue();
				break;
			}
			fieldFormAttributes = Lists.newArrayList(formAttributes);
		}
		
	}
	
	public void preDestory() {
		carrierLotComposite.preDestory();
		super.preDestory();
    }

    public ListTableManager getTargetLotTableManager() {
        return lotTableManager;
    }

}
