package com.glory.mes.wip.custom.depend;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTree;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.entitymanager.tree.EntityTreeNodeAdapter;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.model.Location;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.EquipmentUser;

public class EqpTreeNodeAdapter extends EntityTreeNodeAdapter {

	private static final Logger logger = Logger.getLogger(EqpTreeNodeAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	
	protected List<Location> allLocaltions;
	protected List<Equipment> allEquipments;
	
	public EqpTreeNodeAdapter(List<ADTree> treeNodes) {
		super(treeNodes);
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			allEquipments = adManager.getEntityList(Env.getOrgRrn(), Equipment.class);
			//去筛选出有权限的设备
			RASManager rasManager = Framework.getService(RASManager.class);
			allEquipments = rasManager.getAvailableEquipmentByUser(allEquipments, true, Env.getSessionContext());
			
			allLocaltions = adManager.getEntityList(Env.getOrgRrn(), Location.class);
		} catch (Exception e) {
			logger.error("StepTreeView refrsh error:", e);
		}
	}
	
	@Override
	public Object[] getChildren(Object object) {
		try {
			if (object instanceof EntityNodeObject) {
				EntityNodeObject parentObject = (EntityNodeObject)object;
				
				List<ADTree> childTreeNodes = parentObject.getChildTreeObjects();
				if (childTreeNodes.size() > 0) {
					List<EntityNodeObject> entityNodeObjects = new ArrayList<EntityNodeObject>();
					ADManager adManager = Framework.getService(ADManager.class);
					
					boolean isLocationEquipmentNode = false;
					//跨层显示设备，比如L1-L2-EQP, 可以L1-EQP
					if (Equipment.class.getSimpleName().equals(childTreeNodes.get(0).getModelName())) {
						if (((EntityNodeObject)object).getEntityObject() instanceof Location) {
							isLocationEquipmentNode = true;
							Location location = (Location) ((EntityNodeObject)object).getEntityObject();
							
							List<Equipment> locationEquipments = new ArrayList<Equipment>();
							locationEquipments = getEquipmentByLocation(location, locationEquipments);
							if (locationEquipments != null && locationEquipments.size() > 0) {
								for (Equipment locationEquipment : locationEquipments) {	
									boolean flag = true;
									for (EntityNodeObject entityNodeObject : entityNodeObjects) {
										if (((Equipment)entityNodeObject.getEntityObject()).getEquipmentId().equals(locationEquipment.getEquipmentId())) {
											flag = false;
											break;
										}
									}	
									if (flag) {
										EntityNodeObject newEntityNodeObject = new EntityNodeObject();
										newEntityNodeObject.setEntityObject(locationEquipment);
										newEntityNodeObject.setTreeObject(childTreeNodes.get(0));
										entityNodeObjects.add(newEntityNodeObject);
									}
								}
								//重新按设备ID排序
								Collections.sort(entityNodeObjects, new Comparator<EntityNodeObject>() {  								  
						            @Override  
						            public int compare(EntityNodeObject o1, EntityNodeObject o2) { 
						            	return ((Equipment)o1.getEntityObject()).getEquipmentId()
						            			.compareTo(((Equipment)o2.getEntityObject()).getEquipmentId());
						            }  
						        }); 
							}
							
						}
					}
					
					if (!isLocationEquipmentNode) {
						for (ADTree childTreeNode : childTreeNodes) {
							Class calzz = Class.forName(childTreeNode.getModelClass());
							String whereCaluse = childTreeNode.getWhereClause();
							if (whereCaluse != null && whereCaluse.trim().length() > 0) {
								Map<String, String> paramMap = new HashMap<String, String>();
								List<String> params = StringUtil.parseClauseParam(whereCaluse);
								for (String param : params) {
									paramMap.put(param, String.valueOf(PropertyUtil.getPropertyForString(parentObject.getEntityObject(), param)));
								}	
								whereCaluse = StringUtil.parseClause(whereCaluse, paramMap);
							}
							List<Object> entityObjects = adManager.getEntityList(Env.getOrgRrn(), calzz, Integer.MAX_VALUE, whereCaluse, childTreeNode.getOrderByClause());
							if (entityObjects.size() > 0) {
								//获得子节点的下层ADTree信息
								List<ADTree> thisChildTreeNodes = new ArrayList<ADTree>();
								for (ADTree treeNode : treeNodes) {
									if (childTreeNode.getObjectRrn().equals(treeNode.getParentRrn())) {
										thisChildTreeNodes.add(treeNode);
									}
								}
								
								for (Object entityObject : entityObjects) {
									EntityNodeObject entityNodeObject = new EntityNodeObject();
									entityNodeObject.setEntityObject(entityObject);
									entityNodeObject.setTreeObject(childTreeNode);
									entityNodeObject.setChildTreeObjects(thisChildTreeNodes);
									entityNodeObjects.add(entityNodeObject);
								}
							}	
						}	
					}
										 					 
					return entityNodeObjects.toArray();
				}
			}
		} catch (Exception e) {
	        logger.error("Error at LocationItemAdapter : getChildren() ", e);
		}
		
		return EMPTY;
	}
	
	/**
	 * 根据当前位置，逐层查询设备
	 * @param location
	 * @param allEquipments
	 * @param adManager
	 * @return
	 */
	public List<Equipment> getEquipmentByLocation(Location currentLocation, List<Equipment> locationEquipments) {
		try {	
			for (Equipment equipment : allEquipments) {
				if (currentLocation.getName().equals(equipment.getLocation())) {
					if (!locationEquipments.contains(equipment)) {
						if ("MainEqp".equals(equipment.getCategory())) {
							locationEquipments.add(equipment);
						}
					}
				}
			}
			
			for (Location location : allLocaltions) {
				if (currentLocation.getObjectRrn().equals(location.getParentLocationRrn())) {						
					getEquipmentByLocation(location, locationEquipments);
				}
			}		
		} catch(Exception e) {
			logger.error(e.getMessage(), e);
		}
		return locationEquipments;
	}

	public void refresh(List<Equipment> allEquipments, List<Location> allLocaltions) {
		this.allEquipments = allEquipments;
		this.allLocaltions = allLocaltions;
	}
	
}
