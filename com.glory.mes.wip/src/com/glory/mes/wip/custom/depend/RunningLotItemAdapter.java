package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.eclipse.swt.graphics.Image;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

@Deprecated
public class RunningLotItemAdapter extends ListItemAdapter<Lot> {

	@Override
	public List<Image> getImages(Object object) {
		Lot lot = (Lot) object;
		List<Image> images = Lists.newArrayList();
		
		// 检查Priority，只支持1~10，其它显示？号
		if (lot.getPriority() != null && (lot.getPriority() > 0 && lot.getPriority() < 11)) {
			images.add(SWTResourceCache.getImage("lotstatus_priority_" + lot.getPriority()));
		} else {
			images.add(SWTResourceCache.getImage("lotstatus_priority_unknown"));
		}
		
		// 检查hold状态
		if (Lot.HOLDSTATE_OFF.equals(lot.getHoldState())) {
			images.add(SWTResourceCache.getImage("lotstatus_no_hold"));
		} else {
			images.add(SWTResourceCache.getImage("lotstatus_hold"));
		}
		
		String state = lot.getState();
		if (LotStateMachine.STATE_RUN.equals(state)) {
			images.add(SWTResourceCache.getImage("lotstatus_state_run"));
		} else {
			images.add(SWTResourceCache.getImage("lotstatus_state_unknown"));
		}
		return images;
	}
	
}
