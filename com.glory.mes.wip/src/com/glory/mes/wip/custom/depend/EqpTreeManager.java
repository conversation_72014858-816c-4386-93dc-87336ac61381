package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeColumn;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTree;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.model.Location;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;

public class EqpTreeManager extends TreeViewerManager {
	
	private static final Logger logger = Logger.getLogger(EqpTreeManager.class);
	
	private List<ADTree> treeNodes;
	
	
	public EqpTreeManager(List<ADTree> treeNodes) {
		this.treeNodes = treeNodes;
	}
	
	@Override
	protected ItemAdapterFactory createAdapterFactory() {
		ItemAdapterFactory factory = new ItemAdapterFactory();
		try {
			factory.registerAdapter(ADTree.class, new EqpTreeNodeAdapter(treeNodes));
			factory.registerAdapter(EntityNodeObject.class, new EqpTreeNodeAdapter(treeNodes));	
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return factory;
	}
	
	protected void fillColumns(Tree tree, String[] columns,
			String[] columnsHeader, int[] columnsWidths) {
		if (columns != null) {
			tree.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
			tree.setLinesVisible(true);
			tree.setHeaderVisible(true);

			int totleSize = 0;
			for (int i = 0; i < columns.length; i++) {
				TreeColumn column;
				column = new TreeColumn(tree, SWT.NONE);
				if (columnsHeader != null) {
					column.setText(columnsHeader[i]);
				} else {
					column.setText(columns[i]);
				}
				totleSize += columnsWidths[i];
				column.setData("id", columns[i]);
				column.setWidth(columnsWidths[i]);
				column.setResizable(true);
			}
		}
		tree.pack();
	}
	
	public void refresh() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<Equipment> allEquipments = adManager.getEntityList(Env.getOrgRrn(), Equipment.class);
			//去筛选出有权限的设备
			RASManager rasManager = Framework.getService(RASManager.class);
			allEquipments = rasManager.getAvailableEquipmentByUser(allEquipments, true, Env.getSessionContext());
			
			List<Location> allLocaltions = adManager.getEntityList(Env.getOrgRrn(), Location.class);
			
			EqpTreeNodeAdapter treeAdapter = (EqpTreeNodeAdapter)adapterFactory.getAdapter(ADTree.class);
			EqpTreeNodeAdapter nodeAdapter = (EqpTreeNodeAdapter)adapterFactory.getAdapter(EntityNodeObject.class);
			treeAdapter.refresh(allEquipments, allLocaltions);
			nodeAdapter.refresh(allEquipments, allLocaltions);
		} catch (Exception e) {
			logger.error("EqpTreeManager : refresh()", e);
		}
	}

	public List<ADTree> getTreeNodes() {
		return treeNodes;
	}

	public void setTreeNodes(List<ADTree> treeNodes) {
		this.treeNodes = treeNodes;
	}
}
