package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentComposite;
import com.glory.mes.wip.comp.ComponentComposite.OccupationPolicy;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class ComponentCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(ComponentCustomComposite.class);

	public static enum CarrierFieldType{
		TEXT, REFTABLE
	}

	public static final String ATTRIBUTE_SHOW_CHEKBOX = "ShowCheckBox";
	public static final String ATTRIBUTE_SHOW_CARRIER = "ShowCarrier";
	public static final String ATTRIBUTE_EDIE_CARRIER = "EditCarrier";
	public static final String ATTRIBUTE_SHOW_LOT = "ShowLot";
	public static final String ATTRIBUTE_IS_ASC = "IsAsc";
	public static final String ATTRIBUTE_CARRIER_VALIDATE = "CarrierValidate";
	public static final String ATTRIBUTE_CARRIER_SIZE = "CarrierSize";
	public static final String ATTRIBUTE_TABLE_NAME = "TableName";
	
	public static final String ATTRIBUTE_ITEM_ADAPTER = "ItemAdapter";

	public static final String EVENT_SETLOT = "setLot";
	public static final String EVENT_SETCARRIER = "setCarrier";
	
	public static final String CARRIER_LIST_REFTABLE_NAME = "MMAvailableCarrierList";

	// 是否是Target列表
	protected boolean isTargetFlag = false;
	// 是否可编辑载具
	protected boolean editCarrierFlag = true;
	protected boolean showCarrierFlag;
	protected boolean showLotFlag;
	protected boolean ascFlag;
	protected boolean checkFlag;
	protected boolean checkCarrierVaildFlag = false;
	protected Integer carrierSize;
	protected String tableName;
	protected CarrierFieldType carrierFieldType = CarrierFieldType.TEXT;

	public HeaderText txtCarrierId;
	public RefTableField refCarrierId;
	public HeaderText txtLotId;
	public ListTableManager tableManager;
	public ComponentComposite componentComposite;
	
	public String lblCarrier;
	protected String[] itemAdapters;
	protected String itemAdapter;

	protected EventHandler setLotHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	setLot((String)event.getProperty(GlcEvent.PROPERTY_DATA));
        }
	};
	
	protected EventHandler setCarrierHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	setCarrier((String)event.getProperty(GlcEvent.PROPERTY_DATA));
        }
	};
	
	@Override
	public void initSubscribeEvent() {
		subscribeEvent(null, EVENT_SETLOT, setLotHandler);
		subscribeEvent(null, EVENT_SETCARRIER, setCarrierHandler);
	}
	
	@Override
    public void preDestory() {
		super.preDestory();
		unsubscribeEvent(setLotHandler);
		unsubscribeEvent(setCarrierHandler);
    }
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		try {
			parent.setLayout(new GridLayout(1, false));
			Composite carrierComposite = new Composite(parent, SWT.NONE);
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			carrierComposite.setLayout(new GridLayout(gridLayoutSize(), false));
			carrierComposite.setLayoutData(gd);
			
			Composite carrierCompositeFrom = new Composite(parent, SWT.NONE);
			carrierCompositeFrom.setLayout(new GridLayout(1, false));
			carrierCompositeFrom.setLayoutData(new GridData(GridData.FILL_BOTH));

			if (showCarrierFlag) {
				Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
		        if (StringUtil.isEmpty(lblCarrier)) {
					lblCarrierId.setText(Message.getString("wip.carrier_id"));
				} else {
					lblCarrierId.setText(lblCarrier);
				}
		        lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		        switch (carrierFieldType) {
				case TEXT:
					txtCarrierId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-carrier"));
					txtCarrierId.setTextLimit(32);
					final String controlCarrierId = "carrierId";
					EventHandler defaultCarrierEvent = new EventHandler() {
						public void handleEvent(Event event) {
							getComponentsByCarrier((String)event.getProperty(GlcEvent.PROPERTY_DATA), true);
						}
					}; 
					subscribeDefaultEvent(controlCarrierId, GlcEvent.EVENT_ENTERPRESSED, defaultCarrierEvent);
					txtCarrierId.addKeyListener(new KeyAdapter() {
						@Override
						public void keyPressed(KeyEvent event) {
							if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
								String lotId = ((Text) event.widget).getText().trim();
								if (!StringUtil.isEmpty(lotId)) {
									postEvent(controlCarrierId, GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(lotId));	
								}
							}
						}
					});
					if (!editCarrierFlag) {
						txtCarrierId.setEnabled(false);
					}
					break;
				case REFTABLE:
					ADManager adManager = Framework.getService(ADManager.class);
					
					List<ADRefTable> refTables = adManager.getEntityList(Env.getOrgRrn(), ADRefTable.class, 1, " name = '" + CARRIER_LIST_REFTABLE_NAME + "'" ,  "");
					if (refTables.size() == 1) {
						ADRefTable refTable = refTables.get(0);
						
						ADTable adTable  = adManager.getADTable(refTable.getTableRrn());
						ListTableManager tableManager = new ListTableManager(adTable);
						if (refTable.getWhereClause() == null || "".equalsIgnoreCase(refTable.getWhereClause().trim())
								|| StringUtil.parseClauseParam(refTable.getWhereClause()).size() == 0) {
							List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), 
									adTable.getObjectRrn(), Env.getMaxResult(), refTable.getWhereClause(), refTable.getOrderByClause());
							tableManager.setInput(list);
						}
						int mStyle = SWT.READ_ONLY | SWT.BORDER;
						refCarrierId = new RefTableField("", tableManager, refTable, mStyle);
						refCarrierId.setLabel(null);  //
						refCarrierId.addValueChangeListener(new IValueChangeListener(){
							@Override
							public void valueChanged(Object arg0, Object arg1) {
								try {
									if (!StringUtil.isEmpty(arg1.toString())) {
										getComponentsByCarrier(arg1.toString(), true);
									}
								} catch (Exception e) {
									e.printStackTrace();
								}					
							}   		
			    		});
						refCarrierId.createContent(carrierComposite, toolkit);
						
						if (!editCarrierFlag) {
							refCarrierId.setEnabled(false);
						}
					}
					break;
				}
			} 
			if (showLotFlag) {
				Label lblLotId = new Label(carrierComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtLotId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-lot"));
				txtLotId.setTextLimit(64);
				
				final String controlLotId = "lotId";
				EventHandler defaultLotEvent = new EventHandler() {
		             public void handleEvent(Event event) {
		            	 getComponentsByLot((String)event.getProperty(GlcEvent.PROPERTY_DATA), true);
		             }
		        }; 
		        subscribeDefaultEvent(controlLotId, GlcEvent.EVENT_ENTERPRESSED, defaultLotEvent);
				txtLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// 回车事件
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText().trim();
							if (!StringUtil.isEmpty(lotId)) {
								postEvent(controlLotId, GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(lotId));	
		    				}
						}
					}
				});
			}
			
			final String componentTable = "componentTable";
			componentComposite = new ComponentComposite(carrierCompositeFrom, getTable(), 0, ascFlag, checkFlag);
			componentComposite.setItemAdapters(itemAdapters);
			if (carrierSize != null) {
				componentComposite.setCount(carrierSize);
			}
			componentComposite.init();
			tableManager = componentComposite.getTableManager();
			componentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					postEvent(componentTable, GlcEvent.EVENT_DOUBLE_CLICK, GlcEvent.buildEventData(tableManager.getSelectedObject()));	
				}
			});
		} catch (Exception e) {
			logger.error("ComponentCustomComposite createForm error:", e);
		}
		return parent;
	}

	public List<ComponentUnit> getComponentsByCarrier(String carrierId, boolean isInitComponent) {
		try {
			if (!StringUtil.isEmpty(carrierId)) {	
				if (txtCarrierId != null) {
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				} else if (refCarrierId != null) {
					refCarrierId.setForeColor(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				}
				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, false);
				if(carrier == null) {
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
					return null;
				}
				if (checkCarrierVaildFlag) {
					//进行有效性检查
					durableManager.checkCarrierAvailable(Env.getSessionContext(), carrier);
					if (!carrier.getIsAvailable()) {
						UI.showWarning(Message.getString(carrier.getMessage()));
						return null;
					}
				}
				
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				LotManager lotManager = Framework.getService(LotManager.class);
				
				List<ComponentUnit> components = carrierLotManager.getComponentByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isEmpty(components)) {
					components = Lists.newArrayList();
				}
				//Target列表位置上已有选片的Case
				if (isTargetFlag && MapUtils.isNotEmpty(componentComposite.getInitCarrierMap())) {
					// 清空Target列表 并将选片初始化
					componentComposite.initComponents(new ArrayList<ComponentUnit>());

					// 检查现在新输入的载具Comp信息
					boolean checkCompPosition  = false;
					for (ComponentUnit componentUnit : components) {
						if (!getComponentComposite().checkAddComponent(componentUnit, componentUnit.getPosition(), OccupationPolicy.REJECT)) {
							checkCompPosition = true;
							break;
						}
					}
					if (checkCompPosition) {
						return null;
					}
				}
				
				Map<Long, Lot> parentLots = new HashMap<Long, Lot>();
				for (ComponentUnit component : components) {
					if (!parentLots.containsKey(component.getParentUnitRrn())) {
						Lot parentLot = lotManager.getLot(component.getParentUnitRrn());
						parentLots.put(parentLot.getObjectRrn(), parentLot);
					}
					component.setLotId(parentLots.get(component.getParentUnitRrn()).getLotId());
				}
				
				componentComposite.setCount(carrier.getCapacity() != null ? carrier.getCapacity().intValue() : 25);
				componentComposite.setAscFlag(carrier.getSlotDirectionAsc());
				if (isInitComponent) {
					componentComposite.initComponents(components);
				} 
				return components;
			} else {
				if (isInitComponent && !isTargetFlag) {
					componentComposite.initComponents(new ArrayList<ComponentUnit>());
				}
				if (isTargetFlag) {
					componentComposite.getTableManager().setInput(componentComposite.getInitCarrierComponents());
				}
			}
		} catch(Exception e) {
			logger.error("ComponentCustomComposite getComponentsByCarrier:", e);
		}
		return null;
	}
	
	public Lot getComponentsByLot(String lotId, boolean isInitComponent) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			if (lot == null) {
				return null;
			}
			
			if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				LotManager lotManager = Framework.getService(LotManager.class);
				lot = lotManager.getLotWithComponentOrderByPosition(lot.getObjectRrn(), true);
			}
			
			List<ComponentUnit> components = new ArrayList<ComponentUnit>();
			if (lot == null) {
				tableManager.setInput(components);
				return null;
			}
			
			if (lot.getSubProcessUnit() != null && lot.getSubProcessUnit().size() > 0) {
				for (ProcessUnit processUnit : lot.getSubProcessUnit()) {
					ComponentUnit compUnit = (ComponentUnit)processUnit;
					compUnit.setLotId(lot.getLotId());
					components.add(compUnit);
				}
			}
			
			if (!StringUtil.isEmpty(lot.getDurable())) {
				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), lot.getDurable(), false);
				if (carrier != null) {
					componentComposite.setCount(carrier.getCapacity() != null ? carrier.getCapacity().intValue() : 25);
					componentComposite.setAscFlag(carrier.getSlotDirectionAsc());
				}
			}
			
			if (isInitComponent) {
				componentComposite.initComponents(components);
			}
			lot.setSubProcessUnit((List)(components));
			return lot;
		} catch (Exception e) {
			logger.error("ComponentCustomComposite getComponentsByLot:", e);
		}
		return null;
	}
	
	public ListTableManager getTableManager() {
		return tableManager;
	}
	
	public ComponentComposite getComponentComposite() {
		return componentComposite;
	}
	
	@Override
	public void refresh() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void setValue(Object value) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Object getValue() {
		// TODO Auto-generated method stub
		return null;
	}

	public void setLot(String lotId) {
		Lot lot = getComponentsByLot(lotId, true);
		if (txtLotId != null) {
			if (lot != null) {
				txtLotId.setText(lotId);
			} else {
				txtLotId.setText("");
			}
		}
		if (txtCarrierId != null) {
			if (lot != null && !StringUtil.isEmpty(lot.getDurable())) {
				txtCarrierId.setText(lot.getDurable());
			} else {
				txtCarrierId.setText("");
			}
		}
		
		if (refCarrierId != null) {
			if (lot != null &&!StringUtil.isEmpty(lot.getDurable())) {
				refCarrierId.setValue(lot.getDurable());
			} else {
				refCarrierId.setValue("");
			}
		}
	}

	public void setCarrier(String carrerId) {
		List<ComponentUnit> components = getComponentsByCarrier(carrerId, true);
		if (txtCarrierId != null) {
			if (components != null) {
				txtCarrierId.setText(carrerId);
			} else {
				txtCarrierId.setText("");
			}
		}
		
		if (refCarrierId != null) {
			if (components != null) {
				refCarrierId.setValue(carrerId);
			} else {
				refCarrierId.setValue("");
			}
		}
	}
	
	public ADTable getTable(){
		ADTable adTable = null;
		if(StringUtil.isEmpty(tableName)) {
			return null;
		}
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), tableName);
		} catch (Exception e) {
			logger.error("ComponentCustomComposite getTable:", e);
		}
		return adTable;
	}
	
	public int gridLayoutSize(){
		if(showLotFlag && showCarrierFlag) {
			return 4;
		}else {
			if(showLotFlag || showCarrierFlag) {
				return 2;
			}else {
				return 0;
			}
		}
	}
	
	public Integer getCarrierSize() {
		return carrierSize;
	}

	public void setCarrierSize(Integer carrierSize) {
		this.carrierSize = carrierSize;
	}
	
	public boolean isShowCarrierFlag() {
		return showCarrierFlag;
	}

	public void setShowCarrierFlag(boolean showCarrierFlag) {
		this.showCarrierFlag = showCarrierFlag;
	}

	public boolean isEditCarrierFlag() {
		return editCarrierFlag;
	}

	public void setEditCarrierFlag(boolean editCarrierFlag) {
		this.editCarrierFlag = editCarrierFlag;
	}

	public boolean isShowLotFlag() {
		return showLotFlag;
	}

	public void setShowLotFlag(boolean showLotFlag) {
		this.showLotFlag = showLotFlag;
	}

	public boolean isAscFlag() {
		return ascFlag;
	}

	public void setAscFlag(boolean ascFlag) {
		this.ascFlag = ascFlag;
	}

	public boolean isCheckFlag() {
		return checkFlag;
	}

	public void setCheckFlag(boolean checkFlag) {
		this.checkFlag = checkFlag;
	}

	public boolean isCheckCarrierVaildFlag() {
		return checkCarrierVaildFlag;
	}

	public void setCheckCarrierVaildFlag(boolean checkCarrierVaildFlag) {
		this.checkCarrierVaildFlag = checkCarrierVaildFlag;
	}

	public void setComponentComposite(ComponentComposite componentComposite) {
		this.componentComposite = componentComposite;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	
	public CarrierFieldType getCarrierFieldType() {
		return carrierFieldType;
	}

	public void setCarrierFieldType(CarrierFieldType carrierFieldType) {
		this.carrierFieldType = carrierFieldType;
	}

	public String getLblCarrier() {
		return lblCarrier;
	}

	public void setLblCarrier(String lblCarrier) {
		this.lblCarrier = lblCarrier;
	}

	public void setItemAdapter(String itemAdapter) {
		this.itemAdapter = itemAdapter;
		if (!StringUtil.isEmpty(itemAdapter)) {
			itemAdapters = itemAdapter.split(";");
		}
	}

	public void setCarrierIsEditable(boolean isEditable) {
		if (txtCarrierId != null) {
			txtCarrierId.setEditable(isEditable);
			if (!isEditable) {
				txtCarrierId.setBackground(txtCarrierId.getDisplay().getSystemColor(SWT.COLOR_WIDGET_BACKGROUND));
			} else {
				txtCarrierId.setBackground(txtCarrierId.getDisplay().getSystemColor(SWT.COLOR_WHITE));
			}
		}
		if (refCarrierId != null) {
			refCarrierId.getComboControl().setEditable(isEditable);
			if (!isEditable) {
				refCarrierId.getComboControl().setBackground(SWTResourceCache.getColor(SWT.COLOR_WIDGET_BACKGROUND));
			} else {
				refCarrierId.getComboControl().setBackground(SWTResourceCache.getColor(SWT.COLOR_WHITE));
			}
		}
	}
	
	public void setLotIsEditable(boolean isEditable) {
		if (txtLotId != null) {
			txtLotId.setEditable(isEditable);
			if (!isEditable) {
				txtLotId.setBackground(txtLotId.getDisplay().getSystemColor(SWT.COLOR_WIDGET_BACKGROUND));
			} else {
				txtLotId.setBackground(txtLotId.getDisplay().getSystemColor(SWT.COLOR_WHITE));
			}
		}
	}

	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_SHOW_CHEKBOX:
				checkFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_CARRIER:
				showCarrierFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_EDIE_CARRIER:
				editCarrierFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_LOT:
				showLotFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_IS_ASC:
				ascFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_CARRIER_VALIDATE:
				checkCarrierVaildFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_CARRIER_SIZE:
				carrierSize = formAttribute.getIntValue() == null ? 25 : formAttribute.getIntValue();
				break;
			case ATTRIBUTE_TABLE_NAME:
				tableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_ITEM_ADAPTER:
				itemAdapter = formAttribute.getStringValue();
				if (!StringUtil.isEmpty(itemAdapter)) {
					itemAdapters = itemAdapter.split(";");
				}
				break;
			}
		}
	}

	public boolean isTargetFlag() {
		return isTargetFlag;
	}

	public void setTargetFlag(boolean isTargetFlag) {
		this.isTargetFlag = isTargetFlag;
	}
	
}
