package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.def.TransitionRework;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutReworkQtyTable;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutReworkComposite extends CustomCompsite {

	private static String TABLE_NAME = "WIPTrackOutReworkQty";
	protected final static String TABLE_REWORK_PROCESS_UNIT = "WIPTrackOutReworkProcessUnit";
	protected static String HEADER_REWORK_CODE = Message.getString("wip.trackout_reworkcode");
	
	protected Step step;
	protected List<Lot> objects;
	protected Map<TrackOutReworkQtyTable, Lot> trackOutReworkQtyTableMap = new HashMap<TrackOutReworkQtyTable, Lot>();
	protected Map<CheckBoxTableViewerManager, Lot> checkBoxTableMap = new HashMap<CheckBoxTableViewerManager, Lot>();
	protected Map<CheckBoxTableViewerManager, XCombo> comboReworkProcedureMap = new HashMap<CheckBoxTableViewerManager, XCombo>();
	protected Map<CheckBoxTableViewerManager, XCombo> comboReworkCodeMap = new HashMap<CheckBoxTableViewerManager, XCombo>();
	
	protected FormToolkit toolkit;
	protected Composite parent;
	protected ScrolledForm form;
	protected ManagedForm mform;
	protected TrackOutContext context;
	protected List<Object> scrapUnits = new ArrayList<Object>();
	protected ADRefTable adRefTable;
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		this.toolkit = toolkit;
		this.parent = parent;
		return createConent();
	}
	
	public Composite createConent() {
		form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		if (CollectionUtils.isNotEmpty(objects)) {
			Composite body = form.getBody();
			GridLayout layout = new GridLayout(this.objects.size(), true);
			layout.horizontalSpacing = 8;
			
			body.setLayout(layout);
			body.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			for (int i = 0; i < this.getObjects().size(); i++) {
				createUnit(body, this.getObjects().get(i));
			}
		}
		mform = new ManagedForm(toolkit, form);
		return parent;
	}
	
	public Composite createUnit(Composite com, Lot lot) {
		Group group = new Group(com, SWT.NONE);
		GridLayout layout = new GridLayout(1, true);
		layout.numColumns = 1;
		layout.marginRight = 1;
		layout.marginLeft = 1;
		group.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.widthHint = 250;
		group.setLayoutData(gd);
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}
		
		if (ComponentUnit.getUnitType().equals(objects.get(0).getSubUnitType())) {
			createTableComponent(group, toolkit, lot);
		} else if (QtyUnit.getUnitType().equals(objects.get(0).getSubUnitType())) {
			createTableQty(group, lot);
		}
		return group;
	}
	
	//Qty类型面板
	protected void createTableQty(Composite composite, Lot lot) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			TrackOutReworkQtyTable tableManager = new TrackOutReworkQtyTable(adTable, lot);
			tableManager.newViewer(composite);
	
			String reworkCode = getReworkCode();
			List<ADURefList> list = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, 
					Env.getMaxResult(), "referenceName='" + reworkCode +"' ", "");
			List<QtyUnit> reworkCodeList = new ArrayList<QtyUnit>();
			for (ADURefList ref : list) {
				QtyUnit rework = new QtyUnit();
				rework.setActionCode(ref.getText());
				rework.setDescription(ref.getDescription());
				reworkCodeList.add(rework);
			}
			tableManager.setInput(reworkCodeList);		
			trackOutReworkQtyTableMap.put(tableManager, lot);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	//Component类型面板
	protected void createTableComponent(Composite composite, FormToolkit toolkit, Lot lot) {
		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
		tableContainer.setLayout(new GridLayout());
		tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		try {
			LotManager manager = Framework.getService(LotManager.class);
			lot = manager.getLotWithComponent(lot.getObjectRrn());
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_REWORK_PROCESS_UNIT);
			TrackOutReworkComponentTable tableViewerManager = new TrackOutReworkComponentTable(adTable);
			tableViewerManager.newViewer(tableContainer);
			tableViewerManager.setInput(lot.getSubProcessUnit());
			tableViewerManager.addICheckChangedListener(new ICheckChangedListener() {
				@Override
				public void checkChanged(List<Object> eventObject, boolean checked) {
					
					if (checked) {
						boolean flag = true;
						if (comboReworkProcedureMap.get(tableViewerManager).getText() == null || "".equals(comboReworkProcedureMap.get(tableViewerManager).getText().trim())){
							UI.showWarning(Message.getString("wip.lot_select_reworkProcedure"));
							flag = false;
						} else if(comboReworkCodeMap.get(tableViewerManager).getText() == null || "".equals(comboReworkCodeMap.get(tableViewerManager).getText().trim())){
							UI.showWarning(Message.getString("wip.lot_select_reworkCode"));
							flag = false;
						}
							
						for (Object object : eventObject) {
							if (flag) {
								((ComponentUnit)object).setActionCode(comboReworkCodeMap.get(tableViewerManager).getText());
								((ComponentUnit)object).setReworkTransition(comboReworkProcedureMap.get(tableViewerManager).getText());
							} else {
								tableViewerManager.unCheckObject(object);
								((ComponentUnit)object).setActionCode("");
								((ComponentUnit)object).setReworkTransition("");
							}
						}
					} else {
						for (Object object : eventObject) {
							((ComponentUnit)object).setActionCode("");
							((ComponentUnit)object).setReworkTransition("");
						}
					}
					
					for (Object object : eventObject) {
						if (scrapUnits.contains(object)) {
							tableViewerManager.unCheckObject(object);
							((ComponentUnit)object).setActionCode("");
							((ComponentUnit)object).setReworkTransition("");
						}
					}
				}
			});
			
			tableViewerManager.refresh();
			checkBoxTableMap.put(tableViewerManager, lot);
			
			Composite reworkComp = toolkit.createComposite(composite, SWT.NULL);
			reworkComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			GridLayout gl = new GridLayout(5, false);
			reworkComp.setLayout(gl);
			
			GridData cGd = new GridData(GridData.FILL_BOTH);
			ADManager entityManager = Framework.getService(ADManager.class);
			List<ADRefTable> adRefTables = entityManager.getEntityList(Env.getOrgRrn(), 
					ADRefTable.class, 1, "name = 'WIPTrackOutReworkTransition'", "");
			
			for (ADRefTable field : adRefTables) {
				this.adRefTable = field;
			}
			
			ADTable refAdTable = adManager.getADTable(adRefTable.getTableRrn());
			adRefTable.setAdTable(refAdTable);
			ListTableManager listTableManager= new ListTableManager(refAdTable);
			
			listTableManager.setInput(getFieldList(lot));
			toolkit.createLabel(reworkComp, Message.getString("wip.trackout.rework.procedure"), SWT.READ_ONLY);
			XCombo comboReworkProcedure = new XCombo(reworkComp, listTableManager, adRefTable.getKeyField(), adRefTable.getTextField(), SWT.READ_ONLY, false);
			comboReworkProcedure.setLayoutData(cGd);
			comboReworkProcedure.select(0);
			comboReworkProcedureMap.put(tableViewerManager, comboReworkProcedure);

			toolkit.createLabel(reworkComp, Message.getString("wip.trackout_reworkcode"), SWT.READ_ONLY);
			XCombo comboReworkCode = RCPUtil.getUserRefListCombo(reworkComp, getReworkCode(), Env.getOrgRrn());
			comboReworkCode.select(0);
			comboReworkCode.setEditable(false);
			cGd.horizontalSpan = 4;
			comboReworkCode.setLayoutData(cGd);
			
			toolkit.createLabel(reworkComp, Message.getString("wip.comment"), SWT.NULL);
			Text commentText = toolkit.createText(reworkComp,"", SWT.BORDER);
			commentText.setLayoutData(cGd);
			comboReworkCodeMap.put(tableViewerManager, comboReworkCode);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

    protected String getReworkCode() {
		try {
			if (step != null && step.getReworkCodeSrc() != null 
					&& step.getReworkCodeSrc().trim().length() > 0) {
				return step.getReworkCodeSrc();
			} 
		} catch (Exception e) {
		}
		return "ReworkCode";
	}
	
	@Override
	public void refresh() {
		if (form != null && !form.isDisposed()) {
			form.dispose();
			form = null;
		}
		createConent();
	}

	@Override
	public void setValue(Object value) {
	}

	@Override
	public Object getValue() {
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
	}
	
	public List<Lot> getObjects() {
		return objects;
	}
	
	public void setObjects(List<Lot> objects) {
		this.objects = objects;
	}
	
	public void setStep(Step step) {
		this.step = step;
	}
	
	public List<LotAction> getReworkLotActions() {
		PrdManager prdManager;
		List<LotAction> reworkLotActions = new ArrayList<LotAction>();
		try {
			prdManager = Framework.getService(PrdManager.class);
			if (ComponentUnit.getUnitType().equals(objects.get(0).getSubUnitType())) {
				for (CheckBoxTableViewerManager manager : checkBoxTableMap.keySet()) {
					List<ProcessUnit> reworkUnits = new ArrayList<ProcessUnit>();
					List<Object> objs = manager.getCheckedObject();
					for (Object obj : objs) {
						ProcessUnit unit = (ProcessUnit)obj;
						// unit.setReworkTransition(comboReworkProcedure.getText());
						unit.setEquipmentId(checkBoxTableMap.get(manager).getEquipmentId());
						reworkUnits.add(unit);
					}
					Map<String, List<ProcessUnit>> reworkActionMap = new HashMap<String, List<ProcessUnit>>();
					for (ProcessUnit reworkUnit : reworkUnits) {
						ComponentUnit componentUnit = (ComponentUnit)reworkUnit;
						// componentUnit.setActionCode(reworkComp.getReworkAction().getActionCode());
						if (reworkActionMap.containsKey(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition())) {
							List<ProcessUnit> list = reworkActionMap.get(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition());
							list.add(componentUnit);
							reworkActionMap.put(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition(), list);
						} else {
							List<ProcessUnit> list = new ArrayList<ProcessUnit>();
							list.add(componentUnit);
							reworkActionMap.put(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition(), list);
						}
					}	
					
					for (String key : reworkActionMap.keySet()) {
						LotAction lotAction = new LotAction();				
						lotAction.setActionType(LotAction.ACTIONTYPE_REWORK);
						lotAction.setActionCode(key.split(";")[0]);		
						lotAction.setReworkTransition(key.split(";")[1]);
						
						List<ProcessUnit> processUnits = reworkActionMap.get(key);
						String equipmentId = processUnits.get(0).getEquipmentId();
						if (StringUtil.isEmpty(equipmentId)) {
							equipmentId = checkBoxTableMap.get(manager).getEquipmentId();
						}
						lotAction.setActionUnits(processUnits);
						lotAction.setEquipmentId(equipmentId);
						
						lotAction.setLotRrn(checkBoxTableMap.get(manager).getObjectRrn());
						
						// 判断所选择的返工是否动态返工
						if (!StringUtil.isEmpty(lotAction.getReworkTransition())) {
							StepState state = prdManager.getCurrentStepState(checkBoxTableMap.get(manager).getProcessInstanceRrn(), false);
							Map<String, Transition> transMap = state.getLeavingTransitionsMap();
							TransitionRework transitionRework = (TransitionRework) transMap.get(lotAction.getReworkTransition());
							ReworkState reworkState = (ReworkState) transitionRework.getTo();
							// 是否动态返工
							lotAction.setReworkProcedureByActionCode(reworkState.getIsDynamic());
						}
						reworkLotActions.add(lotAction);
					}
				}
			} else if (QtyUnit.getUnitType().equals(objects.get(0).getSubUnitType())) {
				for (TrackOutReworkQtyTable tableManager : trackOutReworkQtyTableMap.keySet()) {
					List<ProcessUnit> reworkUnits = new ArrayList<ProcessUnit>();
					List<Object> qtyUnits = (List<Object>) tableManager.getInput();
					if (qtyUnits == null){
					} else {
						for (Object qtyObj : qtyUnits) {
							QtyUnit qtyUnit = (QtyUnit) qtyObj;
							if (qtyUnit.getMainQty() != null) {
								qtyUnit.setEquipmentId(trackOutReworkQtyTableMap.get(tableManager).getEquipmentId());
								reworkUnits.add(qtyUnit);
							}
						}
					}
					//按reworkTransition分类
					Map<String, List<ProcessUnit>> reworkActionMap = new HashMap<String, List<ProcessUnit>>();
					
					for (ProcessUnit reworkUnit : reworkUnits) {
						QtyUnit qtyUnit = (QtyUnit)reworkUnit;
						String actionKey = qtyUnit.getActionCode() + ";" + (qtyUnit.getReworkTransition() == null ? "" : qtyUnit.getReworkTransition());
						
						if (reworkActionMap.containsKey(actionKey)) {
							List<ProcessUnit> list = reworkActionMap.get(actionKey);
							list.add(qtyUnit);
							reworkActionMap.put(actionKey, list);
						} else {
							List<ProcessUnit> list = new ArrayList<ProcessUnit>();
							list.add(qtyUnit);
							reworkActionMap.put(actionKey, list);
						}
					}	
					
					for (String key : reworkActionMap.keySet()) {
						LotAction lotAction = new LotAction();				
						lotAction.setActionType(LotAction.ACTIONTYPE_REWORK);
						
						String[] actionArray = key.split(";");
						lotAction.setActionCode(actionArray[0]);
						if (actionArray.length > 1) {
							lotAction.setReworkTransition(actionArray[1]);
						} 
						
						List<ProcessUnit> processUnits = reworkActionMap.get(key);
						String equipmentId = processUnits.get(0).getEquipmentId();
						if (StringUtil.isEmpty(equipmentId)) {
							equipmentId = trackOutReworkQtyTableMap.get(tableManager).getEquipmentId();
						}
						lotAction.setActionUnits(processUnits);
						lotAction.setEquipmentId(equipmentId);
						
						lotAction.setLotRrn(trackOutReworkQtyTableMap.get(tableManager).getObjectRrn());
						
						// 判断所选择的返工是否动态返工
						if (!StringUtil.isEmpty(lotAction.getReworkTransition())) {
							StepState state = prdManager.getCurrentStepState(trackOutReworkQtyTableMap.get(tableManager).getProcessInstanceRrn(), false);
							Map<String, Transition> transMap = state.getLeavingTransitionsMap();
							TransitionRework transitionRework = (TransitionRework) transMap.get(lotAction.getReworkTransition());
							ReworkState reworkState = (ReworkState) transitionRework.getTo();
							// 是否动态返工
							lotAction.setReworkProcedureByActionCode(reworkState.getIsDynamic());
						} 
						reworkLotActions.add(lotAction);
					}			
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return reworkLotActions;
	}
	
	public void setContext(TrackOutContext context) {
		this.context = context;
	}
	
	public boolean validate() {
		for (XCombo unit : comboReworkCodeMap.values()) {
			IMessageManager mmng = mform.getMessageManager();
			mmng.removeAllMessages();
			boolean validateFlag = true;
			boolean sourceIsNull = GenericValidator.isBlankOrNull(unit.getText());
			if(sourceIsNull){
				mmng.addMessage(HEADER_REWORK_CODE, 
						String.format(Message.getString("common.ismandatry"), HEADER_REWORK_CODE), null, IMessageProvider.ERROR, unit);
				validateFlag = false;
			}
			return validateFlag;
		}
		return true;
	}
	
	private class TrackOutReworkComponentTable extends CheckBoxTableViewerManager {
		public TrackOutReworkComponentTable(ADTable adTable) {
			super(adTable);
			registerAdapterFactory();
		}
		
		public void registerAdapterFactory() {
			setAdapterFactory(createAdapterFactory());
		}

		public ItemAdapterFactory createAdapterFactory() {
			ItemAdapterFactory factory = new ItemAdapterFactory();
			factory.registerAdapter(Object.class, new FilterComponentUnitItemAdapter());
			return factory;
		}
	}
	
	public List<ADBase> getFieldList(Lot lot) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			StepState state = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
			List<Transition> reworkTransitions = prdManager.getReworkTransitions(state, true);
			return (List)reworkTransitions;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	private class FilterComponentUnitItemAdapter extends ListItemAdapter<ComponentUnit> {
		@Override
		public Color getBackground(Object element, String id) {
			ComponentUnit unit = (ComponentUnit) element;
			if (context.getInContent().getActions() != null) {			
				List<LotAction> scrapLotActions = new ArrayList<LotAction>();
				for (LotAction lotAction : context.getInContent().getActions()) {
					if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
						scrapLotActions.add(lotAction);
					}
				}
				for (LotAction scrapLotAction : scrapLotActions) {
					if (unit.getParentUnitRrn().equals(scrapLotAction.getLotRrn())) {
						List<ProcessUnit> comps = scrapLotAction.getActionUnits();
						if (comps.contains(unit)) {
							if (!scrapUnits.contains(unit)) {
								scrapUnits.add(unit);
							}
							return (Display.getCurrent().getSystemColor(SWT.COLOR_GRAY));
						}
					}
				}
			}
			return super.getBackground(element, id);
		}
	}

}
