package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.RASColor;
import com.glory.mes.wip.custom.depend.EquipmentLed;

public class ByStepEquipmentLedCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(ByStepEquipmentLedCustomComposite.class);
	
	public FormToolkit toolkit;
	
	public ScrolledComposite scrolledComposite;
	public Composite boardComposite;
	
	public List<Equipment> eqps = new ArrayList<Equipment>();
	public Map<String, EquipmentLed> eqpLedMap = new HashMap<String, EquipmentLed>();
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {	
		parent.setBackgroundMode(SWT.INHERIT_FORCE);
		this.toolkit = toolkit;
		
		Composite body = toolkit.createComposite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.grabExcessHorizontalSpace = true;
		body.setLayoutData(gd);
		GridLayout gl = new GridLayout(2, false);
		gl.horizontalSpacing = 30;
		gl.marginWidth = 0;
		gl.marginLeft = 5;
		gl.marginRight = 0;
		body.setLayout(gl);

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		
		scrolledComposite = new ScrolledComposite(body, SWT.BORDER | SWT.V_SCROLL);
		scrolledComposite.setLayout(layout);
		scrolledComposite.setLayoutData(gd);
		
		boardComposite = toolkit.createComposite(scrolledComposite, SWT.NONE);
		boardComposite.setLayout(new GridLayout(10, false));
		boardComposite.setLayoutData(gd);
		
		Composite stateComposite = toolkit.createComposite(body, SWT.NONE);
		gl = new GridLayout(2, false);
		gl.horizontalSpacing = 10;
		stateComposite.setLayout(gl);
		gd = new GridData(GridData.FILL_BOTH);
		gd.grabExcessHorizontalSpace = false;
		stateComposite.setLayoutData(gd);

		GridData stateColorGridData = new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1);
		stateColorGridData.minimumHeight = 30;
		stateColorGridData.minimumWidth = 40;
		try {
			for (String state : RASColor.getStates()) {
				Label lblState = toolkit.createLabel(stateComposite, state, SWT.NONE);
				lblState.setAlignment(SWT.LEFT);
				Label lblStateColor = toolkit.createLabel(stateComposite, " ", SWT.NONE);
				lblStateColor.setLayoutData(stateColorGridData);
				lblStateColor.setBackground(RASColor.getColor(state));
			}
		} catch (Exception e) {
			logger.error("EquipmentStatusAdapter : getColor", e);
		}
		return body;
	}
	
	/**
	 * 重新生成设备面板
	 */
	@Override
	public void refresh() {
		
	}
	
	/**
	 * 刷新设备状态,此时不重新生成设备面板,只改变设备当前的状态
	 */
	public void refreshEquipmentState() {
		for (Equipment eqp : eqps) {
			EquipmentLed eqpLed = eqpLedMap.get(eqp.getEquipmentId());
			eqpLed.stateChanged(eqp);
		}
	}
	
	/**
	 * 重新生成设备面板
	 */
	public void refreshEquipmentBoard() {
		if (boardComposite != null) {
			eqpLedMap.clear();
			boardComposite.dispose();
		}
		boardComposite = toolkit.createComposite(scrolledComposite, SWT.NONE);
		GridLayout gl = new GridLayout(6, true);
		gl.horizontalSpacing = 10;
		gl.marginWidth = 0;
		gl.marginLeft = 10;
		gl.marginRight = 0;
		boardComposite.setLayout(gl);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.grabExcessHorizontalSpace = true;
		gd.heightHint = 30;
		boardComposite.setLayoutData(gd);
		
		for (Equipment eqp : eqps) {
			EquipmentLed eqpLed = new EquipmentLed(boardComposite, eqp, this);
			eqpLedMap.put(eqp.getEquipmentId(), eqpLed);
		}
		
		scrolledComposite.setContent(boardComposite);
		scrolledComposite.setExpandHorizontal(true);
		scrolledComposite.setExpandVertical(true);
		scrolledComposite.setMinSize(boardComposite.computeSize(SWT.DEFAULT, SWT.DEFAULT));
	}
	
	
	/*
	 * 把Active的工步所对应的设备取出来,
	 * 虽然这样可能会导致在非Active状态的工步的设备没有显示(出现这种问题的几率很小)
	 * 但是为了总体设计及性能考虑,忽略此问题
	 */
	public void stepChanged(Step newStep) {
		try {
			if (newStep != null && newStep.getCapability() != null) {				
				RASManager rasManager = Framework.getService(RASManager.class);
				eqps = rasManager.getAvailableEquipments(newStep.getCapability(), Env.getSessionContext());
			} else {
				eqps.clear();
			}
			refreshEquipmentBoard();
			refreshEquipmentState();
		} catch (Exception e) {
			logger.error("EquipmentBoardField : createEquipmentBoard", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void equipmentChanged(Equipment newEquipment) {
		for (EquipmentLed eqpLed : eqpLedMap.values()) {
			if (eqpLed.isSelected()) {
				eqpLed.setSelected(false);
			}
		}
		if (newEquipment != null) {
			if (eqpLedMap.containsKey(newEquipment.getEquipmentId())) {
				EquipmentLed eqpLed = eqpLedMap.get(newEquipment.getEquipmentId());
				eqpLed.setSelected(true);
				for (Equipment eqp : eqps) {
					if (eqp.getEquipmentId().equals(newEquipment.getEquipmentId())) {
						eqp = newEquipment;
						break;
					}
				}
				eqpLed.setEquipment(newEquipment);
				eqpLed.stateChanged(newEquipment);
			}
		}
	}	
	
	@Override
	public void setValue(Object value) {}

	@Override
	public Object getValue() {
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {}

}
