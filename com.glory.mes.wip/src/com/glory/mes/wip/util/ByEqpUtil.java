package com.glory.mes.wip.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.EquipmentLine;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.custom.depend.MsgConsoleView;
import com.glory.mes.wip.lot.run.byeqp.sorting.SortingJobAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class ByEqpUtil {
	
	private static final Logger logger = Logger.getLogger(ByEqpUtil.class);
	
	public static List<Lot> getRtdLots(Equipment equipment) throws Exception {
		return getRtdLots(equipment, true);
	}
	/**
	 * 获取Rtd批次列表
	 * @return
	 * @throws Exception
	 */
	public static List<Lot> getRtdLots(Equipment equipment, boolean showError) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		ADManager adManager = Framework.getService(ADManager.class);
		List<Lot> waittingLots = Lists.newArrayList();
		List<String> lotIds = null;
		try {
			String dispatchType = equipment.getIsBatch() ? "BATCH" : "LOT";
			List<ADRefList> refLists = adManager.getADRefList(0L, "ByEqpAction");
			if (CollectionUtils.isNotEmpty(refLists)) {
				for (ADRefList refList : refLists) {
					if (SortingJobAction.ACTION_NAME.equals(refList.getText())) {
						dispatchType = "SORTING";
						break;
					}
				}
			}
			
			lotIds = lotManager.getRtdQueue(equipment.getEquipmentId(), dispatchType, Env.getSessionContext());
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : getRtdLots() ", e);
			if (showError) {
				ByEqpConsole console = (ByEqpConsole) MsgConsoleView.getInstance();
				if (console != null) {
					console.error(Message.getString("wip.byeqp_disp_rule_not_found"));
				}
			}
			
			// 报错（一般是规则没有）就显示waittingLots
			waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), equipment.getObjectRrn(),
					LotStateMachine.STATE_WAIT, Env.getSessionContext());
		}
		
		if (CollectionUtils.isNotEmpty(lotIds)) {
			// 只能在Waiting的批次中取，Prepare排除
			waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), equipment.getObjectRrn(),
					LotStateMachine.STATE_WAIT, Env.getSessionContext());
			
			Map<String, Lot> waittingLotMap = waittingLots.stream().collect(Collectors.toMap(Lot::getLotId, l -> l));
			List<Lot> rtdLots = Lists.newArrayList();
			for (String lotId : lotIds) {
				if (waittingLotMap.containsKey(lotId)) {
					rtdLots.add(waittingLotMap.get(lotId));
				}
			}
			
			waittingLots = rtdLots;
		}
		
		return waittingLots;
	}
	
	public static List<Lot> filterWaitingLots(List<Lot> lots, Equipment currentEqp) throws Exception {
		List<Lot> waitingLots = new ArrayList<Lot>();
		if (currentEqp != null && currentEqp.getObjectRrn() != null) {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			boolean isUseLine = MesCfMod.isUseLine(Env.getOrgRrn(), sysParamManager);

			List<String> lines = new ArrayList<String>();
			if (isUseLine) {

				ADManager manager = Framework.getService(ADManager.class);
				String whereClause = "equipmentRrn = " + currentEqp.getObjectRrn();
				List<EquipmentLine> lineEqps = manager.getEntityList(Env.getOrgRrn(), EquipmentLine.class,
						Integer.MAX_VALUE, whereClause, "");
				for (EquipmentLine lineEqp : lineEqps) {
					lines.add(lineEqp.getLineId());
				}

			}

			for (Lot lot : lots) {
				if (isUseLine) {
					if (!StringUtil.isEmpty(lot.getLineId()) && !lines.contains(lot.getLineId())) {
						// 如果批次上有线别并且线别不在设备所属的线别中
						continue;
					}
				}
				
				if ((LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_DISP.equals(lot.getState()))
						&& !Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
					if (lot.getMessageList() != null && lot.getMessageList().size() > 0) {
						ByEqpConsole console = (ByEqpConsole) MsgConsoleView.getInstance();
						for (String msg : lot.getMessageList()) {
							console.warning(Message.formatString(msg));
						}
					}
					// 对于受限制的批次不处理
					if (!lot.getConstraintFlag()) {
						waitingLots.add(lot);
					}
				}
			}
			
//			boolean isByEqpQueryPpid = MesCfMod.isByEqpQueryPpid(Env.getOrgRrn(), sysParamManager);
//			if (isByEqpQueryPpid && CollectionUtils.isNotEmpty(waitingLots)) {
//				StopWatch stopWatch = new StopWatch();
//				stopWatch.start();
//				LotManager lotManager = Framework.getService(LotManager.class);
//				waitingLots = lotManager.calculatePPID(waitingLots, currentEqp, true, Env.getSessionContext());
//				stopWatch.split();
//				logger.info("calculatePPID Time :" + stopWatch.getSplitTime() + " Milliseconds");
//				stopWatch.stop();
//			}
		}
		return waitingLots;
	}
	
	/**
	 * 按设备、区域作业在控制台提示PPID及Reticle
	 * @param lot
	 * @param equipment
	 * @param console
	 * @throws Exception
	 */
	public static void noticeEquipmentRecipeAndReticle(Lot lot, Equipment equipment, ByEqpConsole console) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		if (equipment.getIsBatch() && !StringUtil.isEmpty(lot.getBatchId())) {
			List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			List<Lot> batchLots = lotManager.getEquipmentRecipeByBatch(equipment, lots, false, false, Env.getSessionContext());
			if (CollectionUtils.isNotEmpty(batchLots)) {
				Lot batchLot = batchLots.get(0);
				
				if (StringUtil.isEmpty(batchLot.getEquipmentRecipe())) {
					console.info("PPID: Not Found !");
				} else {
					console.info("PPID: " + batchLot.getEquipmentRecipe());
				}
			} else {
				console.info("PPID: Not Found !");
			}
			
		} else {
			Map<String, Boolean> unitAvailables = null;
			if (equipment.getIsMultiSubEqp()) {
    			List<Equipment> subEquipments = lotManager.getAvailableSubEquipments(lot, equipment.getEquipmentId(), Env.getSessionContext());
    			unitAvailables  = subEquipments.stream().collect(Collectors.toMap(Equipment :: getEquipmentId, Equipment :: getIsAvailable));
    		}
			
			String[] recipes = lotManager.getLotRecipesFromRms(equipment.getEquipmentId(), unitAvailables, null, lot, lot.getMainQty(), true, false);
			if (recipes != null && recipes.length > 0) {
				String recipe = recipes[1];
				if (StringUtil.isEmpty(recipe)) {
					console.info("PPID: Not Found !");
				} else {
					console.info("PPID: " + recipe);
				}
			} else {
				console.info("PPID: Not Found !");
			}
		}
		
//		RASManager rasManager = Framework.getService(RASManager.class);
//		boolean isPhoto = rasManager.isPhotoEqp(Env.getOrgRrn(), equipment.getEqpType());
//		
//		if (isPhoto) {
			String maskStr[] = lotManager.getLotEquipmentReticle(equipment.getEquipmentId(), lot, true, false);
			if (maskStr != null && maskStr.length > 0) {	
				String mask = maskStr[0];
				if (StringUtil.isEmpty(mask)) {
					console.info("Reticle ID: Not Found !");
				} else {
					console.info("Reticle ID: " + mask);
				}
			} else {
				console.info("Reticle ID: Not Found !");
			}
//		}
	}
	
	public static Lot getEquipmentRecipeAndReticle(List<Lot> lots, Equipment equipment) throws Exception {
		return getEquipmentRecipeAndReticle(lots, true, true, false, equipment);
	}
	
	/**
	 * 在Track In或者是Prepare等情况下，查询所选择批次的PPID和光刻板
	 * @param lots
	 * @param queryPPID
	 * @param queryReticle
	 * @param isThrowException
	 * @param equipment
	 * 
	 * @return
	 * @throws Exception
	 */
	public static Lot getEquipmentRecipeAndReticle(List<Lot> lots, boolean queryPPID, boolean queryReticle, boolean isThrowException, Equipment equipment) throws Exception {
		Lot formLot = new Lot();
		
		if (equipment == null) {
			return formLot;
		}
		
		LotManager lotManager = Framework.getService(LotManager.class);
		RASManager rasManager = Framework.getService(RASManager.class);
		if (queryPPID) {
			if (equipment.getIsBatch()) {
				List<Lot> batchLots = lotManager.getEquipmentRecipeByBatch(equipment, lots, false, isThrowException, Env.getSessionContext());
				if (CollectionUtils.isNotEmpty(batchLots)) {
					Lot batchLot = batchLots.get(0);
					formLot.setRecipeName(batchLot.getRecipeName());
					formLot.setEquipmentRecipe(batchLot.getEquipmentRecipe());
					formLot.setEquipmentRecipeCondition(batchLot.getEquipmentRecipeCondition());
				} else {
					formLot.setRecipeName("");
					formLot.setEquipmentRecipe("");
					formLot.setEquipmentRecipeCondition("");
				}
				
			} else {
				Map<String, Boolean> unitAvailables = null;
				if (equipment.getIsMultiSubEqp()) {
	    			List<Equipment> subEquipments = lotManager.getAvailableSubEquipments(lots.get(0), equipment.getEquipmentId(), Env.getSessionContext());
	    			unitAvailables  = subEquipments.stream().collect(Collectors.toMap(Equipment :: getEquipmentId, Equipment :: getIsAvailable));
	    		}
				
				String[] recipes = lotManager.getLotRecipesFromRms(equipment.getEquipmentId(), unitAvailables, null, lots.get(0), lots.get(0).getMainQty(), true, false);
				if (recipes != null && recipes.length > 0) {
					formLot.setRecipeName(recipes[0]);
					formLot.setEquipmentRecipe(recipes[1]);
					formLot.setEquipmentRecipeCondition(recipes[2]);
				} else {
					formLot.setRecipeName("");
					formLot.setEquipmentRecipe("");
					formLot.setEquipmentRecipeCondition("");
				}
			}
		}
		
		if (queryReticle) {
//			boolean isPhoto = rasManager.isPhotoEqp(Env.getOrgRrn(), equipment.getEqpType());
//			if (isPhoto) {
				String maskStr[] = lotManager.getLotEquipmentReticle(equipment.getEquipmentId(), lots.get(0), true, false);
				if (maskStr != null && maskStr.length > 0) {	
					formLot.setMask(maskStr[2]);
					formLot.setEquipmentMask(maskStr[0]);
				} else {
					formLot.setMask("");
					formLot.setEquipmentMask("");
				}						
//			}
		}
		
		//Dummy批次Full-Auto阶段不要Recipe，这里做个标记
		SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
		if (MesCfMod.isDummyNotUseEquipmentRecipe(Env.getOrgRrn(), sysParamManager)) {
			String isNotCheckRecipe = "N";
			for (Lot lot : lots) {
				Boolean isDummy = lotManager.isDummy(Env.getOrgRrn(), lot.getLotType());
				if (isDummy) {
//					Boolean isFurnaceEqp = rasManager.isFurnaceEqp(Env.getOrgRrn(), equipment.getEqpType());
//					if (isFurnaceEqp) {
						isNotCheckRecipe = "Y";
//					}
					break;
				}
			}
			formLot.setAttribute1(isNotCheckRecipe);
		}
		return formLot;
	}
}
