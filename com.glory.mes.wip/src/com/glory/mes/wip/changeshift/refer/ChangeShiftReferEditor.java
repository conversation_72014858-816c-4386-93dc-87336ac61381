package com.glory.mes.wip.changeshift.refer;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.viewers.StructuredSelection;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ChangeShift;
import com.glory.mes.wip.model.ChangeShiftLine;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeShiftReferEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.refer.ChangeShiftReferEditorGlc";

	private static final String FIELD_CHANGESHIFTLIST = "changeShiftList";
	private static final String FIELD_CHANGESHIFTGLC = "changeShiftGlc";
	private static final String FIELD_CHANGESHIFTINFO = "changeShiftInfo";
	private static final String FIELD_CHANGESHIFTLINECOMMON = "changeShiftLineCommon";
	private static final String FIELD_CHANGESHIFTLINEWO = "changeShiftLineWo";
	private static final String FIELD_CHANGESHIFTLINELOT = "changeShiftLineLot";
	private static final String FIELD_CHANGESHIFTLINEEQP = "changeShiftLineEqp";

	private static final String BUTTON_SEARCH = "search";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_APPROVE = "approve";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_ENTITYREFRESH = "entityRefresh";

	protected ListTableManagerField changeShiftListField;
	protected GlcFormField changeShiftGlcField;
	protected EntityFormField changeShiftInfoField;
	protected TableEditorField changeShiftLineCommonField;
	protected TableEditorField changeShiftLineWoField;
	protected TableEditorField changeShiftLineLotField;
	protected TableEditorField changeShiftLineEqpField;
	
	public static String SUCCESSION_TYPE_LOT = "LOT";
	public static String SUCCESSION_TYPE_EQP = "EQP";
	public static String SUCCESSION_TYPE_WO = "WO";
	public static String SUCCESSION_TYPE_COMMENT = "COMMENT";

	protected ChangeShiftRefQueryDialog queryDialog;
	protected String whereClause;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		changeShiftListField = form.getFieldByControlId(FIELD_CHANGESHIFTLIST, ListTableManagerField.class);
		changeShiftGlcField = form.getFieldByControlId(FIELD_CHANGESHIFTGLC, GlcFormField.class);
		changeShiftInfoField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTINFO, EntityFormField.class);
		changeShiftLineCommonField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINECOMMON, TableEditorField.class);
		changeShiftLineWoField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINEWO, TableEditorField.class);
		changeShiftLineLotField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINELOT, TableEditorField.class);
		changeShiftLineEqpField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINEEQP, TableEditorField.class);

		subscribeAndExecute(eventBroker, changeShiftListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::changeShiftListSelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SEARCH), this::searchAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_APPROVE), this::approveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ENTITYREFRESH), this::entityRefreshAdapter);
		
		changeShiftInfoField.setValue(new ChangeShift());
		changeShiftInfoField.refresh();
	}

	private void searchAdapter(Object object) {
		try {
			if (queryDialog != null) {
				queryDialog.setVisible(true);
			} else {
				queryDialog =  new ChangeShiftRefQueryDialog(UI.getActiveShell(), changeShiftListField.getListTableManager(), null, this);
				queryDialog.open();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void refreshAdapter(Object object) {
		try {
			refresh(this.whereClause);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void refresh(String whereClause) {
		try {
			if (StringUtil.isEmpty(whereClause)) {
				whereClause = " docStatus = 'CREATED'";
			} else {
				this.whereClause = whereClause;
			}
			ADManager adManager = Framework.getService(ADManager.class);
			List<ChangeShift> changeShifts = adManager.getEntityList(Env.getOrgRrn(), ChangeShift.class, Env.getMaxResult(), whereClause, "");
			changeShiftListField.getListTableManager().setInput(changeShifts);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void newAdapter(Object object) {
		try {
			changeShiftInfoField.getFormControl().removeAllMessages();
			changeShiftInfoField.setValue(new ChangeShift());
			changeShiftInfoField.refresh();
			changeShiftLineCommonField.setValue(Lists.newArrayList());
			changeShiftLineCommonField.refresh();
			changeShiftLineWoField.setValue(Lists.newArrayList());
			changeShiftLineWoField.refresh();
			changeShiftLineLotField.setValue(Lists.newArrayList());
			changeShiftLineLotField.refresh();
			changeShiftLineEqpField.setValue(Lists.newArrayList());
			changeShiftLineEqpField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void saveAdapter(Object object) {
		try {
			if (changeShiftInfoField.validate()) {
				ChangeShift succession = (ChangeShift) changeShiftInfoField.getValue();
				succession.setOrgRrn(Env.getOrgRrn());
				succession.setDocStatus(Documentation.STATUS_CREATED);
				List<ChangeShiftLine> successionLines = new ArrayList<ChangeShiftLine>();
				if (CollectionUtils.isNotEmpty(changeShiftLineCommonField.getTableManager().getInput())) {
					List<? extends Object > commonLines = changeShiftLineCommonField.getTableManager().getInput();
					commonLines.stream().forEach(line -> {
						ChangeShiftLine successionLine = (ChangeShiftLine) line;
						successionLine.setObjectRrn(null);
						successionLine.setType(SUCCESSION_TYPE_COMMENT);
						successionLines.add(successionLine);
					});
				}
				if (CollectionUtils.isNotEmpty(changeShiftLineWoField.getTableManager().getInput())) {
					List<? extends Object > commonLines = changeShiftLineWoField.getTableManager().getInput();
					commonLines.stream().forEach(line -> {
						ChangeShiftLine successionLine = (ChangeShiftLine) line;
						successionLine.setObjectRrn(null);
						successionLine.setType(SUCCESSION_TYPE_WO);
						successionLines.add(successionLine);
					});
				}
				if (CollectionUtils.isNotEmpty(changeShiftLineLotField.getTableManager().getInput())) {
					List<? extends Object > commonLines = changeShiftLineLotField.getTableManager().getInput();
					commonLines.stream().forEach(line -> {
						ChangeShiftLine successionLine = (ChangeShiftLine) line;
						successionLine.setObjectRrn(null);
						successionLine.setType(SUCCESSION_TYPE_LOT);
						successionLines.add(successionLine);
					});
				}
				if (CollectionUtils.isNotEmpty(changeShiftLineEqpField.getTableManager().getInput())) {
					List<? extends Object > commonLines = changeShiftLineEqpField.getTableManager().getInput();
					commonLines.stream().forEach(line -> {
						ChangeShiftLine successionLine = (ChangeShiftLine) line;
						successionLine.setObjectRrn(null);
						successionLine.setType(SUCCESSION_TYPE_EQP);
						successionLines.add(successionLine);
					});
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				ChangeShift newObject = lotManager.changeShift(succession, successionLines, Documentation.STATUS_CREATED,
						Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				if(succession.getObjectRrn() == null) {
					refreshAdapter(object);
				}
				changeShiftListField.getListTableManager().setSelection(new StructuredSelection(new Object[] {newObject}));
				entityRefresh(newObject);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void approveAdapter(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftInfoField.getValue();
			if (succession == null || succession.getObjectRrn() == null) {
				changeShiftInfoField.getFormControl().removeAllMessages();
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			succession.setDocStatus(Documentation.STATUS_APPROVED);
			lotManager.changeShift(succession, null, Documentation.STATUS_APPROVED, Env.getSessionContext());
			UI.showInfo(Message.getString("alm.add_data_comment_successed"));// 弹出提示框
			newAdapter(object);
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void deleteAdapter(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftInfoField.getValue();
			if (succession != null && succession.getObjectRrn() != null) {
				boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
				if (confirmDelete) {
					ADManager entityManager = Framework.getService(ADManager.class);
					entityManager.deleteEntity(succession, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
					newAdapter(object);
					refreshAdapter(object);
				}
			}
			changeShiftInfoField.getFormControl().removeAllMessages();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void entityRefreshAdapter(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftInfoField.getValue();
			entityRefresh(succession);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void entityRefresh(ChangeShift succession) {
		try {
			if (succession != null && succession.getObjectRrn() != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				succession = (ChangeShift) adManager.getEntity(succession);
				changeShiftInfoField.setValue(succession);
				changeShiftInfoField.refresh();
				setInputTableManager(succession);
			} else {
				changeShiftInfoField.setValue(new ChangeShift());
				changeShiftInfoField.refresh();
				changeShiftLineCommonField.setValue(Lists.newArrayList());
				changeShiftLineCommonField.refresh();
				changeShiftLineWoField.setValue(Lists.newArrayList());
				changeShiftLineWoField.refresh();
				changeShiftLineLotField.setValue(Lists.newArrayList());
				changeShiftLineLotField.refresh();
				changeShiftLineEqpField.setValue(Lists.newArrayList());
				changeShiftLineEqpField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void changeShiftListSelectionChanged(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftListField.getListTableManager().getSelectedObject();
			if (succession != null && succession.getObjectRrn() != null) {
				changeShiftInfoField.getFormControl().removeAllMessages();
				changeShiftInfoField.setValue(succession);
				changeShiftInfoField.refresh();
				setInputTableManager(succession);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void setInputTableManager(ChangeShift succession) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<ChangeShiftLine> successionLines = adManager.getEntityList(Env.getOrgRrn(), ChangeShiftLine.class,
					Env.getMaxResult(), "changeShiftRrn = '" + succession.getObjectRrn() + "'", "objectRrn");
			List<ChangeShiftLine> commonLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_COMMENT.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineCommonField.setValue(commonLines);
			changeShiftLineCommonField.refresh();
			List<ChangeShiftLine> woLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_WO.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineWoField.setValue(woLines);
			changeShiftLineWoField.refresh();
			List<ChangeShiftLine> lotLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_LOT.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineLotField.setValue(lotLines);
			changeShiftLineLotField.refresh();
			List<ChangeShiftLine> eqpLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_EQP.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineEqpField.setValue(eqpLines);
			changeShiftLineEqpField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

}