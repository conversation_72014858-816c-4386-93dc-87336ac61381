package com.glory.mes.wip.changeshift.query;

import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.ChangeShift;
import com.glory.mes.wip.model.ChangeShiftLine;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeShiftQueryDetailDialog extends BaseTitleDialog {

	private static int MIN_DIALOG_WIDTH = 700;
	private static int MIN_DIALOG_HEIGHT = 400;

	protected ManagedForm managedForm;

	private ListTableManager commentTableManager;
	private ListTableManager eqpTableManager;
	private ListTableManager lotTableManager;
	private ListTableManager woTableManager;
	protected static String SUCCESSION_COMMENT = "WIPSuccessionLineComment";
	protected static String SUCCESSION_EQP = "WIPSuccessionLineEqp";
	protected static String SUCCESSION_LOT = "WIPSuccessionLineLot";
	protected static String SUCCESSION_WO = "WIPSuccessionLineWo";
	protected ChangeShift succession;

	public ChangeShiftQueryDetailDialog(ChangeShift succession) {
		super();
		this.succession = succession;
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
		setTitle(Message.getString("wip.change_shift_info"));
		createFormContent(parent);
		return parent;
	}

	protected void createFormContent(Composite composite) {
		composite.setLayout(new GridLayout(1, false));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));

		FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
		ScrolledForm sForm = toolkit.createScrolledForm(composite);
		managedForm = new ManagedForm(toolkit, sForm);
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		Composite body = sForm.getForm().getBody();
		configureBody(body);

		Composite tableComposite = toolkit.createComposite(body, SWT.BORDER);
		tableComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
		GridLayout layoutBody = new GridLayout(1, true);
		tableComposite.setLayout(layoutBody);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = 450;
		tableComposite.setLayoutData(gd);
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable commentADTable = adManager.getADTable(Env.getOrgRrn(), SUCCESSION_COMMENT);
			ADTable eapADTable = adManager.getADTable(Env.getOrgRrn(), SUCCESSION_EQP);
			ADTable lotADTable = adManager.getADTable(Env.getOrgRrn(), SUCCESSION_LOT);
			ADTable woADTable = adManager.getADTable(Env.getOrgRrn(), SUCCESSION_WO);

			Composite topTableComposite = toolkit.createComposite(body, SWT.NONE);
			topTableComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			GridLayout topLayout = new GridLayout(2, true);
			topTableComposite.setLayout(topLayout);
			GridData topGD = new GridData(GridData.FILL_BOTH);
			topTableComposite.setLayoutData(topGD);

			List<ChangeShiftLine> commentLines = adManager.getEntityList(Env.getOrgRrn(), ChangeShiftLine.class,
					Env.getMaxResult(), " type = 'COMMENT' AND changeShiftRrn = '" + succession.getObjectRrn() + "'", "objectRrn");
			if (!commentLines.isEmpty()) {
				Group commentGroup = new Group(tableComposite, SWT.NONE);
				commentGroup.setText(Message.getString("wip.common_info"));
				commentGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
				commentGroup.setLayout(new GridLayout(1, false));
				commentTableManager = new ListTableManager(commentADTable);
				commentTableManager.newViewer(commentGroup);
				commentTableManager.setInput(commentLines);
			}

			List<ChangeShiftLine> woLines = adManager.getEntityList(Env.getOrgRrn(), ChangeShiftLine.class,
					Env.getMaxResult(), " type = 'WO' AND changeShiftRrn = '" + succession.getObjectRrn() + "'", "objectRrn");
			if (!woLines.isEmpty()) {
				Group woGroup = new Group(tableComposite, SWT.NONE);
				woGroup.setText(Message.getString("wip.workorder_info"));
				woGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
				woGroup.setLayout(new GridLayout(1, false));
				woTableManager = new ListTableManager(woADTable);
				woTableManager.newViewer(woGroup);
				woTableManager.setInput(woLines);
			}

			List<ChangeShiftLine> lotLines = adManager.getEntityList(Env.getOrgRrn(), ChangeShiftLine.class,
					Env.getMaxResult(), " type = 'LOT' AND changeShiftRrn = '" + succession.getObjectRrn() + "'", "objectRrn");
			if (!lotLines.isEmpty()) {
				Group lotGroup = new Group(tableComposite, SWT.NONE);
				lotGroup.setText("批次信息");
				lotGroup.setText(Message.getString("wip.lot_info"));
				lotGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
				lotGroup.setLayout(new GridLayout(1, false));
				lotTableManager = new ListTableManager(lotADTable);
				lotTableManager.newViewer(lotGroup);
				lotTableManager.setInput(lotLines);
			}

			List<ChangeShiftLine> eqpLines = adManager.getEntityList(Env.getOrgRrn(), ChangeShiftLine.class,
					Env.getMaxResult(), " type = 'EQP' AND changeShiftRrn = '" + succession.getObjectRrn() + "'", "objectRrn");
			if (!eqpLines.isEmpty()) {
				Group eqpGroup = new Group(tableComposite, SWT.NONE);
				eqpGroup.setText(Message.getString("wip.equipment_info"));
				eqpGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
				eqpGroup.setLayout(new GridLayout(1, false));
				eqpTableManager = new ListTableManager(eapADTable);
				eqpTableManager.newViewer(eqpGroup);
				eqpTableManager.setInput(eqpLines);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID, Message.getString(ExceptionBundle.bundle.CommonCancel()),
				false, UIControlsFactory.BUTTON_GRAY);

		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
	}
}
