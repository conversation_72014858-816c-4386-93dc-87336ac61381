package com.glory.mes.wip.lot.flow;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.exe.Token;
import com.glory.mes.prd.workflow.graph.node.EndState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StartState;
import com.glory.mes.prd.workflow.graph.node.StepState;

/**
 * 用于处理Finish状态下返工时,第一层为Procedure,而导致无法找到正确的路径问题
 */
public class LotProcedureAdapter extends LotItemAdapter {
	
	private static final Logger logger = Logger.getLogger(LotProcedureAdapter.class);
	
	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof Procedure) {
			Procedure procedure = (Procedure)object;
			try {
				if (procedure != null) {
					Object[] nodes = getProcessDefinitionChildern(procedure);
					List<Node> list = new ArrayList<Node>();
					for (Object node : nodes) {
						if (node instanceof StartState) {
							continue;
						} else if (node instanceof EndState) {
							continue;
						} else {
							list.add((Node)node);
						}
					}
					
					Node currentNode = null;
					if (CollectionUtils.isNotEmpty(nodeList)) {
						currentNode = nodeList.get(0);
					}
					if (currentNode instanceof ReworkState) {
						Token currentToken = currentNode.getCurrentToken();
						if (currentToken != null) {
							for (Node node : list) {
								if (node instanceof StepState && 
										node.getObjectRrn().equals(currentToken.getLastNodeRrn())) {
									((StepState)node).setReworkState((ReworkState)currentNode);
									break;
								}
							}
						}
					}
					
					return list.toArray();
				}
	            
	        } catch (Exception e) {
	        	logger.error(e.getMessage(), e);
	        	ExceptionHandlerManager.asyncHandleException(e);
	        }
		} else {
			logger.error("Expect Procedure, but found " + object.toString());
		}
        return EMPTY;
	}
}
