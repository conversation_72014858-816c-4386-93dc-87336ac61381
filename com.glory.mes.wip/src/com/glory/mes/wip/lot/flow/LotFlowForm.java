package com.glory.mes.wip.lot.flow;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.model.Lot;

public class LotFlowForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(LotFlowForm.class);
	
	protected static final String FIELD_ID = "process";
	protected LotMediator lotMediator;
	protected TreeViewerManager treeManager;
	protected LotFlowTreeField field;

	public LotFlowForm(Composite parent, int style, ADTable table, IMessageManager mmng) {
		super(parent, style, table, mmng);
	}
	
	public LotFlowForm(Composite parent, int style, ADTable table,
			LotMediator lotMediator, IMessageManager mmng) {
		super(parent, style, table, mmng);
		this.lotMediator = lotMediator;
		this.mLeftPadding = 0;
		this.mTopPadding = 0;
		this.mRightPadding = 0;
		super.createForm();
	}
	
	public LotFlowForm(Composite parent, int style, ADTable table,
			LotMediator lotMediator, IMessageManager mmng, TreeViewerManager treeManager) {
		super(parent, style, table, mmng);
		this.lotMediator = lotMediator;
		this.treeManager = treeManager;
		this.mLeftPadding = 0;
		this.mTopPadding = 0;
		this.mRightPadding = 0;
		super.createForm();
	}
	
	@Override
    public void createForm(){}

	@Override
	public void addFields() {
		try {
			field = new LotFlowTreeField(FIELD_ID, null, getTreeManager(), this);
			addField(FIELD_ID, field);
		} catch (Exception e) {
			logger.error("LotFlowForm : addFields", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			return true;
		}
		return false;
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			Lot lot = ((Lot) object);
			try {
				field.setValue(lot);
				PrdManager prdManager = Framework.getService(PrdManager.class);
				if (lot!=null&&lot.getObjectRrn()!=null){
					ProcessDefinition pf = prdManager.getProcessInstance(lot.getProcessInstanceRrn()).getProcessDefinition();
				
					List<Node> flowList = prdManager.getProcessFlowList(lot.getProcessInstanceRrn());
					List<ADBase> processes = new ArrayList<ADBase>();
					processes.add(pf);
					
					for (Node node : flowList) {
						//如果存在返工,则需要增加返工前的StepState作为返工的父节点
						if (node instanceof ReworkState) {
							ReworkState reworkState = (ReworkState)node;
							Long nodeRrn = reworkState.getCurrentToken().getLastNodeRrn();
							if (nodeRrn != null) {
								ADManager adManager = Framework.getService(ADManager.class);
								Node stepNode = new Node();
								stepNode.setObjectRrn(nodeRrn);
								stepNode = (Node)adManager.getEntity(stepNode);
								processes.add(stepNode);
							}
						}
						processes.add(node);
					}
					
					field.setFlowList(processes);
				}
			} catch (Exception e) {
				logger.error("NewPartForm : loadFromObject()", e);
			}
			refresh();
		}
	}

	@Override
	public boolean validate() {
		return true;
	}
	
	public LotMediator getLotMediator() {
		return lotMediator;
	}
	
	protected TreeViewerManager getTreeManager() {
		if(treeManager != null) {
			return treeManager;
		} else {
			return new LotFlowTreeManager();
		}
	}
	
	public LotFlowTreeField getField() {
		return field;
	}

}
