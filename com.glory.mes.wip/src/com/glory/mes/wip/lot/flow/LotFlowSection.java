package com.glory.mes.wip.lot.flow;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;
import org.eclipse.ui.forms.widgets.TableWrapData;
import org.eclipse.ui.forms.widgets.TableWrapLayout;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class LotFlowSection extends LotSection {
	
	protected LotFlowForm itemForm;
	protected Label labelPartInfo;
	protected Label state;
	protected LotMediator lotMediator;
	protected TreeViewerManager treeManager;

	protected final static String PARTINFO = Message.getString("common.partinfo");
	
	public LotFlowSection(ADTable table) {
		super(table);
	}
	
	public LotFlowSection(ADTable table, LotMediator lotMediator) {
		super(table);
		this.lotMediator = lotMediator;
	}
	
	public LotFlowSection(ADTable table, LotMediator lotMediator,
			TreeViewerManager treeManager) {
		super(table);
		this.lotMediator = lotMediator;
		this.treeManager = treeManager;
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		this.form = form;
		final FormToolkit toolkit = form.getToolkit();
		section = toolkit.createSection(parent, Section.NO_TITLE);
		section.marginWidth = 0;
		section.marginHeight = 0;
		
		TableWrapLayout layout = new TableWrapLayout();
		layout.topMargin = 0;
		layout.leftMargin = 5;
		layout.rightMargin = 2;
		layout.bottomMargin = 0;
		parent.setLayout(layout);

		section.setLayout(layout);

		TableWrapData td = new TableWrapData(TableWrapData.CENTER,
				TableWrapData.MIDDLE);
		td.grabHorizontal = true;
		td.grabVertical = false;
		section.setLayoutData(td);

		GridData g = new GridData(GridData.FILL_BOTH);

		Composite client = toolkit.createComposite(section);
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		client.setLayout(gridLayout);
		client.setLayoutData(g);

		createSectionTitle(client);
		createSectionContent(client);

		toolkit.paintBordersFor(section);
		section.setClient(client);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(6, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("lot.lot_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		
		txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
		
		labelPartInfo = toolkit.createLabel(top, "");	
		labelPartInfo.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		GridData gLabel = new GridData();
		gLabel.horizontalAlignment = GridData.FILL;
		gLabel.grabExcessHorizontalSpace = true;
		labelPartInfo.setLayoutData(gLabel);
		
		state = toolkit.createLabel(top, "");	
		state.setLayoutData(gLabel);
		state.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		
		txtLot.setTextLimit(32);
		txtLot.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				txtLot.setForeground(SWTResourceCache.getColor("Black"));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					excuteSearch();
					break;
				}
			}
		});
		
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}
			@Override
			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
				tLotId.selectAll();
			}
		});
	}
	
	public void excuteSearch() {
		Lot lot = null;
		String lotId = txtLot.getText();
		if (!isLotIdCaseSensitive()) {
			lotId = lotId.toUpperCase();
		}
		lot = searchLot(lotId);
		if(lot!=null&&lot.getPartId()!=null) {
		    labelPartInfo.setText("Part："+lot.getPartId());
			state.setText("State :" + lot.getCstate());
		} else {
			labelPartInfo.setText("");
			state.setText("");
		}
		txtLot.selectAll();
		if (lot == null) {
			txtLot.setForeground(SWTResourceCache.getColor("Red"));
			initAdObject();
			txtLot.warning();
		} else {
			setAdObject(lot);
			refresh();
			txtLot.focusing();
		}
	}

	@Override
	protected void createSectionContent(Composite client) {
		final IMessageManager mmng = form.getMessageManager();
		itemForm = new LotFlowForm(client, SWT.NONE, table,
				lotMediator, mmng, treeManager);
		itemForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		getDetailForms().add(itemForm);
	}

	@Override
	public void refresh() {
		try {
			Lot lot = (Lot)getAdObject();
			if (lot != null && lot.getObjectRrn() != null) {
				String partname = String.valueOf(lot.getPartId() == null ? "Null" : ((Lot)getAdObject()).getPartId());
				labelPartInfo.setText(PARTINFO+partname);
				String stateName = String.valueOf(lot.getState() == null ? "Null" : ((Lot)getAdObject()).getCstate());
				state.setText("State :" + stateName);
				notitySectionChanged(lot);
			}			
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		super.refresh();
	}
	
	protected void notitySectionChanged(ADBase adBase) {
		if(lotMediator != null || lotMediator.getLotSection() != null) {
			lotMediator.notifySection(adBase);
		}
	}
	
	public LotMediator getLotMediator() {
		return lotMediator;
	}
	
	public LotFlowForm getItemForm() {
		return itemForm;
	}

}
