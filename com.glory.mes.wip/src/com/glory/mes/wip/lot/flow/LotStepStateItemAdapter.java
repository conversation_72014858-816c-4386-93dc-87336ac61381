package com.glory.mes.wip.lot.flow;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import com.glory.mes.prd.adapter.StepStateItemAdapter;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.model.Lot;

public class LotStepStateItemAdapter extends StepStateItemAdapter {
	
	private static final Logger logger = Logger.getLogger(LotStepStateItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	protected String currentPath;
	protected List<Node> nodeList;
	protected Lot lot ;
	
	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof StepState){
			StepState stepState = (StepState)object;
			List<Node> list = new ArrayList<Node>();
			ReworkState reworkState = stepState.getReworkState();
			if (reworkState != null) {
				list.add(reworkState);
			}
			return list.toArray();
//			try {
//				long processInstanceRrn = stepState.getProcessInstanceRrn();
//				String[] paths = stepState.getPath().split("/");
//				
//				//如果StepState为Lot的当前的StepState，并且不是最底层的StepState，则批次需要展开Rework流程
//				if (currentPath.startsWith(stepState.getPath()) && nodeList.size() > paths.length) {
//					Node node = nodeList.get(paths.length);
//					
//					PrdManager prdManager = Framework.getService(PrdManager.class);
//					List<Node> children = prdManager.getProcessDefinitionChildern(node.getProcessDefinition());
//					List<Node> list = new ArrayList<Node>();
//					for (Node child : children) {
//						child.setProcessInstanceRrn(processInstanceRrn);
//						if (child instanceof ProcedureState) {
//							ProcedureState state = (ProcedureState)child;
//							state.setPath(stepState.getPath() + state.getObjectRrn() +  "/");
//							list.add(state);
//						} else if (child instanceof StepState) {
//							StepState state = (StepState)child;
//							state.setPath(stepState.getPath() + state.getObjectRrn() +  "/");
//							list.add(state);
//						}
//					}
//					return list.toArray();
//				}
//				
//			} catch (Exception e) {
//	        	logger.error(e.getMessage(), e);
//	        }
			
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
        return EMPTY;
	}
	
	public boolean hasChildren(Object object) {
		if (object instanceof StepState){
			StepState stepState = (StepState)object;
			if (stepState.getReworkState() != null) {
				return true;
			} else {
				return false;
			}
//			try {
//				String statePath = stepState.getPath();
//				if (!currentPath.startsWith(statePath)) {
//					return false;
//				}
//				String[] paths = statePath.split("/");
//				if (nodeList.size() > paths.length) {
//					return true;
//				}
//			} catch (Exception e) {
//	        	logger.error(e.getMessage(), e);
//	        }
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
        return false;
	}
	
	public void refresh(Lot lot, String currentPath, List<Node> nodeList) {
		this.lot = lot;
		this.currentPath = currentPath;
		this.nodeList = nodeList;
	}
}
