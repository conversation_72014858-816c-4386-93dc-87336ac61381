package com.glory.mes.wip.lot.multicarrier;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComponentComposite.OccupationPolicy;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierComponentAssignComposite extends Composite {

	private static final Logger logger = Logger.getLogger(MultiCarrierComponentAssignComposite.class);

	/**
	 * 指定Source是否以Carrier形式显示,默认为true 否则将以Table的形式显示
	 */
	public boolean sourceIsCarrier = true;
	protected ADTable adTable;

	/**
	 * 显示合计数量
	 */
	public boolean showTotalFlag;

	public Lot sourceLot;
	public Carrier sourceCarrier;
	public Carrier targetCarrier;

	public ListTableManager sourceTableManager;
	public MultiCarrierComponentComposite sourceComponentComposite;
	public MultiCarrierComponentComposite targetComponentComposite;

	public Label lblSourceTotal;

	public Button btnGo;
	public Button btnBack;

	public MultiCarrierComponentAssignComposite(Composite parent, int style, boolean sourceIsCarrier,
			boolean showTotalFlag) {
		super(parent, style);
		this.sourceIsCarrier = sourceIsCarrier;
		this.showTotalFlag = showTotalFlag;
		createForm();
	}

	public void createForm() {
		try {
			GridLayout layout = new GridLayout(3, false);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(GridData.FILL_BOTH));

			createSourceComponent(this);
			createMiddleComponent(this);
			createTargetComponent(this);

		} catch (Exception e) {
			logger.error("ComponentAssignComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createSourceComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = (int) (Toolkit.getDefaultToolkit().getScreenSize().height / 1.5);
		targetComp.setLayoutData(gd);

		if (sourceIsCarrier) {
			sourceComponentComposite = new MultiCarrierComponentComposite(targetComp, null, 0, false, true, false,
					true);
			sourceComponentComposite.init();

			sourceComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter();
				}
			});
		} else {
			if (showTotalFlag) {
				Composite labelCompsite = new Composite(targetComp, SWT.NONE);
				labelCompsite.setLayout(new GridLayout(1, true));

				lblSourceTotal = new Label(labelCompsite, SWT.NONE);
				lblSourceTotal.setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ":0     ");
			}
			sourceTableManager = new ListTableManager(MultiCarrierComponentComposite.getDefaultADTable(), true);
			sourceTableManager.setSortFlag(true);
			sourceTableManager.newViewer(targetComp);
			sourceTableManager.addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter();
				}
			});
		}
	}

	protected void createMiddleComponent(final Composite parent) {
		Composite centerComposite = new Composite(parent, SWT.NONE);
		final GridLayout buttonLayout = new GridLayout();
		buttonLayout.marginWidth = 2;
		buttonLayout.marginHeight = 2;
		centerComposite.setLayout(buttonLayout);
		GridData buttonGd = new GridData(SWT.CENTER, SWT.CENTER, false, false);
		centerComposite.setLayoutData(buttonGd);

		btnGo = new Button(centerComposite, SWT.PUSH);
		btnGo.setText("->");
		btnGo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));

		btnGo.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				goAdapter();
			}
		});

		btnBack = new Button(centerComposite, SWT.PUSH);
		btnBack.setText("<-");
		btnBack.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		btnBack.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				backAdapter();
			}
		});
	}

	protected void createTargetComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = (int) (Toolkit.getDefaultToolkit().getScreenSize().height / 1.5);
		targetComp.setLayoutData(gd);

		targetComponentComposite = new MultiCarrierComponentComposite(targetComp, null, 0, false, true, true, true);
		targetComponentComposite.init();
		targetComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				backAdapter();
			}
		});
	}

	public Carrier searchCarrier(String carrierId, MultiCarrierComponentComposite componentComposite, boolean isValid,
			boolean mixLot) {
		try {
			if (!StringUtil.isEmpty(carrierId)) {
				DurableManager durableManager = Framework.getService(DurableManager.class);
				targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, false, mixLot);
				if (targetCarrier == null) {
					UI.showWarning(Message.getString("mm.carrier_is_not_exist"));
					componentComposite.setCarrierId(null);
					componentComposite.initComponents(new ArrayList<ComponentUnit>());
					return null;
				}
				if (isValid) {
					// 进行有效性检查
					durableManager.checkCarrierAvailable(Env.getSessionContext(), targetCarrier);
					if (!targetCarrier.getIsAvailable()) {
						UI.showWarning(Message.getString(targetCarrier.getMessage()));
						componentComposite.setCarrierId(null);
						componentComposite.initComponents(new ArrayList<ComponentUnit>());
						return null;
					}
				}

				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<ComponentUnit> componentUnits = carrierLotManager.getComponentByCarrierId(Env.getOrgRrn(),
						carrierId);

				// 去重校验
				if (componentComposite.checkIsChangePosition(componentUnits, targetCarrier.getCapacity().intValue(),
						carrierId, targetCarrier.getSlotDirectionAsc())) {
					return null;
				}

				// 校验是否混批，默认不允许混批
				if (mixLot) {
					String whereClause = "carrierId = '" + carrierId + "'";
					ADManager adManager = Framework.getService(ADManager.class);
					List<LotMultiCarrier> lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(),
							LotMultiCarrier.class, Integer.MAX_VALUE, whereClause, null);
					if (targetCarrier.getIsMixLot()) {
						if (lotMultiCarriers.size() > 1 || (lotMultiCarriers.size() == 1
								&& !lotMultiCarriers.get(0).getLotRrn().equals(sourceLot.getObjectRrn()))) {
							UI.showWarning(Message.getString("wip.durable_cannot_contains_multi_lot"));
							componentComposite.setCarrierId(null);
							componentComposite.initComponents(new ArrayList<ComponentUnit>());
							return null;
						}
					}
				}

				// 查询批次
				componentUnits = getComponentWithLotInfo(componentUnits);

				componentComposite.setCount(targetCarrier.getCapacity().intValue());
				componentComposite.setCarrierId(carrierId);
				componentComposite.setAscFlag(targetCarrier.getSlotDirectionAsc());
				componentComposite.initComponents(componentUnits);
				changeTotal();
				return targetCarrier;
			} else {
				componentComposite.initComponents(new ArrayList<ComponentUnit>());
			}
		} catch (Exception e) {
			logger.error("ComponentAssignComposite searchCarrier:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	public boolean removeCarrier(String carrierId, MultiCarrierComponentComposite componentComposite) {
		try {

			if (!StringUtil.isEmpty(carrierId)) {
				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
				if (carrier == null) {
					UI.showWarning(Message.getString("mm.carrier_is_not_exist"));
					componentComposite.initComponents(new ArrayList<ComponentUnit>());
					return false;
				}
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<ComponentUnit> componentUnits = carrierLotManager.getComponentByCarrierId(Env.getOrgRrn(),
						carrierId);
				if (!componentComposite.checkIsChangePosition(componentUnits, carrier.getCapacity().intValue(),
						carrierId, carrier.getSlotDirectionAsc())) {
					componentComposite.remove(carrierId);
					return true;
				}
				changeTotal();
			}
		} catch (Exception e) {
			logger.error("ComponentAssignComposite removeCarrier:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}

	public Lot searchLot(String lotId) {
		try {
			sourceLot = LotProviderEntry.getLot(lotId);
			LotManager lotManager = Framework.getService(LotManager.class);
			sourceLot = lotManager.getLotWithComponentOrderByPosition(sourceLot.getObjectRrn(), true);

			List<ComponentUnit> components = new ArrayList<ComponentUnit>();
			if (sourceLot.getSubProcessUnit() != null && sourceLot.getSubProcessUnit().size() > 0) {
				for (ProcessUnit processUnit : sourceLot.getSubProcessUnit()) {
					ComponentUnit compUnit = (ComponentUnit) processUnit;
					compUnit.setLotId(sourceLot.getLotId());
					components.add(compUnit);
				}
			}

			sourceTableManager.setInput(components);
			changeTotal();
			return sourceLot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	protected void goAdapter() {
		try {
			if (sourceComponentComposite != null) {
				List<Object> objects = sourceComponentComposite.getTableManager().getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}

				List<ComponentUnit> sourceComponentUnits = new ArrayList<ComponentUnit>();
				for (Object obj : objects) {
					sourceComponentUnits.add((ComponentUnit) obj);
				}

				for (ComponentUnit componentUnit : sourceComponentUnits) {
					String targetPosition = targetComponentComposite.getCurrentPositon();
					if (StringUtil.isEmpty(targetPosition)) {
						UI.showWarning(Message.getString("wip.target_position_is_not_select"));
						return;
					}
					String sourcePosition = componentUnit.getDurable() + "-" + componentUnit.getPosition();
					if (targetComponentComposite.addComponent(componentUnit, targetPosition, OccupationPolicy.REJECT)) {
						sourceComponentComposite.removeComponent(sourcePosition);
					}
					targetComponentComposite.addCurrentPosition();
				}
			} else {
				List<Object> objects = sourceTableManager.getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}

				List<ComponentUnit> sourceComponentUnits = new ArrayList<ComponentUnit>();
				for (Object obj : objects) {
					sourceComponentUnits.add((ComponentUnit) obj);
				}

				String targetPosition = targetComponentComposite.getCurrentPositon();
				if (StringUtil.isEmpty(targetPosition)) {
					List<ComponentUnit> addedComponentUnits = targetComponentComposite
							.addComponentAppend(sourceComponentUnits);
					sourceTableManager.removeList(addedComponentUnits);
				} else {
					List<ComponentUnit> addedComponentUnits = addComponentList(sourceComponentUnits, targetPosition,
							OccupationPolicy.REJECT);
					sourceTableManager.removeList(addedComponentUnits);
				}
			}
			changeTotal();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * 按照顺序放置Component
	 * 从指定的位置开始
	 * @throws CloneNotSupportedException
	 */
	public List<ComponentUnit> addComponentList(List<ComponentUnit> componentUnits, String position,
			OccupationPolicy occupationPolicy) throws CloneNotSupportedException {
		boolean startFlag = false;
		int i = 0;

		Map<String, ComponentUnit> carrierMap = targetComponentComposite.getCarrierMap();

		List<ComponentUnit> addedComponentUnits = new ArrayList<ComponentUnit>();
		for (String key : carrierMap.keySet()) {
			if (key.equals(position)) {
				startFlag = true;
			}
			if (startFlag) {
				if (i == componentUnits.size()) {
					break;
				}
				ComponentUnit component = componentUnits.get(i);
				if (!targetComponentComposite.addComponent(component, key, occupationPolicy)) {
					return addedComponentUnits;
				}
				addedComponentUnits.add(component);
				i++;
			}
		}

		return addedComponentUnits;
	}

	protected void backAdapter() {
		try {
			List<Object> objects = targetComponentComposite.getTableManager().getCheckedObject();
			if (objects == null || objects.isEmpty()) {
				UI.showWarning(Message.getString("wip.target_position_is_not_select"));
				return;
			}

			List<ComponentUnit> targetComponentUnits = new ArrayList<ComponentUnit>();
			for (Object obj : objects) {
				targetComponentUnits.add((ComponentUnit) obj);
			}

			if (sourceComponentComposite != null) {
				for (ComponentUnit componentUnit : targetComponentUnits) {
					// 检查选中的Component是否都是sourceComponentUnits
					if (!sourceComponentComposite.getInitCompIdPositionMap().keySet()
							.contains(componentUnit.getComponentId())) {
						UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
						return;
					}

					// 必须放回原来的位置
					String sourcePosition = sourceComponentComposite.getInitCompIdPositionMap()
							.get(componentUnit.getComponentId());
					String targetPosition = componentUnit.getDurable() + "-" + componentUnit.getPosition();
					if (sourceComponentComposite.addComponent(componentUnit, sourcePosition, OccupationPolicy.REJECT)) {
						targetComponentComposite.removeComponent(targetPosition);
					}
				}
			} else {
				for (ComponentUnit componentUnit : targetComponentUnits) {
					// 检查选中的Component是否都是sourceComponentUnits
					if (targetComponentComposite.getInitCompIdPositionMap().keySet()
							.contains(componentUnit.getComponentId())) {
						UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
						return;
					}

					String targetPosition = componentUnit.getDurable() + "-" + componentUnit.getPosition();
					targetComponentComposite.removeComponent(targetPosition);

					// to source
					if (sourceTableManager.getInput().size() == 0) {
						sourceTableManager.add(componentUnit);
					} else {
						int index = sourceTableManager.getInput().size();
						sourceTableManager.insert(index, componentUnit);
					}
				}
			}
			changeTotal();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void changeTotal() {
		if (sourceComponentComposite != null && showTotalFlag) {
			sourceComponentComposite.changeTotal();
		}

		if (sourceTableManager != null) {
			//
		}
		if (targetComponentComposite != null && showTotalFlag) {
			targetComponentComposite.changeTotal();
		}
	}

	/**
	 * 附加批次信息
	 * 
	 * @param lstComponentUnit
	 * @return
	 * @throws Exception
	 */
	protected List<ComponentUnit> getComponentWithLotInfo(List<ComponentUnit> lstComponentUnit) throws Exception {
		// 查询批次
		LotManager lotManager = Framework.getService(LotManager.class);
		Map<Long, Lot> lotMap = new LinkedHashMap<Long, Lot>();
		for (ComponentUnit comp : lstComponentUnit) {
			if (comp.getParentUnitRrn() != null && comp.getParentUnitRrn() != 0) {
				Lot lot = lotMap.get(comp.getParentUnitRrn());
				if (lot == null) {
					lot = lotManager.getLot(comp.getParentUnitRrn());
					lotMap.put(comp.getParentUnitRrn(), lot);
				}
				comp.setLotId(lot.getLotId());
			}
		}

		return lstComponentUnit;
	}
}
