package com.glory.mes.wip.lot.multicarrier;

import java.awt.Toolkit;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierQtyAssignComposite extends MultiCarrierComponentAssignComposite {

	private static final Logger logger = Logger.getLogger(MultiCarrierQtyAssignComposite.class);
	
	private static final String  SOURCE_TABLE_NAME = "WIPLotByMultiCarrierSource";

	public ListTableManager sourceTableManager;
	public MultiCarrierQtyComposite sourceQtyComposite;
	public MultiCarrierQtyComposite targetQtyComposite;

	public MultiCarrierQtyAssignComposite(Composite parent, int style, boolean sourceIsCarrier, boolean showTotalFlag) {
		super(parent, style, sourceIsCarrier, showTotalFlag);
	}
	
	@Override
	protected void createSourceComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = (int) (Toolkit.getDefaultToolkit().getScreenSize().height / 1.1);
		targetComp.setLayoutData(gd);
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), SOURCE_TABLE_NAME);
			if (sourceIsCarrier) {
				sourceQtyComposite = new MultiCarrierQtyComposite(targetComp, adTable, true, false, true, false,
						showTotalFlag);
				sourceQtyComposite.init();
				
				sourceQtyComposite.getTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
					@Override
					public void selectionChanged(SelectionChangedEvent event) {
						changeTotal();
					}
				});
			} else {
				sourceTableManager = new ListTableManager(adTable);
				sourceTableManager.setSortFlag(true);
				sourceTableManager.setIndexFlag(true);
				sourceTableManager.newViewer(targetComp);
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected void createMiddleComponent(final Composite parent) {
		Composite centerComposite = new Composite(parent, SWT.NONE);
		final GridLayout buttonLayout = new GridLayout();
		buttonLayout.marginWidth = 2;
		buttonLayout.marginHeight = 2;
		centerComposite.setLayout(buttonLayout);
		GridData buttonGd = new GridData(SWT.CENTER, SWT.CENTER, false, false);
		centerComposite.setLayoutData(buttonGd);
	}

	@Override
	protected void createTargetComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = (int) (Toolkit.getDefaultToolkit().getScreenSize().height / 1.1);
		targetComp.setLayoutData(gd);

		targetQtyComposite = new MultiCarrierQtyComposite(targetComp, null, true, false, true, true, showTotalFlag);
		targetQtyComposite.init();
	}

	public Carrier searchCarrier(String carrierId, MultiCarrierQtyComposite qtyComposite, boolean isValid,
			boolean checkLot, boolean mixLot) {
		try {
			if (!StringUtil.isEmpty(carrierId)) {
				qtyComposite.getTableManager().getInput().clear();
				DurableManager durableManager = Framework.getService(DurableManager.class);
				targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, false, mixLot);
				if (targetCarrier == null) {
					UI.showWarning(Message.getString("mm.carrier_is_not_exist"));
					qtyComposite.setCarrierId(null);
					qtyComposite.initLotMultiCarriers(new ArrayList<LotMultiCarrier>());
					return null;
				}
				if (isValid) {
					// 进行有效性检查
					durableManager.checkCarrierAvailable(Env.getSessionContext(), targetCarrier);
					if (!targetCarrier.getIsAvailable()) {
						UI.showWarning(Message.getString(targetCarrier.getMessage()));
						qtyComposite.setCarrierId(null);
						qtyComposite.initLotMultiCarriers(new ArrayList<LotMultiCarrier>());
						return null;
					}
				}

				String whereClause = "carrierId = '" + carrierId + "'";
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotMultiCarrier> lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
						Integer.MAX_VALUE, whereClause, null);
				// 校验是否混批，默认不允许混批
				if (mixLot) {
					if (!targetCarrier.getIsMixLot()) {
						if (lotMultiCarriers.size() > 1 || (lotMultiCarriers.size() == 1
								&& !lotMultiCarriers.get(0).getLotRrn().equals(sourceLot.getObjectRrn()))) {
							UI.showWarning(Message.getString("wip.durable_cannot_contains_multi_lot"));
							qtyComposite.setCarrierId(null);
							qtyComposite.initLotMultiCarriers(new ArrayList<LotMultiCarrier>());
							return null;
						}
					}
				}

				if (checkLot) {
					whereClause = whereClause + "and lotRrn = " + sourceLot.getObjectRrn();
					lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
							Integer.MAX_VALUE, whereClause, null);
				}

				qtyComposite.setCarrierId(carrierId);
				qtyComposite.initLotMultiCarriers(lotMultiCarriers);
				changeTotal();
				return targetCarrier;
			} else {
				qtyComposite.initLotMultiCarriers(new ArrayList<LotMultiCarrier>());
			}
		} catch (Exception e) {
			logger.error("QtyAssignComposite searchCarrier:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		changeTotal();
		return null;
	}

	public void removeCarrier(String carrierId, MultiCarrierQtyComposite qtyComposite) {
		qtyComposite.remove(carrierId);
		changeTotal();
	}

	@Override
	@SuppressWarnings("unchecked")
	public void changeTotal() {
		if (sourceQtyComposite != null && showTotalFlag) {
			sourceQtyComposite.changeTotal();
			List<LotMultiCarrier> lotMultiCarriers = (List<LotMultiCarrier>) sourceQtyComposite.getTableManager().getInput();
			BigDecimal reminder = lotMultiCarriers.stream()
					.filter(x -> x.getTransQty() != null && x.getTransQty().compareTo(BigDecimal.ZERO) > 0)
					.map(LotMultiCarrier::getTransQty).reduce(BigDecimal.ZERO, BigDecimal::add);
			BigDecimal currentQty = lotMultiCarriers.stream()
					.filter(x -> x.getCurrentQty() != null && x.getCurrentQty().compareTo(BigDecimal.ZERO) > 0)
					.map(LotMultiCarrier::getCurrentQty).reduce(BigDecimal.ZERO, BigDecimal::add);
			sourceQtyComposite.getLblTotal().setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ":" + currentQty.subtract(reminder));
		}

		if (sourceTableManager != null) {
			//
		}
		if (targetQtyComposite != null && showTotalFlag) {
			targetQtyComposite.changeTotal();
		}
	}
}
