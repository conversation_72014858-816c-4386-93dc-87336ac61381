package com.glory.mes.wip.lot.multicarrier.scrap;


import java.math.BigDecimal;
import java.util.List;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.Composite;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierQtyComposite;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierQtyScrapComposite extends MultiCarrierQtyComposite{

	private static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");
	
	private MultiCarrierScrapEditor multiCarrierScrapEditor;
	
	public MultiCarrierQtyScrapComposite(Composite parent, ADTable adTable, boolean indexFlag, boolean checkFlag,
			boolean editorFlag, boolean showTotalFlag) {
		super(parent, adTable, indexFlag, checkFlag, editorFlag, showTotalFlag);
	}

	public MultiCarrierQtyScrapComposite(Composite parent, ADTable adTable, boolean indexFlag, boolean checkFlag,
			boolean editorFlag, boolean showTotalFlag,MultiCarrierScrapEditor multiCarrierScrapEditor) {
		super(parent, adTable, indexFlag, checkFlag, editorFlag, showTotalFlag);
		this.multiCarrierScrapEditor = multiCarrierScrapEditor;
	}

	@Override
	public void changeTotal() {
		List<Object> inputObject = (List<Object>)getTableManager().getInput();
		BigDecimal total = BigDecimal.ZERO;
		for (Object object : inputObject) {
			if (object instanceof LotMultiCarrier) {
				LotMultiCarrier lotMultiCarrier =(LotMultiCarrier)object;
				if (lotMultiCarrier.getTransQty() != null) {
					if (lotMultiCarrier.getTransQty().compareTo(lotMultiCarrier.getCurrentQty()) > 0) {
						lotMultiCarrier.setTransQty(lotMultiCarrier.getCurrentQty());
						UI.showWarning(lotMultiCarrier.getCarrierId()+Message.getString("wip.lot_multicarrier_outnumber_currentqty"));
					}
					total=total.add(lotMultiCarrier.getTransQty());
				}
			}
		}
		getLblTotal().setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) +  ":" + total);
	}

	@Override
	public void init() {
		super.init();
		tableManagerAddChecklisenner();
	}

	public void tableManagerAddChecklisenner() {
		getTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
			
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				String comboScrapCode = multiCarrierScrapEditor.scrapInfoComposite.comboScrapCode.getText();
				String comment = multiCarrierScrapEditor.scrapInfoComposite.commentText.getText() == null? "":multiCarrierScrapEditor.scrapInfoComposite.commentText.getText();
				if(comboScrapCode == null
		              || "".equals(comboScrapCode.trim())){
					UI.showWarning(String.format(Message.getString("wip.scrap_code_required"),
		                  HEADER_SCRAP_CODE));
					return;
				}
				StructuredSelection structuredSelection = (StructuredSelection) event.getSelection();
				LotMultiCarrier lotMultiCarrier =(LotMultiCarrier) structuredSelection.getFirstElement();
				if (lotMultiCarrier == null) {
					return;
				}
				lotMultiCarrier.setActionCode(comboScrapCode);
				lotMultiCarrier.setActionComments(comment);
			}
		});
		
	}
	
	/**
	 * 检查报废数量必填
	 * */
	public boolean validat() {
		List<LotMultiCarrier> lmcs = (List<LotMultiCarrier>)getTableManager().getInput();
		for (LotMultiCarrier lotMultiCarrier : lmcs) {
			if (lotMultiCarrier.getTransQty() == null) {
				UI.showError(Message.getString("wip.mulit_carrier_scrapqty__required"));
				return false;
			}else if(lotMultiCarrier.getTransQty().compareTo(BigDecimal.ZERO) <= 0) {
				//数量不能等于和小于0
				UI.showError(Message.getString("mm.mlot_receive_must_more_than.zero"));
				return false;
			}
		}
		return true;
	}

}
