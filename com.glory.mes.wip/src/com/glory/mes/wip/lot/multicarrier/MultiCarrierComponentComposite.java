package com.glory.mes.wip.lot.multicarrier;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierComponentComposite {

	protected final Color ColorBackground = SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE);

	private ComponentUnit selectedObj;
	private String currentPositon;

	public static enum DiffType {
		ADD, REMOVE, CHANGE
	}

	public static enum OccupationPolicy {
		REPLACE, IGNORE, REJECT
	}

	private Composite parent;
	private ADTable adTable;
	private ListTableManager tableManager;

	private int count;
	private String carrierId;

	private boolean ascFlag;
	private boolean checkFlag;
	private boolean showCarrierPositionFlag;
	private boolean showTotalFlag;

	public Label lblTotal;

	// 载具初始的ComponentUnit位置,只记录真实的Component,Key为ComponentId,Value未Position
	private Map<String, String> initCompIdPositionMap = new LinkedHashMap<String, String>();

	// 载具初始的ComponentUnit信息
	private Map<String, ComponentUnit> initCompIdMap = new LinkedHashMap<String, ComponentUnit>();

	// 载具位置当前的ComponentUnit位置
	private Map<String, ComponentUnit> carrierMap = new LinkedHashMap<String, ComponentUnit>();

	public MultiCarrierComponentComposite(Composite parent, int count, boolean ascFlag) {
		this.parent = parent;
		this.count = count;
		this.ascFlag = ascFlag;
	}

	public MultiCarrierComponentComposite(Composite parent, int count, boolean ascFlag, boolean checkFlag) {
		this.parent = parent;
		this.count = count;
		this.ascFlag = ascFlag;
		this.checkFlag = checkFlag;
	}

	public MultiCarrierComponentComposite(Composite parent, ADTable adTable, int count, boolean ascFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.count = count;
		this.ascFlag = ascFlag;
	}

	public MultiCarrierComponentComposite(Composite parent, ADTable adTable, int count, boolean ascFlag,
			boolean checkFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.count = count;
		this.ascFlag = ascFlag;
		this.checkFlag = checkFlag;
	}

	public MultiCarrierComponentComposite(Composite parent, ADTable adTable, int count, boolean ascFlag,
			boolean checkFlag, boolean showCarrierPositionFlag, boolean showTotalFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.count = count;
		this.ascFlag = ascFlag;
		this.checkFlag = checkFlag;
		this.showCarrierPositionFlag = showCarrierPositionFlag;
		this.showTotalFlag = showTotalFlag;
	}

	public void init() {
		if (showTotalFlag) {
			Composite labelCompsite = new Composite(parent, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(1, false));

			lblTotal = new Label(labelCompsite, SWT.NONE);
			lblTotal.setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ": 0    ");
		}
		if (adTable == null) {
			adTable = showCarrierPositionFlag ? getDefaultADTable() : getHiddenPositionADTable();
		}
		tableManager = new ListTableManager(adTable, checkFlag);
		tableManager.setSortFlag(false);
		tableManager.newViewer(parent);
		tableManager.setTableBackground(ColorBackground);

		tableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection structuredSelection = (StructuredSelection) event.getSelection();
				selectedObj = (ComponentUnit) structuredSelection.getFirstElement();
				if (selectedObj != null) {
					currentPositon = selectedObj.getDurable() + "-" + selectedObj.getPosition();
				}
				changeTotal();
			}
		});
		initComponents(new ArrayList<ComponentUnit>());
	}

	public void initComponents(List<ComponentUnit> components) {
		initComponents(components, count, carrierId);
	}

	public void initComponents(List<ComponentUnit> components, int count, String carrierId) {
		if (showCarrierPositionFlag && carrierId != null) {
			if (ascFlag) {
				for (int i = 1; i <= count; i++) {
					ComponentUnit component = new ComponentUnit();
					component.setPosition(String.valueOf(i));
					component.setDurable(carrierId);
					carrierMap.put(carrierId + "-" + String.valueOf(i), component);
				}
			} else {
				for (int i = count; i >= 1; i--) {
					ComponentUnit component = new ComponentUnit();
					component.setPosition(String.valueOf(i));
					component.setDurable(carrierId);
					carrierMap.put(carrierId + "-" + String.valueOf(i), component);
				}
			}
		}

		for (ComponentUnit component : components) {
			carrierMap.put(carrierId != null ? carrierId + "-" + component.getPosition() : component.getPosition(),
					component);
			initCompIdMap.put(component.getComponentId(), component);
			initCompIdPositionMap.put(component.getComponentId(), carrierId + "-" + component.getPosition());
		}
		tableManager.setInput(getCarrierComponents());
		selectedObj = null;
	}

	public boolean addComponent(ComponentUnit component, String position, OccupationPolicy occupationPolicy) {
		ComponentUnit curComponent = carrierMap.get(position);
		if (curComponent != null) {
			// 先考虑是否已经 加到了右边列表
			List<? extends Object> input = tableManager.getInput();
			for (Object object : input) {
				ComponentUnit curUnit = (ComponentUnit) object;
				if (StringUtil.isEmpty(component.getComponentId())) {
					return false;
				}

				if (component.getComponentId().equals(curUnit.getComponentId())) {
					UI.showError(Message.getString("wip.durable_assign_component_repeat"));
					return false;
				}
			}

			if (!StringUtil.isEmpty(curComponent.getComponentId())) {
				// 如果当前位置已经有Component
				switch (occupationPolicy) {
				case REPLACE:
					component.setPosition(position.substring(position.lastIndexOf("-") + 1));
					component.setDurable(curComponent.getDurable());
					carrierMap.put(position, component);
					break;
				case IGNORE:
					break;
				case REJECT:
					// UI.showError(Message.getString("wip.component_is_exist"));
					return false;
				}
			} else {
				component.setPosition(position.substring(position.lastIndexOf("-") + 1));
				component.setDurable(curComponent.getDurable());
				carrierMap.put(position, component);
			}
			tableManager.setInput(getCarrierComponents());
			selectedObj = null;
			return true;
		}
		return false;
	}

	public boolean removeComponent(String position) {
		ComponentUnit curComponent = carrierMap.get(position);
		if (!StringUtil.isEmpty(curComponent.getComponentId())) {
			ComponentUnit component = new ComponentUnit();
			component.setPosition(position.substring(position.lastIndexOf("-") + 1));
			component.setDurable(position.substring(0, position.lastIndexOf("-")));
			carrierMap.put(position, component);
		}
		tableManager.setInput(getCarrierComponents());
		selectedObj = null;
		return true;
	}

	public boolean removeComponent(ComponentUnit component) {
		for (String position : carrierMap.keySet()) {
			ComponentUnit curComponent = carrierMap.get(position);
			if (!StringUtil.isEmpty(curComponent.getComponentId())
					&& curComponent.getComponentId().equals(component.getComponentId())) {
				ComponentUnit newComponent = new ComponentUnit();
				newComponent.setPosition(position);
				carrierMap.put(position, component);
				tableManager.setInput(getCarrierComponents());
				break;
			}
		}
		selectedObj = null;
		return true;
	}

	/**
	 * 按照顺序放置Component
	 * 从指定的位置开始
	 * @throws CloneNotSupportedException
	 */
	public List<ComponentUnit> addComponentList(List<ComponentUnit> componentUnits, String position,
			OccupationPolicy occupationPolicy) throws CloneNotSupportedException {
		boolean startFlag = false;
		int i = 0;

		List<ComponentUnit> addedComponentUnits = new ArrayList<ComponentUnit>();
		for (String key : carrierMap.keySet()) {
			if (key.equals(position)) {
				startFlag = true;
			}
			if (startFlag) {
				if (i == componentUnits.size()) {
					break;
				}
				ComponentUnit component = componentUnits.get(i);
				if (!addComponent((ComponentUnit) component.clone(), key, occupationPolicy)) {
					return addedComponentUnits;
				}
				addedComponentUnits.add(component);
				i++;
			}
		}

		return addedComponentUnits;
	}

	/**
	 * 按照顺序放置Component
	 * 从第一个空的位置开始
	 */
	public List<ComponentUnit> addComponentAppend(List<ComponentUnit> componentUnits) {
		boolean startFlag = false;
		int i = 0;

		List<ComponentUnit> addedComponentUnits = new ArrayList<ComponentUnit>();
		for (String position : carrierMap.keySet()) {
			if (StringUtil.isEmpty(carrierMap.get(position).getComponentId())) {
				startFlag = true;
			}
			if (startFlag) {
				if (i == componentUnits.size()) {
					break;
				}
				ComponentUnit component = componentUnits.get(i);
				if (!addComponent(component, position, OccupationPolicy.REJECT)) {
					return addedComponentUnits;
				}
				addedComponentUnits.add(component);
				i++;
			}
		}

		return addedComponentUnits;
	}

	public Map<DiffType, List<ComponentUnit>> getComponentsDiff() {
		List<ComponentUnit> addComponents = new ArrayList<ComponentUnit>();
		List<ComponentUnit> removeComponents = new ArrayList<ComponentUnit>();
		List<ComponentUnit> changeComponents = new ArrayList<ComponentUnit>();

		for (String curPosition : carrierMap.keySet()) {
			String componentId = carrierMap.get(curPosition).getComponentId();
			if (!StringUtil.isEmpty(componentId)) {
				if (!initCompIdPositionMap.keySet().contains(componentId)) {
					// 如果不在初始Carrier
					addComponents.add(carrierMap.get(curPosition));
				} else {
					// 如果在初始Carrier,比较位置是否相同
					if (!curPosition.equals(initCompIdPositionMap.get(componentId))) {
						changeComponents.add(carrierMap.get(curPosition));
					}
				}
			}
		}

		for (String initCompId : initCompIdPositionMap.keySet()) {
			boolean existFlag = false;
			for (String curPosition : carrierMap.keySet()) {
				String componentId = carrierMap.get(curPosition).getComponentId();
				if (initCompId.equals(componentId)) {
					existFlag = true;
					continue;
				}
			}
			if (!existFlag) {
				removeComponents.add(initCompIdMap.get(initCompId));
			}
		}

		Map<DiffType, List<ComponentUnit>> diffMap = new HashMap<DiffType, List<ComponentUnit>>();
		if (addComponents.size() > 0) {
			diffMap.put(DiffType.ADD, addComponents);
		}
		if (removeComponents.size() > 0) {
			diffMap.put(DiffType.REMOVE, removeComponents);
		}
		if (changeComponents.size() > 0) {
			diffMap.put(DiffType.CHANGE, changeComponents);
		}
		return diffMap;
	}

	public Map<String, List<ComponentUnit>> getSourceComponents() {
		Map<String, List<ComponentUnit>> sourceComponentUnitMap = new HashMap<String, List<ComponentUnit>>();

		for (String initCompId : initCompIdPositionMap.keySet()) {
			boolean existFlag = false;
			for (String curPosition : carrierMap.keySet()) {
				String componentId = carrierMap.get(curPosition).getComponentId();
				if (initCompId.equals(componentId)) {
					existFlag = true;
					continue;
				}
			}
			if (!existFlag) {
				if (sourceComponentUnitMap.containsKey(initCompIdPositionMap.get(initCompId).substring(0, initCompIdPositionMap.get(initCompId).lastIndexOf("-")))) {
					List<ComponentUnit> durableComponents = sourceComponentUnitMap
							.get(initCompIdPositionMap.get(initCompId).substring(0, initCompIdPositionMap.get(initCompId).lastIndexOf("-")));
					durableComponents.add(initCompIdMap.get(initCompId));
					sourceComponentUnitMap.put(initCompIdPositionMap.get(initCompId).substring(0, initCompIdPositionMap.get(initCompId).lastIndexOf("-")), durableComponents);
				} else {
					List<ComponentUnit> durableComponents = new ArrayList<ComponentUnit>();
					durableComponents.add(initCompIdMap.get(initCompId));
					sourceComponentUnitMap.put(initCompIdPositionMap.get(initCompId).substring(0, initCompIdPositionMap.get(initCompId).lastIndexOf("-")), durableComponents);
				}
			}
		}

		return sourceComponentUnitMap;
	}

	/**
	 * 获取组件列表，包含空位
	 * 
	 * @return
	 */
	public List<ComponentUnit> getCarrierComponents() {
		List<ComponentUnit> compList = new ArrayList<ComponentUnit>();
		for (ComponentUnit comp : carrierMap.values()) {
			compList.add(comp);
		}
		return compList;
	}

	/**
	 * 获取组件列表，去除空位
	 * 判断组件号是否为空，来断定是否为空位
	 * @return
	 */
	public List<ComponentUnit> getRealComponents() {
		List<ComponentUnit> compList = new ArrayList<ComponentUnit>();
		for (ComponentUnit comp : carrierMap.values()) {
			if (!StringUtil.isEmpty(comp.getComponentId())) {
				compList.add(comp);
			}
		}
		return compList;
	}

	public static ADTable getDefaultADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		ADField adFieldPosition = new ADField();
		adFieldPosition.setName("position");
		adFieldPosition.setIsMain(true);
		adFieldPosition.setIsDisplay(true);
		adFieldPosition.setDataType(DataType.SEQUENCE);
		adFieldPosition.setDisplayLength(15l);
		adFieldPosition.setLabel(Message.getString("wip.position"));
		adFieldPosition.setLabel_zh(Message.getString("wip.position"));
		adFields.add(adFieldPosition);

		ADField adFieldLotId = new ADField();
		adFieldLotId.setName("lotId");
		adFieldLotId.setIsMain(true);
		adFieldLotId.setIsDisplay(true);
		adFieldLotId.setLabel(Message.getString("wip.lot_id"));
		adFieldLotId.setLabel_zh(Message.getString("wip.lot_id"));
		adFields.add(adFieldLotId);

		ADField adFieldDurable = new ADField();
		adFieldDurable.setName("durable");
		adFieldDurable.setIsMain(true);
		adFieldDurable.setIsDisplay(true);
		adFieldDurable.setLabel(Message.getString("wip.durable"));
		adFieldDurable.setLabel_zh(Message.getString("wip.durable"));
		adFields.add(adFieldDurable);

		ADField adFieldWaferId = new ADField();
		adFieldWaferId.setName("componentId");
		adFieldWaferId.setIsMain(true);
		adFieldWaferId.setIsDisplay(true);
		adFieldWaferId.setLabel(Message.getString("wip.component_id"));
		adFieldWaferId.setLabel_zh(Message.getString("wip.component_id"));
		adFields.add(adFieldWaferId);

		adTable.setFields(adFields);

		return adTable;
	}

	public static ADTable getHiddenPositionADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();

		ADField adFieldLotId = new ADField();
		adFieldLotId.setName("lotId");
		adFieldLotId.setIsMain(true);
		adFieldLotId.setIsDisplay(true);
		adFieldLotId.setLabel(Message.getString("wip.lot_id"));
		adFieldLotId.setLabel_zh(Message.getString("wip.lot_id"));
		adFields.add(adFieldLotId);

		ADField adFieldDurable = new ADField();
		adFieldDurable.setName("durable");
		adFieldDurable.setIsMain(true);
		adFieldDurable.setIsDisplay(true);
		adFieldDurable.setLabel(Message.getString("wip.durable"));
		adFieldDurable.setLabel_zh(Message.getString("wip.durable"));
		adFields.add(adFieldDurable);

		ADField adFieldWaferId = new ADField();
		adFieldWaferId.setName("componentId");
		adFieldWaferId.setIsMain(true);
		adFieldWaferId.setIsDisplay(true);
		adFieldWaferId.setLabel(Message.getString("wip.component_id"));
		adFieldWaferId.setLabel_zh(Message.getString("wip.component_id"));
		adFields.add(adFieldWaferId);

		adTable.setFields(adFields);

		return adTable;
	}

	public ComponentUnit getSelectedObj() {
		return selectedObj;
	}

	public void setSelectedObj(ComponentUnit selectedObj) {
		this.selectedObj = selectedObj;
	}

	public ListTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(ListTableManager tableManager) {
		this.tableManager = tableManager;
	}

	public Map<String, String> getInitCompIdPositionMap() {
		return initCompIdPositionMap;
	}

	public Map<String, ComponentUnit> getCarrierMap() {
		return carrierMap;
	}

	public void checkedAll() {
		if (checkFlag) {
			List<ComponentUnit> componentList = (List<ComponentUnit>) tableManager.getInput();
			List<ComponentUnit> units = componentList.stream().filter(comp -> !StringUtil.isEmpty(comp.getLotId()))
					.collect(Collectors.toList());
			for (ComponentUnit componentUnit : units) {
				tableManager.setCheckedObject(componentUnit);
			}
		}
	}

	public void uncheckedAll() {
		if (checkFlag) {
			List<Object> checkedComponentList = tableManager.getCheckedObject();
			checkedComponentList.clear();
			tableManager.refresh();
		}
	}
	
	public void removeAll() {
		carrierMap.clear();
		initCompIdMap.clear();
		initCompIdPositionMap.clear();
		tableManager.setInput(new ArrayList<ComponentUnit>());
		changeTotal();
	}

	/**
	 * 判断是否存在载具的位置移动
	 * 
	 * @return
	 */
	public boolean checkIsChangePosition(List<ComponentUnit> components, int count, String carrierId, boolean ascFlag) {
		if (carrierMap.isEmpty()) {
			return false;
		}
		for (String curPosition : carrierMap.keySet()) {
			if (curPosition.substring(0, curPosition.lastIndexOf("-")).equals(carrierId)) {
				String componentId = carrierMap.get(curPosition).getComponentId();
				if (!StringUtil.isEmpty(componentId)) {
					if (!initCompIdPositionMap.keySet().contains(componentId)) {
						// 如果不在初始Carrier
						return true;
					} else {
						// 如果在初始Carrier,比较位置是否相同
						if (!curPosition.equals(initCompIdPositionMap.get(componentId))) {
							return true;
						}
					}
				} else {
					if (initCompIdPositionMap.containsValue(curPosition)) {
						return true;
					}
				}
			}
		}
		return false;
	}

	public void remove(String carrierId) {
		Map<String, ComponentUnit> newCarrierMap = new LinkedHashMap<String, ComponentUnit>();
		List<ComponentUnit> componentList = new ArrayList<ComponentUnit>();
		for (String position : carrierMap.keySet()) {
			ComponentUnit component = carrierMap.get(position);
			if (position.substring(0, position.lastIndexOf("-")).equals(carrierId)) {
				initCompIdMap.remove(component.getComponentId());
				initCompIdPositionMap.remove(component.getComponentId());
			} else {
				newCarrierMap.put(position, component);
				componentList.add(component);
			}
		}
		carrierMap = newCarrierMap;
		tableManager.setInput(componentList);
		tableManager.refresh();
	}

	public void changeTotal() {
		List<ComponentUnit> componentList = (List<ComponentUnit>) tableManager.getInput();
		List<ComponentUnit> units = componentList.stream().filter(comp -> !StringUtil.isEmpty(comp.getComponentId()))
				.collect(Collectors.toList());
		lblTotal.setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ":" + units.size());
	}

	public String getCurrentPositon() {
		return currentPositon;
	}

	public void setCurrentPositon(String currentPositon) {
		this.currentPositon = currentPositon;
	}

	public void addCurrentPosition() {
		if (!StringUtil.isEmpty(currentPositon)) {
			String positon = currentPositon.substring(currentPositon.lastIndexOf("-") + 1);
			String durable = currentPositon.substring(0, currentPositon.lastIndexOf("-"));
			int index = Integer.parseInt(positon) - 1;
			currentPositon = durable + "-" + index;
			if (index < 1) {
				currentPositon = durable + "-" + String.valueOf(count);
			}
		}
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public boolean isAscFlag() {
		return ascFlag;
	}

	public void setAscFlag(boolean ascFlag) {
		this.ascFlag = ascFlag;
	}

	public String getCarrierId() {
		return carrierId;
	}

	public void setCarrierId(String carrierId) {
		this.carrierId = carrierId;
	}

	public Label getLblTotal() {
		return lblTotal;
	}

	public void setLblTotal(Label lblTotal) {
		this.lblTotal = lblTotal;
	}
}
