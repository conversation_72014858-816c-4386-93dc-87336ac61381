package com.glory.mes.wip.lot.multicarrier.unscrap;

import java.util.List;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComponentComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierCompUnscrapComposite extends MultiCarrierComponentComposite {
	
	private static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");
	
	private MultiCarrierUnscrapEditor multiCarrierScrapEditor;
	
	public MultiCarrierCompUnscrapComposite(Composite parent, ADTable adTable, int count, boolean ascFlag,
			boolean checkFlag, boolean showCarrierPositionFlag, boolean showTotalFlag,MultiCarrierUnscrapEditor multiCarrierScrapEditor) {
		super(parent, adTable, count, ascFlag, checkFlag, showCarrierPositionFlag, showTotalFlag);
		this.multiCarrierScrapEditor = multiCarrierScrapEditor;
	}

	public MultiCarrierCompUnscrapComposite(Composite parent, ADTable adTable, int count, boolean ascFlag,
			boolean checkFlag, boolean showCarrierPositionFlag, boolean showTotalFlag) {
		super(parent, adTable, count, ascFlag, checkFlag, showCarrierPositionFlag, showTotalFlag);
	}

	@Override
	public void changeTotal() {
		List<Object> checkedObject = getTableManager().getCheckedObject();
		getLblTotal().setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) +  ":" + checkedObject.size());
	}
	
	@Override
	public void init() {
		super.init();
		tableManagerAddChecklisenner();
	}
	
	public void tableManagerAddChecklisenner() {
		getTableManager().addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				
				if (checked) {
					boolean flag = true;
					String comboScrapCode = multiCarrierScrapEditor.unScrapInfoComposite.comboUnScrapCode.getText();
					String comment = multiCarrierScrapEditor.unScrapInfoComposite.commentText.getText() == null? "":multiCarrierScrapEditor.unScrapInfoComposite.commentText.getText();
					if(comboScrapCode == null
                          || "".equals(comboScrapCode.trim())){
						UI.showWarning(String.format(Message.getString("wip.scrap_code_required"),
                              HEADER_SCRAP_CODE));
						flag = false;
					}
					for (Object object : eventObjects) {
						ComponentUnit compositeUnit = (ComponentUnit) object;
						if (flag) {
							compositeUnit.setActionCode(comboScrapCode);
							compositeUnit.setAttribute1(comment);
						}else {
							getTableManager().getCheckedObject().clear();
							compositeUnit.setActionCode("");
							compositeUnit.setAttribute1("");
							getTableManager().refresh();
						}
					}
				}else {
					for (Object object : eventObjects) {
						((ComponentUnit)object).setActionCode("");
						((ComponentUnit)object).setAttribute1("");
					}
				}
				changeTotal();
			}
		});
	}
	
	/**
	 * 检验是否选中unit
	 * */
	public boolean validat() {
		List<Object> checkedObject = getTableManager().getCheckedObject();
		if (checkedObject != null && checkedObject.size() > 0) {
			return true;
		}
		UI.showError(Message.getString("common.select_record"));
		return false;
	}
	
}
