package com.glory.mes.wip.lot.multicarrier.merge;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComponentAssignComposite;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComponentComposite.DiffType;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;

public class MultiCarrierMergeSection extends EntitySection {

	private MultiCarrierMergeEditor multiCarrierMergeEditor;
	
	public MultiCarrierComponentAssignComposite assignComposite;
	public MultiCarrierQtyMergeComposite qtyComposite;

	protected ToolItem itemMerge;
	protected MultiCarrierMergeComposite targetMultiCarrierLotComposite;
	protected Composite parent;

	public MultiCarrierMergeSection(ADTable adTable) {
		super(adTable);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSortingSplit(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemSortingSplit(ToolBar tBar) {
		itemMerge = new ToolItem(tBar, SWT.PUSH);
		itemMerge.setEnabled(false);
		itemMerge.setText(Message.getString("wip.merge"));
		itemMerge.setImage(SWTResourceCache.getImage("merge-lot"));
		itemMerge.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				mergeAdapter();
			}
		});
	}
	
	protected void mergeAdapter() {
		Lot lot = (Lot) getAdObject();

		if (!LotStateMachine.STATE_WAIT.equals(lot.getState()) && !LotStateMachine.STATE_FIN.equals(lot.getState())) {
			UI.showInfo(Message.getString("error.state_not_allow"));
			return;
		}
		if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
			mergeQty(lot);
		} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
			mergeComponent(lot);
		}
	}

	// QTY类型合批
	@SuppressWarnings("unchecked")
	protected void mergeQty(Lot parentLot) {
		try {
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			DurableManager durableManager = Framework.getService(DurableManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			
			List<LotMultiCarrier> targetLotMultiCarrierList = new ArrayList<LotMultiCarrier>();
			List<LotMultiCarrier> sourceLotMultiCarrierList = new ArrayList<LotMultiCarrier>();

			Lot mergeLot = multiCarrierMergeEditor.targetMultiCarrierLotComposite.getLot();
			// 检查合批规则
			if (!lotManager.checkLotMergeRule(parentLot, Lists.newArrayList(mergeLot)).isSuccess()) {
				UI.showError(Message.getString("wip.merge_vialote_rule"));
				return;
			}
				
			BigDecimal sourceTranQty = BigDecimal.ZERO;
			LotAction lotAction = new LotAction();
			String actionComment = "sourceCarrier:";
			for (LotMultiCarrier lotMultiCarrier : (List<LotMultiCarrier>) qtyComposite.sourceQtyComposite
					.getTableManager().getInput()) {
				if (lotMultiCarrier.getTransQty().compareTo(BigDecimal.ZERO) < 0) {
					UI.showError(lotMultiCarrier.getCarrierId()
							+ Message.getString("wip.remainder_can_not_less_zero"));
					return;
				}
				if (lotMultiCarrier.getTransQty().compareTo(lotMultiCarrier.getCurrentQty()) > 0) {
					UI.showError(lotMultiCarrier.getCarrierId()
							+ Message.getString("wip.remainder_is_bigger_than_current_qty"));
					return;
				}
				LotMultiCarrier sourceLotMultiCarrier = (LotMultiCarrier) lotMultiCarrier.clone();
				sourceLotMultiCarrier.setObjectRrn(lotMultiCarrier.getObjectRrn());
				sourceLotMultiCarrier.setTransQty(lotMultiCarrier.getCurrentQty().subtract(lotMultiCarrier.getTransQty()));
				
				actionComment = actionComment + sourceLotMultiCarrier.getCarrierId() + ";";
				sourceTranQty = sourceTranQty.add(sourceLotMultiCarrier.getTransQty());
				sourceLotMultiCarrierList.add(sourceLotMultiCarrier);
			}

			BigDecimal targetTranQty = BigDecimal.ZERO;
			if (qtyComposite.targetQtyComposite.getTableManager().getInput() != null
					&& qtyComposite.targetQtyComposite.getTableManager().getInput().size() != 0) {
				actionComment = actionComment + "targetCarrier:";
				for (LotMultiCarrier targetLotMultiCarrier : (List<LotMultiCarrier>) qtyComposite.targetQtyComposite
						.getTableManager().getInput()) {
					Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(),
							targetLotMultiCarrier.getCarrierId());
					if (targetCarrier == null) {
						UI.showWarning(Message.getString("mm.carrier_is_not_exist"));
						return;
					}
					
					if (mergeLot.getObjectRrn().equals(targetLotMultiCarrier.getLotRrn())) {
						// 对于要批次原载具保证操作数量不大于当前数量
						if (targetLotMultiCarrier.getTransQty().compareTo(targetLotMultiCarrier.getCurrentQty()) > 0) { 
							UI.showError(targetLotMultiCarrier.getCarrierId()
									+ Message.getString("wip.split_qty_more_than_carrier_avaliable_qty"));
							return;
						}
						if (targetLotMultiCarrier.getTransQty().compareTo(targetLotMultiCarrier.getCurrentQty()) < 0) {
							actionComment = actionComment + targetLotMultiCarrier.getCarrierId() + ";";
						}
					} else {
						if (targetLotMultiCarrier.getTransQty().compareTo(BigDecimal.ZERO) == 0) {
							continue;
						}
						// 对于不在批次内的载具保证操作数量不大于可用数量
						if (targetLotMultiCarrier.getTransQty().compareTo(targetCarrier.getCapacity().subtract(targetLotMultiCarrier.getCurrentQty())) > 0) { 
							UI.showError(targetLotMultiCarrier.getCarrierId()
									+ Message.getString("wip.merge_qty_more_than_carrier_avaliable_qty"));
							return;
						}
						actionComment = actionComment + targetLotMultiCarrier.getCarrierId() + ";";
					}
					
					targetTranQty = targetTranQty.add(targetLotMultiCarrier.getTransQty());
					targetLotMultiCarrierList.add(targetLotMultiCarrier);
				}
				if (targetTranQty.compareTo(mergeLot.getMainQty()) != 0) {
					UI.showError(Message.getString("wip.merge_qty_not_equal"));
					return;
				}
			} else {
				if (sourceTranQty.compareTo(mergeLot.getMainQty()) != 0) {
					UI.showError(Message.getString("wip.merge_qty_not_equal"));
					return;
				}
				targetLotMultiCarrierList = sourceLotMultiCarrierList;
			}

			QtyUnit qtyUnit = new QtyUnit();
			qtyUnit.setMainQty(sourceTranQty);

			lotAction.setActionComment(actionComment);
			parentLot = carrierLotManager.mergeMultiCarrierLotQty(parentLot, mergeLot, Lists.newArrayList(qtyUnit), lotAction, sourceLotMultiCarrierList, targetLotMultiCarrierList, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.merge_success"));
			setAdObject(parentLot);
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// COMPONENT类型合批
	@SuppressWarnings("unchecked")
	protected void mergeComponent(Lot parentLot) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			Map<String, List<ComponentUnit>> sourceComponentUnitMap = new LinkedHashMap<String, List<ComponentUnit>>();
			Map<String, List<ComponentUnit>> targetComponentUnitMap = new LinkedHashMap<String, List<ComponentUnit>>();

			List<ComponentUnit> addComponents = assignComposite.targetComponentComposite.getComponentsDiff()
					.get(DiffType.ADD);
			Lot mergeLot = multiCarrierMergeEditor.targetMultiCarrierLotComposite.getLot();
			// 检查合批规则
			if (!lotManager.checkLotMergeRule(parentLot, Lists.newArrayList(mergeLot)).isSuccess()) {
				UI.showError(Message.getString("wip.merge_vialote_rule"));
				return;
			}
			if (addComponents == null || addComponents.size() == 0) {
				// 没有目标载具则直接把选中的分成一个子批
				addComponents = new ArrayList<ComponentUnit>();
				List<Object> checkedlist = assignComposite.sourceComponentComposite.getTableManager()
						.getCheckedObject();
				if (checkedlist == null || checkedlist.isEmpty()) {
					UI.showError(Message.getString("wip.need_add_assign_component"));
					return;
				}

				// 必选整个载具全部选择才允许合批
				List<ComponentUnit> inputlist = (List<ComponentUnit>) assignComposite.sourceComponentComposite
						.getTableManager().getInput();
				if (checkedlist.size() != inputlist.size() || BigDecimal.valueOf(checkedlist.size()).compareTo(mergeLot.getMainQty()) != 0) {
					UI.showError(Message.getString("wip.merge_qty_not_equal"));
					return;
				}
				for (Object object : checkedlist) {
					ComponentUnit component = (ComponentUnit) object;
					if (component.getDurable() != null && !"".equals(component.getDurable())) {
						if (sourceComponentUnitMap.containsKey(component.getDurable())) {
							List<ComponentUnit> durableComponents = sourceComponentUnitMap.get(component.getDurable());
							durableComponents.add(component);
							sourceComponentUnitMap.put(component.getDurable(), durableComponents);
						} else {
							List<ComponentUnit> durableComponents = new ArrayList<ComponentUnit>();
							durableComponents.add(component);
							sourceComponentUnitMap.put(component.getDurable(), durableComponents);
						}
					}
				}
				targetComponentUnitMap = sourceComponentUnitMap;
			} else {
				// 原有载具与comp列表
				sourceComponentUnitMap = assignComposite.sourceComponentComposite.getSourceComponents();

				// 目标载具与comp列表
				for (ComponentUnit component : addComponents) {
					if (component.getDurable() != null && !"".equals(component.getDurable())) {
						if (targetComponentUnitMap.containsKey(component.getDurable())) {
							List<ComponentUnit> durableComponents = targetComponentUnitMap.get(component.getDurable());
							durableComponents.add(component);
							targetComponentUnitMap.put(component.getDurable(), durableComponents);
						} else {
							List<ComponentUnit> durableComponents = new ArrayList<ComponentUnit>();
							durableComponents.add(component);
							targetComponentUnitMap.put(component.getDurable(), durableComponents);
						}
					}
				}
				// 合批必须comp都到期
				if (BigDecimal.valueOf(addComponents.size()).compareTo(mergeLot.getMainQty()) != 0) {
					UI.showError(Message.getString("wip.merge_qty_not_equal"));
					return;
				}
			}
			
			parentLot = carrierLotManager.mergeMultiCarrierLotComp(parentLot, mergeLot, sourceComponentUnitMap, targetComponentUnitMap, true,
					Env.getSessionContext());
			UI.showInfo(Message.getString("wip.merge_success"));
			setAdObject(parentLot);
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void refresh() {
		Lot  lot = (Lot)getAdObject();
		multiCarrierMergeEditor.sourceMultiCarrierLotComposite.getLotMultiCarriers(lot.getLotId());
		multiCarrierMergeEditor.changeSourceLot(multiCarrierMergeEditor.sourceMultiCarrierLotComposite, lot.getLotId());
	}
 
	@Override
	protected void createSectionContent(Composite parent) {
		try {
			this.parent = parent;
			itemMerge.setEnabled(false);
			if (getAdObject() != null) {
				Lot  lot = (Lot)getAdObject();
				if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
					qtyComposite = new MultiCarrierQtyMergeComposite(parent, SWT.NONE, true, true);
				} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())){
					assignComposite = new MultiCarrierComponentAssignComposite(parent, SWT.NONE, true, true);
				}
				itemMerge.setEnabled(true);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public MultiCarrierMergeEditor getMultiCarrierMergeEditor() {
		return multiCarrierMergeEditor;
	}

	public void setMultiCarrierMergeEditor(MultiCarrierMergeEditor multiCarrierMergeEditor) {
		this.multiCarrierMergeEditor = multiCarrierMergeEditor;
	}

	public MultiCarrierComponentAssignComposite getAssignComposite() {
		return assignComposite;
	}

	public void setAssignComposite(MultiCarrierComponentAssignComposite assignComposite) {
		this.assignComposite = assignComposite;
	}

	public MultiCarrierQtyMergeComposite getQtyComposite() {
		return qtyComposite;
	}

	public void setQtyComposite(MultiCarrierQtyMergeComposite qtyComposite) {
		this.qtyComposite = qtyComposite;
	}

	public void reflow() {
		if (assignComposite != null && !assignComposite.isDisposed()) {
			assignComposite.dispose();
			assignComposite = null;
		}
		if (qtyComposite != null && !qtyComposite.isDisposed()) {
			qtyComposite.dispose();
			qtyComposite = null;
		}
		createSectionContent(parent);
		parent.layout();
	}
}
