package com.glory.mes.wip.lot.multicarrier;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;

public class MultiCarrierComposite extends Composite {

	private static final Logger logger = Logger.getLogger(MultiCarrierComposite.class);

	private static final String TABLE_NAME = "LotMultiCarrier";
	private static final String LOT_TABLE_NAME = "WIPLotByMultiCarrier";

	public boolean checkFlag;
	public boolean showLotFlag;
	private boolean showDetailFlag = false;
	protected boolean showOperatorFlag = false;
	protected boolean showLotIdFirstFlag;

	public ListTableManager lotTableManager;
	protected EntityForm lotDetailsForm;
	protected String lblCarrier;
	protected Text txtCarrierId;
	protected Text txtLotId;
	protected Text txtOperator;

	protected int tableHeigthHint = 260;

	public MultiCarrierComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style);
		this.checkFlag = checkFlag;
		this.showLotFlag = showLotFlag;
		this.showDetailFlag = showDetailFlag;
		this.showOperatorFlag = showOperatorFlag;
	}
	
	public MultiCarrierComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag, boolean showLotIdFirstFlag, int tableHeigthHint) {
		super(parent, style);
		this.checkFlag = checkFlag;
		this.showLotFlag = showLotFlag;
		this.showDetailFlag = showDetailFlag;
		this.showOperatorFlag = showOperatorFlag;
		this.showLotIdFirstFlag = showLotIdFirstFlag;
		this.tableHeigthHint = tableHeigthHint;
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite carrierComposite = new Composite(this, SWT.NONE);
			int gridY = 2;

			if (showLotFlag) {
				gridY += 2;
			}
			if (showOperatorFlag) {
				gridY += 2;
			}

			carrierComposite.setLayout(new GridLayout(gridY, false));
			
			GridData gText = new GridData();
			gText.widthHint = 150;
			
			if (showLotIdFirstFlag) {
				createtxtLotId(carrierComposite, gText);
				createtxtCarrierId(carrierComposite, gText);
			} else {
				createtxtCarrierId(carrierComposite, gText);
				createtxtLotId(carrierComposite, gText);
			}
			
			if (showOperatorFlag) {
				Label lblOperatorId = new Label(carrierComposite, SWT.NONE);
				lblOperatorId.setText(Message.getString("wip.operator"));
				lblOperatorId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtOperator = new Text(carrierComposite, SWT.BORDER);
				txtOperator.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtOperator.setLayoutData(gText);
				txtOperator.setTextLimit(32);

				txtOperator.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {

					}

				});
				txtOperator.addFocusListener(new FocusListener() {
					public void focusGained(FocusEvent e) {
					}

					public void focusLost(FocusEvent e) {
					}
				});
			}

			Composite lotComposite = new Composite(this, SWT.NONE);
			lotComposite.setLayout(new GridLayout(1, false));

			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
			gridData.heightHint = tableHeigthHint;
			lotComposite.setLayoutData(gridData);
			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable CarrierTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), LOT_TABLE_NAME);
			lotTableManager = new ListTableManager(CarrierTable, checkFlag);
			lotTableManager.setAutoSizeFlag(true);
			lotTableManager.setIndexFlag(true);
			lotTableManager.newViewer(lotComposite);

			if (showDetailFlag) {
				Composite lotDetailsComposite = new Composite(this, SWT.NONE);
				lotDetailsComposite.setLayout(new GridLayout(1, false));
				lotDetailsForm = new EntityForm(lotDetailsComposite, SWT.NONE, lotTable, null);
			}
		} catch (Exception e) {
			logger.error("StepTreeView createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void createtxtCarrierId(Composite carrierComposite, GridData gText) {
		Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
		if (StringUtil.isEmpty(lblCarrier)) {
			lblCarrierId.setText(Message.getString("wip.durable"));
		} else {
			lblCarrierId.setText(lblCarrier);
		}
		lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

		txtCarrierId = new Text(carrierComposite, SWT.BORDER);
		txtCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		txtCarrierId.setLayoutData(gText);
		txtCarrierId.setTextLimit(32);
		
		txtCarrierId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				// 回车事件
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					String carrierId = ((Text) event.widget).getText();
					if (!StringUtil.isEmpty(carrierId)) {
						getLotsByCarrierId(carrierId);
					}
				}
			}
		});
	}
	
	public void createtxtLotId(Composite carrierComposite, GridData gText) {
		if (showLotFlag) {
			Label lblLotId = new Label(carrierComposite, SWT.NONE);
			lblLotId.setText(Message.getString("wip.lot_id"));
			lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

			txtLotId = new Text(carrierComposite, SWT.BORDER);
			txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtLotId.setLayoutData(gText);
			txtLotId.setTextLimit(64);

			txtLotId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// 回车事件
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String lotId = ((Text) event.widget).getText();
						if (!StringUtil.isEmpty(lotId)) {
							getLotMultiCarriers(lotId);
						}
					}
				}
			});
		}
	}

	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);

			Lot lot = null;

			if (carrier != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				String whereClause = " carrierId = '" + carrierId + "'";
				List<LotMultiCarrier> lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
						Integer.MAX_VALUE, whereClause, null);
				if (lotMultiCarriers.size() > 0) {
					LotMultiCarrier lotMultiCarrier = lotMultiCarriers.get(0);

					lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(),
							LotMultiCarrier.class, Integer.MAX_VALUE, " lotRrn = " + lotMultiCarrier.getLotRrn(), null);
					lotTableManager.setInput(lotMultiCarriers);

					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					lotTableManager.refresh();

					LotManager lotManager = Framework.getService(LotManager.class);
					lot = lotManager.getLot(lotMultiCarrier.getLotRrn());
				} else {
					lotTableManager.setInput(new ArrayList<LotMultiCarrier>());
					lotTableManager.refresh();
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				}
			} else {
				lotTableManager.setInput(new ArrayList<LotMultiCarrier>());
				lotTableManager.refresh();
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}

			if (showLotFlag) {
				txtLotId.setText(lot != null && lot.getLotId() != null ? lot.getLotId() : "");
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
			}

			if (lotDetailsForm != null) {
				lotDetailsForm.setObject(lot != null ? lot : new Lot());
				lotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void getLotMultiCarriers(String lotId) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);

			Lot lot = LotProviderEntry.getLot(lotId);
			if (lot != null) {
				String whereClause = " lotRrn = " + lot.getObjectRrn();
				List<LotMultiCarrier> LotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
						Integer.MAX_VALUE, whereClause, null);
				lotTableManager.setInput(LotMultiCarriers);
				lotTableManager.refresh();
				if (showLotFlag) {
					txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				}
			} else {
				lotTableManager.setInput(new ArrayList<LotMultiCarrier>());
				lotTableManager.refresh();
				if (showLotFlag) {
					txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				}
			}

			if (lotDetailsForm != null) {
				lotDetailsForm.setObject(lot != null ? lot : new Lot());
				lotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh() {
		try {
			Lot lot = (Lot) lotDetailsForm.getObject();
			if (lot != null) {
				// 更新列表
				List<Object> checkedList = lotTableManager.getCheckedObject();
				Map<Long, LotMultiCarrier> checkedMap = checkedList.stream()
						.map(o -> ((LotMultiCarrier)o))
						.collect(Collectors.toMap(LotMultiCarrier::getObjectRrn, o -> o));
				ADManager adManager = Framework.getService(ADManager.class);
				String whereClause = " lotRrn = " + lot.getObjectRrn();
				List<LotMultiCarrier> lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
						Integer.MAX_VALUE, whereClause, null);
				for (LotMultiCarrier multiCarrier : lotMultiCarriers) {
					lotTableManager.update(multiCarrier);
					
					if (checkedMap.containsKey(multiCarrier.getObjectRrn())) {
						checkedMap.remove(multiCarrier.getObjectRrn());
					}
				}
				
				if (MapUtils.isNotEmpty(checkedMap)) {
					for (LotMultiCarrier multiCarrier : checkedMap.values()) {
						lotTableManager.remove(multiCarrier);
					}
				}
				
				// 更新批次表单
				lot = (Lot) adManager.getEntity(lot);
				lotDetailsForm.setObject(lot);
				lotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public Text getTxtCarrierId() {
		return txtCarrierId;
	}

	public void setTxtCarrierId(Text txtCarrierId) {
		this.txtCarrierId = txtCarrierId;
	}

	public Text getTxtLotId() {
		return txtLotId;
	}

	public void setTxtLotId(Text txtLotId) {
		this.txtLotId = txtLotId;
	}

	public ListTableManager getLotTableManager() {
		return lotTableManager;
	}

	public void setLotTableManager(ListTableManager lotTableManager) {
		this.lotTableManager = lotTableManager;
	}

	public String getLblCarrier() {
		return lblCarrier;
	}

	public void setLblCarrier(String lblCarrier) {
		this.lblCarrier = lblCarrier;
	}

	public int getTableHeigthHint() {
		return tableHeigthHint;
	}

	public void setTableHeigthHint(int tableHeigthHint) {
		this.tableHeigthHint = tableHeigthHint;
	}

	public Text getTxtOperator() {
		return txtOperator;
	}

	public void setTxtOperator(Text txtOperator) {
		this.txtOperator = txtOperator;
	}
}
