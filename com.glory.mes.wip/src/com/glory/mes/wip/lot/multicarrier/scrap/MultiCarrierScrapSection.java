package com.glory.mes.wip.lot.multicarrier.scrap;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.comp.ComponentAssignComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;

public class MultiCarrierScrapSection extends EntitySection {
	

	private static final Logger logger = Logger.getLogger(MultiCarrierScrapSection.class);
	
	protected static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");
	
	private MultiCarrierScrapEditor multiCarrierScrapEditor;
	public ComponentAssignComposite assignComposite;
	protected ManagedForm managedForm;

	protected ToolItem itemSortingSplit;
	protected ToolItem itemSortingSplitAndMerge;
	protected MultiCarrierCompScrapComposite multiCarrierComponentComposite;
	protected MultiCarrierQtyScrapComposite multiCarrierQtyComposite;
	
	protected ToolItem itemScrap;
	
	protected ScrolledForm sForm;
	protected Composite client;
	
	protected Lot lot;
	
	protected List<LotMultiCarrier> lotMultiCarriers;
	
	private static final String  TABLE_NAME_COMPTYPE = "WIPLotScrapunitlByMultiCarrier";
	private static final String  TABLE_NAME_QTYTYPE = "WIPLotScrapqtyByMultiCarrier";

	public MultiCarrierScrapSection(ADTable adTable,MultiCarrierScrapEditor multiCarrierScrapEditor) {
		super(adTable);
		this.multiCarrierScrapEditor = multiCarrierScrapEditor;
	}
	

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemScrap(ToolBar tBar) {
		itemScrap = new ToolItem(tBar, SWT.PUSH);
		itemScrap.setEnabled(false);
		itemScrap.setText(Message.getString("wip.scrap_lotcn"));
		itemScrap.setImage(SWTResourceCache.getImage("scrap-lot"));
		itemScrap.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				scrapAdapter(event);
			}
		});
	}
	
	/**
	 * 报废根据批次unit类型使用不同的报废方法
	 * */
	protected void scrapAdapter(SelectionEvent event) {
		
		if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
			scrapComponentTypeAdapter();
		}else {
			scrapQtyUnitTypeAdapter();
		}
	}
	
	
	/**
	 * 报废ComponentUnit类型
	 * */
	protected void scrapComponentTypeAdapter() {
		try {
			if (multiCarrierComponentComposite.validat()) {
				//获取到要报废的组件
				List<Object> checkedObject = multiCarrierComponentComposite.getTableManager().getCheckedObject();
				//载具=组件列表
				Map<String, List<ComponentUnit>> carrierIdMap = new HashMap<String,List<ComponentUnit>>();
				for (Object obj : checkedObject) {
					if (obj instanceof ComponentUnit) {
						ComponentUnit cu = (ComponentUnit)obj;
						if (carrierIdMap.containsKey(cu.getDurable())) {
							List<ComponentUnit> units = carrierIdMap.get(cu.getDurable());
							units.add(cu);
							carrierIdMap.put(cu.getDurable(), units);
						}else {
							List<ComponentUnit> newUnits = new ArrayList<ComponentUnit>();
							newUnits.add(cu);
							carrierIdMap.put(cu.getDurable(), newUnits);
						}
						
					}
				}
				//最终被报废的
				List<LotMultiCarrier> scrapLotMultiCarriers = new ArrayList<>();
				for (LotMultiCarrier lotMultiCarrier : lotMultiCarriers) {
					if (carrierIdMap.containsKey(lotMultiCarrier.getCarrierId())) {
						List<ComponentUnit> units = carrierIdMap.get(lotMultiCarrier.getCarrierId());
						lotMultiCarrier.setTransQty(new BigDecimal(units.size()));
						scrapLotMultiCarriers.add(lotMultiCarrier);
					}
				}
				
				List<ComponentUnit> scrapUnits =new ArrayList<ComponentUnit>();
				for (Object object : checkedObject) {
					if (object instanceof ComponentUnit) {
						ComponentUnit cu =(ComponentUnit)object;
						scrapUnits.add(cu);
					}
				}
				// Map<String, List<ProcessUnit>> lotActionsMap = new HashMap<String, List<ProcessUnit>>();
				// Map<String,String> positionMap = new HashMap<String,String>();
				// for (ComponentUnit unit : scrapUnits) {
	            	//代码;备注原因  两个条件拼成一个key
	            	/*String actionInfo = unit.getActionCode() + ";" + unit.getAttribute1();
	            	if (lotActionsMap.containsKey(actionInfo)) {
	                     List<ProcessUnit> units = lotActionsMap.get(actionInfo);
	                     units.add(unit);
	                     lotActionsMap.put(actionInfo, units);
	            	} else {
	                     List<ProcessUnit> units = new ArrayList<ProcessUnit>();
	                     units.add(unit);
	                     lotActionsMap.put(actionInfo, units);
	                }*/
					// 报废时记录原来绑定的载具
					// positionMap.put(unit.getComponentId(), unit.getDurable() + "-" + unit.getPosition());
	             //}
	             Map<String,String> positionMap = new HashMap<String,String>();
	             List<LotAction> scrapLotActions = new ArrayList<LotAction>();
	             for (ComponentUnit unit : scrapUnits) {
	            	 LotAction lotAction = new LotAction();
	                 lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
	                 lotAction.setLotRrn(lot.getObjectRrn());
	                 String comment = unit.getAttribute1() == null ? "" : unit.getAttribute1().toString();
	                 lotAction.setActionComment(comment);
	                 lotAction.setActionCode(unit.getActionCode());
	                 lotAction.setActionUnits(Lists.newArrayList(unit));
                	 
	                 scrapLotActions.add(lotAction);
	                 positionMap.put(unit.getComponentId(), unit.getDurable() + "-" + unit.getPosition());
	             }
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				lot = carrierLotManager.scrapMultiCarrierLot(lot, scrapLotActions, new LotAction(), positionMap, scrapLotMultiCarriers, Env.getSessionContext());
				reload(scrapLotMultiCarriers);
				UI.showInfo(Message.getString("wip.scrapLot_success"));
			}
		} catch (Exception e) {
			logger.error("Error at MultiCarrierScrapSection.scrapComponentTypeAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	/**
	 * 报废QTYUnit类型
	 * */
	protected void scrapQtyUnitTypeAdapter() {
		try {
			if (multiCarrierQtyComposite.validat()) {
				List<LotMultiCarrier> inputList = (List<LotMultiCarrier>)multiCarrierQtyComposite.getTableManager().getInput();
				List<LotMultiCarrier> lotMultiCarriers=new ArrayList<LotMultiCarrier>();
				
				if (inputList != null && inputList.size() > 0) {
					Map<String,List<ProcessUnit>> unitMap = new HashMap<String,List<ProcessUnit>>();
					for (LotMultiCarrier lotMultiCarrier : inputList) {
						QtyUnit qtyUnit = new QtyUnit();
						qtyUnit.setMainQty(lotMultiCarrier.getTransQty());
						String actionInfo=lotMultiCarrier.getActionCode()+";"+lotMultiCarrier.getActionComments();
						if (unitMap.containsKey(actionInfo)) {
		                     List<ProcessUnit> units = unitMap.get(actionInfo);
		                     units.add(qtyUnit);
		                     unitMap.put(actionInfo, units);
		                 } else {
		                     List<ProcessUnit> units = new ArrayList<ProcessUnit>();
		                     units.add(qtyUnit);
		                     unitMap.put(actionInfo, units);
		                 }
						lotMultiCarriers.add(lotMultiCarrier);
					}
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
		             for (String key : unitMap.keySet()) {
		            	 LotAction lotAction = new LotAction();
		                 lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
		                 lotAction.setLotRrn(lot.getObjectRrn());
		                 String[] codeAndComment = key.split(";");
		                 lotAction.setActionCode(codeAndComment[0]);
		                 lotAction.setActionUnits(unitMap.get(key));
		                 if (codeAndComment.length > 1) {
		                	 //有备注
		                	 lotAction.setActionComment(codeAndComment[1]);
		                 }else {
		                	 lotAction.setActionComment("");
		                 }
		                 scrapLotActions.add(lotAction);
		             }
		             CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
		             lot= carrierLotManager.scrapMultiCarrierLot(lot, scrapLotActions, new LotAction(), new HashMap<String,String>(), lotMultiCarriers, Env.getSessionContext());
		             UI.showInfo(Message.getString("wip.scrapLot_success"));
		             reload(lotMultiCarriers);
				}
			}
			
		} catch (Exception e) {
			logger.error("Error at MultiCarrierScrapSection.scrapQtyUnitTypeAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	

	@Override
	protected void createSectionContent(Composite parent) {
		try {
			this.client = parent;
			FormToolkit toolkit = form.getToolkit();
			sForm = toolkit.createScrolledForm(client);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			managedForm = new ManagedForm(toolkit, sForm);
			Composite body = sForm.getForm().getBody();
			configureBody(body);
			if (lot != null) {
				buildComposite(lot.getSubUnitType(),body);
			}
			
		} catch (Exception e) {
			logger.error("Error at MultiCarrierScrapSection.createSectionContent()",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void buildComposite(String unitType,Composite body) {
		try {
			
			ADManager adManager = Framework.getService(ADManager.class);
			if (Lot.UNIT_TYPE_COMPONENT.equals(unitType)) {
				
				ADTable compAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_COMPTYPE);
				multiCarrierComponentComposite = new MultiCarrierCompScrapComposite(body, compAdTable, 25, true, true, false, true,this.multiCarrierScrapEditor);
				multiCarrierComponentComposite.init();
			}else {
				ADTable qtyAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_QTYTYPE);
				multiCarrierQtyComposite = new MultiCarrierQtyScrapComposite(body, qtyAdTable, true, false, true, true, multiCarrierScrapEditor);
				multiCarrierQtyComposite.init();
			}
			itemScrap.setEnabled(true);
		} catch (Exception e) {
			logger.error("Error at MultiCarrierScrapSection.buildComposite(String, Composite)",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	public void reflow() {
		if (sForm != null && !sForm.isDisposed()) {
			sForm.dispose();
			sForm = null;
		}
		createSectionContent(client);
		client.layout();
	}
	
	public void setLotObject(Lot lot) {
		
		this.lot = lot;
	}
	
	/**
	 * 根据批次类型创建右侧列表数据
	 * 
	 * */
	public void buildScrapUnitByLotUnitType(List<LotMultiCarrier> lotMultiCarriers){
		this.lotMultiCarriers=lotMultiCarriers;
		if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
			buildScrapCompUnit(lotMultiCarriers);
		}else {
			buildScrapQty(lotMultiCarriers);
		}
	}
	
	
	
	/**
	 * 
	 * 创建按片报废列表
	 * */
	private void buildScrapCompUnit(List<LotMultiCarrier> lotMultiCarriers) {
		try {
			List<ComponentUnit> subProcessUnit = new ArrayList<ComponentUnit>();
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			for (LotMultiCarrier lotMultiCarrier : lotMultiCarriers) {
				List<ComponentUnit> validComponentByCarrierId = carrierLotManager.getValidComponentByCarrierId(Env.getOrgRrn(), lotMultiCarrier.getCarrierId());
				subProcessUnit.addAll(validComponentByCarrierId);
			}
			for (ComponentUnit componentUnit : subProcessUnit) {
				componentUnit.setActionCode(null);
			}
			multiCarrierComponentComposite.getTableManager().setInput(subProcessUnit);
		} catch (Exception e) {
			logger.error("Error at MultiCarrierScrapSection.buildScrapCompUnit(List<LotMultiCarrier>)",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 创建按批次数量报废列表
	 * */
	private void buildScrapQty(List<LotMultiCarrier> lotMultiCarriers) {
		try {
			multiCarrierQtyComposite.getTableManager().setInput(lotMultiCarriers);
		} catch (Exception e) {
			logger.error("Error at MultiCarrierScrapSection.buildScrapQty(List<LotMultiCarrier>)",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public MultiCarrierScrapEditor getMultiCarrierScrapEditor() {
		return multiCarrierScrapEditor;
	}

	public void setMultiCarrierScrapEditor(MultiCarrierScrapEditor multiCarrierScrapEditor) {
		this.multiCarrierScrapEditor = multiCarrierScrapEditor;
	}
	
	public void reload(List<LotMultiCarrier> multiCarriers) {
		multiCarrierScrapEditor.multiCarrierComposite.refresh();
		
		ListTableManager tableManager = multiCarrierScrapEditor
				.multiCarrierComposite.getLotTableManager();
		buildScrapUnitByLotUnitType((List)tableManager.getCheckedObject());
	}
	
	/*public void reload() {
		multiCarrierScrapEditor.multiCarrierComposite.getLotMultiCarriers(lot.getLotId());
		reflow();
	}*/

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;		
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
