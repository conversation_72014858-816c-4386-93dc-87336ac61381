package com.glory.mes.wip.lot.multicarrier.changecarrier;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;

public class MultiCarrierChangeCarrierEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.multicarrier.changecarrier.MultiCarrierChangeCarrierEditor";

	private MultiCarrierChangeCarrierSection section;

	protected MultiCarrierChangeCarrierComposite sourceMultiCarrierLotComposite;
	protected MultiCarrierChangeCarrierTargetComposite targetMultiCarrierLotComposite;
	protected ListTableManager targetCarrierTableManager;

	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected MPart mPart;

	@PostConstruct
	public void postConstruct(Composite parent) {
		try {
			configureBody(parent);

			FormToolkit toolkit = new FormToolkit(parent.getDisplay());
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_BOTH));
			ManagedForm mform = new ManagedForm(toolkit, form);

			Composite body = mform.getForm().getBody();
			GridLayout layout = new GridLayout(2, false);
			body.setLayout(layout);
			body.setLayoutData(new GridData(GridData.FILL_VERTICAL));
			// body.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));

			createLeftViewContent(body, toolkit);
			createRightSectionContent(body, toolkit, mform);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createLeftViewContent(Composite parent, FormToolkit toolkit) {
		Composite left = toolkit.createComposite(parent, SWT.NONE);
		GridData gdLeft = new GridData(GridData.FILL_VERTICAL);
//		gdLeft.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 3;
		left.setLayout(new GridLayout(1, false));
		left.setLayoutData(gdLeft);

		// left sourceCarrierComposite
		sourceMultiCarrierLotComposite = new MultiCarrierChangeCarrierComposite(left, SWT.BORDER, true, true, true, false);
		sourceMultiCarrierLotComposite.setLblCarrier(Message.getString("wip.source_carrier_id"));
		sourceMultiCarrierLotComposite.createPartControl();
		Text sourceCarrierId = sourceMultiCarrierLotComposite.getTxtCarrierId();
		sourceCarrierId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					changeSourceLot(sourceMultiCarrierLotComposite.getTxtLotId().getText());
				}
			}
		});
		Text sourceLotId = sourceMultiCarrierLotComposite.getTxtLotId();
		sourceLotId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					changeSourceLot(((Text) event.widget).getText());
				}
			}
		});

		ListTableManager sourceCarrierTableManager = sourceMultiCarrierLotComposite.getLotTableManager();
		sourceCarrierTableManager.addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (null == eventObjects || eventObjects.isEmpty()) {
					return;
				}
				for (Object eventObject : eventObjects) {
					LotMultiCarrier lotMultiCarrier = (LotMultiCarrier) eventObject;
					lotMultiCarrierCheckedAdapter(lotMultiCarrier, checked);
				}
			}
		});

		// left targetCarrierComposite
		targetMultiCarrierLotComposite = new MultiCarrierChangeCarrierTargetComposite(left, SWT.BORDER, false, false, false, false);
		targetMultiCarrierLotComposite.setLblCarrier(Message.getString("wip.new_durable"));
		targetMultiCarrierLotComposite.createPartControl();
		targetMultiCarrierLotComposite.getTxtCarrierId().setEnabled(false);

		Text txtTargetCarrierId = targetMultiCarrierLotComposite.getTxtCarrierId();
		txtTargetCarrierId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					String targetCarrierId = ((Text) event.widget).getText();
					Lot lot = (Lot) section.getAdObject();
					Carrier carrier = null;
					if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
						section.qtyAssignComposite.sourceLot = lot;
						carrier = section.qtyAssignComposite.searchCarrier(targetCarrierId,
								section.qtyAssignComposite.targetQtyComposite, true, false, true);
					} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
						section.assignComposite.sourceLot = lot;
						carrier = section.assignComposite.searchCarrier(targetCarrierId,
								section.assignComposite.targetComponentComposite, true, true);
					}

					if (carrier == null) {
						targetMultiCarrierLotComposite.removeCarrier(targetCarrierId);
					}
				}
			}
		});
		targetCarrierTableManager = targetMultiCarrierLotComposite.getLotTableManager();
		targetCarrierTableManager.addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				// 双击移除一行
				LotMultiCarrier lotMultiCarrier = (LotMultiCarrier) targetCarrierTableManager.getSelectedObject();
				Lot lot = (Lot) section.getAdObject();
				boolean removeFlag = true;
				if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
					section.qtyAssignComposite.removeCarrier(lotMultiCarrier.getCarrierId(),
							section.qtyAssignComposite.targetQtyComposite);
				} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
					removeFlag = section.assignComposite.removeCarrier(lotMultiCarrier.getCarrierId(),
							section.assignComposite.targetComponentComposite);
				}
				if (removeFlag) {
					targetCarrierTableManager.getInput().remove(lotMultiCarrier);
				}
			}
		});
	}

	protected void createRightSectionContent(Composite parent, FormToolkit toolkit, ManagedForm mform) {
		ADTable adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		Composite right = toolkit.createComposite(parent, SWT.NONE);
		right.setLayout(new GridLayout(1, false));
		right.setLayoutData(new GridData(GridData.FILL_BOTH));
		// right.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));
		createSection(adTable);
		section.createContents(mform, right);
		section.setMultiCarrierChangeCarrierEditor(this);
		mPart.setLabel(Message.getString("wip.bylot.multiCarrier_change"));
	}

	protected void changeSourceLot(String sourceLotId) {
		// 清除left targetCarrierComposite内容
		if (targetMultiCarrierLotComposite != null) {
			targetMultiCarrierLotComposite.getTxtCarrierId().setText("");
			targetMultiCarrierLotComposite.getLotTableManager().setInput(new ArrayList<LotMultiCarrier>());
		}

		try {
			Lot lot = LotProviderEntry.getLot(sourceLotId);
			if (lot != null) {
				targetMultiCarrierLotComposite.getTxtCarrierId().setEnabled(true);
			} else {
				targetMultiCarrierLotComposite.getTxtCarrierId().setEnabled(false);
			}
			section.setAdObject(lot);
			section.reflow();
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	protected void lotMultiCarrierCheckedAdapter(LotMultiCarrier lotMultiCarrier, boolean checked) {
		try {
			// 选择批次 显示 载具
			Lot lot = (Lot) section.getAdObject();
			if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
				// 设置源批次
				section.qtyAssignComposite.sourceLot = lot;
				if (checked) {
					section.qtyAssignComposite.searchCarrier(lotMultiCarrier.getCarrierId(),
							section.qtyAssignComposite.sourceQtyComposite, true, true, true);
				} else {
					section.qtyAssignComposite.removeCarrier(lotMultiCarrier.getCarrierId(),
							section.qtyAssignComposite.sourceQtyComposite);
				}
				section.qtyAssignComposite.sourceQtyComposite.getTableManager().refresh();
			} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
				// 设置源批次
				section.assignComposite.sourceLot = lot;
				if (checked) {
					section.assignComposite.searchCarrier(lotMultiCarrier.getCarrierId(),
							section.assignComposite.sourceComponentComposite, true, true);
				} else {
					boolean removeFlag = section.assignComposite.removeCarrier(lotMultiCarrier.getCarrierId(),
							section.assignComposite.sourceComponentComposite);
					if (!removeFlag) {
						sourceMultiCarrierLotComposite.getLotTableManager().setCheckedObject(lotMultiCarrier);
					}
				}
				section.assignComposite.sourceComponentComposite.checkedAll();
				section.assignComposite.sourceComponentComposite.getTableManager().refresh();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	protected void createSection(ADTable adTable) {
		section = new MultiCarrierChangeCarrierSection(adTable);
	}

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

}
