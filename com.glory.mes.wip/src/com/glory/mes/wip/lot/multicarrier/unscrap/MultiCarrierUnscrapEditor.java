package com.glory.mes.wip.lot.multicarrier.unscrap;

import java.awt.Toolkit;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;

public class MultiCarrierUnscrapEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.multicarrier.unscrap.MultiCarrierUnscrapEditor";

	private MultiCarrierUnscrapSection section;

	public MultiCarrierUnscrapComposite multiCarrierComposite;
	public UnScrapInfoComposite unScrapInfoComposite;

	@Inject
	protected ESelectionService selectionService;

	@Inject	
	protected MPart mPart;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		try {
			ADTable adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
			configureBody(parent);
			FormToolkit toolkit = new FormToolkit(parent.getDisplay());
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_BOTH));
			ManagedForm mform = new ManagedForm(toolkit, form);

			Composite body = mform.getForm().getBody();
			GridLayout layout = new GridLayout(2, false);
			body.setLayout(layout);
			body.setLayoutData(new GridData(GridData.FILL_VERTICAL));

			Composite left = toolkit.createComposite(body, SWT.NONE);
			GridData gdLeft = new GridData(GridData.FILL_VERTICAL);
//			gdLeft.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 3;
			left.setLayout(new GridLayout(1, false));
			left.setLayoutData(gdLeft);
			// left multiCarrierComposite
			multiCarrierComposite = new MultiCarrierUnscrapComposite(left, SWT.BORDER, false, true, true, false);
			multiCarrierComposite.createPartControl();
			Text sourceCarrierId = multiCarrierComposite.getTxtCarrierId();
			sourceCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						if (multiCarrierComposite.lotTableManager.getInput() != null && 
								multiCarrierComposite.lotTableManager.getInput().size() > 0) {
							LotMultiCarrier lotMutCarrier = (LotMultiCarrier)multiCarrierComposite.lotTableManager.getInput().get(0);
							section.setLotObject(getLotbyLotCarrier(lotMutCarrier));
							section.reflow();
							section.buildScrapUnitByLotUnitType();
						} else {
							section.reflow();
						}
					}
				}
			});
			
			//取消报废信息
			unScrapInfoComposite = new UnScrapInfoComposite(left, SWT.BORDER);
			unScrapInfoComposite.createPartControl();
			
			// right info
			Composite right = toolkit.createComposite(body, SWT.NONE);
			right.setLayout(new GridLayout(1, false));
			right.setLayoutData(new GridData(GridData.FILL_BOTH));
			createSection(adTable);
			section.createContents(mform, right);
			
			//
			Text txtLotId = multiCarrierComposite.getTxtLotId();
			txtLotId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					try {
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText();
							//获取批次
							section.setLotObject(getLotbyLotId(lotId));
							section.reflow();
							section.buildScrapUnitByLotUnitType();
						}
					} catch (Exception e) {
						e.printStackTrace();
						ExceptionHandlerManager.asyncHandleException(e);
					}
					
				}
			});
			
			
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createSection(ADTable adTable) {
		section = new MultiCarrierUnscrapSection(adTable,this);
	}

	private Lot getLotbyLotId(String lotId) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			return lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	private Lot getLotbyLotCarrier(LotMultiCarrier lotMutCarrier) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			return lotManager.getLot(lotMutCarrier.getLotRrn());
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

}
