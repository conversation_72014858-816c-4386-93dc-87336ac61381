package com.glory.mes.wip.lot.multicarrier.unscrap;

import java.math.BigDecimal;
import java.util.List;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierQtyComposite;
import com.glory.mes.wip.model.LotScrap;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierQtyUnscrapComposite extends MultiCarrierQtyComposite {

	private static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");

	private Composite parent;
	private ADTable adTable;
	private ListTableManager tableManager;

	private boolean indexFlag;
	private boolean checkFlag;
	private boolean editorFlag;
	private boolean showTotalFlag;

	private MultiCarrierUnscrapEditor multiCarrierScrapEditor;

	public MultiCarrierQtyUnscrapComposite(Composite parent, ADTable adTable, boolean indexFlag, boolean checkFlag,
			boolean editorFlag, boolean showTotalFlag) {
		super(parent, adTable, indexFlag, checkFlag, editorFlag, showTotalFlag);
		this.parent = parent;
		this.adTable = adTable;
		this.indexFlag = indexFlag;
		this.checkFlag = checkFlag;
		this.editorFlag = editorFlag;
		this.showTotalFlag = showTotalFlag;
	}

	public MultiCarrierQtyUnscrapComposite(Composite parent, ADTable adTable, boolean indexFlag, boolean checkFlag,
			boolean editorFlag, boolean showTotalFlag, MultiCarrierUnscrapEditor multiCarrierScrapEditor) {
		super(parent, adTable, indexFlag, checkFlag, editorFlag, showTotalFlag);
		this.parent = parent;
		this.adTable = adTable;
		this.indexFlag = indexFlag;
		this.checkFlag = checkFlag;
		this.editorFlag = editorFlag;
		this.showTotalFlag = showTotalFlag;
		this.multiCarrierScrapEditor = multiCarrierScrapEditor;
	}

	@Override
	public void changeTotal() {
		List<Object> inputObject = (List<Object>) getTableManager().getInput();
		BigDecimal total = BigDecimal.ZERO;
		for (Object object : inputObject) {
			if (object instanceof LotScrap) {
				LotScrap lotScrap = (LotScrap) object;
				if (lotScrap.getAttribute2() != null) {
					BigDecimal unScrapQty = BigDecimal.valueOf(Long.parseLong((String) lotScrap.getAttribute2()));
					if (unScrapQty.compareTo(lotScrap.getMainQty()) > 0) {
						UI.showWarning(
								lotScrap.getAttribute1() + Message.getString("wip.lot_multicarrier_outnumber_currentqty"));
					}
					total = total.add(unScrapQty);
				}
			}
		}
		getLblTotal().setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ":" + total);
	}

	@Override
	public void init() {
		if (showTotalFlag) {
			Composite labelCompsite = new Composite(parent, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(1, false));

			lblTotal = new Label(labelCompsite, SWT.NONE);
			lblTotal.setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ": 0    ");
		}
		if (adTable == null) {
			adTable = getDefaultADTable();
		}
		if (editorFlag) {
			tableManager = new ListEditorTableManager(adTable, checkFlag);
		} else {
			tableManager = new ListTableManager(adTable, checkFlag);
		}
		tableManager.setSortFlag(false);
		tableManager.setAutoSizeFlag(true);
		tableManager.setIndexFlag(indexFlag);
		tableManager.newViewer(parent);
		tableManager.setTableBackground(ColorBackground);

		tableManager.addSelectionChangedListener(new ISelectionChangedListener() {

			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				String comboScrapCode = multiCarrierScrapEditor.unScrapInfoComposite.comboUnScrapCode.getText();
				String comment = multiCarrierScrapEditor.unScrapInfoComposite.commentText.getText() == null ? ""
						: multiCarrierScrapEditor.unScrapInfoComposite.commentText.getText();
				if (comboScrapCode == null || "".equals(comboScrapCode.trim())) {
					UI.showWarning(String.format(Message.getString("wip.unscrap_select_unscrap_code_first"), HEADER_SCRAP_CODE));
					return;
				}
				StructuredSelection structuredSelection = (StructuredSelection) event.getSelection();
				LotScrap lotScrap = (LotScrap) structuredSelection.getFirstElement();
				if (lotScrap == null) {
					return;
				}
				lotScrap.setAttribute3(comboScrapCode);
				lotScrap.setAttribute4(comment);
				changeTotal();
			}
		});
	}

	/**
	 * 检查报废数量必填
	 */
	public boolean validat() {
		List<LotScrap> lotScraps = (List<LotScrap>) getTableManager().getInput();
		for (LotScrap lotScrap : lotScraps) {
			if (lotScrap.getAttribute2() != null) {
				BigDecimal unScrapQty = new BigDecimal((String) lotScrap.getAttribute2());
				if (lotScrap.getMainQty().subtract(unScrapQty).compareTo(BigDecimal.ZERO) < 0) {
					lotScrap.setAttribute2(lotScrap.getMainQty().toString());
					UI.showError(Message.getString("wip.lot_multicarrier_outnumber_scrapqty"));
					return false;
				}
			}
		}
		return true;
	}

	@Override
	public ListTableManager getTableManager() {
		return this.tableManager;
	}

	@Override
	public void setTableManager(ListTableManager tableManager) {
		this.tableManager = tableManager;
	}
}
