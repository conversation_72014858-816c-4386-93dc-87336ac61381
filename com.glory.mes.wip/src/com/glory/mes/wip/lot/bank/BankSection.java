package com.glory.mes.wip.lot.bank;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.wip.action.LotBankAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class BankSection extends LotSection {
	
	private static final Logger logger = Logger.getLogger(EntitySection.class);
	
	public static final String KEY_BANK = "bank";
	public static final String KEY_CANCEL_BANK = "cancelBank";
	
	protected final static String BANK_STATE = "BANK";
	protected final static String ALLOW_STATE = "WAIT";
//	protected final static String ALLOW_HOLDSTATE = "On";
	
	protected AuthorityToolItem itemBank;
	protected AuthorityToolItem itemCancelBank;
	
	private static final String TABLE_NAME = "WIPBankAction";
	protected EntityForm bankForm;
	private static final String FILED_WAREHOUSEID = "warehouseId";
	private static final String FILED_LOCATORID = "locatorId";
	
	private static final String EVENTID = "BANK";
	
	public BankSection() {
		super();
		this.eventId = EVENTID;
	}

	public BankSection(ADTable table) {
		super(table);
		this.eventId = EVENTID;
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.bank_detail"));
		initAdObject();
	}

	@Override
	protected void createSectionContent(Composite client) {
		try {
			final FormToolkit toolkit = form.getToolkit();
            mmng = form.getMessageManager();
            super.createSectionContent(client);

            Composite body = toolkit.createComposite(client, SWT.NONE);
            GridLayout layoutBtn = new GridLayout(1, false);
            body.setLayout(layoutBtn);
            GridData gd1 = new GridData(GridData.FILL_HORIZONTAL);
            body.setLayoutData(gd1);  
            
            ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
            bankForm = new EntityForm(body, SWT.NONE, table, mmng);
            bankForm.setLayoutData(gd1);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    }

	}
	 
	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	public void refresh() {
		for (IForm detailForm : getDetailForms()) {
			detailForm.setObject(getAdObject());
			detailForm.loadFromObject();
		}
		if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
			Lot lot = (Lot) getAdObject();
			LotBankAction lotBankAction = new LotBankAction();
			lotBankAction.setWarehouseId(lot.getWarehouseId());
			lotBankAction.setLocatorId(lot.getLocatorId());
			bankForm.setObject(lotBankAction);
			bankForm.loadFromObject();
			if (BANK_STATE.equals(lot.getState())) {
				bankForm.getFields().get(FILED_WAREHOUSEID).setEnabled(false);
				bankForm.getFields().get(FILED_LOCATORID).setEnabled(false);
			} else {
				bankForm.getFields().get(FILED_WAREHOUSEID).setEnabled(true);
				bankForm.getFields().get(FILED_LOCATORID).setEnabled(true);
			}		
		} else {
			bankForm.setObject(new LotBankAction());
			bankForm.loadFromObject();
			bankForm.getFields().get(FILED_WAREHOUSEID).setEnabled(true);
			bankForm.getFields().get(FILED_LOCATORID).setEnabled(true);
		}
		
		getMessageManager().removeAllMessages();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemBank(tBar);
		createToolItemUnBank(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemBank(ToolBar tBar) {
		itemBank = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_BANK);
		itemBank.setText(Message.getString("common.bank"));
		itemBank.setImage(SWTResourceCache.getImage("ship-lot"));
		itemBank.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				bankAdapter(event);
			}
		});
	}

	protected void createToolItemUnBank(ToolBar tBar) {
		itemCancelBank = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_CANCEL_BANK);
		itemCancelBank.setText(Message.getString("common.cancelBank"));
		itemCancelBank.setImage(SWTResourceCache.getImage("unscrap-lot"));
		itemCancelBank.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				cancelBankAdapter(event);
			}
		});
	}

	protected void bankAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (!bankForm.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					ADManager adManager = Framework.getService(ADManager.class);
					Lot lot = (Lot) this.getAdObject();
					LotBankAction lotBankAction = (LotBankAction) bankForm.getObject();
					//判断仓库下库位是否存在
					List<Storage> storagelist = adManager.getEntityList(Env.getOrgRrn(), Storage.class, Env.getMaxResult(), 
							" warehouseId = '" + lotBankAction.getWarehouseId() + "' AND name = '" + lotBankAction.getLocatorId() + "'", null);
					//如果能找到，对比录入库位ID
					if (storagelist != null && storagelist.size() > 0) {
						lot.setWarehouseId(lotBankAction.getWarehouseId());
						lot.setLocatorId(lotBankAction.getLocatorId());
						LotManager lotManager = Framework.getService(LotManager.class);
						lotManager.bankIn(lot, lotBankAction, Env.getSessionContext());
						UI.showInfo(Message.getString("common.bank_successed"));// 弹出提示框
					} else {
						UI.showInfo(Message.getString("mm.storage_no_exist"));// 弹出提示框
						return;
					}
					refreshAdapter();
				}
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void cancelBankAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (!bankForm.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					Lot lot = (Lot) this.getAdObject();
					LotBankAction lotBankAction = (LotBankAction) bankForm.getObject();
					lot.setWarehouseId(null);
					lot.setLocatorId(null);
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.bankOut(lot, lotBankAction, Env.getSessionContext());
					UI.showInfo(Message.getString("common.cancelBank_successed"));// 弹出提示框
					refreshAdapter();
				}
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}

	@Override
	public void statusChanged(String newStatus) {
		Lot lot = (Lot) this.getAdObject();
		if (lot != null) {
			Boolean result = checkLotStateModel(lot);
			if (result == null) {
				if (ALLOW_STATE.equals(newStatus)) {
					itemBank.setEnabled(true);
				} else {
					itemBank.setEnabled(false);
				}
				
				if (BANK_STATE.equals(newStatus)) {
					itemCancelBank.setEnabled(true);
				} else {
					itemCancelBank.setEnabled(false);
				}
				
			} else if (result) {
				if (ALLOW_STATE.equals(newStatus)) {
					itemBank.setEnabled(true);
				} else {
					itemBank.setEnabled(false);
				}
				
				if (BANK_STATE.equals(newStatus)) {
					itemCancelBank.setEnabled(true);
				} else {
					itemCancelBank.setEnabled(false);
				}
			} else {
				itemBank.setEnabled(false);
				itemCancelBank.setEnabled(false);
			}
		} else {
			itemBank.setEnabled(false);
			itemCancelBank.setEnabled(false);
		}		
	}

}
