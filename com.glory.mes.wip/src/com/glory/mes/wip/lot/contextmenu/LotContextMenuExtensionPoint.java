package com.glory.mes.wip.lot.contextmenu;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

public class LotContextMenuExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(LotContextMenuExtensionPoint.class);

	private static List<LotContextActionModel> actionModels = new ArrayList<LotContextActionModel>();
	
    public final static String X_POINT = "com.glory.mes.wip.contextmenu";
    public final static String E_ACTION = "action";
    public final static String A_ID = "id";
    public final static String A_SEQ = "seq";
    public final static String A_AUTHORITY = "authority";
    public final static String A_CLASS = "class";
    public final static String A_LABEL = "label";
    public final static String A_IMAGE = "image";
    
    static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
			for (int j = 0; j < configElements.length; j++) {
				try {
					LotContextActionModel model = new LotContextActionModel();
					model.setId(configElements[j].getAttribute(A_ID));
					String seqStr = configElements[j].getAttribute(A_SEQ);
					model.setSeqNo(Integer.parseInt(seqStr));
					String authority = configElements[j].getAttribute(A_AUTHORITY);
					if (authority != null && authority.trim().length() > 0) {
						//如果在扩展点中定义了Authority,则以Authority为标准
						model.setAuthority(authority);
						getActionModels().add(model);
					} else {
						LotContextAction action = (LotContextAction)configElements[j].createExecutableExtension(A_CLASS);
						model.setAction(action);
						model.setLabel(configElements[j].getAttribute(A_LABEL));
						model.setImage(configElements[j].getAttribute(A_IMAGE));
						getActionModels().add(model);
					}
					//TODO 按照seq排序
					//Collections.sort(actionModels)
				} catch (Exception e){
					logger.error("LotContextMenuExtensionPoint : init ", e);
				}
			}
		}			
	}

	public static void setActionModels(List<LotContextActionModel> actionModels) {
		LotContextMenuExtensionPoint.actionModels = actionModels;
	}

	public static List<LotContextActionModel> getActionModels() {
		return actionModels;
	}
}


