package com.glory.mes.wip.lot.unship;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class UnShipSection extends LotSection {

	public static final String KEY_UNSHIP = "unShip";
	protected AuthorityToolItem itemUnShip;
	protected UnShipForm UnShipForm;

	public UnShipSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.unship_sectiontitle"));
		initAdObject();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemUnShip(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemUnShip(ToolBar tBar) {
		itemUnShip = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() +"."+ KEY_UNSHIP);
		itemUnShip.setAuthEventAdaptor(this::unShipAdapter);
		itemUnShip.setText(Message.getString("wip.unship"));
		itemUnShip.setImage(SWTResourceCache.getImage("unship-lot"));
//		itemUnShip.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				unShipAdapter(event);
//			}
//		});
	}

	protected void unShipAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						UI.showWarning(Message.getString("warn.required_entry"));
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						LotManager lotManager = Framework
								.getService(LotManager.class);
						LotAction lotAction = ((UnShipForm) detailForm)
								.getLotAction();
						Lot lot = ((UnShipForm) detailForm).getLot();
						String operator = Env.getUserName();
						if (itemUnShip.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
							operator = (String) itemUnShip.getData(LotAction.ACTION_TYPE_OPERATOR);
						}
						lot.setOperator1(operator);
						lotManager.unShipLot(lot, lotAction, Env.getSessionContext());
					}
					UI.showInfo(Message.getString("wip.unship_successed"));// 弹出提示框
					refresh();
					setFocus();
				}
			} else {
				UI.showError(Message.getString("wip.unship_error_nolot_tounship"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		UnShipForm = new UnShipForm(composite, SWT.NONE, tab, mmng);
		return UnShipForm;
	}

	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		super.refresh();
	}

	@Override
	public void statusChanged(String newStatus) {
		Lot lot =(Lot)getAdObject();
		stateChange(lot);
	}
	
	public void stateChange(Lot lot) {
		if (lot != null && lot.getState() != null) {
			if (LotStateMachine.TRANS_SHIPLOT.equalsIgnoreCase(lot.getPreTransType())) {
				itemUnShip.setEnabled(true);
			} else {
				itemUnShip.setEnabled(false);
			}
		} else {
			itemUnShip.setEnabled(false);
		}
	}
	
	
}
