package com.glory.mes.wip.lot.lotprocedure.setup;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.LotTecn;

public class LotProcedureTecnEditDialog extends EntityDialog {

	public static final String TABLE_NAME = "WIPLotProcedureChangeLotTecn";
	
	public LotProcedureTecnEditDialog(ADTable table, ADBase adObject) {
		super(table, adObject);
	}
	
	protected void createFormContent(Composite composite) {
		FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
		ScrolledForm sForm = toolkit.createScrolledForm(composite);
		managedForm = new ManagedForm(toolkit, sForm);
		final IMessageManager mmng = managedForm.getMessageManager();
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		Composite body = sForm.getForm().getBody();
		configureBody(body);
		
		EntityForm itemForm = new LotProcedureTecnEditForm(body, SWT.NONE, adObject, table, mmng);
		itemForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		getDetailForms().add(itemForm);
		
	}
	
	protected boolean saveAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (Form detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					LotManager lotManager = Framework.getService(LotManager.class);
					
					LotTecn lotTecn = (LotTecn)getAdObject();	
					if (StringUtil.isEmpty(lotTecn.getEquipmentId())
							&& StringUtil.isEmpty(lotTecn.getRecipeName())
							&& StringUtil.isEmpty(lotTecn.getEquipmentRecipeName())
							&& StringUtil.isEmpty(lotTecn.getReticleName())
							&& StringUtil.isEmpty(lotTecn.getEdcName())) {
						UI.showError(Message.getString("edc.data_save_is_null"));
						return false;
					}
					
					if (!StringUtil.isEmpty(lotTecn.getRecipeName())) {
						//判断Recipe是否存在
						lotManager.validLogicRecipe(Env.getOrgRrn(), lotTecn.getRecipeName());
					}
						
					if (!StringUtil.isEmpty(lotTecn.getEquipmentRecipeName())) {			
						if (StringUtil.isEmpty(lotTecn.getRecipeName())) {
							//设置PPID，LogicRecipe不能为空。
							UI.showError(Message.getString("wip.lot_tecn_setup_ppid_logicrecipe_not_is_null"));
							return false;
						} else {
							if (!StringUtil.isEmpty(lotTecn.getEquipmentId())) {
								//设备，LogicRecipe，PPID都不为空的情况检查。
								lotManager.validRecipeEquipment(Env.getOrgRrn(), lotTecn.getEquipmentId(), lotTecn.getRecipeName(), lotTecn.getEquipmentRecipeName());
							} else {
								//LogicRecipe，PPID都不为空的情况。
								lotManager.validRecipeEquipment(Env.getOrgRrn(), null, lotTecn.getRecipeName(), lotTecn.getEquipmentRecipeName());
							}
						}
					}	
					
					setAdObject(getAdObject());
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return false;
	}

}
