package com.glory.mes.wip.lot.lotprocedure.setup;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.model.LotTecn;

public class LotProcedureTecnEditForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(LotProcedureTecnEditForm.class);

	private RefTableField refTableField;
	
	public LotProcedureTecnEditForm(Composite parent, int style, Object object, ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}
	
	@Override
	public IField getField(ADField adField) {
		try {
			LotTecn lotTecn = (LotTecn) getObject();
			if (lotTecn != null && !StringUtil.isEmpty(lotTecn.getStepName())) {
				if ("equipmentId".equals(adField.getName())) {
					String displayType = this.getDisplayType(adField);
					String name = adField.getName();
					String displayLabel = getLabelText(adField);
					int displayLength = adField.getDisplayLength() != null ? adField.getDisplayLength().intValue() : 32;
					IField field = null;
					
					ADRefTable refTable = new ADRefTable();
					refTable.setObjectRrn(adField.getReftableRrn());
					refTable = (ADRefTable)getADManger().getEntity(refTable);
					if (refTable == null || refTable.getTableRrn() == null){
						logger.error("Field [" + name + "], display type[" + displayType + "], refTableRrn[" + adField.getReftableRrn() + "] is null.");
						return null;
					}
					logger.debug("Start create field[" + name + "], displayType[" + displayType + "], refTable[" + adField.getReftableRrn() + "], adTable[" + refTable.getTableRrn() + "]");
					
					ADTable adTable = getADManger().getADTable(refTable.getTableRrn());
					refTable.setAdTable(adTable);
					ListTableManager tableManager = new ListTableManager(adTable);
					Step step = new Step();
					step.setOrgRrn(Env.getOrgRrn());
					step.setName(lotTecn.getStepName());
					step.setVersion(lotTecn.getStepVersion());
					PrdManager prdManager = Framework.getService(PrdManager.class);
					step = (Step) prdManager.getSimpleProcessDefinition(step, false);
					if (step != null) {
						// 根据工步能力加载设备
						RASManager rasManager = Framework.getService(RASManager.class);
						List<Equipment> equipments = rasManager.getEquipmentsByCapa(step.getCapability());
						if (CollectionUtils.isNotEmpty(equipments)) {
							tableManager.setInput(equipments);
						}
					}
					
					refTableField = createRefTableFieldList(adField.getName(), displayLabel, tableManager, refTable, displayLength);					
					addField(name, refTableField);
					return field;
				} 	
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		return super.getField(adField);
	}
}
