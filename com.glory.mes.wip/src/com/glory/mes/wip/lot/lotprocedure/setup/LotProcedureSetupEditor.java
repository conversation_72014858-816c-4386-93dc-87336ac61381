package com.glory.mes.wip.lot.lotprocedure.setup;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;
import javax.inject.Inject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.e4.ui.workbench.modeling.IPartListener;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.TreeItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.LotProcedure;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.model.ReworkProcedure;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotProcedureManager;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotProcedureChange;
import com.glory.mes.wip.model.LotProcedureChangeStep;
import com.glory.mes.wip.model.LotTecn;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

public class LotProcedureSetupEditor extends GlcEditor implements IPartListener {

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.lotprocedure.setup.LotProcedureSetupEditor";

	@Inject
	protected EPartService partService;
	
	public static final String FORM_ADD_STEP = "WIPLotProcedureAddStepQuery";
	public static final String FORM_VIEW_PROCEDURE = "WIPLotProcedureStepView";

	public static final String CONTROL_LOT_ID = "lotId";
	
	public static final String CONTROL_GLCFORM_LOT_INFO = "lotInfo";
	public static final String CONTROL_FLOW_TREE = "lotFlowTree";
	public static final String CONTROL_LOT_PROCEDURE_STEP_LIST = "lotProcedureStepList";
	
	public static final String CONTROL_GLCFORM_LOT_PROCEDURE_INFO = "lotProcedureInfo";
	public static final String CONTROL_STEP_LIST = "stepList";
	public static final String CONTROL_CHANGE_LOT_PROCEDURE_STEP_LIST = "changeLotProcedureStepList";
	
	public static final String BTN_LOAD_PROCEDURE = "loadProcedure";
	public static final String BTN_ADD_STEP = "addStep";
	public static final String BTN_REMOVE_STEP = "removeStep";
	public static final String BTN_ADD_TECN = "addTecn";
	public static final String BTN_REMOVE = "remove";
	public static final String BTN_EDIT_TECN = "editTecn";
	public static final String BTN_VIEW_PROCEDURE = "viewProcedure";

	protected GlcFormField lotInfoGlcForm;
	protected CustomField flowTreeCustomField;
	private FlowCustomComposite flowCustomComposite;
	protected ListTableManagerField lotProcedureStepListTableField;
	
	protected GlcFormField lotProcedureInfoGlcForm;
	protected ListTableManagerField stepListTableField;
	protected ListTableManagerField changeLotProcedureStepListTableField;
	
	protected ReworkState currentReworkState;
	protected ProcedureState currentProcedure;
	protected Lot currentLot;

	protected ADManager adManager;
	protected LotManager lotManager;
	protected PrdManager prdManager;
	protected LotProcedureManager lotProcedureManager;

	@PreDestroy
	public void preDestroy() {
		partService.removePartListener(this);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		partService.addPartListener(this);

		lotInfoGlcForm = form.getFieldByControlId(CONTROL_GLCFORM_LOT_INFO, GlcFormField.class);
		flowTreeCustomField = lotInfoGlcForm.getFieldByControlId(CONTROL_FLOW_TREE, CustomField.class);
		flowCustomComposite = (FlowCustomComposite) flowTreeCustomField.getCustomComposite();
		lotProcedureStepListTableField = lotInfoGlcForm.getFieldByControlId(CONTROL_LOT_PROCEDURE_STEP_LIST, ListTableManagerField.class);
		
        subscribeAndExecute(eventBroker, lotProcedureStepListTableField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::lotProcedureStepSeletedAdapter);

		// 获取流程控件
        lotProcedureInfoGlcForm = form.getFieldByControlId(CONTROL_GLCFORM_LOT_PROCEDURE_INFO, GlcFormField.class);     
		stepListTableField = lotProcedureInfoGlcForm.getFieldByControlId(CONTROL_STEP_LIST, ListTableManagerField.class);
		changeLotProcedureStepListTableField = lotProcedureInfoGlcForm.getFieldByControlId(CONTROL_CHANGE_LOT_PROCEDURE_STEP_LIST, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_REFRESH), this::refreshAdapter);
		
		subscribeAndExecute(eventBroker, lotProcedureInfoGlcForm.getFullTopic(BTN_ADD_STEP), this::addStepAdapter);
		subscribeAndExecute(eventBroker, lotProcedureInfoGlcForm.getFullTopic(BTN_REMOVE_STEP), this::removeStepAdapter);
		subscribeAndExecute(eventBroker, lotProcedureInfoGlcForm.getFullTopic(BTN_ADD_TECN), this::addTecnAdapter);
		
		subscribeAndExecute(eventBroker, lotProcedureInfoGlcForm.getFullTopic(BTN_REMOVE), this::removeAdapter);
		subscribeAndExecute(eventBroker, lotProcedureInfoGlcForm.getFullTopic(BTN_EDIT_TECN), this::editTecnAdapter);
		subscribeAndExecute(eventBroker, lotProcedureInfoGlcForm.getFullTopic(BTN_VIEW_PROCEDURE), this::viewProcedureAdapter);
		
		
		subscribeAndExecute(eventBroker, flowTreeCustomField.getFullTopic(GlcEvent.EVENT_CLICK), this::flowClickAdapter);
		subscribeAndExecute(eventBroker, flowTreeCustomField.getFullTopic(CONTROL_LOT_ID + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::enterPressedAdapter);
		
		try {
			adManager = Framework.getService(ADManager.class);
			lotManager = Framework.getService(LotManager.class);
			prdManager = Framework.getService(PrdManager.class);
			lotProcedureManager = Framework.getService(LotProcedureManager.class);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 批次回车事件
	 * @param obj
	 */
	protected void enterPressedAdapter(Object obj) {
		try {
			String lotId = flowCustomComposite.getTxtId().getText();
			if (StringUtil.isEmpty(lotId)) {
				return;
			}
			currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, true);
			flowCustomComposite.loadFlowTreeByLot(currentLot);
			changeLotProcedureStepListTableField.setValue(null);		
			changeLotProcedureStepListTableField.refresh();
			//获取当前返工工步
			currentReworkState = prdManager.getCurrentReworkState(currentLot.getProcessInstanceRrn());
			
			
			//加载All的LotProcedureChangeStep
			loadLotProcedureChangeStepList();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 加载所有的LotProcedureChangeStep
	 */
	protected void loadLotProcedureChangeStepList() {
		try {
			List<LotProcedureChangeStep> allLotProcedureChangeStep = Lists.newArrayList();
			Set<String> states = Sets.newHashSet();
			states.add(LotProcedureChange.STATE_CREATED);
//			states.add(LotProcedureChange.STATE_APPROVED);			
			List<LotProcedureChange> allLotProcedureChanges = lotProcedureManager.getLotProcedureChangeList(Env.getOrgRrn(), currentLot.getLotId(), null, states);
			if (CollectionUtils.isNotEmpty(allLotProcedureChanges)) {
				for (LotProcedureChange lotProcedureChange : allLotProcedureChanges) {
					for (LotProcedureChangeStep lotProcedureChangeStep : lotProcedureChange.getLotProcedureChangeSteps()) {
						if (lotProcedureChangeStep.getLotTecnRrn() != null) {
							LotTecn lotTecn = new LotTecn();
							lotTecn.setEquipmentId(lotProcedureChangeStep.getEquipmentId());
							lotTecn.setEdcName(lotProcedureChangeStep.getEdcName());
							lotTecn.setEquipmentRecipeName(lotProcedureChangeStep.getEquipmentRecipeName());
							lotTecn.setRecipeName(lotProcedureChangeStep.getRecipeName());
							lotTecn.setReticleName(lotProcedureChangeStep.getReticleName());
							lotTecn.setLotId(lotProcedureChange.getLotId());
							lotTecn.setProcedureName(lotProcedureChange.getProcedureName());
							lotTecn.setStepStateName(lotProcedureChangeStep.getActionStepState());
							lotTecn.setStepName(lotProcedureChangeStep.getStepName());
							lotTecn.setStepVersion(lotProcedureChangeStep.getStepVersion());
							lotProcedureChangeStep.setLotTecn(lotTecn);
						}
					}									
					allLotProcedureChangeStep.addAll(lotProcedureChange.getLotProcedureChangeSteps());
				}
			}
			Collections.sort(allLotProcedureChangeStep, new Comparator<LotProcedureChangeStep>() {
				@Override
				public int compare(LotProcedureChangeStep o1, LotProcedureChangeStep o2) {
					if (o2.getSeqNo() == null) {
						return o2.getProcedureName().compareTo(o1.getProcedureName());
					} else {
						if (o2.getProcedureName().compareTo(o1.getProcedureName()) == 0) {
							return (int)(o2.getSeqNo() - o1.getSeqNo());
						} else {
							return o2.getProcedureName().compareTo(o1.getProcedureName());
						}
					}
				}
				
			});
			lotProcedureStepListTableField.setValue(allLotProcedureChangeStep);
			lotProcedureStepListTableField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 流程控件单击事件
	 * @param object
	 */
	protected void flowClickAdapter(Object object) {
		try {
			Event event = (Event) object;
			TreeItem treeItem = (TreeItem) event.getProperty(GlcEvent.PROPERTY_DATA);

			Object data = treeItem.getData();

			ProcessDefinition procedure = null;
			String path = null;
			if (data instanceof StepState) {
				StepState stepState = (StepState) data;

				procedure = stepState.getProcessDefinition();
				TreeItem parentItem = treeItem.getParentItem();
				Long l = currentLot.getObjectRrn();
				if (parentItem.getData() != null) {
					currentProcedure = prdManager.getProcedureStateUsedProcedure(l, (ProcedureState) parentItem.getData());
				}
				path = currentProcedure.getPath();
			} else if (data instanceof ProcedureState) {
				currentProcedure = (ProcedureState) data;
				Long l = currentLot.getObjectRrn();
				currentProcedure = prdManager.getProcedureStateUsedProcedure(l, currentProcedure);
				path = currentProcedure.getPath();
				procedure = currentProcedure.getUsedProcedure();
			}

			procedureChangeRefresh(procedure, path);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void procedureChangeRefresh(ProcessDefinition procedure, String procedurePath) {
		try {
			if (procedure == null) {
				return;
			}
			PrdManager prdManager = Framework.getService(PrdManager.class);
			//1.校验如果在返工中，只能修改返工流程
			if (currentReworkState != null) {
				List<Node> nodeList = prdManager.getProcessFlowList(currentLot.getProcessInstanceRrn());
				String currentReworkStatePath = "";
				for (Node node : nodeList) {
					if (!(node instanceof StepState)) {
						currentReworkStatePath += node.getName() + "/";
					}
				}
				if (!StringUtils.equals(currentReworkStatePath, procedurePath)) {
					if(!(procedure instanceof LotProcedure))
						UI.showError(Message.getString("wip.lot_rework_cannot_change_other_procedure"));
						return;
				}
			}

			//2.获取普通流程的工步列表
			ProcessDefinition simpleProcedure = null;
			if (procedure instanceof Procedure) {
				simpleProcedure = (Procedure) procedure;
			} else if (procedure instanceof LotProcedure) {
				simpleProcedure = (LotProcedure) procedure;
			}
			List<StepState> stepList = Lists.newArrayList();
			if (simpleProcedure != null) {
				stepList = getStepsByProcedure(simpleProcedure);
			}
			stepListTableField.getListTableManager().setInput(stepList);
			
			//3.获取当前流程的changeStep列表
			List<LotProcedureChangeStep> allLotProcedureChangeSteps = (List<LotProcedureChangeStep>) lotProcedureStepListTableField.getListTableManager().getInput();
			List<LotProcedureChangeStep> changeCurrentProcedureSteps = allLotProcedureChangeSteps.stream().filter(
					allLotProcedureChangeStep -> StringUtils.equals(allLotProcedureChangeStep.getProcedureName(), procedure.getName())).collect(Collectors.toList());
			changeLotProcedureStepListTableField.getListTableManager().setInput(changeCurrentProcedureSteps);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private List<StepState> getStepsByProcedure(ProcessDefinition procedure) {
		try {
			if (procedure != null) {
				PrdQueryAction action = PrdQueryAction.newIntance();
				action.setCopyNode(true);
				List<Node> nodeList = prdManager.getProcessDefinitionChildern(procedure, action);
				List<StepState> stepList = nodeList.stream().filter(node -> node instanceof StepState).map(stepState -> {
					return ((StepState) stepState);
				}).collect(Collectors.toList());
				
				if (CollectionUtils.isNotEmpty(stepList)) {
					List<StepState> stepStates = new ArrayList<StepState>();
					for(StepState stepState : stepList) {
						StepState node = (StepState) adManager.getEntity(stepState);
						stepState.setArrivingTransitions(node.getArrivingTransitions());
						stepStates.add(stepState);
					}
					stepList = stepStates;
				}

				return stepList;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

		return null;
	}
	
	/**
	 * lotProcedureStepListTableField选择事件
	 * @param object
	 */
	protected void lotProcedureStepSeletedAdapter(Object object) {
		try {
			Event event = (Event) object;
            Object data = event.getProperty(GlcEvent.PROPERTY_DATA);

            if (data == null) {
                return;
            }

            LotProcedureChangeStep lotProcedureChangeStep = (LotProcedureChangeStep) data;

            LotProcedureChange lotProcedureChange = new LotProcedureChange();
            lotProcedureChange.setObjectRrn(lotProcedureChangeStep.getLotProcedureActionRrn());
            lotProcedureChange = (LotProcedureChange) adManager.getEntity(lotProcedureChange);
           
            loadLotProcedureChangeData(lotProcedureChange);           
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}		
	
	protected void loadLotProcedureChangeData(LotProcedureChange lotProcedureChange) {
		try {
            FlowCustomComposite flowCustomComposite = (FlowCustomComposite) flowTreeCustomField.getCustomComposite();
            TreeViewer viewer = flowCustomComposite.getViewer();
            if (currentReworkState != null) {
                TreeSelection section = (TreeSelection) viewer.getSelection();
                TreePath[] paths = section.getPaths();

                TreePath treePath = paths[0];
                Object segment = treePath.getSegment(treePath.getSegmentCount() - 2);

                if (segment != null && segment instanceof ReworkState) {
                    currentProcedure = prdManager.getProcedureStateUsedProcedure(currentLot.getObjectRrn(), (ReworkState) segment);
                    procedureChangeRefresh(currentProcedure.getUsedProcedure(), currentProcedure.getPath());
                }

            } else {
                List<Node> processNodes = prdManager.getProcessChildren(currentLot.getProcessInstanceRrn());
                // flow选中流程的第一步
                for (Node node : processNodes) {
                    if (node instanceof ProcedureState) {
                        ProcedureState procedureState = (ProcedureState) node;
                        ProcessDefinition usedProcedure = procedureState.getUsedProcedure();

                        if (StringUtils.equals(usedProcedure.getName(), lotProcedureChange.getProcedureName())
                            && usedProcedure.getVersion().compareTo(lotProcedureChange.getProcedureVersion()) == 0) {
                            currentProcedure = prdManager.getProcedureStateUsedProcedure(currentLot.getObjectRrn(), procedureState);

                            procedureState.setProcessInstanceRrn(currentLot.getProcessInstanceRrn());
                            procedureState.setUsedProcedure(null);
                            List<Node> childernNodes = prdManager.getProcedureStateChildern(currentLot.getObjectRrn(), procedureState, false);

                            Optional<Node> findFirst = childernNodes.stream().filter(childernNode -> childernNode instanceof StepState).findFirst();
                            if (findFirst.isPresent()) {
                                TreeSelection section = new TreeSelection(
                                		new TreePath(Arrays.asList(currentProcedure.getParent(), currentProcedure, findFirst.get()).toArray()));
                                viewer.setSelection(section);
                            }

                            procedureChangeRefresh(usedProcedure, procedureState.getPath());
                            break;
                        }
                    }
                }
            }
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}		
	
	protected void refreshAdapter(Object object) {	
		currentLot = null;
		FlowCustomComposite flowCustomComposite = (FlowCustomComposite) flowTreeCustomField.getCustomComposite();
		flowCustomComposite.getTreeManager().setInput(null);
		
		currentProcedure = null;
		lotProcedureStepListTableField.getListTableManager().setInput(Lists.newArrayList());
		stepListTableField.getListTableManager().setInput(Lists.newArrayList());
		changeLotProcedureStepListTableField.getListTableManager().setInput(Lists.newArrayList());		
	}

	/**
	 * 增加工步
	 * @param object
	 */
	protected void addStepAdapter(Object object) {
		Object selectedObject = stepListTableField.getListTableManager().getSelectedObject();
		if (selectedObject == null) {
			UI.showError(Message.getString("wip.select_lot_procedure_step"));
			return;
		}

		StepState actionStepState = (StepState) selectedObject;
		String stepStateName = actionStepState.getName();;
		actionStepState = (StepState) adManager.getEntity(actionStepState);
		List<Transition> leavingTransitions = actionStepState.getLeavingTransitions();
		if (leavingTransitions.size() >= 2) {
			UI.showError(Message.getString("wip.select_step_exist_rework_procedure"));
			return;
		}
		
		LotProcedureAddStepDialog dialog = new LotProcedureAddStepDialog(FORM_ADD_STEP, null, eventBroker);

		// 解决窗体返回 父级窗体不刷新问题
		dialog.setOkAdaptor(new Consumer<GlcBaseDialog>() {
			@Override
			public void accept(GlcBaseDialog t) {
				List<Step> stepList = dialog.getSelectedStepList();

				List<LotProcedureChangeStep> addList = stepList.stream().map(step -> {
					LotProcedureChangeStep lotProcedureChangeStep = new LotProcedureChangeStep();
					lotProcedureChangeStep.setProcedureName(currentProcedure.getName());
					lotProcedureChangeStep.setActionType(LotProcedureChangeStep.ACTION_TYPE_ADDSTEP);
					lotProcedureChangeStep.setActionStepState(stepStateName);
					lotProcedureChangeStep.setStepName(step.getName());
					lotProcedureChangeStep.setStepVersion(step.getVersion());
					return lotProcedureChangeStep;
				}).collect(Collectors.toList());

                List<LotProcedureChangeStep> changeLotProcedureSteps = (List<LotProcedureChangeStep>) changeLotProcedureStepListTableField.getListTableManager().getInput();
                for (LotProcedureChangeStep lotProcedureChangeStep : addList) {
                    Optional<LotProcedureChangeStep> optional = changeLotProcedureSteps.stream().filter(exsitStep
                    		-> StringUtils.equals(exsitStep.getActionType(), lotProcedureChangeStep.getActionType())
                            && StringUtils.equals(exsitStep.getStepName(), lotProcedureChangeStep.getStepName())).findAny();
                    if (!optional.isPresent()) {
                    	changeLotProcedureStepListTableField.getListTableManager().add(lotProcedureChangeStep);
                    } else {
                    	changeLotProcedureStepListTableField.getListTableManager().remove(optional.get());
                    	changeLotProcedureStepListTableField.getListTableManager().add(lotProcedureChangeStep);
                    }
                }
			}
		});

		dialog.open();
	}

	/**
	 * 删除工步
	 * @param object
	 */
	protected void removeStepAdapter(Object object) {
		try {
			Object selectedObject = stepListTableField.getListTableManager().getSelectedObject();
			if (selectedObject == null) {
				UI.showInfo(Message.getString("wip.select_lot_procedure_step"));
				return;
			}

			StepState actionStepState = (StepState) selectedObject;
			Step step = actionStepState.getUsedStep();
			
			actionStepState = (StepState) adManager.getEntity(actionStepState);
			List<Transition> leavingTransitions = actionStepState.getLeavingTransitions();
			if (leavingTransitions.size() >= 2) {
				UI.showError(Message.getString("wip.select_step_exist_rework_procedure"));
				return;
			}
			
			Set<Transition> arrivingTransitions = actionStepState.getArrivingTransitions();
			if (arrivingTransitions.size() >= 2) {
				UI.showError(Message.getString("wip.select_step_exist_rework_procedure_the_arrival_step"));
				return;
			}
			 
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
	        if (!MesCfMod.isChangeFlowRemoveSkipStep(Env.getOrgRrn(), sysParamManager)) {
	        	if (step.getNoSkip()) {
	        		UI.showError(Message.getString("lot.skip_step_can_not_skip"));
	        		return;
	        	}
	        }            

	        LotProcedureChangeStep lotProcedureChangeStep = new LotProcedureChangeStep();
	        lotProcedureChangeStep.setProcedureName(currentProcedure.getName());
	        lotProcedureChangeStep.setActionType(LotProcedureChangeStep.ACTION_TYPE_REMOVESTEP);
	        lotProcedureChangeStep.setActionStepState(actionStepState.getName());
	        lotProcedureChangeStep.setStepName(step.getName());
					
			List<LotProcedureChangeStep> lotProcedureChangeSteps = (List<LotProcedureChangeStep>) changeLotProcedureStepListTableField.getListTableManager().getInput();
            Optional<LotProcedureChangeStep> optional = lotProcedureChangeSteps.stream().filter(exsitStep 
            		-> StringUtils.equals(exsitStep.getActionType(), lotProcedureChangeStep.getActionType())
                    && StringUtils.equals(exsitStep.getActionStepState(), lotProcedureChangeStep.getActionStepState())).findAny();
            if (!optional.isPresent()) {
            	changeLotProcedureStepListTableField.getListTableManager().add(lotProcedureChangeStep);
            }
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	/**
	 * 增加LotTecn
	 * @param object
	 */
	protected void addTecnAdapter(Object object) {
		try {
			Object selectedObject = stepListTableField.getListTableManager().getSelectedObject();
			if (selectedObject == null) {
				UI.showInfo(Message.getString("wip.select_lot_procedure_step"));
				return;
			}

			StepState actionStepState = (StepState) selectedObject;
			Step step = actionStepState.getUsedStep();
			LotProcedureChangeStep lotProcedureChangeStep = new LotProcedureChangeStep();
			lotProcedureChangeStep.setProcedureName(currentProcedure.getName());
			lotProcedureChangeStep.setActionType(LotProcedureChangeStep.ACTION_TYPE_TECN);
			lotProcedureChangeStep.setActionStepState(actionStepState.getName());
			lotProcedureChangeStep.setStepName(step.getName());
	        lotProcedureChangeStep.setStepVersion(step.getVersion());

            List<LotProcedureChangeStep> lotProcedureChangeSteps = (List<LotProcedureChangeStep>) changeLotProcedureStepListTableField.getListTableManager().getInput();
            Optional<LotProcedureChangeStep> optional = lotProcedureChangeSteps.stream().filter(exsitStep 
            		-> StringUtils.equals(exsitStep.getActionType(), lotProcedureChangeStep.getActionType())
                    && StringUtils.equals(exsitStep.getActionStepState(), lotProcedureChangeStep.getActionStepState())).findAny();
            if (!optional.isPresent()) {
            	changeLotProcedureStepListTableField.getListTableManager().add(lotProcedureChangeStep);
            }
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void removeAdapter(Object object) {
		try {
			Object selectedObject = changeLotProcedureStepListTableField.getListTableManager().getSelectedObject();
			if (selectedObject == null) {
				UI.showInfo(Message.getString("wip.select_lot_procedure_step_change"));
				return;
			}

			changeLotProcedureStepListTableField.getListTableManager().remove(selectedObject);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void editTecnAdapter(Object object) {
		try {
			Object selectedObject = changeLotProcedureStepListTableField.getListTableManager().getSelectedObject();
			if (selectedObject == null) {
				UI.showInfo(Message.getString("wip.select_lot_procedure_step_change"));
				return;
			}

			LotProcedureChangeStep lotProcedureChangeStep = (LotProcedureChangeStep) selectedObject;
			if (StringUtils.equals(LotProcedureChangeStep.ACTION_TYPE_REMOVESTEP, lotProcedureChangeStep.getActionType())) {
				return;
			}
			
			LotTecn lotTecn = lotProcedureChangeStep.getLotTecn();
			if (lotTecn == null) {
				lotTecn = new LotTecn();
				lotTecn.setLotId(currentLot.getLotId());
				lotTecn.setProcedureName(lotProcedureChangeStep.getProcedureName());
				lotTecn.setStepStateName(lotProcedureChangeStep.getActionStepState());
				lotTecn.setStepName(lotProcedureChangeStep.getStepName());
				lotTecn.setStepVersion(lotProcedureChangeStep.getStepVersion());
				lotTecn.setEquipmentId(lotProcedureChangeStep.getEquipmentId());
				lotTecn.setRecipeName(lotProcedureChangeStep.getRecipeName());
				lotTecn.setReticleName(lotProcedureChangeStep.getReticleName());
				lotTecn.setEquipmentRecipeName(lotProcedureChangeStep.getEquipmentRecipeName());
				lotTecn.setEdcName(lotProcedureChangeStep.getEdcName());
			}

			ADTable adTable = adManager.getADTable(Env.getOrgRrn(),LotProcedureTecnEditDialog.TABLE_NAME);
			LotProcedureTecnEditDialog dialog = new LotProcedureTecnEditDialog(adTable, lotTecn);
			if (dialog.open() == Dialog.OK) {
				lotTecn = (LotTecn) dialog.getAdObject();

				lotProcedureChangeStep.setEquipmentId(lotTecn.getEquipmentId());
				lotProcedureChangeStep.setRecipeName(lotTecn.getRecipeName());
				lotProcedureChangeStep.setReticleName(lotTecn.getReticleName());
				lotProcedureChangeStep.setEquipmentRecipeName(lotTecn.getEquipmentRecipeName());
				lotProcedureChangeStep.setEdcName(lotTecn.getEdcName());
				lotProcedureChangeStep.setLotTecn(lotTecn);

				changeLotProcedureStepListTableField.getListTableManager().update(lotProcedureChangeStep);
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void viewProcedureAdapter(Object object) {
		try {
            if (currentProcedure == null || currentLot == null) {
                return;
            }

            ProcessDefinition usedProcedure = currentProcedure.getUsedProcedure();
            Procedure fromProcedure = new Procedure();
            fromProcedure.setOrgRrn(Env.getOrgRrn());
            fromProcedure.setName(usedProcedure.getName());
            fromProcedure.setVersion(usedProcedure.getVersion());
            fromProcedure = (Procedure) prdManager.getSimpleProcessDefinition(fromProcedure, false);

			LotProcedure lotProcedure = new LotProcedure();
			lotProcedure.setOrgRrn(Env.getOrgRrn());
			lotProcedure.setLotRrn(currentLot.getObjectRrn());
			lotProcedure.setReplacedProcedureState(currentProcedure.getName());
			lotProcedure.setReplacedProcedureStatePath(currentProcedure.getPath());

			List<LotProcedureChangeStep> lotProcedureChangeSteps = (List<LotProcedureChangeStep>) changeLotProcedureStepListTableField.getListTableManager().getInput();

			Map<String, List<LotProcedureChangeStep>> addLotProcedureChangeSteps = lotProcedureChangeSteps.stream()
					.filter(p -> LotProcedureChangeStep.ACTION_TYPE_ADDSTEP.equals(p.getActionType()))
					.collect(Collectors.groupingBy(LotProcedureChangeStep::getActionStepState));
			List<LotProcedureChangeStep> removeLotProcedureChangeSteps = lotProcedureChangeSteps.stream()
					.filter(p -> LotProcedureChangeStep.ACTION_TYPE_REMOVESTEP.equals(p.getActionType()))
					.collect(Collectors.toList());

			Map<String, List<Step>> addSteps = Maps.newHashMap();
			for (String addStepState : addLotProcedureChangeSteps.keySet()) {
				List<Step> steps = Lists.newArrayList();
				for (LotProcedureChangeStep lotProcedureChangeStep : addLotProcedureChangeSteps.get(addStepState)) {
					Step step = new Step();
					step.setOrgRrn(Env.getOrgRrn());
					step.setName(lotProcedureChangeStep.getStepName());
					step.setVersion(lotProcedureChangeStep.getStepVersion());
//					step = (Step) prdManager.getSimpleProcessDefinition(step, false);
					steps.add(step);
				}
				addSteps.put(addStepState, steps);
			}

			List<StepState> removeSteps = Lists.newArrayList();
			PrdQueryAction action = PrdQueryAction.newIntance();
			action.setCopyNode(true);
			List<Node> childernNodes =  prdManager.getProcessDefinitionChildern(fromProcedure, action);
			for (LotProcedureChangeStep removeStepState : removeLotProcedureChangeSteps) {
				Optional<Node> optNode = childernNodes.stream()
						.filter(p -> p.getName().equals(removeStepState.getActionStepState())).findFirst();
				if (optNode.isPresent()) {
					removeSteps.add((StepState) optNode.get());
				}
			}

			lotProcedure = prdManager.createLotProcedureFrom(lotProcedure, fromProcedure, addSteps, removeSteps, false);

			List<Node> nodes = lotProcedure.getNodes();

//			nodes = ProcessDefinition.sortNodes(nodes, false, Maps.newHashMap());

			nodes = nodes.stream().filter(node -> node instanceof StepState || node instanceof ReworkState)
					.collect(Collectors.toList());

			LotProcedureViewDialog dialog = new LotProcedureViewDialog(FORM_VIEW_PROCEDURE, null,
					eventBroker, nodes);
			dialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void saveAdapter(Object object) {
		try {
			if (currentProcedure == null) {
				return;
			}
			ProcessDefinition usedProcedure = currentProcedure.getUsedProcedure();

			Set<String> states = Sets.newHashSet();
			states.add(LotProcedureChange.STATE_CREATED);
//			states.add(LotProcedureChange.STATE_APPROVED);
			
			List<LotProcedureChange> lotProcedureChanges = lotProcedureManager.getLotProcedureChangeList(Env.getOrgRrn(), currentLot.getLotId(), currentProcedure.getPath(), states);
	
			LotProcedureChange lotProcedureChange = null;
			if (CollectionUtils.isNotEmpty(lotProcedureChanges)) {
//				for (LotProcedureChange checkLotProcedureChange : lotProcedureChanges) {
//					if (LotProcedureChange.STATE_APPROVED.equals(checkLotProcedureChange.getState())) {
//						UI.showError(Message.getString("wip.lot_procedure_change_state_is_approved"));
//						return;
//					}
//				}
				lotProcedureChange = lotProcedureChanges.get(0);
			} else {
				lotProcedureChange = new LotProcedureChange();
				lotProcedureChange.setLotId(currentLot.getLotId());
				lotProcedureChange.setProcedureName(usedProcedure.getName());
				lotProcedureChange.setProcedureVersion(usedProcedure.getVersion());
				lotProcedureChange.setProcedureStatePath(currentProcedure.getPath());
			}
	
			List<LotProcedureChangeStep> lotProcedureChangeSteps = Lists.newArrayList();
			List<LotProcedureChangeStep> changeLotProcedureChangeSteps = (List<LotProcedureChangeStep>) changeLotProcedureStepListTableField.getListTableManager().getInput();
			lotProcedureChangeSteps.addAll(changeLotProcedureChangeSteps);
			lotProcedureChange.setLotProcedureChangeSteps(lotProcedureChangeSteps);

			if (CollectionUtils.isEmpty(lotProcedureChangeSteps)) {
				UI.showError(Message.getString("error.data_exception"));
				return;
			}
			
			lotProcedureManager.saveLotProcedureChange(lotProcedureChange, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			
			loadLotProcedureChangeStepList();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void partBroughtToTop(MPart part) {}

	@Override
	public void partDeactivated(MPart part) {}

	@Override
	public void partHidden(MPart part) {}

	@Override
	public void partVisible(MPart part) {}
	
	@Override
	public void partActivated(MPart part) {
		if (part.equals(mPart)) {
			//如果激活的是当前MPart
			ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);	
			//用于通过右键菜单显示页面,Attribute记录批次号
			 if ("New".equals(adEditor.getAttribute1())) {
				this.refreshAdapter(null);			
			} else if (!StringUtil.isEmpty(adEditor.getAttribute1())) {
				loadLotProcedureChangeEditData(adEditor.getAttribute1());
			}
			adEditor.setAttribute1(null);
		}
	}
	
	/**
	 * 加载编辑数据
	 * @param controlId
	 */
	public void loadLotProcedureChangeEditData(String controlId) {
		try {
			List<LotProcedureChange> lotProcedureChanges = adManager.getEntityList(Env.getOrgRrn(), LotProcedureChange.class, 
					Env.getMaxResult(), "controlId = '" + controlId + "'", "");
			if (CollectionUtils.isNotEmpty(lotProcedureChanges)) {
				flowCustomComposite.getTxtId().setText(lotProcedureChanges.get(0).getLotId());
				currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotProcedureChanges.get(0).getLotId(), true);
				flowCustomComposite.loadFlowTreeByLot(currentLot);
				
				//获取当前返工工步
				currentReworkState = prdManager.getCurrentReworkState(currentLot.getProcessInstanceRrn());
				
				//加载All的LotProcedureChangeStep
				loadLotProcedureChangeStepList();
				
				loadLotProcedureChangeData(lotProcedureChanges.get(0));
			}											
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
}
