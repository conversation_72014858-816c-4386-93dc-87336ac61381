package com.glory.mes.wip.lot.lotprocedure.setup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;

public class LotProcedureAddStepDialog extends GlcBaseDialog{

    private static final Logger logger = Logger.getLogger(LotProcedureAddStepDialog.class);

	private static final String CONTORL_FORM_STEP = "stepInfo";
	private static final String CONTORL_FORM_SHOW_INACTIVE_STEP = "showInActiveStep";
	private static final String CONTORL_FORM_SPECIFY_VERSION = "specifyVersion";
	
	private static final String FIELD_STEP_NAME = "name";
	private static final String FIELD_PROCEDURE_NAME = "procedureName";
	protected QueryFormField stepQueryFormField;
	protected BooleanField showInActiveStepField;
	protected BooleanField specifyVersionField;
	
	private String whereClause;
	
    public LotProcedureAddStepDialog(String adFormName, String authority, IEventBroker eventBroker){
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {     	
			stepQueryFormField = form.getFieldByControlId(CONTORL_FORM_STEP, QueryFormField.class);
			subscribeAndExecute(eventBroker, stepQueryFormField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
			
			showInActiveStepField = form.getFieldByControlId(CONTORL_FORM_SHOW_INACTIVE_STEP, BooleanField.class);
			showInActiveStepField.addValueChangeListener(new IValueChangeListener() {

				@Override
				public void valueChanged(Object sender, Object newValue) {
					boolean value = (boolean) newValue;
					List list = stepQueryFormField.getQueryForm().getTableManager().getInput();
					if (list != null && list.size() > 0) {
						if (value) {
							String whereClause = " (status = 'Active' OR status = 'InActive')";
							setWhereClause(whereClause);							
							queryAdapter(null);
						} else {
							String whereClause = " status = 'Active'";
							setWhereClause(whereClause);							
							queryAdapter(null);
						}
					}				
				}			
			});
			
			specifyVersionField = form.getFieldByControlId(CONTORL_FORM_SPECIFY_VERSION, BooleanField.class);		

        } catch (Exception e) {
            logger.error("LotProcedureAddStepDialog : Init createFormAction", e);
            e.printStackTrace();
        }
	}
	
	// 查询按钮事件
	protected void queryAdapter(Object object) {
		LinkedHashMap<String, IField> fields = stepQueryFormField.getQueryForm().getFields();
		try {
			String stepName = "";
			Long procedurerRrn = 0L;
			for (IField f : fields.values()) {
				Object value = f.getValue();
				if (FIELD_STEP_NAME.equals(f.getId()) && value != null) {
					stepName = value.toString();
				}
				if (FIELD_PROCEDURE_NAME.equals(f.getId()) && value != null) {
					procedurerRrn = Long.valueOf(value.toString());
				}
			}

			PrdManager prdManager = Framework.getService(PrdManager.class);
			ADManager adManager = Framework.getService(ADManager.class);

			List<Step> stepList = new ArrayList<>();
			if (StringUtils.isNotEmpty(stepName) && procedurerRrn > 0) {
				PrdQueryAction action = PrdQueryAction.newIntance();
				action.setCopyNode(true);			
				Procedure procedure = new Procedure();
				procedure.setObjectRrn(procedurerRrn);
				List<Node> nodeList = prdManager.getProcessDefinitionChildern(procedure, action);

				stepList = nodeList.stream().filter(node -> node instanceof StepState).map(stepState -> {
					return ((StepState) stepState).getUsedStep();
				}).collect(Collectors.toList());
							
				StringBuffer whereClause = new StringBuffer(" name like '" + stepName + "'");				
				if (StringUtils.isNotEmpty(getWhereClause())) {
					whereClause.append(" AND ");
					whereClause.append(getWhereClause());
				} else {
					whereClause.append(" AND status = 'Active' ");
				}					
				if (stepList != null && stepList.size() > 0) {
					whereClause.append(" AND ");
					whereClause.append(" name in ( :stepNameSet )");			
					Set stepNameSet = stepList.stream().map(Step::getName).collect(Collectors.toSet());	
					Map<String, Object> fieldMap = new HashMap<String, Object>();
					fieldMap.put("stepNameSet", stepNameSet);					
					stepList = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MIN_VALUE, Integer.MAX_VALUE, whereClause.toString(), "name" ,fieldMap);
				}	
				
			} else if (StringUtils.isNotEmpty(stepName)) {
				StringBuffer whereClause = new StringBuffer(" name like '" + stepName + "'");
				if (StringUtils.isNotEmpty(getWhereClause())) {
					whereClause.append(" AND ");
					whereClause.append(getWhereClause());
				} else {
					whereClause.append(" AND status = 'Active' ");
				}
				stepList = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MAX_VALUE, whereClause.toString(), "name");
				
			} else if (procedurerRrn > 0) {
				PrdQueryAction action = PrdQueryAction.newIntance();
				action.setCopyNode(true);
				
				Procedure procedure = new Procedure();
				procedure.setObjectRrn(procedurerRrn);
				List<Node> nodeList = prdManager.getProcessDefinitionChildern(procedure, action);

				stepList = nodeList.stream().filter(node -> node instanceof StepState).map(stepState -> {
					return ((StepState) stepState).getUsedStep();
				}).collect(Collectors.toList());
							
				StringBuffer whereClause = new StringBuffer();										
				if (stepList != null && stepList.size() > 0) {	
					whereClause.append(" name in ( :stepNameSet )");			
					if (StringUtils.isNotEmpty(getWhereClause())) {
						whereClause.append(" AND ");
						whereClause.append(getWhereClause());
						
						Set stepNameSet = stepList.stream().map(Step::getName).collect(Collectors.toSet());	
						Map<String, Object> fieldMap = new HashMap<String, Object>();
						fieldMap.put("stepNameSet", stepNameSet);
						stepList = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MIN_VALUE, Integer.MAX_VALUE, whereClause.toString(), "name", fieldMap);
					} 					
				}		
				
			} else {				
				StringBuffer whereClause = new StringBuffer();			
				if (StringUtils.isNotEmpty(getWhereClause())) {
					whereClause.append(getWhereClause());
				} else {
					whereClause.append(" status = 'Active' ");
				}
				stepList = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MAX_VALUE, whereClause.toString(), "name");
			}

			stepQueryFormField.getQueryForm().getTableManager().setInput(stepList);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public List<Step> getSelectedStepList() {
		List<Object> list = this.stepQueryFormField.getCheckedObjects();

		List<Step> stepList = new ArrayList<>();
		for (Object object : list) {
			Step step = (Step) object;
			if (!(Boolean)specifyVersionField.getValue()) {
				step.setVersion(null);
			}
			stepList.add((Step) object);
		}
		return stepList;
	}

	public String getWhereClause() {
		return whereClause;
	}

	public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	
}
