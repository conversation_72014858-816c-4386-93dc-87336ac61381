//package com.glory.mes.wip.lot;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import org.apache.log4j.Logger;
//import org.eclipse.jface.viewers.ISelectionChangedListener;
//import org.eclipse.jface.viewers.SelectionChangedEvent;
//import org.eclipse.jface.viewers.StructuredSelection;
//import org.eclipse.swt.SWT;
//import org.eclipse.swt.events.KeyAdapter;
//import org.eclipse.swt.events.KeyEvent;
//import org.eclipse.swt.graphics.Color;
//import org.eclipse.swt.layout.GridData;
//import org.eclipse.swt.layout.GridLayout;
//import org.eclipse.swt.widgets.Composite;
//import org.eclipse.swt.widgets.Display;
//import org.eclipse.swt.widgets.Label;
//import org.eclipse.swt.widgets.Text;
//
//import com.glory.framework.activeentity.client.ADManager;
//import com.glory.framework.activeentity.model.ADTable;
//import com.glory.framework.base.ui.nattable.ListTableManager;
//import com.glory.framework.base.ui.util.Env;
//import com.glory.framework.base.ui.util.Message;
//import com.glory.framework.base.ui.util.SWTResourceCache;
//import com.glory.framework.core.util.StringUtil;
//import com.glory.framework.runtime.Framework;
//import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
//import com.glory.mes.wip.client.LotManager;
//import com.glory.mes.wip.lot.provider.LotProviderEntry;
//import com.glory.mes.wip.model.Lot;
//
///**
// * 显示Lot列表
// */
//public class LotListComposite extends Composite {
//
//	private static final Logger logger = Logger.getLogger(LotListComposite.class);
//	
//	private static final String TABLE_NAME = "WIPLotByInput";
//	
//	private boolean checkFlag;
//	private boolean showQueryFlag;
//	
//	private ListTableManager lotTableManager;
//	private Text txtLotId;
//	
//	private int tableHeigthHint = 120;
//
//	public LotListComposite(Composite parent, int style, boolean checkFlag, boolean showQueryFlag) {
//		super(parent, style);
//		this.checkFlag = checkFlag;
//		this.showQueryFlag = showQueryFlag;
//	}
//
//	public void createPartControl() {
//		try {
//			this.setLayout(new GridLayout(1, false));
//			this.setLayoutData(new GridData(GridData.FILL_BOTH));
//			this.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));
//
//			Composite carrierComposite = new Composite(this, SWT.NONE);
//			int gridY = 2;
//			if (showLotFlag) {
//				gridY += 2;
//			}
//			
//			carrierComposite.setLayout(new GridLayout(gridY, false));
//
//	        Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
//	        if (StringUtil.isEmpty(lblBatch)) {
//				lblCarrierId.setText(Message.getString("wip.carrier_id"));
//	        } else {
//	        	lblCarrierId.setText(lblBatch);
//	        }
//			lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
//
//			txtBatchId = new Text(carrierComposite, SWT.BORDER);
//			txtBatchId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
//			GridData gText = new GridData();
//			gText.widthHint = 210;
//			txtBatchId.setLayoutData(gText);
//			txtBatchId.setTextLimit(32);
//			
//			txtBatchId.addKeyListener(new KeyAdapter() {
//				@Override
//				public void keyPressed(KeyEvent event) {
//					// 回车事件
//					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
//						String batchId = ((Text) event.widget).getText();
//						if (!StringUtil.isEmpty(batchId)) {
//							getLotsByBatchId(batchId);
//						}
//					}
//				}
//			});
//			if (showLotFlag) {
//				Label lblLotId = new Label(carrierComposite, SWT.NONE);
//				lblLotId.setText(Message.getString("wip.lot_id"));
//				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
//
//				txtLotId = new Text(carrierComposite, SWT.BORDER);
//				txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
//				txtLotId.setLayoutData(gText);
//				txtLotId.setTextLimit(64);
//				
//				txtLotId.addKeyListener(new KeyAdapter() {
//					@Override
//					public void keyPressed(KeyEvent event) {
//						// 回车事件
//						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
//							String lotId = ((Text) event.widget).getText();
//							if (!StringUtil.isEmpty(lotId)) {
//								getBatchLotsByLotId(lotId);
//							}
//						}
//					}
//				});
//			}
//			
//			Composite lotComposite = new Composite(this, SWT.NONE);
//			lotComposite.setLayout(new GridLayout(1, false));
//			
//			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
//			gridData.heightHint = tableHeigthHint;
//			lotComposite.setLayoutData(gridData);
//			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
//
//			ADManager adManager = Framework.getService(ADManager.class);
//			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
//			lotTableManager = new ListTableManager(lotTable, checkFlag);
//			lotTableManager.setAutoSizeFlag(true);
//			lotTableManager.newViewer(lotComposite);
//			lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
//				@Override
//				public void selectionChanged(SelectionChangedEvent event) {
//					StructuredSelection selection = (StructuredSelection) event.getSelection();
//					Lot lot = (Lot) selection.getFirstElement();
//					
//				}
//			});
//			
//		} catch (Exception e) {
//			logger.error("BatchLotComposite createPartControl error:", e);
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//	
//	public void getBatchLotsByLotId(String lotId) {
//		try {
//			Lot lot = LotProviderEntry.getLot(lotId);
//			if (lot != null) {
//				List<Lot> lots = new ArrayList<Lot>();
//				if (!StringUtil.isEmpty(lot.getBatchId())) {
//					LotManager lotManager = Framework.getService(LotManager.class);
//					lots = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
//				} else {
//					lots.add(lot);
//				}
//				
//				lotTableManager.setInput(lots);
//				// 默认全选
//				if (checkFlag) {
//					lotTableManager.setCheckedObject(lot);
//				}
//				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
//				lotTableManager.refresh();
//			
//				lotTableManager.setSelection(new StructuredSelection(new Object[] {lot}));
//			} else {
//				lotTableManager.setInput(new ArrayList<Lot>());
//				lotTableManager.refresh();
//
//				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
//				
//				lotTableManager.setSelection(new StructuredSelection(new Object[] {new Lot()}));
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//	
//	public void getLotsByBatchId(String batchId) {
//		try {
//			LotManager lotManager = Framework.getService(LotManager.class);
//			List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), batchId);
//
//			lotTableManager.setInput(lots);
//			// 默认全选
//			if (checkFlag) {
//				for (Lot lot : lots) {
//					lotTableManager.setCheckedObject(lot);
//				}
//			}
//			txtBatchId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
//			lotTableManager.refresh();
//			
//		} catch (Exception e) {
//			e.printStackTrace();
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//
//	public Text getTxtBatchId() {
//		return txtBatchId;
//	}
//
//	public void setTxtBatchId(Text txtCarrierId) {
//		this.txtBatchId = txtCarrierId;
//	}
//
//	public ListTableManager getLotTableManager() {
//		return lotTableManager;
//	}
//
//	public void setLotTableManager(ListTableManager lotTableManager) {
//		this.lotTableManager = lotTableManager;
//	}
//
//	public String getLblCarrier() {
//		return lblBatch;
//	}
//
//	public void setLblCarrier(String lblCarrier) {
//		this.lblBatch = lblCarrier;
//	}
//	
//	public int getTableHeigthHint() {
//		return tableHeigthHint;
//	}
//
//	public void setTableHeigthHint(int tableHeigthHint) {
//		this.tableHeigthHint = tableHeigthHint;
//	}
//}
