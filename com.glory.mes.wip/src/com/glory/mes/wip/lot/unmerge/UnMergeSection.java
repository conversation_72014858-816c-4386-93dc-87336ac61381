package com.glory.mes.wip.lot.unmerge;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class UnMergeSection extends LotSection {
	
	public static final String KEY_UNMERGE = "unMerge";
	
	protected AuthorityToolItem itemUnMerge;
	protected UnMergeForm unMergeForm;
	
	public UnMergeSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.unmerge_section"));
		initAdObject();
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		unMergeForm = new UnMergeForm(composite, SWT.NONE, tab, mmng);
		return unMergeForm;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemUnMerge(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemUnMerge(ToolBar tBar) {
		itemUnMerge = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() +"."+ KEY_UNMERGE);
		itemUnMerge.setText(Message.getString("wip.unmerge"));
		itemUnMerge.setImage(SWTResourceCache.getImage("merge"));
		itemUnMerge.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unMergeAdapter(event);
			}
		});
	}

	protected void unMergeAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						LotManager lotManager = Framework
								.getService(LotManager.class);
						LotAction lotAction = ((UnMergeForm) detailForm)
								.getLotAction();
						Lot lot = ((UnMergeForm) detailForm).getLot();
						lot.setOperator1(Env.getUserName());
			
						if (lot.getChildrenLots() == null || lot.getChildrenLots().size() == 0) {
							UI.showInfo(Message.getString("wip.unmerge_child_lot_is_null"));
							return;
						}
						lotManager.unMergeLot(lot, lot.getChildrenLots(), lotAction, Env.getSessionContext());
					}
					UI.showInfo(Message.getString("wip.unmerge_successed"));// 弹出提示框
					refresh();
					setFocus();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus)) {
			itemUnMerge.setEnabled(true);
		} else {
			itemUnMerge.setEnabled(false);
		}
	}

}
