package com.glory.mes.wip.lot.reassign;

import java.awt.Toolkit;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.carrier.CarrierLotComposite;
import com.glory.mes.wip.comp.ComponentAssignComposite;
import com.glory.mes.wip.model.Lot;

public class LotReassignEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.reassign.LotReassignEditor";

	private LotReassignSection section;
	
	protected LotSortingReassignLeftComposite sourceCarrierLotComposite;
	private CarrierLotComposite targetCarrierLotComposite;
	
	@Inject
	protected ESelectionService selectionService;
	
	@Inject
	protected MPart mPart;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		try {
			ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);

			configureBody(parent);
			
			FormToolkit toolkit = new FormToolkit(UI.getActiveShell().getDisplay());
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_BOTH));
			ManagedForm mform = new ManagedForm(toolkit, form);
			
			Composite body = mform.getForm().getBody();
	        GridLayout layout = new GridLayout(2, false);
	        body.setLayout(layout);
	        body.setLayoutData(new GridData(GridData.FILL_VERTICAL));
//	        body.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));
	        
	        Composite left = toolkit.createComposite(body, SWT.NONE);
			GridData gdLeft = new GridData(GridData.FILL_VERTICAL);
			gdLeft.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width)/3;
			left.setLayout(new GridLayout(1, false));
			left.setLayoutData(gdLeft);
			
			// left sourceCarrierComposite
			sourceCarrierLotComposite = new LotSortingReassignLeftComposite(left, SWT.BORDER, true, true, true, false);
			sourceCarrierLotComposite.setLblCarrier(Message.getString("wip.source_carrier_id"));
			sourceCarrierLotComposite.createPartControl();
			
			HeaderText sourceCarrierId = sourceCarrierLotComposite.getTxtCarrierId();
			sourceCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String sourceCarrierId = ((Text)event.widget).getText();
						
						ComponentAssignComposite assignComposite = section.getAssignComposite();
						// 清空源component列表
						assignComposite.sourceComponentComposite.getTableManager().getInput().clear();
						// 默认全选
						assignComposite.sourceComponentComposite.checkedAll();
						
						// 显示源载具名称
						section.setTxtSourceCarrierId(sourceCarrierId);
					}
				}
			});
			
			ListTableManager sourceCarrierTableManager = sourceCarrierLotComposite.getLotTableManager();
			sourceCarrierTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					try {
						// 点击行任意地方实现checkBox选中与否
						StructuredSelection structuredSelection = (StructuredSelection) event.getSelection();
						Lot lot = (Lot) structuredSelection.getFirstElement();
						if (lot == null) {
							return;
						}

						// 设置源批次
						sourceCarrierTableManager.setCheckedObject(lot);
						section.assignComposite.sourceLot = lot;

						// 设置源载具
						String durableId = lot.getDurable();
						if (StringUtil.isEmpty(durableId)) {
							UI.showError("wip.lot_not_assign_durable");
							return;
						}
						
						sourceCarrierId.setText(durableId);
						section.setTxtSourceCarrierId(durableId);
						
						// 显示数据
						section.assignComposite.searchCarrier(durableId, section.assignComposite.sourceComponentComposite, true);
						section.assignComposite.sourceComponentComposite.checkedAll();
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
					}
				}
			});
			
			// left targetCarrierComposite
			targetCarrierLotComposite = new CarrierLotComposite(left, SWT.BORDER, false);
			targetCarrierLotComposite.setLblCarrier(Message.getString("wip.target_carrier_id"));
			targetCarrierLotComposite.createPartControl();
			
			HeaderText txtTargetCarrierId = targetCarrierLotComposite.getTxtCarrierId();
			txtTargetCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String targetCarrierId = ((Text)event.widget).getText();
						section.assignComposite.searchCarrier(targetCarrierId, section.assignComposite.targetComponentComposite, true);
						section.setTxtTargetCarrierId(targetCarrierId);
					}
				}
			});
			
			// right info
			Composite right = toolkit.createComposite(body, SWT.NONE);
			right.setLayout(new GridLayout(1, false));
			right.setLayoutData(new GridData(GridData.FILL_BOTH));
//			right.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));

			createSection(adTable);
			section.createContents(mform, right);
			section.setLotReassignEditor(this);
			mPart.setLabel(Message.getString("wip.reassign_carrier"));
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createSection(ADTable adTable) {
		section = new LotReassignSection(adTable);
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public LotSortingReassignLeftComposite getSourceCarrierLotComposite() {
		return sourceCarrierLotComposite;
	}

	public void setSourceCarrierLotComposite(LotSortingReassignLeftComposite sourceCarrierLotComposite) {
		this.sourceCarrierLotComposite = sourceCarrierLotComposite;
	}
	
}
