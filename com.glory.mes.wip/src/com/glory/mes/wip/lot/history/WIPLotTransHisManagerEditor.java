package com.glory.mes.wip.lot.history;


import java.util.List;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.widgets.Display;
import org.osgi.service.event.Event;

import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class WIPLotTransHisManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.history.WIPLotTransHisManagerEditor";
	
	protected static final String FIELD_ENTERPRESS = "EnterPressed";
	
	public static final String FIELD_LOTID = "lotId";
	public static final String FIELD_LOTHIS = "lotHis";

	public static final String BUTTON_DETAILSERCH = "detailSerch";

	protected CustomField lotIdField;
	protected ListTableManagerField lotHisField;

	protected EnterPressComposite lotIdEnterPressComposite;
	protected HeaderText lotText;
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotIdField = form.getFieldByControlId(FIELD_LOTID, CustomField.class);
		lotHisField = form.getFieldByControlId(FIELD_LOTHIS, ListTableManagerField.class);
		
		lotIdEnterPressComposite = (EnterPressComposite) lotIdField.getCustomComposite();
		lotText = lotIdEnterPressComposite.getTxtLot();

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DETAILSERCH), this::detailSerchAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_ENTERPRESS), this::enterPressed);
		
		lotHisField.getListTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				detailSerchAdapter(null);
			}
		});
	}
	
	@Override
	public void partActivated(MPart part) {
		if (part.equals(mPart)) {
			//如果激活的是当前MPart
			ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);
			String lotId = adEditor.getAttribute1();
			if (!StringUtil.isEmpty(lotId)) {
				lotText.setText(lotId);
				inputLotHisList(lotId, null);
			}
		}
	}

	private void detailSerchAdapter(Object object) {
		try {
			Object ob = lotHisField.getListTableManager().getSelectedObject();
			if (null == ob) {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			LotHis line = (LotHis) ob;
			ADManager manager = Framework.getService(ADManager.class);
			LotHis lothis = new LotHis();
			lothis = (LotHis) manager.getEntity(line);
			if (LotStateMachine.TRANS_SCRAPLOT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_UNSCRAPLOT.equals(lothis.getTransType())) {
				WIPScrapSBDHisViewDialog dialog = new WIPScrapSBDHisViewDialog("WIPScrapSBDHisViewDialog", null, eventBroker, lothis);
				dialog.open();
			} else if (LotStateMachine.TRANS_SPLITLOT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_MERGELOT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_SPLITOUT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_MERGEIN.equals(lothis.getTransType())) {
				WIPSplitLotInfoDialog dialog = new WIPSplitLotInfoDialog("WIPSplitLotInfoDialog", null, eventBroker, lothis);
				dialog.open();
			} else if (LotStateMachine.TRANS_EDC.equals(lothis.getTransType())) {
				edcAdapter(lothis);
			} else {
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.LotHisNoTranType()));
			}
		
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	private void enterPressed(Object object) {
		try {
			Event event = (Event) object;
			ADBase adBase = (ADBase) event.getProperty(GlcEvent.PROPERTY_DATA);
			Lot currentLot = (Lot) adBase;
			inputLotHisList(null, currentLot);
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	private void inputLotHisList(String lotId, Lot lot) {
		try {
			Lot currentLot = new Lot();
			if (!StringUtil.isEmpty(lotId) && lot == null) {
				currentLot = LotProviderEntry.getLot(lotId);
			} else {
				currentLot = lot;
			}
			ADManager adManager = Framework.getService(ADManager.class);
			List<LotHis> lotHiss = adManager.getEntityList(Env.getOrgRrn(), 
					LotHis.class, Env.getMaxResult(), " lotRrn = '" + currentLot.getObjectRrn() + "'", "transTime Desc");
			lotHisField.getListTableManager().setInput(lotHiss);
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	protected void edcAdapter(LotHis lothis) {
		try {
			Lot lot = new Lot();
			lot.setObjectRrn(lothis.getLotRrn());
			ADManager adManager = Framework.getService(ADManager.class);
			lot = (Lot)adManager.getEntity(lot);
			String condition = " hisSeq = '"+lothis.getHisSeq()+"'";
			List<EdcData> datas = adManager.getEntityList(Env.getOrgRrn(), 
					EdcData.class, Env.getMaxResult(), condition, "");
			EdcSetCurrent edc = new EdcSetCurrent();
			edc.setHistorySeq(lothis.getHisSeq());
			edc.setItemSetRrn(datas.get(0).getEdcSetRrn());
			edc.setEdcFlag(EdcSetCurrent.FLAG_TEMP);
			
			EdcEvent event = new EdcEvent();
			event.setLot(lot);
			
			EdcHisDialog d = new EdcHisDialog(
						Display.getCurrent().getActiveShell(), edc, event, lothis);
				if (d == null)
					return;
				if (d.open() == Dialog.OK) {
				}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}