package com.glory.mes.wip.lot;

import javax.annotation.PreDestroy;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.e4.ui.workbench.modeling.IPartListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.core.util.StringUtil;

public class LotEditor implements IPartListener {

	@Inject
	protected ESelectionService selectionService;
	
	@Inject
	protected EPartService partService;
	
	@Inject
	protected MPart mPart;
	
	protected LotSection section;

	protected LotListSection listSection;

	public void postConstruct(Composite parent) {
		partService.addPartListener(this);
	}
	
	@PreDestroy
	public void preDestroy() {
		partService.removePartListener(this);
	}
	
	@Override
	public void partActivated(MPart part) {
		if (part.equals(mPart)) {
			//如果激活的是当前MPart
			ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);
			
			//用于通过右键菜单显示页面,Attribute记录批次号
			if (!StringUtil.isEmpty(adEditor.getAttribute1())) {
				if (section != null) {
					section.setLotById(adEditor.getAttribute1());
				}
				if (listSection != null) {
					listSection.setLotById(adEditor.getAttribute1());
				}
				adEditor.setAttribute1(null);
			}
		}
	}

	@Override
	public void partBroughtToTop(MPart part) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void partDeactivated(MPart part) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void partHidden(MPart part) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void partVisible(MPart part) {
		// TODO Auto-generated method stub
		
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
}
