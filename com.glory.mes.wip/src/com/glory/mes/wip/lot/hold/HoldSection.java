package com.glory.mes.wip.lot.hold;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.mes.wip.model.LotStateMachine;

public class HoldSection extends LotSection {
	private static final Logger logger = Logger.getLogger(EntitySection.class);

	protected AuthorityToolItem itemHold;
	
	public static final String KEY_HOLD = "hold";
	
	protected EntityForm holdForm;
	protected LotAction lotAction = new LotAction();
	
	public HoldSection() {
		super();
	}

	public HoldSection(ADTable table) {
		super(table);
		this.eventId = LotStateMachine.TRANS_HOLDLOT;
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.hold_sectiontitle"));
		initAdObject();
		Composite client = (Composite) section.getClient();

		holdForm = new EntityForm(client, SWT.NONE, new LotAction(), getADTable1(), form.getMessageManager());
		holdForm.setLayout(new GridLayout());
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = 140;
		holdForm.setLayoutData(gd);
	}

	
	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemHold(ToolBar tBar) {
		itemHold = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_HOLD);
		itemHold.setAuthEventAdaptor(this::holdAdapter);
		itemHold.setText(Message.getString("wip.hold"));
		itemHold.setImage(SWTResourceCache.getImage("hold-lot"));
//		itemHold.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				holdAdapter(event);
//			}
//		});
	}

	protected void holdAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					if (holdForm.validate()) {
						holdForm.saveToObject();
						lotAction = (LotAction) holdForm.getObject();
						LotHold lotHold = new LotHold();
						lotHold.setHoldCode(lotAction.getActionCode());
						lotHold.setHoldReason(lotAction.getActionReason());
						lotHold.setHoldOwner(lotAction.getActionOperator());
						lotHold.setHoldComment(lotAction.getActionComment());
						LotManager lotManager = Framework.getService(LotManager.class);
						//Lot lot = (Lot) this.getAdObject();
						String lotid = ((Lot) this.getAdObject()).getLotId();
						Lot lot = lotManager.getLotByLotId(Env.getSessionContext().getOrgRrn(), lotid);
						Boolean result = checkLotStateModel(lot);
						if(result) {
							String operator = Env.getUserName();
							if (itemHold.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
								operator = (String) itemHold.getData(LotAction.ACTION_TYPE_OPERATOR);
							}
							lot.setOperator1(operator);
							SessionContext sc = Env.getSessionContext();
							sc.setUserName(operator);
							
							//暂停码重复是否卡控改为由系统参数控制
							Boolean isHoldCodeRepeateCheck = false;
							SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
					        if (MesCfMod.isHoldCodeRepeateCheck(Env.getOrgRrn(), sysParamManager)) {
					        	isHoldCodeRepeateCheck = true;
					        }
							lotManager.holdLotInternal(lot, lotHold, LotStateMachine.TRANS_HOLDLOT, isHoldCodeRepeateCheck, true, null, sc);
							UI.showInfo(Message.getString("wip.hold_successed"));// 弹出提示框
							refresh();
						}else {
							UI.showWarning(Message.getString("wip.lot_state_is_run"));
							return;
						}
						
					} else {
						UI.showWarning(Message.getString("warn.required_entry"));
						return;
					}
				}
			}
			txtLot.setFocus();
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
//	@Override
//	protected EntityForm getForm(Composite composite, ADTab tab) {
//		itemForm = new HoldForm(composite, SWT.NONE, tab, mmng);
//		return itemForm;
//	}
	
	@Override
	public void lotStatusChanged(Lot lot){
		if (lot != null) {
			Boolean result = checkLotStateModel(lot);
			if (result == null) {
				if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState())
						|| LotStateMachine.STATE_RUN.equalsIgnoreCase(lot.getState())
						|| LotStateMachine.STATE_TRACKOUT.equalsIgnoreCase(lot.getState())
						|| LotStateMachine.STATE_DISP.equalsIgnoreCase(lot.getState())) {
					itemHold.setEnabled(true);
				} else {
					itemHold.setEnabled(false);
				}
				
			} else if (result) {
				itemHold.setEnabled(true);
			} else {
				itemHold.setEnabled(false);
			}
		} else {
			itemHold.setEnabled(false);
		}
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));				
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
		if(holdForm != null) {
			lotAction= new LotAction();
			holdForm.setObject(lotAction);
			holdForm.loadFromObject();
		}
	}

	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPHoldAction");
		return midTable;
	}
}
