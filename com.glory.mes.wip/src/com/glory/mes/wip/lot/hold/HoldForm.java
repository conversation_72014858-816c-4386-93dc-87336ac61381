package com.glory.mes.wip.lot.hold;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADOwnerRefList;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;

public class HoldForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(HoldForm.class);

	private IField fieldHoldCode;
	private static final String HOLDCODE = "HoldCode";
	private static final String HOLDCODE_ID = "holdCode";
	private IField fieldHoldReason;
	private static final String HOLDREASON = "HoldReason";
	private static final String HOLDREASON_ID = "holdReason";
	private IField fieldHoldOwner;
	private static final String HOLDOWNER = "HoldOwner";
	private static final String HOLDOWNER_ID = "holdOwner";
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "holdComment";

	private String holdCodeRefName;
	protected LotHold lotHold = new LotHold();

	public HoldForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", Message.getString("wip.hold_info"));
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);

			fieldHoldCode = createUserRefList(HOLDCODE_ID, Message.getString("wip.holdcode"), "HoldCode", false);	
			fieldHoldReason = createText(HOLDREASON_ID, Message.getString("wip.holdreason"), "", 256);

			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADUserGroup");
			ADRefTable refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setKeyField("name");
			refTable.setTextField("name");
			fieldHoldOwner = createSearchField(HOLDOWNER_ID, Message.getString("wip.holdowner"), adTable, refTable, "", SWT.READ_ONLY);
			fieldHoldOwner.setADManager(adManager);
			
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);

			addField(HOLDCODE, fieldHoldCode);
			addField(HOLDREASON, fieldHoldReason);
			addField(HOLDOWNER, fieldHoldOwner);
			addField(COMMENT, fieldComment);			
		} catch (Exception e) {
			logger.error("HoldLotForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.equals(fieldHoldCode)
						&& !f.equals(fieldHoldReason)
						&& !f.equals(fieldComment) && !f.equals(fieldHoldOwner)) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public void setEnabled() {
		if (object != null && object instanceof ADBase) {
			ADBase base = (ADBase) object;
			for (IField f : fields.values()) {
				ADField adField = adFields.get(f.getId());
				if (adField != null && !adField.getIsEditable()) {
					if (base.getObjectRrn() == null || base.getObjectRrn() == 0) {
						f.setEnabled(true);
					} else {
						f.setEnabled(false);
					}
				}
			}
			fieldHoldCode.setEnabled(true);
			fieldHoldReason.setEnabled(true);
			fieldHoldOwner.setEnabled(true);
			fieldComment.setEnabled(true);
		}
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Lot lot = (Lot) object;
			if (lot != null && lot.getObjectRrn() != null) {
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) prdManager.getSimpleProcessDefinition(step);
				holdCodeRefName = step.getHoldCodeSrc(); // 取得对应的保留码
			} else {
				holdCodeRefName = "";
			}

			XCombo combo = (XCombo) ((RefTableField)fieldHoldCode).getComboControl();
			RCPUtil.refreshUserRefListCombo(combo, holdCodeRefName);

			fieldHoldCode.setValue("");
			fieldHoldCode.refresh();
			((TextField) fieldHoldReason).setText("");
			fieldHoldOwner.setValue("");
			((TextField) fieldComment).setText("");
		} catch (Exception e) {
			logger.error("HoldLotForm : refresh()", e);
		}
	}

	@Override
	// 保存到数据库
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			lotHold.setHoldCode(fieldHoldCode.getValue().toString());
			lotHold.setHoldReason(fieldHoldReason.getValue().toString());
			lotHold.setHoldOwner(DBUtil.toString(fieldHoldOwner.getValue()));
			lotHold.setHoldComment(fieldComment.getValue().toString());
			return true;
		}
		return false;
	}

	@Override
	public boolean validate() {
		boolean validFlag = true;
		if (fieldHoldCode.getValue() != null
				&& !"".equals(String.valueOf(fieldHoldCode.getValue()).trim())) {
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(HOLDCODE_ID, String.format(Message
					.getString("common.ismandatry"), HOLDCODE_ID), null,
					IMessageProvider.ERROR, fieldHoldCode.getControls()[1]);
		}

//		if (fieldHoldOwner.getValue() != null
//				&& !"".equals(String.valueOf(fieldHoldOwner.getValue()).trim())) {
//			validFlag = validFlag && true;
//		} else {
//			validFlag = validFlag && false;
//			mmng.addMessage(HOLDOWNER_ID, String.format(Message
//					.getString("common.ismandatry"), HOLDOWNER_ID), null,
//					IMessageProvider.ERROR, fieldHoldOwner.getControls()[1]);
//		}
		return validFlag;
	}

	public LotHold getLotHold() {
		return lotHold;
	}

}
