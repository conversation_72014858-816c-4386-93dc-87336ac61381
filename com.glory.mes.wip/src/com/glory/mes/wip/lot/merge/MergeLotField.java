package com.glory.mes.wip.lot.merge;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.base.merge.MergeRuleResult.MergeRuleException;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class MergeLotField extends AbstractField {
	
	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
	protected Text txtMergeLot;
	protected ListTableManager viewer;
    protected SquareButton remove, add;
    protected IManagedForm form;
    private Lot lot;
    
    public MergeLotField(String id, String label, ListTableManager viewer, IManagedForm form) {
        super(id);
        this.viewer = viewer;
        this.label = label;
        this.form = form;
    }
    
    public MergeLotField(String id, ListTableManager viewer, int style) {
        super(id);
        this.viewer = viewer;
        mStyle = style;
    }
    
    @Override
	public void createContent(Composite composite, FormToolkit toolkit) {
    	toolkit.createLabel(composite, Message.getString("wip.merge_lot"));
    	txtMergeLot = toolkit.createText(composite, "", SWT.BORDER);
		txtMergeLot.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
		GridData gText = new GridData();
		gText.widthHint = 220;
		txtMergeLot.setLayoutData(gText);
		txtMergeLot.setTextLimit(64);
		txtMergeLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					searchLot(lotId);
					tLotId.selectAll();
					break;
				}
			}

		});
    	
    	int i = 0;
		String labelStr = getLabel();
		if (labelStr != null) {
			mControls = new Control[2];
			Label label = toolkit.createLabel(composite, labelStr);
			mControls[0] = label;
			i = 1;
		} else {
			mControls = new Control[1];
		}

		Composite container = toolkit.createComposite(composite, SWT.NONE);
		GridData gd1 = new GridData(GridData.FILL_BOTH);
		gd1.grabExcessHorizontalSpace = true;
		gd1.horizontalAlignment = SWT.FILL;
		container.setLayoutData(gd1);
		container.setLayout(new GridLayout(1, false));
		
		Composite tableContainer = toolkit.createComposite(container, SWT.NONE);
		GridData gd2 = new GridData(GridData.FILL_BOTH);
		gd2.grabExcessHorizontalSpace = true;
		gd2.horizontalAlignment = SWT.FILL;
		tableContainer.setLayoutData(gd2);
		tableContainer.setLayout(new GridLayout(1, false));
		viewer.newViewer(tableContainer);
		viewer.refresh();
		
		createButtons(toolkit, container);
		add.addSelectionListener(getAddListener());
		remove.addSelectionListener(getDeleteListener());

		mControls[i] = container;
	}
    
    public void searchLot(String lotId) {
		try {
			if (lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("error.no_lot_input"));
				return;
			}
			Lot mergeLot = LotProviderEntry.getLot(lotId);
			if (mergeLot == null) {
				UI.showError(Message.getString("error.lot_not_exist"));
				return;
			}
			if (lot.getObjectRrn().equals(mergeLot.getObjectRrn())) {
				UI.showError(Message.getString("wip.lotid_repeat"));
				return;
			}
			
			List<Lot> cLots = new ArrayList<Lot>();
			cLots.add(mergeLot);
			//检查合批规则
			LotManager lotManager = Framework.getService(LotManager.class);
			MergeRuleResult result = lotManager.checkLotMergeRule(lot, cLots);
			if (!result.isSuccess()) {
				MergeRuleException exception = result.getException();
				throw new ClientParameterException(exception.getExceptionName(),exception.getExceptionParams());
			}
			
			List<Lot> mergeLots = (List<Lot>)getValue();
			if (mergeLots == null) {
				mergeLots = new ArrayList<Lot>();
			}
			if (mergeLots.contains(mergeLot)) {
				UI.showError(Message.getString("wip.box_already_exists"));
				return;
			}
			mergeLots.add(mergeLot);
			filter(mergeLots);
		} catch (ClientParameterException e) {
        	ExceptionHandlerManager.asyncHandleException(e);
        } catch (Exception e) {
			throw new ClientException(e);
		}
	}
    
    private SelectionListener getAddListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if(lot == null || lot.getObjectRrn() == null) {
					UI.showError(Message.getString("wip.merge_noparent"));
					return;
				}
				MergeLotDialog dialog = new MergeLotDialog(Display.getCurrent().getActiveShell(), form, lot);
				List<Lot> value = (List<Lot>)getValue();
				dialog.setExistMergedLots(value);
				if (dialog.open() == Dialog.OK) {
					List<Lot> selectedLots = dialog.getSelectionLots();
					if(value != null) {
						for(Lot lot : selectedLots){
							if (!value.contains(lot)) {
								value.add(lot);
							}
						}
						filter(value);
					} else {
						filter(selectedLots);
					}
				}
			}
		};
	}
    
    public void filter(List<Lot> list) {
		setValue(list);
		refresh();
	}
    
    private SelectionListener getDeleteListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				List<Object> checkedObject = viewer.getCheckedObject();
				
				List<Lot> removedLots = (List<Lot>)getValue();
				if(checkedObject != null && !checkedObject.isEmpty()) {
					for(Object o : checkedObject) {
						Lot lot = (Lot)o;
						removedLots.remove(lot);
					}
					filter(removedLots);
				}
			}
		};
	}

    public void createButtons(FormToolkit toolkit, Composite composite) {
		Composite bn = toolkit.createComposite(composite, SWT.NONE);
		bn.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		bn.setLayout(new GridLayout(4, false));
		GridData g = new GridData(GridData.FILL_BOTH);
		g.horizontalSpan = 2;
		g.horizontalAlignment = GridData.END;
		bn.setLayoutData(g);
		add = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonAdd()), "DEFAULT");
		remove =  UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonDelete()), "DEFAULT");
	}

	@Override
	public void refresh() {
		List<Lot> val = (List<Lot>)getValue();
        if(val != null) {
        	viewer.setInput(val);
        } else {
        	viewer.setInput(new ArrayList<Lot>());
        }
	}
	
	public void setManagedForm(IManagedForm form) {
		this.form = form;
	}
	
	@Override
    public void setEnabled(boolean enabled) {
    	super.setEnabled(enabled);
    	this.add.setEnabled(enabled);
    	this.remove.setEnabled(enabled);
    }
	
	public void setLot(Lot lot) {
		this.lot = lot;
	}

	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
