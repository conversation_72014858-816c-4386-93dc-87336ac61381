package com.glory.mes.wip.lot.merge;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.lot.split.SplitForm;
import com.glory.mes.wip.model.Lot;

public class MergeForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(SplitForm.class);
	private ListTableManager listTableManager; 
	
	private IField field;	
	protected IManagedForm form;
	
	private IField fieldComment;
	
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";
	
	private static final String FIELDID_MERGE = "mergeLots";
	
	private static final String AD_TABLE_MERGE = "WIPLotMerge";
	
	public MergeForm(Composite parent, int style, ADTab tab, IManagedForm form) {
		super(parent, style, tab, form.getMessageManager());
		this.form = form;
		((MergeLotField)field).setManagedForm(form);
	}

	public MergeForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}
	
	@Override
	public void addFields() {
		super.addFields();
		
		ADField separatorADField = new ADField();
		separatorADField.setIsSameline(true);
		SeparatorField separatorField = createSeparatorField("Separator", "");
		separatorField.setADField(separatorADField);
		this.addField("Separator", separatorField);
		
		getMergeField();
		
		fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);
        ADField adField = new ADField();
        adField.setIsMandatory(true); //必须
		adField.setIsSameline(true);
		fieldComment.setADField(adField);
		addField(COMMENT, fieldComment);
		
		
	}	

	public IField getMergeField(){
//		String displayLabel = Message.getString("wip.merge_lot");
		try{
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE_MERGE);
			listTableManager = new ListTableManager(adTable, true);
			field = new MergeLotField(FIELDID_MERGE, null, listTableManager, form);
			ADField adField = new ADField();
			adField.setIsSameline(true);
			field.setADField(adField);
			addField(FIELDID_MERGE, field);
		} catch (Exception e){
			logger.error("SplitLotForm : Init tablelist", e);
		}
		return field;
	}
	
	@Override
	public void loadFromObject() {
		if (object != null){
			for (IField f : fields.values()){
				if(!(f instanceof SeparatorField) && !f.getId().equals(COMMENT_ID) && !f.getId().equals(FIELDID_MERGE)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}			
			refresh();
			setEnabled();
		}
    }
	
	public List<Lot> getMergedLots() {
		return (List<Lot>)((MergeLotField)field).getValue();
	}
	
	public void setLot(Lot lot) {
		((MergeLotField)field).setLot(lot);
	}
	
	@Override
	public void refresh() {
		super.refresh();
		field.setValue(null);
		field.refresh();
	}

	public IField getFieldComment() {
		return fieldComment;
	}

	public void setFieldComment(IField fieldComment) {
		this.fieldComment = fieldComment;
	}
	
	
	
}
