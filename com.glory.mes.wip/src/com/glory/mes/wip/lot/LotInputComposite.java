package com.glory.mes.wip.lot;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.FormLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * 显示Lot列表
 */
public class LotInputComposite extends Composite {

	private static final Logger logger = Logger.getLogger(LotInputComposite.class);

	private static final String TABLE_NAME = "WIPLotByInput";

	private boolean checkFlag;
	private boolean queryFlag;

	private QueryForm queryForm;
	private ADTable queryTable;
	private List<String> lotStates;

	private ListTableManager lotTableManager;
	private HeaderText txtLotId;

	private int tableHeigthHint = 900;

	public LotInputComposite(Composite parent, int style, boolean checkFlag) {
		super(parent, style);
		this.checkFlag = checkFlag;
	}

	public LotInputComposite(Composite parent, int style, boolean checkFlag, ADTable queryTable,
			List<String> lotStates) {
		super(parent, style);
		this.checkFlag = checkFlag;
		this.queryFlag = true;
		this.queryTable = queryTable;
		this.lotStates = lotStates;
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			ADManager adManager = Framework.getService(ADManager.class);
			if (queryFlag) {
				Composite queryComp = new Composite(this, SWT.NONE);
				GridData gd = new GridData(GridData.FILL_HORIZONTAL);
				queryComp.setLayoutData(gd);

				FormLayout formLayout = new FormLayout();
				formLayout.marginHeight = 0;
				formLayout.marginWidth = 10;
				queryComp.setLayout(formLayout);

				queryForm = new QueryForm(adManager, queryComp, SWT.NONE, queryTable, null);

				int gridY = 1;
				if (queryForm.getADTable().getGridYQuery() != null) {
					gridY = queryForm.getADTable().getGridYQuery().intValue();
				}

				SquareButton btnQuery = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
				btnQuery.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));
				FormData fd = new FormData();
				fd.bottom = new FormAttachment(queryForm, -20, SWT.BOTTOM);
				fd.left = new FormAttachment(queryForm, 20, SWT.RIGHT);
				btnQuery.setLayoutData(fd);
				btnQuery.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						queryAdapter();
					}
				});

				SquareButton btnRemove = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
				btnRemove.setText(Message.getString(ExceptionBundle.bundle.CommonRemove()));
				FormData fdRemove = new FormData();
				fdRemove.bottom = new FormAttachment(btnQuery, -20, SWT.BOTTOM);
				fdRemove.left = new FormAttachment(btnQuery, 20, SWT.RIGHT);
				btnRemove.setLayoutData(fd);
				btnRemove.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						removeAdapter();
					}
				});
			} else {
				Composite queryComposite = new Composite(this, SWT.NONE);
				int gridY = 4;

				queryComposite.setLayout(new GridLayout(gridY, false));

				Label lblLotId = new Label(queryComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtLotId = new HeaderText(queryComposite, SWTResourceCache.getImage("header-text-lot"));
				txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtLotId.setTextLimit(64);

				txtLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// 回车事件
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText();
							if (!StringUtil.isEmpty(lotId)) {
								getLotByLotId(lotId);
							}
						}
					}
				});

				SquareButton btnAdd = UIControlsFactory.createButton(queryComposite, UIControlsFactory.BUTTON_DEFAULT);
				btnAdd.setText(Message.getString(ExceptionBundle.bundle.CommonAdd()));
				btnAdd.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						String lotId = txtLotId.getText();
						if (!StringUtil.isEmpty(lotId)) {
							getLotByLotId(lotId);
						}
					}
				});

				SquareButton btnRemove = UIControlsFactory.createButton(queryComposite,
						UIControlsFactory.BUTTON_DEFAULT);
				btnRemove.setText(Message.getString(ExceptionBundle.bundle.CommonRemove()));
				btnRemove.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						removeAdapter();
					}
				});
			}

//			Composite lotComposite = new Composite(this, SWT.NONE);
//			lotComposite.setLayout(new GridLayout(1, false));
//
//			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
//			gridData.heightHint = tableHeigthHint;
//			lotComposite.setLayoutData(gridData);
//			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
			
			Composite resultComp = new Composite(this, SWT.NONE);
			GridLayout layout = new GridLayout(); 
	        layout.verticalSpacing = 0;
	        layout.marginHeight = 0;
	        resultComp.setLayout(layout);
	        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));

			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotTableManager = new FixSizeListTableManager(lotTable, checkFlag);
			lotTableManager.setIndexFlag(true);
			//lotTableManager.setAutoSizeFlag(true);
			lotTableManager.newViewer(resultComp);
			lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					StructuredSelection selection = (StructuredSelection) event.getSelection();
					Lot lot = (Lot) selection.getFirstElement();

				}
			});

		} catch (Exception e) {
			logger.error("BatchLotComposite createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void getLotByLotId(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			if (lot != null) {
				List<Lot> selectLots = new ArrayList<Lot>();
				if (checkFlag) {
					selectLots = (List) lotTableManager.getCheckedObject();
				} else {
					if (lotTableManager.getSelectedObject() != null) {
						selectLots.add((Lot) lotTableManager.getSelectedObject());
					}
				}
				boolean isAdd = true;
				List<Lot> alllot = (List<Lot>) lotTableManager.getInput();
				for (Lot lt : alllot) {
					if (lt.getLotId().equals(lot.getLotId())) {
						isAdd = false;
					}
				}
				if (isAdd) {
					lotTableManager.insert(0, lot);
				}
				// 默认全选
				if (checkFlag) {
					lotTableManager.setCheckedObject(lot);
				}
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				txtLotId.selectAll();
				txtLotId.focusing();
			} else {
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.warning();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public ListTableManager getLotTableManager() {
		return lotTableManager;
	}

	public void setLotTableManager(ListTableManager lotTableManager) {
		this.lotTableManager = lotTableManager;
	}

	public int getTableHeigthHint() {
		return tableHeigthHint;
	}

	public void setTableHeigthHint(int tableHeigthHint) {
		this.tableHeigthHint = tableHeigthHint;
	}

	protected void queryAdapter() {
		try {
			if (!queryForm.validate()) {
				return;
			}
			String whereClause = " 1 = 1 " + queryForm.createWhereClause();
			whereClause = StringUtil.relpaceWildcardCondition(whereClause);

			StringBuffer sb = null;
			if (lotStates != null && !lotStates.isEmpty()) {
				for (String lotState : lotStates) {
					if (sb == null) {
						sb = new StringBuffer("'");
					}
					sb.append(lotState);
					sb.append("',");
				}
				sb.substring(0, sb.length() - 1);
				whereClause = " AND state IN (" + sb.toString() + ") ";
			}

			ADManager adManager = Framework.getService(ADManager.class);
			List<Lot> lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Integer.MAX_VALUE, whereClause, "");
			lotTableManager.setInput(lots);
			// 默认全选
			if (checkFlag) {
				for (Lot lot : lots) {
					lotTableManager.setCheckedObject(lot);
				}
			}
			lotTableManager.refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void removeAdapter() {
		try {
			List<Lot> selectLots = new ArrayList<Lot>();
			if (checkFlag) {
				selectLots = (List) lotTableManager.getCheckedObject();
			} else {
				if (lotTableManager.getSelectedObject() != null) {
					selectLots.add((Lot) lotTableManager.getSelectedObject());
				}
			}
			List<Lot> allLot = (List<Lot>) lotTableManager.getInput();
			allLot.remove(selectLots);
			lotTableManager.setInput(allLot);
			lotTableManager.refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void refresh() {
		try {
			List<Lot> lots = (List<Lot>) lotTableManager.getInput();
			String where = "(";
			if(lots!=null && lots.size()>0){
				for (Lot lot : lots) {
					where += "'" + lot.getLotId() + "',";
				}
				where = " lotId in " + where.substring(0, where.length() - 1) + ")";
				ADManager adManager = Framework.getService(ADManager.class);
				List<Lot> newlots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Integer.MAX_VALUE, where, "");
				lotTableManager.setInput(newlots);
			}else{
				lotTableManager.setInput(null);
			}
			lotTableManager.refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
