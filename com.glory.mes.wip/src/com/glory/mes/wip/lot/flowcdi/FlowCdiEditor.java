package com.glory.mes.wip.lot.flowcdi;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.cdi.IFlowCdiAction;
import com.glory.mes.wip.cdi.LotFlowCdiPoint;
import com.glory.mes.wip.cdi.client.FlowCdiActionManager;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class FlowCdiEditor extends GlcEditor {

	private static final Logger logger = Logger.getLogger(FlowCdiEditor.class);

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.flowcdi.FlowCdiEditor";
	
	public static final String CONTROL_CDI_POINT_GLC_INFO = "cdiPointGlcInfo";
	public static final String CONTROL_CDI_POINT_INFO = "cdiPointInfo";
	public static final String CONTROL_CDI_TRIGGER_POINT_INFO = "cdiTriggerPointInfo";
	public static final String CONTROL_CDI_ACTION_INFO = "cdiActionInfo";
	
	public static final List<String> filterateCdiPointName = List.of("StepQueue", "StepRun", "StepEnd", "ProcedureEnd", "TrackIn", "TrackOut", "Abort", "Edc");
	
	public GlcFormField glcFormField;
	public ListTableManagerField cdiPointInfoField;
	public ListTableManagerField cdiTriggerPointInfoField;
	public ListTableManagerField cdiActionInfoField; 
	
	private String cdiPointName;
	
	private String cdiTriggerPoint;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		//获取左侧动态表控件
		glcFormField = form.getFieldByControlId(CONTROL_CDI_POINT_GLC_INFO, GlcFormField.class);
		
		cdiPointInfoField = glcFormField.getFieldByControlId(CONTROL_CDI_POINT_INFO, ListTableManagerField.class);
		cdiTriggerPointInfoField = glcFormField.getFieldByControlId(CONTROL_CDI_TRIGGER_POINT_INFO, ListTableManagerField.class);
		
		cdiActionInfoField = form.getFieldByControlId(CONTROL_CDI_ACTION_INFO, ListTableManagerField.class);
	
		//查询表格选择事件
		subscribeAndExecute(eventBroker, cdiPointInfoField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);
		
		subscribeAndExecute(eventBroker, cdiTriggerPointInfoField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::triggerSelectAdapter);
		
		//保存事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);			
		
		//刷新事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ENTITYREFRESH), this::refreshAdapter);
		
		refreshAdapter(null);
	}
	
	protected void selectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotFlowCdiPoint == null) {
				return;
			}
			
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getCdiPointName())) {
				cdiPointName = lotFlowCdiPoint.getCdiPointName();
				if (!StringUtil.isEmpty(cdiTriggerPoint)) {
					FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
					List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), cdiPointName, cdiTriggerPoint, null, null);
					cdiActionInfoField.setValue(lotFlowCdiPoints);
					cdiActionInfoField.refresh();
				} else {
					cdiActionInfoField.setValue(new ArrayList<LotFlowCdiPoint>());
					cdiActionInfoField.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void triggerSelectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotFlowCdiPoint == null) {
				return;
			}
			
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getTriggerPoint())) {
				cdiTriggerPoint = lotFlowCdiPoint.getTriggerPoint();	
				if (!StringUtil.isEmpty(cdiPointName)) {			
					FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
					List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), cdiPointName, cdiTriggerPoint, null, null);
					cdiActionInfoField.setValue(lotFlowCdiPoints);
					cdiActionInfoField.refresh();
					
				} else {
					cdiActionInfoField.setValue(new ArrayList<LotFlowCdiPoint>());
					cdiActionInfoField.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void saveAdapter(Object object) {
		try {	
			if (cdiActionInfoField.getValue() != null) {
				List<LotFlowCdiPoint> lotFlowCdiPoints = (List<LotFlowCdiPoint> ) cdiActionInfoField.getValue();
				if (lotFlowCdiPoints.isEmpty()) {
					return;
				}
				boolean confirmSave = UI.showConfirm(Message.getString("wip.confirm_flowcdi_save"));
				if (confirmSave) {
					FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
					lotFlowCdiPoints = flowCdiActionManager.saveCommonFlowCdiPoint(cdiPointName, cdiTriggerPoint, lotFlowCdiPoints, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					cdiActionInfoField.setValue(lotFlowCdiPoints);
					cdiActionInfoField.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void refreshAdapter(Object object) {
		try {
			//刷新流程注入点列表
			FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
			List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiPoints();
			lotFlowCdiPoints = lotFlowCdiPoints.stream().filter(point -> !filterateCdiPointName.contains(point.getCdiPointName())).collect(Collectors.toList());
			cdiPointInfoField.setValue(lotFlowCdiPoints);
			cdiPointInfoField.refresh();
			
			List<LotFlowCdiPoint> lotFlowCdiTriggerPoints = Lists.newArrayList();
			LotFlowCdiPoint triggerPoint1 = new LotFlowCdiPoint();
			triggerPoint1.setTriggerPoint(IFlowCdiAction.TRIGGER_POINT_PRECHECK);
			lotFlowCdiTriggerPoints.add(triggerPoint1);
			LotFlowCdiPoint triggerPoint2 = new LotFlowCdiPoint();
			triggerPoint2.setTriggerPoint(IFlowCdiAction.TRIGGER_POINT_PREEXECUTE);
			lotFlowCdiTriggerPoints.add(triggerPoint2);
			LotFlowCdiPoint triggerPoint3 = new LotFlowCdiPoint();
			triggerPoint3.setTriggerPoint(IFlowCdiAction.TRIGGER_POINT_POSTEXECUTE);
			lotFlowCdiTriggerPoints.add(triggerPoint3);
			cdiTriggerPointInfoField.setValue(lotFlowCdiTriggerPoints);
			cdiTriggerPointInfoField.refresh();
			
			//清空注入动作列表
			cdiActionInfoField.setValue(null);
			cdiActionInfoField.refresh();
			cdiPointName = "";
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
