package com.glory.mes.wip.lot.flowcdi;

import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.lang.ObjectUtils;
import org.apache.log4j.Logger;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.cdi.LotFlowCdiPoint;
import com.glory.mes.wip.cdi.client.FlowCdiActionManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class StepFlowCdiEditor extends GlcEditor {

	private static final Logger logger = Logger.getLogger(StepFlowCdiEditor.class);

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.flowcdi.StepFlowCdiEditor";
	
	public static final String CONTROL_STEP_FLOW_CDI_QUERY_INFO = "stepFlowCdiQueryInfo";
	public static final String CONTROL_STEP_FLOW_CDI_EDIT_INFO = "stepFlowCdiEditInfo";
	public static final String CONTROL_CDI_DOWN_LEFT_QUERY_INFO = "leftQueryInfo";
	public static final String CONTROL_CDI_DOWN_RIGHT_EDIT_INFO = "rightEditInfo";
	public static final String CONTROL_CDI_POINT_NAME = "cdiPointName";
	public static final String CONTROL_STEP_CATEGORY = "stepCategory";
	
	//上面部分
	public QueryFormField stepFlowCdiQueryInfoField;
	public RefTableField flowCdiPointsQueryInfo;
	
	//下面部分
	public GlcFormField stepFlowCdiEditInfoField;
	public EntityFormField downLeftEntityFormField; 
	public ListTableManagerField downRightListTableManagerField;
	public RefTableField downLeftFlowCdiPointsQueryInfo;
	public RefTableField downLeftStepCategoryQueryInfo; 
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		//获取上面的查询控件
		stepFlowCdiQueryInfoField = form.getFieldByControlId(CONTROL_STEP_FLOW_CDI_QUERY_INFO, QueryFormField.class);
		
		//获取注入点的查询控件
		flowCdiPointsQueryInfo = stepFlowCdiQueryInfoField.getQueryForm().getFieldByControlId(CONTROL_CDI_POINT_NAME, RefTableField.class);
		
		//获取下面的编辑控件
		stepFlowCdiEditInfoField = form.getFieldByControlId(CONTROL_STEP_FLOW_CDI_EDIT_INFO, GlcFormField.class);
	
		//获取左下查询控件
		downLeftEntityFormField = stepFlowCdiEditInfoField.getFieldByControlId(CONTROL_CDI_DOWN_LEFT_QUERY_INFO, EntityFormField.class);
		
		//获取注入点的查询控件
		downLeftFlowCdiPointsQueryInfo = downLeftEntityFormField.getFieldByControlId(CONTROL_CDI_POINT_NAME, RefTableField.class);
		
		//获取工步类别的查询控件
		downLeftStepCategoryQueryInfo = downLeftEntityFormField.getFieldByControlId(CONTROL_STEP_CATEGORY, RefTableField.class);
		
		//获取右下编辑控件
		downRightListTableManagerField = stepFlowCdiEditInfoField.getFieldByControlId(CONTROL_CDI_DOWN_RIGHT_EDIT_INFO, ListTableManagerField.class);	
		
		//查询表格选择事件
		subscribeAndExecute(eventBroker, stepFlowCdiQueryInfoField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);
		
		//查询表格查询事件
		subscribeAndExecute(eventBroker, stepFlowCdiQueryInfoField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryFormAdapter);
		
		//左下查询事件
		subscribeAndExecute(eventBroker, stepFlowCdiEditInfoField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		
		//保存事件
		subscribeAndExecute(eventBroker, stepFlowCdiEditInfoField.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);			
		
		//刷新事件
		subscribeAndExecute(eventBroker, stepFlowCdiEditInfoField.getFullTopic(ADButtonDefault.BUTTON_NAME_ENTITYREFRESH), this::queryAdapter);
		
		init();
	}
	
	protected void selectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotFlowCdiPoint == null) {
				return;
			}
			
			//刷新下面的编辑列表
			downLeftEntityFormField.setValue(lotFlowCdiPoint);
			downLeftEntityFormField.refresh();
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getCdiPointName())) {
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
						lotFlowCdiPoint.getCdiPointName(), null, lotFlowCdiPoint.getStepCategory(), null);
				downRightListTableManagerField.setValue(lotFlowCdiPoints);
				downRightListTableManagerField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void queryFormAdapter(Object object) {
		LinkedHashMap<String, IField> fields = stepFlowCdiQueryInfoField.getQueryForm().getFields();
		boolean queryFlag = false;
        for(IField f : fields.values()) {
        	Object value = f.getValue();
        	if (CONTROL_STEP_CATEGORY.equals(f.getId())) {
        		if (value != null && !StringUtil.isEmpty(value.toString())) {
        			queryFlag = true;
	        		break;
	        	}
        	}
        }
//		if (!queryFlag) {
//			UI.showError(Message.getString("wip.step_name_and_step_category_is_all_empty"));
//			stepFlowCdiQueryInfoField.getQueryForm().getTableManager().getInput().clear();
//		}
	}
	
	protected void queryAdapter(Object object) {
		try {
			downLeftEntityFormField.getFormControl().getMessageManager().setAutoUpdate(true); //设置为true时才会刷新界面出现icon
			downLeftEntityFormField.getFormControl().getMessageManager().removeAllMessages();

			LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint)downLeftEntityFormField.getValue();
			if (lotFlowCdiPoint == null || StringUtil.isEmpty(lotFlowCdiPoint.getCdiPointName())) {
				return;
			}
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getStepCategory())) {
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
						lotFlowCdiPoint.getCdiPointName(), null, lotFlowCdiPoint.getStepCategory(), null);
				downRightListTableManagerField.setValue(lotFlowCdiPoints);
				downRightListTableManagerField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			downLeftEntityFormField.getFormControl().getMessageManager().setAutoUpdate(false);
		}
	}
	
	protected void saveAdapter(Object object) {
		try {	
			if (downRightListTableManagerField.getValue() != null) {
				List<LotFlowCdiPoint> cdiPoints = (List<LotFlowCdiPoint> ) downRightListTableManagerField.getValue();
				LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint)downLeftEntityFormField.getValue();
				if (cdiPoints.isEmpty() || lotFlowCdiPoint == null) {
					return;
				}
				if (!ObjectUtils.equals(lotFlowCdiPoint.getCdiPointName(), cdiPoints.get(0).getCdiPointName())) {
					UI.showError(String.format(Message.getString("wip.flowcdi_is_different"), ((ADField)downLeftFlowCdiPointsQueryInfo.getADField()).getDescription()));
					return;
				}
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				flowCdiActionManager.saveStepFlowCdiPoint(lotFlowCdiPoint.getCdiPointName(), lotFlowCdiPoint.getStepCategory(), null, cdiPoints, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				queryAdapter(object);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void init() {
		try {
			//刷新流程注入点列表
			FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
			List<LotFlowCdiPoint> cdiPoints = flowCdiActionManager.getFlowCdiPoints(false);
			cdiPoints.add(new LotFlowCdiPoint());
			flowCdiPointsQueryInfo.setInput(cdiPoints);
			flowCdiPointsQueryInfo.refresh();
			
			//刷新左下部分内容
			downLeftFlowCdiPointsQueryInfo.setInput(cdiPoints);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
