package com.glory.mes.wip.lot.flowcdi;


import java.util.ArrayList;
import java.util.List;

import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.cdi.LotFlowCdiPoint;
import com.glory.mes.wip.cdi.client.FlowCdiActionManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class AutoInterfaceCdiManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.flowcdi.AutoInterfaceCdiManagerEditor";

	private static final String FIELD_CDIPOINTINFO = "cdiPointInfo";
	private static final String FIELD_CDIACTIONINFO = "cdiActionInfo";

	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_SAVE = "save";

	protected ListTableManagerField cdiPointInfoField;
	protected ListTableManagerField cdiActionInfoField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		cdiPointInfoField = form.getFieldByControlId(FIELD_CDIPOINTINFO, ListTableManagerField.class);
		cdiActionInfoField = form.getFieldByControlId(FIELD_CDIACTIONINFO, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, cdiPointInfoField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::cdiPointInfoSelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		
		refreshAdapter(null);
	}

	private void refreshAdapter(Object object) {
		try {
			//刷新流程注入点列表
			FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
			List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getAutoCdiPoints();
			cdiPointInfoField.setValue(lotFlowCdiPoints);
			cdiPointInfoField.refresh();
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings("unchecked")
	private void saveAdapter(Object object) {
		try {	
			if (cdiActionInfoField.getValue() != null) {
				List<LotFlowCdiPoint> lotFlowCdiPoints = (List<LotFlowCdiPoint> ) cdiActionInfoField.getValue();
				if (lotFlowCdiPoints.isEmpty()) {
					return;
				}
				
				boolean confirmSave = UI.showConfirm(Message.getString("wip.confirm_flowcdi_save"));
				if (confirmSave) {
					FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
					String cdiPointName = lotFlowCdiPoints.get(0).getCdiPointName();
					lotFlowCdiPoints = flowCdiActionManager.saveCommonFlowCdiPoint(cdiPointName, null, lotFlowCdiPoints, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					cdiActionInfoField.setValue(lotFlowCdiPoints);
					cdiActionInfoField.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void cdiPointInfoSelectionChanged(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotFlowCdiPoint == null) {
				return;
			}
			
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getCdiPointName())) {
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getAutoCdiAcutalActions(Env.getOrgRrn(), lotFlowCdiPoint.getCdiPointName());
				cdiActionInfoField.setValue(lotFlowCdiPoints);
				cdiActionInfoField.refresh();
			} else {
				cdiActionInfoField.setValue(new ArrayList<LotFlowCdiPoint>());
				cdiActionInfoField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

}