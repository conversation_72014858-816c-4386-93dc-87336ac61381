package com.glory.mes.wip.lot.unscrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;

@Deprecated
public class UnScrapListTableComponentLotDialog extends BaseTitleDialog {
	private static final Logger logger = Logger.getLogger(UnScrapListTableComponentLotDialog.class);

	protected IManagedForm form;
	protected XCombo combo;
	protected Button remove, qtyAdd;
	protected Lot lot;
	protected List<ProcessUnit> unScrapLots = new ArrayList<ProcessUnit>();
	protected List<LotUnScrapAction> unScrapActions = new ArrayList<LotUnScrapAction>();
	protected String bonusCode;
	protected BigDecimal inputMainQty, inputSubQty, addMainQty, addSubQty, splitedMainQty, splitedSubQty;
	protected Text commentText;
	protected Object commentQty;
	protected String comment, unScrapCode;
	protected static String UNSCRAP_CODE = "UnScrapCode";
	protected String actionCode;
	
	protected CheckBoxTableViewerManager listTableManager;
	private static final String AD_TABLE = "WIPUnScrapComponentView";
	protected static String HEADER_SCRAP_CODE = Message.getString("mm.unscrapcode");

	public UnScrapListTableComponentLotDialog(Shell parent) {
		super(parent);
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(300, 100, 800, 500);
		getShell().setMinimumSize(800, 500);
	}

	public UnScrapListTableComponentLotDialog(Shell parent, IManagedForm form, Lot lot) {
		this(parent);
		this.form = form;
		try {
			LotManager manager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			lot = manager.getLotWithComponent(lot.getObjectRrn());

			List<LotScrap> lotScraps = manager.getLotScrap(lot, Env.getSessionContext());
			for (LotScrap lotScrap : lotScraps) {
				ComponentUnit componentUnit = new ComponentUnit();
				componentUnit.setObjectRrn(lotScrap.getComponentRrn());
				componentUnit = (ComponentUnit) adManager.getEntity(componentUnit);
				lot.getSubProcessUnit().add(componentUnit);
			}
			this.lot = lot;
		} catch (Exception e) {
			logger.error("Constructor in ScrapIdentifiedLotDialog", e);
		}
	}

	@Override
	protected Control buildView(Composite parent) {
		Color gray = new Color(Display.getCurrent(), 236, 233, 216);
		FormToolkit toolkit = form.getToolkit();
		setTitleImage(SWTResourceCache.getImage("trackin-dialog"));
		setTitle(Message.getString("wip.unscrap_lot"));
		setMessage(Message.getString("wip.unscrapLot_Info"));

		Composite content = toolkit.createComposite(parent);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setLayout(new GridLayout(1, false));
		content.setBackground(gray);

		Composite tableContainer = toolkit.createComposite(content, SWT.NULL);
		tableContainer.setLayout(new GridLayout());
		tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE);
			listTableManager = new CheckBoxTableViewerManager(adTable);
			listTableManager.addICheckChangedListener(new ICheckChangedListener() {
				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if (checked) {
						boolean flag = true;
						if(combo.getText() == null
	                          || "".equals(combo.getText().trim())){
							flag = false;
							UI.getActiveShell().getDisplay().asyncExec(new Runnable() {
			                    @Override
			                    public void run() {
			                    	UI.showWarning(String.format(Message.getString("wip.scrap_code_required"), HEADER_SCRAP_CODE));
			  						for (Object object : eventObjects) {
			  							ComponentUnit compositeUnit = (ComponentUnit) object;
			  							listTableManager.unCheckObject(object);
		  								compositeUnit.setAttribute1("");
			  						}
			                    }
			                });
						}
						for (Object object : eventObjects) {
							ComponentUnit compositeUnit = (ComponentUnit) object;
							if (flag) {
								compositeUnit.setAttribute1(combo.getText());
							}else {
								listTableManager.unCheckObject(object);
								compositeUnit.setAttribute1("");
							}
						}
					}else {
						for (Object object : eventObjects) {
							((ComponentUnit)object).setAttribute1("");
						}
					}
				}
			});
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		
		Composite unScrapComp = toolkit.createComposite(content, SWT.NONE);
		unScrapComp.setLayout(new GridLayout(2, false));
		unScrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		unScrapComp.setBackground(gray);

		Label lab = new Label(unScrapComp, SWT.NULL);
		lab.setText(Message.getString("wip.unscrapcode_lot"));
		combo = RCPUtil.getUserRefListCombo(unScrapComp, getBonusCodeSrc(), Env.getOrgRrn());

		combo.addFocusListener(new FocusListener() {

			@Override
			public void focusLost(FocusEvent e) {
				// TODO Auto-generated method stub
				unScrapCode = combo.getText();
				comment = commentText.getText();
			}

			@Override
			public void focusGained(FocusEvent e) {
				// TODO Auto-generated method stub
				focusLost(e);
			}
		});
		
		GridData cGd = new GridData(GridData.FILL_BOTH);
		cGd.horizontalSpan = 4;
		combo.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
		Label lab2 = new Label(unScrapComp, SWT.NULL);
		lab2.setText(Message.getString("wip.comment"));
		commentText = toolkit.createText(unScrapComp, "", SWT.BORDER);
		commentText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
		
		listTableManager.newViewer(tableContainer);
		listTableManager.setInput(getScrapWafer());
		listTableManager.refresh();

		return parent;
	}

	private ArrayList<ProcessUnit> getScrapWafer() {
		ArrayList<ProcessUnit> list = new ArrayList<ProcessUnit>();
		for (ProcessUnit childUnit : lot.getSubProcessUnit()) {
			if (childUnit instanceof ComponentUnit) {
				ComponentUnit unit = (ComponentUnit) childUnit;
				if (ComponentUnit.STATE_SCRAP.equalsIgnoreCase(unit.getState())) {
					list.add(unit);
				}
			}
		}
		return list;
	}

	private String getBonusCodeSrc() {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			if (lot != null && lot.getObjectRrn() != null && lot.getStepRrn() != null) {
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) prdManager.getSimpleProcessDefinition(step);
				bonusCode = step.getBonusCodeSrc();
			} else {
				bonusCode = UNSCRAP_CODE;
			}
			if (bonusCode == null || bonusCode.trim().length() == 0) {
				bonusCode = UNSCRAP_CODE;
			}
		} catch (Exception e) {
			logger.error("UnScrapDialog : initComoContent() ", e);
		}
		return bonusCode;
	}

	@Override
	protected void okPressed() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			lot.setOperator1(Env.getUserName());
			LotAction action = new LotAction();
			action.setActionComment(commentText.getText());
			action.setActionCode(getActionCode());
			//
			List<ComponentUnit> selectComponentUnits = new ArrayList<ComponentUnit>();
			
			List<Object> checkedLots = listTableManager.getCheckedObject();
			for(int i = 0; i < checkedLots.size(); i++ ) {
				selectComponentUnits.add((ComponentUnit)checkedLots.get(i));
			}
			//找出lot中与selectComponentUnits相同的componentUnit，并进行位置更新
			for(ComponentUnit componentUnit : selectComponentUnits) {
				for(ProcessUnit processUnit : lot.getSubProcessUnit()) {
					if(componentUnit.getState() == "SCRAP") {
						ComponentUnit tempComp = (ComponentUnit) processUnit;
						if(componentUnit.getComponentId().equals(tempComp.getComponentId())) {
							tempComp.setPosition(componentUnit.getPosition());
							tempComp.setAttribute1(tempComp.getAttribute1());
						}
					}
				}
			}
			
			for (ProcessUnit unit : unScrapLots) {
				ComponentUnit q = (ComponentUnit) unit;
				LotUnScrapAction unAction = new LotUnScrapAction();
				unAction.setActionCode(String.valueOf(q.getAttribute1()));
				//unAction.setUnMainQty(q.getMainQty());
				//unAction.setUnSubQty(q.getSubQty());
				List<LotScrap> lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class, 1,
						"componentRrn = " + unit.getObjectRrn(), null);
				lotScraps.get(0).setUnScrapMainQty(lotScraps.get(0).getMainQty());
				lotScraps.get(0).setUnScrapSubQty(lotScraps.get(0).getSubQty());
				unAction.setLotScrap(lotScraps.get(0));
				List<ProcessUnit> actionUnits = new ArrayList<ProcessUnit>();
				actionUnits.add(q);
				unAction.setActionUnits(actionUnits);
				unScrapActions.add(unAction);
			}
			try {
				lotManager.unScrapLot(lot, unScrapActions, action, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.unscrapLot_success"));
			}catch(Exception e) {
				unScrapActions = new ArrayList<LotUnScrapAction>();
				ExceptionHandlerManager.asyncHandleException(e);
	//			UI.showError(Message.getString("mm.slot_duplicate_or_position_error"));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		super.okPressed();
	}

	protected String getActionCode() {
		if (unScrapLots != null) {
			for (ProcessUnit punit : unScrapLots) {
				ComponentUnit q = (ComponentUnit) punit;
				actionCode = q.getActionCode();
			}
		}
		return actionCode;
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == IDialogConstants.OK_ID) {
			unScrapLots = getScrapLots();
			if (unScrapCode == null || unScrapCode.trim() == "" || unScrapLots == null || unScrapLots.size() == 0) {
				UI.showError(Message.getString("wip.unscrap_not_select"));
				return;
			}
		}
		super.buttonPressed(buttonId);
	}


	public void removeSelected(Object[] os) {
		if (os.length != 0) {
			for (Object o : os) {
				ProcessUnit pe = (ProcessUnit) o;
				unScrapLots.remove(pe);
			}
			refresh();
		}
	}

	public void refresh() {
		if (getScrapLots() != null) {
			listTableManager.setInput(getScrapLots());
			listTableManager.refresh();
		}
	}

	public List<ProcessUnit> getScrapLots() {
	    List<ProcessUnit> unScrapLots = new ArrayList<ProcessUnit>();
	        List<Object> objs = listTableManager.getCheckedObject();
	        for (Object obj : objs) {
	            ProcessUnit unit = (ProcessUnit) obj;
	            unit.setEquipmentId(lot.getEquipmentId());
	            unScrapLots.add(unit);
	        }
		return unScrapLots;
	}

	public void decorateButton(Button button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData();
		data.horizontalAlignment = GridData.END;
		data.widthHint = 93;
		int widthHint = 92;
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}

	public class ScrapLabelProvider extends LabelProvider implements ITableLabelProvider {
		@Override
		public Image getColumnImage(Object element, int columnIndex) {
			return null;
		}

		@Override
		public String getColumnText(Object element, int columnIndex) {
			if (element instanceof ComponentUnit) {
				ComponentUnit component = (ComponentUnit) element;
				switch (columnIndex) {
				case 0:
					return component.getComponentId();
				case 1:
					if (component.getPosition() == null) {
						return "";
					}
					return component.getPosition().toString();
				case 2:
					if (component.getState() == null) {
						return "";
					}
					return component.getState();
				case 3:
					return component.getActionCode() != null ? component.getActionCode() : "";
				}
			}
			return "";
		}
	}
	
}
