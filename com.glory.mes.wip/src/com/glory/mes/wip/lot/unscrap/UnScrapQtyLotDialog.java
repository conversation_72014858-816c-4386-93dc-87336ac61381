package com.glory.mes.wip.lot.unscrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.jface.viewers.ColumnWeightData;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.TableLayout;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.glory.framework.core.exception.ExceptionBundle;

public class UnScrapQtyLotDialog extends BaseTitleDialog {
	private static final Logger logger = Logger.getLogger(UnScrapQtyLotDialog.class);
	protected IManagedForm form;
	protected CheckboxTableViewer viewer;
	protected Table table;
	protected XCombo combo;
	protected Button remove, qtyAdd;
	protected Text mainText, subText;
	protected Lot lot;
	protected List<ProcessUnit> unScrapLots = new ArrayList<ProcessUnit>();
	protected String bonusCode;
	protected BigDecimal inputMainQty, inputSubQty, addMainQty, addSubQty, splitedMainQty, splitedSubQty;
	protected static String UNSCRAP_CODE = "UnScrapCode";
	protected String actionCode;
	
	public UnScrapQtyLotDialog(Shell parent) {
        super(parent);
    }
	
	public UnScrapQtyLotDialog(Shell parent, IManagedForm form, Lot lot){
		this(parent);
		this.form = form;
		this.lot = lot;
	}
	
	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(300, 100, 800, 500);
		getShell().setMinimumSize(800, 600);
	}
	
	@Override
    protected Control buildView(Composite parent) {
		FormToolkit toolkit = form.getToolkit();
        setTitleImage(SWTResourceCache.getImage("trackin-dialog"));
        setTitle(Message.getString("wip.unscrap_lot"));
        setMessage(Message.getString("wip.unscrapLot_Info"));
        
        Composite content = toolkit.createComposite(parent);
        content.setLayoutData(new GridData(GridData.FILL_BOTH));
        content.setLayout(new GridLayout(1, false));
        
        Composite tableContainer = toolkit.createComposite(content, SWT.NULL);
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
        createTableViewer(tableContainer, toolkit);
        viewer.setLabelProvider(new ScrapLabelProvider());
        viewer.setContentProvider(new ScrapContentProvider());        
        
        Composite buttonComp = toolkit.createComposite(content, SWT.NULL);        
        GridData gd = new GridData(GridData.FILL_BOTH);
        gd.horizontalAlignment = GridData.END;
        buttonComp.setLayoutData(gd);
        GridLayout gridLayout = new GridLayout(1, true);
        buttonComp.setLayout(gridLayout);
        
        remove = toolkit.createButton(buttonComp, Message.getString(ExceptionBundle.bundle.CommonDelete()), SWT.PUSH);
		decorateButton(remove);
		remove.addSelectionListener(getDeleteListener());
		
		Composite unScrapComp = toolkit.createComposite(content, SWT.NULL);
		unScrapComp.setLayoutData(new GridData(GridData.FILL_BOTH));
		GridData tGd = new GridData(GridData.FILL_HORIZONTAL);
		GridLayout gl = new GridLayout(5, false);
		unScrapComp.setLayout(gl);
		
		toolkit.createLabel(unScrapComp, Message.getString("wip.unscrapcode_lot"), SWT.NULL);
		combo = RCPUtil.getUserRefListCombo(unScrapComp, getBonusCodeSrc(), Env.getOrgRrn());
		GridData cGd = new GridData(GridData.FILL_HORIZONTAL);
		cGd.horizontalSpan = 4;
		combo.setLayoutData(cGd);
		
		toolkit.createLabel(unScrapComp, Message.getString("wip.main_qty"), SWT.NULL);
		mainText = toolkit.createText(unScrapComp, "", SWT.BORDER);
		mainText.setLayoutData(tGd);
		toolkit.createLabel(unScrapComp, Message.getString("wip.sub_qty"), SWT.NULL);
		subText = toolkit.createText(unScrapComp, "", SWT.BORDER);
		subText.setLayoutData(tGd);		

		qtyAdd = toolkit.createButton(unScrapComp, Message.getString(ExceptionBundle.bundle.CommonAdd()), SWT.PUSH);		
		decorateButton(qtyAdd);		
		qtyAdd.addSelectionListener(getQtyAddListener());
		mainText.addModifyListener(getModifyListener("double"));
		subText.addModifyListener(getModifyListener("double"));
        
        return parent;
	}
	
	private String getBonusCodeSrc() {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			if (lot != null && lot.getObjectRrn() != null && lot.getStepRrn() != null) {
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) prdManager.getSimpleProcessDefinition(step);
				bonusCode = step.getBonusCodeSrc();
			} else {
				bonusCode = UNSCRAP_CODE;
			}
			if (bonusCode == null || bonusCode.trim().length() == 0) {
				bonusCode = UNSCRAP_CODE;
			}
		} catch (Exception e) {
			logger.error("UnScrapDialog : initComoContent() ", e);
		}
		return bonusCode;
	}

	private SelectionListener getQtyAddListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if(validate()) {
					if(addIsNotNull()) {
						bonusCode = combo.getText();
						QtyUnit usl = new QtyUnit();
						usl.setActionCode(bonusCode);
						usl.setMainQty(inputMainQty);
						usl.setSubQty(inputSubQty);
						
						if(unScrapLots != null) {
							unScrapLots.add(usl);
							refresh();
						}
						mainText.setText("");
						subText.setText("");
						combo.setText("");
						combo.setFocus();
					}
				}
				inputMainQty = inputSubQty = BigDecimal.ZERO;
			}
		};
	}

	public boolean validate(){
		IMessageManager mmng = form.getMessageManager();
		mmng.removeAllMessages();
		boolean validateFlag = true;
		boolean unScrapCodeIsNull = GenericValidator.isBlankOrNull(combo.getText());
		boolean mainIsNull = GenericValidator.isBlankOrNull(mainText.getText());
		boolean subIsNull = GenericValidator.isBlankOrNull(subText.getText());
		if(unScrapCodeIsNull){
			mmng.addMessage(UNSCRAP_CODE, 
					String.format(Message.getString("common.ismandatry"), UNSCRAP_CODE), null, IMessageProvider.ERROR, combo);
			return false;
		}
		if(mainIsNull && subIsNull){
			mmng.addMessage(Message.getString("wip.lot_mainQty"), String.format(Message.getString("common.mainQtyAndSubQty"),
					Message.getString("wip.lot_mainQty"), Message.getString("wip.lot_subQty")), null, IMessageProvider.ERROR, mainText);
			mmng.addMessage(Message.getString("wip.lot_subQty"), String.format(Message.getString("common.mainQtyAndSubQty"),
					Message.getString("wip.lot_subQty"), Message.getString("wip.lot_mainQty")), null, IMessageProvider.ERROR, subText);
			validateFlag = false;
		}
		return validateFlag;
	}
	
	public boolean addIsNotNull() {
		inputMainQty = BigDecimal.ZERO;
		inputSubQty = BigDecimal.ZERO;
		
		if ("0".equals(mainText.getText().trim()) && "".equals(subText.getText().trim())) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}
		
		if ("".equals(mainText.getText().trim()) && "0".equals(subText.getText().trim())) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}
		
		if(!"".equals(mainText.getText().trim())) {
			inputMainQty = new BigDecimal(mainText.getText());
			if (inputMainQty.compareTo(BigDecimal.ZERO) < 0) {
				UI.showError(Message.getString("wip.scrap_check_qty"));
				return false;
			}
		}
		if(!"".equals(subText.getText().trim())) {
			inputSubQty = new BigDecimal(subText.getText());
			if (inputSubQty.compareTo(BigDecimal.ZERO) < 0) {
				UI.showError(Message.getString("wip.scrap_check_qty"));
				return false;
			}
		}
		
		if(inputMainQty.compareTo(BigDecimal.ZERO) == 0 && inputSubQty.compareTo(BigDecimal.ZERO) == 0) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}
		
		return true;
	}
	
	@Override
    protected void okPressed() {
//		try {
//			LotManager lotManager = Framework.getService(LotManager.class);
//			lot.setOperator1(Env.getUserName());
//			LotAction lotAction = new LotAction();
//			lotAction.setActionCode(getActionCode());
//			lotManager.unScrapLot(lot, unScrapLots, lotAction, Env.getSessionContext());
//			UI.showInfo(Message.getString("wip.unscrapLot_success"));
//		} catch(Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//			return;
//		}
//        super.okPressed();
    }
	
	/*
	 * 获得报废的动作码
	 */
	protected String getActionCode(){
		if(unScrapLots!=null){
			for(ProcessUnit  punit : unScrapLots){
				QtyUnit q=(QtyUnit)punit;
				actionCode=q.getActionCode();
			}
		}
		return actionCode;
	}
	
	@Override
	protected void buttonPressed(int buttonId) {
		if(buttonId == IDialogConstants.OK_ID) {
			if (unScrapLots == null || unScrapLots.size() == 0) {
				UI.showError(Message.getString("wip.scrapLot_donot"));
				combo.setFocus();
				return;
			}
		}
		super.buttonPressed(buttonId);
	}
	
	public ModifyListener getModifyListener(final String dataType) {
		return new ModifyListener() {
			public void modifyText(ModifyEvent e) {
				Text text = (Text)e.widget;
				String value = text.getText().trim();
				if ("".equalsIgnoreCase(value.trim())) {
					return;
				}
				if (!discernQty(value)) {
					text.setText("");
					text.setFocus();
				}
			}
			public boolean discernQty(String value) {
				if (!ValidatorFactory.isValid(dataType, value)) {
					UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInputError()), Message.getString("common.inputerror_title"));
					return false;
				}
				return true;
			}
		};
	}
	
	protected void createTableViewer(Composite tc, FormToolkit toolkit) {
		String[] columnsHeaders = new String[] {UNSCRAP_CODE, Message.getString("wip.main_qty"), Message.getString("wip.sub_qty") };
		Table table = toolkit.createTable(tc, SWT.CHECK | SWT.FULL_SELECTION | SWT.BORDER);
		table.setHeaderVisible(true);
		table.setLinesVisible(true);
		TableLayout tlayout = new TableLayout();
		table.setLayout(tlayout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = table.getItemHeight() * 13;
		table.setLayoutData(gd);
		
		viewer = new CheckboxTableViewer(table);
		if (columnsHeaders != null) {
			for (int i = 0; i < columnsHeaders.length; i++) {
				TableColumn column;
				column = new TableColumn(table, SWT.NONE);
				column.setText(columnsHeaders[i]);
				column.setWidth(240);
				column.setResizable(true);
				tlayout.addColumnData(new ColumnWeightData(30));
			}
		}
	}
	
	private SelectionListener getDeleteListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				Object[] os = viewer.getCheckedElements();
				removeSelected(os);
			}
		};
	}
	
	public void removeSelected(Object[] os) {
		if(os.length != 0) {
			for(Object o : os) {
				QtyUnit pe = (QtyUnit)o;
				unScrapLots.remove(pe);			
			}
			refresh();
		}
	}
	
	public void refresh() {
		if(getScrapLots() != null) {
			viewer.setInput(getScrapLots());
			viewer.refresh();
		}
	}
	
	public List<ProcessUnit> getScrapLots() {
		return unScrapLots;
	}
	
	public void decorateButton(Button button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData();
		data.horizontalAlignment = GridData.END;
		data.widthHint = 93;
		int widthHint = 92; // IDialogConstants.BUTTON_WIDTH
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}

	public class ScrapLabelProvider extends LabelProvider implements ITableLabelProvider {
		@Override
		public Image getColumnImage(Object element, int columnIndex) {
			return null;
		}
		@Override
		public String getColumnText(Object element, int columnIndex) {
			if (element instanceof QtyUnit) {
				QtyUnit unScrap = (QtyUnit) element;
				switch (columnIndex) {
					case 0:
						return unScrap.getActionCode();
					case 1:
						if (unScrap.getMainQty() == null) {
							return "";
						}
						return unScrap.getMainQty().toString();
					case 2: {
						if (unScrap.getSubQty() == null) {
							return "";
						}
						return unScrap.getSubQty().toString();
					}
				}
			}
			return "";
		}
	}
	private class ScrapContentProvider implements IStructuredContentProvider {
		@Override
		public Object[] getElements(Object inputElement) {
			if (inputElement instanceof List) {
				return ((List) inputElement).toArray();
			}
			return new Object[0];
		}
		@Override
		public void dispose() {
		}
		@Override
		public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		}
	}
}
