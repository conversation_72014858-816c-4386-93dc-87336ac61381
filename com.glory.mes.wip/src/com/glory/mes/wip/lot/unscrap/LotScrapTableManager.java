package com.glory.mes.wip.lot.unscrap;


import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
public class LotScrapTableManager extends FixEditorTableManager {

	public LotScrapTableManager(ADTable adTable) {
		super(adTable);
	}
	
//	public boolean validate(VerifyEvent event) {
//    	char myChar = event.character;
//    	if (Character.isDigit(myChar) || myChar < 32 || myChar == 127) {
//			return true;
//		}
//    	ParameterDefinition param = (ParameterDefinition)this.getSelectedObject();
//    	String dataType = param.getType();
//    	Text text = (Text)event.widget;
//    	if (dataType != null && dataType.trim().length() > 0){
//    		if (DataType.INTEGER.equalsIgnoreCase(dataType)) {
//    			//非数字或者负号
//    			if (myChar == '-') {
//    				if (text.getText().indexOf('-') == -1 && text.getCaretPosition() == 0) {
//    					return true;
//    				}
//    			}
//			} else if (DataType.DOUBLE.equalsIgnoreCase(dataType)) {
//				//非数字或者负号或小数点
//				if (myChar == '.') {
//					if (text.getText().indexOf('.') == -1) {
//						return true;
//					}
//				}
//				if (myChar == '-') {
//					if (text.getText().indexOf('-') == -1 && text.getCaretPosition() == 0) {
//						return true;
//					}
//				}
//			} else {
//				return true;
//			}
//    	}
//    	return false;
//    }
}
