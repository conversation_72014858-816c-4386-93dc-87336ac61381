package com.glory.mes.wip.lot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.edc.model.EdcAQLSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class ChangeWoIdLotProcessor extends AbstractLotProcessor  {

	public ChangeWoIdLotProcessor(boolean isBatch) {
		super(isBatch);
	}

	private static final String TABLE_NAME = "WIPLotProcessorChangeWo";
	private static final String TABLE_NAME_LOT_LIST = "WIPLotProcessorChangeWoList";
	private static final String LOTWOID = "woId";
	
	private EntityForm entityForm;
	private IMessageManager mmng;
	
	/**
	 * 获得显示选中的批次信息动态表
	 */
	@Override
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOT_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	@Override
	public boolean process(List<Lot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				Lot lot = (Lot) entityForm.getObject();
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.changeLotWo(lots, lot.getWoId(), Env.getSessionContext());
				UI.showInfo(Message.getString("wip.changemo_successed"));
			} else {
//				UI.showError(Message.getString("wip.woid_field_not_empty"));
				return false;
			}
			
		} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	}
		return true;
	}

	@Override
	public boolean checkLotState(Lot lot) {
		if(LotStateMachine.STATE_SHIP.equals(lot.getState())) {
			return false;
		} else {
			return true;
		}	
	}

	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {	
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		ManagedForm mform = new ManagedForm(toolkit, form);
		mmng = mform.getMessageManager();
		
		Composite body = form.getBody();
		configureBody(body);
		
		entityForm = new EntityForm(body, SWT.NONE, new Lot(), getADTable(), mmng);
		entityForm.setLayout(new GridLayout(1, false));
		entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}
	
	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldWoId = new ADField();
		adFieldWoId.setName(LOTWOID);
		adFieldWoId.setIsMain(true);
		adFieldWoId.setIsDisplay(true);
		adFieldWoId.setIsEditable(true);
		adFieldWoId.setDisplayLength(15l);
		adFieldWoId.setLabel(Message.getString("wip.workorder_id"));
		adFieldWoId.setLabel_zh(Message.getString("wip.workorder_id"));
		adFieldWoId.setDataType("string");
		adFieldWoId.setDisplayType("reftable");
		adFieldWoId.setReftableRrn(getRefTableRrn());
		adFieldWoId.setIsMandatory(true);
		adFields.add(adFieldWoId);
		
		adTable.setFields(adFields);
		return adTable;
	}
	
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> adFields = adTable.getFields();
		
		ADField adFieldWoId = new ADField();
		adFieldWoId.setName(LOTWOID);
		adFieldWoId.setIsMain(true);
		adFieldWoId.setIsDisplay(true);
		adFieldWoId.setIsEditable(true);
		adFieldWoId.setDisplayLength(15l);
		adFieldWoId.setLabel(Message.getString("wip.workorder_id"));
		adFieldWoId.setLabel_zh(Message.getString("wip.workorder_id"));
		adFieldWoId.setDataType("string");
		adFieldWoId.setDisplayType("text");
		adFields.add(adFieldWoId);
		
		return adTable;
	}

	public Long getRefTableRrn() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADRefTable> refTables = adManager.getEntityList(Env.getOrgRrn(), ADRefTable.class, 
					1, "name = 'WIPWorkOrderName'", "");
			if(refTables != null && !refTables.isEmpty()) {
				return refTables.get(0).getObjectRrn();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
