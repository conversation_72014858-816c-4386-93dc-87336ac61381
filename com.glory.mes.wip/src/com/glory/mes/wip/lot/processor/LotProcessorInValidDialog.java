package com.glory.mes.wip.lot.processor;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;

/**
 * 显示未通过校验的批次列表
 */
public class LotProcessorInValidDialog extends BaseDialog {

	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;
	
	private ILotProcessor processor;
	private List<Lot> inValidLots;
	
	private ListTableManager tableManager;
	
	public LotProcessorInValidDialog(ILotProcessor processor, List<Lot> inValidLots) {
		super();
		this.inValidLots = inValidLots;
		this.processor = processor;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
	
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		
		Section section = toolkit.createSection(parent, Section.TITLE_BAR | Section.EXPANDED);
		section.setLayout(new GridLayout(1, true));
		section.setLayoutData(new GridData(GridData.FILL_BOTH));
		section.setText(Message.getString("wip.lot_processor_invalid_list_title"));
		
		Composite content = toolkit.createComposite(section, SWT.NONE);
      	content.setLayout(new GridLayout(1, true));
      	content.setLayoutData(new GridData(GridData.FILL_BOTH));
        
      	AbstractLotProcessor abstractLotProcessor = (AbstractLotProcessor)processor;
      	
      	try {
      		tableManager = new LotProcessorTableManager(abstractLotProcessor.getListADTable());
      		tableManager.newViewer(content);
      		tableManager.setInput(inValidLots);
      	} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	}
      
      	section.setClient(content);
		return parent;
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
