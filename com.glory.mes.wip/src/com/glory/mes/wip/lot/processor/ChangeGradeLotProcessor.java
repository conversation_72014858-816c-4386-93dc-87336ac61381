package com.glory.mes.wip.lot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class ChangeGradeLotProcessor extends AbstractLotProcessor {

	private static final String TABLE_NAME = "WIPLotProcessorChangeGrade";
	private static final String TABLE_NAME_LOT_LIST = "WIPLotProcessorChangeGradeList";

	
	private static final String LOTGRADE1 = "LotGrade1";
	private static final String LOTGRADE2 = "LotGrade2";
	
	private EntityForm entityForm;
	private IMessageManager mmng;
	
	public ChangeGradeLotProcessor(boolean isBatch) {
		super(isBatch);
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	@Override
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOT_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	@Override
	public boolean process(List<Lot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();
			
			if (entityForm.saveToObject()) {
				Lot lot = (Lot) entityForm.getObject();
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.changeLotGrade(lots, lot.getGrade1(), lot.getGrade2(), Env.getSessionContext());
				UI.showInfo(Message.getString("wip.grade_successed"));
			} else {
//				UI.showError(Message.getString("wip.riority_field_not_empty"));
				return false;
			}
			
		} catch (Exception e) {
      		ExceptionHandlerManager.asyncHandleException(e);
      	} finally {
      		mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkLotState(Lot lot) {
		if(LotStateMachine.STATE_SHIP.equals(lot.getState())) {
			return false;
		} else {
			return true;
		}	
	}

	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {	
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		ManagedForm mform = new ManagedForm(toolkit, form);
		mmng = mform.getMessageManager();
		
		Composite body = form.getBody();
		configureBody(body);
		
		entityForm = new EntityForm(body, SWT.NONE, new Lot(), getADTable(), mmng);
		entityForm.setLayout(new GridLayout(1, false));
		entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}
	
	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adFieldGrade1 = new ADField();
		adFieldGrade1.setName("grade1");
		adFieldGrade1.setIsMain(true);
		adFieldGrade1.setIsDisplay(true);
		adFieldGrade1.setIsEditable(true);
		adFieldGrade1.setDisplayLength(15l);
		adFieldGrade1.setLabel(Message.getString("wip.lot_grade") + "1");
		adFieldGrade1.setLabel_zh(Message.getString("wip.lot_grade") + "1");
		adFieldGrade1.setDataType("string");
		adFieldGrade1.setDisplayType("userreflist");
		adFieldGrade1.setReftableRrn(14479l);
		adFieldGrade1.setUreflistName(LOTGRADE1);
		adFieldGrade1.setIsMandatory(true);
		adFields.add(adFieldGrade1);
		
		ADField adFieldGrade2 = new ADField();
		adFieldGrade2.setName("grade2");
		adFieldGrade2.setIsMain(true);
		adFieldGrade2.setIsDisplay(true);
		adFieldGrade2.setIsEditable(true);
		adFieldGrade2.setDisplayLength(15l);
		adFieldGrade2.setLabel(Message.getString("wip.lot_grade") + "2");
		adFieldGrade2.setLabel_zh(Message.getString("wip.lot_grade") + "2");
		adFieldGrade2.setDataType("string");
		adFieldGrade2.setDisplayType("userreflist");
		adFieldGrade2.setReftableRrn(14479l);
		adFieldGrade2.setUreflistName(LOTGRADE2);
		adFieldGrade2.setIsMandatory(true);
		adFields.add(adFieldGrade2);

		adTable.setFields(adFields);
		
		return adTable;
	}
	
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> adFields = adTable.getFields();
		
		ADField adFieldGrade1 = new ADField();
		adFieldGrade1.setName("grade1");
		adFieldGrade1.setIsMain(true);
		adFieldGrade1.setIsDisplay(true);
		adFieldGrade1.setIsEditable(true);
		adFieldGrade1.setDisplayLength(15l);
		adFieldGrade1.setLabel(Message.getString("wip.lot_grade") + "1");
		adFieldGrade1.setLabel_zh(Message.getString("wip.lot_grade") + "1");
		adFieldGrade1.setDataType("string");
		adFieldGrade1.setDisplayType("text");
		adFields.add(adFieldGrade1);
		
		ADField adFieldGrade2 = new ADField();
		adFieldGrade2.setName("grade2");
		adFieldGrade2.setIsMain(true);
		adFieldGrade2.setIsDisplay(true);
		adFieldGrade2.setIsEditable(true);
		adFieldGrade2.setDisplayLength(15l);
		adFieldGrade2.setLabel(Message.getString("wip.lot_grade") + "2");
		adFieldGrade2.setLabel_zh(Message.getString("wip.lot_grade") + "2");
		adFieldGrade1.setDataType("string");
		adFieldGrade1.setDisplayType("text");
		adFields.add(adFieldGrade2);
		
		return adTable;
	}
}
