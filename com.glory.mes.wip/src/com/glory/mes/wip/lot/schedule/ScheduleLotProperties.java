package com.glory.mes.wip.lot.schedule;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.model.ParameterDefinition;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotParameterForm;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotParameter;
import com.glory.mes.wip.model.LotStateMachine;

public class ScheduleLotProperties extends EntityProperties {
	
	protected AuthorityToolItem itemSchedule;
	protected AuthorityToolItem itemUnSchedule;
	protected LotParameterForm paramForm;
	
	public static final String KEY_SCHEDULE = "schedule";
	public static final String KEY_UNSCHEDULE = "unSchedule";
	
	
	public ScheduleLotProperties() {
		super();
    }	
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		EntityForm itemForm = null;
		String tabName = tab.getName();
		if(tabName.equalsIgnoreCase("Parameter")) {
			paramForm = new LotParameterForm(composite, SWT.NONE, tab, mmng);
			return paramForm;
		} else {
			itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
		}
		return itemForm;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSchedule(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnSchedule(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
		
	protected void createToolItemSchedule(ToolBar tBar) {
		itemSchedule = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_SCHEDULE);
		itemSchedule.setText(Message.getString("common.schedule"));
		itemSchedule.setImage(SWTResourceCache.getImage("schedule"));
		itemSchedule.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event){
				scheduleAdapter();
			}
		});
	}
	
	protected void createToolItemUnSchedule(ToolBar tBar) {
		itemUnSchedule = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_UNSCHEDULE);
		itemUnSchedule.setText(Message.getString("common.unschedule"));
		itemUnSchedule.setImage(SWTResourceCache.getImage("unschedule"));
		itemUnSchedule.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event){
				unScheduleAdapter();
			}
		});
	}
	
	protected void scheduleAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;						
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()){
						saveFlag = false;
					}
				}	
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());								
					}
					LotManager lotManager = Framework.getService(LotManager.class);
					Lot lot = (Lot)getAdObject();
					lot.setOperator1(Env.getUserName());
					
					if (!StringUtil.isEmpty(lot.getLotId())) {
						SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
						String regEx = MesCfMod.getLotNamingRule(0, sysParameterManager);
						Pattern pattern = Pattern.compile(regEx);
						Matcher matcher = pattern.matcher(lot.getLotId());
						
						if (!matcher.matches()) {
							UI.showError("Lot ID: " + Message.getString("wip.changemo_checking"));
							return;
						}
					}
					
					ADBase abBase = lotManager.scheduleLot(lot, Env.getSessionContext());						
					ADManager entityManager = Framework.getService(ADManager.class);
					setAdObject(entityManager.getEntity(abBase));
					
					UI.showInfo(Message.getString("common.schedule_successed"));//弹出提示框
					getMasterParent().refresh();
					this.createAdObject();
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	protected void unScheduleAdapter() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString("common.confirm_unschedule"));
			if (confirmDelete) {
				if (getAdObject().getObjectRrn() != null) {
					LotManager lotManager = Framework.getService(LotManager.class);
					Lot lot = (Lot) this.getAdObject();
					lot.setOperator1(Env.getUserName());
					lotManager.deleteLot(lot, true, Env.getSessionContext());
					setAdObject(createAdObject());
					refresh();
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		getMasterParent().refresh();
	}
	
	@Override
	public ADBase createAdObject() throws Exception {
		Lot lot = new Lot();
		lot.setOrgRrn(Env.getOrgRrn());
		
		String whereClause = " isLotDefault = 'Y' ";
		ADManager adManager = Framework.getService(ADManager.class);
		List<ParameterDefinition> parameters = 
			adManager.getEntityList(Env.getOrgRrn(), ParameterDefinition.class, Env.getMaxResult(), whereClause, " name ASC ");
		if (parameters != null) {
			List<LotParameter> lotParamters = new ArrayList<LotParameter>();
			for (ParameterDefinition parameter : parameters) {
				LotParameter lotParamter = new LotParameter(parameter.getName(), null, null, parameter.getDefValue());
				lotParamter.setIsActive(true);
				lotParamter.setOrgRrn(parameter.getOrgRrn());
				lotParamter.setType(parameter.getType());
				lotParamters.add(lotParamter);
			}
			lot.setLotParameters(lotParamters);
		}	
		return lot;
	}

    @Override
    public void refresh() {
        Lot lot = (Lot)getAdObject();
        stateChange (lot.getState());
        super.refresh();
    }

    public void stateChange (String state) {
	    if (LotStateMachine.STATE_SCHD.equals(state) || null == state) {
	        itemNew.setEnabled(Boolean.TRUE);
            itemSchedule.setEnabled(Boolean.TRUE);
            itemUnSchedule.setEnabled(Boolean.TRUE);
            itemRefresh.setEnabled(Boolean.TRUE);
        } else {
            itemNew.setEnabled(Boolean.TRUE);
            itemSchedule.setEnabled(Boolean.FALSE);
            itemUnSchedule.setEnabled(Boolean.FALSE);
            itemRefresh.setEnabled(Boolean.TRUE);
	    }
	}
}