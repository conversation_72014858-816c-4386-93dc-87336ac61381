package com.glory.mes.wip.lot.condition;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.ShellHelper;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.condition.AbstractCondition;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class ConditionSimulateDialog extends BaseDialog {

	private static final String TABLE_NAME_LOT = "WIPFlowConditionLotQuery";
	private static final String TABLE_NAME_SIMULATE_LOT = "WIPFlowConditionSimulateResultLot";
	private static final String TABLE_NAME_SIMULATE_COMPONENT = "WIPFlowConditionSimulateResultComponent";
	
	private QueryTableForm formLotQuery;
	private LotConditionSimulate formSimulateResult;
	
	private Lot lotSimulate;
	private AbstractCondition condition;
	private List<Object> simulateResults;
	
	public ConditionSimulateDialog(Shell parent, AbstractCondition condition, boolean isView) {
		super(parent);
		this.condition = condition;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		try {
			configureBody(parent);
			
			ADManager adManager = Framework.getService(ADManager.class);
			
			// 创建查询form
			ADTable	adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOT);
			createQueryForm(parent, adTable, new FMessageManager());
			
			// 模拟结果Form
			ADTable	adTableSimulate;
			if (this.condition.getObjectType().equals(AbstractCondition.OBJECT_TYPE_LOT)) {
				adTableSimulate = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_SIMULATE_LOT);
			} else if (this.condition.getObjectType().equals(AbstractCondition.OBJECT_TYPE_COMPONENT)) {
				adTableSimulate = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_SIMULATE_COMPONENT);				
			} else {
				return null;
			}			
			formSimulateResult = new LotConditionSimulate(parent, SWT.NONE, simulateResults, condition, adTableSimulate, null);
	
			return parent;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton ok = createSquareButton(parent, IDialogConstants.OK_ID,
				Message.getString(ExceptionBundle.bundle.CommonSimulate()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
	}
	
	@Override
	protected void okPressed() {
		try {
			lotSimulate = (Lot)formLotQuery.getTableManager().getSelectedObject();
			if (lotSimulate == null) {
				return;
			}

			if (this.condition.getObjectType().equals(AbstractCondition.OBJECT_TYPE_LOT)) {
				//对批次的模拟

				boolean ret;
				LotManager lotManager = Framework.getService(LotManager.class);			
				try {
					ret = lotManager.evaluateLotCondition(lotSimulate, condition);
				} catch (Exception e) {
					UI.showError(e.toString());
					ret = false;
				}
				
				//组织模拟结果列表
				simulateResults = new ArrayList<Object>();
				Lot lot = new Lot();
				lot.setLotId(lotSimulate.getLotId());
				lot.setAttribute1(ret); //模拟结果
				simulateResults.add(lot);
				
				//刷新form
				formSimulateResult.setObject(simulateResults);
				formSimulateResult.refresh();
				
			} else if (this.condition.getObjectType().equals(AbstractCondition.OBJECT_TYPE_COMPONENT)) {
				//对组件的模拟
				
				//获取Component list
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				List<ComponentUnit> components = componentManager.getComponentsByParentUnitRrn(lotSimulate.getObjectRrn());
				if (components == null || components.size() == 0) {
					return;
				}
				
				simulateResults = new ArrayList<Object>();
				for (ComponentUnit component : components) {
					boolean ret;
					LotManager lotManager = Framework.getService(LotManager.class);			
					try {
						ret = componentManager.evaluateComponentCondition(component, condition);
					} catch (Exception e) {
						ret = false;
					}
					
					component.setLotId(lotSimulate.getLotId());
					component.setAttribute1(ret); //模拟结果
					simulateResults.add(component);
				}
				
				//刷新form
				formSimulateResult.setObject(simulateResults);
				formSimulateResult.refresh();
				
			} else {
				return;
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	public void createQueryForm(Composite parent, ADTable adTable, IMessageManager manager) {
		formLotQuery = new QueryTableForm(parent, SWT.NONE, adTable, manager);
		GridData gridData = new GridData(GridData.FILL_BOTH);
		formLotQuery.setLayoutData(gridData);
		formLotQuery.setLayout(new GridLayout(1, false));
	}
}
