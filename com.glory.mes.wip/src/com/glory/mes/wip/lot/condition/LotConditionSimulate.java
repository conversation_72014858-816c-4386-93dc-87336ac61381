package com.glory.mes.wip.lot.condition;

import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.condition.AbstractCondition;

/**
 *  Lot Condition模拟页面
 *  根据输入表达式,选择Lot,根据Lot模拟返回结果
 */
public class LotConditionSimulate extends EntityForm {

	private AbstractCondition condition;
	//private List<ComponentUnit> simulateResults;
	
	protected ListTableManager tableManager;
	protected NatTable natTable;
	private boolean indexFlag = true;
	
	Text txtExperssion;
	
	public LotConditionSimulate(Composite parent, int style, Object object, AbstractCondition condition, ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
		this.condition = condition;
		
		if (condition != null) {
			loadFromCondition();
		}
	}
	
	@Override
	public void createForm() {
		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit = new FormToolkit(getDisplay());
		toolkit.setBackground(getBackground());
		//form = toolkit.createScrolledForm(this);
		//form.setLayoutData(new GridData(GridData.FILL));
		
		Composite content = new Composite(this.getParent(), SWT.NONE);
		configureBody(content);
		
		//显示的条件表达式
		createSimpleConditionText(toolkit, content);

		//结果列表
	    createNewViewer(content);
	    
//	    try {
//			if (condition != null) {
//	            loadFromObject();
//	        }
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
	}
	
	public void loadFromCondition() {
		//表达式
		if (condition.getIsUseExpression()) {
			txtExperssion.setText(condition.getConditionExpression());
		} else {
			txtExperssion.setText(condition.buildParameterExpression());			
		}
	}

	@Override
	public void refresh() {
//		List<ADBase> adBases = new ArrayList<ADBase>();
		try {
//        	ADManager manager = getADManger();
//        	long objectRrn = tableManager.getADTable().getObjectRrn();
//        	adBases = manager.getEntityList(Env.getOrgRrn(), objectRrn, 
//                		Env.getMaxResult(), getWhereCluase(), "");	

			List<Object> simulateResults = (List<Object>)object;
			
			tableManager.setInput(simulateResults);	
        } catch (Exception e) {
        	ExceptionHandlerManager.asyncHandleException(e);
        }	
	}

	public void createSimpleConditionText(FormToolkit toolkit, Composite parent) {
		try {
			Composite compositeCurrent = toolkit.createComposite(parent, SWT.NONE);
			compositeCurrent.setLayout(new GridLayout(5, false));
			GridData gdForm1 = new GridData(GridData.FILL_BOTH);
			compositeCurrent.setLayoutData(gdForm1);

			
			GridData gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 200;
			
			//参数名
			toolkit.createLabel(compositeCurrent, Message.getString("prd.condition_expression"));
			txtExperssion = toolkit.createText(compositeCurrent, "", SWT.BORDER);
			txtExperssion.setLayoutData(gText);
			//txtExperssion.setTextLimit(32);
			
			
//			Button btnCheck = toolkit.createButton(form2, "Check", SWT.BUTTON1);
//			//btnCheck.setBounds(5, 60, 60, 30);
//			btnCheck.addSelectionListener(new SelectionListener() {
//				@Override
//				public void widgetDefaultSelected(SelectionEvent e) {
//					widgetSelected(e);
//				}
//
//				@Override
//				public void widgetSelected(SelectionEvent e) {
//					try {
//						//viewCheckFormAdapter();
//					} catch (Exception ex) {
//						ExceptionHandlerManager.asyncHandleException(ex);
//						return;
//					}
//				}
//			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createNewViewer(Composite parent){
		Composite resultComp = new Composite(parent, SWT.BORDER);
		GridLayout layout = new GridLayout(); 
        layout.verticalSpacing = 0;
        layout.marginHeight = 0;
        resultComp.setLayout(layout);
        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        
		tableManager = new FixSizeListTableManager(table);
		tableManager.setIndexFlag(indexFlag);
		tableManager.newViewer(resultComp);
	    natTable = tableManager.getNatTable();
		//tableManager.addDoubleClickListener(getDoubleClickListener());
		//tableManager.addSelectionChangedListener(getSelectChangeListener());
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
