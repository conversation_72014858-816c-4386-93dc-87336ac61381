package com.glory.mes.wip.lot.condition;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.FlowConditionDetail;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.condition.AbstractCondition;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.ComponentUnitAttribute;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotAttribute;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * Lot Condition定义页面
 */
public class ConditionForm extends EntityForm {

	public static final String FIELD_NAME_CONDITION_TYPE = "conditionType";
	public static final String FIELD_NAME_PARAMETER_NAME = "parameterName";
	public static final String FIELD_NAME_PARAMETER_COMPARISON = "parameterComparison";
	public static final String FIELD_NAME_PARAMETER_VALUE = "parameterValue";
	public static final String FIELD_NAME_PARAMETER_EXPRESSION = "parameterExpression";

	public static final String FIELD_NAME_IS_USE_EXPRESSION = "isUseExpression";
	public static final String FIELD_NAME_IS_CONDITION_EXPRESSION = "conditionExpression";

	private FlowConditionDetail detail;
	
	private ADManager adManager;

	private Button checkBoxIsUseExpression;
	private Button btnLeftBracket;
	private Button btnRightBracket;
	private Button btnAnd;
	private Button btnOr;
	private Button btnClear;

	private SquareButton btnAddToTable;
	
	private Text txtParemeterValue;
	private Text txtFormula;
	private RefTableField reworkProcedureField;
	private RefTableField reworkBackStepField;
	
	private XCombo comboObjectType;
	private XCombo comboParameterType;
	private XCombo comboParemeterName;;
	private XCombo comboParameterComparison;	
	private XCombo comboActionType;
	
	private Procedure reworkProcedure;
	
	public ConditionForm(Composite parent, int style, Object object, ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}
	
	public ConditionForm(Composite parent, int style, Object object, ADTable table, Procedure reworkProcedure, IMessageManager mmng) {
    	super(parent, style, object, mmng);
    	this.table = table;
		this.mmng = mmng;
		this.reworkProcedure = reworkProcedure;
		createForm();
    }

	@Override
	public void createForm() {
		try {
			adManager = Framework.getService(ADManager.class);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

		toolkit = new FFormToolKit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);

		Composite content = new Composite(body, SWT.NULL);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));

		// 对象类型
		createObjectText(toolkit, content);

		// 简单条件
		createSimpleConditionText(toolkit, content);

		// 添加按钮
		createAddButon(toolkit, content);

		// 表达式
		createFormulaText(toolkit, content);

		// 底部
		createCheckForm(toolkit, content);
		
		// 动作
		createActionForm(toolkit, content);
		
		try {
			if (object != null) {
				loadFromObject();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public boolean saveToObject() {

		if (!checkDataAll()) {
			return false;
		}

		saveData();

		return true;
	}
	
	public void saveData() {

		this.detail.setObjectType(comboObjectType.getText()); // 对象类型
		this.detail.setParameterType(comboParameterType.getText());
		this.detail.setIsUseExpression(checkBoxIsUseExpression.getSelection());
		
		// 表达式信息
		if (checkBoxIsUseExpression.getSelection()) {
			this.detail.setParameterName("");
			this.detail.setParameterComparison("");
			this.detail.setParameterValue("");
			this.detail.setConditionExpression(txtFormula.getText());
		} else {
			this.detail.setParameterName(comboParemeterName.getText());
			this.detail.setParameterComparison(comboParameterComparison.getText());
			this.detail.setParameterValue(txtParemeterValue.getText());
			this.detail.setConditionExpression("");
		}
		
		// 动作信息
		this.detail.setActionType(comboActionType.getText());
		this.detail.setActionParam1(reworkProcedureField.getText());
		this.detail.setActionParam2(reworkBackStepField.getText());
	}
	
	public boolean saveExperssionData() {

		if (!checkDataExperssion()) {
			return false;
		}

		saveData();

		return true;
	}

	private boolean checkDataAll() {
		if (!checkDataExperssion()) {
			return false;
		}
		
		if (!checkDataAction()) {
			return false;
		}
		
		return true;
	}
	
	private boolean checkDataExperssion() {
		if (StringUtil.isEmpty(comboObjectType.getText())) {
			comboObjectType.setFocus();
			return false;
		}

		if (StringUtil.isEmpty(comboParameterType.getText())) {
			comboParameterType.setFocus();
			return false;
		}

		if (checkBoxIsUseExpression.getSelection()) {
			if (StringUtil.isEmpty(txtFormula.getText())) {
				txtFormula.setFocus();
				return false;
			}
		} else {
			if (StringUtil.isEmpty(comboParemeterName.getText())) {
				comboParemeterName.setFocus();
				return false;
			}

			if (StringUtil.isEmpty(comboParameterComparison.getText())) {
				comboParameterComparison.setFocus();
				return false;
			}

			if (StringUtil.isEmpty(txtParemeterValue.getText())) {
				txtParemeterValue.setFocus();
				return false;
			}
		}

		return true;
	}
	
	private boolean checkDataAction() {
		if (StringUtil.isEmpty(comboActionType.getText())) {
			comboActionType.setFocus();
			return false;
		}

		if (comboActionType.getText().equals(AbstractCondition.ACTION_TYPE_REWORK)) {
			if (StringUtil.isEmpty(reworkProcedureField.getText())) {
				reworkProcedureField.getComboControl().setFocus();
				return false;
			}
		}

		return true;
	}

	@Override
	public void loadFromObject() {
		this.detail = (FlowConditionDetail) object;
		setExpressionControl(this.detail.getIsUseExpression());

		comboObjectType.setText(this.detail.getObjectType());
		comboParameterType.setText(this.detail.getParameterType());
		comboParemeterName.setText(this.detail.getParameterName() == null ? "" : this.detail.getParameterName());
		comboParameterComparison.setText(this.detail.getParameterComparison() == null ? "" : this.detail.getParameterComparison());
		txtParemeterValue.setText(this.detail.getParameterValue() == null ? "" : this.detail.getParameterValue());
		txtFormula.setText(this.detail.getConditionExpression() == null ? "" : this.detail.getConditionExpression());
		checkBoxIsUseExpression.setSelection(this.detail.getIsUseExpression());

		comboActionType.setText(this.detail.getActionType() == null ? "" : this.detail.getActionType());
		reworkProcedureField.setValue(this.detail.getActionParam1() == null ? "" : this.detail.getActionParam1());
		reworkBackStepField.setValue(this.detail.getActionParam2() == null ? "" : this.detail.getActionParam2());
	}

	public void createObjectText(FormToolkit toolkit, Composite parent) {
		try {
			Section sFormla = toolkit.createSection(parent,  Section.NO_TITLE | FFormSection.FFORM);
			sFormla.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			sFormla.setText(Message.getString("prd.object_type"));
			sFormla.setLayout(new GridLayout());
			Composite composite = toolkit.createComposite(sFormla);
			composite.setLayout(new GridLayout());
			composite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			sFormla.setClient(composite);
			
			Composite compositeCurrent = toolkit.createComposite(parent, SWT.NULL);
			compositeCurrent.setLayout(new GridLayout(2, false));
			GridData gdForm1 = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
			compositeCurrent.setLayoutData(gdForm1);

			GridData gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 400;

			// 对象类型
			toolkit.createLabel(compositeCurrent, Message.getString("prd.object_type"));
			// Label lblMeasure = toolkit.createLabel(form2,
			// Message.getString("common.lotqty"));
			// lblMeasure.setBounds(5, 7, 100, 20);
			ADTable comboAdTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			ListTableManager tableManager = new ListTableManager(comboAdTable);
			String whereClause = " referenceName = 'WIPConditionObjectType'";
			List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), comboAdTable.getObjectRrn(),
					Env.getMaxResult(), whereClause, null);
			tableManager.setInput(list);
			comboObjectType = new XCombo(compositeCurrent, tableManager, "key", "text", SWT.BORDER, true);
			comboObjectType.setLayoutData(gText);
			comboObjectType.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
			// comboObjectType.setBounds(460, 5, 230, 20);
			comboObjectType.setText("Lot");
			comboObjectType.addModifyListener(new ModifyListener() {
				
				@Override
				public void modifyText(ModifyEvent e) {
					detail.setObjectType(comboObjectType.getText());
					setParameterNameListAdapter();
				}
			});
			
			// 参数类型
			toolkit.createLabel(compositeCurrent, Message.getString("prd.if_parameter_type"));
			// Label lblMeasure = toolkit.createLabel(form2,
			// Message.getString("common.lotqty"));
			// lblMeasure.setBounds(5, 7, 100, 20);
			comboAdTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			tableManager = new ListTableManager(comboAdTable);
			whereClause = " referenceName = 'WIPConditionParameterType'";
			list = adManager.getEntityList(Env.getOrgRrn(), comboAdTable.getObjectRrn(), Env.getMaxResult(),
					whereClause, null);
			tableManager.setInput(list);
			comboParameterType = new XCombo(compositeCurrent, tableManager, "key", "text", SWT.BORDER, true);
			comboParameterType.setLayoutData(gText);
			comboParameterType.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
			comboParameterType.setText("property");
			comboParameterType.addModifyListener(new ModifyListener() {
				
				@Override
				public void modifyText(ModifyEvent e) {
					detail.setParameterType(comboParameterType.getText());
					setParameterNameListAdapter();
				}
			});

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void createSimpleConditionText(FormToolkit toolkit, Composite parent) {
		try {
			Section sFormla = toolkit.createSection(parent,  Section.NO_TITLE | FFormSection.FFORM);
			sFormla.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			sFormla.setText(Message.getString("prd.flow_condition"));
			sFormla.setLayout(new GridLayout());
			Composite composite = toolkit.createComposite(sFormla);
			composite.setLayout(new GridLayout());
			composite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			sFormla.setClient(composite);
			
			Composite compositeCurrent = toolkit.createComposite(parent, SWT.NULL);
			compositeCurrent.setLayout(new GridLayout(2, false));
			GridData gdForm2 = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
			compositeCurrent.setLayoutData(gdForm2);

			GridData gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 200;

			// 参数名
			toolkit.createLabel(compositeCurrent, Message.getString("wip.param_name"));
			ADTable comboAdTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			ListTableManager tableManager = new ListTableManager(comboAdTable);
			String whereClause = " referenceName = 'WIPConditionParaNameLot'";
			List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), comboAdTable.getObjectRrn(),
					Env.getMaxResult(), whereClause, null);
			tableManager.setInput(list);
			comboParemeterName = new XCombo(compositeCurrent, tableManager, "key", "text", SWT.BORDER, true);
			comboParemeterName.setLayoutData(gText);
			comboParemeterName.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));

			// 比较符
			toolkit.createLabel(compositeCurrent, Message.getString("common.comparator"));

			//ADTable 
			comboAdTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			//ListTableManager 
			tableManager = new ListTableManager(comboAdTable);
			//String 
			whereClause = " referenceName = 'WIPConditionComparison'";
			//List<ADBase> 
			list = adManager.getEntityList(Env.getOrgRrn(), comboAdTable.getObjectRrn(),
					Env.getMaxResult(), whereClause, null);
			tableManager.setInput(list);
			comboParameterComparison = new XCombo(compositeCurrent, tableManager, "key", "text", SWT.BORDER, true);
			comboParameterComparison.setLayoutData(gText);
			comboParameterComparison.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
			// comboObjectType.setBounds(460, 5, 230, 20);
			comboParameterComparison.setText("=");

			// 参数值
			toolkit.createLabel(compositeCurrent, Message.getString("wip.param_value"));
			txtParemeterValue = toolkit.createText(compositeCurrent, "", SWT.BORDER);
			txtParemeterValue.setLayoutData(gText);
			txtParemeterValue.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
			txtParemeterValue.setTextLimit(32);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void createAddButon(FormToolkit toolkit, Composite composite) {
		try {
			Composite compositeButton = toolkit.createComposite(composite);
			GridLayout layoutButtom = new GridLayout(2, false);
			compositeButton.setLayout(layoutButtom);
			GridData gdButtom = new GridData(GridData.FILL_BOTH);
			// gdForm2F.heightHint = 100;
			compositeButton.setLayoutData(gdButtom);

			// 是否使用表达式
			checkBoxIsUseExpression = toolkit.createButton(compositeButton, Message.getString("prd.is_use_expression"),
					SWT.CHECK);
			checkBoxIsUseExpression.addSelectionListener(new SelectionListener() {

				@Override
				public void widgetSelected(SelectionEvent e) {
					widgetDefaultSelected(e);
				}

				@Override
				public void widgetDefaultSelected(SelectionEvent e) {
					setExpressionControl(checkBoxIsUseExpression.getSelection());
				}
			});

			// 添加按钮
			// SquareButton
			btnAddToTable = UIControlsFactory.createButton(compositeButton, UIControlsFactory.BUTTON_DEFAULT);
			btnAddToTable.setText(Message.getString(ExceptionBundle.bundle.CommonAdd()));
			GridData gd = new GridData();
			gd.horizontalAlignment = SWT.RIGHT;
			gd.grabExcessHorizontalSpace = true;
			btnAddToTable.setLayoutData(gd);
			btnAddToTable.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						if (!checkBoxIsUseExpression.getSelection()) {
							return;
						}

						if (StringUtil.isEmpty(comboParemeterName.getText())) {
							comboParemeterName.setFocus();
							return;
						}

						if (StringUtil.isEmpty(comboParameterComparison.getText())) {
							comboParameterComparison.setFocus();
							return;
						}

						if (StringUtil.isEmpty(txtParemeterValue.getText())) {
							txtParemeterValue.setFocus();
							return;
						}

						detail.setParameterName(comboParemeterName.getText());
						detail.setParameterComparison(comboParameterComparison.getText());
						detail.setParameterValue(txtParemeterValue.getText());
						String simpleExp = detail.buildParameterExpression();

						txtFormula.setText(txtFormula.getText() + simpleExp);
					} catch (Exception ex) {
						ExceptionHandlerManager.asyncHandleException(ex);
						return;
					}
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void createFormulaText(FormToolkit toolkit, Composite parent) {
		// ---------------------- 公式
		Section sFormla = toolkit.createSection(parent,  Section.NO_TITLE | FFormSection.FFORM);
//		GridData gsControlInfo1 = new GridData(GridData.FILL_HORIZONTAL);
		sFormla.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		sFormla.setText(Message.getString("prd.condition_expression"));
		sFormla.setLayout(new GridLayout());
//		toolkit.createCompositeSeparator(sFormla);
		Composite composite = toolkit.createComposite(sFormla);
		composite.setLayout(new GridLayout());
		composite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		sFormla.setClient(composite);

		
		Composite formFormla = toolkit.createComposite(parent);
		GridLayout flayout2F = new GridLayout(20, false);
		formFormla.setLayout(flayout2F);
		GridData gdForm2F = new GridData(GridData.FILL_BOTH);
		gdForm2F.heightHint = 100;
		formFormla.setLayoutData(gdForm2F);

		// toolkit.createLabel(formFormla, " ");
		// toolkit.createLabel(formFormla, " ");
		btnLeftBracket = toolkit.createButton(formFormla, "(", SWT.NONE);
		btnRightBracket = toolkit.createButton(formFormla, ")", SWT.NONE);

		toolkit.createLabel(formFormla, "  ");
		btnAnd = toolkit.createButton(formFormla, "&&&&", SWT.NONE);
		btnOr = toolkit.createButton(formFormla, "||", SWT.NONE);

		toolkit.createLabel(formFormla, "  ");
		btnClear = toolkit.createButton(formFormla, "CLEAR", SWT.NONE);

		// --------------
		createTextArea(toolkit, formFormla);

		// --------------
		btnClear.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText("");
			}
		});

		btnLeftBracket.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + "(");
			}
		});

		btnRightBracket.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + ")");
			}
		});

		btnAnd.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + " && ");
			}
		});

		btnOr.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + " || ");
			}
		});
	}

	public void createTextArea(FormToolkit toolkit, Composite formFormla) {
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.horizontalSpan = 20;
		txtFormula = toolkit.createText(formFormla, "", SWT.WRAP | SWT.MULTI | SWT.V_SCROLL);
		// txtFormula = toolkit.createText(formFormla,
		// detail.getConditionExpression() == null ? "" :
		// detail.getConditionExpression(),
		// SWT.WRAP | SWT.MULTI | SWT.V_SCROLL);
		txtFormula.setLayoutData(gd);
		txtFormula.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
	}

	public void createCheckForm(FormToolkit toolkit, Composite parent) {
		Composite formBottom = toolkit.createComposite(parent);
		GridLayout flayoutBottom = new GridLayout(2, false);
		formBottom.setLayout(flayoutBottom);
		GridData gdFormBottom = new GridData(GridData.FILL_BOTH);
		// gdForm2F.heightHint = 100;
		formBottom.setLayoutData(gdFormBottom);

		// 模拟按钮
		GridData gd = new GridData();
		//gd.horizontalAlignment = SWT.RIGHT;
		//gd.grabExcessHorizontalSpace = true;

		// 校验按钮
		SquareButton btnCheck = UIControlsFactory.createButton(formBottom, UIControlsFactory.BUTTON_DEFAULT);
		btnCheck.setText(Message.getString("prd.condition_check"));
		btnCheck.setLayoutData(gd);
		btnCheck.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				try {
					checkExperssionFormAdapter();
				} catch (Exception ex) {
					ExceptionHandlerManager.asyncHandleException(ex);
					return;
				}
			}
		});

		gd = new GridData();
		//gd.horizontalAlignment = SWT.RIGHT;
		//gd.grabExcessHorizontalSpace = true;
		
		// 模拟按钮		
		SquareButton btnSimulate = UIControlsFactory.createButton(formBottom, UIControlsFactory.BUTTON_DEFAULT);
		btnSimulate.setText(Message.getString(ExceptionBundle.bundle.CommonSimulate()));
		btnSimulate.setLayoutData(gd);
		btnSimulate.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				try {
					viewSimulateFormAdapter();
				} catch (Exception ex) {
					ExceptionHandlerManager.asyncHandleException(ex);
					return;
				}
			}
		});
	}
	
	public void createActionForm(FormToolkit toolkit, Composite parent) {
		try {
			// ---------------------- 动作
			Section section = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
//			GridData gsControlInfo1 = new GridData(GridData.FILL_HORIZONTAL);
			section.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			section.setText(Message.getString("edc.bin_lab_action_info"));
			section.setLayout(new GridLayout());

//			toolkit.createCompositeSeparator(section);
			Composite formFormla = toolkit.createComposite(section);
			formFormla.setLayout(new GridLayout());
			formFormla.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			section.setClient(formFormla);
			
			Composite composite = toolkit.createComposite(parent);
			GridLayout layout = new GridLayout(2, false);
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			//gd.heightHint = 200;
			composite.setLayoutData(gd);
			
			GridData gText = new GridData(GridData.FILL_HORIZONTAL);
			//gText.widthHint = 400;
	
			// 动作类型
			toolkit.createLabel(composite, Message.getString("edc.bin_action_type"));

			comboActionType = RCPUtil.getSysRefListCombo(composite, "WIPConditionActionType", Env.getOrgRrn(), true);
			comboActionType.setLayoutData(gText);
			comboActionType.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
			comboActionType.addModifyListener(new ModifyListener() {
				
				@Override
				public void modifyText(ModifyEvent e) {
					XCombo widget = (XCombo) e.widget;
					if (AbstractCondition.ACTION_TYPE_REWORK.equals(widget.getText())) {
						reworkBackStepField.setEnabled(true);
						reworkProcedureField.setEnabled(true);
					} else if (AbstractCondition.ACTION_TYPE_REWORK_BY_DEFECFT.equals(widget.getText())) {
						reworkBackStepField.setEnabled(true);
						reworkProcedureField.setEnabled(false);
					} else {
						reworkProcedureField.setEnabled(false);
						reworkProcedureField.setValue("");
						reworkBackStepField.setEnabled(false);
						reworkBackStepField.setValue("");
					}
				}
			});
			
			// 返工流程
			reworkProcedureField = RCPUtil.createRefTableField(composite, "reworkFlow", "PRDProcedureNameList(Active)", 
					Message.getString("wip.trackout.rework.procedure"), false);
			// 返工返回流程
			reworkBackStepField = RCPUtil.createRefTableField(composite, "backStep", "PRDStepStateList", Message.getString("wip.return_steps"), false);
			if (reworkProcedure != null && reworkProcedure.getObjectRrn() != null) {
    			PrdManager prdManager = Framework.getService(PrdManager.class);
    			List<StepState> steps = prdManager.getStepChildren(reworkProcedure);
    			reworkBackStepField.setInput(steps);
    		}
		} catch (Exception ex) {
			ExceptionHandlerManager.asyncHandleException(ex);
		}
	}
	
	private void setExpressionControl(boolean flag) {
		btnLeftBracket.setEnabled(flag);
		btnRightBracket.setEnabled(flag);
		btnAnd.setEnabled(flag);
		btnOr.setEnabled(flag);
		btnClear.setEnabled(flag);

		txtFormula.setText("");
		txtFormula.setEnabled(flag);
	}

	protected void viewSimulateFormAdapter() {
		try {
			if (!saveExperssionData()) {
				return;
			}

			ConditionSimulateDialog dialog = new ConditionSimulateDialog(UI.getActiveShell(), detail, false);
			if (dialog.open() == 0) {
				// conditionField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void checkExperssionFormAdapter() {
		try {
			if (!saveExperssionData()) {
				return;
			}

			this.detail = (FlowConditionDetail) object;
			
			//获取变量
			Map<String, Object> variables = Maps.newHashMap();
			if (AbstractCondition.OBJECT_TYPE_LOT.equals(detail.getObjectType())) {
				//对批次的模拟
				
				if (AbstractCondition.PARAMETER_TYPE_ATTRIBUTE.contentEquals(detail.getParameterType())) {
					//获取 LotAttribute
					List<LotAttribute> lotAttributes = adManager.getEntityList(Env.getOrgRrn(), LotAttribute.class, 
							Integer.MAX_VALUE, "", "seqNo");
					
					if (CollectionUtils.isNotEmpty(lotAttributes)) {
						// attribute当前只支持传String，值设置为0，方便各种数据类型的校验
						Map<String, String> attrs = lotAttributes.stream().collect(Collectors.toMap(a -> a.getName(), a -> "0"));
						variables.put(AbstractCondition.EXPRESSION_VARIABLE_ATTRIBUTES, attrs);
					}
				}
				
				variables.put(AbstractCondition.EXPRESSION_VARIABLE_LOT, new Lot());
			} else if (AbstractCondition.OBJECT_TYPE_COMPONENT.equals(detail.getObjectType())) {
				//对组件的模拟
				
				if (AbstractCondition.PARAMETER_TYPE_ATTRIBUTE.contentEquals(detail.getParameterType())) {
					//获取 LotAttribute
					List<ComponentUnitAttribute> compAttributes = adManager.getEntityList(Env.getOrgRrn(), ComponentUnitAttribute.class, 
							Integer.MAX_VALUE, "", "seqNo");
					if (CollectionUtils.isNotEmpty(compAttributes)) {
						Map<String, String> attrs = compAttributes.stream().collect(Collectors.toMap(a -> a.getName(), a -> "0"));
						variables.put(AbstractCondition.EXPRESSION_VARIABLE_ATTRIBUTES, attrs);
					}
				}
				variables.put(AbstractCondition.EXPRESSION_VARIABLE_COMPONENT, new ComponentUnit());
			} else {
				return;
			}
			
			//获取表达式
			String expression = StringUtil.isEmpty(detail.getConditionExpression()) ? 
					detail.buildParameterExpression() : detail.getConditionExpression();
			// 报异常说明表达式解析失败，表达式有问题
			AbstractCondition.evaluateExpression(expression, variables);
			UI.showInfo(Message.getString("prd.expression_is_correct"));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void setParameterNameListAdapter() {
		try {
			comboParemeterName.setText("");
			
			if (comboParameterType.getText().equals(AbstractCondition.PARAMETER_TYPE_PROPERTY)) {
				String referenceName;
				if (comboObjectType.getText().equals(AbstractCondition.OBJECT_TYPE_LOT)) {
					referenceName = "WIPConditionParaNameLot";
				} else if (comboObjectType.getText().equals(AbstractCondition.OBJECT_TYPE_COMPONENT)) {
					referenceName = "WIPConditionParaNameComp";
				} else {
					return;
				}
				
				ADTable comboAdTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
				//ListTableManager tableManager = new ListTableManager(comboAdTable);
				String whereClause = " referenceName = '" + referenceName + "'";
				List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), comboAdTable.getObjectRrn(),
						Env.getMaxResult(), whereClause, null);
				//tableManager.setInput(list);
				comboParemeterName.setInput(list);
			} else if (comboParameterType.getText().equals(AbstractCondition.PARAMETER_TYPE_ATTRIBUTE)) {

				List<ADRefList> adRefLists = new ArrayList<ADRefList>();
				if (comboObjectType.getText().equals(AbstractCondition.OBJECT_TYPE_LOT)) {
					//获取 LotAttribute
					List<LotAttribute> lotAttributes = adManager.getEntityList(Env.getOrgRrn(), LotAttribute.class, 
							Integer.MAX_VALUE, "", "seqNo");
					
					//重新加载list
					for (LotAttribute attribute : lotAttributes) {
						ADRefList adRefList = new ADRefList();
						adRefList.setKey(attribute.getName());
						adRefList.setText(attribute.getName());
						adRefList.setDescription(attribute.getDescription());
						adRefLists.add(adRefList);
					}
					
					//comboParemeterName.setInput(lotAttributes);
				} else if (comboObjectType.getText().equals(AbstractCondition.OBJECT_TYPE_COMPONENT)) {
					List<ComponentUnitAttribute> compAttributes = adManager.getEntityList(Env.getOrgRrn(), ComponentUnitAttribute.class, 
							Integer.MAX_VALUE, "", "seqNo");
					
					//重新加载list
					for (ComponentUnitAttribute attribute : compAttributes) {
						ADRefList adRefList = new ADRefList();
						adRefList.setKey(attribute.getName());
						adRefList.setText(attribute.getName());
						adRefList.setDescription(attribute.getDescription());
						adRefLists.add(adRefList);
					}
					//comboParemeterName.setInput(compAttributes);
				} else {
					return;
				}
				comboParemeterName.setInput(adRefLists);
			} else {
				return;
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void refresh() {
		super.refresh();
	}
}
