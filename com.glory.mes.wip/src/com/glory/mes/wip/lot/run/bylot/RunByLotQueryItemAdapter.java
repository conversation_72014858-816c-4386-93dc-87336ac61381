package com.glory.mes.wip.lot.run.bylot;

import java.util.Date;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;

public class RunByLotQueryItemAdapter extends ListItemAdapter<Lot> {
	@Override
	public Color getBackground(Object element, String id) {
		Lot lot = (Lot) element;
		Date sysDate = new Date();
		//批次暂停标红，批次的最大或者最小到期时间在三十分钟以内颜色标黄。
    	if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
    		Color color = new Color(Display.getCurrent(), 255, 111, 111);
    		return color;
    	} else if(lot.getMinimalExpireTime() !=null){
    		if((0 < lot.getMinimalExpireTime().getTime() - sysDate.getTime() && lot.getMinimalExpireTime().getTime() - sysDate.getTime() < 30)) {
    			Color color = new Color(Display.getCurrent(), 231, 255, 31);
        		return color;
    		}
    	} else if(lot.getExpireTime() != null) {
    		if((0 < lot.getExpireTime().getTime() - sysDate.getTime() && lot.getExpireTime().getTime() - sysDate.getTime() < 30)) {
    			Color color = new Color(Display.getCurrent(), 231, 255, 31);
        		return color;
    		}
    	}else {
    		return null;
    	}
		return null;
	}
}
