package com.glory.mes.wip.lot.run.byeqp.glc;

import javax.annotation.PreDestroy;
import javax.inject.Inject;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.e4.ui.workbench.modeling.IPartListener;

import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.custom.depend.MsgConsoleView;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.IByEqpAction;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.LotRunByEqpExtensionPoint;
import com.glory.mes.wip.model.Lot;

/**
 * 当前按设备作业代码弃用，使用新版本按设备作业，实现可以根据设备作业模式区分作业页面。
 * 新代码路径com.glory.mes.wip.byeqp.ByEqpEditor
 */
@Deprecated
public class ByEqpEditor extends GlcEditor implements IPartListener {
	
	private static final Logger logger = Logger.getLogger(ByEqpEditor.class);
	
	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.run.byeqp.glc.ByEqpEditor";
	
	@Inject
	protected EPartService partService;
	
	@Inject
	protected EModelService modelService;
	
	protected ByEqpConsole console;
	
	protected ByEqpManager byEqpManager;
	
	@PreDestroy
	public void preDestroy() {
		partService.removePartListener(this);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		partService.addPartListener(this);
		
		IByEqpAction byEqpAction = LotRunByEqpExtensionPoint.getMainAction();
		byEqpManager = byEqpAction.getManager();
		if (byEqpManager == null) {
			byEqpManager = new ByEqpManagerDefault();
		}
		registerByEqpManager(byEqpManager, true);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.BUTTON_EQPINFO), this::eqpInfoAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.BUTTON_PREPARE), this::prepareAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.BUTTON_MYREFRESH), this::eqpRefreshAdapter);

		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.BUTTON_TRACKIN), this::trackInAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.BUTTON_PREPARE), this::prepareAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.BUTTON_INREFRESH), this::waitRefreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.BUTTON_DOCP), this::docpAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.BUTTON_TRACKOUT), this::trackOutAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.BUTTON_ABORT), this::abortAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.BUTTON_RUNREFRESH), this::runRefreshAdapter);

		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_EQUIPMENT_ID, GlcEvent.EVENT_ENTERPRESSED), this::equipmentEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_LOT_ID, GlcEvent.EVENT_ENTERPRESSED), this::lotEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_CARRIER_ID, GlcEvent.EVENT_ENTERPRESSED), this::carrierEnterPressed);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_RUNNINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED), this::runningSelectionAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_WAITTINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED), this::waitingSelectionAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_RUNNINGLOTS, GlcEvent.EVENT_DOUBLE_CLICK), this::lotListDoubleClickAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_WAITTINGLOTS, GlcEvent.EVENT_DOUBLE_CLICK), this::lotListDoubleClickAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(ByEqpManagerContext.FIELD_EQPTREE, GlcEvent.EVENT_SELECTION_CHANGED), this::eqpSelectionChangeAdapter);
	}
	
	public void registerByEqpManager(ByEqpManager byEqpManager, boolean init) {
		byEqpManager.setContext(getDefaultContext(init));
		byEqpManager.byEqpFormActiom();
		this.byEqpManager = byEqpManager;
		byEqpManager.setRegistered(true);
	}
	
	private void eqpInfoAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.eqpInfoAdapter(obj);
		}
	}
	
	private void prepareAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.prepareAdapter(obj);
		}
	}
	
	private void eqpRefreshAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.eqpRefreshAdapter(obj);
		}
	}
	
	private void waitRefreshAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.waitRefreshAdapter(obj);
		}
	}
	
	private void docpAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.docpAdapter(obj);
		}
	}
	
	
	private void trackOutAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.trackOutAdapter(obj);
		}
	}
	
	private void abortAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.abortAdapter(obj);
		}
	}
	
	private void runRefreshAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.runRefreshAdapter(obj);
		}
	}
	
	private void equipmentEnterPressed(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.equipmentEnterPressed(obj);
		}
	}
	
	private void lotEnterPressed(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.lotEnterPressed(obj);
		}
	}
	
	private void carrierEnterPressed(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.carrierEnterPressed(obj);
		}
	}
	
	private void lotListDoubleClickAdaptor(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.lotListDoubleClickAdaptor(obj);
		}
	}
	
	private void waitingSelectionAdaptor(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.waitingSelectionAdaptor(obj);
		}
	}
	
	private void eqpSelectionChangeAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.eqpSelectionChangeAdapter(obj);
		}
	}
	
	public void eqpSelectionChanged() {
		if (byEqpManager != null) {
			byEqpManager.eqpSelectionChanged();
		}
	}
	
	private ByEqpManagerContext getDefaultContext(boolean init) {
		ByEqpManagerContext context = new ByEqpManagerContext();
		context.setEqpEditor(this);
		context.setPartService(partService);
		context.setModelService(modelService);
		context.setMainGlcForm(form);
		context.setEventBroker(eventBroker);
		context.setAdManager(getADManger());
		context.setCurrentEqp(getCurrentEqp());
		
		ByEqpConsole console = (ByEqpConsole) MsgConsoleView.getInstance();
		context.setConsole(console);
		
		if (init) {
			context.setCurrentAction(LotRunByEqpExtensionPoint.getMainAction());
			context.setCurrentEqp(null);
		} else if (getByEqpManager() != null && getByEqpManager().getContext() != null) {
			context.setCurrentAction(getByEqpManager().getContext().getCurrentAction());
			context.setCurrentEqp(getCurrentEqp());
		}
		
		return context;
	}
	
	public void lotStatusChanged(String state, String holdState) {
		if (byEqpManager != null) {
			byEqpManager.lotStatusChanged(state, holdState);
		}
	}
	
	public boolean isEqpAvailable() {
		if (byEqpManager != null && byEqpManager.getContext() != null) {
			return byEqpManager.getContext().isEqpAvailable();
		}
		
		return false;
	}
	
	public CustomField getFieldWaitting() {
		if (byEqpManager != null) {
			return byEqpManager.getFieldWaitting();
		}
		return null;
	}
	
	public void runningSelectionAdaptor(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.runningSelectionAdaptor(obj);
		}
	}
	
	public void trackInAdapter(Object obj) {
		if (byEqpManager != null) {
			byEqpManager.trackInAdapter(obj);
		}
	}
	
	public void refresh(int i) {
		if (byEqpManager != null) {
			byEqpManager.refresh(i);
		}
	}
	
	public void setCurrentEqp(Equipment currentEqp) {
		if (byEqpManager != null && byEqpManager.getContext() != null) {
			byEqpManager.getContext().setCurrentEqp(currentEqp);
		}
		
	}
	
	public boolean isRTDAvailable() {
		if (byEqpManager != null) {
			byEqpManager.isRTDAvailable();
		}
		return false;
	}
	
	public Equipment getCurrentEqp() {
		if (byEqpManager != null && byEqpManager.getContext() != null) {
			return byEqpManager.getContext().getCurrentEqp();
		}
		return null;
	}

	public ByEqpManager getByEqpManager() {
		return byEqpManager;
	}

	public void setByEqpManager(ByEqpManager byEqpManager) {
		this.byEqpManager = byEqpManager;
	}
	
	@Override
	public void partActivated(MPart part) {
		if (part.equals(mPart)) {
			//如果激活的是当前MPart
			ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);
			
			//用于通过右键菜单显示页面,Attribute记录批次号
			if (!StringUtil.isEmpty(adEditor.getAttribute1()) && byEqpManager != null) {
				String lotId = adEditor.getAttribute1();
				try {
					byEqpManager.eqpRefreshAdapter(null);
					Lot lot = LotProviderEntry.getLotByLine(lotId);
					RASManager rasManager = Framework.getService(RASManager.class);
					Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), lot.getEquipmentId(), false);
					if (equipment != null) {
						byEqpManager.getContext().setCurrentEqp(equipment);
						byEqpManager.setTextValue(equipment.getEquipmentId(), lotId, "");
						byEqpManager.eqpSelectionChanged();
					} else {
						byEqpManager.getContext().setCurrentEqp(null);
						if (console != null && !console.isDisposed()) {
							console.error(Message.getString("ras.equipment_no_found"));
							return;
						}
					}
					byEqpManager.searchLot(lot);
				} catch (Exception e) {
					logger.error("Error at ByEqpEditor : partActivated() ", e);
					ExceptionHandlerManager.asyncHandleException(e);
				}
				
				adEditor.setAttribute1(null);
			}
		}
	}
	
	public ByEqpConsole getConsole() {
		return console;
	}

	public void setConsole(ByEqpConsole console) {
		this.console = console;
	}
	
	public IEventBroker getEventBroker() {
		return eventBroker;
	}

	@Override
	public void partBroughtToTop(MPart part) {}

	@Override
	public void partDeactivated(MPart part) {}

	@Override
	public void partHidden(MPart part) {}

	@Override
	public void partVisible(MPart part) {}

	

}
