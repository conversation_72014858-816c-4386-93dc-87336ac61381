package com.glory.mes.wip.lot.run.abort;

import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardDialog;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.mes.wip.lot.run.abort.multieqp.MultiEqpAbortWizard;

public class AbortDialog extends FlowWizardDialog {
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 350;

	public AbortDialog(Shell parentShell, FlowWizard newWizard) {
		super(parentShell, newWizard);
	}
	
	@Override
	public int open() {
		AbortWizard wizard = (AbortWizard)getWizard(); 
		if (wizard instanceof MultiEqpAbortWizard) {
			MultiEqpAbortWizard multiEqpAbortWizard = (MultiEqpAbortWizard) wizard;
			if (multiEqpAbortWizard.validateStepCategory()) {
				return super.open();
			} else {
				setReturnCode(CANCEL);
				close();
				return this.getReturnCode();
			}
		}else {
			if (wizard.validateStepCategory()) {
				return super.open();
			} else {
				setReturnCode(CANCEL);
				close();
				return this.getReturnCode();
			}
		}		
	}

	/*
	 * 取消将焦点默认的放在Next Button上
	 */
	public void updateButtons() {
		boolean canFlipToNextPage = false;
		if (currentPage != null) {
			if (backButton != null) {
				backButton.setEnabled(getCurrentPage().getPreviousPage() != null);
			}
			if (nextButton != null) {
				canFlipToNextPage = getCurrentPage().canFlipToNextPage();
				nextButton.setEnabled(canFlipToNextPage);
			}
		}
	}
	
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("abort-dialog"));
		return super.buildView(parent);
	}

	@Override
	public void showPage(IWizardPage page) {
		super.showPage(page);
		FlowWizardPage fPage = ((FlowWizardPage)page);
		fPage.refresh();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
}
