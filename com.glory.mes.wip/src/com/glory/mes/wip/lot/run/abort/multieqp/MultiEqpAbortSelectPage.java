package com.glory.mes.wip.lot.run.abort.multieqp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.abort.AbortBatchLotSelectPage;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;

public class MultiEqpAbortSelectPage extends AbortBatchLotSelectPage {
	
	protected static String TABLE_NAME = "WIPMultiEqpLotAbort";
	
	protected void createLotListComposite(Composite parent)  throws Exception {
		//获得需要TrackOut的批次
		List<Lot> cloneLots = cloneTempLotByContext();
		context.setLots(cloneLots);
		if (CollectionUtils.isNotEmpty(cloneLots)) {
		    for (Lot lot : cloneLots) {
		        lot.setLotComment(null);
		    }
		}

		ADManager adManager = Framework.getService(ADManager.class);
		ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		tableManager = new CheckBoxFixEditorTableManager(adTable);
		tableManager.newViewer(parent);
		tableManager.setInput(cloneLots);
		
		//设置默认选中项
		if (context.getLots() != null) {
			List<Lot> selectedAbortLots = new ArrayList<Lot>();
			for (Lot lot : context.getLots()) {
				for (Lot abortLot : cloneLots) {
					if (abortLot.equals(lot)) {
						selectedAbortLots.add(abortLot);
						break;
					}
				}
			}
			for(Lot lot : selectedAbortLots) {
				tableManager.checkObject(lot);
			}
		}
		
		context.setLots(cloneLots);
	}

	protected List<Lot> cloneTempLotByContext() {
		try {
			Lot lot = context.getLots().get(0);
			LotManager lotManager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);

			List<ProcessUnit> processUnits = null;
			// 查询带Component的lot
			if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
				lot = lotManager.getLot(lot.getObjectRrn());
				List<QtyUnit> qtyUnits = adManager.getEntityList(Env.getOrgRrn(), QtyUnit.class, Integer.MAX_VALUE,
						" parentUnitRrn = " + lot.getObjectRrn() + " AND processState = '"
								+ QtyUnit.PROCESS_STATE_RUN + "'",
						"");
				processUnits = Lists.newArrayList(qtyUnits);
			} else {
				lot = lotManager.getLotWithComponent(lot.getObjectRrn());
				processUnits = lot.getSubProcessUnit();
				processUnits = processUnits.stream().filter(p -> p.getEquipmentId() != null)
						.collect(Collectors.toList());
			}

			if (processUnits == null || processUnits.size() == 0) {
				return null;
			}

			List<Lot> tempLotList = new ArrayList<Lot>();
			long i = 1;
			if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
				for (ProcessUnit qtyUnit : processUnits) {
					Lot tempLot = (Lot) lot.clone();
					tempLot.setObjectRrn(i++);
					tempLot.setEquipmentId(qtyUnit.getEquipmentId());
					tempLot.setSubProcessUnit(Lists.newArrayList(qtyUnit));
					
					tempLot.setMainQty(qtyUnit.getMainQty());
					tempLot.setSubQty(qtyUnit.getSubQty());
					tempLotList.add(tempLot);
				}
			} else if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				Map<String, List<ProcessUnit>> processUnitByEqpMap = processUnits.stream()
						.collect(Collectors.groupingBy(ProcessUnit::getEquipmentId));
				for (String equipmentId : processUnitByEqpMap.keySet()) {
					List<ProcessUnit> units = processUnitByEqpMap.get(equipmentId);
					Lot tempLot = (Lot) lot.clone();
					tempLot.setObjectRrn(i++);
					tempLot.setEquipmentId(equipmentId);
					tempLot.setSubProcessUnit(units);
					
					tempLot.setMainQty(ProcessUnit.getUnitMainTotal(units));
					tempLot.setSubQty(ProcessUnit.getUnitSubTotal(units));
					tempLotList.add(tempLot);
				}
			}
			return tempLotList;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

}
