package com.glory.mes.wip.lot.run.abort;

import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.IWizardPage;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.GlcFlowWizard;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;

public class AbortWizard extends GlcFlowWizard {
    public static String ABORT_BYEQP = "DefaultAbort";
	private static final Logger logger = Logger.getLogger(AbortWizard.class);
	protected InContext context;
	protected String operator1;
	
	public AbortWizard() {
	}
	
	public AbortWizard(String abortId) {
		super();
		this.setCategory(abortId);
	}
	
	public AbortWizard(InContext context) {
		this(ABORT_BYEQP);
		this.setContext(context);
	}
	
	@Override
	public boolean performFinish() {
		try{
		    InContext context = (InContext)this.getContext();
		    for (Lot lot : context.getLots()) {
		    	if (!StringUtil.isEmpty(operator1)) {
					lot.setOperator1(operator1);
		    	} else {
					lot.setOperator1(Env.getUserName());
		    	}
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.abortLot(context, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.abort_success"));
			return true;
		} catch (Exception e) {
        	logger.error("TrackOutWizard : performFinish", e);
        	ExceptionHandlerManager.asyncHandleException(e);
        }
		return false;
	}

	public boolean validateStepCategory() {
		if (context.getCurrentStep() != null && !StringUtil.isEmpty(context.getCurrentStep().getUseCategory())) {
			if (Step.USE_CATEGORY_PROCESS.equals(context.getCurrentStep().getUseCategory())
					|| Step.USE_CATEGORY_MEASURE.equals(context.getCurrentStep().getUseCategory())
					|| Step.USE_CATEGORY_BOTH.equals(context.getCurrentStep().getUseCategory())) {
				//标准UseCategory,不处理
				return true;
			} else {
				if (!context.getCurrentStep().getUseCategory().equals(context.getUseCategory())) {
					//特殊UseCategory,必须校验传值是否一致
					//提示(特殊类别作业必须使用特定功能)
					UI.showError(Message.getString("wip.track_special_category"));
					return false;
				}
			}
		} 
		return true;
	}
	
	@Override
	public IWizardPage getStartingPage() {
		return startPage;
	}
	
	public void setContext(InContext context) {
		this.context = context;
	}

	public InContext getContext() {
		return context;
	}
	
	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}
}
