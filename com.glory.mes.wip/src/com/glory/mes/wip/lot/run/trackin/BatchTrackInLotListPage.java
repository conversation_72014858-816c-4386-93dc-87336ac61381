package com.glory.mes.wip.lot.run.trackin;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

/**
 * 用于Batch TrackIn时列出所有的批次
 */
public class BatchTrackInLotListPage extends GlcFlowWizardPage {

	protected TrackInContext context;
	protected ListTableManagerField batchListField;
	protected ListTableManager tableManager;
	
	public static final String TABLE_NAME = "WIPBatchTrackLot";
	
	public static final String FIELD_BATCHLIST = "batchList";
	
	public List<Lot> lots = Lists.newArrayList();

	public BatchTrackInLotListPage() {
		super();
	}
	
	@Override
	public void refresh() {
		setErrorMessage(null);
	}

	@Override
	protected boolean skipPageValidate() {
		try {
			context = (TrackInContext)((RunWizard) getWizard()).getContext();
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> inLots = context.getLots();
			if (inLots.size() > 1) {
				// 操作员重新组的batch
				lots.addAll(inLots);
			} else {
				Lot lot = context.getLots().get(0);
				lots = lotManager.getLotsByBatchTrack(Env.getOrgRrn(), 
						lot.getBatchId(), lot, LotStateMachine.TRANS_TRACKIN);
				if (StringUtil.isEmpty(lot.getBatchId()) || lots.size() == 1) {
					// 没有batch跳过该页面
					((RunWizard) getWizard()).getDialog().skipPressed();
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return super.skipPageValidate();
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		batchListField = form.getFieldByControlId(FIELD_BATCHLIST, ListTableManagerField.class);
		batchListField.getListTableManager().setInput(lots);
		setTitle(Message.getString(WipExceptionBundle.bundle.BatchLotTrackIn()));
		setMessage(Message.getString(WipExceptionBundle.bundle.BatchLotTrackInInfo()));
	}
	
	@Override
	public String doNext() {
		try {
			List<Lot> lots = (List)Lists.newArrayList(batchListField.getListTableManager().getInput());
			
			if (CollectionUtils.isEmpty(lots)) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.WipTrackInLotIsNull()));
				return "";
			}
			
			// 确认选择了一个完整的batch，或者没有Batch
			List<String> batchIds = lots.stream().map(l -> StringUtil.isEmpty(l.getBatchId()) ? "" : l.getBatchId())
					.distinct().collect(Collectors.toList());
			if (batchIds.size() > 1) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.WipTrackInMuiltBatchNotAllow()));
				return "";
			}

			String batchId = batchIds.get(0);
			if (!StringUtil.isEmpty(batchId)) {
				// 检查batch是否完整
				LotManager lotManager = Framework.getService(LotManager.class);
				List<Lot> batchLots = lotManager.getLotsByBatch(Env.getOrgRrn(), batchId);
				if (lots.size() != batchLots.size()) {
					batchLots.removeAll(lots);
					String ids = batchLots.stream().map(Lot::getLotId).collect(Collectors.joining(","));
					UI.showError(String.format(Message.getString(WipExceptionBundle.bundle.WipTrackInBatchNotComplete()), ids));
					return "";
				}
			}
			
			context.setLots(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return "";
		}
		return getDefaultDirect();
	}
	
	@Override
	public String doPrevious() {
		return "";
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

}