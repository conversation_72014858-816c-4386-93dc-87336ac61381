package com.glory.mes.wip.lot.run.byeqp.sorting;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.nebula.widgets.nattable.config.IConfigRegistry;
import org.eclipse.nebula.widgets.nattable.edit.EditConfigAttributes;
import org.eclipse.nebula.widgets.nattable.edit.editor.ComboBoxCellEditor;
import org.eclipse.nebula.widgets.nattable.style.DisplayMode;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.base.Objects;
import com.glory.framework.core.exception.ExceptionBundle;

public class SortingJobMergeReserveDialog extends GlcBaseDialog{

	private static final String ACTION_TYPE_CHANGE = "change";
	
	private static final String FIELD_SOURCEPORT = "targetPort";
	private static final String FIELD_SOURCECARRIER = "targetCarrier";
	private static final String FIELD_SORTINGJOBLIST = "sortingJobList";
	
	private TextField carrierTextField;
	private RefTableField portRefTableField;
	private TableEditorField tableManagerField;
	
	private LotSortingJob sortingJob;
	private Equipment equipment;
	private String actionType;
	
	public SortingJobMergeReserveDialog(String adFormName, String authority, IEventBroker eventBroker, LotSortingJob sortingJob, Equipment equipment, String actionType) {
		super(adFormName, authority, eventBroker);
		this.sortingJob = sortingJob;
		this.equipment = equipment;
		this.actionType = actionType;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		portRefTableField = form.getFieldByControlId(FIELD_SOURCEPORT, RefTableField.class);
		carrierTextField = form.getFieldByControlId(FIELD_SOURCECARRIER, TextField.class);
		tableManagerField = form.getFieldByControlId(FIELD_SORTINGJOBLIST, TableEditorField.class);
		
		init();
	}

	private void init() {
		try {
			RASManager rasManager = Framework.getService(RASManager.class);
			SortingManager sortingManager = Framework.getService(SortingManager.class);
			
			List<Port> ports = rasManager.getPortsByEquipment(Env.getOrgRrn(), equipment.getEquipmentId());
			portRefTableField.setInput(ports);
			carrierTextField.setText(sortingJob.getToDurableId());
			
			List<LotSortingJob> sortingJobs = sortingManager.getSortingJobByBatch(Env.getOrgRrn(), sortingJob.getBatchId());
			//过滤已完成的Sorter任务
			sortingJobs = sortingJobs.stream().filter(s -> !LotSortingJob.STATE_DONE.equals(s.getJobState())).collect(Collectors.toList());
			for(LotSortingJob sortingJob : sortingJobs) {
				sortingJob.setLotRrn(equipment.getObjectRrn());
				tableManagerField.getTableManager().addEditorObjects(sortingJob);;
			}
			tableManagerField.getTableManager().setInput(sortingJobs);
			sortingJobs.stream().forEach(s -> {
				tableManagerField.getTableManager().setCheckedObject(s);
			});
			CheckBoxFixEditorTableManager boxFixEditorTableManager = (CheckBoxFixEditorTableManager) tableManagerField.getTableManager().getTableManager();
			boxFixEditorTableManager.addICheckChangedListener(new ICheckChangedListener() {
				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if(!checked) {
						eventObjects.stream().forEach(e -> {
							tableManagerField.getTableManager().setCheckedObject(e);
						});
					}
				}
			});
			registerConfigAttribute(tableManagerField.getTableManager().getNatTable().getConfigRegistry(), 
					tableManagerField.getTableManager().getADTable().getFields(), sortingJobs, ports);
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
	}
	
	private void registerConfigAttribute(IConfigRegistry configRegistry, List<ADField> adFields, List<LotSortingJob> sortingJobs, List<Port> ports) {
		List<String> portIds = ports.stream().map(Port::getPortId).collect(Collectors.toList());
		int i = 0;
		for(ADField adField : adFields) {
			i++;
			if("toPortId".equals(adField.getName())) {
				break;
			}
		}
		
		for(int j = 0; j<sortingJobs.size(); j++) {
			String lable = "CELL_" + j + "_" + i;
			configRegistry.unregisterConfigAttribute(EditConfigAttributes.CELL_EDITOR, DisplayMode.EDIT, lable);
			configRegistry.registerConfigAttribute(EditConfigAttributes.CELL_EDITOR, new ComboBoxCellEditor(portIds), DisplayMode.EDIT, lable);
		}
	}
	

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton ok = null;
		if(ACTION_TYPE_CHANGE.equals(actionType)) {
			ok = createSquareButton(parent, IDialogConstants.OK_ID,
					"Change", false, null);
		} else {
			ok = createSquareButton(parent, IDialogConstants.OK_ID,
					"Reserve", false, null);
		}
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
	}
	
	@Override
	protected void okPressed() {
		try {
			SortingManager sortingManager = Framework.getService(SortingManager.class);
			Map<String, String> fromPortMap = new HashMap<String, String>();
			Map<String, String> toPortMap = new HashMap<String, String>();
			List<LotSortingJob> sortingJobs = (List<LotSortingJob>) tableManagerField.getTableManager().getInput();
			String targetPort = (String) portRefTableField.getValue();
			for(LotSortingJob job : sortingJobs) {
				if(StringUtil.isEmpty(job.getFromPortId()) || StringUtil.isEmpty(targetPort)) {
					UI.showError(Message.getString("wip.lot_sorting_job_select_port"));
	                return;
				}
				
				if(Objects.equal(job.getFromPortId(), targetPort)) {
					UI.showError(Message.getString("wip.sorting_port_same"));
	                return;
				}
				fromPortMap.put(job.getLotId(), job.getFromPortId());
				toPortMap.put(job.getLotId(), targetPort);
			}
			List<LotSortingJob> sortingJobList = new ArrayList<LotSortingJob>();
			sortingJobList.addAll(sortingJobs);
			sortingManager.reserveSortingJobs(sortingJobList, equipment.getEquipmentId(), fromPortMap, toPortMap, Env.getSessionContext());
    		
			if(ACTION_TYPE_CHANGE.equals(actionType)) {
				UI.showInfo(Message.getString("common.setport_success"));
			} else {
				UI.showInfo(Message.getString("common.reserve_success"));
			}
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
		super.okPressed();
	}
}
