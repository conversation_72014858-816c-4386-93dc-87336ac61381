package com.glory.mes.wip.lot.run.extensionpoints;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

import com.glory.framework.core.util.DBUtil;
import com.google.common.collect.Maps;

/**
 */
public class OperationActionExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(OperationActionExtensionPoint.class);
	
	private static final OperationActionExtensionPoint instance = new OperationActionExtensionPoint();
	
	/**
	 * PREPAREDIALOG:主界面点击Prepare按钮,弹出PrepareDialog前触发
	 * PREPARECREATE:在PrepareDialog界面,选择批次点击Prepare按钮,创建PrepareJob时触发
	 */
	public static final String CHECK_POINT_PREPARE_DIALOG = "PREPAREDIALOG";
	public static final String CHECK_POINT_PREPARE_CREATE = "PREPARECREATE";
	public static final String CHECK_POINT_PREPARE_REMOVE = "PREPAREREMOVE";
	
	public static final String CHECK_POINT_MULTI_CHAMBER_PREPARE_CREATE = "MULTICHAMBERPREPARECREATE";
	public static final String CHECK_POINT_FURNACE_PREPARE_CREATE = "FURNACEPREPARECREATE";
	public static final String CHECK_POINT_PREPARE_CHANGE = "PREPARECHANGE";

	private static Map<String, List<ILotOperationAction>> operationAction = Maps.newHashMap();
	private static List<String> customerActions = Lists.newArrayList();
	
    public final static String X_POINT = "com.glory.mes.wip.operation.action";
    public final static String A_CHECKPOINT = "checkpoint";
    public final static String A_CLASS = "class";
    public final static String A_IS_CUSTOMER = "isCustomer";
    
    public static OperationActionExtensionPoint getInstance() {
    	return instance;
    }
    
	public static List<ILotOperationAction> getTrackInChecks(String checkPoint) {
		return operationAction.get(checkPoint);
	}

	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
			for (int j = 0; j < configElements.length; j++) {
				try {
					String type = configElements[j].getAttribute(A_CHECKPOINT);
					ILotOperationAction check = (ILotOperationAction)configElements[j].createExecutableExtension(A_CLASS);
					boolean isCustomer = DBUtil.toBoolean(configElements[j].getAttribute(A_IS_CUSTOMER));
					if (!operationAction.containsKey(type) || isCustomer) {
						operationAction.put(type, Lists.newArrayList());
					}
					if (!customerActions.contains(type)) {
						if (isCustomer) {
							customerActions.add(type);
						}
						operationAction.get(type).add(check);
					}
				} catch (Exception e){
					logger.error("TrackInCheckExtensionPoint : init ", e);
				}
			}
		}			
	}
	
	public static OperationContext executeOperationAction(OperationContext context, String checkPoint) {
		List<ILotOperationAction> checks = getTrackInChecks(checkPoint);
		if (!CollectionUtils.isEmpty(checks)) {
			for (ILotOperationAction check : checks) {
				context = check.execute(context);
				if (OperationContext.FAILED_ID == context.getReturnCode()) {
					break;
				}
			}
		}
		return context;
	}
   
}
