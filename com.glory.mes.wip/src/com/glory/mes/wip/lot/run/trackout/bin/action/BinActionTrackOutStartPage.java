package com.glory.mes.wip.lot.run.trackout.bin.action;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcDataItem;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.lot.run.trackout.TrackOutStartPage;
import com.glory.mes.wip.model.Lot;

public class BinActionTrackOutStartPage extends TrackOutStartPage {
	
	public static final String ACTION_SPLIT = "SplitTrackOut";
	public static final String ACTION_SCRAP = "Scrap";
	public static final String ACTION_REWORK = "Rework";
	public static final String ACTION_NONE = "None";
	
	private static final String LOT_TABLE = "WIPBinRemainderLot";
	private static final String BIN_TABLE = "WIPBinData";
	
	protected EntityForm remainderForm;	
	protected FixEditorTableManager binActionTableManager;
	protected LotStepAttributeForm attributeForm;

	protected List<EdcBinSetLine> edcBinSetLineList = null;
	EdcData edcData = null;
	
	public BinActionTrackOutStartPage() {
		super();
	}
	
	public BinActionTrackOutStartPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}
	
	@Override
	public void createControl(Composite parent) {
		try {
			tw = (BinActionTrackOutWizard) this.getWizard();
			context = (BinActionTrackOutContext)tw.getContext();
			FormToolkit toolkit = new FormToolkit(parent.getDisplay());
			
			ScrolledForm sForm = toolkit.createScrolledForm(parent);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			Composite composite = sForm.getForm().getBody();
			composite.setLayout(new GridLayout(1, true));
			composite.setLayoutData(new GridData(GridData.FILL_BOTH));
			setControl(sForm);
			
			createControlLotInfo(composite, toolkit);
			createControlEdcBinAction(composite, toolkit);
			createControlAttribute(composite, toolkit);
						
			setTitle("TrackOut");
			setDescription(Message.getString("common.trackOut_first_title"));
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	//批次信息
	public void createControlLotInfo(Composite parent, FormToolkit toolkit) {
		Lot lot = context.getLots().get(0);		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), LOT_TABLE);

			remainderForm = new BinActionRemainderLotForm(parent, SWT.BORDER, lot, adTable.getFields(), 3, null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	//Bin列表
	public void createControlEdcBinAction(Composite parent, FormToolkit toolkit) {
		Group binActionGroup = new Group(parent, SWT.NONE);
		binActionGroup.setText(Message.getString("wip.lot_bin_action_tab"));
		binActionGroup.setBackground(new Color(null, 255, 255, 255));
		binActionGroup.setLayout(new GridLayout(1, true));
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = 180;
		binActionGroup.setLayoutData(gd);
		
		gd = new GridData(GridData.FILL_BOTH);
		Composite binActionComposite = toolkit.createComposite(binActionGroup, SWT.NONE);
		binActionComposite.setLayout(new GridLayout(1, false));
		binActionComposite.setLayoutData(gd);
		try {
			edcData = getEdcData();
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), BIN_TABLE);
			if (edcData != null) {
				EdcBinSet set = new EdcBinSet();
				set.setObjectRrn(edcData.getEdcSetRrn());
				set = (EdcBinSet) adManager.getEntity(set);
				List<ADField> fields = adTable.getFields();
				
				if (set.getIsBinActionEdit()) {
					for (ADField field : fields) {
						if ("binAction".equals(field.getName())) {
							field.setIsEditable(true);
						}
						if ("reworkTransition".equals(field.getName())) {
							field.setIsEditable(true);
						}
					}
				} else {
					for (ADField field : fields) {
						if ("binAction".equals(field.getName())) {
							field.setIsEditable(false);
						}
						if ("reworkTransition".equals(field.getName())) {
							field.setIsEditable(false);
						}
					}
				}
			}
			
			binActionTableManager = new FixEditorTableManager(adTable);
			binActionTableManager.newViewer(binActionComposite);
	
			List<EdcDataItem> edcDataItems = new ArrayList<EdcDataItem>();
			
			edcBinSetLineList = adManager.getEntityList(Env.getOrgRrn(), EdcBinSetLine.class, Env.getMaxResult(), 
					" edcSetRrn = " + edcData.getEdcSetRrn(), "");
			String[] dcNames = edcData.getDcName().split(";");
			String[] dcDatas = edcData.getDcData().split(";", -1);
			
			for (int i = 0; i < dcNames.length; i++) {
				for (EdcBinSetLine edcBinSetLine : edcBinSetLineList) {
					if (dcNames[i].equals(edcBinSetLine.getName())) {
						if (EdcBinSetLine.BINTYPE_PASS.equals(edcBinSetLine.getBinType()) || 
								EdcBinSetLine.BINTYPE_FAIL.equals(edcBinSetLine.getBinType())){
							String data = dcDatas[i];
							if (data != null && data.trim().length() > 0) {
								BigDecimal dataQty = new BigDecimal(data);
								if (dataQty.compareTo(BigDecimal.ZERO) != 0) { 
									EdcDataItem edcDataItem = new EdcDataItem();
									edcDataItem.setName(edcBinSetLine.getName());
									edcDataItem.setDescription(edcBinSetLine.getDescription());
									edcDataItem.setValue(data);
									
									edcDataItem.setBinAction(edcBinSetLine.getBinAction());	
									if (ACTION_REWORK.equals(edcBinSetLine.getBinAction())) {
										edcDataItem.setReworkTransition(edcBinSetLine.getReworkTransition());
									} else {
										edcDataItem.setReworkTransition(null);
									}
									edcDataItem.setActionParam1(edcBinSetLine.getActionParam1());////Bin上面的等级
									edcDataItem.setHoldLot(false);
									edcDataItems.add(edcDataItem);
								}
							}
						}
						break;
					}					
				}
			}
			binActionTableManager.setInput(edcDataItems);
			binActionTableManager.refresh();
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
    //批次属性信息
  	public void createControlAttribute(Composite parent, FormToolkit toolkit) {
   		try {
  			Long stepRrn = context.getStep().getObjectRrn();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			List<StepAttribute> stepAttributes = prdManager.getStepAttribute(stepRrn, StepAttribute.CATEGORY_TRACKOUT);
			if (stepAttributes != null && stepAttributes.size() > 0){
				Group attribute = new Group(parent, SWT.NONE);
				attribute.setText(Message.getString("common.attribute"));
				attribute.setLayout(new GridLayout(1, true));
				attribute.setBackground(new Color(null, 255, 255, 255));
				GridData gd = new GridData(GridData.FILL_BOTH);
				gd.heightHint = 120;
				attribute.setLayoutData(gd);
								
				Equipment eqp = null;
				if (context.getSelectEquipments() != null && context.getSelectEquipments().size() > 0) {
					eqp = context.getSelectEquipments().get(0);
				}
				attributeForm = new LotStepAttributeForm(attribute, SWT.NONE , null, 
						context.getLots().get(0), stepAttributes, eqp);
				attributeForm.createForm();
				attributeForm.setLayoutData(gd);
			}
  		} catch (Exception e) {
  			ExceptionHandlerManager.asyncHandleException(e);
  		}
  	}
	
  	public void setContextEdcDataItem(Lot lot) throws Exception {	
  		LotManager lotManager = Framework.getService(LotManager.class);
  		lot = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
  		
  		//再处理BIN中要求分批的批次
  		if (binActionTableManager != null) {
  			List<EdcDataItem> edcDataItems = (List<EdcDataItem>) binActionTableManager.getInput();
  			if (edcDataItems != null) {
  				for(EdcDataItem edcDataItem : edcDataItems) {
  					edcDataItem.setParentLotId(lot.getLotId());
  					if (edcDataItem.getValue() != null && edcDataItem.getValue().trim().length() != 0) {	
  						for (EdcBinSetLine edcBinSetLine : edcBinSetLineList) {
  							if (edcDataItem.getName().equals(edcBinSetLine.getName())) {	
  								edcDataItem.setHoldCode(edcBinSetLine.getHoldCode());
  								edcDataItem.setHoldReason(edcBinSetLine.getHoldReason());
  								edcDataItem.setHoldOwner(edcBinSetLine.getHoldOwner());
  								String lsl = edcBinSetLine.getLslString() == null ? "" : edcBinSetLine.getLslString();
								String usl = edcBinSetLine.getUslString() == null ? "" : edcBinSetLine.getUslString();
								BigDecimal checkQty =  new BigDecimal(edcDataItem.getValue());
								BigDecimal totalQty = edcData.getTotalQty();
								BigDecimal yield = checkQty.divide(totalQty, 5, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
								edcDataItem.setHoldComment("Type:" + edcBinSetLine.getBinType()
										+ "; Name:"+ edcBinSetLine.getName() + "; LSL/USL:" + lsl + "/" +  usl
										+ "; Total/Quantity:" + edcData.getTotalQty() + "/" + checkQty + "; Yield:" + yield);
  								break;
  							}
  						}
  					} 
  				}
  				List<EdcDataItem> newEdcDataItems = new ArrayList<EdcDataItem>();
  				newEdcDataItems.addAll(edcDataItems);
  				((BinActionTrackOutContext)context).setBinDataItems(newEdcDataItems);
  			}		
  		}
  	}	
	
	public boolean checkTotalQty(Lot lot) throws Exception {		
		BigDecimal totalQty = BigDecimal.ZERO;	
		List<EdcDataItem> edcDataItems = (List<EdcDataItem>)binActionTableManager.getInput();
		if (edcDataItems != null) {
			for (EdcDataItem edcDataItem : edcDataItems) {		
				if (edcDataItem.getValue() != null && edcDataItem.getValue().trim().length() != 0) {
					totalQty = totalQty.add(new BigDecimal(edcDataItem.getValue()));
				}			
			}
		}
				
		if (lot.getMainQty().compareTo(totalQty) < 0) {
			UI.showError(Message.getString("wip.revert_error_qty"));
			return false;
		}
		
		return true;
	}
	
	@Override
	public String doNext() {
		try {
			if (!validate()) {
	 			return "";
	 		}
			if (attributeForm != null) {
	 			//保存Attribute信息
				context.setLotAttributeValues(attributeForm.getAttributeValues());	
			}
			
			List<Lot> outLots = new ArrayList<Lot>();
			outLots.addAll(context.getLots());
		
			Lot outlot = outLots.get(0);
			//出站操作：报废分批、合批、返工
			if (outlot != null && outlot.getObjectRrn() != null) {
				setContextEdcDataItem(outlot);
				
				context.setOutLots(outLots);
				
				if (checkTotalQty(outlot)) {
					tw.invokeTrackOut();
					return getDefaultDirect();
				}
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}
	
	@Override
	public boolean validate() {
		if (attributeForm != null) {
 			//检查Attribute信息
			if (!attributeForm.saveToObject()) {
				return false;
			}					
		}
		
		List<EdcDataItem> edcDataItems = (List<EdcDataItem>) binActionTableManager.getInput();
		if (edcDataItems != null) {
			for (EdcDataItem edcDataItem : edcDataItems) {
				if (ACTION_SCRAP.equals(edcDataItem.getBinAction())) {
					 if (edcDataItem.isHoldLot()) {
						setErrorMessage(Message.getString("wip.lot_scrap_not_allow_hold"));
						return false;
					 }
				 }
				for (EdcBinSetLine edcBinSetLine : edcBinSetLineList) {
					if (edcDataItem.getName().equals(edcBinSetLine.getName())) {	
						if (edcDataItem.isHoldLot()) {
							if (!edcBinSetLine.getIsHoldLot()) {
								setErrorMessage(Message.getString("wip.lot_bin_not_allow_hold"));
								return false;
							}
						}
					}
				}	
			}
		}
		return true;
	}
	
	// 最新的EdcData
	public EdcData getEdcData() {
		try {
			Lot outlot = context.getLots().get(0);
			ADManager adManager = Framework.getService(ADManager.class);
			List<EdcData> edcDataList = adManager.getEntityList(Env.getOrgRrn(), EdcData.class, Env.getMaxResult(),
					" lotRrn = " + outlot.getObjectRrn() + " AND stepName = '"+ outlot.getStepName() + "' and isRetest = 'N' ", " updated desc ");
			if (edcDataList != null && edcDataList.size() > 0) {
				EdcData edcData = edcDataList.get(0);
				return edcData;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

}