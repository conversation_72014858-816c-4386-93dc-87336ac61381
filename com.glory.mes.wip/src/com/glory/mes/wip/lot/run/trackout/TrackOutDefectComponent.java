package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.entitymanager.forms.ScorllFormComposite;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.defect.DefectComponentUnitForm;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class TrackOutDefectComponent extends ScorllFormComposite {

	TrackOutContext context;
	
	public TrackOutDefectComponent(Composite parent, List<Lot> scrapLots, TrackOutContext context) {
		super(parent, scrapLots);
		this.context = context;
	}
	
	public Composite createUnit(Composite com, Object obj) {
		Group group = new Group(com, SWT.NONE);
		GridLayout gd = new GridLayout(1, true);
		gd.numColumns = 1;
		gd.marginRight = 1;
		gd.marginLeft = 1;
		group.setLayout(gd);
		group.setLayoutData(new GridData(GridData.FILL_BOTH));
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		Lot lot = (Lot) obj;
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}
		
		DefectComponentUnitForm form = new DefectComponentUnitForm(group, SWT.NONE, lot, context.getStep().getDefectCodeSrc());
		return form;
	}
	
//	public boolean validate() {
//		for (Composite unit : units) {
//			DefectComponentUnitForm scrapUnit = (DefectComponentUnitForm)unit;
//			if (!scrapUnit.validate()) {
//				return false;
//			}
//		}
//		return true;
//	}
	
	public List<LotAction> getDefectLotActions() {
		List<LotAction> defectLotAction = new ArrayList<LotAction>();		
		for (Composite unit : units) {
			DefectComponentUnitForm defectUnit = (DefectComponentUnitForm)unit;		
			Map<String, List<ProcessUnit>> defectActionMap = new HashMap<String, List<ProcessUnit>>();
			for (ProcessUnit processUnit : defectUnit.getDefectUnits()) {								
				ComponentUnit componentUnit = (ComponentUnit)processUnit;
				if (defectActionMap.containsKey(componentUnit.getActionCode())) {
					List<ProcessUnit> list = defectActionMap.get(componentUnit.getActionCode());
					list.add(componentUnit);
					defectActionMap.put(componentUnit.getActionCode(), list);
				} else {
					List<ProcessUnit> list = new ArrayList<ProcessUnit>();
					list.add(componentUnit);
					defectActionMap.put(componentUnit.getActionCode(), list);
				}
			}	
			
			for (String key : defectActionMap.keySet()) {
				//DedectCode动作
				LotAction lotDefectAction = new LotAction();				
				lotDefectAction.setActionType(LotAction.ACTIONTYPE_DEFECT);
				lotDefectAction.setActionCode(key);				
				lotDefectAction.setActionUnits(defectActionMap.get(key));
				lotDefectAction.setLotRrn(defectUnit.getLot().getObjectRrn());
				List<ProcessUnit> processUnit = defectActionMap.get(key);
				ComponentUnit componentUnit = (ComponentUnit)processUnit.get(0);
				lotDefectAction.setActionCodeGroup(defectUnit.getActionCodeGroup(componentUnit));
				lotDefectAction.setActionCodeObject(defectUnit.getActionCodeObject(componentUnit));
				
				defectLotAction.add(lotDefectAction);
			}
		}			
		return defectLotAction;
	}
	
}
