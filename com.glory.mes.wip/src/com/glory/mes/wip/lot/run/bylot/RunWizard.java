package com.glory.mes.wip.lot.run.bylot;

import org.apache.log4j.Logger;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.GlcFlowWizard;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.prd.model.Step;

public class RunWizard extends GlcFlowWizard {

	private static final Logger logger = Logger.getLogger(RunWizard.class);

	protected RunWizardContext context;
	
	public RunWizard() {	
	}
	
	public RunWizard(RunWizardContext context) {
		super();
		this.category = context.getCategory();
		this.context = context;
	}

	@Override
	public boolean performFinish() {
		return false;
	}

	public void setContext(RunWizardContext context) {
		this.context = context;
	}

	public RunWizardContext getContext() {
		return context;
	}
	
	public boolean validateStepCategory() {
		if (context.getStep() != null && !StringUtil.isEmpty(context.getStep().getUseCategory())) {
			if (Step.USE_CATEGORY_PROCESS.equals(context.getStep().getUseCategory())
					|| Step.USE_CATEGORY_MEASURE.equals(context.getStep().getUseCategory())
					|| Step.USE_CATEGORY_BOTH.equals(context.getStep().getUseCategory())) {
				//标准UseCategory,不处理
				return true;
			} else {
				if (!context.getStep().getUseCategory().equals(context.getUseCategory())) {
					//特殊UseCategory,必须校验传值是否一致
					//提示(特殊类别作业必须使用特定功能)
					if (Step.USE_CATEGORY_EXCHANGESORT.equals(context.getStep().getUseCategory())) {
						UI.showError(Message.getString("wip.please_create_sorting_job_and_execute"));
					} else if (Step.USE_CATEGORY_NPWM.equals(context.getStep().getUseCategory())) {
						UI.showError(Message.getString("wip.please_prepare_for_npw_split_first"));
					} else {
						UI.showError(Message.getString("wip.track_special_category"));
					}	
					return false;
				}
			}
		} 
		return true;
	}

}
