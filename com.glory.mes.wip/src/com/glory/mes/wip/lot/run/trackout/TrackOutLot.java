package com.glory.mes.wip.lot.run.trackout;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class TrackOutLot {

	private Lot lot;

	/**
	 * 本次出站的批次数量(包括返工数量,不包括报废数量)
	 */
	private BigDecimal outMainQty;
	private BigDecimal outSubQty;

	/**
	 * 本次出站的返工数量
	 */
	private BigDecimal reworkMainQty;
	private BigDecimal reworkSubQty;

	/**
	 * 本次出站的报废数量
	 */
	private BigDecimal scrapMainQty;
	private BigDecimal scrapSubQty;

	/**
	 * 出站时需要绑定的载具ID
	 */
	private String durable;

	private String operator1;
	private String operator2;
	private String comment;

	/**
	 * 本次出站的只保存到批次历史表的信息
	 */
	private String teamId;
	private Boolean isRework = false;

	public TrackOutLot(Lot lot) {
		this.lot = lot;
		List<ProcessUnit> subUnits = lot.getSubProcessUnit();
		if (CollectionUtils.isNotEmpty(subUnits)) {
			BigDecimal outMainQty = subUnits.stream().map(ProcessUnit::getMainQty).filter(b -> b != null)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
			this.outMainQty = outMainQty;
		}
	}

	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public String getLotId() {
		return lot.getLotId();
	}

	public String getPartName() {
		return lot.getPartName();
	}

	public String getPartId() {
		return lot.getPartId();
	}

	public String getPartDesc() {
		return lot.getPartDesc();
	}

	public String getCustomerCode() {
		return lot.getCustomerCode();
	}

	public String getCustomerPartId() {
		return lot.getCustomerPartId();
	}

	public String getStepName() {
		return lot.getStepName();
	}

	public String getStepId() {
		return lot.getStepId();
	}

	public String getStepDesc() {
		return lot.getStepDesc();
	}

	public BigDecimal getMainQty() {
		return lot.getMainQty();
	}

	public BigDecimal getSubQty() {
		return lot.getSubQty();
	}

	public String getEquipmentId() {
		return lot.getEquipmentId();
	}

	public String getState() {
		return lot.getState();
	}

	public String getHoldState() {
		return lot.getHoldState();
	}

	public BigDecimal getOutMainQty() {
		return outMainQty;
	}

	public void setOutMainQty(BigDecimal outMainQty) {
		this.outMainQty = outMainQty;
	}

	public BigDecimal getOutSubQty() {
		return outSubQty;
	}

	public void setOutSubQty(BigDecimal outSubQty) {
		this.outSubQty = outSubQty;
	}

	public BigDecimal getReworkMainQty() {
		return reworkMainQty;
	}

	public void setReworkMainQty(BigDecimal reworkMainQty) {
		this.reworkMainQty = reworkMainQty;
	}

	public BigDecimal getReworkSubQty() {
		return reworkSubQty;
	}

	public void setReworkSubQty(BigDecimal reworkSubQty) {
		this.reworkSubQty = reworkSubQty;
	}

	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}

	public String getOperator2() {
		return operator2;
	}

	public void setOperator2(String operator2) {
		this.operator2 = operator2;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getTeamId() {
		return teamId;
	}

	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public void setIsRework(Boolean isRework) {
		this.isRework = isRework;
	}

	public Boolean getIsRework() {
		return isRework;
	}

	public BigDecimal getScrapMainQty() {
		return scrapMainQty;
	}

	public void setScrapMainQty(BigDecimal scrapMainQty) {
		this.scrapMainQty = scrapMainQty;
	}

	public BigDecimal getScrapSubQty() {
		return scrapSubQty;
	}

	public void setScrapSubQty(BigDecimal scrapSubQty) {
		this.scrapSubQty = scrapSubQty;
	}

	public String getDurable() {
		return durable;
	}

	public void setDurable(String durable) {
		this.durable = durable;
	}

}
