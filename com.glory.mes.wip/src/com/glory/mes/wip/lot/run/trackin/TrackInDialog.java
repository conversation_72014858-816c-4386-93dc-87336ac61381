package com.glory.mes.wip.lot.run.trackin;

import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardDialog;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.mes.wip.lot.run.bylot.RunWizard;

public class TrackInDialog extends FlowWizardDialog {
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 480;

	public TrackInDialog(Shell parentShell, FlowWizard newWizard) {
		super(parentShell, newWizard);
	}

	@Override
	public int open() {
		RunWizard wizard = (RunWizard)getWizard(); 
		if (wizard.validateStepCategory()) {
			return super.open();
		} else {
			setReturnCode(CANCEL);
			close();
			return this.getReturnCode();
		}
	}
	
	@Override
	public void showPage(IWizardPage page) {
		super.showPage(page);
		FlowWizardPage fPage = ((FlowWizardPage) page);
		fPage.refresh();
	}
	
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("trackin-dialog"));
		return super.buildView(parent);
	}

	protected void updateSizeForPage(IWizardPage page) {

	}

	/*
	 * 取消将焦点默认的放在Next Button上
	 */
	public void updateButtons() {
		boolean canFlipToNextPage = false;
		if (currentPage != null) {
			if (backButton != null) {
				backButton.setEnabled(getCurrentPage().getPreviousPage() != null);
			}
			if (nextButton != null) {
				canFlipToNextPage = getCurrentPage().canFlipToNextPage();
				nextButton.setEnabled(canFlipToNextPage);
			}
		}
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

//	@Override
//	protected void setShellStyle(int newShellStyle) {
//		super.setShellStyle(SWT.CLOSE);
//	}
	
	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.APPLICATION_MODAL | getDefaultOrientation());
	}
}
