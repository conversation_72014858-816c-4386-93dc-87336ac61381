package com.glory.mes.wip.lot.run.trackin.combineeqp;

import java.util.List;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.eqp.CapaSub;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.subcapa.SelectEquipmentSubCapaPage;

/**
 * 在选择设备的基础上,根据子能力和绑定关系来带出子设备
 * 子能力可设定为必须或可选
 * 如果是必须,则必须选择对应的子设备
 * 如果是可选,则子设备不是必须选择
 */
public class SelectCombineEquipmentPage extends SelectEquipmentSubCapaPage {
	
	protected void createContent(Composite composite, List<Equipment> availableEqps, List<Equipment> selectedEqps) {
		super.createContent(composite, availableEqps, selectedEqps);
		
		selectEqpTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				Object selectObj = event.getSelection();		
				if (selectObj != null) {
					Equipment selectEquipment = (Equipment) ((StructuredSelection) selectObj).getFirstElement();
					
					try {	
						((SubCapaCombineEquipmentForm)subCapaform).setUnitEquipment(selectEquipment);
					} catch (Exception e) {
						e.printStackTrace();
					}
					
					canFlipNextPage = true;
				}
				((RunWizard)getWizard()).getDialog().updateButtons();
			}
		});
	}
	
	protected List<Equipment> createSubCapaEquipment(Composite composite, List<Equipment> availableEqps) {						
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			ADManager adManager = Framework.getService(ADManager.class);
			//获得子能力
			List<CapaSub> subCapas = adManager.getEntityList(Env.getOrgRrn(), CapaSub.class, Env.getMaxResult(), " capaRrn = " + context.getStep().getCapability(), "");
			
			if (subCapas != null && subCapas.size() > 0) {
				Composite subParent = toolkit.createComposite(composite, SWT.NONE);
				subParent.setLayout(new GridLayout(1, true));
				subParent.setLayoutData(new GridData(GridData.FILL_BOTH));
				
				Group subTabGroup = new Group(subParent, SWT.NONE);
				subTabGroup.setText(Message.getString("wip.trackin_eqp_list"));
				subTabGroup.setBackground(new Color(null, 255, 255, 255));
				subTabGroup.setLayout(new GridLayout(subCapas.size(), true));
				subTabGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
				
				subCapaform = new SubCapaCombineEquipmentForm(subTabGroup, subCapas);		
				subCapaform.createContent();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}	
		return availableEqps;
	}
	
}
