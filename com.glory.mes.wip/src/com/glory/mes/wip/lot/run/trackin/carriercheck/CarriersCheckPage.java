package com.glory.mes.wip.lot.run.trackin.carriercheck;

import java.util.List;
import java.util.Objects;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;

/**
 * 进站的向导界面 用来确认上一步出站的载具ID（必须所有载具到齐）
 * 
 * <AUTHOR> He
 * 
 */
public class CarriersCheckPage extends FlowWizardPage {

	protected static final String TABLE_NAME = "LotMulitCarrierCheck";

	protected Text txt;
	protected Lot lot;
	protected FormToolkit toolkit;
	protected ListTableManager tableManager;
	
	private boolean canFlipNextPage = false;

	@Override
	public void createControl(Composite parent) {
		TrackInWizard tw = (TrackInWizard) this.getWizard();
		TrackInContext context = (TrackInContext)tw.getContext();
		lot = context.getLots().get(0);
		
		List<LotMultiCarrier> lotCarriers = getLotMulitCarriers();

		// 若没有绑定记录，跳过本页
		if (lotCarriers == null || lotCarriers.isEmpty()) {
			FlowWizard tiWizard = (FlowWizard) this.getWizard();
			tiWizard.getDialog().skipPressed();
			return;
		}
		
		toolkit = new FormToolkit(Display.getCurrent());
		Composite composite = new Composite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
		composite.setLayoutData(gd);

		createContent(composite, lotCarriers);
		setControl(composite);
		setPageTitle();

	}

	private List<LotMultiCarrier> getLotMulitCarriers() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			String whereClause = " lotRrn = " + lot.getObjectRrn();
			return adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class, Integer.MAX_VALUE, whereClause, null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	private LotMultiCarrier getLotMultiCarrier(String carrierId) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			String whereClause = " lotRrn = " + lot.getObjectRrn() + " AND carrierId = '" + carrierId + "'";
			List<LotMultiCarrier> lotCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class, Integer.MAX_VALUE,
					whereClause, null);

			if (lotCarriers != null && !lotCarriers.isEmpty()) {
				return lotCarriers.get(0);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	protected void setPageTitle() {
		setTitle(Message.getString("assembly.trackin_carrier_check"));
		setMessage(Message.getString("assembly.trackin_carrier_check_detail"));
	}

	protected void createContent(Composite composite, List<LotMultiCarrier> lotCarriers) {
		Composite parent = toolkit.createComposite(composite, SWT.NONE);
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite input = toolkit.createComposite(composite, SWT.NONE);
		input.setLayout(new GridLayout(3, false));
		input.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		Label label = toolkit.createLabel(input, Message.getString("assembly.track_carrier_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		
		txt = toolkit.createText(input, "", SWT.BORDER);
		GridData gText = new GridData();
		gText.widthHint = 216;
		txt.setLayoutData(gText);
		txt.setTextLimit(32);
		txt.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		
		final int size = lotCarriers.size();
		txt.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String id = tLotId.getText();
					LotMultiCarrier lotCarrier = getLotMultiCarrier(id);
					if (Objects.isNull(lotCarrier)) {
						UI.showError(Message.getString("assembly.trackin_wrong_carrier"));
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
					} else {
						tableManager.setCheckedObject(lotCarrier);
						
						// 所有载具ID都检查成功才能去到下一步
						int checkedObjectsSize = tableManager.getCheckedObject().size();
						if (checkedObjectsSize == size) {
							canFlipNextPage = true;
						}
						((RunWizard) getWizard()).getDialog().updateButtons();
						tLotId.selectAll();
					}
					refresh();
					break;
				}
			}
		});

		Group tabGroup = new Group(parent, SWT.NONE);
		tabGroup.setText(Message.getString("assembly.trackin_cc_list"));
		tabGroup.setBackground(new Color(null, 255, 255, 255));
		tabGroup.setLayout(new GridLayout(1, true));
		tabGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
		tabGroup.setEnabled(false);

		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListTableManager(adTable, true);
			tableManager.newViewer(tabGroup);
			tableManager.setInput(lotCarriers);
			tableManager.getNatTable().setLayoutData(new GridData(GridData.FILL_BOTH));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public String doNext() {
		return getDefaultDirect();
	}

	@Override
	public boolean canFlipToNextPage() {
		return canFlipNextPage;
	}

}
