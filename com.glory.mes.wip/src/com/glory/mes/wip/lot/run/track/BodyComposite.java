package com.glory.mes.wip.lot.run.track;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackEditorConfig;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackFormConfig;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackFormExtensionPoint;
import com.glory.mes.wip.lot.run.track.forms.ConsoleLogForm;

public class BodyComposite extends Composite {

	private TrackEditorConfig config;
	private IEventBroker broker;
	private FFormToolKit toolKit;
	private String fontName = "微软雅黑";
	private int fontHight = 15;
	
	public BodyComposite(Composite parent, TrackEditorConfig config, IEventBroker broker, int style) {
		super(parent, style);
		this.config = config;
		this.broker = broker;
		createForm();
	}

	public BodyComposite(Composite parent, TrackEditorConfig config, IEventBroker broker, int style,
			FFormToolKit toolkit) {
		super(parent, style);
		this.config = config;
		this.broker = broker;
		this.toolKit = toolkit;
		createForm();
	}

	public void createForm() {
		GridLayout layout = new GridLayout(config.getBodyYGrid(), false);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		
		// 获取当前屏幕的分辨率
		Rectangle area = Display.getDefault().getClientArea();
		int resolution = area.height;
		
		ConsoleLogForm logForm = null;
		
		List<ITrackForm> forms = new ArrayList<ITrackForm>();
		for (TrackFormConfig formConfig : config.getBodyFormConfigs()) {
			ITrackForm trackForm = TrackFormExtensionPoint.getTrackForm(formConfig.getId());
			if (trackForm != null) {
				trackForm.setBroker(broker);
				trackForm.setToolKit(toolKit);

				Composite component = trackForm.createForm(this);
				GridData gd = (GridData) component.getLayoutData();
				gd.horizontalSpan = formConfig.getColSpan();
				gd.verticalSpan = formConfig.getRowSpan();
				if (formConfig.getHeightHint() != null) {
					gd.heightHint = resolution * formConfig.getHeightHint() / 1000;
				}
				if (formConfig.getWidthHint() != null) {
					gd.widthHint = resolution * formConfig.getWidthHint() / 1000;
				}
				
				if (formConfig.getFontName() != null) {
					fontName = formConfig.getFontName();
				}
				if (formConfig.getFontHight() != null) {
					fontHight = resolution * 2 / 100;
				}
				trackForm.setFieldFont(fontName, fontHight);
				component.setLayoutData(gd);
				
				forms.add(trackForm);
				
				if (trackForm instanceof ConsoleLogForm) {
					logForm = (ConsoleLogForm) trackForm;
				}
			}
		}
		
		for (ITrackForm form : forms) {
			form.load();
		}
		
		for (ITrackForm form : forms) {
			if (logForm != null) {
				if (form instanceof EntityTrackFrom) {
					EntityTrackFrom trackFrom = (EntityTrackFrom) form;
					logForm.subscribe(trackFrom.getConsoleTopicName());
				}
			}
		}
	}
		
	
}
