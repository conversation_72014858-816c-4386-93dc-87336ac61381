package com.glory.mes.wip.lot.run.track.forms;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;
import com.google.common.collect.Maps;

public class LotTrackForm extends AbstractTrackForm {

	protected final static String FIELD_LOT = "Lot";
	protected final static String FIELD_EQP = "Eqp";
	protected final static String FIELD_TEST = "Test";
	
	protected final static String BTN_RESET = "Reset";
	protected final static String BTN_MOVE = "Move";
	protected final static String BTN_TEST = "Test";

	public Equipment equipment;
	public Lot lot;
	
	@Override
	public Map<String, String> getFieldIds() {
		Map<String, String> fieldIds = Maps.newLinkedHashMap();
		fieldIds.put(FIELD_EQP, "当站设备");
		fieldIds.put(FIELD_LOT, "批号");
		fieldIds.put(FIELD_TEST, "目标工步");
		return fieldIds;
	}

	@Override
	public Map<String, String> getButtonIds() {
		Map<String, String> fieldIds = Maps.newLinkedHashMap();
		fieldIds.put(BTN_MOVE, "过站");
		fieldIds.put(BTN_RESET, "重置");
		fieldIds.put(BTN_TEST, "测试");
		return fieldIds;
	}

	@Override
	public void addBtnEventListener() {
		buttonMap.get(BTN_MOVE).addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtLotCREvent((Text) getField(FIELD_LOT).getControls()[1]);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});


		buttonMap.get(BTN_RESET).addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				refresh();
			}
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});
		
	}

	@Override
	public void addFieldEventListener() {
		((TextField) getField(FIELD_EQP)).getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tEqoId = ((Text) event.widget);
				tEqoId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					txtEqpCREvent(tEqoId);
					break;
				}
			}

		});

		((TextField) getField(FIELD_LOT)).getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					txtLotCREvent(tLotId);
					break;
				}
			}
		});
	}

	/**
	 * 设备ID的回车事件处理方法
	 * 
	 * @param text
	 */
	protected void txtEqpCREvent(Text text) {
		mmg.removeAllMessages();
		String txtEqp = text.getText();
		try {
			equipment = null;
			RASManager rasManager = Framework.getService(RASManager.class);
			equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), txtEqp);

		} catch (Exception e) {
			mmg.addMessage("Eqp", e.getMessage(), null, MsgType.MSG_ERROR.getIndex(),
					getField(FIELD_EQP).getControls()[getField(FIELD_EQP).getControls().length - 1]);
			broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
		}
		if (equipment == null) {
			text.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
		}
		if (equipment != null && equipment.getObjectRrn() != null) {
			getField(FIELD_EQP).setValue(equipment.getEquipmentId());
			getField(FIELD_EQP).setEnabled(false);
			((TextField) getField(FIELD_LOT)).getTextControl().setFocus();
		}
	}

	/**
	 * 批次ID的回车事件处理方法
	 * 
	 * @param text
	 */
	protected void txtLotCREvent(Text text) {
		mmg.removeAllMessages();
		String txtLotId = text.getText();
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), txtLotId);
			if (lot != null && lot.getObjectRrn() != null) {
				this.lot = lot;
				passAdapter();
			} else {
				text.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
		} catch (Exception e) {
			mmg.addMessage("Lot", e.getMessage(), null, MsgType.MSG_ERROR.getIndex(),
					getField(FIELD_LOT).getControls()[getField(FIELD_LOT).getControls().length - 1]);
			broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
		}
	}

	/**
	 * 过站前的校验
	 * 
	 * @return
	 */
	public boolean validate() {
		if (equipment == null) {
			return false;
		}
		if (lot == null) {
			((TextField) getField(FIELD_LOT)).getTextControl().setText("");
			return false;
		}
		return true;
	}

	/**
	 * 过站事件
	 */
	protected void passAdapter() {
		if (validate()) {
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				InContext inContext = new InContext();
				List<Lot> lots = new ArrayList<>();
				lot.setEquipmentId(equipment.getEquipmentId());
				lots.add(lot);
				List<Equipment> equipments = new ArrayList<>();
				equipments.add(equipment);
				inContext.setLots(lots);
				inContext.setEquipments(equipments);
				inContext.setOperator1(Env.getUserName());
				/*//==添加测试
			 	String stepName = ((Text) getField(BTN_TEST).getControls()[1]).getText();
				Step step = new Step();
				ADManager adManager = Framework.getService(ADManager.class);
				List<Step> steps = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MAX_VALUE, " status = 'Active' and name = '" + stepName + "'", "");
				if(steps != null && steps.size() != 0) {
					step = steps.get(0);
				}
				inContext.setCurrentStep(step);
				lotManager.trackMoveSkipStep(inContext, Env.getSessionContext());*/
				lotManager.trackMove(inContext, Env.getSessionContext());
				
				mmg.addMessage("TrackMove", lot.getLotId() + " pass!", null, MsgType.MSG_INFORMATION.getIndex());
				broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
			} catch (Exception e) {
				mmg.addMessage("TrackMove", e.getMessage(), null, MsgType.MSG_ERROR.getIndex(),
						getField(FIELD_LOT).getControls()[getField(FIELD_LOT).getControls().length - 1]);
				broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
			}
		}
		lot = null;
		Text txtLot = ((TextField) getField(FIELD_LOT)).getTextControl();
		txtLot.setText("");
	}

	@Override
	public void refresh() {
		super.refresh();
		getField(FIELD_EQP).setEnabled(true);
		((TextField) getField(FIELD_EQP)).getTextControl().setFocus();
		if (equipment != null) {
			equipment = null;
		}
		if (lot != null) {
			lot = null;
		}
	}

}
