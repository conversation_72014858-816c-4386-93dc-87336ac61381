package com.glory.mes.wip.lot.run.track.forms;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.base.ui.custom.FMessageConsole;
import com.glory.framework.base.ui.forms.FMessage;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.mes.wip.lot.run.track.TrackForm;

public class ConsoleLogForm extends TrackForm {
	
	public static String TOPIC_NEAME = "consoleLog";
	
	protected FMessageConsole fMessageConsole = null;

	@Override
	public Composite createForm(Composite parent) {
		Group consoleGroup = new Group(parent, SWT.NONE);
		consoleGroup.setText("控制台信息");
		consoleGroup.setFont(new Font(Display.getCurrent(), "楷体", DPIUtil.autoScaleUpUsingNativeDPI(17), SWT.BOLD));
		consoleGroup.setLayout(new GridLayout(1, false));
		consoleGroup.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		GridData gd = new GridData(SWT.FILL, SWT.FILL, true, false);
		consoleGroup.setLayoutData(gd);
		
		fMessageConsole = new FMessageConsole(consoleGroup, SWT.MULTI | SWT.READ_ONLY | SWT.BORDER | SWT.V_SCROLL);
		Font font = new Font(Display.getCurrent(), "微软雅黑", DPIUtil.autoScaleUpUsingNativeDPI(15), SWT.BOLD);
		fMessageConsole.setFont(font);
		fMessageConsole.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		if (broker != null) {
			broker.subscribe(TOPIC_NEAME, new EventHandler() {
				@Override
				public void handleEvent(Event event) {
					FMessageManager mmg = (FMessageManager) event.getProperty("org.eclipse.e4.data");
					writeConsoleMessage(mmg);
				}
	        });
		}
		return consoleGroup;
	}
	
	public void subscribe(String topicName) {
		if (broker != null) {
			broker.subscribe(topicName, new EventHandler() {
				@Override
				public void handleEvent(Event event) {
					FMessageManager mmg = (FMessageManager) event.getProperty("org.eclipse.e4.data");
					writeConsoleMessage(mmg);
				}
	        });
		}
	}
	public void writeConsoleMessage(FMessageManager fMessageManager) {
		if (fMessageConsole != null) {
			for (FMessage fMessage : fMessageManager.getMessages()) {
				fMessageConsole.put(fMessage);
			}
		}
	}
}
