package com.glory.mes.wip.lot.run.track.forms;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;
import com.glory.mes.wip.lot.run.track.TrackContext;
import com.glory.mes.wip.lot.run.track.TrackDialog;
import com.glory.mes.wip.lot.run.track.TrackForm;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class SelectEquipmentForm extends TrackForm {

	public static String TABLE_NAME = "WIPTrackEquipment";
	protected ListTableManager listTableManager = null;
	
	public SelectEquipmentForm() {}
	
	public Composite createForm(Composite parent) {		
		try {
			Group eqpGroup = new Group(parent, SWT.NONE);
			eqpGroup.setText("设备信息列表");
			eqpGroup.setLayout(new GridLayout(1, true));
			GridData gd = new GridData(GridData.FILL_BOTH);
			eqpGroup.setLayoutData(gd);
			eqpGroup.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
			
			ADManager entityManager = Framework.getService(ADManager.class);
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
//			
//			if (step != null && step.getIsMultiEqp()) {
//				listTableManager = new ListTableManager(adTable, true);				
//			} else {
//				listTableManager = new ListTableManager(adTable, false);	
//			}
			listTableManager.setIndexFlag(true);
			listTableManager.newViewer(eqpGroup);
			listTableManager.setInput(null);
			
			return eqpGroup;
		} catch (ClientException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;

	}
		
	public List<Equipment> getSelectedEquipments() {
		List<Equipment> equipments = null;
//		if (step != null && step.getIsMultiEqp()) {
//			equipments = (List)listTableManager.getCheckedObject();
//		} else {
//			Equipment selectEquipment = (Equipment) listTableManager.getSelectedObject();
//			List<Equipment> selectEquipments = new ArrayList<Equipment>();
//			selectEquipments.add(selectEquipment);
//			equipments = selectEquipments;
//		}
		return equipments;
	}
	
	public TrackContext saveToObject(TrackContext trackContext) {		
		try {
			List<Equipment> equipments = getSelectedEquipments();
			if (equipments == null || equipments.size() == 0) {
				if (trackContext.getStep().getIsRequireEqp()) {
					throw new ClientException("wip.error.must_select_eqp");
				}		
			}
			for (Equipment equipment : equipments) {
				if (!equipment.getIsAvailable()) {
					throw new ClientException(equipment.getMessage());
				}
			}
			trackContext.setSelectEquipments(equipments);
			
			if (trackContext.getStep().getIsMultiEqp()) {
				if (equipments.size() == 1) {
					Equipment equipment = equipments.get(0);
					List<Lot> lots = trackContext.getLots();
					for (Lot lot : lots) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
					return trackContext;
				} else if (equipments.size() > 1) {
					List<ProcessUnit> processUnits = new ArrayList<ProcessUnit>();
					for (Equipment e : equipments) {
						QtyUnit unit = new QtyUnit();
						unit.setEquipmentId(e.getEquipmentId());
						unit.setParentProcessUnit(trackContext.getLots().get(0));
						unit.setParentUnitRrn(trackContext.getLots().get(0).getObjectRrn());
						processUnits.add(unit);
					}
					trackContext.getLots().get(0).setSubUnitType(QtyUnit.getUnitType());
					trackContext.getLots().get(0).setSubProcessUnit(processUnits);
					return trackContext;
				}
			} else {
				Equipment equipment = equipments.get(0);
				List<Lot> lots = trackContext.getLots();
				for (Lot lot : lots) {
					lot.setEquipmentId(equipment.getEquipmentId());
				}
			}
		} catch (ClientException e) {
			throw new ClientException(e);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return trackContext;
	}
	
	public boolean validate() {
		return true;
	}
	
	//@Override
	public void lotChanged(Object sender, List<Lot> lots) {
		try {
			if (lots != null && lots.size() > 0) {
				ADManager manager = Framework.getService(ADManager.class);
				Step step = new Step();
				step.setObjectRrn(lots.get(0).getStepRrn());
				step = (Step) manager.getEntity(step);
				if (step == null || step.getCapability() == null) {
					TrackDialog dialog = (TrackDialog) sender;
					dialog.writeConsoleMessage(Message.getString("wip.step_capability_is_null"), TrackDialog.LOGGER_LEVEL_ERROR);				
				}
				
				LotManager lotManager = Framework.getService(LotManager.class);
				List<Equipment> availableEqps = lotManager.getAvailableEquipments(lots.get(0), Env.getSessionContext());	
				listTableManager.setInput(availableEqps);
				listTableManager.refresh();
			} else {
				listTableManager.setInput(null);
				listTableManager.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}			
	}
	
}
