package com.glory.mes.wip.lot.run.trackout;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutReworkPage extends FlowWizardPage {

	public static final Color GRAY = null;
	protected TrackOutContext context;
	protected TrackOutReworkComponent reworkComponent;
	protected TrackOutReworkQty reworkQty;
	
	public TrackOutReworkPage() {
		super();
	}
	
	public TrackOutReworkPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		context = (TrackOutContext)((TrackOutWizard)this.getWizard()).getContext();
	}
	
	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		if (context.getInContent().getActions() != null) {
			List<LotAction> reworkLotActions = new ArrayList<LotAction>();
			for (LotAction lotAction : context.getInContent().getActions()) {
				if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
					reworkLotActions.add(lotAction);
				}
			}
			context.getInContent().getActions().removeAll(reworkLotActions);
		}
		return super.doPrevious();
	}

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	protected boolean checkReworkQty() throws Exception {
		List<TrackOutLot> trackOutLots = context.getTrackOutLots();
		
		//返工数量2
		List<LotAction> reworkUnits;
		if (reworkComponent!= null) {
			reworkUnits = reworkComponent.getReworkLotActions();
		} else {
			reworkUnits = reworkQty.getReworkLotActions();
		}
		
		// 检查units的返工transition是否为空，为空不让下一步
		Optional<LotAction> op = reworkUnits.stream().filter(u -> u.getReworkTransition() == null).findFirst();
		if (op.isPresent()) {
			setErrorMessage(Message.getString("wip.lot_select_reworkProcedure"));
			return false;
		}
		for (TrackOutLot trackOutLot : trackOutLots) {
			BigDecimal qty = trackOutLot.getReworkMainQty();
			
			if (qty != null) {
				BigDecimal reworkMainQty = BigDecimal.ZERO;
				BigDecimal reworkSubQty = null;
				if (trackOutLot.getSubQty() != null) {
					reworkSubQty = BigDecimal.ZERO;
				}
				
				for (LotAction reworkUnit : reworkUnits) {
					if (reworkUnit.getLotRrn().equals(trackOutLot.getLot().getObjectRrn())) {
						for (ProcessUnit unit : reworkUnit.getActionUnits()) {
							//列表数量2
							reworkMainQty = reworkMainQty.add(unit.getMainQty());
							if (reworkSubQty != null) {
								reworkSubQty = reworkSubQty.add(unit.getSubQty() != null ? unit.getSubQty() : BigDecimal.ZERO);
							}
						}
					}
				}
				if (reworkMainQty.compareTo(qty) != 0) {
					setErrorMessage(trackOutLot.getLotId() + Message.getString("wip.trackout_rework_qty") + qty);
					return false;
				}
			}	
		}
		return true ;
	}

	
	@Override
	public void createControl(Composite parent) {
		setTitle(Message.getString("wip.lot_rework_operate"));	
		List<Lot> lots = context.getLots();
		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		reworkComponent = null;
		reworkQty = null;
		List<Lot> reworkLots = new ArrayList<Lot>();
		for (Lot lot : lots) {
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				if (trackOutLot.getLot().getObjectRrn().equals(lot.getObjectRrn())) {
					if (trackOutLot.getReworkMainQty() != null) {
						reworkLots.add(lot);
					}
					break;
				}
			}
		}
		if (ComponentUnit.UNIT_TYPE_COMPONENT.equals(lots.get(0).getSubUnitType())) {
			reworkComponent = new TrackOutReworkComponent(composite, reworkLots, context);
			reworkComponent.createContent();
		} else if (QtyUnit.UNIT_TYPE_QTY.equals(lots.get(0).getSubUnitType())) {
			reworkQty = new TrackOutReworkQty(composite, reworkLots, context);
			reworkQty.createContent();
		}
		setControl(composite);		
	}

	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if(ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())){
				if (!reworkComponent.validate()) {
					return "";
				}
				if(!checkReworkQty()){
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> reworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							reworkLotActions.add(lotAction);
						}
					}
					list.removeAll(reworkLotActions);
					list.addAll(reworkComponent.getReworkLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkComponent.getReworkLotActions());
				}
			} else {
				if(!checkReworkQty()){
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> reworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							reworkLotActions.add(lotAction);
						}
					}
					list.removeAll(reworkLotActions);
					list.addAll(reworkQty.getReworkLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkQty.getReworkLotActions());
				}
			}
			((TrackOutWizard)this.getWizard()).invokeTrackOut();
			return getDefaultDirect();
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}
	

}
