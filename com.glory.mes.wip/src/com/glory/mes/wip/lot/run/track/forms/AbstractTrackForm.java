package com.glory.mes.wip.lot.run.track.forms;

import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.wip.lot.run.track.EntityTrackFrom;

public abstract class AbstractTrackForm extends EntityTrackFrom {
	
	/**
	 * 获取界面上需要显示的TextBox信息map
	 * @return Map<String, String> key<栏位ID> value<栏位显示名，请使用Message.getString(key)>
	 */
	public abstract Map<String, String> getFieldIds();
	
	/**
	 * 获取界面上需要显示的Button信息map
	 * @return Map<String, String> key<按钮ID> value<按钮显示名，请使用Message.getString(key)>
	 */
	public abstract Map<String, String> getButtonIds();
	
	/**
	 * 添加按钮事件监听
	 */
	public abstract void addBtnEventListener();
	
	/**
	 * 添加栏位事件监听
	 */
	public abstract void addFieldEventListener();

	@Override
	public Composite createForm(Composite parent) {
		Composite body = toolKit.createComposite(parent);
		body.setLayout(new GridLayout(1, false));
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
		body.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));

		Composite fieldBody = toolKit.createComposite(body);
		GridLayout gridLayout = new GridLayout(2, false);
		gridLayout.marginTop = DPIUtil.autoScaleUpUsingNativeDPI(14);
		gridLayout.verticalSpacing = DPIUtil.autoScaleUpUsingNativeDPI(30);
		gridLayout.horizontalSpacing = DPIUtil.autoScaleUpUsingNativeDPI(20);
		fieldBody.setLayout(gridLayout);
		GridData gridData = new GridData(GridData.FILL_BOTH);
		gridData.horizontalAlignment = GridData.CENTER;
		gridData.verticalAlignment = GridData.CENTER;
		fieldBody.setLayoutData(gridData);
		fieldBody.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		createFields();
		createContent(fieldBody, toolKit);
		createButtons(fieldBody, toolKit);
		addFieldEventListener();
		return body;
	}
	
	protected void createButtons(Composite fieldBody, FFormToolKit toolKit) {
		Composite bodyBtn = toolKit.createComposite(fieldBody, SWT.NONE);
		GridData data = new GridData(GridData.FILL_HORIZONTAL);
		data.horizontalSpan = getButtonIds().size();
		bodyBtn.setLayoutData(data);
		
		GridLayout gd = new GridLayout(getButtonIds().size(), false);
		gd.horizontalSpacing = getButtonIds().size() > 2 ? DPIUtil.autoScaleUpUsingNativeDPI(20) : DPIUtil.autoScaleUpUsingNativeDPI(90);
		bodyBtn.setLayout(gd);
		bodyBtn.setBackground(new Color(Display.getCurrent(), new RGB(255, 255, 255), 255));
		
		for (Map.Entry<String, String> entry : getButtonIds().entrySet()) {
			SquareButton btn = UIControlsFactory.createButton(bodyBtn, UIControlsFactory.BUTTON_DEFAULT);
			btn.setText(entry.getValue());
			btn.setLayoutData(new GridData(GridData.CENTER));
			btn.setSize(DPIUtil.autoScaleUpUsingNativeDPI(130), DPIUtil.autoScaleUpUsingNativeDPI(65));
			btn.setInactiveColors(new Color(Display.getCurrent(), new RGB(146, 163, 178), 255));
			btn.setInactiveFontColors(new Color(Display.getCurrent(), new RGB(255, 255, 255), 255));
			btn.setHoverColors(new Color(Display.getCurrent(), new RGB(1, 115, 199), 255));
			buttonMap.put(entry.getKey(), btn);
		}
		
		addBtnEventListener();
	}
	
	protected void createFields() {
		for (Map.Entry<String, String> entry : getFieldIds().entrySet()) {
			AbstractField txtLot = createField(entry.getKey(), entry.getValue());
			addField(entry.getKey(), txtLot);
		}
	}
	
	protected AbstractField createField(String id, String label) {
		return createText(id, label, "", 32);
	}

	public void createContent(Composite parent, FormToolkit toolkit) {
		fields.values().stream().forEach(field -> field.createContent(parent, toolkit));
	}
	
	@Override
	public void setFieldFont(String name, int height) {
		super.setFieldFont(name, height);
		
		FontData fontData = new FontData(name, DPIUtil.autoScaleUpUsingNativeDPI(21), SWT.CENTER);
		Font font = new Font(Display.getCurrent(), fontData);
		for (SquareButton button : buttonMap.values()) {
			button.setFont(font);
		}
	}
}
