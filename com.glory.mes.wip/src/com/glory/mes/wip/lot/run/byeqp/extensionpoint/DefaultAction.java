package com.glory.mes.wip.lot.run.byeqp.extensionpoint;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpEditor;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManager;

/**
 * 默认Action不处理业务，不做修改
 * <AUTHOR>
 *
 */
public class DefaultAction implements IByEqpAction {
	
	private static final Logger logger = Logger.getLogger(DefaultAction.class);
	
	public static final String ACTION_NAME = "ByEqpDefaultAction";
	public static final String ADFORM_NAME = "WIPLotByEqpForm";
	
	protected EPartService partService;
	
	protected EModelService modelService;
	
	private ByEqpManager manager;
	
	@Override
	public ADForm getADForm() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			return adManager.getADForm(Env.getOrgRrn(), ADFORM_NAME);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return null;
		}
	}

	@Override
	public void initExtend(ByEqpEditor byEqpEditor, GlcForm form) {
	}

	@Override
	public void subscribeExtend(IEventBroker eventBroker, GlcForm form) {
	}

	@Override
	public void unSubscribeExtend() {
	}

	@Override
	public String getADFormName() {
		return ADFORM_NAME;
	}

	@Override
	public String getActionName() {
		return ACTION_NAME;
	}

	@Override
	public void setPartService(EPartService partService) {
		this.partService = partService;
	}

	@Override
	public void setModelService(EModelService modelService) {
		this.modelService = modelService;
	}

	@Override
	public void setManager(ByEqpManager manager) {
		this.manager = manager;
	}

	@Override
	public ByEqpManager getManager() {
		return manager;
	}
}
