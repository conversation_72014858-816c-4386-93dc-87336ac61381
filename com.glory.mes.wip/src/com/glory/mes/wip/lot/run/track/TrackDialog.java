package com.glory.mes.wip.lot.run.track;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import org.eclipse.jface.text.ITextListener;
import org.eclipse.jface.text.TextEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CLabel;
import org.eclipse.swt.custom.StackLayout;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.events.MouseMoveListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.console.ConsolePlugin;
import org.eclipse.ui.console.IConsole;
import org.eclipse.ui.console.MessageConsole;
import org.eclipse.ui.console.MessageConsoleStream;
import org.eclipse.ui.console.TextConsoleViewer;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.DateUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.client.SecurityManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.track.listener.ILotChangeListener;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;

public class TrackDialog implements ILotChangeListener {
	
	private Display display;
	private Shell shell;
	
	protected Rectangle area;
	
	public static String TABLE_NAME = "WIPTrackLot";	
	public static String TRANS_TYPE_TRACKIN = "TrackIn";
	public static String TRANS_TYPE_TRACKOUT = "TrackOut";
	
	public static String LOGGER_LEVEL_INFO = "INFO";	
	public static String LOGGER_LEVEL_WARN = "WARN";
	public static String LOGGER_LEVEL_ERROR = "ERROR";
	
	protected Composite bodyComposite;
	protected Composite bodyLeftComposite;
	protected Composite bodyRightComposite;
	
	protected Text txtLot, txtOperator;
	protected ListTableManager lotListManager;
	protected MessageConsole fMessageConsole = null;
	protected ToolItem itemTrackIn, itemTrackOut, itemAbort, itemRefresh, itemControlScreen, itemClose;
	protected StackLayout stacklayout;
	
	protected Composite trackContentComposite;
	protected Composite trackInComposite;
	protected Composite trackOutComposite;
	
	protected String transType;
	protected TrackContext trackContext;
	
	protected List<ILotChangeListener> lotChangeListeners = new LinkedList<ILotChangeListener>();
	
	private boolean isDraw = false;  
	private int xx;  
	private int yy;
    
	public TrackDialog(TrackContext trackContext) {
		this.trackContext = trackContext;
		this.display = Display.getDefault();
		area = Display.getDefault().getClientArea();
	}
	
	public TrackDialog(Display display, TrackContext trackContext) {	
		this.display = display;
		this.trackContext = trackContext;
		area = Display.getDefault().getPrimaryMonitor().getBounds();
	}
	
	public TrackDialog(Display display, String transType, TrackContext trackContext) {	
		this.display = display;
		this.transType = transType;
		this.trackContext = trackContext;
		area = Display.getDefault().getPrimaryMonitor().getBounds();
	}
	
	public void open() {
		shell = new Shell(getDisplay(), SWT.BORDER | SWT.RESIZE | SWT.NO_TRIM);
		shell.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		FillLayout fillLayout = new FillLayout();
		fillLayout.marginHeight = 0;
		fillLayout.marginWidth = 0;
		shell.setLayout(fillLayout);
		shell.setBounds(Display.getDefault().getPrimaryMonitor().getBounds());
		
		createContents();		
		refresh();
		
		shell.open();
		while (shell != null && !shell.isDisposed()) {
			try {
				if (!getDisplay().readAndDispatch()) {
					getDisplay().sleep();
				}
			} catch (Throwable e) {
				e.printStackTrace();
			}
		}
	}

	protected Control createContents() {
		GridLayout layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		//父面板
		Composite parent = new Composite(shell, SWT.BORDER | SWT.COLOR_BLACK);
		parent.setLayout(layout);
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));	
		parent.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		//子面板，头部
		createHeaderComposite(parent);
		//子面板，内容部分
		createBodyComposite(parent);
		return parent;
	}
	
	protected void createHeaderComposite(Composite composite) {
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 3;
		gridLayout.marginHeight = 0;
		gridLayout.verticalSpacing = 0;
		gridLayout.marginWidth = 0;
		gridLayout.horizontalSpacing = 0;
		
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.heightHint = 81;
		
		Composite headerComposite = new Composite(composite, SWT.NONE);
		headerComposite.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		headerComposite.setLayout(gridLayout);
		headerComposite.setLayoutData(gd);
		headerComposite.addMouseMoveListener( new MouseMoveListener() {
			@Override
			public void mouseMove(MouseEvent e) {
				if (isDraw) {   
					shell.setLocation(shell.getLocation().x + e.x - xx, shell.getLocation().y + e.y - yy); 			
				}
			}			
		});
		headerComposite.addMouseListener(new MouseListener() {
			@Override
			public void mouseDoubleClick(MouseEvent e) {}
			@Override
			public void mouseDown(MouseEvent e) {
				isDraw = true;   
				xx = e.x;   
				yy = e.y;							
			}
			@Override
			public void mouseUp(MouseEvent e) {
				isDraw = false;							
			}			
		});
		
		gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		gridLayout.marginHeight = 0;
		gridLayout.verticalSpacing = 0;
		gridLayout.marginWidth = 0;
		gridLayout.horizontalSpacing = 0;
		
		gd = new GridData(GridData.FILL_BOTH);	
		gd.horizontalAlignment = SWT.LEFT;
		Composite headerLeftComposite = new Composite(headerComposite, SWT.NONE);
		headerLeftComposite.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		headerLeftComposite.setLayout(gridLayout);
		headerLeftComposite.setLayoutData(gd);
		createLoginComposite(headerLeftComposite);
		
		gridLayout = new GridLayout();
		gridLayout.numColumns = 4;
		gridLayout.marginHeight = 10;
		gridLayout.verticalSpacing = 0;
		gridLayout.marginWidth = 0;
		gridLayout.horizontalSpacing = 0;
		
		gd = new GridData(GridData.FILL_BOTH);	
		gd.horizontalAlignment = SWT.LEFT;
		Composite headerCenterComposite = new Composite(headerComposite, SWT.NONE);
		headerCenterComposite.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		headerCenterComposite.setLayout(gridLayout);
		headerCenterComposite.setLayoutData(gd);
		createCurrentStateComposite(headerCenterComposite);
		
		gd = new GridData(GridData.FILL_HORIZONTAL);		
		gd.horizontalAlignment = SWT.RIGHT;
		Composite headerRightComposite = new Composite(headerComposite, SWT.NONE);
		headerRightComposite.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		headerRightComposite.setLayout(new GridLayout(5, false));
		headerRightComposite.setLayoutData(gd);
		createToolBar(headerRightComposite);
	}
	
	protected void createBodyComposite(Composite parent) {
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 2;
		gridLayout.marginHeight = 0;
		gridLayout.verticalSpacing = 0;
		gridLayout.marginWidth = 0;
		gridLayout.horizontalSpacing = 0;
	
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.heightHint = area.height - 81;
		
		bodyComposite = new Composite(parent, SWT.NONE);
		bodyComposite.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		bodyComposite.setLayout(gridLayout);		
		bodyComposite.setLayoutData(gd);
			
		gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = Double.valueOf((area.height - 81) * 0.35).intValue();
		//body的上左面板
		bodyLeftComposite = new Composite(bodyComposite, SWT.NONE);
		bodyLeftComposite.setLayout(new GridLayout(2, false));
		bodyLeftComposite.setLayoutData(gd);
		bodyLeftComposite.setBackground(bodyLeftComposite.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		createLotInputAndTableComposite(bodyLeftComposite);//批次信息Table面板
		//body的上右面板
		bodyRightComposite = new Composite(bodyComposite, SWT.NONE);
		bodyRightComposite.setLayout(new GridLayout(2, false));
		bodyRightComposite.setLayoutData(gd);
		bodyRightComposite.setBackground(bodyRightComposite.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		createMessageComposite(bodyRightComposite);//消息面板	
		
		gd = new GridData(GridData.FILL_BOTH);	
		gd.heightHint = Double.valueOf((area.height - 81) * 0.54).intValue();
		gd.grabExcessHorizontalSpace = true;
		gd.horizontalAlignment = GridData.FILL;
		gd.horizontalSpan = 2;
		
		stacklayout = new StackLayout();
		//body的下进出站组件面板		
		trackContentComposite = new Composite(bodyComposite, SWT.NONE);
		trackContentComposite.setBackground(bodyComposite.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		trackContentComposite.setLayout(stacklayout);		
		trackContentComposite.setLayoutData(gd);					
		createTrackSwitchComposite(trackContentComposite);
	}
	
	public void createLoginComposite(Composite parent) {
		CLabel cblMessage = new CLabel(parent, SWT.NONE);
		cblMessage.setText("MESwell");
		cblMessage.setForeground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		cblMessage.setImage(SWTResourceCache.getImage("glory_logo32"));
		cblMessage.setFont(new Font(null,"微软雅黑",10, SWT.BOLD));
		cblMessage.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
	}
	
	public void createCurrentStateComposite(Composite parent) {
		CLabel cblMessage1 = new CLabel(parent, SWT.NONE);
		cblMessage1.setText("系统登录用户：");
		cblMessage1.setImage(SWTResourceCache.getImage("big-user"));
		cblMessage1.setFont(new Font(null,"楷体",14, SWT.NONE));
		cblMessage1.setForeground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		cblMessage1.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		
		Label userLabel = new Label(parent, SWT.NONE);
		userLabel.setText(Env.getUserName() + "    ");
		userLabel.setFont(new Font(null,"楷体",16, SWT.BOLD));
		userLabel.setForeground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		userLabel.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		
		CLabel cblMessage2 = new CLabel(parent, SWT.NONE);
		cblMessage2.setFont(new Font(null,"楷体",14, SWT.NONE));
		cblMessage2.setForeground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		cblMessage2.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		
		Label eqpLabel = new Label(parent, SWT.NONE);
		eqpLabel.setFont(new Font(null,"楷体",16, SWT.BOLD));
		eqpLabel.setForeground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		eqpLabel.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		
		if (trackContext.getSelectEquipments() != null && trackContext.getSelectEquipments().size() > 0) {
			cblMessage2.setText("当前选择设备：");
			cblMessage2.setImage(SWTResourceCache.getImage("big-equipment"));
			eqpLabel.setText(trackContext.getSelectEquipments().get(0).getEquipmentId() + "    ");
		} else {
			cblMessage2.setText("当前选择工序：");
			cblMessage2.setImage(SWTResourceCache.getImage("big-step"));
			eqpLabel.setText(trackContext.getStep().getName() + "    ");
		}
		
	}
	
	public void createToolBar(Composite parent) {
		ToolBar tBar = new ToolBar(parent, SWT.FLAT | SWT.HORIZONTAL);
		tBar.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		createToolItemTrackIn(tBar);
		createToolItemAbort(tBar);
		createToolItemTrackOut(tBar);
		createToolItemRefresh(tBar);
		createToolItemUnFullScreen(tBar);
		createToolItemClose(tBar);
	}
	
	protected void createToolItemTrackIn(ToolBar tBar) {
		itemTrackIn = new ToolItem(tBar, SWT.BORDER);
		itemTrackIn.setText(Message.getString("wip.trackin"));
		itemTrackIn.setImage(SWTResourceCache.getImage("big-trackin"));
		itemTrackIn.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackInAdapter();
			}
		});
	}
	
	protected void createToolItemAbort(ToolBar tBar) {
		itemAbort = new ToolItem(tBar, SWT.PUSH);
		itemAbort.setText(Message.getString("wip.abort"));
		itemAbort.setImage(SWTResourceCache.getImage("big-abort"));
		itemAbort.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				abortAdapter();
			}
		});
	}
	
	protected void createToolItemTrackOut(ToolBar tBar) {
		itemTrackOut = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemTrackOut.setText(Message.getString("wip.trackout"));
		itemTrackOut.setImage(SWTResourceCache.getImage("big-trackout"));
		itemTrackOut.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackOutAdapter();
			}
		});
	}
	
	protected void createToolItemRefresh(ToolBar tBar) {
		itemRefresh = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemRefresh.setText("刷新");
		itemRefresh.setImage(SWTResourceCache.getImage("big-refresh"));
		itemRefresh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				cleanAdapter();
			}
		});
	}
	
	protected void createToolItemUnFullScreen(ToolBar tBar) {
		itemControlScreen = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemControlScreen.setText("取消全屏");
		itemControlScreen.setImage(SWTResourceCache.getImage("big-unfullscreen"));
		itemControlScreen.setData("0");
		itemControlScreen.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if (itemControlScreen.getData().equals("0")) {
					unFullScreenAdapter();
				} else {
					fullScreenAdapter();
				}			 
			}
		});
	}
	
	protected void createToolItemClose(ToolBar tBar) {
		itemClose = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemClose.setText("关闭");
		itemClose.setImage(SWTResourceCache.getImage("big-close"));
		itemClose.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				close();
			}
		});
	}
		 
	protected void changeStatus(String status) {
    	if (status == null) {
    		itemTrackIn.setEnabled(Boolean.TRUE);
    		itemTrackOut.setEnabled(Boolean.TRUE);
    		itemAbort.setEnabled(Boolean.TRUE);
    	} else {
    		if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(status)) {
    			itemTrackIn.setEnabled(Boolean.TRUE);
    			itemTrackOut.setEnabled(Boolean.FALSE);
    			itemAbort.setEnabled(Boolean.FALSE);
    		} else if (LotStateMachine.STATE_RUN.equalsIgnoreCase(status)) {
    			itemTrackIn.setEnabled(Boolean.FALSE);
    			itemTrackOut.setEnabled(Boolean.TRUE);
    			itemAbort.setEnabled(Boolean.TRUE);
    		} else {
    			itemTrackIn.setEnabled(Boolean.TRUE);
    			itemTrackOut.setEnabled(Boolean.TRUE);
    			itemAbort.setEnabled(Boolean.TRUE);
    		}
    	}
    }
	
	protected void createLotInputAndTableComposite(Composite parent) {
		try {
			Group lotInfoTabelGroup = new Group(parent, SWT.NONE);
			lotInfoTabelGroup.setText("输入信息");
			lotInfoTabelGroup.setLayout(new GridLayout(1, false));
			lotInfoTabelGroup.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
			GridData gd = new GridData(GridData.FILL_BOTH);
			lotInfoTabelGroup.setLayoutData(gd);
			
			createInputBox(lotInfoTabelGroup);
			
			ADManager entityManager = Framework.getService(ADManager.class);
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotListManager = new ListTableManager(adTable);	
			lotListManager.setIndexFlag(true);
			lotListManager.newViewer(lotInfoTabelGroup);
			lotListManager.setInput(null);
		} catch (ClientException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void createInputBox(Composite parent) {
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		//左顶面板
		Composite topLeftComposite = new Composite(parent, SWT.NONE);
		topLeftComposite.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		topLeftComposite.setLayout(new GridLayout(4, false));		
		topLeftComposite.setLayoutData(gd);
		
		Font fontLbl = new Font(null, "宋体", 18, SWT.BOLD);
		Font fontText = new Font(null, "楷体", 14, SWT.BOLD);
		GridData gText = new GridData();
		gText.widthHint = 216;
		gText.heightHint = 30;
		
		Label lotLbl = new Label(topLeftComposite, SWT.NONE);
		lotLbl.setText(Message.getString("wip.lot_id") + ":");	
		lotLbl.setFont(fontLbl);
		lotLbl.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));	
		lotLbl.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));						
		txtLot = new Text(topLeftComposite, SWT.BORDER);
		txtLot.setLayoutData(gText);
		txtLot.setTextLimit(32);	
		txtLot.setFont(fontText);
		txtLot.setFocus();
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					lot = searchLot(lotId);
					tLotId.selectAll();
					if (lot == null) {
						writeConsoleMessage("批次不存在", LOGGER_LEVEL_ERROR);					
					} else {
						validateLot(lot);
					}
					break;
				}
			}
		});
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {}
			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});
		
		Label operatorLbl = new Label(topLeftComposite, SWT.NONE);
		operatorLbl.setText("操作员:");	
		operatorLbl.setFont(fontLbl);
		operatorLbl.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));	
		operatorLbl.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));		
		gText = new GridData();
		gText.widthHint = 160;
		gText.heightHint = 30;
		txtOperator = new Text(topLeftComposite, SWT.BORDER);
		txtOperator.setLayoutData(gText);
		txtOperator.setTextLimit(32);	
		txtOperator.setFont(fontText);	
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			return lot;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public void validateLot(Lot lot) {
		try {						
			if (LotStateMachine.STATE_WAIT.equals(lot.getState())) { //进站情况
				List<Lot> lots = trackContext.getLots();
				if (lots == null) {
					lots = new ArrayList<Lot>();
				}
				for (Lot selectedLot : lots) {
					if (selectedLot.getLotId().equals(lot.getLotId())) {
						this.writeConsoleMessage("批次已存在列表中!", LOGGER_LEVEL_ERROR);
						return;
					}
				}
				
				if (trackContext.getSelectEquipments() != null && trackContext.getSelectEquipments().size() > 0) {
					lot.setEquipmentId(trackContext.getSelectEquipments().get(0).getEquipmentId());
					for (Equipment equipment : trackContext.getSelectEquipments()) {					
						if (!equipment.getIsBatch() && lots != null && lots.size() > 0) {
							this.writeConsoleMessage(Message.getString("wip.trackin_cannot_batch"), LOGGER_LEVEL_ERROR);
							return;
						}
					}
				}	
				
				if (trackContext.getStep() != null) {
					if (!trackContext.getStep().getObjectRrn().equals(lot.getStepRrn())) {
						this.writeConsoleMessage("批次不在选择的工步上!", LOGGER_LEVEL_ERROR);
						return;
					}
				} else {
					PrdManager prdManager = Framework.getService(PrdManager.class);
					Step step = new Step();
					step.setObjectRrn(lot.getStepRrn());
					step = (Step) prdManager.getSimpleProcessDefinition(step);
					trackContext.setStep(step);
				}
							
				lots.add(lot);
				trackContext.setLots(lots);
				
				InContext inContext = new InContext();
				inContext.setLots(trackContext.getLots());
				inContext.setEquipments(trackContext.getSelectEquipments());							
				LotManager lotManager = Framework.getService(LotManager.class);
				ChainContext wipContext = lotManager.checkTrackInConstraint(inContext, Env.getSessionContext());
				if (wipContext.getReturnMessage() != null
						&& wipContext.getReturnMessage().trim().length() > 0) {
					this.writeConsoleMessage(wipContext.getReturnMessage(), LOGGER_LEVEL_WARN);
				}
				if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
					trackContext.getLots().remove(lot);
					return;
				}			
			} else if (LotStateMachine.STATE_RUN.equals(lot.getState())) { //出站情况
				List<Lot> runLots = getRunningLotList(lot);
				trackContext.setLots(runLots);
				
				InContext inContext = new InContext();
				inContext.setLots(trackContext.getLots());
				LotManager lotManager = Framework.getService(LotManager.class);
				ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
				if (wipContext.getReturnMessage() != null
						&& wipContext.getReturnMessage().trim().length() > 0) {
					this.writeConsoleMessage(wipContext.getReturnMessage(), LOGGER_LEVEL_WARN);
				}
				if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
					trackContext.setLots(null);
					return;
				}
			}
			trackContext.setCurrentLot(lot);
			
			refresh(); //刷新
		} catch (ClientException e) {	
			trackContext.getLots().remove(lot);
			writeConsoleMessage(Message.getString(e.getErrorCode()), LOGGER_LEVEL_ERROR);				
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void refresh() {
		try {
			if (trackContext.getCurrentLot() != null) {
				changeStatus(trackContext.getCurrentLot().getState()); //刷新按钮	
				lotListManager.setInput(trackContext.getLots()); //刷新批次信息列表	
				
				if (LotStateMachine.STATE_WAIT.equals(trackContext.getCurrentLot().getState())) {
					stacklayout.topControl = trackInComposite;							
				} else {
					stacklayout.topControl = trackOutComposite;
				}
				trackContentComposite.layout(); //切换进出站面板
				notifyLotChangeListeners(this, trackContext.getLots()); //发送批次信息给每个组件
			} else {
				txtLot.setText("");
				changeStatus(null);
				lotListManager.setInput(null);
				notifyLotChangeListeners(this, null);
			}		
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected List<Lot> getRunningLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}
	
	protected boolean checkOperator(String operatorStr) {
		try {
			SecurityManager secManager = Framework.getService(SecurityManager.class);
			ADUser user = secManager.getUserByUserName(Env.getOrgRrn(), operatorStr);
			if (user != null) {
				return true;
			}
		} catch (Exception e) {
			
		}
		return false;
	}
	
	protected void createMessageComposite(Composite composite) {	
		Group consoleGroup = new Group(composite, SWT.NONE);
		consoleGroup.setText("控制台信息");
		consoleGroup.setLayout(new GridLayout(1, false));
		consoleGroup.setBackground(composite.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		GridData gd = new GridData(GridData.FILL_BOTH);
		consoleGroup.setLayoutData(gd);
		
		IConsole[] consoles = ConsolePlugin.getDefault().getConsoleManager().getConsoles();
		for (IConsole console : consoles) {
			if (LotStateMachine.TRANS_TRACKOUT.equals(console.getName())) {
				fMessageConsole = (MessageConsole)console;
			}
		}		
		fMessageConsole = new MessageConsole(LotStateMachine.TRANS_TRACKIN, null);
		Font font = new Font(null, "Arial", 9, SWT.BOLD);
		fMessageConsole.setFont(font);
		ConsolePlugin.getDefault().getConsoleManager().addConsoles(new IConsole[] { fMessageConsole });
		
		final TextConsoleViewer tcv = new TextConsoleViewer(consoleGroup, fMessageConsole);
		gd = new GridData(GridData.FILL_BOTH);
		composite.setLayoutData(gd);
		tcv.getTextWidget().setLayoutData(gd);
		tcv.getTextWidget().setWordWrap(true);
		toTopIndex(tcv);
		tcv.addTextListener(new ITextListener() {
			public void textChanged(TextEvent event) {
				toTopIndex(tcv);
			}
		});
	}
	
	private void toTopIndex(final TextConsoleViewer tcv) {
		StyledText textWidget = tcv.getTextWidget();
		if (textWidget != null && !textWidget.isDisposed()) {
			int lineCount = textWidget.getLineCount();
			tcv.setTopIndex(lineCount - 1);
		}
	}
	
	public void writeConsoleMessage(String message, String logLevel) {
		if (fMessageConsole != null) {		
			MessageConsoleStream msgConsoleStream = fMessageConsole.newMessageStream();						
			String prefix = DateUtil.formatTime(new Date());
			if (LOGGER_LEVEL_INFO.equals(logLevel)) {
				msgConsoleStream.setColor(SWTResourceCache.getColor(SWTResourceCache.COLOR_GREEN));
				prefix += " INFO ";
			} else if (LOGGER_LEVEL_WARN.equals(logLevel)) {
				msgConsoleStream.setColor(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLUE));
				prefix += " WARN ";
				
			} else if (LOGGER_LEVEL_ERROR.equals(logLevel)) {
				msgConsoleStream.setColor(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				prefix += " ERROR ";
			}			
			msgConsoleStream.println(prefix + message);	
		}
	}
	
	protected void createTrackSwitchComposite(Composite composite) {				
		try {		
//			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
//			trackInComposite = new Composite(composite, SWT.NONE);		
//			trackInComposite.setLayoutData(gd);	
//			trackInComposite.setBackground(composite.getDisplay().getSystemColor(SWT.COLOR_WHITE));			
//			TrackEntry.createForm(this, trackInComposite, TRANS_TYPE_TRACKIN, trackContext.getStep(), 
//					trackContext.getSelectEquipments() != null &&  trackContext.getSelectEquipments().size() > 0 ? trackContext.getSelectEquipments().get(0) : null);
//			
//			trackOutComposite = new Composite(composite, SWT.NONE);
//			trackOutComposite.setLayoutData(gd);	
//			trackOutComposite.setBackground(composite.getDisplay().getSystemColor(SWT.COLOR_WHITE));
//			TrackEntry.createForm(this, trackOutComposite, TRANS_TYPE_TRACKOUT, trackContext.getStep(), 
//					trackContext.getSelectEquipments() != null &&  trackContext.getSelectEquipments().size() > 0 ? trackContext.getSelectEquipments().get(0) : null);
//			
//			if (TRANS_TYPE_TRACKIN.equals(transType)) {				
//				stacklayout.topControl = trackInComposite;				
//			} else {			
//				stacklayout.topControl = trackOutComposite;				
//			}						
		} catch (Exception e) {
			e.printStackTrace();
		}		
	}	
	
	public void trackInAdapter() {
		try { 
//			trackContext = TrackEntry.saveToObject(trackContext, TRANS_TYPE_TRACKIN);
//			if (txtOperator.getText() == null || "".equals(txtOperator.getText())) {
//				writeConsoleMessage(Message.getString("wip.track_operator_is_null"), LOGGER_LEVEL_ERROR);
//				return;	
//			}			
//			LotManager lotManager = Framework.getService(LotManager.class);
//			for (Lot lot : trackContext.getLots()) {			
//				lot.setOperator1(trackContext.getOperator1());
//			}
//			if (trackContext.getStep().getIsRequireEqp() && trackContext.getSelectEquipments() != null
//					&& trackContext.getSelectEquipments().size() > 0) {
//				Equipment equipment = trackContext.getSelectEquipments().get(0);
//				for (Lot lot : trackContext.getLots()) {
//					if (lot.getEquipmentId() == null) {
//						lot.setEquipmentId(equipment.getEquipmentId());
//					}
//				}
//			}		
//			InContext inContext = new InContext();
//			inContext.setLots(trackContext.getLots());
//			inContext.setAttributeValues(trackContext.getLotAttributeValues());
//			OutContext outContext = lotManager.trackIn(inContext, Env.getSessionContext());
//			this.writeConsoleMessage(Message.getString("wip.trackin_success"), LOGGER_LEVEL_INFO);
//			
//			Lot currentLot = searchLot(trackContext.getCurrentLot().getLotId());
//			trackContext.setCurrentLot(currentLot);
//			trackContext.setLots(outContext.getLots());
//			refresh();
		} catch (ClientException e) {			
			writeConsoleMessage(Message.getString(e.getErrorCode()), LOGGER_LEVEL_ERROR);				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void abortAdapter() {
		try { 
			if (txtOperator.getText() == null || "".equals(txtOperator.getText())) {
				writeConsoleMessage(Message.getString("wip.track_operator_is_null"), LOGGER_LEVEL_ERROR);
				return;	
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			for (Lot lot : trackContext.getLots()) {			
				lot.setOperator1(trackContext.getOperator1());
			}
			InContext inContext = new InContext();
			inContext.setLots(trackContext.getLots());			
			OutContext outContext = lotManager.abortLot(inContext, Env.getSessionContext());
			this.writeConsoleMessage(Message.getString("wip.abort_success"), LOGGER_LEVEL_INFO);		
			
			Lot currentLot = searchLot(trackContext.getCurrentLot().getLotId());
			trackContext.setCurrentLot(currentLot);
			trackContext.setLots(outContext.getLots());
			refresh();
		} catch (ClientException e) {			
			writeConsoleMessage(Message.getString(e.getErrorCode()), LOGGER_LEVEL_ERROR);				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void trackOutAdapter() {
		try { 
			if (txtOperator.getText() == null || "".equals(txtOperator.getText())) {
				writeConsoleMessage(Message.getString("wip.track_operator_is_null"), LOGGER_LEVEL_ERROR);
				return;	
			}
			for (Lot lot : trackContext.getLots()) {
				lot.setOperator1(trackContext.getOperator1());
			}
			InContext inContext = new InContext();
			inContext.setLots(trackContext.getLots());
			inContext.setAttributeValues(trackContext.getLotAttributeValues());			
			LotManager lotManager = Framework.getService(LotManager.class);
			OutContext outContext = lotManager.trackOut(inContext, Env.getSessionContext());	
			this.writeConsoleMessage(Message.getString("wip.trackout_success"), LOGGER_LEVEL_INFO);
			
			StringBuffer sb = new StringBuffer();
			List<Lot> reworkLots = outContext.getReworkLots();
			for (Lot reworkLot : reworkLots) {
				sb.append("批次<" + reworkLot.getLotId() + ">返工到了" + reworkLot.getStepName() + "工步! \n");	
			}
			List<Lot> outLots = outContext.getLots();
			for (Lot outLot : outLots) {
				sb.append("批次<" + outLot.getLotId() + ">到了下一站工步<" + outLot.getStepName() + ">! \n");
			}
			this.writeConsoleMessage(sb.toString(), LOGGER_LEVEL_INFO);
			
			trackContext.setCurrentLot(null);
			trackContext.setLotAttributeValues(null);
			trackContext.setLots(null);
			trackContext.setOperator1(null);
			trackContext.setOperator2(null);
			refresh();
		} catch (ClientException e) {			
			writeConsoleMessage(Message.getString(e.getErrorCode()), LOGGER_LEVEL_ERROR);				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void unFullScreenAdapter() {
		area = new Rectangle(100, 50, area.width - 200, area.height - 100);
		shell.setBounds(area);
		bodyComposite.setSize(bodyComposite.getBounds().width, Double.valueOf((area.height - 85)).intValue());
		bodyLeftComposite.setSize(bodyLeftComposite.getBounds().width, Double.valueOf((area.height - 85) * 0.40).intValue());
		bodyRightComposite.setSize(bodyRightComposite.getBounds().width, Double.valueOf((area.height - 85) * 0.40).intValue());
		trackContentComposite.setSize(trackContentComposite.getBounds().width, Double.valueOf((area.height - 85) * 0.60).intValue());	
		itemControlScreen.setImage(SWTResourceCache.getImage("big-fullscreen"));
		itemControlScreen.setText("全屏");
		itemControlScreen.setData("1");
	}
	
	public void fullScreenAdapter() {
		area = Display.getDefault().getPrimaryMonitor().getBounds();
		shell.setBounds(area);
		bodyComposite.setSize(bodyComposite.getBounds().width, Double.valueOf((area.height - 85)).intValue());
		bodyLeftComposite.setSize(bodyLeftComposite.getBounds().width, Double.valueOf((area.height - 85) * 0.40).intValue());
		bodyRightComposite.setSize(bodyRightComposite.getBounds().width, Double.valueOf((area.height - 85) * 0.40).intValue());
		trackContentComposite.setSize(trackContentComposite.getBounds().width, Double.valueOf((area.height - 85) * 0.60).intValue());	
		itemControlScreen.setImage(SWTResourceCache.getImage("big-unfullscreen"));
		itemControlScreen.setText("取消全屏");
		itemControlScreen.setData("0");
	}
	
	public void cleanAdapter() {
		changeStatus(null);
		txtLot.setText("");
		txtOperator.setText("");
		lotListManager.setInput(null);
		fMessageConsole.clearConsole();
		notifyLotChangeListeners(this, null);
	}
	
	public boolean close() {
		if (shell == null || shell.isDisposed()) {
			return true;
		}
		
    	shell.dispose();
		shell = null;
//		TrackEntry.trackFormMap.clear();
		return true;  	
	}
	
	public Display getDisplay() {
		return display;
	}

	public void setDisplay(Display display) {
		this.display = display;
	}	
	
	public void addLotChangeListeners(ILotChangeListener listener) {
		synchronized (lotChangeListeners) {
			lotChangeListeners.add(listener);
		}
	}

	public void removeLotChangeListeners(ILotChangeListener listener) {
		synchronized (lotChangeListeners) {
			lotChangeListeners.remove(listener);
		}
	}

	public void notifyLotChangeListeners(Object sender, List<Lot> lots) {
		synchronized (lotChangeListeners) {
			for (ILotChangeListener listener : lotChangeListeners) {
				try {
					listener.lotChanged(sender, lots);
				} catch (Throwable t) {
					t.printStackTrace();
				}
			}
		}
	}
	
	@Override
	public void lotChanged(Object sender, List<Lot> lots) {
		System.out.println("已加载");		
	}
	
	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
