package com.glory.mes.wip.lot.run.trackin;

import java.util.List;
import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Text;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Operation;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.operation.ShowStepOperationForm;
import com.glory.mes.wip.model.Lot;

public class ShowStepOperationPage extends FlowWizardPage {
	
	private static final Logger logger = Logger.getLogger(ShowStepOperationGlcPage.class);
	
	private ShowStepOperationForm form;
	private LotStepAttributeForm attributeForm;
	
	public ShowStepOperationPage() {
		super();
	}
	
	public ShowStepOperationPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		try {
			RunWizardContext context = (RunWizardContext) ((TrackInWizard) getWizard()).getContext();
			Lot lot = context.getLots().get(0);
			LotManager lotManager = Framework.getService(LotManager.class);
			PrdManager prdManager = Framework.getService(PrdManager.class);
			
			List<Operation> operations = lotManager.getLotCurrentOpeartion(lot);
			List<Node> nodes = prdManager.getProcessFlowList(lot.getProcessInstanceRrn());
			List<FutureNote> futureNotes = lotManager.getLotFutureNote((StepState) nodes.get(nodes.size() - 1), lot);
			List<FutureNote> procedurefutureNotes = lotManager.getProcedureFutureNote((StepState) nodes.get(nodes.size() - 1));
			List<StepAttribute> stepAttributes = prdManager.getStepAttribute(lot.getStepRrn(),StepAttribute.CATEGORY_TRACKIN);
			
			// 若没有操作指示，则转入下一页
			FlowWizard tiWizard = (FlowWizard) this.getWizard();
			if ((operations == null || operations.size() == 0)
					&& (stepAttributes == null || stepAttributes.size() == 0)
					&& (futureNotes == null || futureNotes.size() == 0)
					&& (procedurefutureNotes == null || procedurefutureNotes.size() == 0)) {
				tiWizard.getDialog().skipPressed();
				return ;
			}

			Composite composite = new Composite(parent, SWT.NULL);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			composite.setLayoutData(gd);
			setControl(composite);

			Group group = new Group(composite, SWT.NONE);
			group.setText(Message.getString("common.operation"));
			group.setLayout(layout);
			group.setLayoutData(gd);

			form = new ShowStepOperationForm(group, SWT.NONE, null);
			form.setLayoutData(gd);
			form.setValue(operations);
			form.refresh();
			
			if (stepAttributes != null && stepAttributes.size() > 0) {
				Group attribute = new Group(composite, SWT.NONE);
				attribute.setText(Message.getString("common.attribute"));
				attribute.setLayout(layout);
				attribute.setLayoutData(gd);
				
				Equipment eqp = null;
				if (context.getSelectEquipments() != null && context.getSelectEquipments().size() > 0) {
					eqp = context.getSelectEquipments().get(0);
				}
				attributeForm = new LotStepAttributeForm(attribute, SWT.NONE, null, 
						lot, stepAttributes, eqp);
				attributeForm.createForm();
				attributeForm.setLayoutData(gd);
			}
			
			//显示未来备注
			StringBuffer textvalue = new StringBuffer();
			if ((futureNotes != null && futureNotes.size() > 0)
					|| (procedurefutureNotes != null && procedurefutureNotes.size() > 0)) {
				
				if (futureNotes != null && futureNotes.size() > 0) {
					textvalue.append(Message.getString("wipadv.lot_future_note") + "----" + lot.getLotId() + "---------------------------------------\n");
					for (int n = 0; n < futureNotes.size(); n++) {
						textvalue.append((n + 1) + ": " + futureNotes.get(n).getNote() + "\n");
					}
				}
				
				if (procedurefutureNotes != null && procedurefutureNotes.size() > 0) {
					textvalue.append(Message.getString("wipadv.procedure_future_note") + "----" + lot.getLotId() + "---------------------------------------\n");
					for (int n = 0; n < procedurefutureNotes.size(); n++) {
						textvalue.append((n + 1) + ": " + procedurefutureNotes.get(n).getNote() + "\n");
					}
				}
				
				Group note = new Group(composite, SWT.NONE);
				note.setText("Note");
				note.setLayout(layout);
				note.setLayoutData(new GridData(940, 170));
				
				Text text = new Text(note, SWT.MULTI | SWT.READ_ONLY | SWT.V_SCROLL);
				text.setLayoutData(new GridData(GridData.FILL_BOTH));
				text.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				text.setText(textvalue.toString());
				text.setForeground(new Color(Display.getCurrent(), 255, 0, 0));
			}

			setTitle(Message.getString("wip.operation_title"));
			setMessage(Message.getString("wip.operation_message"));
			setPageComplete(true);
		} catch (Exception e) {
			logger.error("ShowStepOperationForm : addFields()", e);
		}
	}

	@Override
	public String doNext() {
		RunWizardContext context = (RunWizardContext) ((TrackInWizard) getWizard()).getContext();
		if (attributeForm != null) {
			boolean saveFlag = true;					
			if (!attributeForm.saveToObject()) {
				saveFlag = false;
			}					
			if (saveFlag) {
				context.setLotAttributeValues(attributeForm.getAttributeValues());	
			}else{
				return null;
			}			
		}
		Step  step = context.getStep();
		if (step == null || step.getCapability() == null) {
			UI.showError(Message.getString("wip.step_capability_is_null"));
			return null;
		}
		return getDefaultDirect();
	}
	
	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		this.setErrorMessage(null);
		return super.doPrevious();
	}

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, true);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

}
