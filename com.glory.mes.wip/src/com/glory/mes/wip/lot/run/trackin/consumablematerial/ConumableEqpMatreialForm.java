package com.glory.mes.wip.lot.run.trackin.consumablematerial;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.EquipmentMaterialAppend;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.model.Lot;

public class ConumableEqpMatreialForm extends ConumableMatreialForm {

	private static final Logger logger = Logger.getLogger(ConumableMatreialForm.class);
	
	private static final String TABLE_NAME_MATERIAL = "WIPStepMaterialNameList";
	private static final String TABLE_NAME_MLOT = "WIPStepMaterialLotList";
	
	public ListTableManager mLotManager;
	
	public ConumableEqpMatreialForm(Composite parent, int style, RunWizardContext context) {
		super (parent, style, context);
	}
	
	public void createForm(Composite parent) {		
		try {	
			toolkit = new FormToolkit(Display.getCurrent());
			
			Group materialGroup = new Group(parent, SWT.BORDER);
			materialGroup.setText(Message.getString("wip.record_raw_material_info"));
			materialGroup.setBackground(new Color(null, 255, 255, 255));
			materialGroup.setLayout(new GridLayout(1, false));
			materialGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			ADManager adManager = Framework.getService(ADManager.class);	
			ADTable adTable0 = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MATERIAL);
			materialManager = new ListTableManager(adTable0);
			materialManager.setIndexFlag(true);
			materialManager.newViewer(materialGroup);
			
			ADTable adTable1 = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT);
			mLotManager = new ListTableManager(adTable1);
			mLotManager.setIndexFlag(true);
			mLotManager.newViewer(materialGroup);	
		} catch (Exception e) {
			logger.error("ConumableMatreialForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public void loadFromObject() {
		try {
			//单位用量总数是多少
			BigDecimal totalQty = BigDecimal.ZERO;
			for (Lot lot : context.getLots()) {
				totalQty = totalQty.add(lot.getMainQty());
			}
			List<WorkOrderBomLine> stepMaterialBomLines = new ArrayList<WorkOrderBomLine>();
			for (WorkOrderBomLine bomLine : context.getBomLines()) {
				if (BomLine.ITEMCATEGORY_COMPONENT.equals(bomLine.getItemCategory())) {
					bomLine.setTransQty(totalQty.multiply(bomLine.getUnitQty())); //算出一共要多少
					stepMaterialBomLines.add(bomLine);
				}
			}	
			materialManager.setInput(stepMaterialBomLines);
			
			ADManager adManager = Framework.getService(ADManager.class);
			MLotManager lotManager = Framework.getService(MLotManager.class);		
			List<MLot> mLots = new ArrayList<MLot>();
			if (context.getSelectEquipments() != null && context.getSelectEquipments().size() > 0) {
				for (Equipment equipment : context.getSelectEquipments()) {
					List<EquipmentMaterial> equipmentMaterials = lotManager.getEquipmentMaterials(Env.getOrgRrn(), equipment.getEquipmentId(), null);
					if (equipmentMaterials != null && equipmentMaterials.size() > 0) {
						for (EquipmentMaterial equipmentMaterial : equipmentMaterials) {
							MLot mLot = new MLot();
							mLot.setObjectRrn(equipmentMaterial.getMLotRrn());
							mLot = (MLot) adManager.getEntity(mLot);
							mLot.setTransMainQty(equipmentMaterial.getAttachMainQty());
							mLot.setTransSubQty(equipmentMaterial.getAttachSubQty());
							mLots.add(mLot);
						}
					}
					List<EquipmentMaterialAppend> appends = lotManager.getEquipmentMaterialAppends(Env.getOrgRrn(), equipment.getEquipmentId(), null);
					if (appends != null && appends.size() > 0) {
						for (EquipmentMaterialAppend append : appends) {
							MLot mLot = new MLot();
							mLot.setObjectRrn(append.getMLotRrn());
							mLot = (MLot) adManager.getEntity(mLot);
							mLot.setTransMainQty(append.getAttachMainQty());
							mLot.setTransSubQty(append.getAttachSubQty());
							mLots.add(mLot);
						}
					}
				}
			}
			mLotManager.setInput(mLots);			
		} catch (Exception e) {
			logger.error("ConumableMatreialForm : loadFromObject", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public boolean saveToObject() {
		if (context != null) {
			if (!validate()){
				return false;
			}
			return true;
		}
		return false;
	}
	
	@Override
	public boolean validate() {
		List<MLot> allMLot = (List<MLot>)(List<? extends Object>)mLotManager.getInput();
		//判断数量要必填，不能为负数量
		List<MLot> newMLots = new ArrayList<MLot>();
		for (MLot mLot : allMLot) {
			if (mLot.getTransMainQty() == null || mLot.getTransMainQty().compareTo(BigDecimal.ZERO) <= 0) {
				UI.showError(Message.getString("mm.mlot_qty_less_than_or_equal_zero"));
				return false;
			}
			newMLots.add(mLot);
		}	
	    
	    List<WorkOrderBomLine> materialBomLines = (List<WorkOrderBomLine>)(List<? extends Object>)materialManager.getInput();		
	    StringBuffer sb = new StringBuffer();
	    boolean flag = true;
		for (WorkOrderBomLine materialBomLine : materialBomLines) {
			BigDecimal totalQty = BigDecimal.ZERO;
			for (MLot mLot : allMLot) {
				if (materialBomLine.getMaterialName().equals(mLot.getMaterialName())) {
					totalQty = totalQty.add(mLot.getTransMainQty());
				}
			}
			BigDecimal lackQty = materialBomLine.getTransQty().subtract(totalQty);//缺少数
			if (totalQty.compareTo(materialBomLine.getTransQty()) < 0) {
				flag = false;
				sb.append(materialBomLine.getMaterialName() + Message.getString("common.object_is_missing") + lackQty + materialBomLine.getUomId() + "! \n");
			}
		}
		if (!flag) {
//			sb.append(Message.getString("common.whether_to_continue"));
//			boolean confirmContinue = UI.showConfirm(sb.toString());
//			if (!confirmContinue) {
//			} 
			UI.showError(sb.toString());
			return false;
		}
		return true;
	}

}
