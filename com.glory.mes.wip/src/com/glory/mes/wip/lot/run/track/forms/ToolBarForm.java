package com.glory.mes.wip.lot.run.track.forms;

import java.util.Arrays;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.wip.lot.run.track.TrackContext;
import com.glory.mes.wip.lot.run.track.TrackForm;

/**
 * 按钮Form
 */
public class ToolBarForm extends TrackForm {

	public static final String ID_HEADER_TOOLBAR = "ToolBar";

	protected ToolItem itemTrackIn;
	protected ToolItem itemTrackOut;
	protected ToolItem itemAbort;
	protected ToolItem itemRefresh;
	protected ToolItem itemControlScreen;
	protected ToolItem itemClose;
	
	@Override
	public Composite createForm(Composite parent) {
		Composite form = new Composite(parent, SWT.NONE);
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 2;
		gridLayout.marginHeight = 0;
		gridLayout.verticalSpacing = 0;
		gridLayout.marginWidth = 0;
		gridLayout.horizontalSpacing = 0;
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		form.setLayout(gridLayout);
		form.setLayoutData(gd);
	
		createToolBar(form);
		return form;
	}
	
	public void createToolBar(Composite parent) {
		ToolBar tBar = new ToolBar(parent, SWT.FLAT | SWT.HORIZONTAL);
		tBar.setBackgroundImage(SWTResourceCache.getImage("background-blue"));
		createToolItemTrackIn(tBar);
		createToolItemAbort(tBar);
		createToolItemTrackOut(tBar);
		createToolItemRefresh(tBar);
		createToolItemUnFullScreen(tBar);
		createToolItemClose(tBar);
	}

	protected void createToolItemTrackIn(ToolBar tBar) {
		itemTrackIn = new ToolItem(tBar, SWT.BORDER);
		itemTrackIn.setText(Message.getString("wip.trackin"));
		itemTrackIn.setImage(SWTResourceCache.getImage("big-trackin"));
		itemTrackIn.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				context.execute(TrackContext.ACTION_TYPE_TRACKIN);
			}
		});
	}
	
	protected void createToolItemAbort(ToolBar tBar) {
		itemAbort = new ToolItem(tBar, SWT.PUSH);
		itemAbort.setText(Message.getString("wip.abort"));
		itemAbort.setImage(SWTResourceCache.getImage("big-abort"));
		itemAbort.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				context.execute(TrackContext.ACTION_TYPE_TRACKIN);
			}
		});
	}
	
	protected void createToolItemTrackOut(ToolBar tBar) {
		itemTrackOut = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemTrackOut.setText(Message.getString("wip.trackout"));
		itemTrackOut.setImage(SWTResourceCache.getImage("big-trackout"));
		itemTrackOut.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				context.execute(TrackContext.ACTION_TYPE_TRACKIN);
			}
		});
	}
	
	protected void createToolItemRefresh(ToolBar tBar) {
		itemRefresh = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemRefresh.setText("刷新");
		itemRefresh.setImage(SWTResourceCache.getImage("big-refresh"));
		itemRefresh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				context.execute(TrackContext.ACTION_TYPE_TRACKIN);
			}
		});
	}
	
	protected void createToolItemUnFullScreen(ToolBar tBar) {
		itemControlScreen = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemControlScreen.setText("取消全屏");
		itemControlScreen.setImage(SWTResourceCache.getImage("big-unfullscreen"));
		itemControlScreen.setData("0");
		itemControlScreen.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if (itemControlScreen.getData().equals("0")) {
					context.execute(TrackContext.ACTION_TYPE_TRACKIN);
				} else {
					context.execute(TrackContext.ACTION_TYPE_TRACKIN);
				}			 
			}
		});
	}
	
	protected void createToolItemClose(ToolBar tBar) {
		itemClose = new ToolItem(tBar, SWT.PUSH | SWT.ARROW);
		itemClose.setText("关闭");
		itemClose.setImage(SWTResourceCache.getImage("big-close"));
		itemClose.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				context.execute(TrackContext.ACTION_TYPE_TRACKIN);
			}
		});
	}
}
