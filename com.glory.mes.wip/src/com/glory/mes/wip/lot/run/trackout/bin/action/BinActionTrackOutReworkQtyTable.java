package com.glory.mes.wip.lot.run.trackout.bin.action;

import java.util.ArrayList;
import java.util.List;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.viewers.FixEditorTableManager;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.model.Lot;

public class BinActionTrackOutReworkQtyTable extends FixEditorTableManager {
	
	private Lot lot;
	
	public BinActionTrackOutReworkQtyTable(ADTable adTable, Lot lot) {
		super(adTable);
		this.lot = lot;
	}
	
	public List<ADBase> getFieldList(String columnId, ADField field) {
		try {
			//reserved2为返工流程
			if ("reserved2".equals(field.getName())) {
				List<ADBase> fieldList = new ArrayList<ADBase>();
				PrdManager prdManager = Framework.getService(PrdManager.class);
				StepState state = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
				List<Transition> transitions = state.getLeavingTransitions();
				List<Transition> reworkTransitions = prdManager.getReworkTransitions(state, true);
				return (List)reworkTransitions;
			} else {
				return super.getFieldList(columnId, field);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
}
