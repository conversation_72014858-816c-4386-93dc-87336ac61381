package com.glory.mes.wip.lot.run.trackout;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.ui.forms.widgets.Form;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.LotStepAttributeComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.model.Lot;
import com.google.common.base.Objects;
import com.google.common.collect.Sets;

public class TrackOutStartGlcPage extends GlcFlowWizardPage {
	
	public static final String SYSPARAM_MES_LOT_TRACKOUT_INPUT_SCRAPQTY = "mes_lot_trackout_input_scrapqty";
	
	public static String TABLE_NAME = "WIPTrackOutLot";
	
	public static String FIELD_REWORKMAINQTY = "reworkMainQty";
	public static String FIELD_SCRAPMAINQTY = "scrapMainQty";
	public static String FIELD_OUTMAINQTY = "outMainQty";
	
	protected Form form;
	protected FormToolkit toolkit;
	protected TrackOutWizard tw;
	protected TrackOutContext context;
	
	protected String tableName;
//	protected LotStepAttributeForm attributeForm;
	
	public static final String FIELD_LOTLIST = "lotList";
	public static final String FIELD_STEPATTRIBUTES = "stepAttributes";
	public static final String FIELD_REWORKAUTOMERGE = "reworkAutoMerge";
	public static final String FIELD_CURRENTBATCH = "currentBatch";

	protected ListTableManagerField lotListField;
	protected CustomField stepAttributesField;
	protected BooleanField reworkAutoMergeField;
	protected BooleanField currentBatchField;
	protected LotStepAttributeComposite attributeComposite;
	
	protected List<TrackOutLot> trackOutLots;
	protected List<StepAttribute> stepAttributes;
	
	public TrackOutStartGlcPage() {
		super();
	}
	
	public TrackOutStartGlcPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super();
	}

	@Override
	protected ADTable changeADTable(ADTable adTable) {
		try {
			tw = (TrackOutWizard) this.getWizard();
			context = (TrackOutContext)tw.getContext();
			
			List<Lot> lots = new ArrayList<Lot>();
			List<ADField> adFields = adTable.getTabs().get(0).getFields();
			if (context.getLots() != null && context.getLots().size() > 0) {
				lots.addAll(context.getLots());
			}
			trackOutLots = new ArrayList<TrackOutLot>();
			for (Lot lot : lots) {
				TrackOutLot trackOutLot = new TrackOutLot(lot);
				trackOutLot.setOutMainQty(trackOutLot.getMainQty());
				trackOutLot.setOutSubQty(trackOutLot.getSubQty());
				trackOutLots.add(trackOutLot);
			}
			
			ADTable trackOutAdTable = getTrackOutADTable(trackOutLots);
			adFields.stream().filter(field -> Objects.equal(field.getName(), FIELD_LOTLIST)).forEach(f -> {
				f.setFieldADTable(trackOutAdTable);
			});
			context.setTrackOutLots(trackOutLots);
			
			//显示Attribute信息
			Long stepRrn = context.getStep().getObjectRrn();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			stepAttributes = prdManager.getStepAttribute(stepRrn, StepAttribute.CATEGORY_TRACKOUT);
			if (CollectionUtils.isEmpty(stepAttributes)) {
				Optional<ADField> adField = adFields.stream().filter(field -> Objects.equal(field.getName(), FIELD_STEPATTRIBUTES)).findFirst();
				if (adField.isPresent()) {
					adFields.remove(adField.get());
				}
			}
			if (!context.isReworkFlag()) {
				Optional<ADField> adField = adFields.stream().filter(field -> Objects.equal(field.getName(), FIELD_REWORKAUTOMERGE)).findFirst();
				if (adField.isPresent()) {
					adFields.remove(adField.get());
				}
			}
			
			Boolean isKeepBatch = false;
			if (context.getTrackOutLots().size() > 1) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<Step> lstStep = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MAX_VALUE, 
						"objectRrn = " + stepRrn, "");
				if (lstStep != null && lstStep.size() > 0) {
					Step step = lstStep.get(0);
					if (step.getKeepBatch()) {
						isKeepBatch = true; 
					}
				}
			} else {
				isKeepBatch = false; 
			}
			
			if (!isKeepBatch) {
				Optional<ADField> adField = adFields.stream().filter(field -> Objects.equal(field.getName(), FIELD_CURRENTBATCH)).findFirst();
				if (adField.isPresent()) {
					adFields.remove(adField.get());
				}
			}
			adTable.getTabs().get(0).setFields(adFields);
			return adTable;
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return super.changeADTable(adTable);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		setTitle(Message.getString(WipExceptionBundle.bundle.WipTrackOutTitle()));
		setDescription(Message.getString(WipExceptionBundle.bundle.WipTrackOutFirstTitle()));
		lotListField = form.getFieldByControlId(FIELD_LOTLIST, ListTableManagerField.class);
		stepAttributesField = form.getFieldByControlId(FIELD_STEPATTRIBUTES, CustomField.class);
		reworkAutoMergeField = form.getFieldByControlId(FIELD_REWORKAUTOMERGE, BooleanField.class);
		currentBatchField = form.getFieldByControlId(FIELD_CURRENTBATCH, BooleanField.class);
		if (stepAttributesField != null) {
			attributeComposite = (LotStepAttributeComposite) stepAttributesField.getCustomComposite();
		}
		lotListField.getListTableManager().setInput(trackOutLots);
		//设置默认选中项
		if (context.getOutLots() != null && context.getLots()!=null) {
			List<Object> selectedTrackOutLots = new ArrayList<Object>();
			for (Lot outLot : context.getLots()) {
				for (TrackOutLot trackOutLot : trackOutLots) {
					if (trackOutLot.getLot().equals(outLot)) {
						selectedTrackOutLots.add(trackOutLot);
						break;
					}
				}
			}
			for (Object o : selectedTrackOutLots) {
				lotListField.getListTableManager().setCheckedObject(o);
			}
		}
		if (attributeComposite != null) {
			attributeComposite.setValue(stepAttributes);
			attributeComposite.setLot(context.getLots().get(0));
			attributeComposite.refresh();
		} 
	}
	
	public ADTable getTrackOutADTable(List<TrackOutLot> trackOutLots)
			throws Exception, ClientException {
		ADManager adManager = Framework.getService(ADManager.class);
		//是否出站显示报废数量框
		SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
		boolean flag = sysParameterManager.getBooleanSysParameterValue(Env.getOrgRrn(), SYSPARAM_MES_LOT_TRACKOUT_INPUT_SCRAPQTY);
		
		if (tableName == null || tableName.trim().length() == 0) {
			tableName = TABLE_NAME;
		}
		ADTable adTable = adManager.getADTable(Env.getOrgRrn(), tableName);
		List<ADField> adFields = adTable.getFields();
		for (ADField adField : adFields) {
			if (flag) { //显示报废数量框情况
				context.setInputScrap(true);
				if (FIELD_OUTMAINQTY.equals(adField.getName())) {
					//不能编辑OutMain栏位
					adField.setIsEditable(false);
					continue;
				}	
				
				if (FIELD_SCRAPMAINQTY.equals(adField.getName())) {
					//显示SCrap栏位
					adField.setIsDisplay(true);
					adField.setIsMain(true);
					adField.setIsEditable(true);
					continue;
				}			
			} else {//不显示报废数量框情况
				context.setInputScrap(false);
				if (FIELD_OUTMAINQTY.equals(adField.getName())) {
					//不能编辑OutMain栏位
					adField.setIsEditable(true);
					continue;
				}	
				
				if (FIELD_SCRAPMAINQTY.equals(adField.getName())) {
					//不显示SCrap栏位
					adField.setIsDisplay(false);
					adField.setIsMain(false);
					adField.setIsEditable(false);
					continue;
				}	
			}
			
			if (FIELD_REWORKMAINQTY.equals(adField.getName())) {
				if (isNeedRework(trackOutLots)) {
					context.setReworkFlag(true);	
					//显示Rework栏位
					adField.setIsDisplay(true);
					adField.setIsMain(true);
					adField.setIsEditable(true);
				} else {
					context.setReworkFlag(false);	
					//不显示Rework栏位
					adField.setIsDisplay(false);
					adField.setIsMain(false);
					adField.setIsEditable(false);			
				}
				continue;
			}
		}
		return adTable;
	}
	
	public boolean isNeedRework(List<TrackOutLot> trackOutLots) throws Exception {
		boolean reworkFlag = false;
		PrdManager prdManager = Framework.getService(PrdManager.class);
		for(TrackOutLot trackOutLot : trackOutLots){
		   StepState state = prdManager.getCurrentStepState(trackOutLot.getLot().getProcessInstanceRrn());
		   if (state != null && state.getIsTransitionRework()){
			   trackOutLot.setIsRework(true);
			   reworkFlag = true;
		   }
		   // pilot批次需要去在线返工界面进行返工
		   if (trackOutLot.getLot().getIsPilot()) {
			   trackOutLot.setIsRework(false);
			   reworkFlag = false;
		   }
		}
		return reworkFlag;
	}
	
	@Override
	public String doNext() {
		String returnStr = "";
		try {
	 		if (!validate()) {
	 			return "";
	 		}
	 		if (attributeComposite != null){
	 			//保存Attribute信息
				context.setLotAttributeValues((List)attributeComposite.getValue());	
			}
	 		
	 		List<TrackOutLot> trackOutLots = context.getTrackOutLots();
			List<Lot> outLots = new ArrayList<Lot>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				outLots.add(trackOutLot.getLot());
			}
			context.setTrackOutLots(trackOutLots);
			context.setOutLots(outLots);
			
			Set<String> eqpIds = Sets.newHashSet();
			for (Lot lot : context.getOutLots()) {
				if (!StringUtil.isEmpty(lot.getEquipmentId())) {
					eqpIds.add(lot.getEquipmentId());
				}
			}
			
			if (CollectionUtils.isNotEmpty(eqpIds)) {
				RASManager rasManager = Framework.getService(RASManager.class);
				
				String onlineEquipmentId = null;
				for (String eqpId : eqpIds) {
					Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), eqpId);
					if (equipment != null && Equipment.COMMUNICATION_STATE_ONLINE.equals(equipment.getCommunicationState())) {
						onlineEquipmentId = equipment.getEquipmentId();
						break;
					}
				}
				
				if (!StringUtil.isEmpty(onlineEquipmentId)) {
					if (!UI.showConfirm(String.format(Message.getString(WipExceptionBundle.bundle.WipLotTrackManualOnlineConfirm()), onlineEquipmentId) )) {
						return "";
					}
				}
			}
			
			if (reworkAutoMergeField != null) {
				if (reworkAutoMergeField.getCheckboxControl().getSelection()) {
					context.getInContent().setAutoMerge(true);
				} else {
					context.getInContent().setAutoMerge(false);
				}
			}
			
			//保持Batch到下一站
			if (currentBatchField != null) {
				if (currentBatchField.getCheckboxControl().getSelection()) {
					context.getInContent().setKeepBatch(true);
				} else {
					context.getInContent().setKeepBatch(false);
				}
			}

			//记录Comments
			List<LotAction> actions = new ArrayList<LotAction>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				if (trackOutLot.getComment() != null && trackOutLot.getComment().trim().length() > 0) {
					LotAction lotAction = new LotAction();
					lotAction.setLotRrn(trackOutLot.getLot().getObjectRrn());
					lotAction.setActionComment(trackOutLot.getComment());
					actions.add(lotAction);
				}
			}
			context.getInContent().setActions(actions);
			
			if (context.isScrapFlag()) {
				returnStr = TrackOutDialog.SCRAP_PAGE;
			} else if (context.isReworkFlag()) {
				returnStr = TrackOutDialog.REWORK_PAGE;
			} else {
				tw.invokeTrackOut();
				returnStr = TrackOutDialog.LOT_LIST_PAGE;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return returnStr;
	}

	public boolean validate() {
		List<Object> trackOutObjs = lotListField.getListTableManager().getCheckedObject();
		if (trackOutObjs == null || trackOutObjs.size() == 0) {
			setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipLotSelectAlert()));
			return false;
		}
		List<TrackOutLot> trackOutLots = new ArrayList<TrackOutLot>();
		for (Object trackOutObj : trackOutObjs) {
			TrackOutLot trackOutLot = (TrackOutLot) trackOutObj;
			trackOutLots.add(trackOutLot);
		}
		
		context.setTrackOutLots(trackOutLots);
		
		if (attributeComposite != null){
 			//检查Attribute信息
			if (attributeComposite.validate()) {
				return false;
			}					
		}
		
		//根据实际输入重置Flag
		context.setReworkFlag(false);
		context.setScrapFlag(false);
		
		for (TrackOutLot trackOutLot : trackOutLots) {
			if ((trackOutLot.getOutMainQty() == null) && (trackOutLot.getReworkMainQty() == null) && (trackOutLot.getScrapMainQty() == null)) {
				setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipLotBothBull()));
				return false;
			}
			
			if (trackOutLot.getScrapMainQty() != null) {
				if (trackOutLot.getScrapMainQty().compareTo(trackOutLot.getOutMainQty()) > 0) {
					setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipLotScrapOutQtyBigger()) + trackOutLot.getOutMainQty());
					return false;
				} else {
					//如果有填写报废数量，outMain减去报废，去走报废页
					if (trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
						trackOutLot.setOutMainQty(trackOutLot.getOutMainQty().subtract(trackOutLot.getScrapMainQty()));
					}
				}
			}
			
			if (trackOutLot.getOutMainQty() != null) {
				if (trackOutLot.getOutMainQty().compareTo(BigDecimal.ZERO) < 0) {
					setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipQtyCanNotLessZero()));
					return false;
				}
				if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) > 0) {
					setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipLotMainQtyBigger()) + trackOutLot.getMainQty());
					return false;
				}
				
				if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) < 0) {
					//出站数量小于批次数量,说明批次有报废
					context.setScrapFlag(true);
				}	
			}

			if (trackOutLot.getReworkMainQty() != null) {
				if (trackOutLot.getReworkMainQty().compareTo(BigDecimal.ZERO) < 0) {
					setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipQtyCanNotLessZero()));
					return false;
				}
				context.setReworkFlag(true);
				if (trackOutLot.getReworkMainQty().compareTo(trackOutLot.getOutMainQty()) > 0) {
					//返工数量必须小于出站数量
					setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipLotReworkBigger()) + trackOutLot.getOutMainQty());
					return false;
				}
			}
		}
		return true;
	}
	
	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

	protected List<Lot> getLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = null;
		if (lot.getBatchId() != null) {
			lotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
		} else {
			lotList = new ArrayList<Lot>();
			lot = lotManager.getLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getTableName() {
		return tableName;
	}


}