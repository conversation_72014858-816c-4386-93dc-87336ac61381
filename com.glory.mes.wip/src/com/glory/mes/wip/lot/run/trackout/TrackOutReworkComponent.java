package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.entitymanager.forms.ScorllFormComposite;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.def.TransitionRework;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class TrackOutReworkComponent extends ScorllFormComposite {

	private TrackOutContext context;
	
	public TrackOutReworkComponent(Composite parent, List<Lot> reworkLots, TrackOutContext context) {
		super(parent, reworkLots);
		this.context = context;
	}
	
	@Override
	public Composite createUnit(Composite com, Object obj) {
		Group group = new Group(com, SWT.NONE);
		GridLayout gd = new GridLayout(1, true);
		gd.numColumns = 1;
		gd.marginRight = 1;
		gd.marginLeft = 1;
		group.setLayout(gd);
		group.setLayoutData(new GridData(GridData.FILL_BOTH));
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		Lot lot = (Lot) obj;
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}
		
		TrackOutReworkComponentComposite reworkUnit = new TrackOutReworkComponentComposite(group, SWT.NONE, lot, context);
		return reworkUnit;
	}
	
	public boolean validate() {
		for (Composite unit : units) {
			TrackOutReworkComponentComposite reworkUnit = (TrackOutReworkComponentComposite)unit;
			if (!reworkUnit.validate()) {
				return false;
			}
		}
		return true;
	}
	
	public List<LotAction> getReworkLotActions() throws Exception {
		List<LotAction> reworkLotActions = new ArrayList<LotAction>();
		PrdManager prdManager = Framework.getService(PrdManager.class);
		
		for (Composite unit : units) {
			TrackOutReworkComponentComposite reworkComp = (TrackOutReworkComponentComposite)unit;				
			//按reworkTransition分类
			Map<String, List<ProcessUnit>> reworkActionMap = new HashMap<String, List<ProcessUnit>>();
			for (ProcessUnit reworkUnit : reworkComp.getReworkUnits()) {
				ComponentUnit componentUnit = (ComponentUnit)reworkUnit;
				// componentUnit.setActionCode(reworkComp.getReworkAction().getActionCode());
				if (reworkActionMap.containsKey(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition())) {
					List<ProcessUnit> list = reworkActionMap.get(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition());
					list.add(componentUnit);
					reworkActionMap.put(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition(), list);
				} else {
					List<ProcessUnit> list = new ArrayList<ProcessUnit>();
					list.add(componentUnit);
					reworkActionMap.put(componentUnit.getActionCode() + ";" + componentUnit.getReworkTransition(), list);
				}
			}	
			
			for (String key : reworkActionMap.keySet()) {
				LotAction lotAction = new LotAction();				
				lotAction.setActionType(LotAction.ACTIONTYPE_REWORK);
				lotAction.setActionCode(key.split(";")[0]);		
				lotAction.setReworkTransition(key.split(";")[1]);
				
				List<ProcessUnit> processUnits = reworkActionMap.get(key);
				String equipmentId = processUnits.get(0).getEquipmentId();
				if (StringUtil.isEmpty(equipmentId)) {
					equipmentId = reworkComp.getLot().getEquipmentId();
				}
				lotAction.setActionUnits(processUnits);
				lotAction.setEquipmentId(equipmentId);
				
				lotAction.setLotRrn(reworkComp.getLot().getObjectRrn());
				
				// 判断所选择的返工是否动态返工
				if (!StringUtil.isEmpty(lotAction.getReworkTransition())) {
					StepState state = prdManager.getCurrentStepState(reworkComp.getLot().getProcessInstanceRrn());
					Map<String, Transition> transMap = state.getLeavingTransitionsMap();
					TransitionRework transitionRework = (TransitionRework) transMap.get(lotAction.getReworkTransition());
					ReworkState reworkState = (ReworkState) transitionRework.getTo();
					// 是否动态返工
					lotAction.setReworkProcedureByActionCode(reworkState.getIsDynamic());
				}
				reworkLotActions.add(lotAction);
			}
		}
		
		return reworkLotActions;
	}
}
