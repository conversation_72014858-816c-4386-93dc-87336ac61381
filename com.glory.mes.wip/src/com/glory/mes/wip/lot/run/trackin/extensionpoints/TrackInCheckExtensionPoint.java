package com.glory.mes.wip.lot.run.trackin.extensionpoints;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.google.common.collect.Maps;

/**
 * 在进站时做的一些检查和确认
 * 可用在不同点触发,如选择设备后
 */
public class TrackInCheckExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(TrackInCheckExtensionPoint.class);
	
	private static final TrackInCheckExtensionPoint instance = new TrackInCheckExtensionPoint();
	
	/**
	 * SELECTEQP:在选中设备后触发
	 * FINISH:在Wizard performFinish方法执行前触发
	 */
	public static final String CHECK_POINT_SELECTEQP = "SELECTEQP";
	public static final String CHECK_POINT_FINISH = "FINISH";

	private static Map<String, List<ITrackInCheck>> trackInChecks = Maps.newHashMap();
	
    public final static String X_POINT = "com.glory.mes.wip.trackin.check";
    public final static String A_CHECKPOINT = "checkpoint";
    public final static String A_CLASS = "class";
    
    public static TrackInCheckExtensionPoint getInstance() {
    	return instance;
    }
    
	public static List<ITrackInCheck> getTrackInChecks(String checkPoint) {
		return trackInChecks.get(checkPoint);
	}

	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
			for (int j = 0; j < configElements.length; j++) {
				try {
					String type = configElements[j].getAttribute(A_CHECKPOINT);
					ITrackInCheck check = (ITrackInCheck)configElements[j].createExecutableExtension(A_CLASS);
					if (!trackInChecks.containsKey(type)) {
						trackInChecks.put(type, Lists.newArrayList());
					}
					trackInChecks.get(type).add(check);
				} catch (Exception e){
					logger.error("TrackInCheckExtensionPoint : init ", e);
				}
			}
		}			
	}
	
	public static TrackInContext executeTrackInCheck(TrackInContext context, String checkPoint) {
		List<ITrackInCheck> checks = getTrackInChecks(checkPoint);
		if (!CollectionUtils.isEmpty(checks)) {
			for (ITrackInCheck check : checks) {
				context = check.executeCheck(context);
				if (TrackInContext.FAILED_ID == context.getCheckCode()) {
					break;
				}
			}
		}
		return context;
	}
   
}
