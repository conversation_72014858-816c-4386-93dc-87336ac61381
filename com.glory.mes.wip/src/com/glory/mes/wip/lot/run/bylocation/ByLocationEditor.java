package com.glory.mes.wip.lot.run.bylocation;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.inject.Inject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.edc.EdcEntry;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcSetCurrentComponentDialog;
import com.glory.edc.collection.EdcSetCurrentDialog;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.application.command.OpenEditorCommand;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.extensionpoints.WizardPageExtensionPoint;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.client.SecurityManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.Location;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.state.RasState;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotPrepareManager;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.custom.depend.MsgConsoleView;
import com.glory.mes.wip.lot.run.abort.AbortDialog;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpPrepareDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackin.extensionpoints.TrackInCheckExtensionPoint;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

public class ByLocationEditor extends GlcEditor {
	
	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.run.bylocation.ByLocationEditor";

	private static final Logger logger = Logger.getLogger(ByLocationEditor.class);
	
	@Inject
	EPartService partService;
	
	@Inject
	EModelService modelService;
	
	private Equipment currentEqp;
	private boolean eqpAvailable = false;
	private Location currentLocation;
	private Location subLocation;
	
	private String defaultPageName;
	private Set<String> pageEqpGroupSet;
	
	private ToolItem itemChangeLocation;
	private ToolItem itemEqpInfo;
	private ToolItem itemReset;
	private ToolItem itemLocationEqps;
	
	private ToolItem itemTrackIn;
	private ToolItem itemPrepare;
	private ToolItem itemDcop;
	private ToolItem itemTrackOut;
	private ToolItem itemAbort;
	private ToolItem itemRunRefresh;
	
	private static final String FIELD_MAINFORM = "mainForm";
	private static final String FIELD_RUNNINGLOTS = "runningLots";
	private static final String FIELD_WAITTINGLOTS = "waittingLots";
	private static final String FIELD_EQUIPMENT_ID = "currentEqp";
	private static final String FIELD_LOCATION_ID = "currentLocation";
	
	private static final String BUTTON_CHANGELOCATION = "changeLocation";
	private static final String BUTTON_EQPINFO = "currentEqpInfo";
	private static final String BUTTON_RESET = "reset";
	private static final String BUTTON_LOCATIONEQPS = "locationEqps";
	
	private static final String BUTTON_TRACKIN = "trackin";
	private static final String BUTTON_PREPARE = "prepare";
	private static final String BUTTON_INREFRESH = "inrefresh";
	private static final String BUTTON_DOCP = "docp";
	private static final String BUTTON_TRACKOUT = "trackout";
	private static final String BUTTON_ABORT = "abort";
	private static final String BUTTON_RUNREFRESH = "runrefresh";
	
	private CustomField fieldRunning;
	protected CustomField fieldWaitting;
	private TextField fieldEqp;
	private TextField fieldLocation;
	
	protected List<Lot> holdLots;

	private ByEqpConsole console;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHANGELOCATION), this::changeLocationAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EQPINFO), this::eqpInfoAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RESET), this::resetAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_LOCATIONEQPS), this::locationEqpsAdapter);

		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_TRACKIN), this::trackInAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_PREPARE), this::prepareAdapter);

		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_INREFRESH), this::waitRefreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_DOCP), this::docpAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_TRACKOUT), this::trackOutAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_ABORT), this::abortAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_RUNREFRESH), this::runRefreshAdapter);

		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_RUNNINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED), this::runningSelectionAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED), this::waitingSelectionAdaptor);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_RUNNINGLOTS, GlcEvent.EVENT_DOUBLE_CLICK), this::doubleClickAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGLOTS, GlcEvent.EVENT_DOUBLE_CLICK), this::doubleClickAdaptor);
		
		init();
		registerExtendPages();
		addKeyListener();
		initDefault();
	}
	
	private void initDefault() {
		try {
			SecurityManager securityManager = Framework.getService(SecurityManager.class);
			ADUser adUser = securityManager.getUserByUserName(Env.getUserName());
			if (adUser != null && !StringUtil.isEmpty(adUser.getDefaultLocation())) {
				MBASManager basManager = Framework.getService(MBASManager.class);
				Location location = basManager.getLocationById(Env.getOrgRrn(), adUser.getDefaultLocation());
				if (location == null) {
					return;
				}
				this.currentLocation = location;
				
				fieldLocation.setValue(location.getName());
				fieldLocation.refresh();
				
				itemLocationEqps.setEnabled(true);
				itemReset.setEnabled(true);
				// 重新加载该区域批次
				refresh(1);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void changeLocationAdapter(Object obj) {
		ByLocationSelectLocationDialog dialog = new ByLocationSelectLocationDialog("WIPLotRunByLocationDialog", null, eventBroker);
		dialog.setOkAdaptor(this::changeOkAdaptor);
		
		if (currentLocation != null) {
			Map<String, Object> propValues = Maps.newHashMap();
			propValues.put("location", currentLocation.getName());
			if (subLocation != null) {
				//subLocation临时使用Attribute1
				propValues.put("attribute1", subLocation.getName());
			}
			dialog.setPropValues(propValues);
		}
		dialog.open();
	}
	
	private void changeOkAdaptor(Object obj) {
		GlcBaseDialog baseDialog = (GlcBaseDialog) obj;
		try {
			List<Form> subForms = baseDialog.getForm().getSubForms();
			Lot lot = new Lot();
			subForms.forEach(f -> f.setObject(lot));
			
			boolean saveFlag = true;
			for (Form subForm : subForms) {
				baseDialog.setCloseFlag(true);
				if (!subForm.saveToObject()) {
					saveFlag = false;
					baseDialog.setCloseFlag(false);
					break;
				}
			}
			
			if (saveFlag) {
				Lot newLot = (Lot) baseDialog.getForm().getSubForms().get(0).getObject();
				String locationId = newLot.getLocation();
				//subLocation临时使用Attribute1
				String subLocationId = (String) newLot.getAttribute1();
				if (StringUtil.isEmpty(locationId)) {
					baseDialog.setCloseFlag(false);
					return;
				}
				
				clear();
				
				MBASManager basManager = Framework.getService(MBASManager.class);
				Location location = basManager.getLocationById(Env.getOrgRrn(), locationId);
				
				if (!StringUtil.isEmpty(subLocationId)) {
					Location subLocation = basManager.getLocationById(Env.getOrgRrn(), subLocationId);
					this.subLocation = subLocation;
				} else {
					this.subLocation = null;
				}
				
				this.currentLocation = location;
				
				fieldLocation.setValue(location.getName());
				fieldLocation.refresh();
				
				itemLocationEqps.setEnabled(true);
				itemReset.setEnabled(true);
				// 重新加载该区域批次
				refresh(1);
			}
		} catch (Exception e) {
			baseDialog.setCloseFlag(false);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void resetAdapter(Object obj) {
		clear();
	}

	private void locationEqpsAdapter(Object obj) {
		ByLocationSelectEqpDialog dialog = new ByLocationSelectEqpDialog(UI.getActiveShell(), currentLocation, subLocation);
		if (Dialog.OK == dialog.open()) {
			Equipment equipment = dialog.getCurrentEqp();
			eqpChangeAdapter(equipment);
		}
	}
	
	/**
	 * 设备详细信息
	 * @param obj
	 */
	private void eqpInfoAdapter(Object obj) {
		if (currentEqp != null) {
			try {
				// 设备详细新Dialog
				GlcBaseDialog dialog = new GlcBaseDialog("RASEquipmentInfo", null, eventBroker);
				Map<String, Object> propValues = Maps.newHashMap();
				propValues.put("equipmentInfo", currentEqp);
				
				// 子设备信息
				RASManager rasManager = Framework.getService(RASManager.class);
				List<Equipment> subEqps = rasManager.getSubEquipments(Env.getOrgRrn(), currentEqp.getEquipmentId());
				propValues.put("otherInfo-subEquipments", subEqps);
				
				// 端口信息
				List<Port> ports = rasManager.getPortsByEquipment(Env.getOrgRrn(), currentEqp.getEquipmentId(), false);
				propValues.put("otherInfo-ports", ports);
				dialog.setPropValues(propValues);
				dialog.open();
			} catch (Exception e) {
				logger.error("Error at ByLocationEditor : eqpInfoAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
			
		}
	}
	
	@SuppressWarnings("static-access")
	protected void registerExtendPages() {}
	
	/**
	 * 进站
	 * @param obj
	 */
	protected void trackInAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			if (currentLocation == null) {
				return;
			}
			
			LotListComposite composite = (LotListComposite) fieldWaitting
					.getCustomComposite();

			List<Lot> lots = (List)composite.getCheckedObjects();
			if (lots == null || lots.size() == 0) {
				UI.showError(Message.getString("common.byEquipment_check_trackIn_lot"));
				return;
			}
			
			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYEQP);
			context.setLots(lots);
			context.setOperator1(operator1);
			
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lots.get(0).getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			if (currentEqp != null && eqpAvailable) {
				for (Lot lot : lots) {
					lot.setEquipmentId(currentEqp.getEquipmentId());
				}
				
				if (!currentEqp.getIsBatch() && lots.size() > 1) {
					UI.showError(Message.getString("wip.trackin_cannot_batch"));
					return;
				}
				
				// 检查Prepare
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				List<LotPrepare> prepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), null, false);
				if (CollectionUtils.isNotEmpty(prepares)) {
					// 首先检查是否是最小的作业号批次
					Map<String, List<LotPrepare>> prepareMap = prepares.stream().collect(Collectors.groupingBy(LotPrepare::getJobId));
					
					Optional<String> f = prepareMap.keySet().stream().min(Comparator.comparingInt(Integer::valueOf));
					if (f.isPresent()) {
						List<LotPrepare> nextPrepareBatch = prepareMap.get(f.get());
						boolean matchFlag = false;
						// 检查是否匹配批次，只要匹配搭配到一个批次号就通过
						for (LotPrepare lotPrepare : nextPrepareBatch) {
							boolean thisMatch = false;
							for (Lot lot : lots) {
								if (lotPrepare.getLotId().equals(lot.getLotId())) {
									thisMatch = true;
									break;
								}
							}
							
							matchFlag = matchFlag || thisMatch;
						}
						
						if (!matchFlag) {
							if (!UI.showConfirm(Message.getString("wip.byeqp_prepare_rule_check"))) {
								return;
							}
						}
					}
					
					// 处理Prepare进站批次
					Set<String> jobIds = Sets.newHashSet();
					int i = 0;
					for (LotPrepare lotPrepare : prepares) {
						for (Lot lot : lots) {
							if (lotPrepare.getLotId().equals(lot.getLotId())) {
								jobIds.add(lotPrepare.getJobId());
								i++;
								break;
							}
						}
					}
					
					if (jobIds.size() > 1) {
						// 不允许多个作业准备任务同时进站
						UI.showError(Message.getString("wip.byeqp_prepare_job_multi_error"));
						return;
					} else if (jobIds.size() == 1 && i != lots.size()) {
						// 不允许作业准备任务批次与无任务批次同时进站
						UI.showError(Message.getString("wip.byeqp_prepare_mix"));
						return;
					} else {
						// 没有Job不做处理
					}
				}
				
				List<Equipment> equipments = new ArrayList<Equipment>();
				ADManager adManager = Framework.getService(ADManager.class);
				currentEqp = (Equipment) adManager.getEntity(currentEqp);
				equipments.add(currentEqp);
				context.setSelectEquipments(equipments);
			}

			//选设备点进站时，做有关检验
			context = TrackInCheckExtensionPoint.executeTrackInCheck(context, TrackInCheckExtensionPoint.CHECK_POINT_SELECTEQP);
			if (TrackInContext.FAILED_ID == context.getCheckCode()) {
				return;
			}
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			

			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackInConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null && wipContext.getReturnMessage().trim().length() > 0) {
				if (console != null) {
					if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
						console.error(Message.formatString(wipContext.getReturnMessage()));
					} else {
						console.info(Message.formatString(wipContext.getReturnMessage()));
					}
				}
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				refresh(1);
				return;
			}
			
			ChainContext checkAutoFutureMergeContext = lotManager.checkAutoFutureMergeConstraint(inContext
					, Env.getSessionContext());
			if (checkAutoFutureMergeContext.getReturnMessage() != null
					&& checkAutoFutureMergeContext.getReturnMessage().trim().length() > 0) {
				UI.showError(checkAutoFutureMergeContext.getReturnMessage());
			}
			if (checkAutoFutureMergeContext.getReturnCode() == ChainContext.FAILED_ID) {
				refresh(1);
				return;
			}

			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry().get(step.getTrackInFlow());
			if (wizard instanceof TrackInWizard) {
				((TrackInWizard) wizard).setContext(context);
			}
			TrackInDialog dialog = new TrackInDialog(Display.getCurrent().getActiveShell(), wizard);
			int result = dialog.open();
			if ((result == Dialog.OK || result == TrackInDialog.FIN)
					&& context.getReturnCode() == TrackInContext.OK_ID) {
				if (console != null) {
					String message = "";
					for (Lot slot : context.getLots()) {
						if (message.isEmpty()) {
							message += "[" + slot.getLotId() + "]";
						} else {
							message += ";[" + slot.getLotId() + "]";
						}
					}
					message += " TrackIn Successful! ";
					console.info(message);
				}
				refresh(1);
			}
		} catch (Exception e) {
			logger.error("Error at ByLocationEditor : trackInAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 作业准备
	 * @param obj
	 */
	private void prepareAdapter(Object obj) {
		if (currentEqp != null && eqpAvailable) {
			try {
				// 作业准备Dialog
				ByEqpPrepareDialog dialog = new ByEqpPrepareDialog("WIPLotPrepare", null, eventBroker);
				dialog.setRTDAvailable(false);
				Map<String, Object> propValues = Maps.newHashMap();
				// 作业准备记录
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				List<LotPrepare> lotPrepares = prepareManager.getPrepareJobs(
						Env.getOrgRrn(), currentEqp.getEquipmentId(), null, true);
				propValues.put("prepareList", lotPrepares);
				// 设备信息
				propValues.put("leftFrom-equipmentInfo", currentEqp);
				
				// 可设置作业准备的批次
				LotManager lotManager = Framework.getService(LotManager.class);
				List<Lot> waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(),
							LotStateMachine.STATE_WAIT, Env.getSessionContext());
				
				waittingLots = ByLocationUtil.filterWaitingLots(waittingLots, currentEqp);
				propValues.put("leftFrom-lotList", waittingLots);
				dialog.setPropValues(propValues);
				dialog.setCloseAdaptor(new Consumer<ByEqpPrepareDialog>() {
					
					@Override
					public void accept(ByEqpPrepareDialog t) {
						refresh(2);
						
					}
				});
				dialog.open();
			} catch (Exception e) {
				logger.error("Error at ByEqpEditor : prepareAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}

	/**
	 * Wait批次列表刷新
	 * @param obj
	 */
	private void waitRefreshAdapter(Object obj) {
		refresh(2);
	}

	
	private void doubleClickAdaptor(Object obj) {
		try {
			Event event = (Event) obj;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADAuthority> authority = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + "Wip.LotDetail" + "'", "");
			if (authority.size() != 1) {
				return;
			}
			authority.get(0).setAttribute1(lot.getLotId());
			OpenEditorCommand.open(authority.get(0), partService, modelService);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 数据收集
	 * @param obj
	 */
	private void docpAdapter(Object obj) {
		try {
			if (currentEqp == null) {
				UI.showInfo("请选择测量设备！");
				return;
			}

			LotListComposite composite = (LotListComposite) fieldRunning
					.getCustomComposite();
			Lot lot = (Lot) composite.getSelectedObject();
			
			List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
			if (currents != null && currents.size() > 0) {				
				if (currents.size() == 1 
						&& !EdcSetCurrent.FLAG_DONE.equals(currents.get(0).getEdcFlag())
						&& !EdcSetCurrent.FLAG_PASS.equals(currents.get(0).getEdcFlag())){
					//当只有一个EDC时并且未完成时
					int result = EdcEntry.open(EdcData.EDCFROM_LOT, currents.get(0), null, lot);
					if (result == Dialog.OK) {
						refresh(1);
					}
				} else {
					boolean flag = false;
					for (EdcSetCurrent current : currents) {
						if (!StringUtil.isEmpty(current.getComponentUnitId())) {
							flag = true;
							break;
						}
					}
					EdcSetCurrentDialog edcSetCurrentDialog = null;
					if (flag) {
						edcSetCurrentDialog = new EdcSetCurrentComponentDialog(UI.getActiveShell(), lot, currents);
						
					} else {
						edcSetCurrentDialog = new EdcSetCurrentDialog(UI.getActiveShell(), lot, currents);	
					}
					edcSetCurrentDialog.open();
					if (edcSetCurrentDialog.getReturnCode() == Dialog.OK) {
						refresh(1);
						if (console != null) {
							String message = "";
							message += "[" + lot.getLotId() + "];";
							message += " Data Collection Successful! ";
							console.info(message);
						}
					}
				}
			} else {
				UI.showWarning(Message.getString("edc.alert_message"), Message
						.getString("edc.alert_message_title"));
			}
		} catch (Exception e) {
			logger.error("Error at ByLocationEditor : docpAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected List<EdcSetCurrent> getEdcSetCurrent(Lot lot) {
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			return edcManager.getItemSetCurrents(Env.getOrgRrn(), lot.getBatchId(), lot.getObjectRrn(), null);
		} catch (ClientException e) {
			return null;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 出站
	 * @param obj
	 */
	private void trackOutAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			form.getMessageManager().removeAllMessages();
			TrackOutContext context = new TrackOutContext();
			context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);
	
			LotListComposite composite = (LotListComposite) fieldRunning
					.getCustomComposite();
			Lot lot = (Lot) composite.getSelectedObject();
			if (lot == null) {
				return;
			}
			
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);
			context.setOperator1(operator1);

			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
	
			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry()
					.get(step.getTrackOutFlow());
			if (wizard instanceof TrackOutWizard) {
				((TrackOutWizard) wizard).setContext(context);
			}
			TrackOutDialog dialog = new TrackOutDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackOutDialog.FIN) {
				refresh(1);
				UI.showInfo(Message.getString("wip.trackout_success"));
				if (console != null) {
					String message = "";
					for (Lot slot : context.getOutLots()) {
						if (message.isEmpty()) {
							message += "[" + slot.getLotId() + "]";
						} else {
							message += ";[" + slot.getLotId() + "]";
						}
					}
					message += " TrackOut Successful! ";
					console.info(message);
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByLocationEditor : trackOutAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * 退出
	 * @param obj
	 */
	private void abortAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			form.getMessageManager().removeAllMessages();
			InContext context = new InContext();

			LotListComposite composite = (LotListComposite) fieldRunning .getCustomComposite();
			Lot lot = (Lot) composite.getSelectedObject();
			if (lot == null) {
				return;
			}
			
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);
			context.setOperator1(operator1);

			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);

			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry().get(step.getAbortFlow());
			if (wizard instanceof AbortWizard) {
				((AbortWizard) wizard).setContext(context);
			}

			AbortDialog dialog = new AbortDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == AbortDialog.FIN) {
				refresh(1);
				if (console != null) {
					String message = "";
					for (Lot slot : context.getLots()) {
						if (message.isEmpty()) {
							message += "[" + slot.getLotId() + "]";
						} else {
							message += ";[" + slot.getLotId() + "]";
						}
					}
					message += " Abort Successful! ";
					console.info(message);
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByLocationEditor : abortAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * Run批次列表刷新
	 * @param obj
	 */
	private void runRefreshAdapter(Object obj) {
		refresh(3);
	}

	/**
	 * 设备变更
	 * @param obj
	 */
	private void eqpChangeAdapter(Equipment equipment) {
		this.currentEqp = equipment;
		fieldEqp.setValue(currentEqp.getEquipmentId());
		fieldEqp.refresh();
		itemPrepare.setEnabled(true);
		
		equipmentChanged();
		
		if (console != null && getHoldLots() != null && getHoldLots().size() > 0) {
			String message = "";
			for (Lot slot : getHoldLots() ) {
				message += "[" + slot.getLotId() + "];";
			}
			message += Message.getString("wip.lot_current_state_is_hold");
			console.info(message);
		}
	}
	
	protected void equipmentChanged() {
		try {
			if (currentEqp == null) {
				itemEqpInfo.setEnabled(false);
				return;
			}
			
			boolean isDefault = true;
			if (pageEqpGroupSet != null) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put(GlcEvent.PROPERTY_DATA, currentEqp);
				if (pageEqpGroupSet.contains(currentEqp.getEqpGroup())) {
					map.put(GlcEvent.PROPERTY_PAGE_KEY, currentEqp.getEqpGroup());
					eventBroker.post(form.getFullTopic(GlcEvent.EVENT_GLC_MDFIRESECTION), map);
					isDefault = false;
				} else {
					map.put(GlcEvent.PROPERTY_PAGE_KEY, defaultPageName);
					eventBroker.post(form.getFullTopic(GlcEvent.EVENT_GLC_MDFIRESECTION), map);
				}
			}
			
			itemEqpInfo.setEnabled(true);
			RASManager ras = Framework.getService(RASManager.class);
			RasState state = ras.getState(Env.getOrgRrn(), currentEqp.getState());
			this.eqpAvailable = state.getIsAvailable();
			if (!state.getIsAvailable()) {
				if (console != null) {
					String message = "";
					message += "[" + currentEqp.getEquipmentId() + "];";
					message += Message.getString("byeqp.runninglot_eqpisnotavailable") + currentEqp.getState();
					console.error(message);
				}
			}
			
			if (isDefault) {
				refresh(1);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 刷新批次列表
	 * index 1，都刷新
	 * index 2，只刷新waiting
	 * index 3，只刷新running
	 * @param index
	 */
	public void refresh(int index) {
		try {
			if (currentLocation != null && currentLocation.getObjectRrn() != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				
				if (this.currentEqp != null && this.currentEqp.getObjectRrn() != null) {
					if (1 == index || 2 == index) {
						List<Lot> dispLots = prepareManager.getSortedLotsByEquipmentId(Env.getOrgRrn(), currentEqp.getEquipmentId());
						
						List<Lot> waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(),
									LotStateMachine.STATE_WAIT, Env.getSessionContext());
						dispLots.addAll(waittingLots);
						// 只显示当前Location批次
						dispLots = dispLots.stream().filter(l -> 
							currentLocation.getName().equals(l.getLocation())).collect(Collectors.toList());
						
						fieldWaitting.setValue(ByLocationUtil.filterWaitingLots(dispLots, currentEqp));
						fieldWaitting.refresh();
						setHoldLots(filterHoldLots(dispLots));
					} 
					
					if (1 == index || 3 == index) {
						List<Lot> runningLots = lotManager.getRunningLotsByEqp(Env.getOrgRrn(), currentEqp.getEquipmentId());
						// 只显示当前Location批次
						runningLots = runningLots.stream().filter(l -> 
							currentLocation.getName().equals(l.getLocation())).collect(Collectors.toList());
						fieldRunning.setValue(runningLots);
						fieldRunning.refresh();
					}
				} else {
					if (1 == index || 2 == index) {
						List<Lot> dispLots = lotManager.getLotsByLocation(currentLocation.getName(), LotStateMachine.STATE_DISP, Env.getSessionContext());
						List<Lot> waittingLots = lotManager.getLotsByLocation(currentLocation.getName(), LotStateMachine.STATE_WAIT, Env.getSessionContext());
						dispLots.addAll(waittingLots);
						
						fieldWaitting.setValue(ByLocationUtil.filterWaitingLots(dispLots, null));
						fieldWaitting.refresh();
						setHoldLots(filterHoldLots(dispLots));
					} 
					
					if (1 == index || 3 == index) {
						List<Lot> runningLots = lotManager.getLotsByLocation(currentLocation.getName(), LotStateMachine.STATE_RUN, Env.getSessionContext());
						fieldRunning.setValue(runningLots);
						fieldRunning.refresh();
					}
				}
			} else {
				clear();
			}
			
			form.getMessageManager().removeAllMessages();
		} catch (Exception e) {
			logger.error("Error at ByLocationEditor : refresh() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected List<Lot> filterHoldLots(List<Lot> lots) {
		List<Lot> holdLots = new ArrayList<Lot>();
		if (this.currentEqp != null && this.currentEqp.getObjectRrn() != null) {
			for (Lot lot : lots) {
				if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
				    holdLots.add(lot);
				}
			}
		}
		return holdLots;
	}
	
	private void addKeyListener() {
		LotListComposite wComposite = (LotListComposite) fieldWaitting
				.getCustomComposite();
		wComposite.getTableManager().getNatTable().addListener(SWT.KeyDown, new Listener() {
			@Override
			public void handleEvent(org.eclipse.swt.widgets.Event event) {
				switch (event.keyCode) {
				case SWT.F3:
					if (itemTrackIn != null && itemTrackIn.isEnabled()) {
						trackInAdapter(null);
					}
					break;
				case SWT.F8:
					refresh(2);
					break;
				}
			}
			
		});
		
		LotListComposite rComposite = (LotListComposite) fieldRunning
				.getCustomComposite();
		rComposite.getTableManager().getNatTable().addListener(SWT.KeyDown, new Listener() {
			public void handleEvent(org.eclipse.swt.widgets.Event e) {
				switch (e.keyCode) {
				case SWT.F4:
					if (itemDcop != null && itemDcop.isEnabled()) {
						docpAdapter(null);
					}
					break;
				case SWT.F5:
					if (itemTrackOut != null && itemTrackOut.isEnabled()) {
						trackOutAdapter(null);
					}
					break;
				case SWT.F6:
					if (itemAbort != null && itemAbort.isEnabled()) {
						abortAdapter(null);
					}
					break;
				case SWT.F8:
					if (itemRunRefresh != null && itemRunRefresh.isEnabled()) {
						refresh(3);
					}
					break;
				}
			}
		});
		
		// 通过刷新解决热键失效问题
		wComposite.getTableManager().getNatTable().refresh();
		rComposite.getTableManager().getNatTable().refresh();
	}
	
	/**
	 * Running批次列表的选中事件
	 * @param obj
	 */
	private void runningSelectionAdaptor(Object obj) {
		Event event = (Event) obj;
		Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
		if (lot != null) {
			lotStatusChanged(lot.getState(), lot.getHoldState());
		} else {
			itemDcop.setEnabled(false);
			itemTrackOut.setEnabled(false);
			itemAbort.setEnabled(false);
		}
	}
	
	/**
	 * Waiting批次列表的选中事件
	 * @param obj
	 */
	private void waitingSelectionAdaptor(Object obj) {
		try {
			Event event = (Event) obj;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lot != null) {
				lotStatusChanged(lot.getState(), lot.getHoldState());				
				
				if (console != null && currentEqp != null) {
					//查找设备recipe
					/*LotManager lotManager = Framework.getService(LotManager.class);
					List<Lot> lots = lotManager.calculatePPID(Lists.newArrayList(lot), currentEqp, Env.getSessionContext());
					if (CollectionUtils.isNotEmpty(lots)) {
						if (StringUtil.isEmpty(lots.get(0).getEquipmentRecipe())) {
							console.info("PPID: Not Found !");
						} else {
							console.info("PPID: " + lot.getEquipmentRecipe());
						}
						
					} else {
						console.info("PPID: Not Found !");
					}
					//控制台提示推荐recitle
					if (!StringUtil.isEmpty(lot.getMask())) {
						try {			
							String maskStr[] = lotManager.getLotEquipmentReticle(currentEqp.getEquipmentId(), lot, true, true);
							if (maskStr != null && maskStr.length > 0) {	
								console.info(Message.getString("ras.recommend.reticle_id")+": "+maskStr[0]);
							}										
						} catch (Exception e) {
							console.error(Message.getString(e.toString()));
						}
					}*/
					ByEqpUtil.noticeEquipmentRecipeAndReticle(lot, currentEqp, console);
				}					
			} else {
				itemTrackIn.setEnabled(false);
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	/**
	 * 批次状态变更
	 * @param state
	 * @param holdState
	 */
	private void lotStatusChanged(String state, String holdState) {
		if (currentEqp != null) {
			if (!eqpAvailable) {
				itemTrackIn.setEnabled(false);
				if (Lot.HOLDSTATE_ON.equals(holdState)) {
					itemDcop.setEnabled(false);
					itemTrackOut.setEnabled(false);
					itemAbort.setEnabled(false);
					
					if (LotStateMachine.STATE_RUN.equals(state)) {
						itemAbort.setEnabled(true);
					}
					
				} else if (LotStateMachine.STATE_WAIT.equals(state) || LotStateMachine.STATE_DISP.equals(state)) {
					itemDcop.setEnabled(false);
					itemTrackOut.setEnabled(false);
					itemAbort.setEnabled(false);
				} else if (LotStateMachine.STATE_RUN.equals(state)) {
					LotListComposite composite = (LotListComposite) fieldRunning
							.getCustomComposite();
					Lot lot = (Lot) composite.getSelectedObject();
					boolean docpFlag = false;
					if (lot != null) {
						List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
						if (currents != null && currents.size() > 0) {	
							docpFlag = true;
						}
					}
					itemDcop.setEnabled(docpFlag);					
					itemTrackOut.setEnabled(true);
					itemAbort.setEnabled(true);
				} else {
					itemDcop.setEnabled(false);
					itemTrackOut.setEnabled(false);
					itemAbort.setEnabled(false);
				}		
			} else {
				if (Lot.HOLDSTATE_ON.equals(holdState)) {
					itemDcop.setEnabled(false);
					itemTrackOut.setEnabled(false);
					itemTrackIn.setEnabled(false);
					itemAbort.setEnabled(false);
					
					if (LotStateMachine.STATE_RUN.equals(state)) {
						itemAbort.setEnabled(true);
					}
				} else if (LotStateMachine.STATE_WAIT.equals(state) || LotStateMachine.STATE_DISP.equals(state)) {
					itemTrackIn.setEnabled(true);
					itemDcop.setEnabled(false);
					itemTrackOut.setEnabled(false);
					itemAbort.setEnabled(false);
				} else if (LotStateMachine.STATE_RUN.equals(state)) {
					LotListComposite composite = (LotListComposite) fieldRunning
							.getCustomComposite();
					Lot lot = (Lot) composite.getSelectedObject();
					boolean docpFlag = false;
					if (lot != null) {
						List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
						if (currents != null && currents.size() > 0) {	
							docpFlag = true;
						}
					}
					itemTrackIn.setEnabled(false);
					itemDcop.setEnabled(docpFlag);					
					itemTrackOut.setEnabled(true);
					itemAbort.setEnabled(true);
				} else {
					itemDcop.setEnabled(false);
					itemTrackOut.setEnabled(false);
					itemTrackIn.setEnabled(false);
					itemAbort.setEnabled(false);
				}
			}
		} else {
			if (Lot.HOLDSTATE_ON.equals(holdState)) {
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				itemTrackIn.setEnabled(false);
				itemAbort.setEnabled(false);
				
				if (LotStateMachine.STATE_RUN.equals(state)) {
					itemAbort.setEnabled(true);
				}
			} else if (LotStateMachine.STATE_WAIT.equals(state) || LotStateMachine.STATE_DISP.equals(state)) {
				itemTrackIn.setEnabled(true);
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				itemAbort.setEnabled(false);
			} else if (LotStateMachine.STATE_RUN.equals(state)) {
				LotListComposite composite = (LotListComposite) fieldRunning
						.getCustomComposite();
				Lot lot = (Lot) composite.getSelectedObject();
				boolean docpFlag = false;
				if (lot != null) {
					List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
					if (currents != null && currents.size() > 0) {	
						docpFlag = true;
					}
				}
				itemTrackIn.setEnabled(false);
				itemDcop.setEnabled(docpFlag);					
				itemTrackOut.setEnabled(true);
				itemAbort.setEnabled(true);
			} else {
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				itemTrackIn.setEnabled(false);
				itemAbort.setEnabled(false);
			}
		}
		
	}
	
	private void init() {
		itemChangeLocation = (ToolItem) form.getButtonByControl(null, BUTTON_CHANGELOCATION);
		itemEqpInfo = (ToolItem) form.getButtonByControl(null, BUTTON_EQPINFO);
		itemReset = (ToolItem) form.getButtonByControl(null, BUTTON_RESET);
		itemLocationEqps = (ToolItem) form.getButtonByControl(null, BUTTON_LOCATIONEQPS);
		
		itemTrackIn = (ToolItem) form.getButtonByControl(FIELD_MAINFORM, BUTTON_TRACKIN);
		itemPrepare = (ToolItem) form.getButtonByControl(FIELD_MAINFORM, BUTTON_PREPARE);
		itemDcop = (ToolItem) form.getButtonByControl(FIELD_MAINFORM, BUTTON_DOCP);
		itemTrackOut = (ToolItem) form.getButtonByControl(FIELD_MAINFORM, BUTTON_TRACKOUT);
		itemAbort = (ToolItem) form.getButtonByControl(FIELD_MAINFORM, BUTTON_ABORT);
		itemRunRefresh = (ToolItem) form.getButtonByControl(FIELD_MAINFORM, BUTTON_RUNREFRESH);

		itemChangeLocation.setEnabled(true);
		itemEqpInfo.setEnabled(false);
		itemReset.setEnabled(false);
		itemLocationEqps.setEnabled(false);
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);
		itemTrackIn.setEnabled(false);
		itemAbort.setEnabled(false);
		itemPrepare.setEnabled(false);

		fieldRunning = form.getFieldByControlId(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_RUNNINGLOTS, CustomField.class);
		fieldWaitting = form.getFieldByControlId(FIELD_MAINFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGLOTS, CustomField.class);
		fieldEqp = form.getFieldByControlId(FIELD_EQUIPMENT_ID, TextField.class);
		fieldLocation = form.getFieldByControlId(FIELD_LOCATION_ID, TextField.class);
		
		console = (ByEqpConsole) MsgConsoleView.getInstance();
	}
	
	private void clear() {
		fieldWaitting.setValue(Lists.newArrayList());
		fieldWaitting.refresh();
		setHoldLots(Lists.newArrayList());
		fieldRunning.setValue(Lists.newArrayList());
		fieldRunning.refresh();
		
		itemLocationEqps.setEnabled(false);
		itemReset.setEnabled(false);
		itemEqpInfo.setEnabled(false);
		
		itemTrackIn.setEnabled(false);
		itemPrepare.setEnabled(false);
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);
		itemAbort.setEnabled(false);
		
		ListTableManager runningTableManager = 
				((LotListComposite)fieldRunning.getCustomComposite()).getTableManager();
		ListTableManager waittingTableManager = 
				((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
		runningTableManager.getTableManager().setSelectedObject(null);
		waittingTableManager.getTableManager().setSelectedObject(null);
		
		fieldEqp.setText(null);
		fieldLocation.setText(null);
		
		eqpAvailable = false;
		setCurrentEqp(null);
		setCurrentLocation(null);
	}
	
	public List<Lot> getRunningLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}

	public Equipment getCurrentEqp() {
		return currentEqp;
	}

	public void setCurrentEqp(Equipment currentEqp) {
		this.currentEqp = currentEqp;
	}

	public Location getCurrentLocation() {
		return currentLocation;
	}

	public void setCurrentLocation(Location currentLocation) {
		this.currentLocation = currentLocation;
	}

	public List<Lot> getHoldLots() {
		return holdLots;
	}

	public void setHoldLots(List<Lot> holdLots) {
		this.holdLots = holdLots;
	}
	
}
