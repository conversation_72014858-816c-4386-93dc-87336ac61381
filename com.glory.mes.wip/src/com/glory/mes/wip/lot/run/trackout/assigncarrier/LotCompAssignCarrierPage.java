package com.glory.mes.wip.lot.run.trackout.assigncarrier;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.ui.extensionpoints.CustomFieldExtensionPoint;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.comp.ComponentComposite;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

/**
 * 绑定载具界面
 *
 */
public class LotCompAssignCarrierPage extends FlowWizardPage {
	
	private static final String COMPOSITE_ID = "ComponentAssignCustomComposite";
	
	protected Map<String, ComponentAssignCustomComposite> lotCompositeMap = new HashMap<String, ComponentAssignCustomComposite>();
	protected List<Lot> lots;
		
	public LotCompAssignCarrierPage() {
		super();
	}
	
	public LotCompAssignCarrierPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		TrackOutWizard tw = (TrackOutWizard) this.getWizard();
		TrackOutContext context = (TrackOutContext)tw.getContext();
		lots = context.getOutLots();
		
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
		composite.setLayoutData(gd);

		createContent(composite, toolkit);
		setControl(composite);
		setPageTitle();
	}
	
	protected void setPageTitle() {
		setTitle(Message.getString("wip.trackout_assign_durable_title"));
		setMessage(Message.getString("wip.please_enter_target_durable_and_position"));
	}

	protected void createContent(Composite composite, FormToolkit toolkit) {
		try {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			for (Lot lot : lots) {
				Composite lotComposite = composite;
				if (lots.size() > 1) {
					Group group = new Group(composite, SWT.NONE);
					group.setLayout(new GridLayout(1, true));
					group.setLayoutData(new GridData(GridData.FILL_BOTH));
					group.setText(lot.getLotId());
					lotComposite = group;
				}
				ComponentAssignCustomComposite customComposite = (ComponentAssignCustomComposite) CustomFieldExtensionPoint.getCustomCompsite(COMPOSITE_ID);
				if (customComposite == null) {
					return;
				}
				lotCompositeMap.put(lot.getLotId(), customComposite);
						
				customComposite.setAttributes(getADFormAttribute());	
				customComposite.setField(new CustomField("assignCarrier", "assignCarrier", null));
				Composite form = customComposite.createForm(toolkit, lotComposite);	
				GridData gd = new GridData(GridData.FILL_BOTH);
				form.setLayoutData(gd);		
				
				List<ComponentUnit> componentUnits = componentManager.getComponentsByParentUnitRrn(lot.getObjectRrn());
				for (ComponentUnit componentUnit : componentUnits) {
					componentUnit.setLotId(lot.getLotId());
				}
				Collections.sort(componentUnits, new Comparator<ComponentUnit>() {
	                 @Override
	                 public int compare(ComponentUnit unit1, ComponentUnit unit2) {
	                	 if (StringUtil.isEmpty(unit1.getPosition()) || StringUtil.isEmpty(unit2.getPosition())) {
	                		 return unit1.getComponentId().compareTo(unit2.getComponentId());
	                	 }
	                	 if (Long.valueOf(unit1.getPosition()) >= Long.valueOf(unit2.getPosition())) {
	                		 return 1;        
	                	 } else {
	                		 return 0;
	                	 }           	       
	                 }
	             });
				
				ComponentComposite sourceComponentComposite = customComposite.getSourceComponentComposite().getComponentComposite();	
				sourceComponentComposite.initComponents(componentUnits);
				if (customComposite.getSourceComponentComposite().txtCarrierId != null) {
					customComposite.getSourceComponentComposite().txtCarrierId.setText(lot.getDurable() != null ? lot.getDurable() : "");
					customComposite.getSourceComponentComposite().txtCarrierId.setEnabled(false);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}

	public List<ADFormAttribute> getADFormAttribute() {
		List<ADFormAttribute> fieldFormAttributes = new ArrayList<ADFormAttribute>();
		ADFormAttribute showSourceCheckBoxAttribute = new ADFormAttribute();
		showSourceCheckBoxAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_SOURCE_SHOW_CHEKBOX);
		showSourceCheckBoxAttribute.setValue("Y");
		fieldFormAttributes.add(showSourceCheckBoxAttribute);
		ADFormAttribute showSourceCarrierAttribute = new ADFormAttribute();
		showSourceCarrierAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_SOURCE_SHOW_CARRIER);
		showSourceCarrierAttribute.setValue("Y");
		fieldFormAttributes.add(showSourceCarrierAttribute);				
		ADFormAttribute showSourceCarrierSizeAttribute = new ADFormAttribute();
		showSourceCarrierSizeAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_SOURCE_CARRIER_SIZE);
		showSourceCarrierSizeAttribute.setValue("25");
		fieldFormAttributes.add(showSourceCarrierSizeAttribute);
		ADFormAttribute showSourceIsAscAttribute = new ADFormAttribute();
		showSourceIsAscAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_SOURCE_IS_ASC);
		showSourceIsAscAttribute.setValue("N");
		fieldFormAttributes.add(showSourceIsAscAttribute);			
		ADFormAttribute showTargetCheckBoxAttribute = new ADFormAttribute();
		showTargetCheckBoxAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_TARGET_SHOW_CHEKBOX);
		showTargetCheckBoxAttribute.setValue("Y");
		fieldFormAttributes.add(showTargetCheckBoxAttribute);
		ADFormAttribute showTargetCarrierAttribute = new ADFormAttribute();
		showTargetCarrierAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_TARGET_SHOW_CARRIER);
		showTargetCarrierAttribute.setValue("Y");
		fieldFormAttributes.add(showTargetCarrierAttribute);
		ADFormAttribute showTargetCarrierSizeAttribute = new ADFormAttribute();
		showTargetCarrierSizeAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_TARGET_CARRIER_SIZE);
		showTargetCarrierSizeAttribute.setValue("25");
		fieldFormAttributes.add(showTargetCarrierSizeAttribute);
		ADFormAttribute showTargetIsAscAttribute = new ADFormAttribute();
		showTargetIsAscAttribute.setAttributeName(ComponentAssignCustomComposite.ATTRIBUTE_TARGET_IS_ASC);
		showTargetIsAscAttribute.setValue("N");
		fieldFormAttributes.add(showTargetIsAscAttribute);
		return fieldFormAttributes;
	}
	
	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		setErrorMessage(null);
		setMessage(null);
		return super.doPrevious();
	}
	
	@Override
	public String doNext() {
		try {			
			TrackOutWizard wizard = ((TrackOutWizard)this.getWizard());
			//不同的批次的组件对应不同的位置
			Map<String, List<ComponentUnit>> durableCompMap = new HashMap<String, List<ComponentUnit>>();
			for (Lot lot : wizard.getContext().getLots()) {
				ComponentAssignCustomComposite customComposite = lotCompositeMap.get(lot.getLotId());
				if (customComposite.getTargetComponentComposite().txtCarrierId != null 
						&& StringUtil.isEmpty(customComposite.getTargetComponentComposite().txtCarrierId.getText())) {
					UI.showError(Message.getString("wip.sort_please_enter_target_durable"));
					return null;
				}
				List<ComponentUnit> targetComponents = (List<ComponentUnit>) customComposite.getTargetComponentComposite().getTableManager().getInput();	
				targetComponents = targetComponents.stream()
						.filter(o -> !StringUtil.isEmpty(o.getComponentId())).collect(Collectors.toList());
				if (targetComponents == null || targetComponents.size() != lot.getMainQty().intValue() ) {
					UI.showError(Message.getString("wip.please_all_component_assign_target_durable"));
					return null;
				}
				durableCompMap.put(customComposite.getTargetComponentComposite().txtCarrierId.getText(), targetComponents);
			}
			
			wizard.getContext().setComponentUnitMap(durableCompMap);
			wizard.invokeTrackOut();
			return getDefaultDirect();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

	@Override
	public boolean canFlipToNextPage() {
		return true;
	}
	
}
