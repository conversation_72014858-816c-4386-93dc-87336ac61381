package com.glory.mes.wip.lot.run.trackout.carrierbind;

import java.util.ArrayList;
import java.util.List;

import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.run.trackout.TrackOutReworkPage;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

/**
 * 重载的TrackOutReworkPage，改变跳转页面
 * <AUTHOR>
 *
 */
@Deprecated
public class CarriersBindReworkPage extends TrackOutReworkPage {
	
	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if(ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())){
				if (!reworkComponent.validate()) {
					return "";
				}
				if(!checkReworkQty()){
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> reworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							reworkLotActions.add(lotAction);
						}
					}
					list.removeAll(reworkLotActions);
					list.addAll(reworkComponent.getReworkLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkComponent.getReworkLotActions());
				}
			} else {
				if(!checkReworkQty()){
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> reworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							reworkLotActions.add(lotAction);
						}
					}
					list.removeAll(reworkLotActions);
					list.addAll(reworkQty.getReworkLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkQty.getReworkLotActions());
				}
			}
			// 与原版不同之处
			// ((TrackOutWizard)this.getWizard()).invokeTrackOut();
			return getDefaultDirect();
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

}
