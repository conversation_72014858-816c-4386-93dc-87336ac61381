package com.glory.mes.wip.lot.run.track.extensionpoints;

import java.util.ArrayList;
import java.util.List;

public class TrackEditorConfig {

	private int headerYGrid = 1;

	private Integer headerHeigthHint;

	private List<TrackFormConfig> headerFormConfigs = new ArrayList<TrackFormConfig>();

	private int bodyYGrid;

	private Integer bodyHeigthHint;
	
	private List<TrackFormConfig> bodyFormConfigs = new ArrayList<TrackFormConfig>();

	public int getHeaderYGrid() {
		return headerYGrid;
	}

	public void setHeaderYGrid(int headerYGrid) {
		this.headerYGrid = headerYGrid;
	}

	public Integer getHeaderHeigthHint() {
		return headerHeigthHint;
	}

	public void setHeaderHeigthHint(Integer headerHeigthHint) {
		this.headerHeigthHint = headerHeigthHint;
	}

	public List<TrackFormConfig> getHeaderFormConfigs() {
		return headerFormConfigs;
	}

	public void setHeaderFormConfigs(List<TrackFormConfig> headerFormConfigs) {
		this.headerFormConfigs = headerFormConfigs;
	}

	public int getBodyYGrid() {
		return bodyYGrid;
	}

	public void setBodyYGrid(int bodyYGrid) {
		this.bodyYGrid = bodyYGrid;
	}

	public Integer getBodyHeigthHint() {
		return bodyHeigthHint;
	}

	public void setBodyHeigthHint(Integer bodyHeigthHint) {
		this.bodyHeigthHint = bodyHeigthHint;
	}

	public List<TrackFormConfig> getBodyFormConfigs() {
		return bodyFormConfigs;
	}

	public void setBodyFormConfigs(List<TrackFormConfig> bodyFormConfigs) {
		this.bodyFormConfigs = bodyFormConfigs;
	}
	
	public void validate() {
		//检查设置有效性,如果为进行设置则使用默认设置
		if (headerFormConfigs.isEmpty()) {
			//默认显示输入和ToolBar
		}
		if (bodyFormConfigs.isEmpty()) {
			//默认显示批次基本信息
		}
	}
	
	
}
