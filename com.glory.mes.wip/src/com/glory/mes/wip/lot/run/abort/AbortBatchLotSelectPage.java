package com.glory.mes.wip.lot.run.abort;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.InContext;

public class AbortBatchLotSelectPage extends BatchLotSelectPage {
	
	private static final Logger logger = Logger.getLogger(AbortBatchLotSelectPage.class);
	private static String TABLE_NAME = "WIPLotAbort";
	
//	private AbortBatchLotSelectSection section;
	protected IManagedForm form;
	protected AbortWizard aw;
	protected InContext context;
	protected FormToolkit toolkit;
	protected CheckBoxFixEditorTableManager tableManager;
	protected Button checkboxKeepBatch;
	
	public AbortBatchLotSelectPage() {
		super();
	}
	
	public AbortBatchLotSelectPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		try {
			aw = (AbortWizard) this.getWizard();
			context = (InContext)aw.getContext();
			toolkit = new FormToolkit(parent.getDisplay());
			
			ADManager adManager = Framework.getService(ADManager.class);
			//ADTable adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			setTitle(Message.getString("wip.abort_lot"));
			
			Composite composite = toolkit.createComposite(parent, SWT.NULL);
			GridLayout layout = new GridLayout();
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			composite.setLayoutData(gd);
			
			Composite tableComp = toolkit.createComposite(composite, SWT.NONE);
			tableComp.setLayout(new GridLayout(1, true));
			tableComp.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			createLotListComposite(tableComp);
			
			ScrolledForm sForm = toolkit.createScrolledForm(composite);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			Composite body = sForm.getForm().getBody();
			form = new ManagedForm(toolkit, sForm);
			configureBody(body);

//			section = new AbortBatchLotSelectSection(adTable, this);
//			section.createContents(form, body);
			
			setControl(composite);
			setPageComplete(true);
			setTitle(Message.getString("wip.abort"));
			setDescription(Message.getString("wip.abort_select_lot"));
		} catch (Exception e) {
			logger.error("ShowStepOperationForm : addFields()",e);
		}
		
	}
	
	protected void createLotListComposite(Composite parent)  throws Exception {
		//获得需要TrackOut的批次
		List<Lot> lots = new ArrayList<Lot>();
		if (context.getLots() != null && context.getLots().size() > 0) {
		    for (Lot lot : context.getLots()) {
		    	//临时存储LotComment
		    	lot.setAttribute1(lot.getLotComment());
		        lot.setLotComment(null);
		        lots.add(lot);
		    }
		}

		ADManager adManager = Framework.getService(ADManager.class);
		ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		tableManager = new CheckBoxFixEditorTableManager(adTable);
		tableManager.newViewer(parent);
		tableManager.setInput(lots);
		
		//显示保持Batch控件
		if (context.getLots() != null && context.getLots().size() > 0) {
			Step currentStep = context.getCurrentStep();
			if (currentStep == null) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				currentStep = new Step();
				currentStep.setObjectRrn(context.getLots().get(0).getStepRrn());
				currentStep = (Step) prdManager.getSimpleProcessDefinition(currentStep, false);
			}
			if (currentStep != null && currentStep.getKeepBatch()) {
				checkboxKeepBatch = toolkit.createButton(parent, Message.getString("wip.bylot.trackout_keepcurrentbatch"), SWT.CHECK);
				checkboxKeepBatch.setSelection(true);
			}
		}
		
		//设置默认选中项
		if (context.getLots() != null) {
			List<Lot> selectedAbortLots = new ArrayList<Lot>();
			for (Lot lot : context.getLots()) {
				for (Lot abortLot : lots) {
					if (abortLot.equals(lot)) {
						selectedAbortLots.add(abortLot);
						break;
					}
				}
			}
			for(Lot lot : selectedAbortLots) {
				tableManager.checkObject(lot);
			}
		}
		
		context.setLots(lots);
	}

	@SuppressWarnings("unchecked")
    @Override
	public String doNext() {
	    InContext context = ((AbortWizard)getWizard()).getContext();

		List<Object> abortLots = tableManager.getCheckedObject();
		if(abortLots != null && abortLots.size() > 0){
			List<Lot> lots = new ArrayList<Lot>();
			List<LotAction> actions = new ArrayList<LotAction>();
			for (Object abortObj : abortLots) {
				Lot abortLot = (Lot) abortObj;
				if (checkAbortCommentsNotNull(abortLot)) {
					UI.showError(Message.getString("wip.abort_comments_null"));
					return "";
				}
				if (abortLot.getLotComment() != null && abortLot.getLotComment().trim().length() > 0) {
					LotAction lotAction = new LotAction();
					lotAction.setLotRrn(abortLot.getObjectRrn());
					lotAction.setActionCode("AbortLot");
					lotAction.setActionComment(abortLot.getLotComment());
					
					List<ProcessUnit> units = abortLot.getSubProcessUnit();
					if (CollectionUtils.isNotEmpty(units)) {
						lotAction.setEquipmentId(units.get(0).getEquipmentId());
					}
					
					if (StringUtil.isEmpty(abortLot.getEquipmentId())) {
						lotAction.setEquipmentId(abortLot.getEquipmentId());
					}
					abortLot.setLotComment(DBUtil.toString(abortLot.getAttribute1()));
					actions.add(lotAction);
				}
			}
			lots.addAll((List<Lot>)(List)abortLots);
			if (checkboxKeepBatch != null) {
				context.setKeepBatch(checkboxKeepBatch.getSelection());
			}
			context.setLots(lots);
			context.setActions(actions);
			if (lots.size() == 1 && !StringUtil.isEmpty(lots.get(0).getBatchId())) {
				UI.showInfo(Message.getString("wip.lot_single_abort_clean_batch_id"));
			}
			return "finish";
		} else {
			setErrorMessage(Message.getString("wip.lot_select_alert"));
			return "null";
		}
	}

	@Override
	public String doPrevious() {
		return "";
	}
	
	public IWizardPage getPreviousPage() {
		return null;
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	private boolean checkAbortCommentsNotNull(Lot abortLot) {
		boolean flag = false;
		String comment = abortLot.getLotComment();
		if (comment == null || comment.trim().length() == 0) {
			flag = true;
		}
		return flag;
	}
}
