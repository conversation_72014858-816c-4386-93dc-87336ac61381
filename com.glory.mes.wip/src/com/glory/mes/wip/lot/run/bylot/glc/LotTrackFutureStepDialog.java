package com.glory.mes.wip.lot.run.bylot.glc;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.lot.detail.DetailLotEquipmentDialog;
import com.glory.mes.wip.model.Lot;

public class LotTrackFutureStepDialog extends GlcBaseDialog{
	
	private static final String FIELD_FUTURE_STEP = "lotTrackFutureStep";
	
	protected ListTableManagerField listTableManagerField;

	private Lot lot;
	private List<String> actionTypes;
	
	public LotTrackFutureStepDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
		this.setBlockOnOpen(false);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		listTableManagerField = form.getFieldByControlId(FIELD_FUTURE_STEP, ListTableManagerField.class);
		
		listTableManagerField.getListTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable arg0, MouseEvent arg1) {
				Lot lot = (Lot) listTableManagerField.getListTableManager().getSelectedObject();
				DetailLotEquipmentDialog baseDialog = new DetailLotEquipmentDialog("WIPTrackInFutureSelEqpInfo", null, eventBroker, lot);
				if (Dialog.OK == baseDialog.open()) {
					
				}
			}				
		});
		
		init();
	}
	
	private void init() {
        if (lot != null && lot.getObjectRrn() != null) {
            try {
                LotManager manager = Framework.getService(LotManager.class);
                PrdManager prdManager = Framework.getService(PrdManager.class);
                Display.getDefault().asyncExec(new Runnable() {

                    @Override
                    public void run() {
                        // 获得批次当前参数
                        Map<String, Object> lotParamMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
                        List<Lot> currentList = manager.getLotFutureSteps(lot, lotParamMap, false, true, false, 10, true);
                        List<FutureAction> futureActions = manager.getLotFutureActions(
                        		lot, 10, getActionTypes());
                        List<Lot> adList = new ArrayList<Lot>();
                        for (int i = 0; i < currentList.size(); i++) {
                        	//显示未来动作
                        	for (FutureAction futureAction : futureActions) {
                        		if (currentList.get(i).getStepName().equals(futureAction.getStepName())) {
                        			currentList.get(i).setAttribute3(currentList.get(i).getAttribute3() == null ? futureAction.getAction() : currentList.get(i).getAttribute3()+","+futureAction.getAction());
                        			currentList.get(i).setAttribute4(currentList.get(i).getAttribute4() == null ? futureAction.getDescription() : currentList.get(i).getAttribute4()+","+futureAction.getDescription());
								}
                        	}
                            if (i == 0) {
                                currentList.get(i).setAttribute1("Y");
                            }
                            currentList.get(i).setTrackInTime(null);
                            currentList.get(i).setTrackOutTime(null);
						
                            // 添加显示设备能力
                            StepState stepState = (StepState) currentList.get(i).getData("STEP");
                            currentList.get(i).setAttribute2(stepState.getUsedStep().getCapabilityName());
					}

                        adList.addAll(currentList);
                        listTableManagerField.getListTableManager().setInput(adList);
                        listTableManagerField.getListTableManager().refresh();
				}
                });

            } catch (Exception e) {
                ExceptionHandlerManager.asyncHandleException(e);
            } finally {
			}
		}
    }
	
	public List<String> getActionTypes() {
		actionTypes = new ArrayList<String>();
		this.actionTypes.add(FutureAction.ACTION_NOTE);
		this.actionTypes.add(FutureAction.ACTION_HOLD);
		this.actionTypes.add(FutureAction.ACTION_TIMER);
		this.actionTypes.add(FutureAction.ACTION_MERGE);
		this.actionTypes.add(FutureAction.ACTION_SKIP);
		return actionTypes;
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	@Override
	protected void cancelPressed() {
		super.cancelPressed();
	}
	
	@Override
	 protected Point getInitialSize() {
	    return new Point(1600,800);
	 }
}
