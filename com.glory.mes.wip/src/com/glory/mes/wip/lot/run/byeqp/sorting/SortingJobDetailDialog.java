package com.glory.mes.wip.lot.run.byeqp.sorting;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.glory.mes.wip.sorting.LotSortingJobDetail;
import com.glory.framework.core.exception.ExceptionBundle;

public class SortingJobDetailDialog extends GlcBaseDialog{

	private static final String FIELD_DETAIL = "Detail";
	
	private ListTableManagerField listTableManagerField;
	
	private LotSortingJob sortingJob;

	public SortingJobDetailDialog(String adFormName, String authority, IEventBroker eventBroker, LotSortingJob sortingJob) {
		super(adFormName, authority, eventBroker);
		this.sortingJob = sortingJob;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		listTableManagerField = form.getFieldByControlId(FIELD_DETAIL, ListTableManagerField.class);
		init();
	}

	private void init() {
		try {
        	if (sortingJob != null && sortingJob.getObjectRrn() != null) {
        		ADManager adManager = Framework.getService(ADManager.class);
            	List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, 
            			Integer.MAX_VALUE, " jobRrn = " + sortingJob.getObjectRrn(), "");
            	orderbySortingJobDetail(details);
            	listTableManagerField.setValue(details);
            	listTableManagerField.refresh();
        	} else {
        		listTableManagerField.setValue(new ArrayList<LotSortingJobDetail>());
        		listTableManagerField.refresh();
        	} 	
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
	}
	
	/**
	 * JAVA内置排序ComponentUnit排序
	 * @ComponentUnit
	 */
   private void orderbySortingJobDetail(List<LotSortingJobDetail> list) {
		Collections.sort(list ,new Comparator<LotSortingJobDetail>() {
			public int compare(LotSortingJobDetail o1,LotSortingJobDetail o2) {
				if(o2.getComponentId().compareTo(o1.getComponentId()) < 0) {
					return 1;
				}else if (o2.getComponentId().compareTo(o1.getComponentId()) == 0) {
					return 0;
				}else {
					return -1;
				}
			}
		});
	}
   
   @Override
   protected void createButtonsForButtonBar(Composite parent) {
	   SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
			   Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
	   
	   FormData fd = new FormData();
	   fd.width = 90;
	   fd.height = 35;
	   fd.top = new FormAttachment(0, 15);
	   fd.right = new FormAttachment(100, -12);
	   fd.bottom = new FormAttachment(100, -15);
	   cancel.setLayoutData(fd);
   }
   
   @Override
	protected Point getInitialSize() {
		return new Point(1200,800);
	}
}
