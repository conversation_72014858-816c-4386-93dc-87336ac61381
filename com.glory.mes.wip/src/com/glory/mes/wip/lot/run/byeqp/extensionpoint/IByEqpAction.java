package com.glory.mes.wip.lot.run.byeqp.extensionpoint;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;

import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpEditor;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManager;

public interface IByEqpAction {
	
	/**
	 * 获取该Action名称
	 * @return
	 */
	String getActionName();
	
	/**
	 * 获取动态表单名称
	 * @return
	 */
	String getADFormName();
	
	/**
	 * 获取动态表单
	 * @return
	 */
	ADForm getADForm();
	
	/**
	 * 初始化扩展方法
	 * @param byEqpEditor
	 */
	void initExtend(ByEqpEditor byEqpEditor, GlcForm form);
	
	/**
	 * 事件订阅扩展
	 * @param byEqpEditor
	 */
	void subscribeExtend(IEventBroker eventBroker, GlcForm form);
	
	/**
	 * 事件取消订阅，必须要取消
	 */
	void unSubscribeExtend();
	
	
	
	void setPartService(EPartService partService);

	void setModelService(EModelService modelService);
	
	ByEqpManager getManager();
	
	void setManager(ByEqpManager managerDefault);
}
