package com.glory.mes.wip.lot.run.manul.movenext;

import java.util.function.Consumer;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotManualMoveNextEditor extends GlcEditor {

    public static final String  EDITOR_ID       = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.run.manul.movenext.LotManualMoveNextEditor";

    private static final String BUTTON_RELEASE    = "release";

    private static final String BUTTON_REWORK     = "rework";

    private static final String BUTTON_SPECIAL_REWORK     = "specialRework";
    
    private static final String FIELD_QUERYFORM = "HoldLotQuery";
    
    private static final String FORM_RELEASEFORM = "WIPLotReleaseForm";

    private static final String FORM_REWORKFORM  = "LotManualTrackMoveReworkGLC";

	private QueryFormField queryFormField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

        queryFormField = form.getFieldByControlId(FIELD_QUERYFORM, QueryFormField.class);

        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RELEASE), this::releaseAdaptor);

        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REWORK), this::reworkAdaptor);
        
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SPECIAL_REWORK), this::specialReworkAdaptor);

	}

    private void releaseAdaptor(Object obj) {
		try {
            Object selectedObject = queryFormField.getQueryForm().getTableManager().getSelectedObject();

            if (selectedObject == null) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }

            Lot lot = (Lot) selectedObject;

            if (!StringUtils.equals(lot.getHoldState(), Lot.HOLDSTATE_ON)) {
                UI.showInfo(Message.getString("wip.release_lothold_not_found"));
                return;
            }
            
            if (StringUtils.equals(lot.getState(), LotStateMachine.STATE_TRACKOUT) && StringUtils.equals(lot.getHoldState(), Lot.HOLDSTATE_ON)) {
            	boolean releaseFlag = UI.showConfirm(Message.getString("wip.trackouthold_release_continue"));
            	if (!releaseFlag) return;
            }

            Event event = (Event) obj;
            LotReleaseDialog baseDialog = new LotReleaseDialog(FORM_RELEASEFORM, null, eventBroker,
                                                               (Lot) selectedObject, event);
            if (Dialog.OK == baseDialog.open()) {
                queryFormField.refresh();
            }

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

    private void reworkAdaptor(Object obj) {
        try {
            Event event = (Event) obj;
            String operator1 = Env.getUserName();
            if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
                operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
            }

            Object selectedObject = queryFormField.getQueryForm().getTableManager().getSelectedObject();

            if (selectedObject == null) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }

            Lot lot = (Lot) selectedObject;

            if (!(StringUtils.equals(lot.getHoldState(), Lot.HOLDSTATE_ON) && StringUtils.equals(lot.getState(), LotStateMachine.STATE_TRACKOUT))
                && !StringUtils.equals(lot.getState(), LotStateMachine.STATE_WAIT)) {
                UI.showInfo(Message.getString("wip.lot_state_not_allow"));
                return;
            }

            lot.setOperator1(operator1);

            LotReworkDialog dialog = new LotReworkDialog(FORM_REWORKFORM, null, eventBroker, lot,  true, super.mPart);

            // 解决窗体返回 父级窗体不刷新问题
            dialog.setCloseAdaptor(new Consumer<LotReworkDialog>() {
				
				@Override
				public void accept(LotReworkDialog t) {
					queryFormField.refresh();
					
				}
			});
            if (Dialog.OK == dialog.open()) {
            	queryFormField.refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }

	}
    
    private void specialReworkAdaptor(Object obj) {
    	try {
    		Event event = (Event) obj;
    		String operator1 = Env.getUserName();
    		if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
    			operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
    		}
    		
    		Object selectedObject = queryFormField.getQueryForm().getTableManager().getSelectedObject();
    		
    		if (selectedObject == null) {
    			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
    			return;
    		}
    		
    		Lot lot = (Lot) selectedObject;
    		
    		if (!(StringUtils.equals(lot.getHoldState(), Lot.HOLDSTATE_ON) && StringUtils.equals(lot.getState(), LotStateMachine.STATE_TRACKOUT))
    				&& !StringUtils.equals(lot.getState(), LotStateMachine.STATE_WAIT)) {
    			UI.showInfo(Message.getString("wip.lot_state_not_allow"));
    			return;
    		}
    		
    		lot.setOperator1(operator1);
    		
    		LotReworkDialog dialog = new LotReworkDialog(FORM_REWORKFORM, null, eventBroker, lot,  false, super.mPart);
    		
    		// 解决窗体返回 父级窗体不刷新问题
    		dialog.setCloseAdaptor(new Consumer<LotReworkDialog>() {
    			
    			@Override
    			public void accept(LotReworkDialog t) {
    				queryFormField.refresh();
    				
    			}
    		});
    		if (Dialog.OK == dialog.open()) {
    			queryFormField.refresh();
    		}
    	} catch (Exception e) {
    		ExceptionHandlerManager.asyncHandleException(e);
    	}
    	
    }


}
