package com.glory.mes.wip.lot.run.trackout.multieqp;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutLot;
import com.glory.mes.wip.lot.run.trackout.TrackOutLotTable;
import com.glory.mes.wip.lot.run.trackout.TrackOutStartPage;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;

public class MultiEqpTrackOutStartPage extends TrackOutStartPage {

	private static final String TRACK_IN_WIZARD = "MultiEqpTrackIn";
	
	protected Button splitOut;

	@Override
	public void createLotListComposite(Composite parent) throws Exception {
		// 获得需要TrackOut的批次
		List<Lot> lots = new ArrayList<Lot>();
		List<Lot> tempLots = cloneTempLotByContext();
		if (CollectionUtils.isEmpty(tempLots)) {
			throw new ClientException(Message.getString("wip.multi_eqp_no_trackin_records"));
		} else {
			context.setLots(tempLots);
		}

		if (context.getLots() != null && context.getLots().size() > 0) {
			orderByEqpId(context.getLots());
			lots.addAll(context.getLots());
		}

		List<TrackOutLot> trackOutLots = new ArrayList<TrackOutLot>();
		for (Lot lot : lots) {
			TrackOutLot trackOutLot = new TrackOutLot(lot);
			trackOutLots.add(trackOutLot);
		}

		ADTable adTable = getTrackOutADTable(trackOutLots);
		tableManager = new TrackOutLotTable(adTable);
		tableManager.newViewer(parent);
		tableManager.setEditorObject(trackOutLots.stream().collect(Collectors.toList()));
		tableManager.setInput(trackOutLots);

		// 设置默认选中项
		tableManager.setCheckedObject(trackOutLots.stream().collect(Collectors.toList()));
		context.setTrackOutLots(trackOutLots);
	}

	@Override
	public void createControl(Composite parent) {
		try {
			tw = (TrackOutWizard) this.getWizard();
			context = (TrackOutContext) tw.getContext();
			toolkit = new FormToolkit(parent.getDisplay());

			Composite composite = toolkit.createComposite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			setControl(composite);

			Composite tableComp = toolkit.createComposite(composite, SWT.NONE);
			tableComp.setLayout(new GridLayout(1, true));
			tableComp.setLayoutData(new GridData(GridData.FILL_BOTH));
			createLotListComposite(tableComp);

			// 显示Attribute信息
			Long stepRrn = context.getStep().getObjectRrn();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			List<StepAttribute> stepAttributes = prdManager.getStepAttribute(stepRrn, StepAttribute.CATEGORY_TRACKOUT);
			if (stepAttributes != null && stepAttributes.size() > 0) {
				Group attribute = new Group(composite, SWT.NONE);
				attribute.setText(Message.getString("common.attribute"));
				attribute.setLayout(layout);
				attribute.setLayoutData(gd);

				Equipment eqp = null;
				if (context.getSelectEquipments() != null && context.getSelectEquipments().size() > 0) {
					eqp = context.getSelectEquipments().get(0);
				}

				if (context.getLots() != null && context.getLots().size() > 0) {
					attributeForm = new LotStepAttributeForm(attribute, SWT.NONE, null, context.getLots().get(0),
							stepAttributes, eqp);
					attributeForm.createForm();
					attributeForm.setLayoutData(gd);
				}
			}

			// 显示其它部分
			Composite labCom = toolkit.createComposite(composite, SWT.NONE);
			labCom.setLayout(new GridLayout(1, true));
			labCom.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			if (context.isReworkFlag()) {
				reworkAutoMerge = toolkit.createButton(labCom, Message.getString("common.rework_auto_merger"),
						SWT.CHECK);
			}

			splitOut = toolkit.createButton(labCom, Message.getString("wip.multi_eqp_split_trackout"), SWT.CHECK);
			// 显示保持Batch控件
			if (context.getTrackOutLots().size() > 1) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<Step> lstStep = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MAX_VALUE,
						"objectRrn = " + stepRrn, "");
				if (lstStep != null && lstStep.size() > 0) {
					Step step = lstStep.get(0);
					if (step.getKeepBatch()) {
						saveCurrentBatch = toolkit.createButton(labCom,
								Message.getString("wip.bylot.trackout_keepcurrentbatch"), SWT.CHECK);
						saveCurrentBatch.setSelection(true);
					}
				}
			}

			setTitle(Message.getString("wip.trackout_title"));
			setDescription(Message.getString("common.trackOut_first_title"));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * 按照EQP ID排序
	 * 
	 * @param lots
	 */
	protected void orderByEqpId(List<Lot> lots) {
		Comparator<Lot> comparator = new Comparator<Lot>() {
			@Override
			public int compare(Lot lot1, Lot lot2) {
				return lot1.getEquipmentId().compareTo(lot2.getEquipmentId());
			}
		};
		Collections.sort(lots, comparator);
	}

	protected List<Lot> cloneTempLotByContext() {
		try {
			Lot lot = context.getLots().get(0);
			LotManager lotManager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);

			List<ProcessUnit> processUnits = null;
			// 查询带Component的lot
			if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
				lot = lotManager.getLot(lot.getObjectRrn());
				List<QtyUnit> qtyUnits = adManager.getEntityList(Env.getOrgRrn(), QtyUnit.class, Integer.MAX_VALUE,
						" parentUnitRrn = " + lot.getObjectRrn() + " AND processState = '"
								+ QtyUnit.PROCESS_STATE_RUN + "'",
						"");
				processUnits = Lists.newArrayList(qtyUnits);
			} else {
				lot = lotManager.getLotWithComponent(lot.getObjectRrn());
				processUnits = lot.getSubProcessUnit();
				processUnits = processUnits.stream().filter(p -> p.getEquipmentId() != null)
						.collect(Collectors.toList());
			}

			if (processUnits == null || processUnits.size() == 0) {
				return null;
			}

			List<Lot> tempLotList = new ArrayList<Lot>();
			long i = 1;
			if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
				for (ProcessUnit qtyUnit : processUnits) {
					Lot tempLot = (Lot) lot.clone();
					tempLot.setObjectRrn(i++);
					tempLot.setEquipmentId(qtyUnit.getEquipmentId());
					tempLot.setSubProcessUnit(Lists.newArrayList(qtyUnit));
					tempLotList.add(tempLot);
				}
			} else if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				Map<String, List<ProcessUnit>> processUnitByEqpMap = processUnits.stream()
						.collect(Collectors.groupingBy(ProcessUnit::getEquipmentId));
				for (String equipmentId : processUnitByEqpMap.keySet()) {
					Lot tempLot = (Lot) lot.clone();
					tempLot.setObjectRrn(i++);
					tempLot.setEquipmentId(equipmentId);
					tempLot.setSubProcessUnit(processUnitByEqpMap.get(equipmentId));
					tempLotList.add(tempLot);
				}
			}
			return tempLotList;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	public ADTable getTrackOutADTable(List<TrackOutLot> trackOutLots) throws Exception, ClientException {
		ADTable adTable = super.getTrackOutADTable(trackOutLots);
		List<ADField> adFields = adTable.getFields();
		for (ADField adField : adFields) {
			if (FIELD_OUTMAINQTY.equals(adField.getName())) {
				// 编辑OutMain栏位
				adField.setIsEditable(!isTrackInInputQty(trackOutLots.get(0)));
				continue;
			}

			if (FIELD_SCRAPMAINQTY.equals(adField.getName())) {
				// 显示SCrap栏位
				adField.setIsDisplay(true);
				adField.setIsMain(true);
				adField.setIsEditable(true);
				continue;
			}
		}
		return adTable;
	}

	@Override
	public boolean validate() {
		List<Object> trackOutObjs = tableManager.getCheckedObject();
		if (trackOutObjs == null || trackOutObjs.size() == 0) {
			setErrorMessage(Message.getString("wip.lot_select_alert"));
			return false;
		}
		List<TrackOutLot> trackOutLots = new ArrayList<TrackOutLot>();
		for (Object trackOutObj : trackOutObjs) {
			TrackOutLot trackOutLot = (TrackOutLot) trackOutObj;
			trackOutLots.add(trackOutLot);
		}

		context.setTrackOutLots(trackOutLots);

		if (attributeForm != null) {
			// 检查Attribute信息
			if (!attributeForm.saveToObject()) {
				return false;
			}
		}

		// 根据实际输入重置Flag
		context.setReworkFlag(false);
		context.setScrapFlag(false);

		for (TrackOutLot trackOutLot : trackOutLots) {
			if ((trackOutLot.getOutMainQty() == null) && (trackOutLot.getReworkMainQty() == null)
					&& (trackOutLot.getScrapMainQty() == null)) {
				setErrorMessage(Message.getString("wip.lot_both_bull"));
				return false;
			}

			BigDecimal outMainQty = trackOutLot.getOutMainQty() == null ? BigDecimal.ZERO : trackOutLot.getOutMainQty();
			BigDecimal reworkMainQty = trackOutLot.getReworkMainQty() == null ? BigDecimal.ZERO
					: trackOutLot.getReworkMainQty();
			BigDecimal scrapMainQty = trackOutLot.getScrapMainQty() == null ? BigDecimal.ZERO
					: trackOutLot.getScrapMainQty();

			if ((outMainQty.compareTo(BigDecimal.ZERO) == 0) && (reworkMainQty.compareTo(BigDecimal.ZERO) == 0)
					&& (scrapMainQty.compareTo(BigDecimal.ZERO) == 0)) {
				setErrorMessage(Message.getString("wip.lot_both_bull"));
				return false;
			}

			// 判断输入数量不能小于0
			if (outMainQty.compareTo(BigDecimal.ZERO) < 0) {
				setErrorMessage(Message.getString("wip.qty_can_not_less_zero"));
				return false;
			}

			if (scrapMainQty.compareTo(BigDecimal.ZERO) < 0) {
				setErrorMessage(Message.getString("wip.qty_can_not_less_zero"));
				return false;
			}

			if (reworkMainQty.compareTo(BigDecimal.ZERO) < 0) {
				setErrorMessage(Message.getString("wip.qty_can_not_less_zero"));
				return false;
			}

			// 判断报废数量不能大于出站数量
			if (scrapMainQty.compareTo(outMainQty) > 0) {
				setErrorMessage(Message.getString("wip.lot_scrap_outqty_bigger") + outMainQty);
				return false;
			}

			// 判断是否有返工和报废
			if (scrapMainQty.compareTo(BigDecimal.ZERO) > 0) {
				// 出站数量小于批次数量,说明批次有报废
				context.setScrapFlag(true);
			}

			if (reworkMainQty.compareTo(BigDecimal.ZERO) > 0) {
				context.setReworkFlag(true);
			}

			// 判断返工数量不能大于出站数量
			if (reworkMainQty.compareTo(outMainQty) > 0) {
				// 返工数量必须小于出站数量
				setErrorMessage(Message.getString("wip.lot_rework_bigger") + outMainQty);
				return false;
			}

			// 判断报废、返工数量之和不能大于出站数量
			if (outMainQty.compareTo(reworkMainQty.add(scrapMainQty)) < 0) {
				setErrorMessage(Message.getString("wip.multi_eqp_scrap_rework_out_qty_error"));
				return false;
			}

			Lot cloneLot = trackOutLot.getLot();
			if (QtyUnit.getUnitType().equals(cloneLot.getSubUnitType())) {
				List<ProcessUnit> units = cloneLot.getSubProcessUnit();
				if (units != null && units.size() > 0) {
					ProcessUnit processUnit = units.get(0);
					processUnit.setMainQty(outMainQty.subtract(reworkMainQty.add(scrapMainQty)));
				}
			}
		}
		return true;
	}

	protected boolean isTrackInInputQty(TrackOutLot trackOutLot) {
		Lot lot = trackOutLot.getLot();
		if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
			return true;
		}
		// 根据前向导ID来判断是否需要输入数量
		Step step = context.getStep();
		if (step != null && TRACK_IN_WIZARD.equals(step.getTrackInWizard())) {
			return true;
		}
		return false;
	}

	@Override
	public String doNext() {
		context.setInputScrap(true);
		if (splitOut != null) {
			MultiEqpTrackOutWizard wizard = (MultiEqpTrackOutWizard) getWizard();
			wizard.setTrackOutSplit(splitOut.getSelection());
		}
		return super.doNext();
	}

}
