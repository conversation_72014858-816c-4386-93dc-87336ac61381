package com.glory.mes.wip.lot.run.abort;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.swt.widgets.Button;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.InContext;
import com.google.common.collect.Lists;

public class AbortBatchLotSelectGlcPage extends GlcFlowWizardPage {
	
	private static final Logger logger = Logger.getLogger(AbortBatchLotSelectGlcPage.class);
	
	public static final String FIELD_ABORTTABLE = "abortTable";
	public static final String FIELD_KEEPBATCH = "keepBatch";
	
	protected AbortWizard aw;
	protected InContext context;
	protected CheckBoxFixEditorTableManager tableManager;
	protected Button checkboxKeepBatch;

	protected ListTableManagerField abortTableField;
	protected BooleanField keepBatchField;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		abortTableField = form.getFieldByControlId(FIELD_ABORTTABLE, ListTableManagerField.class);
		keepBatchField = form.getFieldByControlId(FIELD_KEEPBATCH, BooleanField.class);
		
		aw = (AbortWizard) this.getWizard();
		context = (InContext)aw.getContext();
		tableManager = (CheckBoxFixEditorTableManager) abortTableField.getListTableManager().getTableManager();
		checkboxKeepBatch = keepBatchField.getCheckboxControl();
		
		init();
		setTitle(Message.getString("wip.abort"));
		setDescription(Message.getString("wip.abort_select_lot"));
	}
	
	protected void init() {
		try {
			List<Lot> lots = new ArrayList<Lot>();
			if (context.getLots() != null && context.getLots().size() > 0) {
			    for (Lot lot : context.getLots()) {
			    	//临时存储LotComment
			    	lot.setAttribute1(lot.getLotComment());
			        lot.setLotComment(null);
			        lots.add(lot);
			    }
			}
			tableManager.setInput(lots);
			//显示保持Batch控件
			if (context.getLots() != null && context.getLots().size() > 0) {
				Step currentStep = context.getCurrentStep();
				if (currentStep == null) {
					PrdManager prdManager = Framework.getService(PrdManager.class);
					currentStep = new Step();
					currentStep.setObjectRrn(context.getLots().get(0).getStepRrn());
					currentStep = (Step) prdManager.getSimpleProcessDefinition(currentStep, false);
				}
				if (currentStep != null && currentStep.getKeepBatch()) {
					checkboxKeepBatch.setSelection(true);
				} else {
					keepBatchField.setEnabled(false);
				}
			}
			//设置默认选中项
			if (context.getLots() != null) {
				List<Lot> selectedAbortLots = new ArrayList<Lot>();
				for (Lot lot : context.getLots()) {
					for (Lot abortLot : lots) {
						if (abortLot.equals(lot)) {
							selectedAbortLots.add(abortLot);
							break;
						}
					}
				}
				for(Lot lot : selectedAbortLots) {
					tableManager.setCheckedObject(Lists.newArrayList(lot));
				}
			}
			context.setLots(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
    @Override
	public String doNext() {
	    InContext context = ((AbortWizard)getWizard()).getContext();

		List<Object> abortLots = tableManager.getCheckedObject();
		if(abortLots != null && abortLots.size() > 0){
			List<Lot> lots = new ArrayList<Lot>();
			List<LotAction> actions = new ArrayList<LotAction>();
			for (Object abortObj : abortLots) {
				Lot abortLot = (Lot) abortObj;
				if (checkAbortCommentsNotNull(abortLot)) {
					UI.showError(Message.getString("wip.abort_comments_null"));
					return "";
				}
				if (abortLot.getLotComment() != null && abortLot.getLotComment().trim().length() > 0) {
					LotAction lotAction = new LotAction();
					lotAction.setLotRrn(abortLot.getObjectRrn());
					lotAction.setActionCode("AbortLot");
					lotAction.setActionComment(abortLot.getLotComment());
					
					List<ProcessUnit> units = abortLot.getSubProcessUnit();
					if (CollectionUtils.isNotEmpty(units)) {
						lotAction.setEquipmentId(units.get(0).getEquipmentId());
					}
					
					if (StringUtil.isEmpty(abortLot.getEquipmentId())) {
						lotAction.setEquipmentId(abortLot.getEquipmentId());
					}
					abortLot.setLotComment(DBUtil.toString(abortLot.getAttribute1()));
					actions.add(lotAction);
				}
			}
			lots.addAll((List<Lot>)(List)abortLots);
			if (checkboxKeepBatch != null) {
				context.setKeepBatch(checkboxKeepBatch.getSelection());
			}
			context.setLots(lots);
			context.setActions(actions);
			if (lots.size() == 1 && !StringUtil.isEmpty(lots.get(0).getBatchId())) {
				UI.showInfo(Message.getString("wip.lot_single_abort_clean_batch_id"));
			}
			return "finish";
		} else {
			setErrorMessage(Message.getString("wip.lot_select_alert"));
			return "null";
		}
	}

	@Override
	public String doPrevious() {
		return "";
	}
	
	public IWizardPage getPreviousPage() {
		return null;
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	private boolean checkAbortCommentsNotNull(Lot abortLot) {
		boolean flag = false;
		String comment = abortLot.getLotComment();
		if (comment == null || comment.trim().length() == 0) {
			flag = true;
		}
		return flag;
	}
}
