package com.glory.mes.wip.lot.run.track.forms;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.lot.run.track.TrackContext;
import com.glory.mes.wip.lot.run.track.TrackForm;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;
import com.glory.mes.wip.model.Lot;

public class StepFutureNoteForm extends TrackForm {

	protected Text text;
	
	public StepFutureNoteForm() {}

	public Composite createForm(Composite parent) {
		Group futureNoteGroup = new Group(parent, SWT.NONE);
		futureNoteGroup.setText("未来备注");
		futureNoteGroup.setLayout(new GridLayout(1, true));
		GridData gd = new GridData(GridData.FILL_BOTH);	
		futureNoteGroup.setLayoutData(gd);
		futureNoteGroup.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
		
		text = new Text(futureNoteGroup, SWT.MULTI | SWT.READ_ONLY | SWT.V_SCROLL);
		text.setLayoutData(new GridData(GridData.FILL_BOTH));
		text.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		text.setForeground(new Color(Display.getCurrent(), 255, 0, 0));
		
		return futureNoteGroup;
	}
	
	public TrackContext saveToObject(TrackContext trackContext) {
		return trackContext;
	}
	
	public boolean validate() {
		return true;
	}
	
	//@Override
	public void lotChanged(Object sender, List<Lot> lots) {
		try {
			if (lots != null && lots.size() > 0) {
				Lot lot = lots.get(0);
				LotManager lotManager = Framework.getService(LotManager.class);		
				PrdManager prdManager = Framework.getService(PrdManager.class);
				List<Node> nodes = prdManager.getProcessFlowList(lot.getProcessInstanceRrn());
				List<FutureNote> futureNotes = lotManager.getLotFutureNote((StepState) nodes.get(nodes.size() - 1), lot);
				
				String textvalue = "";
				if (futureNotes != null && futureNotes.size() > 0) {
					textvalue = "----" + lot.getLotId()
							+ "---------------------------------------\n   ";
					for (int n = 0; n < futureNotes.size(); n++) {
						textvalue = textvalue + (n + 1) + ": "
								+ futureNotes.get(n).getNote() + "\n   ";
					}
				}
				text.setText(textvalue);
			} else {
				text.setText("");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}	
		
	}
	
}
