package com.glory.mes.wip.lot.run.track.forms;

import java.util.Arrays;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.base.ui.custom.NamingText;
import com.glory.mes.wip.lot.run.track.TrackForm;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;

/**
 * 批次输入Form
 */
public class LotInputForm extends TrackForm {

	public static final String ID_HEADER_LOTINPUT = "LotInput";
	
	@Override
	public Composite createForm(Composite parent) {
		Composite from = new Composite(parent, SWT.NONE);
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 2;
		gridLayout.marginHeight = 0;
		gridLayout.verticalSpacing = 0;
		gridLayout.marginWidth = 0;
		gridLayout.horizontalSpacing = 0;
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		from.setLayout(gridLayout);
		from.setLayoutData(gd);
	
		NamingText txtLotId = new NamingText(from, Arrays.asList("Lot"), NamingText.TEXT_RED, SWT.BORDER);
		return from;
	}

}
