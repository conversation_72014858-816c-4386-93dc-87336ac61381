package com.glory.mes.wip.lot.run.trackout.edc;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.edc.bin.collection.BinEdcDialogForm;
import com.glory.edc.bin.collection.multi.BinEdcDialogFormMulti;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcDialogForm;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

public class EdcTrackOutPage extends FlowWizardPage {
	
	private Lot lot;
	private EdcSetCurrent edcCurrent;
	private AbstractEdcSet edcSet;
	private List<EdcData> lastDcDatas;
	
	private EdcDialogForm edcForm;
	
	protected TrackOutContext context;
	
	public EdcTrackOutPage() {
		super();
	}
	
	public EdcTrackOutPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		context = (TrackOutContext)((TrackOutWizard)this.getWizard()).getContext();
		init();
	}
	
	@Override
	public void createControl(Composite parent) {
		setTitle(Message.getString("wip.dcop"));	
		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		setControl(composite);
		createDialogForm(composite, toolkit);
	}
	
	protected void createDialogForm(Composite parent, FormToolkit toolkit) {
		if (edcSet instanceof EdcItemSet) {
			edcForm = new EdcDialogForm(parent, edcCurrent, lot, edcSet, lastDcDatas, toolkit);
			edcForm.createForm();
		} else if (edcSet instanceof EdcBinSet) {
			if (edcSet.getIsMultiInput()) {
				edcForm = new BinEdcDialogFormMulti(parent, edcCurrent, lot, edcSet, 
						lastDcDatas, toolkit);
				edcForm.createForm();
			} else {
				edcForm = new BinEdcDialogForm(parent, edcCurrent, lot, edcSet, lastDcDatas, toolkit);
				edcForm.createForm();
			}
		}
	}
	
	private void init() {
		try {
			List<Lot> lots = context.getLots();
			if (CollectionUtils.isNotEmpty(lots)) {
				if (lots.size() > 1) {
					// 暂不支持多批次数据收集
					doSkip();
				}
				
				this.lot = lots.get(0);
				List<EdcSetCurrent> setCurrents = getEdcSetCurrent();
				if (CollectionUtils.isNotEmpty(setCurrents)) {
					this.edcCurrent = setCurrents.get(0);
					
					AbstractEdcSet itemEdcSet = new AbstractEdcSet();
                    itemEdcSet.setObjectRrn(edcCurrent.getItemSetRrn());
                    EDCManager edcManager = Framework.getService(EDCManager.class);
        			this.edcSet = edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), null, null);
                    
                    if (!(edcSet instanceof EdcItemSet || edcSet instanceof EdcBinSet)) {
                    	// 暂时不支持的数据采集类型，不处理
                    	doSkip();
                    }
				}
			}
			
			// 没有设置EDC数据收集就跳过该界面
			if (edcCurrent == null) {
				doSkip();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	private List<EdcSetCurrent> getEdcSetCurrent() throws Exception {
		if (lot == null) {
			return null;
		}
		EDCManager edcManager = Framework.getService(EDCManager.class);
		return edcManager.getItemSetCurrents(Env.getOrgRrn(), lot.getBatchId(), lot.getObjectRrn(), null);
	}

	@Override
	public String doNext() {
		if (!edcForm.validate()) {
			return "";
		}
		
		List<EdcData> datas = edcForm.getEdcDatas();
		//检查强制输入
		if (!checkMandatory(datas)) {
			return "";
		}
		
		List<EdcData> dcDatas = Lists.newArrayList();
		if((edcSet instanceof EdcItemSet) || (edcSet instanceof EdcTextSet)){
			dcDatas = removeEmptyEdcData(datas);
		} else {
			dcDatas = datas;
		}
	
		if (dcDatas == null || dcDatas.size() < 1){
			//不允许所有采集项采集数据为空
			UI.showError(Message.getString("edc.cannot_save"), Message.getString("edc.alert_message_title"));
			return "";
		}

		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			for (EdcData data : datas) {
				if(data.getBatchId() != null){
					List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), data.getBatchId());
					String lotIds = "";
					for (Lot lot : lots) {
						lotIds += lot.getLotId() + ";";
					}
					data.setBatchLots(lotIds.substring(0, lotIds.length() - 1));
				}
				data.setLineId(lot.getLineId());
				data.setTeamId(Env.getSessionContext().getTeam());
			}
			
			EdcTrackOutWizard outWizard = (EdcTrackOutWizard) this.wizard;
			outWizard.setEdcDatas(dcDatas);
			return getDefaultDirect();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		return "";
	}
	
	/**
	 * 在多个数据采集项时
	 * 不保存没有输入数据的采集结果
	 */
	public List<EdcData> removeEmptyEdcData(List<EdcData> datas){
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		for (EdcData data : datas) {
			if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())) {
				edcDatas.add(data);
			} else if (data.getDcData() != null) {
				String dcDatas[] = data.getDcData().split(";");
				if (!data.getDcData().equals("") 
						&& dcDatas != null && dcDatas.length != 0){
					edcDatas.add(data);
				}
			}
		}
		return edcDatas;
	}
	
	/**
	 * 检查是否强制输入，如果强制输入
	 * 1，Variable类型数据必须每个都输入值
	 * 2，Attribute、BIN类型的数据Total必须有值且不能为0
	 * 3.所有选择了选择设备的数据采集都必须选择设备
	 * 4.文本数据采集设置了采集项为必填的检查强制输入
	 */
	public boolean checkMandatory(List<EdcData> dcDatas){

		if (edcSet instanceof EdcItemSet){
			for (EdcItemSetLine line : ((EdcItemSet) edcSet).getItemSetLines()) {
				if (line.getIsShowEquipment()) {
					for (EdcData edcData : dcDatas) {
						if (edcData.getMeasureEqp().equals("") || edcData.getMeasureEqp() == null) {
							return false;
						}
					}
				}
				//如果为必须数据采集
				if (line.getIsMandatory()){
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if(line.getDataType().equals(EdcItem.DATATYPE_ATTRIBUTE)){
								//检查Attribute类型,Total必须有值且不能为0
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								for (int i = 0; i < edcIds.length; i++) {
									if (EdcData.ATTRIBUTE_TOTAL.equals(edcIds[i])) {
										if (i < edcDatas.length) {
											String totalData = edcDatas[i];
											if (totalData.trim().length() > 0 
													&& !"0".equals(totalData.trim())) {
												return true;
											}
										}
									}
								}
								UI.showError(Message.getString("edc.data_mandatory_fail"));
								return false;
							} else {
								//检查Variable类型
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								//检查长度是否相同
								if (!(edcIds.length == edcDatas.length)) {
									UI.showError(Message.getString("edc.data_mandatory_fail"));
									return false;
								}
								//检查每个栏位是否有值
								for (String edcData : edcDatas) {
									if (edcData.trim().length() == 0) {
										UI.showError(Message.getString("edc.data_mandatory_fail"));
										return false;
									}
								}
							}
						}
					}
				}
				
				if (line.getIsJudgeByManual()) {
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (StringUtil.isEmpty(data.getJudge1())) {
								UI.showError(Message.getString("edc.data_judge_must_input"));
								return false;
							}
						}
					}
				}
			}
		} else if (edcSet instanceof EdcBinSet){
			//如果是BIN数据采集
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (StringUtil.isEmpty(data.getJudge1())) {
						UI.showError(Message.getString("edc.data_judge_must_input"));
						return false;
					}
				}
			}
		} else if (edcSet instanceof EdcTextSet) {
			for (EdcTextSetLine line : ((EdcTextSet) edcSet).getTextSetLines()) {
				if (edcSet.getIsShowEquipment()) {
					for (EdcData edcData : dcDatas) {
						if (edcData.getMeasureEqp().equals("") || edcData.getMeasureEqp() == null) {
							return false;
						}
					}
				}
				//如果为必须数据采集
				if (line.getIsMandatory()){
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (data.getDcData().equals("") || data.getDcData() == null) {
								return false;
							}
						}
					}
				}
			}	
			
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())){
						if (StringUtil.isEmpty(data.getJudge1())) {
							UI.showError(Message.getString("edc.data_judge_must_input"));
							return false;
						}
					}
				}
			}
		}
		return true;
	}

}
