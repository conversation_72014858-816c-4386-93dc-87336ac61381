package com.glory.mes.wip.lot.run.bylocation;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseAdapter;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.RASColor;
import com.glory.mes.wip.custom.depend.EquipmentToolTip;

@SuppressWarnings("restriction")
public class ByLocationEqpLed extends Composite {
	private Logger logger = Logger.getLogger(ByLocationEqpLed.class);
	
	private Equipment equipment;
	protected ByLocationSelectEqpDialog eqpForm;
	private boolean isSelected = false;
	
	private Label lblEquipment;
		
	public ByLocationEqpLed(Composite parent, Equipment equipment, ByLocationSelectEqpDialog eqpForm) {
		super(parent, SWT.NONE);
		this.equipment = equipment;
		this.eqpForm = eqpForm;
		createFrom();
	}
	
	private void createFrom(){
		FormToolkit toolkit = new FormToolkit(getDisplay());

		GridLayout gl = new GridLayout(1, false);
		gl.horizontalSpacing = 0;
		gl.marginWidth = 0;
		gl.marginHeight = 0;
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		setLayout(gl);
		setLayoutData(gd);
		
		lblEquipment = toolkit.createLabel(this, getEquipment().getEquipmentId(), SWT.BORDER);
		lblEquipment.setAlignment(SWT.CENTER);
		GridData gdEquipment = new GridData(SWT.FILL, SWT.CENTER, true, false);
		gdEquipment.heightHint = DPIUtil.autoScaleUpUsingNativeDPI(16);
		gdEquipment.widthHint = DPIUtil.autoScaleUpUsingNativeDPI(getEquipment().getEquipmentId().length() * 9);
		lblEquipment.setLayoutData(gdEquipment);
		
		lblEquipment.addMouseListener(new MouseAdapter(){
			@Override
			public void mouseDown(MouseEvent e) {
				switch(e.button){
					case 1:
						setSelected(!isSelected);
						searchEquipment(equipment.getEquipmentId());
						break;
				}
			}
		});
		EquipmentToolTip toolTip = new EquipmentToolTip(lblEquipment, getEquipment());
		toolTip.setPopupDelay(1000);
	}
	
	public void selectChanged(boolean selected){
		if (selected){
			Font font = lblEquipment.getFont();
			FontData[] fds = font.getFontData();
			fds[0].setStyle(SWT.BOLD);
			lblEquipment.setFont(new Font(Display.getCurrent(), fds));
		} else {
			Font font = lblEquipment.getFont();
			FontData[] fds = font.getFontData();
			fds[0].setStyle(SWT.NONE);
			lblEquipment.setFont(new Font(Display.getCurrent(), fds));
		}
	}
	
	public void stateChanged(Equipment equipment){
		Color color = RASColor.getColor(equipment.getState());
		if (color == null) {
			color = Display.getDefault().getSystemColor(SWT.COLOR_GRAY);
		}
		lblEquipment.setBackground(color);
	}

	public void setSelected(boolean isSelected) {
		this.isSelected = isSelected;
		selectChanged(isSelected);
	}

	public boolean isSelected() {
		return isSelected;
	}

	public void setEquipment(Equipment equipment) {
		this.equipment = equipment;
	}

	public Equipment getEquipment() {
		return equipment;
	}
	
	public void searchEquipment(String equipmentId) {
		try {
			//取得最新的设备信息
			RASManager rasManager = Framework.getService(RASManager.class);
			Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentId);
			
			if (equipment != null) {
				eqpForm.notifyEquipmentChangeListeners(this, equipment);
			}
		} catch (Exception e) {
			logger.error("ByLocationEqpLed searchEquipment(): Equipment isn' t exsited!");
		}
	}
}
