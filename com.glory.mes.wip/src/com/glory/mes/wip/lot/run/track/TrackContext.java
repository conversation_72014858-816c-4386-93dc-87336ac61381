package com.glory.mes.wip.lot.run.track;

import java.util.List;

import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotAttributeValue;
import com.glory.mes.wip.lot.run.track.listener.IActionListener;

public class TrackContext implements IActionListener {
	
	public static final String ACTION_TYPE_TRACKIN = "TrackIn";
	public static final String ACTION_TYPE_TRACKOUT = "TrackOut";
	public static final String ACTION_TYPE_ABORT = "Abort";
	public static final String ACTION_TYPE_CLOSE = "Close";

	//TrackIn、TrackOut、TrackMove操作中时选中或者输入的批次
	private List<Lot> lots;
	
	private Step step;
	
	private List<Equipment> selectEquipments;
	
	private List<LotAttributeValue> lotAttributeValues;
	
	private String operator1;
	private String operator2;
	
	private Lot currentLot;
	
	public void setLots(List<Lot> lots) {
		this.lots = lots;
	}

	public List<Lot> getLots() {
		return lots;
	}

	public void setStep(Step step) {
		this.step = step;
	}

	public Step getStep() {
		return step;
	}

	public void setSelectEquipments(List<Equipment> selectEquipments) {
		this.selectEquipments = selectEquipments;
	}

	public List<Equipment> getSelectEquipments() {
		return selectEquipments;
	}
	
	public String getCategory() {
		return "";
	}
	
	public List<LotAttributeValue> getLotAttributeValues() {
		return lotAttributeValues;
	}

	public void setLotAttributeValues(List<LotAttributeValue> lotAttributeValues) {
		this.lotAttributeValues = lotAttributeValues;
	}

	//当前Track out和Movement Next都是一个批次，即getLots().size() == 1
	//所以以第一个Lot判断是否有process unit
	public boolean isProcessUnitable() {
		if(lots != null && lots.size() > 0) {
			return lots.get(0).getSubProcessUnit() == null 
				|| lots.get(0).getSubProcessUnit().size() == 0 ? false : true;
		}
		return false;
	}

	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}

	public String getOperator2() {
		return operator2;
	}

	public void setOperator2(String operator2) {
		this.operator2 = operator2;
	}

	public Lot getCurrentLot() {
		return currentLot;
	}

	public void setCurrentLot(Lot currentLot) {
		this.currentLot = currentLot;
	}

	@Override
	public void execute(String actionType) {
		
		
	}
	
}
