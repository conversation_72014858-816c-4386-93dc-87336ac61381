package com.glory.mes.wip.lot.run.manul.movenext;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.node.EndState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitDialog;
import com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitManagerEditor;
import com.glory.mes.wip.lot.carrier.glc.split.LotSortingSplitContext;
import com.glory.mes.wip.lot.carrier.glc.split.SelectFutureMergeStepDialog;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.mes.wip.model.LotRework;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.glory.mes.wip.track.model.InContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class LotReworkDialog extends GlcBaseDialog {

	public static final String FORM_EDCHISFORM = "WIPLotHisForm";
	public static final String CONTROL_BUTTON_EDCDATA = "edcData";
	public static final String CONTROL_COMP_INFO = "compList";
	public static final String CONTROL_BODY_INFO = "bodyInfo";
	public static final String CONTROL_BODY_REWORKMERGE_INFO = "reworkMergeInfo";
	public static final String CONTROL_BODY_REWORK_INFO = "reworkInfo";
	public static final String CONTROL_BODY_MERGE_INFO = "mergeInfo";
	public static final String CONTROL_BODY_HOLD_INFO = "holdInfo";
	public static final String CONTROL_BODY_HOLD_LIST = "holdList";

	public static final String REOWRKFORM_REWORKPRODUCE = "reworkProduce";
	public static final String REOWRKFORM_REWORKCODE = "reworkCode";
	public static final String REOWRKFORM_REWORKREASON = "reworkReason";
	public static final String REOWRKFORM_COMMENT = "comment";
	public static final String REOWRKFORM_EXTERNALSPLIT = "externalSplit";
	public static final String REOWRKFORM_AUTOMERGE = "autoMerge";
	public static final String REOWRKFORM_MERGESTEP = "mergeStep";
	public static final String REOWRKFORM_RETURNSTEP = "returnStep";
	public static final String REOWRKFORM_AUTOHOLD = "autoHold";
	public static final String REOWRKFORM_REWORKCONTROLTYPE = "reworkControlType";
	public static final String REOWRKFORM_REWORKCOUNT = "reworkCount";

	public static final String HOLDLISTFORM_CHECKBOX = "checkBox";

	public static final String MAP_KEY_RETURNSTEPNAME = "returnStepName";

	public static final String AD_TABLE_FUTURESTEP_NAME = "SelectFutureMergeStepDialog";
	public static final String TITILE_NAME = "Wip.SplitLot";

	public static final String BTN_CHOICE = "choice";
	public static final String BTN_ADD = "add";

	private ListTableManagerField compListTableManagerField;
	private ListTableManagerField holdInfoListTableManagerField;
	private GlcFormField bodyFormField;
	private GlcFormField holdFormField;
	private BooleanField autoReleaseField;
	private EntityFormField reowrkFormField;

	private RefTableField reworkCodeField;
	private RefTableField reworkTransientField;
	private TextField commentField;
	private BooleanField externalSplitField;
	private BooleanField autoMergeField;
	private BooleanField autoHoldField;
	private TextField mergeStepField;
	private TextField returnStepField;
	private TextField reworkControlTypeField;
	private TextField reworkCountField;

	protected MPart mPart;

	private Lot lot;
	private boolean isCheckReworkCount;
	private List<LotHold> lotHolds;
	private List<ComponentUnit> findComps;
	// 合批工步实例
	private StepState stepState;

	private Consumer<LotReworkDialog> closeAdaptor;

	protected LotSortingSplitContext context;

	private List<Transition> reworkTransitions;
	public static final String STR_PILOT_PARENTLOTID = "parentLotId";
	public static final String STR_PILOT_PILOTPLAN_NAME = "pilotPlanName";
	public static final String STR_PILOT_PILOTPLAN_ISAUTOMERGE = "isAutoMerge";
	public static final String STR_PILOT_PILOTACTION_PROCEDURENAME = "procedureName";

	public LotReworkDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot,
			boolean isCheckReworkCount, MPart mPart) {
		super(adFormName, authority, eventBroker);
		this.setLot(lot);
		this.setBlockOnOpen(false);
		this.setmPart(mPart);
		this.isCheckReworkCount = isCheckReworkCount;
	}

	protected void createFormAction(GlcForm form) {
		compListTableManagerField = form.getFieldByControlId(CONTROL_COMP_INFO, ListTableManagerField.class);
		bodyFormField = form.getFieldByControlId(CONTROL_BODY_INFO, GlcFormField.class);
		reowrkFormField = bodyFormField.getFieldByControlId(CONTROL_BODY_REWORKMERGE_INFO, EntityFormField.class);
		reworkCodeField = reowrkFormField.getFieldByControlId(REOWRKFORM_REWORKCODE, RefTableField.class);
		reworkTransientField = reowrkFormField.getFieldByControlId(REOWRKFORM_REWORKREASON, RefTableField.class);
		commentField = reowrkFormField.getFieldByControlId(REOWRKFORM_COMMENT, TextField.class);
		holdFormField = bodyFormField.getFieldByControlId(CONTROL_BODY_HOLD_INFO, GlcFormField.class);
		autoReleaseField = holdFormField.getFieldByControlId(HOLDLISTFORM_CHECKBOX, BooleanField.class);
		holdInfoListTableManagerField = holdFormField.getFieldByControlId(CONTROL_BODY_HOLD_LIST, ListTableManagerField.class);

		externalSplitField = reowrkFormField.getFieldByControlId(REOWRKFORM_EXTERNALSPLIT, BooleanField.class);
		autoMergeField = reowrkFormField.getFieldByControlId(REOWRKFORM_AUTOMERGE, BooleanField.class);
		mergeStepField = reowrkFormField.getFieldByControlId(REOWRKFORM_MERGESTEP, TextField.class);
		returnStepField = reowrkFormField.getFieldByControlId(REOWRKFORM_RETURNSTEP, TextField.class);
		autoHoldField = reowrkFormField.getFieldByControlId(REOWRKFORM_AUTOHOLD, BooleanField.class);
		reworkControlTypeField = reowrkFormField.getFieldByControlId(REOWRKFORM_REWORKCONTROLTYPE, TextField.class);
		reworkCountField = reowrkFormField.getFieldByControlId(REOWRKFORM_REWORKCOUNT, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_BUTTON_EDCDATA), this::edcDataAdapter);
		subscribeAndExecute(eventBroker, bodyFormField.getTopicId() + GlcEvent.NAMESPACE_SEPERATOR + BTN_ADD, this::addReworkInfoToComp);
		subscribeAndExecute(eventBroker, autoReleaseField.getTopicId() + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_VALUECHANGE, this::releaseSelectAdapter);
		subscribeAndExecute(eventBroker, reworkTransientField.getTopicId() + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_VALUECHANGE, this::changeProcedure);
		
		createReworkTransientField();
		initCompListAndHoldList();
		if (!lot.getIsPilot()) {
			subscribeAndExecute(eventBroker, bodyFormField.getTopicId() + GlcEvent.NAMESPACE_SEPERATOR + BTN_CHOICE, this::choiceMergeStep);
			subscribeAndExecute(eventBroker, externalSplitField.getTopicId() + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_VALUECHANGE, this::externalSplitChangeAdapter);
		} else {
			initPilotFiledControl();
		}
	}

	protected void edcDataAdapter(Object object) {
		LotHisEdcDialog baseDialog = new LotHisEdcDialog(FORM_EDCHISFORM, null, eventBroker, lot);
		baseDialog.open();
	}

	// 选择合批工步按钮点击事件
	protected void choiceMergeStep(Object object) {
		if (!autoMergeField.isChecked()) {
			UI.showError(Message.getString("wip.auto_merge_must_ishold"));
			return;
		}
		SelectFutureMergeStepDialog dialog = new SelectFutureMergeStepDialog(AD_TABLE_FUTURESTEP_NAME, TITILE_NAME, eventBroker, lot);
		if (dialog.open() == Dialog.OK) {
			mergeStepField.setText(dialog.getStepState().getStepName());
			this.setStepState(dialog.getStepState());
		}
	}

	// 添加返工信息按钮点击事件
	protected void addReworkInfoToComp(Object object) {
		try {
			if (lot.getIsPilot()) {
				List<ComponentUnit> comps = (List<ComponentUnit>) compListTableManagerField.getListTableManager().getInput();
				List<ComponentUnit> newComps = new ArrayList<ComponentUnit>();
				for (ComponentUnit comp : comps) {
					comp.setReworkCode(reworkCodeField.getValue() == null ? null : reworkCodeField.getValue().toString());
					comp.setReworkTransition(reworkTransientField.getValue() == null ? null: reworkTransientField.getValue().toString());
					comp.setLotComment(commentField.getText());
					newComps.add(comp);
				}
				compListTableManagerField.getListTableManager().setInput(newComps);
			} else {
				List<Object> comps = compListTableManagerField.getListTableManager().getCheckedObject();
				if (CollectionUtils.isEmpty(comps)) {
					UI.showError(Message.getString("wms.select_one_line"));
					return;
				}
				if (reworkCodeField.getValue() == null || reworkTransientField.getValue() == null) {
					UI.showError(Message.getString("wip.rework_control_data_error"));
					return;
				}
				for (Object obj : comps) {
					ComponentUnit comp = (ComponentUnit) obj;
					for (ComponentUnit findComp : findComps) {
						if (StringUtils.equals(comp.getComponentId(), findComp.getComponentId())) {
							findComp.setReworkCode(reworkCodeField.getValue() == null ? null : reworkCodeField.getValue().toString());
							findComp.setReworkTransition(reworkTransientField.getValue() == null ? null: reworkTransientField.getValue().toString());
							findComp.setLotComment(commentField.getText());
						}
					}
				}
				compListTableManagerField.getListTableManager().setInput(findComps);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	// 全解除hold点击事件
	protected void releaseSelectAdapter(Object object) {
		holdInfoListTableManagerField.getListTableManager().setInput(lotHolds);
		if (autoReleaseField.isChecked()) {
			for (LotHold lotHold : lotHolds) {
				holdInfoListTableManagerField.getListTableManager().setCheckedObject(lotHold);
			}

		} else {
			for (Object obj : holdInfoListTableManagerField.getListTableManager().getCheckedObject()) {
				LotHold tmp = (LotHold) obj;
				for (LotHold lotHold : lotHolds) {
					if (tmp.getObjectRrn() == lotHold.getObjectRrn()) {
						continue;
					}
					holdInfoListTableManagerField.getListTableManager().setCheckedObject(lotHold);
				}
			}
		}

	}

	// ExternalSplit点击事件
	protected void externalSplitChangeAdapter(Object object) {
		if (externalSplitField.isChecked()) {

			boolean result = UI.showConfirm(Message.getString("wip.create_sorting_job"));
			if (!result) {
				externalSplitField.setValue("N");
				externalSplitField.refresh();
				return;
			}
			List<ComponentUnit> comps = (List<ComponentUnit>) compListTableManagerField.getListTableManager().getInput();
			if (CollectionUtils.isEmpty(comps)) {
				UI.showError(Message.getString("wms.select_one_line"));
				externalSplitField.setValue("N");
				externalSplitField.refresh();
				return;
			}
			List<ComponentUnit> allComps = new ArrayList<ComponentUnit>();
			for (ComponentUnit comp : comps) {
				allComps.add(comp);
			}

			Map<String, List<ComponentUnit>> reworkCodeMap = allComps.stream().filter(tmp -> !StringUtils.isEmpty(tmp.getReworkCode()))
					.collect(Collectors.groupingBy(ComponentUnit::getReworkCode));
			if (reworkCodeMap.isEmpty()) {
				UI.showError(Message.getString("wip.rework_control_data_error"));
				externalSplitField.setValue("N");
				externalSplitField.refresh();
				return;
			}
			for (String reworkCode : reworkCodeMap.keySet()) {
				// 同一个返工code 返工指示必须一致
				Map<Object, Long> countType = reworkCodeMap.get(reworkCode).stream()
						.collect(Collectors.groupingBy(ComponentUnit::getReworkTransition, Collectors.counting()));
				if (countType.keySet().size() > 1) {
					UI.showError(Message.getString("prd.same_code_rework_flow_must_same"));
					externalSplitField.setValue("N");
					externalSplitField.refresh();
					return;
				}
			}
			int targetCompsSize = 0;
			List<List<ComponentUnit>> targetComps = Lists.newArrayList();
			for (String reworkCode : reworkCodeMap.keySet()) {
				targetComps.add(reworkCodeMap.get(reworkCode));
				targetCompsSize = targetCompsSize + reworkCodeMap.get(reworkCode).size();
			}

			if (targetCompsSize == comps.size()) {
				UI.showError(Message.getString("prd.lot_all_rework_not_split"));
				externalSplitField.setValue("N");
				externalSplitField.refresh();
				return;
			}

			Map<String, Object> initDatas = Maps.newHashMap();
			initDatas.put(MesGlcEvent.PROPERTY_LOT_ID, lot.getLotId());
			initDatas.put(CarrierLotSplitManagerEditor.IS_SORT, true);
			initDatas.put(CarrierLotSplitManagerEditor.TARGET_TAB_SIZE, reworkCodeMap.keySet().size());
			initDatas.put(CarrierLotSplitManagerEditor.TARGET_COMPONENT_LIST, targetComps);
			initDatas.put(CarrierLotSplitManagerEditor.IS_SHOW_MERGE_STEP, false);

			CarrierLotSplitDialog dialog = new CarrierLotSplitDialog(CarrierLotSplitManagerEditor.ADFORM_NAME, CarrierLotSplitManagerEditor.AUTHORITY_NAME, eventBroker);
			if (Dialog.OK == dialog.open(initDatas)) {
				context = dialog.getContext();
				externalSplitField.setEnabled(false);
			} else {
				externalSplitField.setValue("N");
				externalSplitField.refresh();
			}
		}
	}

	// 初始化片和hold信息
	protected void initCompListAndHoldList() {
		try {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			List<ComponentUnit> comps = componentManager.getComponentsByParentUnitRrn(lot.getObjectRrn());
			comps.stream().forEach(t -> {
				t.setAttribute1(lot.getLotId());
			});
			findComps = comps;
			compListTableManagerField.getListTableManager().setInput(comps);
			if (CollectionUtils.isNotEmpty(comps)) {
				for (ComponentUnit componentUnit : comps) {
					compListTableManagerField.getListTableManager().setCheckedObject(componentUnit);
				}
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			lotHolds = lotManager.getLotHolds(lot.getObjectRrn());
			holdInfoListTableManagerField.getListTableManager().setInput(lotHolds);
			if (lotHolds != null && lotHolds.size() == 1) {
				autoReleaseField.setValue("Y");
				autoReleaseField.refresh();
				for (LotHold lotHold : lotHolds) {
					holdInfoListTableManagerField.getListTableManager().setCheckedObject(lotHold);
				}
			}
			autoHoldField.setValue("Y");
			autoHoldField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	// pilot批次初始化控件是否可编辑
	protected void initPilotFiledControl() {
		try {
			externalSplitField.setEnabled(false);

			autoMergeField.setEnabled(false);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	// Pilot 返工流程选择事件
	protected void changeProcedure(Object object) {
		try {
			String procedureName = (String) reworkTransientField.getValue();
			if (CollectionUtils.isEmpty(reworkTransitions) || StringUtils.isEmpty(procedureName)) {
				return;
			}
			Transition pilotTransition = reworkTransitions.stream().filter(p -> p.getName().equals(procedureName)).findFirst().get();
			if (!checkLotCanRework(lot, pilotTransition.getProcessDefinition())) {
				reworkTransientField.setValue(null);
				returnStepField.setText(null);
				reworkCountField.setText(null);
				reworkControlTypeField.setText(null);
				return;
			}
			// pilot带出返回主流程工步
			ReworkState reworkState = ((ReworkState) pilotTransition.getTo());
			if (lot.getIsPilot()) {
				Procedure pilotReworkProcedure = (Procedure) ((StepState) pilotTransition.getFrom()).getProcessDefinition();
				PrdManager prdManager = Framework.getService(PrdManager.class);
				reworkState = (ReworkState) prdManager.getNodeByName(Env.getOrgRrn(), pilotReworkProcedure, true, reworkState.getName());
				Transition transition = reworkState.getNormalLeavingTransition();
				if (transition.getTo() instanceof EndState) {
					EndState endState = (EndState)transition.getTo();
					if (!StringUtil.isEmpty(endState.getHiSuperInstruction())) {
						returnStepField.setText(endState.getName());
					} else {
						returnStepField.setText(null);
					}
				}
			}
			String reworkCount = reworkState.getTarget();
			String reworkControlType = reworkState.getObjectType();

			// 显示选择的返工流程最大返工次数
			reworkCountField.setText(reworkCount);
			reworkControlTypeField.setText(reworkControlType);
			reworkCountField.refresh();
			reworkControlTypeField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	// 点击确定按钮事件
	@Override
	protected void okPressed() {
		try {
			InContext inContext = new InContext();
			List<LotAction> lotActions = Lists.newArrayList();

			// 需要解除的hold
			List<LotHold> checkedLotHolds = (List<LotHold>) (List) holdInfoListTableManagerField.getListTableManager().getCheckedObject();
			if (checkedLotHolds != null && checkedLotHolds.size() > 0) {
				List<Long> lotHoldRrns = Lists.newArrayList();
				for (LotHold checkedLotHold : checkedLotHolds) {
					lotHoldRrns.add(checkedLotHold.getObjectRrn());
				}
				LotAction lotHoldAction = new LotAction();
				lotHoldAction.setLotRrn(lot.getObjectRrn());
				lotHoldAction.setActionType(LotAction.ACTIONTYPE_RELEASE);
				lotHoldAction.setLotHoldRrns(lotHoldRrns);
				lotActions.add(lotHoldAction);
			}

			List<FutureAction> futureActions = Lists.newArrayList();

			if (lot.getIsPilot()) {
				if (!pilotHiRework(inContext, lotActions)) {
					return;
				}
			} else {
				if (!normalLotRework(inContext, lotActions)) {
					return;
				}
			}

			if (LotStateMachine.STATE_WAIT.equals(lot.getState())) {
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipLotStateNotAllow()));
				return;
			}

			SessionContext sc = Env.getSessionContext();
			sc.setUserName(lot.getOperator1());
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.manualMoveNext(inContext, futureActions, false, sc);

			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	// pilot批次返工
	public boolean pilotHiRework(InContext inContext, List<LotAction> lotActions) {
		try {
			// 拿到整个需要返工的pilot批次列表
			List<ComponentUnit> comps = (List<ComponentUnit>) compListTableManagerField.getListTableManager().getInput();
			// 判断是否设置返工信息
			Map<String, List<ComponentUnit>> reworkCodeMap = comps.stream().filter(tmp -> !StringUtils.isEmpty(tmp.getReworkCode()))
					.collect(Collectors.groupingBy(ComponentUnit::getReworkCode));
			if (MapUtils.isEmpty(reworkCodeMap)) {
				UI.showError(Message.getString("wip.lot_select_reworkCode"));
				return false;
			}

			String transitionName = comps.get(0).getReworkTransition();
			if (CollectionUtils.isEmpty(reworkTransitions)) {
				UI.showError(Message.getString("wip.rework_procedure_is_empty"));
				return false;
			}
			Transition pilotTransition = reworkTransitions.stream().filter(p -> p.getName().equals(transitionName)).findFirst().get();
			String pilotProcedureName = ((StepState) pilotTransition.getFrom()).getProcessDefinition().getName();
			LotManager lotManager = Framework.getService(LotManager.class);
			Map<String, String> pilotInfo = lotManager.getPilotInfoByLotId(lot.getOrgRrn(), lot.getLotId(), pilotProcedureName);

			// 自动hold checked 检查pilotplan是否设置了自动merge 设置自动merge 警告提示
			if (autoHoldField.isChecked()) {
				Map<String, String> planInfo = lotManager.getActivePilotPlanByName(Env.getOrgRrn(), pilotInfo.get(STR_PILOT_PILOTPLAN_NAME));
				if (MapUtils.isNotEmpty(planInfo)) {
					if (StringUtils.equals("Y", planInfo.get(STR_PILOT_PILOTPLAN_ISAUTOMERGE))) {
						boolean result = UI.showConfirm(Message.getString("wip.auto_hold_merge_invalid"));
						if (!result) {
							return false;
						} else {
							inContext.setFutureHold(true);
						}
					}
				} else {
					UI.showError(Message.getString("wip.pilot_info_error"));
					return false;
				}
			}

			LotAction reworkAction = new LotAction();
			reworkAction.setLotRrn(lot.getObjectRrn());
			reworkAction.setActionType(LotAction.ACTIONTYPE_REWORK);
			reworkAction.setActionCode(comps.get(0).getReworkCode());
			reworkAction.setActionComment(comps.get(0).getLotComment());
			reworkAction.setReworkTransition(comps.get(0).getReworkTransition());
			reworkAction.setCheckReworkCount(isCheckReworkCount);

			List<ProcessUnit> procesUnits = lotManager.getLotSubProcessUnits(lot);
			reworkAction.setActionUnits(procesUnits);

			Map<String, String> pilotAction = lotManager.getPilotActionByLotId(lot.getOrgRrn(), lot.getLotId());
			if (!MapUtils.isEmpty(pilotAction)) {
				reworkAction.setReworkReturnProcedure(pilotAction.get(STR_PILOT_PILOTACTION_PROCEDURENAME));
			}

			lotActions.add(reworkAction);

			LotAction commentAction = new LotAction();
			commentAction.setLotRrn(lot.getObjectRrn());
			commentAction.setActionType(LotAction.ACTIONTYPE_COMMENT);
			commentAction.setActionCode(comps.get(0).getReworkCode());
			commentAction.setActionComment(comps.get(0).getLotComment());
			commentAction.setReworkTransition(comps.get(0).getReworkTransition());
			commentAction.setCheckReworkCount(isCheckReworkCount);
			lotActions.add(commentAction);
			
			inContext.setLots(Lists.newArrayList(lot));
			inContext.setActions(lotActions);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}

	// 普通批次返工
	public boolean normalLotRework(InContext inContext, List<LotAction> lotActions) {
		try { // 需要操作的片 对应的返工码 返工指示 备注在 reworkCode\reworkTransition\lotComment上
			List<ComponentUnit> componentUnits = (List<ComponentUnit>) (List) compListTableManagerField.getListTableManager().getInput();
			if (componentUnits == null || componentUnits.size() == 0) {
				UI.showError(Message.getString("wip.no_wafer_info"));
				return false;
			}

			if (autoMergeField.isChecked() && autoHoldField.isChecked()) {
				boolean result = UI.showConfirm(Message.getString("wip.auto_merge_invalid"));
				if (!result) {
					return false;
				}

			}
			if (autoHoldField.isChecked()) {
				inContext.setFutureHold(true);
			}

			Map<String, List<ComponentUnit>> reworkCodeMap = componentUnits.stream()
					.filter(tmp -> !StringUtils.isEmpty(tmp.getReworkCode()))
					.collect(Collectors.groupingBy(ComponentUnit::getReworkCode));
			if (reworkCodeMap.isEmpty()) {
				UI.showError(Message.getString("pp.workorder_no_select_rework_lot"));
				return false;
			}
			for (String reworkCode : reworkCodeMap.keySet()) {
				// 同一个返工code 返工指示必须一致
				Map<Object, Long> countType = reworkCodeMap.get(reworkCode).stream().collect(Collectors.groupingBy(ComponentUnit::getReworkTransition, Collectors.counting()));
				if (countType.keySet().size() > 1) {
					UI.showError(Message.getString("prd.same_code_rework_flow_must_same"));
					return false;
				}
				LotAction splitLotAction = new LotAction();
				if (externalSplitField.isChecked()) {
					List<LotAction> contextActions = context.getLotActions();
					boolean checkSort = false;
					for (LotAction lotAction : contextActions) {
						if (lotAction instanceof LotSortingAction) {
							// 查找返工流程对应的sortingaction
							Set<String> returnCompIds = ((LotSortingAction) lotAction).getToPositionMap().keySet();
							List<String> compIds = reworkCodeMap.get(reworkCode).stream().map(ComponentUnit::getComponentId).collect(Collectors.toList());
							if (!CollectionUtils.isEmpty(returnCompIds) && !CollectionUtils.isEmpty(compIds)) {
								if ((returnCompIds.size() == compIds.size()) && compIds.containsAll(returnCompIds)) {
									checkSort = true;
									((LotSortingAction) lotAction).setSortingMode(LotSortingJob.SORTING_MODE_SPLIT_BYLOGIC);
									splitLotAction = lotAction;
									break;
								}
							}
						}
					}
					if (!checkSort) {
						return false;
					}
				}
				splitLotAction.setLotRrn(lot.getObjectRrn());
				splitLotAction.setActionType(LotAction.ACTIONTYPE_REWORK);
				splitLotAction.setActionCode(reworkCodeMap.get(reworkCode).get(0).getReworkCode());
				splitLotAction.setActionComment((String) reworkCodeMap.get(reworkCode).get(0).getLotComment());
				splitLotAction.setReworkTransition(reworkCodeMap.get(reworkCode).get(0).getReworkTransition());
				splitLotAction.setCheckReworkCount(isCheckReworkCount);

				LotManager lotManager = Framework.getService(LotManager.class);
				if (autoMergeField.isChecked()) {
					if (getStepState() != null) {
						// 有未来合批工步，在分批中会处理到指定的工步合批
						List<String> mergeStepStateNames = Lists.newArrayList(getStepState().getName());
						List<StepState> stepStates = lotManager.getFutureMergeStepState(lot, null, mergeStepStateNames);
						splitLotAction.setFutureStepState(stepStates.get(0));
					} else {
						// 没有指定合批工步，默认在下一站合批
						Node nextStep = lotManager.getFutureMergeStepState(lot, lot.getProcessInstanceRrn(), null);
						if (nextStep == null) {
							return false;
						}
						splitLotAction.setFutureStepState(nextStep);

					}
				}
				// 在分批时已经创建了FutureMerge,不需要再返工时创建
				splitLotAction.setActionUnits((List<ProcessUnit>) (List) reworkCodeMap.get(reworkCode));
				lotActions.add(splitLotAction);

				LotAction commentAction = new LotAction();
				commentAction.setLotRrn(lot.getObjectRrn());
				commentAction.setActionType(LotAction.ACTIONTYPE_COMMENT);
				commentAction.setActionCode(reworkCodeMap.get(reworkCode).get(0).getReworkCode());
				commentAction.setActionComment((String) reworkCodeMap.get(reworkCode).get(0).getLotComment());
				commentAction.setReworkTransition(reworkCodeMap.get(reworkCode).get(0).getReworkTransition());
				commentAction.setCheckReworkCount(isCheckReworkCount);
				lotActions.add(commentAction);
			}
			inContext.setAutoMerge(false);
			inContext.setLots(Lists.newArrayList(lot));
			inContext.setActions(lotActions);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}

	// 获取当前批次的返工流程
	public void createReworkTransientField() {
		try {
			List<Procedure> lists = new ArrayList<Procedure>();

			List<Transition> transitions = getReworkTransitions();
			if (!CollectionUtils.isEmpty(transitions)) {
				for (Transition transition : transitions) {
					Procedure procedure = new Procedure();
					procedure.setName(transition.getName());
					procedure.setDescription(transition.getDescription());
					lists.add(procedure);
				}
			}
			reworkTransientField.getComboControl().setInput(lists);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public List<Transition> getReworkTransitions() {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			StepState state = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
			reworkTransitions = prdManager.getReworkTransitions(state, true);
			return reworkTransitions;
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	// 检查返工次数
	public boolean checkLotCanRework(Lot lot, ProcessDefinition processDefinition) {
		try {
			if (processDefinition instanceof Procedure) {
				Procedure reworkProcedure = (Procedure) processDefinition;
				if (reworkProcedure.getMaxReworkCount() != null) {

					LotManager lotManager = Framework.getService(LotManager.class);
					LotRework thisLotRework = new LotRework();
					thisLotRework.setOrgRrn(Env.getOrgRrn());
					thisLotRework.setCreatedBy(Env.getUserName());
					thisLotRework.setUpdatedBy(Env.getUserName());
					thisLotRework.setLotRrn(lot.getObjectRrn());
					thisLotRework.setReworkTime(new Date());
					thisLotRework.setMaxReworkCountType(ReworkState.OBJECTTYPE_TOTAL);
					thisLotRework.setMaxReworkCount(reworkProcedure.getMaxReworkCount());
					if (StringUtils.isNotEmpty(lot.getStepName())) {
						thisLotRework.setReworkStepName(lot.getStepName());
					} else {
						thisLotRework.setReworkStepName(lot.getLastStepName());
					}
					thisLotRework.setReworkProcedureName(reworkProcedure.getName());

					lotManager.checkAndsaveLotRework(thisLotRework, true, Env.getSessionContext());

				}
			}
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}

	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public StepState getStepState() {
		return stepState;
	}

	public void setStepState(StepState stepState) {
		this.stepState = stepState;
	}

	public MPart getmPart() {
		return mPart;
	}

	public void setmPart(MPart mPart) {
		this.mPart = mPart;
	}

	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<LotReworkDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<LotReworkDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}
}
