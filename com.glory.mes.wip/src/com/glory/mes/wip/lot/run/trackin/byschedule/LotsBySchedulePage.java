package com.glory.mes.wip.lot.run.trackin.byschedule;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class LotsBySchedulePage extends FlowWizardPage {
	
	private static final String TABLE_NAME = "WIPTrackInLotsBySchedule";
	
	protected Lot lot;
	protected Step step;
	protected ListTableManager tableManager;
	
	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
		List<Lot> lots = context.getLots();
		lot = lots.get(0);
	}
	
	@Override
	public void createControl(Composite parent) {
		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotsBySchedule = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new LotListTableManager(lotsBySchedule, true);
			List<Lot> lots = new ArrayList<Lot>();
			if (lot.getWoId() != null) {
				//相同工单，相同工步，状态为wait
				String whereClause = " woId = '" + lot.getWoId() + 
						"' AND stepName = '" + lot.getStepName() + 
						"' AND state = '" + LotStateMachine.STATE_WAIT + "'";  
				lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Env.getMaxResult(), whereClause, " lotId ");
			} else {
				lots.add(lot);
			}
			tableManager.newViewer(composite);
			tableManager.setInput(lots);
			for ( Lot lot : lots ) {
				tableManager.setCheckedObject(lot);
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		setControl(composite);
		setPageTitle();
	}

	protected void setPageTitle() {
		setTitle(Message.getString("wip.byscheduletrackin_info"));
		setMessage(Message.getString("wip.byscheduletrackin_list"));
	}
	
	@Override
	public String doNext() {
		if ( tableManager.getCheckedObject().size() < 1 ) {
			UI.showWarning(Message.getString("wip.byscheduletrackin_check_null"));
			return null;
		}
		RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
		List<Object> objList = new ArrayList<Object>();
		List<Lot> lotList = new ArrayList<Lot>();
		objList = tableManager.getCheckedObject();
		for ( Object obj : objList ) {
			lotList.add((Lot) obj);
		}
		context.setLots(lotList);
		return getDefaultDirect();
	}

	@Override
    public boolean canFlipToNextPage() {
        return Boolean.TRUE;
    }
}
