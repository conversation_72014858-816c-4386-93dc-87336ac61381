package com.glory.mes.wip.lot.pack.splitunpack;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.SquareButton;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.mm.client.PackManager;
import com.glory.mes.mm.model.PackageDetail;
import com.glory.mes.mm.model.PackageRule;
import com.glory.mes.mm.model.PackageType;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.MergeManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.Lot;

public class SplitUnPackManagerEditor extends GlcEditor { 

	private static final Logger logger = Logger.getLogger(SplitUnPackManagerEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.pack.splitunpack.SplitUnPackManagerEditor";

	protected static final String FIELD_BOXENTERPRESS = "packDetail-EnterPressed";
	protected static final String FIELD_LOTENTERPRESS = "packLot-EnterPressed";
	
	protected static final String FIELD_PACKDETAIL = "packDetail";
	protected static final String FIELD_PACKLOT = "packLot";
	protected static final String FIELD_LOTINFO = "lotInfo";
	protected static final String FIELD_PACKDETAILTABLE = "packDetailTable";
	protected static final String FIELD_PACKLOTTABLE = "packLotTable";
	protected static final String FIELD_BOXID = "lotInfo";
	protected static final String FIELD_LOTID = "lotInfo";

	protected static final String BUTTON_UNPACKING = "unpacking";
	protected static final String BUTTON_PACKUNPACK = "packUnpack";
	protected static final String BUTTON_ADD = "add";
	protected static final String BUTTON_CLEAR = "clear";
	
	protected static final String FIELD_MAINQTY = "mainQty";
	protected static final String FIELD_TOTALQTY = "totalQty";

	protected GlcFormField packDetailField;
	protected GlcFormField packLotField;
	protected CustomField lotInfoField;
	protected ListTableManagerField packDetailTableField;
	protected CustomField lotInfoField1;
	protected ListTableManagerField packLotTableField;
	protected TextField mainQtyField, totalQtyField, packMainQtyField, packTotalQtyField;
	
	protected EnterPressComposite boxIdEnterPressComposite;
	protected EnterPressComposite lotIdEnterPressComposite;
	
	protected String packageTypeName;
	protected String packageLayer;
	
	protected Lot packedLot;
	protected Lot parentLot;
	protected PackageType packageType;
	protected List<Lot> packedSourceLots;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		ADEditor adEditor = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR));
		packageTypeName = adEditor.getPARAM3();
		packageLayer = adEditor.getPARAM5();
		
		packDetailField = form.getFieldByControlId(FIELD_PACKDETAIL, GlcFormField.class);
		packLotField = form.getFieldByControlId(FIELD_PACKLOT, GlcFormField.class);
		lotInfoField = packDetailField.getFieldByControlId(FIELD_LOTINFO, CustomField.class);
		packDetailTableField = packDetailField.getFieldByControlId(FIELD_PACKDETAILTABLE, ListTableManagerField.class);
		lotInfoField1 = packLotField.getFieldByControlId(FIELD_LOTINFO, CustomField.class);
		packLotTableField = packLotField.getFieldByControlId(FIELD_PACKLOTTABLE, ListTableManagerField.class);
		
		boxIdEnterPressComposite = (EnterPressComposite) lotInfoField.getCustomComposite();
		lotIdEnterPressComposite = (EnterPressComposite) lotInfoField1.getCustomComposite();
		
		EntityForm sourceLotEntityForm = (EntityForm) packDetailTableField.getSlotForm();
		EntityForm packedLotEntityForm = (EntityForm) packLotTableField.getSlotForm();
		
		mainQtyField = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		totalQtyField = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		
		packMainQtyField = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		packTotalQtyField = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_BOXENTERPRESS), this::boxIdEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTENTERPRESS), this::lotIdEnterPressed);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNPACKING), this::unpackingAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PACKUNPACK), this::packUnpackAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ADD), this::addAdapter);
		
		SquareButton packTotalQtyButton = (SquareButton) packTotalQtyField.getButtonByControl(BUTTON_CLEAR);
		packTotalQtyButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				clearAdapter();
			}
		});
	}
	
	protected void boxIdEnterPressed(Object object) {
		try {
			Event event = (Event) object;
			ADBase adBase = (ADBase) event.getProperty(GlcEvent.PROPERTY_DATA);
			Lot currentLot = (Lot) adBase;
			if (currentLot == null) {
				boxIdEnterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				UI.showWarning(Message.getString("mm.package_id_not_exist"));
				boxIdEnterPressComposite.getTxtLot().selectAll();
				return;
			} 
			PackageType newPackageType = getPackageType(currentLot);			
			if (newPackageType == null) {
				return;
			}
			
			if (currentLot.getMainMatType() != null && 
					newPackageType.getPackMainMatType() != null &&
					!currentLot.getMainMatType().equals(newPackageType.getPackMainMatType())) {
				UI.showWarning(Message.getString("mm.pack_mainmattype_not_allow"));
				boxIdEnterPressComposite.getTxtLot().selectAll();
				return;
			}
			
			if (currentLot.getMainMatType() == null ||
					!currentLot.getMainMatType().equals(newPackageType.getPackMainMatType())) {
				boxIdEnterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				UI.showWarning(Message.getString("mm.package_id_not_exist"));
				boxIdEnterPressComposite.getTxtLot().selectAll();
				return;
			}
			
			setPackageType(newPackageType);
			packedLot = currentLot;
			refresh();
			
			//获取包装批次
			boxIdEnterPressComposite.getTxtLot().setText("");		
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	protected void lotIdEnterPressed(Object object) {
		try {
			Event event = (Event) object;
			ADBase adBase = (ADBase) event.getProperty(GlcEvent.PROPERTY_DATA);
			Lot currentLot = (Lot) adBase;
			if (currentLot == null) {
				lotIdEnterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				UI.showWarning(Message.getString("wip.lot_not_found"));
				return;
			}	
			
			if (parentLot == null) {
				if (getPackageType() == null) {
					UI.showWarning(Message.getString("mm.pack_package_type_not_exist"));
					return;
				}
				
				
				if (!StringUtils.equals(getPackageType().getSourceLotState(), currentLot.getState())) {
					UI.showWarning(Message.getString("error.lot_state_not_allow"));
					return;
				}
				
				if (!StringUtil.isEmpty(getPackageType().getSourceMainMatType())) { //源包装类型未维护时，不检查此项
					if (!currentLot.getMainMatType().equals(getPackageType().getSourceMainMatType())) {
						UI.showWarning(Message.getString("mm.pack_mainmattype_not_allow"));
						return;
					}
				}
				parentLot = currentLot;
			}
			
			addSource(currentLot);
			lotIdEnterPressComposite.getTxtLot().setText("");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}

	protected void unpackingAdapter(Object object) {
		try {
			Lot packedLot = getPackedLot();
			if (packedLot == null) {
				UI.showInfo(Message.getString("mm.pack_unpack_lot_is_null"));
				return;
			}
			
			boolean confirmUnPack = UI.showConfirm(Message.getString("mm.pack_unpack_confirm"));
			if (confirmUnPack) {
				// 拆包处理
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> tmpLots = carrierLotManager.unpackLotMany(packedLot, Env.getSessionContext());
				
				//清空
				clearForm();
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipUnPacked()));
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	
	}

	protected void packUnpackAdapter(Object object) {
		try {
			Lot packedLot = getPackedLot();
			if (packedLot == null) {
				UI.showInfo(Message.getString("mm.pack_unpack_lot_is_null"));
				return;
			}
			
			List<Lot> checkedPackLotList = getCheckedPackLotList();
			if (checkedPackLotList.size() == 0) {
                UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			boolean confirm = UI.showConfirm(Message.getString("mm.pack_unpack_confirm"));
			if (confirm) {
				// 部分拆包处理
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				carrierLotManager.unpackLotMany(packedLot, checkedPackLotList, Env.getSessionContext());
				
				removeLots(checkedPackLotList);
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipUnPacked()));
			} 
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	
	}

	protected void addAdapter(Object object) {
		try {
			Lot packedLot = getPackedLot();
			if (packedLot == null) {
				UI.showInfo(Message.getString("mm.pack_unpack_lot_is_null"));
				return;
			}
			
			// 获取选择的明细，并保存到List
			List<Lot> lotList = getLotList();
			if (lotList.size() == 0) {
				UI.showInfo(Message.getString("mm.packing_add_mlot_is_null"));
			} else {
				for(Lot lot : lotList) {
					if(BigDecimal.ZERO.compareTo(lot.getMainQty()) == 0) {
						UI.showInfo(Message.getString("mm.packing_add_mlotqty_is_zero"));
						return;
					}
				}
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				packedLot = carrierLotManager.addPackLotMany(packedLot, lotList, this.getPackageType(), Env.getSessionContext());
				
				refresh();
				sourceClearForm();
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipAddPacked()));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	
	}
	
	protected void addSource(ADBase adBase) {
		try {
			if (adBase instanceof Lot) {
				//未包装的lot
				List<Lot> lots = new ArrayList<Lot>();
				lots.addAll((List<Lot>) packLotTableField.getListTableManager().getInput());

				LotManager lotManager = Framework.getService(LotManager.class);
				PackManager packManager = Framework.getService(PackManager.class);
				MergeManager mergeManager  = Framework.getService(MergeManager.class);
				// table 里面的lot
				Lot lot = (Lot) adBase;
				PackageRule packageRule = packManager.getPackageRule(Env.getOrgRrn(), lot, PackageType.OBJECT_TYPE_LOT, getPackageType().getName(), null);
				if (lots != null && lots.size() > 0) {
					// 判断界面有没有已存在的lot
					for (Lot oldLot : lots) {
						if (oldLot.getLotId().equals(lot.getLotId())) {
							oldLot.setMainQty(lot.getMainQty());
							oldLot.setSubQty(lot.getSubQty());
							packLotTableField.getListTableManager().setInput(lots);
							return;
						}
					}
					
					if (!PackageType.OBJECT_TYPE_LOT.equals(getPackageType().getObjectType())) {
						UI.showError(Message.getString("mm.pack_object_type_mlot"));
						return;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceMainMatType())
							&& !getPackageType().getSourceMainMatType().equals(lot.getMainMatType())) {
						UI.showError(Message.getString("mm.pack_mainmattype_not_allow"));
						return;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceSubMatType())
							&& !getPackageType().getSourceSubMatType().equals(lot.getSubMatType())) {
						UI.showError(Message.getString("mm.pack_submattype_not_allow"));
						return;
					}
					if (!StringUtil.isEmpty(packageRule.getMergeRuleName())) {
						// 检查包装合批规则,如果不通过则加入rejectSourceTableManager
						List<Lot> childLots = new ArrayList<>();
						childLots.add(lot);
					
						MergeRuleResult pckResult = mergeManager.checkLotPackRule(parentLot, childLots, packageRule.getMergeRuleName(), false);
						if (!pckResult.isSuccess()) {
							UI.showError(Message.getString("mm.pack_mergerule_not_allow"));
							return;
						} else {
							lots.add(lot);
						}
					}
				} else {
					lots.add(lot);
				}
				
				//累计lot后的合计
				BigDecimal mainTotalQty = BigDecimal.ZERO;
				if (!packTotalQtyField.getText().trim().isEmpty()) {
					mainTotalQty = new BigDecimal(packTotalQtyField.getText());
				}
				BigDecimal mainQty = lot.getMainQty();
				mainTotalQty = mainQty.add(mainTotalQty);
				
				BigDecimal countTotalQty = new BigDecimal(lots.size());

				// 如果不满足,加入sourceTableManager累计SourceCount和SourceMainQty
				packMainQtyField.setText(countTotalQty.toString());
				packTotalQtyField.setText(mainTotalQty.toString());
				packLotTableField.getListTableManager().setInput(lots);
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}

	protected void clearAdapter() {
		List<Object> checkedList = packLotTableField.getListTableManager().getCheckedObject();
		List<Lot> clearLots = new ArrayList<>();
		
		BigDecimal sourceMainQty =  BigDecimal.ZERO;
		for (Object object1 : checkedList) {
			if (object1 instanceof Lot) {
				Lot clearLot = (Lot)object1;
				clearLots.add(clearLot);
				sourceMainQty = clearLot.getMainQty().add(sourceMainQty);
			}
		}
		
		List<Lot> sourceLots = new ArrayList<Lot>();
		sourceLots.addAll((List<Lot>) packLotTableField.getListTableManager().getInput());
		sourceLots.removeAll(clearLots);
		if (clearLots.contains(parentLot)) {
			if (sourceLots.size()<=0) {
				parentLot = null;
			} else {
				parentLot = sourceLots.get(0);
			}
		}
		
		BigDecimal sourceMainTotalQty = BigDecimal.ZERO;
		if (!packTotalQtyField.getText().trim().isEmpty()) {
			sourceMainTotalQty = new BigDecimal(packTotalQtyField.getText());
		}
		sourceMainTotalQty = sourceMainTotalQty.subtract(sourceMainQty);
		
		packMainQtyField.setText(String.valueOf(sourceLots.size()));
		packTotalQtyField.setText(sourceMainTotalQty.toString());
		packLotTableField.getListTableManager().setInput(sourceLots);
	}
	
	protected PackageType getPackageType(Lot lot) throws Exception {
		PackManager packManager = Framework.getService(PackManager.class);
		PackageType newPackageType = packManager.getPackageType(Env.getOrgRrn(), lot, PackageType.OBJECT_TYPE_LOT, packageLayer, null);
		if (newPackageType == null) {
			if (StringUtil.isEmpty(packageTypeName)) {
				logger.warn("Package type name is null, please set adeditor param2 as package type name.");
				UI.showWarning(Message.getString("mm.pack_package_type_not_exist"));				
			} else {
				newPackageType = packManager.getPackageType(Env.getOrgRrn(), packageTypeName, PackageType.OBJECT_TYPE_LOT);
				if (newPackageType == null) {
					logger.warn("Package type " + packageTypeName + "is not found");
					UI.showWarning(Message.getString("mm.pack_package_type_not_exist"));					
				}
			}
		}
		return newPackageType;
	}
	
	protected void refresh() {
		try {
			PackManager packManager = Framework.getService(PackManager.class);
			List<PackageDetail> lstPackageDetail = packManager.getPackageDetail(PackageType.OBJECT_TYPE_LOT, packedLot.getObjectRrn());
			if (lstPackageDetail.isEmpty()) {
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			packedSourceLots = new ArrayList<Lot>();
			for (PackageDetail pd : lstPackageDetail) {
				//获取批次信息
				Lot lot = lotManager.getLot(pd.getSourceObjectRrn());
				lot.setMainQty(pd.getPackMainQty());//批次在该箱中包装的主数量
				lot.setSubQty(pd.getPackSubQty());//批次在该箱中包装的子数量
				packedSourceLots.add(lot);
			}
			
			resetPackedSource();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected PackageType getPackageType() {
		return packageType;
	}

	protected void setPackageType(PackageType packageType) {
		this.packageType = packageType;
	}
	
	protected Lot getPackedLot() {
		return packedLot;
	}
	
	protected List<Lot> getCheckedPackLotList() {
		List<Object> checkedList = packDetailTableField.getListTableManager().getCheckedObject();
		if (checkedList.size() > 0) {
			List<Lot> lstPackLot = new ArrayList<Lot>();
			for (Object obj : checkedList) {
				lstPackLot.add((Lot)obj);
			}
			return lstPackLot;
		} else {
			return new ArrayList<Lot>();
		}
	}
	
	protected void resetPackedSource() {
		if (packedSourceLots == null || packedSourceLots.size() == 0) {
			mainQtyField.setText("");
			totalQtyField.setText("");
		} else {
			mainQtyField.setText(String.valueOf(packedSourceLots.size()));
			BigDecimal mainTotalQty = BigDecimal.ZERO;
			for (Lot packedSourceLot : packedSourceLots) {
				BigDecimal mainQty = packedSourceLot.getMainQty();
				mainTotalQty = mainQty.add(mainTotalQty);
			}
			totalQtyField.setText(String.valueOf(mainTotalQty));
		}
		packDetailTableField.getListTableManager().setInput(packedSourceLots);
	}
	
	protected List<Lot> getLotList() {
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll((List<Lot>) packLotTableField.getListTableManager().getInput());
		return lots;
	}
	
	protected void removeLots(List<Lot> lots) {
		packedSourceLots.removeAll(lots);
		resetPackedSource();
	}
	
	protected void sourceClearForm() {
		packMainQtyField.setText("0");
		packTotalQtyField.setText("0");
		packLotTableField.getListTableManager().setInput(new ArrayList<Lot>());
		parentLot = null;
	}
	
	protected void clearForm() {
		mainQtyField.setText("0");
		totalQtyField.setText("0");
		packDetailTableField.getListTableManager().setInput(new ArrayList<Object>());
		packedLot = null;
	}

}