package com.glory.mes.wip.lot.pack.scrap;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.log4j.Logger;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.FormLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IFormPart;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.event.AppEvent;
import com.glory.framework.base.application.event.AppEventManager;
import com.glory.framework.base.entitymanager.forms.EntityQueryProgress;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.entitymanager.forms.ViewQueryProgress;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.ui.dialog.QueryProgressMonitorDialog;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.framework.core.exception.ExceptionBundle;

public class ScrapPackSection extends EntitySection{
	private static final Logger logger = Logger.getLogger(ScrapPackSection.class);

	protected ToolItem itemPackage;
	protected ToolItem itemUnPackage;
	protected ToolItem itemClear;	
	protected ListTableManager packTableManager;
	protected ListTableManager tableManager;
	protected NatTable natTable;
	protected Text txtPackLot, txtPackDate;	
	protected ManagedForm managedForm;
	private QueryForm queryForm;
	private boolean indexFlag = true;
	protected IFormPart spart;
	
	protected Text txtSourceMainQty;
	protected Text txtPackMainQty;
	
	protected static int PROGRESS_THRESHOLD = 100;
	protected static int MONITOR_THRESHOLD = 999;
	protected long totalNumber;
	protected long showNumber;
	private String packageType;
	private String whereClause;
	
	// 打印按钮
    protected ToolItem itemPrint;
	
	
	public ScrapPackSection(ADTable adTable, String packageType) {
		super(adTable);
		this.tableManager = new ListTableManager(adTable);
		this.packageType = packageType;
	} 
	
	public Lot searchLot(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			return lot;
		} catch (Exception e) {
			logger.warn("ScrapPkgSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	public List<LotScrap> searchLotScrapByParentLotRrn(Long parentLotRrn) {
		try {			
			ADManager adManager = Framework.getService(ADManager.class);	
			List<LotScrap> lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class,
					Integer.MAX_VALUE, " parentLotRrn = " + parentLotRrn + " AND parentLotRrn is not null ", null);
			return lotScraps;
		} catch (Exception e) {
			logger.warn("ScrapPkgSection searchLotScrapByParentLotRrn(): Lot isn' t exsited!");
		}
		return null;
	}

	protected void createSectionContent(Composite client) {
		try {
			final FormToolkit toolkit = form.getToolkit();	
			client.setLayoutData(new GridData(GridData.FILL_BOTH));
			this.managedForm = (ManagedForm) form;
			
			Composite queryComp = new Composite(client, SWT.NONE);
		    GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		    queryComp.setLayoutData(gd);
	        FormLayout formLayout = new FormLayout();
	        formLayout.marginHeight = 0;
	        formLayout.marginWidth = 10;
	    	gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 300;
	        queryComp.setLayout(formLayout);	         
	        
	        setQueryForm(createQueryFrom(form, queryComp));
	        
	        if (this.getQueryForm().getADTable().getGridYQuery() != null) {
	            this.getQueryForm().getADTable().getGridYQuery().intValue();
	         }
			
	        SquareButton btnQuery = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
	        btnQuery.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));
	        
	        FormData fd = new FormData();
	        fd.bottom = new FormAttachment(this.getQueryForm(), -20, SWT.BOTTOM);
	        fd.left = new FormAttachment(this.getQueryForm(), 20, SWT.RIGHT);
			btnQuery.setLayoutData(fd);
			btnQuery.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					queryAdapter();
				}
			});
		
			
			GridLayout layout = new GridLayout();
			layout.marginWidth = 5;
			layout.marginHeight = 5;
			
			Composite resultComp = new Composite(client, SWT.NONE);
	        layout = new GridLayout(); 
	        layout.verticalSpacing = 0;
	        layout.marginHeight = 0;
	        resultComp.setLayout(layout);
	        resultComp.setLayoutData(gd);
		    createNewViewer(resultComp, form);
		    section.setClient(client);
		    
		    GridData gdata = new GridData(GridData.FILL_HORIZONTAL);
		    gdata.horizontalAlignment = SWT.END;
		    gdata.grabExcessHorizontalSpace = true;
		    
			Composite textComposite = toolkit.createComposite(resultComp);
			textComposite.setLayout(new GridLayout(4, false));
			textComposite.setLayoutData(gdata);
			toolkit.createLabel(textComposite, Message.getString(ExceptionBundle.bundle.CommonTotal()));
			txtSourceMainQty = toolkit.createText(textComposite, "", SWT.BORDER);
			
			GridData gds = new GridData(GridData.BEGINNING);
			Composite compTxt = toolkit.createComposite(client);
			compTxt.setLayout(new GridLayout(5, false));
			compTxt.setLayoutData(gds);
			
			GridData gText = new GridData();
			gText.widthHint = 156;
			
			Label packLabel = toolkit.createLabel(compTxt, Message.getString("wip.pack_id"));
			packLabel.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
			txtPackLot = toolkit.createText(compTxt, "", SWT.NONE);
			txtPackLot.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
			txtPackLot.setLayoutData(gText);
			txtPackLot.setTextLimit(32);			
			txtPackLot.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					txtPackLot.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
						String packId = txtPackLot.getText();
						txtPackLot.setText(packId.toUpperCase());
						Lot packedLot = searchLot(packId.toUpperCase());
						if (packedLot == null) {
							txtPackLot.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							return;
						}
						txtPackLot.selectAll();
						
						List<LotScrap> lotScraps = searchLotScrapByParentLotRrn(packedLot.getObjectRrn());
						if (lotScraps != null) {	
							for(LotScrap lotScrap : lotScraps) {
							    Lot lot = searchLot(lotScrap.getLotId());
							    if (lot != null) {
							        lotScrap.setAttribute1(lot.getWoId());
							        lotScrap.setAttribute2(lot.getCustomerCode());
							        lotScrap.setAttribute3(lot.getCustomerOrder());
							        lotScrap.setAttribute4(lot.getCustomerLotId());
							        lotScrap.setAttribute5(lot.getCustomerPartId());
							    }
							}
							packTableManager.setInput(lotScraps);
							packTableManager.refresh();		
							SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							txtPackDate.setText(sdf.format(packedLot.getCreated()).toString());
						} else {
							packTableManager.setInput(new ArrayList<LotScrap>());
							packTableManager.refresh();		
							
						}
						initSourceMainQty();
						break;
					}
				}
			
			});
			txtPackLot.addFocusListener(new FocusListener() {
				public void focusGained(FocusEvent e) {}
				
				public void focusLost(FocusEvent e) {
					Text tLotId = ((Text) e.widget);
					tLotId.setText(tLotId.getText().toUpperCase());
				}
			});
			
			Label packDate = toolkit.createLabel(compTxt, Message.getString("wip.pack_time"));
			packDate.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
			txtPackDate = toolkit.createText(compTxt, "", SWT.BORDER);
			txtPackDate.setLayoutData(gText);
			txtPackDate.setTextLimit(32);
			txtPackDate.setEditable(false);
			
			Group packComposite = new Group(client, SWT.NONE);
			GridLayout gridLayout = new GridLayout();
			packComposite.setText(Message.getString("wip.pack_detail"));
			packComposite.setLayout(gridLayout);
			packComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			GridData gdatas = new GridData(GridData.FILL_HORIZONTAL);
			gdatas = new GridData(GridData.FILL_BOTH);
			gdatas.heightHint = 200;
			
			Composite packTableComposite = toolkit.createComposite(packComposite);
			packTableComposite.setLayout(new GridLayout(1, false));
			packTableComposite.setLayoutData(gdatas);
			
			packTableManager = new ListTableManager(this.getTable(), true);
			packTableManager.newViewer(packTableComposite);
			packTableManager.addICheckChangedListener(new ICheckChangedListener() {

				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if (checked) {
						if(null == eventObjects || eventObjects.isEmpty()) {
							return;
						}
						LotScrap sbd = (LotScrap) eventObjects.get(0);
						List<LotScrap> sbds = (List<LotScrap>)(List) packTableManager.getInput();
						packTableManager.setCheckedObject(sbds);
					}
				}			
			});
			
		    gd = new GridData(GridData.FILL_BOTH);
			gd.horizontalAlignment = SWT.END;
			gd.grabExcessHorizontalSpace = true;
			
			Composite packTextComposite = toolkit.createComposite(packComposite);
			packTextComposite.setLayout(new GridLayout(4, false));
			packTextComposite.setLayoutData(gd);
			toolkit.createLabel(packTextComposite, Message.getString(ExceptionBundle.bundle.CommonTotal()));
			txtPackMainQty = toolkit.createText(packTextComposite, "", SWT.BORDER);	
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);		
		createToolItemPackage(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnPackage(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemPrint(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClean(tBar);
		section.setTextClient(tBar);
	}
	
	public void createToolItemPackage(ToolBar tBar) {
		itemPackage = new ToolItem(tBar, SWT.NULL);
		itemPackage.setText(Message.getString("wip.pack"));
		itemPackage.setImage(SWTResourceCache.getImage("active"));
		itemPackage.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				packageAdpater();
			 }
		});
	}
	
	protected void createToolItemUnPackage(ToolBar tBar) {
		itemUnPackage = new ToolItem(tBar, 8);
		itemUnPackage.setText(Message.getString("wip.unpack"));
		itemUnPackage.setImage(SWTResourceCache.getImage("inactive"));
		itemUnPackage.addSelectionListener(new SelectionAdapter() {
			
			@Override
			public void widgetSelected(SelectionEvent event) {
				unPackageAdpater();
			}
		});
	}
	
	protected void createToolItemPrint(ToolBar tBar) {
		this.itemPrint = new ToolItem(tBar, 8);
		this.itemPrint.setText(Message.getString(ExceptionBundle.bundle.CommonPrint()));
		this.itemPrint.setImage(SWTResourceCache.getImage("label"));
		this.itemPrint.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				printAdapter();
			}
		});
	}
	
	private void createToolItemClean(ToolBar tBar) {
		itemClear = new ToolItem(tBar, SWT.NULL);
		itemClear.setText(Message.getString(ExceptionBundle.bundle.CommonClear()));
		itemClear.setImage(SWTResourceCache.getImage("refresh"));
		itemClear.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				cleanAdpater();
			}
		});
	}
	
	protected void packageAdpater() {
		try {
			List<Object> selectLotScraps = tableManager.getCheckedObject(); 
			if (selectLotScraps.size() == 0) {
				UI.showError(Message.getString("mm.please_select_records"));
				return;
			}
			
			Set<String> actionCodeSet = new HashSet<String>();
			List<LotScrap> lotScraps = new ArrayList<LotScrap>();
			for (Object selectLotScrap : selectLotScraps) {
				LotScrap sbd = (LotScrap) selectLotScrap;
				lotScraps.add(sbd);
				actionCodeSet.add(sbd.getActionCode());
			}
			
			if (actionCodeSet.size() > 1) {
				if (!UI.showConfirm(Message.getString("wip.pack_choice_multi_scrapcode"))) {
					return;
				}
			}
			
			if (lotScraps != null && lotScraps.size() > 0) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				Lot lot = carrierLotManager.packScrapLot(lotScraps, txtPackLot.getText(), packageType, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.pack_scrap_cuccess") + Message.getString("wip.pack_scrap_new_lot")  + lot.getLotId());
				txtPackLot.setText(lot.getLotId().toUpperCase());
				Lot packedLot = searchLot(lot.getLotId().toUpperCase());
				txtPackLot.selectAll();
				SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				txtPackDate.setText(sdf.format(packedLot.getCreated()).toString());
				List<LotScrap> allLotScraps = (List<LotScrap>)(List)this.tableManager.getInput();
				allLotScraps.removeAll(lotScraps);
				List<LotScrap> packlotScraps = searchLotScrapByParentLotRrn(packedLot.getObjectRrn());
				for(LotScrap lotScrap : packlotScraps) {
				    Lot packlot = searchLot(lotScrap.getLotId());
				    if (packlot != null) {
				        lotScrap.setAttribute1(packlot.getWoId());
				        lotScrap.setAttribute2(packlot.getCustomerCode());
				    }
				}
				this.tableManager.setInput(allLotScraps);
				this.packTableManager.setInput(packlotScraps);
			}
			initSourceMainQty();
		} catch (Exception e) {
			tableManager.refresh();
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error(e.getMessage(), e);
			return;
		}
	}
	
	protected void unPackageAdpater() {
		try {
			List<Object> selectLotScraps = packTableManager.getCheckedObject(); 
			if (selectLotScraps.size() == 0) {
				UI.showError(Message.getString("mm.please_select_records"));
				return;
			}
			
			List<LotScrap> lotScraps = new ArrayList<LotScrap>();
			for (Object selectLotScrap : selectLotScraps) {
				LotScrap sbd = (LotScrap) selectLotScrap;
				lotScraps.add(sbd);			
			}			
			
			if (lotScraps != null && lotScraps.size() > 0) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				Lot packedLot = searchLot(txtPackLot.getText().toUpperCase());
				packedLot = carrierLotManager.unPackScrapLot(packedLot, lotScraps, packageType, Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.unpack_scrap_cuccess"));
				List<LotScrap> allLotScraps = (List<LotScrap>)(List)packTableManager.getInput();
				allLotScraps.removeAll(lotScraps);
				packTableManager.setInput(allLotScraps);
			}
			initSourceMainQty();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error(e.getMessage(), e);
			return;
		}
	}
	
	protected void printAdapter() {
        try {
        	String txtPackLots = txtPackLot.getText();
        	if (txtPackLots != null && txtPackLots.length() != 0) {
        		LotManager lotManager = Framework.getService(LotManager.class);
        		Lot sourceLot = lotManager.getLotByLotId(Env.getOrgRrn(),txtPackLots.toUpperCase());
    			if (sourceLot != null) {
    				AppEvent appEvent = new AppEvent();
					appEvent.setEventParam1("PRINT"); //actionType
					appEvent.setEventParam2("LotList"); //objectType
					appEvent.setEventParam3(sourceLot); //object
					AppEventManager.postEvent(AppEvent.EVENT_ID_PRINTLABEL, appEvent);
					
    			} else {
    				UI.showWarning(Message.getString("wip.lot_not_found") +
    						Message.getString("mm.package_id"));
    				return;
    			}
        	}else {
        		UI.showWarning(Message.getString("mm.package_type_object_has_no_choice") +
						Message.getString("mm.package_id"));
				return;
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void cleanAdpater() {
		txtPackLot.setText("");
		txtPackDate.setText("");
		tableManager.setInput(null);;
		packTableManager.setInput(null);
	}
	
	private void initSourceMainQty(){
		List<LotScrap> sourceLotScraps = (List<LotScrap>)(List)this.tableManager.getInput();
		if (sourceLotScraps != null && sourceLotScraps.size() > 0) {
			long sumSource = 0;
			for (LotScrap lotscrap : sourceLotScraps) {
				sumSource += lotscrap.getMainQty().longValue();
			}
			txtSourceMainQty.setText(String.valueOf(sumSource));
		} else {
			txtSourceMainQty.setText("");
		}
	
		List<LotScrap> packLotScraps = (List<LotScrap>)(List)packTableManager.getInput();
		if (packLotScraps != null && packLotScraps.size() > 0) {
			long sumPack = 0;
			for (LotScrap lotscrap : packLotScraps) {
				sumPack += lotscrap.getMainQty().longValue();
			}
			txtPackMainQty.setText(String.valueOf(sumPack));
		} else {
			txtPackMainQty.setText("");
		}	
	}
	
	public ListTableManager getTableManager() {
		return this.tableManager;
	}
	
	protected ADTable getADTable() {
		return this.getTableManager().getADTable();
	}
	
	public void setLayout(Composite parent) {
		this.section.setLayoutData(new GridData(1808));
	}
	
	protected void createNewViewer(Composite client, IManagedForm form) {
		this.tableManager = new ListTableManager(this.getTable(), true);
        this.tableManager.addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (checked) {
					if(null == eventObjects || eventObjects.isEmpty()) {
						return;
					}
					LotScrap sbd = (LotScrap) eventObjects.get(0);
					List<LotScrap> sbds = (List<LotScrap>)(List)tableManager.getInput();
					tableManager.setCheckedObject(sbds);
				}
			}			
		});
		this.tableManager.setIndexFlag(this.indexFlag);
		this.tableManager.newViewer(client);
	}

	protected QueryForm createQueryFrom(final IManagedForm form, Composite queryComp) {
		return new QueryForm(getADManger(), queryComp, SWT.NONE, tableManager.getADTable(), form.getMessageManager());
	}
	
	protected void queryAdapter() {
		managedForm.getMessageManager().removeAllMessages();
		if (!getQueryForm().validate()){
			return;
		}
		
		String whereClause = " 1 = 1 " + getQueryForm().createWhereClause();
		whereClause = StringUtil.relpaceWildcardCondition(whereClause);
		setWhereClause(whereClause);
		refresh();
	}

	public void refresh() {
		try {
			long count = this.getEntityNumber();
			List<Object> adList = new ArrayList();
			if (count > (long) this.getMonitorThreshold()) {
				Object progress;
				if (this.tableManager.getADTable().getIsView()) {
					progress = new ViewQueryProgress(this.getADManger(), count, this.getProcessThreshold(),
							this.tableManager.getADTable(), this.getWhereClause(), "");
				} else {
					progress = new EntityQueryProgress(this.getADManger(), count, this.getProcessThreshold(),
							this.tableManager.getADTable(), this.getWhereClause(), "");
				}

				QueryProgressMonitorDialog progressDiglog = new QueryProgressMonitorDialog(UI.getActiveShell(), "");
				progressDiglog.run(true, true, (IRunnableWithProgress) progress);
				adList = ((EntityQueryProgress) progress).getAdList();
			} else {
				ADManager manager = this.getADManger();
				List currentList;
				if (this.tableManager.getADTable().getIsView()) {
					currentList = manager.getEntityMapListByColumn(Env.getOrgRrn(),
							this.tableManager.getADTable().getObjectRrn(), 0, Env.getMaxResult(), this.getWhereClause(),
							"", false);
					((List) adList).addAll(currentList);
				} else if (this.tableManager.getADTable().isContainMainAttribute()) {
					currentList = manager.getEntityList(Env.getOrgRrn(), this.tableManager.getADTable().getObjectRrn(),
							0, Env.getMaxResult(), this.getWhereClause(), "", true,
							this.tableManager.getADTable().getMainAttributes());
					((List) adList).addAll(currentList);
				} else {
					currentList = manager.getEntityList(Env.getOrgRrn(), this.tableManager.getADTable().getObjectRrn(),
							Env.getMaxResult(), this.getWhereClause(), "");
					((List) adList).addAll(currentList);
				}
			}

			this.showNumber = (long) ((List) adList).size();
			this.createSectionDesc(this.section);
			for(Object o : (List) adList) {
				LotScrap lotScrap = (LotScrap) o;
			    Lot lot = searchLot(lotScrap.getLotId());
			    if (lot != null) {
			        lotScrap.setAttribute1(lot.getWoId());
			        lotScrap.setAttribute2(lot.getCustomerCode());
			    }
			}
			this.tableManager.setInput((List) adList);
			initSourceMainQty();
		} catch (Exception var6) {
			logger.error("Error at Refresh ", var6);
		}
	}
	
	public String getWhereClause() {
		if (this.tableManager.getADTable() != null && this.tableManager.getADTable().getWhereClause() != null
				&& this.tableManager.getADTable().getWhereClause().trim().length() > 0) {
			return this.whereClause != null && this.whereClause.trim().length() > 0
					? "( " + this.tableManager.getADTable().getWhereClause() + ") AND ( " + this.whereClause + ")"
					: "( " + this.tableManager.getADTable().getWhereClause() + ")";
		} else {
			return this.whereClause;
		}
	}
	
	protected long getEntityNumber() {
		try {
			ADManager entityManager = this.getADManger();
			this.totalNumber = entityManager.getEntityCount(Env.getOrgRrn(),
					this.getTableManager().getADTable().getObjectRrn(), this.getWhereClause());
			return this.totalNumber;
		} catch (Exception var2) {
			logger.error("EntityBlock : createSectionDesc ", var2);
			return 0L;
		}
	}
	
	public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	public int getProcessThreshold() {
		return PROGRESS_THRESHOLD;
	}
	
	public int getMonitorThreshold() {
		return MONITOR_THRESHOLD;
	}
	
	/**
	 * @param queryForm the queryForm to set
	 */
	public void setQueryForm(QueryForm queryForm) {
		this.queryForm = queryForm;
	}

	/**
	 * @return the queryForm
	 */
	public QueryForm getQueryForm() {
		return queryForm;
	}
	
	
}
