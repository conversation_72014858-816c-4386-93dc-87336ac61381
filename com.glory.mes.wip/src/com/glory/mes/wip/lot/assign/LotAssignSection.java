package com.glory.mes.wip.lot.assign;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.carrier.CarrierLotComposite;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.comp.ComponentAssignComposite;
import com.glory.mes.wip.comp.ComponentComposite.DiffType;
import com.glory.mes.wip.lot.AbstractAssignComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;

@Deprecated
public class LotAssignSection extends EntitySection {
	
	public AbstractAssignComposite assignComposite;
	public EntityForm lotDetailsForm;
	
	public CarrierLotComposite carrierLotComposite;
	
	protected AuthorityToolItem itemAssign;

	private Composite parent;
	private Carrier targetCarrier;
	private Lot sourceLot;
	
	public static String KEY_LOTASSIGN = "lotAssign";
	
	public LotAssignSection(ADTable adTable) {
		super(adTable);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemAssign(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemAssign(ToolBar tBar) {
		itemAssign = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_LOTASSIGN);
		itemAssign.setEnabled(false);
		itemAssign.setText(Message.getString("wip.assign"));
		itemAssign.setImage(SWTResourceCache.getImage("assign"));
		itemAssign.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				assignAdapter();
			}
		});
	}
	
	protected void assignAdapter() {
		try {
			// 绑定载具
			DurableManager durableManager = Framework.getService(DurableManager.class);
			
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), assignComposite.targetCarrier.getDurableId(), true);
			
			Lot lot = assignComposite.sourceLot;
			
			if (lot == null || lot.getObjectRrn() == null) {
				UI.showInfo(Message.getString("wip.carrier_assign_lot_scan"));
				return;
			}
			
			if (BigDecimal.ZERO.compareTo(lot.getMainQty()) >= 0) {
				UI.showInfo(Message.getString("wip.carrier_assign_lot_qty"));
				return;
			}
			
			
			String lotId = "";
			if (lot != null) {
				lotId = lot.getLotId();
			}
			
			if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				carrierLotManager.assignCarrierLotOnly(carrier, null, Lists.newArrayList(lot), true, true, Env.getSessionContext());
			} else {
				ComponentAssignComposite composite = (ComponentAssignComposite) assignComposite;
				
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<ComponentUnit> addComponents = composite.targetComponentComposite.getComponentsDiff().get(DiffType.ADD);
				if (addComponents != null && addComponents.size() != 0) {
					// 不允许两个以及多个批次同时绑定   
					Map<Long, Integer> map = new HashMap<Long, Integer>(); 
					for (ComponentUnit componentUnit : addComponents) {
						map.put(componentUnit.getParentUnitRrn(), 1);
					}
					
					Set<Long> keySet = map.keySet();
					if (keySet.size() > 1) {
						UI.showInfo(Message.getString("wip.not_allow_more_than_one_lot"));
						return;
					}
					
					for (Long objectRrn : keySet) {
						if (!assignComposite.sourceLot.getObjectRrn().equals(objectRrn)) {
							UI.showInfo(Message.getString("wip.source_lot_component_not_match"));
							return;
						}
					}
					
					carrierLotManager.assignCarrierLot(carrier, lotId, addComponents, false, false, Env.getSessionContext());
				} else {
					UI.showError(Message.getString("wip.need_add_assign_component"));
					return;
				}
			}
			
			UI.showInfo(Message.getString("wip.assign_success"));
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public void refresh() {
		if (assignComposite instanceof ComponentAssignComposite) {
			ComponentAssignComposite composite = (ComponentAssignComposite) assignComposite;
			String carrierId = composite.txtTargetCarrierId.getText();
			// 刷新 中部  批次信息
			composite.setSourceLot(null);
			composite.sourceTableManager.getInput().clear();
			// 刷新 右边 批次信息
			if (composite.targetComponentComposite != null) {
				composite.searchCarrier(carrierId, composite.targetComponentComposite, true);	
			}
		} else {
			LotAssignQtyComposite composite = (LotAssignQtyComposite) assignComposite;
			// 刷新界面信息
			composite.afterAssign();
		}
		// 刷新 左边上方载具信息
		carrierLotComposite.getLotsByCarrierId(getTargetCarrier().getDurableId());
		// 刷新 左边下方载具信息
		lotDetailsForm.setObject(new Lot());
		lotDetailsForm.loadFromObject();
		itemAssign.setEnabled(false);
		setSourceLot(null);
		setTargetCarrier(assignComposite.getTargetCarrier());
		super.refresh();
	}
	
	@Override
	protected void createSectionContent(Composite parent) {
		try {
			this.parent = parent;
			if (getSourceLot() != null) {
				Lot lot = (Lot) getSourceLot();
				if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
					LotAssignQtyComposite assignComposite = new LotAssignQtyComposite(
							parent, SWT.NONE, getTargetCarrier(), lot);
					this.assignComposite = assignComposite;
				} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
					ComponentAssignComposite assignComposite = new ComponentAssignComposite(parent, SWT.NONE, false, true, true);
					assignComposite.searchCarrier(getTargetCarrier().getDurableId(), assignComposite.targetComponentComposite, true);
					assignComposite.searchLot(lot.getLotId());
					assignComposite.txtSourceLotId.setEditable(false);
					assignComposite.txtTargetCarrierId.setEditable(false);
					this.assignComposite = assignComposite;
				}
				itemAssign.setEnabled(true);
			} else {
				itemAssign.setEnabled(false);
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void reflow() {
		if (assignComposite != null && !assignComposite.isDisposed()) {
			assignComposite.dispose();
			assignComposite = null;
		}
		createSectionContent(parent);
		parent.layout();
	}

	public Carrier getTargetCarrier() {
		return targetCarrier;
	}

	public void setTargetCarrier(Carrier targetCarrier) {
		this.targetCarrier = targetCarrier;
	}

	public Lot getSourceLot() {
		return sourceLot;
	}

	public void setSourceLot(Lot sourceLot) {
		this.sourceLot = sourceLot;
	}

	public AbstractAssignComposite getAssignComposite() {
		return assignComposite;
	}

	public void setAssignComposite(AbstractAssignComposite assignComposite) {
		this.assignComposite = assignComposite;
	}
	
}
