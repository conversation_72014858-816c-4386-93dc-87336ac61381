package com.glory.mes.wip.lot.bonding;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class LotBondingLeftComposite extends Composite {

	private static final Logger logger = Logger.getLogger(LotBondingLeftComposite.class);
	
	private static final String TABLE_NAME = "WIPLotComponentBonding";	
	//Table种是否显示checkBox
	protected boolean checkFlag;
	protected ListTableManager lotComponentTableManager;
	
	protected HeaderText txtCarrierId;
	protected HeaderText txtLotId;

//	protected int tableHeigthHint = Toolkit.getDefaultToolkit().getScreenSize().height / 3;
	protected int tableHeigthHint = 550;
	
	protected LotBondingSection section;
	
	public LotBondingLeftComposite(Composite parent, int style, boolean checkFlag, LotBondingSection section) {
		super(parent, style);
		this.checkFlag = checkFlag;
		this.section = section;
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite inputComposite = new Composite(this, SWT.NONE);	
			inputComposite.setLayout(new GridLayout(4, false));
			inputComposite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
			Label lblLotId = new Label(inputComposite, SWT.NONE);
			lblLotId.setText(Message.getString("wip.lot_id"));
			lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));		
			txtLotId = new HeaderText(inputComposite, SWTResourceCache.getImage("header-text-lot"));
			txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtLotId.setTextLimit(64);				
			txtLotId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// 回车事件
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String lotId = ((Text) event.widget).getText();
						if (!StringUtil.isEmpty(lotId)) {
							getLotByLotId(lotId);
						}
					}
				}
			});		
			
	        Label lblCarrierId = new Label(inputComposite, SWT.NONE);
	        lblCarrierId.setText(Message.getString("wip.carrier_id"));
			lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
			txtCarrierId = new HeaderText(inputComposite, SWTResourceCache.getImage("header-text-carrier"));
			txtCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));	
			txtCarrierId.setTextLimit(32);
			txtCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// 回车事件
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String carrierId = ((Text) event.widget).getText();
						if (!StringUtil.isEmpty(carrierId)) {
							getLotsByCarrierId(carrierId);
						}
					}
				}
			});
																
			Composite componentComposite = new Composite(this, SWT.NONE);
			componentComposite.setLayout(new GridLayout(1, false));
			
			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
			gridData.heightHint = tableHeigthHint;
			componentComposite.setLayoutData(gridData);
			componentComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotComponentTableManager = new ListTableManager(lotTable, checkFlag);
			lotComponentTableManager.setAutoSizeFlag(true);
			lotComponentTableManager.newViewer(componentComposite);
		} catch (Exception e) {
			logger.error("LotBondingLeftComposite createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void getLotByLotId(String lotId) {
		try {	
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
			if (lot != null) {
				//检查Bonding的批次状态
	    		if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
	    			UI.showError("error.lot_holdstate_not_allow");
	    			return;
				}
	    		if (!LotStateMachine.COMCLASS_WIP.equals(lot.getComClass())) {
	    			UI.showError("error.lot_state_not_allow");
	    			return;
				}
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) prdManager.getSimpleProcessDefinition(step);
				if (step != null) {
	    			if (!Step.USE_CATEGORY_BONDING.equals(step.getUseCategory())) {
	    				UI.showError("wip.lot_step_no_use_category_bonding");
	    				return;
	    			}
    			} else {
    				UI.showError("wip.error.no_step_found");
    				return;
    			}
				
				section.setAdObject(lot);
				
				List<ComponentUnit> componentUnits = new ArrayList<ComponentUnit>();	
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				List<ComponentUnit> lotComponentUnits = componentManager.getComponentsByParentUnitRrn(lot.getObjectRrn());
				if (lotComponentUnits == null) {
					return;
				}	
				for (ComponentUnit lotComponentUnit : lotComponentUnits) {
					lotComponentUnit.setLotId(lot.getLotId());
				}
				componentUnits.addAll(lotComponentUnits);							
				lotComponentTableManager.setInput(componentUnits);
				lotComponentTableManager.refresh();		
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));										
			} else {
				section.setAdObject(section.createAdObject());	
				
				lotComponentTableManager.setInput(new ArrayList<ComponentUnit>());
				lotComponentTableManager.refresh();
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
			section.refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (lots != null && lots.size() > 0) {
					for (Lot lot : lots) {
						getLotByLotId(lot.getLotId());
					}
				} else {
					lotComponentTableManager.setInput(new ArrayList<ComponentUnit>());
					lotComponentTableManager.refresh();				
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				}							
			} else {
				lotComponentTableManager.setInput(new ArrayList<ComponentUnit>());
				lotComponentTableManager.refresh();				
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public HeaderText getTxtCarrierId() {
		return txtCarrierId;
	}

	public void setTxtCarrierId(HeaderText txtCarrierId) {
		this.txtCarrierId = txtCarrierId;
	}

	public HeaderText getTxtLotId() {
		return txtLotId;
	}

	public void setTxtLotId(HeaderText txtLotId) {
		this.txtLotId = txtLotId;
	}

	public ListTableManager getLotComponentTableManager() {
		return lotComponentTableManager;
	}

	public void setLotComponentTableManager(ListTableManager lotComponentTableManager) {
		this.lotComponentTableManager = lotComponentTableManager;
	}
	
	public void refresh() {
		try {		
			ListTableManager componentTableManager = getLotComponentTableManager();
	    	List<ComponentUnit> componentUnits = (List<ComponentUnit>)(List)componentTableManager.getInput();
	    	if (componentUnits == null || componentUnits.size() == 0) {
				return;
			}
	    	
	    	ADManager adManager = Framework.getService(ADManager.class);
	    	Lot lot = new Lot();
	    	lot.setObjectRrn(componentUnits.get(0).getParentUnitRrn());
	    	lot = (Lot) adManager.getEntity(lot);
    	
	    	if (lot != null) {		
				getLotByLotId(lot.getLotId());		
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
}
