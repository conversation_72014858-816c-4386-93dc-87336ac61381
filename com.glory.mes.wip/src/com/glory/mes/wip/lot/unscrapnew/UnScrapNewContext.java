package com.glory.mes.wip.lot.unscrapnew;

import java.util.List;

import org.eclipse.ui.forms.IManagedForm;

import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;

public class UnScrapNewContext {

	public static String UN_SCRAP_NEW = "UnScrapNew";
	private IManagedForm mangedForm;
	private List<ProcessUnit> units;
	private List<Node> nodeList;
	private LotAction lotAction;
	private List<LotScrap> lotScraps;
	private Lot lot;
	private String operator;
	// 是否按片报废
	private boolean isUnScrapComponent = false;
	// 是否有子数量
	private boolean isUnScrapSubQty = false;
	
	public UnScrapNewContext() {
	}

	public IManagedForm getMangedForm() {
		return mangedForm;
	}

	public void setMangedForm(IManagedForm mangedForm) {
		this.mangedForm = mangedForm;
	}

	public List<ProcessUnit> getUnits() {
		return units;
	}

	public void setUnits(List<ProcessUnit> units) {
		this.units = units;
	}

	public List<Node> getNodeList() {
		return nodeList;
	}

	public void setNodeList(List<Node> nodeList) {
		this.nodeList = nodeList;
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	public void setLotAction(LotAction lotAction) {
		this.lotAction = lotAction;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public Lot getLot() {
		return lot;
	}

	public boolean isUnScrapComponent() {
		return isUnScrapComponent;
	}

	public void setUnScrapComponent(boolean isUnScrapComponent) {
		this.isUnScrapComponent = isUnScrapComponent;
	}

	public boolean isUnScrapSubQty() {
		return isUnScrapSubQty;
	}

	public void setUnScrapSubQty(boolean isUnScrapSubQty) {
		this.isUnScrapSubQty = isUnScrapSubQty;
	}

	public List<LotScrap> getLotScraps() {
		return lotScraps;
	}

	public void setLotScraps(List<LotScrap> lotScraps) {
		this.lotScraps = lotScraps;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

}
