package com.glory.mes.wip.lot.unscrapnew;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;

public class UnScrapNewStartPage extends FlowWizardPage {
	
	private static final String TABLE_NAME_LOT = "WIPLotUnScrapEdit";
	private static final String TABLE_NAME_COMP = "WIPComponentUnScrapNew";

	private CheckBoxFixEditorTableManager tableManager;

	private UnScrapNewContext context;
	
	protected XCombo combo;
	protected Text commentText;

	public UnScrapNewStartPage() {
		super();
	}
	
	public UnScrapNewStartPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	protected void createTableViewer(Composite tc, FormToolkit toolkit) {
		tc.setLayoutData(new GridData(GridData.FILL_BOTH));
		tc.setLayout(new GridLayout(1, false));
		
		tableManager = new CheckBoxFixEditorTableManager(getTable());
		tableManager.newViewer(tc);
		tableManager.setInput(context.getLotScraps());
		tableManager.addICheckChangedListener(new ICheckChangedListener() {
			
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (CollectionUtils.isEmpty(eventObjects)) {
					return;
				}
				
				boolean removeCheckFlag = false;
				for (Object object : eventObjects) {
					LotScrap unit = (LotScrap) object;
					if (unit == null) {
						return;
					}
					
					if (checked) {
						String unScrapCode = combo.getText();
						if (StringUtil.isEmpty(unScrapCode)) {
							removeCheckFlag = true;
							break;
						}
						String comments = commentText.getText();
						unit.setUnScrapCode(unScrapCode);
						unit.setUnScrapComment(comments);
					} else {
						unit.setUnScrapCode("");
						unit.setUnScrapComment("");
					}
				}
				
				if (removeCheckFlag) {
					tableManager.getCheckedObject().removeAll(eventObjects);
					UI.showWarning(Message.getString("unscrap.unscrapnew_dialog_selectcodefirst"));
				}
			}
		});
	}
	
	private ADTable getTable() {
		ADTable adTable = null;
    	try {
    		ADManager adManager = Framework.getService(ADManager.class);
    		if (context.isUnScrapComponent()) {
    			adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_COMP);
    		} else {
    			adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOT);
    		}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	return adTable;
    }

	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}


	@Override
	public String doNext() {
		List<Object> objects = tableManager.getCheckedObject();
		if (CollectionUtils.isNotEmpty(objects)) {
			List<LotScrap> lotScraps = objects.stream().map(l -> ((LotScrap) l)).collect(Collectors.toList());
			// 反报废的主数量和字数量不能为空
			Optional<LotScrap> f = lotScraps.stream().filter(
					l -> l.getUnScrapMainQty() == null || l.getUnScrapSubQty() == null).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_qty_input"));
				return "";
			}
			// 反报废发主数量和子数量不能都为0
			f = lotScraps.stream().filter(
					l -> {
						return BigDecimal.ZERO.compareTo(l.getUnScrapMainQty()) >= 0 &&
								BigDecimal.ZERO.compareTo(l.getUnScrapSubQty()) >= 0;
					}).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_main_sub_qty_zero"));
				return "";
			}
			
			// 反报废的主数量和子数量不能大于报废数量
			f = lotScraps.stream().filter(
					l -> {
						return l.getMainQty().compareTo(l.getUnScrapMainQty()) < 0 ||
								l.getSubQty().compareTo(l.getUnScrapSubQty()) < 0;
					}).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_greater_than_scrapped"));
				return "";
			}
			
			// unit
			if (context.isUnScrapComponent()) {
				List<ProcessUnit> units = Lists.newArrayList();
				for (LotScrap lotScrap : lotScraps) {
					ProcessUnit unit = (ProcessUnit) lotScrap.getAttribute3();
					if (!units.contains(unit)) {
						units.add(unit);
					}
				}
				context.setUnits(units);
			}
			
			context.setLotScraps(lotScraps);
			if (context.isUnScrapComponent()) {
				return UnScrapNewDialog.COMP_UNIT_PAGE;
			} else {
				return UnScrapNewDialog.SCHEDULE_PAGE;
			}
		} else {
			UI.showInfo(Message.getString("wip.unscrap_no_units_selected"));
			return "";
		}
		
	}

	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public IWizardPage getPreviousPage() {
		return null;
	}

	@Override
	public void createControl(Composite parent) {
		context = (UnScrapNewContext) ((UnScrapNewWizard) getWizard()).getContext();
		
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		setTitle(Message.getString("unscrap.unscrapnew_dialog_scrap"));
		setDescription(Message.getString("wip.unscrap_unscrap_info"));

		Composite composite = new Composite(parent, SWT.NONE);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
		tableContainer.setLayout(new GridLayout());
		tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
		createTableViewer(tableContainer, toolkit);
		
		Composite unScrapComp = toolkit.createComposite(composite, SWT.NONE);
		unScrapComp.setLayout(new GridLayout(2, false));
		unScrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		unScrapComp.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));

		Label lab1 = toolkit.createLabel(unScrapComp, Message
				.getString("wip.unscrapcode_lot"), SWT.NULL);
		lab1.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		combo = RCPUtil.getUserRefListCombo(unScrapComp, "UnScrapCode", Env.getOrgRrn());
		
		combo.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));

		Label lab2 = toolkit.createLabel(unScrapComp, Message
				.getString("common.comment"), SWT.NULL);
		lab2.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		commentText = toolkit.createText(unScrapComp, "", SWT.BORDER);
		commentText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true,
				false));
		
		setControl(composite);
	}

	@Override
	public boolean canFlipToNextPage() {
		return true;
	}

}
