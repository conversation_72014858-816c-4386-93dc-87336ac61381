package com.glory.mes.wip.lot.unscrapnew;

import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardDialog;
import com.glory.framework.base.ui.wizard.FlowWizardPage;

public class UnScrapNewDialog extends FlowWizardDialog {
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 420;
	protected static String START_PAGE = "unScrapNewStartPage";
	protected static String COMP_UNIT_PAGE = "unScrapNewCompUnitPage";
	protected static String SCHEDULE_PAGE = "unScrapNewSchedulePage";
	protected static String NEW_PART_PAGE = "unScrapNewPartPage";
	protected static String LOT_LIST_PAGE = "unScrapNewLotListPage";

	public UnScrapNewDialog(Shell parentShell, FlowWizard newWizard) {
		super(parentShell, newWizard);
	}

	protected void updateSizeForPage(IWizardPage page) {}

	/*
	 * 取消将焦点默认的放在Next Button上
	 */
	public void updateButtons() {
		boolean canFlipToNextPage = false;
		if (currentPage != null) {
			if (backButton != null) {
				backButton.setEnabled(getCurrentPage().getPreviousPage() != null);
			}
			if (nextButton != null) {
				canFlipToNextPage = getCurrentPage().canFlipToNextPage();
				nextButton.setEnabled(canFlipToNextPage);
			}
		}
	}

//	@Override
//	protected void constrainShellSize() {
//		Point shellSize = super.getInitialSize();
//		super.constrainShellSize();
//		getShell().setBounds(
//				300,
//				100,
//				Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH),
//						shellSize.x),
//				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
//						shellSize.y));
//		getShell().setMinimumSize(800, 300);
//	}
	
	@Override
	protected Point getInitialSize() {
		super.getShellStyle();
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.NONE);
	}
}
