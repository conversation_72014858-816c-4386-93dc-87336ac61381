package com.glory.mes.wip.lot.replenish;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.run.bylot.RunByLotCarrierLotComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class LotReplenishSection extends LotSection {
		private static final Logger logger = Logger.getLogger(LotReplenishSection.class);

		public CheckBoxFixEditorTableManager manager;	
		protected List<LotScrap> LotScraps = new ArrayList<LotScrap>();
		protected List<ProcessUnit> scrapLots = new ArrayList<ProcessUnit>();
		protected ManagedForm mForm;
		private ToolItem itemReplenish;
		public EntityForm lotReplenishForm;
		public HeaderText txtReplenishLot;

		public LotReplenishSection(ADTable adTable) {
			super(adTable);
		}
		
		@Override
		public void createToolBar(Section section) {
			ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
			createToolItemReplenish(tBar);
			new ToolItem(tBar, SWT.SEPARATOR);
			createToolItemRefresh(tBar);
			section.setTextClient(tBar);
		}
		
		protected void createToolItemReplenish(ToolBar tBar) {
		    itemReplenish = new ToolItem(tBar, SWT.PUSH);
		    itemReplenish.setText(Message.getString("backend_waste_replenish"));
		    itemReplenish.setImage(SWTResourceCache.getImage("mlot_receive"));
		    itemReplenish.addSelectionListener(new SelectionAdapter() {
		         @Override
		         public void widgetSelected(SelectionEvent event) {
		        	 replenish(event);
		         }
		     });
		}

	    @Override
		protected void createSectionTitle(Composite client) {
			final FormToolkit toolkit = form.getToolkit();
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			gd.verticalAlignment = SWT.TOP;
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
		        if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
		        	//如果使用载具
					carrierLotComposite = new RunByLotCarrierLotComposite(this, client, SWT.NONE, false, true, false, false);
					carrierLotComposite.createForm(toolkit, client);
					
					carrierLotComposite.getLotTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
						@Override
						public void selectionChanged(SelectionChangedEvent event) {
							StructuredSelection selection = (StructuredSelection) event.getSelection();
							Lot lot = (Lot) selection.getFirstElement();
							if (lot != null) {
								lot = searchLot(lot.getLotId());
								
								setAdObject(lot);
								try {
									String scrapCode = getScrapCode(lot);
									ADManager adManager = Framework.getService(ADManager.class);
									List<ADURefList> adRefLists = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, Integer.MAX_VALUE, "referenceName = '" + scrapCode +"' ", "");
									if(adRefLists != null && adRefLists.size() > 0) {
										LotScraps.clear();
								    	for(ADURefList adRefList : adRefLists) {
								    		LotScrap o = new LotScrap();
								    		o.setActionCode(adRefList.getText());
								    		o.setActionComment(adRefList.getDescription());
								    		LotScraps.add(o);
								    	}
								   	}
									manager.setInput(LotScraps);
								} catch (Exception e) {
									e.printStackTrace();
								}
								txtLot.focusing();
							}
							setAdObject(lot);
							refresh();
						}
					});
					txtLot = carrierLotComposite.getTxtLotId();
		        } else {
		        	//如果不使用载具
					Composite top = toolkit.createComposite(client);
					top.setLayout(new GridLayout(3, false));
					top.setLayoutData(gd);
					Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
					label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
					txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
					txtLot.setTextLimit(32);
					txtLot.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
					txtLot.addKeyListener(new KeyAdapter() {
						@Override
						public void keyPressed(KeyEvent event) {
							Text tLotId = ((Text) event.widget);
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
							switch (event.keyCode) {
							case SWT.CR:
							case SWT.KEYPAD_CR:
								Lot lot = null;
								String lotId = tLotId.getText();
								if (!isLotIdCaseSensitive()) {
									lotId = lotId.toUpperCase();
								}
								tLotId.setText(lotId);
								lot = searchLot(lotId);
								tLotId.selectAll();
								if (lot == null) {
									tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
									try {
				        				setAdObject(createAdObject());		        			
				        			} catch(Exception en) {
				        				logger.error("createADObject error at searchEntity Method!");
				        			}
									txtLot.warning();
								} else {
									setAdObject(lot);
									try {
										String scrapCode = getScrapCode(lot);
										ADManager adManager = Framework.getService(ADManager.class);
										List<ADURefList> adRefLists = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, Integer.MAX_VALUE, "referenceName = '" + scrapCode +"' ", "");
										if(adRefLists != null && adRefLists.size() > 0) {
											LotScraps.clear();
									    	for(ADURefList adRefList : adRefLists) {
									    		LotScrap o = new LotScrap();
									    		o.setActionCode(adRefList.getText());
									    		o.setActionComment(adRefList.getDescription());
									    		LotScraps.add(o);
									    	}
									   	}
										manager.setInput(LotScraps);
									} catch (Exception e) {
										e.printStackTrace();
									}
									
									txtLot.focusing();
								}
								refresh();
								break;
							}
						}
			
					});
					txtLot.addFocusListener(new FocusListener() {
						public void focusGained(FocusEvent e) {
						}
			
						public void focusLost(FocusEvent e) {
							Text tLotId = ((Text) e.widget);
							String lotId = tLotId.getText();
							if (!isLotIdCaseSensitive()) {
								lotId = lotId.toUpperCase();
							}
							tLotId.setText(lotId);
						}
					});
					
					Composite right = toolkit.createComposite(top);
					GridLayout layout = new GridLayout(2, false);
					right.setLayout(layout);
					gd = new GridData(GridData.FILL_HORIZONTAL);
					gd.horizontalAlignment = SWT.END;
					gd.grabExcessHorizontalSpace = true;
					right.setLayoutData(gd);
		        }
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
	        	return;
			}
		}
	    
	    protected void createSectionContent(Composite client) {
	    	super.createSectionContent(client);
	    	try {
	    		FFormToolKit toolkit = (FFormToolKit) form.getToolkit();
	    		Composite content = toolkit.createComposite(client);
	    		content.setLayout(new GridLayout(2, true));
	    		content.setLayoutData(new GridData(GridData.FILL_BOTH));
	    		//报废信息
				Section scrapSection = toolkit.createSection(content, Section.NO_TITLE | FFormSection.FFORM);		
				scrapSection.setText(Message.getString("backend_lot_scrap"));
				GridData gd = new GridData(GridData.FILL_BOTH);
				scrapSection.setLayoutData(gd);
				scrapSection.setLayout(new GridLayout(1, true));
				Composite parameterComp = toolkit.createComposite(scrapSection);
				GridLayout layout = new GridLayout(1, true);
				layout.marginHeight = 0;
				layout.marginWidth = 0;
				parameterComp.setLayout(layout);
				gd = new GridData(GridData.FILL_BOTH);
				gd.heightHint = 190;
				parameterComp.setLayoutData(gd);			
				ADManager adManager = (ADManager) Framework.getService(ADManager.class);
				final ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "WIPLotScrap");			
				manager = new CheckBoxFixEditorTableManager(adTable);	
				manager.setIndexFlag(true);
				manager.newViewer(parameterComp);
				scrapSection.setClient(parameterComp);
				
				// 补充信息
				Section lotReplenishSection = toolkit.createSection(content, Section.NO_TITLE | FFormSection.FFORM);	
				lotReplenishSection.setText(Message.getString("backend_lot_replenish"));
				GridData gds = new GridData(GridData.FILL_BOTH);
				lotReplenishSection.setLayoutData(gds);
				lotReplenishSection.setLayout(new GridLayout(2, true));
				Composite lotReplenishComposite = toolkit.createComposite(lotReplenishSection);
				GridLayout layouts = new GridLayout(1, false);
				layouts.marginHeight = 0;
				layouts.marginWidth = 0;
				lotReplenishComposite.setLayout(layouts);
				gd = new GridData(GridData.FILL_BOTH);
				gd.heightHint = 190;
				lotReplenishComposite.setLayoutData(gd);
				
				Composite txt = toolkit.createComposite(lotReplenishComposite, SWT.NONE);
				GridLayout layoutTxt = new GridLayout(2, false);
				layoutTxt.marginHeight = 0;
				layoutTxt.marginWidth = 0;
				txt.setLayout(layoutTxt);
				GridData txtgd = new GridData();
				txt.setLayoutData(txtgd);
				toolkit.createLabel(txt, Message.getString("wip.lot_id"));
				txtReplenishLot = new HeaderText(txt);
				txtReplenishLot.setTextLimit(32);	
				txtReplenishLot.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						Text tLotId = ((Text) event.widget);
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
						switch (event.keyCode) {
						case SWT.CR:
						case SWT.KEYPAD_CR:
							Lot lot = null;
							String lotId = tLotId.getText();
							tLotId.setText(lotId.toUpperCase());
							lot = searchLot(lotId.toUpperCase());
							tLotId.selectAll();
							if (lot == null) {
								tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
								try {
			        				setAdObject(createAdObject());		        			
			        			} catch(Exception en) {
			        				logger.error("createADObject error at searchEntity Method!");
			        			}
								txtReplenishLot.warning();
							} else {
								//将entityForm显示出来
								lotReplenishForm.setObject(lot);
								lotReplenishForm.loadFromObject();
								txtReplenishLot.focusing();
							}
							break;
						}
					}
		
				});
				
				ADManager adManagers = (ADManager) Framework.getService(ADManager.class);
				final ADTable adTables = adManagers.getADTable(Env.getOrgRrn(), "WIPLotReplenish");			
				lotReplenishForm = new EntityForm(lotReplenishComposite, SWT.NONE, new Lot(), adTables, null); 
				lotReplenishForm.setLayout(layout);
				lotReplenishForm.setLayoutData(gd);
				lotReplenishSection.setClient(lotReplenishComposite);
	    	} catch (Exception e) {
	    		ExceptionHandlerManager.asyncHandleException(e);
	        	return;
			}
	    }
		
		protected String getScrapCode(Lot lot) {
			try {
				ADManager adManager = Framework.getService(ADManager.class);
				String stepId = "objectRrn = " + lot.getStepRrn();
				List<Step> steps = adManager.getEntityList(Env.getOrgRrn(), Step.class, Integer.MAX_VALUE, stepId, "");
				if (steps.get(0) != null && steps.get(0).getScrapCodeSrc() != null 
						&& steps.get(0).getScrapCodeSrc().trim().length() > 0) {
					return steps.get(0).getScrapCodeSrc();
				} 
			} catch (Exception e) {
				logger.error("ScrapLotDialog : initComoContent() ", e);
			}
			return "ScrapCode";
		}
			
		public List<ProcessUnit> getScrapLots() {
			return scrapLots;
		}
		
		public LotAction getLotAction() {
			LotAction lotAction = new LotAction();
//			lotAction.setActionComment(LotScraps.get());
			//lotAction.setOcapId(this.OCAPTxt.getText());
			lotAction.setOcapId("");
			return lotAction;
		}

		public boolean isLessThanLotQtys(List<LotScrap> lotScrap, Lot lot) {
			scrapLots.clear();
			BigDecimal num = new BigDecimal("0");
			for(LotScrap lots : lotScrap) {
				if(lots.getMainQty() != null) {
				    num = num.add(lots.getMainQty());
				    QtyUnit sh = new QtyUnit();
					sh.setActionCode(lots.getActionCode());
					sh.setMainQty(lots.getMainQty());
					if (scrapLots != null) {
						scrapLots.add(sh);
					}
				}
			}
			if(isNumeric(num.toString())) {
			    if(num.compareTo(lot.getMainQty()) < 1) {
				    return false;
			    }
			    return true;
			}
			return true;
		}
		
		//判断报废数量是否为整数
		public static boolean isNumeric(String str){  
		    Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");    
		    return pattern.matcher(str).matches();     
		}  
		
		public void cleanLotScrap(){  
			List<LotScrap> newLotScraps = new ArrayList<LotScrap>();
			for(LotScrap adRefList : LotScraps) {
	    		LotScrap o = new LotScrap();
	    		o.setActionCode(adRefList.getActionCode());
	    		o.setActionComment(adRefList.getActionComment());
	    		o.setMainQty(null);
	    		newLotScraps.add(o);
	    	}
			LotScraps.clear();
			LotScraps = newLotScraps;
		}  
		
		private void replenish(SelectionEvent event) {
			try {
				Lot lot = (Lot)this.getAdObject();
				Lot mergeLot = (Lot) lotReplenishForm.getObject();
				List<LotScrap> lotScrap = (List<LotScrap>) manager.getInput();
				if (lot == null || lot.getObjectRrn() == null) {
					UI.showError(Message.getString("wip.lot_basic_information_is_null"));
					return;
				} else if (mergeLot == null || mergeLot.getObjectRrn() == null) {
					UI.showError(Message.getString("wip.lot_additional_information_is_null"));
					return;
				}else if (isLessThanLotQtys(lotScrap ,mergeLot)) {
					UI.showError(Message.getString("wip.lot_the_amount_of_scrapped_is_incorrect"));
					return;
				}else {
					try {
			            LotManager lotManager = Framework.getService(LotManager.class);
			            lot.setOperator1(Env.getUserName());

			            Map<String, List<ProcessUnit>> lotActionsMap = new HashMap<String, List<ProcessUnit>>();
			            for (ProcessUnit unit : getScrapLots()) {
			                QtyUnit qtyUnit = (QtyUnit) unit;
			                if (lotActionsMap.containsKey(qtyUnit.getActionCode())) {
			                    List<ProcessUnit> units = lotActionsMap.get(qtyUnit.getActionCode());
			                    units.add(qtyUnit);
			                    lotActionsMap.put(qtyUnit.getActionCode(), units);
			                } else {
			                    List<ProcessUnit> units = new ArrayList<ProcessUnit>();
			                    units.add(qtyUnit);
			                    lotActionsMap.put(qtyUnit.getActionCode(), units);
			                }
			            }

			            List<LotAction> scrapLotActions = new ArrayList<LotAction>();
			            for (String key : lotActionsMap.keySet()) {
			                LotAction lotAction = getLotAction();
			                lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
			                lotAction.setLotRrn(lot.getObjectRrn());
			                lotAction.setActionCode(key);
			                lotAction.setActionUnits(lotActionsMap.get(key));
			                lotAction.setActionComment(getLotAction().getActionComment());
			                scrapLotActions.add(lotAction);
			            }

			            LotAction lotAction = getLotAction();
			            lotAction.setActionComment(getLotAction().getActionComment());
			            lotAction.setActionCode(getLotAction().getActionCode());
			            Lot newLot = lotManager.replenishLot(lot, scrapLotActions, lotAction, mergeLot, Env.getSessionContext());
			            UI.showInfo(Message.getString("wip.lot_supplementary_success"));
			            scrapLots.clear();
			            this.setAdObject(newLot);
			            this.refresh();
						lotReplenishForm.setObject(searchLot(mergeLot.getLotId().toUpperCase()));
						lotReplenishForm.loadFromObject();
						cleanLotScrap();
						manager.setInput(LotScraps);
			        } catch (Exception e) {
			            logger.error("Scrap Lot Failure at FTLotReplenishSection : " + e);
			            UI.showError(Message.getString("wip.lot_supplement failed"));
			            return;
			        }
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
	        	return;
			}	
		}
}
