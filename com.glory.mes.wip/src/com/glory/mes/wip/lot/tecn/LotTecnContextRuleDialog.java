package com.glory.mes.wip.lot.tecn;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.common.context.client.ContextManager;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextRule;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.google.common.collect.Lists;

public class LotTecnContextRuleDialog extends BaseTitleDialog {
	
    private static int MIN_DIALOG_WIDTH = 500;
    private static int MIN_DIALOG_HEIGHT = 350;
    
    private ListTableManager tableManager;
    private static final String TABLE_NAME = "BASContextRule";
    
    private static final String TABLE_NAME_LOTTECN = "LotTecnContextRuleValue";

    private static final String CONTEXT_NAME = "LOTTECN";
    
    public LotTecnContextRuleDialog(Shell parentShell) {
        super(parentShell);
    }

	@Override
	protected Control buildView(Composite parent) {
        setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        
        FormToolkit toolkit = new FormToolkit(parent.getDisplay());
        Composite tableCom = toolkit.createComposite(parent, SWT.NULL);
        tableCom.setLayout(new GridLayout(4, false));
        tableCom.setLayoutData(new GridData(GridData.FILL_BOTH));
        try {
            ADManager adManager = Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
            ContextManager contextManager = Framework.getService(ContextManager.class);
            Context context = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME);

            setTitle(context.getName() + Message.getString("common.context_rule"));

            // 获取选择Context所有字段
            List<ADField> contextAdFields =  adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOTTECN).getFields();
            List<ADField> contextAdFieldsExceptResultValue = new ArrayList<ADField>();
            // 将所有的结果移除掉不显示
            for (ADField adField : contextAdFields) {
                if (!adField.getName().contains("resultValue")) {
                    contextAdFieldsExceptResultValue.add(adField);
                }
            }
            // 获取选择的Context的所有的Rule
            List<ContextRule> contextRules = adManager.getEntityList(Env.getOrgRrn(),
                    ContextRule.class, Env.getMaxResult(),
                    " contextRrn = " + context.getObjectRrn() + "ORDER BY seqNo ASC", "");

          //筛选LotTenc只需要LotID为必须的
            List<ContextRule> contextRuleslist = Lists.newArrayList();
            if(contextRules != null && contextRules.size() > 0) {
            	for(ContextRule contextRule : contextRules) {
            		if(contextRule.getContextFieldFlag2()) {
            			contextRuleslist.add(contextRule);
            		}
            	}
            }
            
            List<ADField> fields = new ArrayList<ADField>();
            // 显示序号列表
            for (ADField adField : adTable.getFields()) {
                if (adField.getName().contains("seqNo")) {
                    fields.add(adField);
                }
            }
            // 将所获取的字段添加到列表显示
            for (int i = 1; i <= contextAdFieldsExceptResultValue.size(); i++) {
            	//保持栏位下标一致因为共用一个context，不一致会导致规则赋值到未显示栏位
            	int num = Integer.parseInt(contextAdFieldsExceptResultValue.get(i - 1).getName()
            			.substring(contextAdFieldsExceptResultValue.get(i - 1).getName().length()-1, contextAdFieldsExceptResultValue.get(i - 1).getName().length()));
                adTable.getFields().get(num)
                        .setLabel(contextAdFieldsExceptResultValue.get(i - 1).getLabel());
                adTable.getFields().get(num)
                        .setLabel_zh(contextAdFieldsExceptResultValue.get(i - 1).getLabel_zh());
                fields.add(adTable.getFields().get(num));
            }
            adTable.setFields(fields);
            tableManager = new ListTableManager(adTable, false);
            tableManager.newViewer(tableCom);

            tableManager.setInput(contextRuleslist);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return parent;
    }

    @Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }

}
