package com.glory.mes.wip.lot.tecn;

import org.eclipse.swt.widgets.ToolItem;

import com.glory.common.context.client.ContextManager;
import com.glory.common.context.custom.ContextObjectQueryCustomComposite;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextValue;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.LotTecn;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotTecnEditor extends GlcEditor {

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.tecn.LotTecnEditor";
	
	public static final String BUTTON_NAME_SHOWRULE = "showRule";
	
	public static final String CONTROL_TECN_QUERY_INFO = "lotTecnQueryInfo";
	public static final String CONTROL_TECN_EDIT_INFO = "lotTecnEditInfo";
	public static final String CONTROL_CONTEXT_INFO = "contextInfo";
	public static final String CONTROL_TECN_INFO = "tecnInfo";
	
	public CustomField lotTecnQueryInfoCustomField; 
	public GlcFormField tecnEditInfoGlcFormField; 
	public EntityFormField contextInfoCustomField;
	public EntityFormField tecnInfoEntityFormField;
	
	private ToolItem itemInActive;
	private ToolItem itemActive;
	private ToolItem itemDelete;
	private ToolItem itemSave;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		//获取查询控件
		lotTecnQueryInfoCustomField = form.getFieldByControlId(CONTROL_TECN_QUERY_INFO, CustomField.class);
		
		//获取下面的Context与EdcTecn编辑控件
		tecnEditInfoGlcFormField = form.getFieldByControlId(CONTROL_TECN_EDIT_INFO, GlcFormField.class);
		
		// 获取Context编辑控件
		contextInfoCustomField = tecnEditInfoGlcFormField.getFieldByControlId(CONTROL_CONTEXT_INFO, EntityFormField.class);
		
		//获取LotTecn编辑控件
		tecnInfoEntityFormField = tecnEditInfoGlcFormField.getFieldByControlId(CONTROL_TECN_INFO, EntityFormField.class);	
	
		//查询表格选择事件
		subscribeAndExecute(eventBroker, lotTecnQueryInfoCustomField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);
		
		//新建事件
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_SHOWRULE), this::ruleAdapter);
				
		//新建事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);
		
		//保存事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);			
		
		//激活事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ACTIVE), this::activeAdapter);		
		
		//失效事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_INACTIVE), this::inActiveAdapter);		
		
		//删除事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);		
		
		//刷新事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ENTITYREFRESH), this::refreshAdapter);	
		
		itemSave = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_SAVE);
		itemActive = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_ACTIVE);
		itemInActive = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_INACTIVE);
		itemDelete = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_DELETE);
		
		//打开页面加载新建事件
		newAdapter(null);
	}

	protected void selectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			org.osgi.service.event.Event event = (org.osgi.service.event.Event) object;		
			ContextValue lotTecnContextValue = (ContextValue) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotTecnContextValue == null) {
				return;
			}
			if (lotTecnContextValue.getObject() != null) {
				contextInfoCustomField.setValue(lotTecnContextValue);
				contextInfoCustomField.refresh();
			}
			
			if (lotTecnContextValue.getObject() != null) {
				LotTecn lotTecn = new LotTecn();
				lotTecn.setEquipmentId(lotTecnContextValue.getResultValue1());
				lotTecn.setRecipeName(lotTecnContextValue.getResultValue2());
				lotTecn.setReticleName(lotTecnContextValue.getResultValue3());
				lotTecn.setEdcName(lotTecnContextValue.getResultValue4());
				lotTecn.setEquipmentRecipeName(lotTecnContextValue.getResultValue5());
				
				tecnInfoEntityFormField.setValue(lotTecn);
				tecnInfoEntityFormField.refresh();	
			}
			statusChanged((LotTecn) lotTecnContextValue.getObject());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void ruleAdapter(Object object) {
        try {
        	LotTecnContextRuleDialog dialog = new LotTecnContextRuleDialog(UI.getActiveShell());  
            dialog.open();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void newAdapter(Object object) {
		try {
			contextInfoCustomField.setValue(new ContextValue());
			contextInfoCustomField.refresh();
			
			tecnInfoEntityFormField.setValue(new LotTecn());
			tecnInfoEntityFormField.refresh();
			
			statusChanged(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void saveAdapter(Object object) {
		EntityForm contextInfoCustomForm = (EntityForm) contextInfoCustomField.getControls()[0];
		EntityForm tecnInfoEntityForm = (EntityForm) tecnInfoEntityFormField.getControls()[0];
		try {	
			contextInfoCustomForm.getMessageManager().setAutoUpdate(true); //设置为true时才会刷新界面出现icon
			contextInfoCustomForm.getMessageManager().removeAllMessages();
			
			tecnInfoEntityForm.getMessageManager().setAutoUpdate(true); //设置为true时才会刷新界面出现icon
			tecnInfoEntityForm.getMessageManager().removeAllMessages();
			
			if (contextInfoCustomField.getValue() != null && tecnInfoEntityFormField.getValue() != null) {
				boolean saveFlag = true;			
				if (!contextInfoCustomForm.saveToObject()) {
					saveFlag = false;
				}
				if (!tecnInfoEntityForm.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					LotTecn lotTecn = (LotTecn) tecnInfoEntityFormField.getValue();
					
					ContextManager contextManager = Framework.getService(ContextManager.class);
			        Context context = contextManager.getContextByName(Env.getOrgRrn(), LotTecn.CONTEXT_LOTTECN);
			           	
			        ContextValue contextValue = (ContextValue) contextInfoCustomField.getValue();
					contextValue.setContextRrn(context.getObjectRrn());
					contextValue.setOrgRrn(Env.getOrgRrn());
					LotTecn.setContextEquipmentId(contextValue, lotTecn.getEquipmentId());
					LotTecn.setContextLogicRecipe(contextValue, lotTecn.getRecipeName());
					LotTecn.setContextReticleName(contextValue, lotTecn.getReticleName());
					LotTecn.setContextEdcSetName(contextValue, lotTecn.getEdcName());
					LotTecn.setContextEquipmentRecipe(contextValue, lotTecn.getEquipmentRecipeName());
					
					lotTecn.setLotId(contextValue.getContextFieldValue2());
					lotTecn.setStepName(contextValue.getContextFieldValue3());
					lotTecn.setEcnSourceName(contextValue.getContextFieldValue2() + "_" + contextValue.getContextFieldValue3());
					
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.saveLotTecn(Lists.newArrayList(lotTecn), Lists.newArrayList(contextValue), Env.getSessionContext());
					
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框

					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}  finally {
			contextInfoCustomForm.getMessageManager().setAutoUpdate(false);
			tecnInfoEntityForm.getMessageManager().setAutoUpdate(false);
		}
	}
	
	protected void activeAdapter(Object object) {
		try {
			LotTecnContextActiveDialog dialog = new LotTecnContextActiveDialog(UI.getActiveShell());
            dialog.open();
            
            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void inActiveAdapter(Object object) {
		try {		
			ContextObjectQueryCustomComposite contextObjectQueryComposite = 
					(ContextObjectQueryCustomComposite) lotTecnQueryInfoCustomField.getCustomComposite();
			ContextValue edcTecnContextValue = (ContextValue)contextObjectQueryComposite.getQueryForm().getTableManager().getSelectedObject();	
            if (edcTecnContextValue == null) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
            if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmInActive()))) {             
            	LotManager lotManager = Framework.getService(LotManager.class);
            	LotTecn lotTecn = (LotTecn) edcTecnContextValue.getObject();
                lotManager.inActiveLotTecn(Lists.newArrayList(lotTecn), Env.getSessionContext());
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
            }	
            
            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void deleteAdapter(Object object) {
		try {
			ContextObjectQueryCustomComposite contextObjectQueryComposite = 
					(ContextObjectQueryCustomComposite) lotTecnQueryInfoCustomField.getCustomComposite();
			ContextValue lotTecnContextValue = (ContextValue)contextObjectQueryComposite.getQueryForm().getTableManager().getSelectedObject();		
            if (lotTecnContextValue == null ) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
            LotTecn lotTecn = (LotTecn) lotTecnContextValue.getObject();
			if (lotTecn != null && !StringUtil.isEmpty(lotTecn.getEcnSource())) {
				UI.showError(Message.getString("wip.lot_tecn_delete_error"));
				return;
			}
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				LotManager lotManager = Framework.getService(LotManager.class);
                lotManager.deleteLotTecn(Lists.newArrayList(lotTecn), Env.getSessionContext());
			}
			
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void refreshAdapter(Object object) {
		try {
			//查询列表清空
			lotTecnQueryInfoCustomField.refresh();
			
			//打开页面加载新建事件
			newAdapter(null);
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}
	
	public void statusChanged(LotTecn lotTecn) {
		if (lotTecn != null && !StringUtil.isEmpty(lotTecn.getEcnSource())) {
			itemSave.setEnabled(false);
			itemInActive.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(false);
		} else {
			itemSave.setEnabled(true);
			itemInActive.setEnabled(true);
			itemDelete.setEnabled(true);
			itemActive.setEnabled(true);
		}
	}
}
