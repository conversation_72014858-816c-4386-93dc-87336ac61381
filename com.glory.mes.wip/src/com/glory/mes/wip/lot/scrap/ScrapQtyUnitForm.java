package com.glory.mes.wip.lot.scrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.glory.framework.core.exception.ExceptionBundle;

public class ScrapQtyUnitForm extends Composite {
	
	private static final Logger logger = Logger.getLogger(ScrapQtyUnitForm.class);
	//protected Table table;
	protected SquareButton remove, qtyAdd;
	protected XCombo combo;
	protected Text mainText, subText, commentText;
	protected List<ProcessUnit> scrapLots = new ArrayList<ProcessUnit>();
	protected Lot lot;
	protected String scrapCode, commentQty;
	protected BigDecimal inputMainQty, inputSubQty, addMainQty, addSubQty, splitedMainQty, splitedSubQty;
	protected static String SCRAP_CODE = Message.getString("wip.trackout_scrapcode");
	protected String actionCode;

	public static final String DEFAULT_SCRAP_TABLE = "ScrapCode";
	
	protected ManagedForm mform;
	protected ListTableManager viewer;
	
	public ScrapQtyUnitForm(Composite parent, int style, Lot lot) {
		super(parent, style);
		this.lot = lot;
		createForm();
		
	}
	
	public void createForm() {
		FormToolkit toolkit = new FormToolkit(getDisplay());
		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));
		this.setLayoutData(new GridData(GridData.FILL_BOTH));
		ScrolledForm sform = toolkit.createScrolledForm(this);
		sform.setLayoutData(new GridData(GridData.FILL_BOTH));
		mform = new ManagedForm(toolkit, sform);
		Composite body = sform.getBody();
		layout = new GridLayout();
		body.setLayout(layout);
		createViewerComponent(body, toolkit);
		createOtherComponent(body, toolkit);
	}

	// 创建按钮及页面选择项
	protected void createOtherComponent(Composite content, FormToolkit toolkit) {
		Composite buttonComp = toolkit.createComposite(content, SWT.NULL);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.horizontalAlignment = GridData.END;
		buttonComp.setLayoutData(gd);
		GridLayout gridLayout = new GridLayout(1, true);
		buttonComp.setLayout(gridLayout);

		remove = UIControlsFactory.createButton(buttonComp, Message.getString(ExceptionBundle.bundle.CommonDelete()), "DEFAULT");
		decorateButton(remove);
		remove.addSelectionListener(getDeleteListener());

		Composite scrapComp = toolkit.createComposite(content, SWT.NULL);
		scrapComp.setLayoutData(new GridData(GridData.FILL_BOTH));
		GridData tGd = new GridData(GridData.FILL_HORIZONTAL);
		GridLayout gl = new GridLayout(5, false);
		scrapComp.setLayout(gl);

		toolkit.createLabel(scrapComp, Message.getString("wip.scrapcode_lot"),
				SWT.NULL);
		combo = RCPUtil.getUserRefListCombo(scrapComp, getScrapCode(), Env
				.getOrgRrn());
		GridData cGd = new GridData(GridData.FILL_HORIZONTAL);
		cGd.horizontalSpan = 4;
		combo.setLayoutData(cGd);

		toolkit.createLabel(scrapComp, Message.getString("wip.scrap_mainqty"),
				SWT.NULL);
		mainText = toolkit.createText(scrapComp, "", SWT.BORDER);
		mainText.setLayoutData(tGd);

		toolkit.createLabel(scrapComp, Message.getString("wip.scrap_subqty"),
				SWT.NULL);
		subText = toolkit.createText(scrapComp, "", SWT.BORDER);
		subText.setLayoutData(tGd);
		
		qtyAdd = UIControlsFactory.createButton(scrapComp, Message.getString(ExceptionBundle.bundle.CommonAdd()), "DEFAULT");
		decorateButton(qtyAdd);

		qtyAdd.addSelectionListener(getQtyAddListener()); //”添加 “按钮
		mainText.addModifyListener(getModifyListener("double")); //添加片数量
		subText.addModifyListener(getModifyListener("double")); //添加die数量

		toolkit.createLabel(scrapComp, Message.getString("wip.comment") + "*",
				SWT.NULL);
		commentText = toolkit.createText(scrapComp, "", SWT.BORDER);
		commentText.setLayoutData(cGd);
	}	

	// 获得报废码
	protected String getScrapCode() {
		try {
			if (lot != null && lot.getObjectRrn() != null && lot.getStepRrn() != null) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) prdManager.getSimpleProcessDefinition(step);
				scrapCode = step.getScrapCodeSrc();
			} else {
				scrapCode = DEFAULT_SCRAP_TABLE;
			}
			if (scrapCode == null || scrapCode.trim().length() == 0) {
				scrapCode = DEFAULT_SCRAP_TABLE;
			}
		} catch (Exception e) {
			logger.error("ScrapLotDialog : initComoContent() ", e);
		}
		return scrapCode;
	}

	//添加……
	public SelectionListener getQtyAddListener() {
		return new SelectionAdapter(){
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (validate()) {
					if (isLessThanLotQtys()) {
						scrapCode = combo.getText();
						//commentQty = commentText.getText();
						QtyUnit sh = new QtyUnit();
						sh.setActionCode(scrapCode);
						sh.setMainQty(inputMainQty);
						sh.setSubQty(inputSubQty);
						if (scrapLots != null) {
							scrapLots.add(sh);
							refresh();
						}
						mainText.setText("");
						subText.setText("");
						commentText.setText(" ");
						mainText.setFocus();
					}
				}
				inputMainQty = inputSubQty = BigDecimal.ZERO;
			}
		};
	}

	public void computeAddQty() {
		addMainQty = BigDecimal.ZERO;
		addSubQty = BigDecimal.ZERO;

		if (scrapLots != null) {
			for (ProcessUnit sl : scrapLots) {
				if (sl.getMainQty() != null) {
					addMainQty = addMainQty.add(sl.getMainQty());
				}
				if (sl.getSubQty() != null) {
					addSubQty = addSubQty.add(sl.getSubQty());
				}
			}
		}
	}

	public boolean validate() {
		IMessageManager mmng = mform.getMessageManager();
		mmng.removeAllMessages();
		boolean validateFlag = true;
		boolean sourceIsNull = GenericValidator.isBlankOrNull(combo.getText());
		boolean mainIsNull = GenericValidator.isBlankOrNull(mainText.getText());
		boolean subIsNull = GenericValidator.isBlankOrNull(subText.getText());
		if (sourceIsNull) {
			mmng.addMessage(SCRAP_CODE, String.format(Message
					.getString("common.ismandatry"), SCRAP_CODE), null,
					IMessageProvider.ERROR, combo);
			validateFlag = false;
		}
		if (mainIsNull && subIsNull) {
			mmng.addMessage(Message.getString("wip.lot_mainqty"), String
					.format(Message.getString("common.mainqty_and_subqty"),
							Message.getString("wip.lot_mainqty"), Message
									.getString("wip.lot_subqty")), null,
					IMessageProvider.ERROR, mainText);
			mmng.addMessage(Message.getString("wip.lot_subqty"), String.format(
					Message.getString("common.mainqty_and_subqty"), Message
							.getString("wip.lot_subqty"), Message
							.getString("wip.lot_mainqty")), null,
					IMessageProvider.ERROR, subText);
			validateFlag = false;
		}
		return validateFlag;
	}

	public boolean isLessThanLotQtys() {
		computeAddQty();
		inputMainQty = BigDecimal.ZERO;
		inputSubQty = BigDecimal.ZERO;
		
		if ("0".equals(mainText.getText().trim()) && "".equals(subText.getText().trim())) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}
		
		if ("".equals(mainText.getText().trim()) && "0".equals(subText.getText().trim())) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}
		
		if (lot.getMainQty() != null && !"".equals(mainText.getText().trim())) {
			inputMainQty = new BigDecimal(mainText.getText());
			if (inputMainQty.compareTo(BigDecimal.ZERO) < 0) {
				UI.showError(Message.getString("wip.scrap_check_qty"));
				return false;
			}
			if (lot.getMainQty().compareTo(inputMainQty.add(addMainQty)) < 0) {
				UI.showError(Message.getString("wip.scrap_more_than_main_qty"));
				mainText.setText("");
				mainText.setFocus();
				return false;
			}
		}
		
		if (lot.getSubQty() != null && !"".equals(subText.getText().trim())) {
			inputSubQty = new BigDecimal(subText.getText());
			if (inputSubQty.compareTo(BigDecimal.ZERO) < 0) {
				UI.showError(Message.getString("wip.scrap_check_qty"));
				return false;
			}
			if (lot.getSubQty().compareTo(inputSubQty.add(addSubQty)) < 0) {
				UI.showError(Message.getString("wip.scrap_more_than_subqty"));
				subText.setText("");
				subText.setFocus();
				return false;
			}
		}
		
		if (inputMainQty.compareTo(BigDecimal.ZERO) == 0
				&& inputSubQty.compareTo(BigDecimal.ZERO) == 0) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}
		return true;
	}

	public ModifyListener getModifyListener(final String dataType) {
		return new ModifyListener() {
			public void modifyText(ModifyEvent e) {
				Text text = (Text) e.widget;
				String value = text.getText().trim();
				if ("".equalsIgnoreCase(value.trim())) {
					return;
				}
				if (!discernQty(value)) {
					text.setText("");
					text.setFocus();
				}
			}

			public boolean discernQty(String value) {
				if (!ValidatorFactory.isValid(dataType, value)) {
					UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInputError()),
							Message.getString("common.inputerror_title"));
					return false;
				}
				return true;
			}
		};
	}

	//删除的方法
	public SelectionListener getDeleteListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {		
			}
			@Override
			public void widgetSelected(SelectionEvent e) {
				//Object[] os = viewer.getCheckedElements();tableViewer
				List<Object> os =  viewer.getCheckedObject();
				removeSelected(os);
			}
		};
	}

	public class ScrapLabelProvider extends LabelProvider implements
			ITableLabelProvider {
		@Override
		public Image getColumnImage(Object element, int columnIndex) {
			return null;
		}

		@Override
		public String getColumnText(Object element, int columnIndex) {
			if (element instanceof ComponentUnit) {
				ComponentUnit component = (ComponentUnit) element;
				switch (columnIndex) {
				case 0:
					return component.getComponentId();
				case 1:
					if (component.getPosition() == null) {
						return "";
					}
					return component.getPosition().toString();
				case 2:
					if (component.getState() == null) {
						return "";
					}
					return component.getState();
				case 3:
					return component.getActionCode() != null ? component
							.getActionCode() : "";
				}
			}
			return "";
		}
	}

	public LotAction getLotAction() {
		LotAction lotAction = new LotAction();
		lotAction.setActionComment(commentText.getText());
		//lotAction.setOcapId(this.OCAPTxt.getText());
		lotAction.setOcapId("");
		return lotAction;
	}

	public void removeSelected(Object[] os) {
		if (os.length != 0) {
			for (Object o : os) {
				QtyUnit pe = (QtyUnit) o;
				scrapLots.remove(pe);
				//viewer.setInput(null);
			}
			refresh();
		}
	}
	
	public void removeSelected(List<Object> os) {
		if (os.size() != 0) {
			for (Object o : os) {
				QtyUnit pe = (QtyUnit) o;
				scrapLots.remove(pe);
			}
			refresh();
		}
	}

	public void refresh() {
		if (getScrapLots() != null) {
			viewer.setInput(getScrapLots());
			//viewer.getInput();
			viewer.refresh();
		}
	}

	public List<ProcessUnit> getScrapLots() {
		return scrapLots;
	}

	public void decorateButton(SquareButton button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData();
		data.horizontalAlignment = GridData.END;
		data.widthHint = 93;
		int widthHint = 92; // IDialogConstants.BUTTON_WIDTH
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}

	public class ScrapLabelProvider1 extends LabelProvider implements
			ITableLabelProvider {
		@Override
		public Image getColumnImage(Object element, int columnIndex) {
			return null;
		}

		@Override
		public String getColumnText(Object element, int columnIndex) {
			//判断显示列表内容-------
			if (element instanceof QtyUnit) {

				// LotHisSBD scrap = (LotHisSBD) element;
				QtyUnit scrap = (QtyUnit) element;
				switch (columnIndex) {
				case 0:
					return scrap.getActionCode();

				case 1:
					if (scrap.getMainQty() == null) {
						return "";
					}
					return scrap.getMainQty().toString();

				case 2:
					if (scrap.getSubQty() == null) {
						return "";
					}
					return scrap.getSubQty().toString();
				}
			}
			return "";
		}
	}

	class ScrapContentProvider implements IStructuredContentProvider {
		@Override
		public Object[] getElements(Object inputElement) {
			if (inputElement instanceof List) {
				return ((List) inputElement).toArray();
			}
			return new Object[0];
		}

		@Override
		public void dispose() {
		}

		@Override
		public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		}
	}

	protected void createViewerComponent(Composite composite,
			FormToolkit toolkit) {
		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
	    tableContainer.setLayout(new GridLayout());
	    tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
	    
		try {
			ADTable table = createADTable();
			viewer = new ListTableManager(table, true);
			viewer.newViewer(tableContainer);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public ADTable createADTable() {
    	ADTable adTable = new ADTable();
    	adTable.setIsFilter(true);
		adTable.setModelName("ADOwnerRefList");
		List<ADField> adFields = new ArrayList<ADField>();		
		
		ADField actionCode = new ADField();
		actionCode.setIsDisplay(true);
		actionCode.setIsMain(true);
		actionCode.setName("actionCode");
		actionCode.setLabel("actionCode");
		actionCode.setLabel_zh(Message.getString("wip.lot_scrap_code"));
		adFields.add(actionCode);
		
		ADField mainQty = new ADField();
		mainQty.setIsDisplay(true);
		mainQty.setIsMain(true);
		mainQty.setName("mainQty");
		mainQty.setLabel("mainQty");
		mainQty.setLabel_zh(Message.getString("wip.main_qty"));
		adFields.add(mainQty);
		
		ADField subQty = new ADField();
		subQty.setIsDisplay(true);
		subQty.setIsMain(true);
		subQty.setName("subQty");
		subQty.setLabel("subQty");
		subQty.setLabel_zh(Message.getString("wip.subqty"));
		adFields.add(subQty);
    	
		adTable.setFields(adFields);
		adTable.setIsFilter(false);
		
		return adTable;
    }
	
	/*
	 * 获得报废
	 */
	protected String getActionCode(){
		if(scrapLots!=null){
			for(ProcessUnit  punit : scrapLots){
				QtyUnit q=(QtyUnit)punit;
				actionCode=q.getActionCode();
			}
		}
		return actionCode;
	}
	
	public List<ProcessUnit> getScrapUnits() {
		List<ProcessUnit> scrapUnits = new ArrayList<ProcessUnit>();
		ArrayList<ProcessUnit> objs = (ArrayList<ProcessUnit>)viewer.getInput();
		if(objs==null){
			return scrapUnits;
		}
		for (ProcessUnit obj : objs) {
			scrapUnits.add(obj);
		}
		return scrapUnits;
	}
	
	public LotAction getScrapAction() {
		LotAction lotAction = new LotAction();
		lotAction.setActionComment(commentText.getText());
		return lotAction;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public Lot getLot() {
		return lot;
	}
}
