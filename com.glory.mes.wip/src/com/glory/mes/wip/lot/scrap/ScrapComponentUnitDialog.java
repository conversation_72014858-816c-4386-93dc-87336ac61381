package com.glory.mes.wip.lot.scrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.HistoryUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitDialog;
import com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitManagerEditor;
import com.glory.mes.wip.lot.carrier.glc.split.LotSortingSplitContext;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class ScrapComponentUnitDialog extends ScrapDialog {
    private static final Logger logger = Logger.getLogger(ScrapComponentUnitDialog.class);
    protected String actionCode;
    protected ScrapComponentUnitForm form;
    protected ScrapEditor scrapEditor;
    protected LotSortingSplitContext context;
    protected boolean isExternal = false;
    protected SquareButton externalSplit;
    protected String operator;

    public ScrapComponentUnitDialog(Shell parentShell) {
        super(parentShell);
    }

    public ScrapComponentUnitDialog(Shell parent, Lot lot) {
        super(parent, lot);
    }
    
    public ScrapComponentUnitDialog(Shell parent, Lot lot, ScrapEditor scrapEditor, String operator) {
        super(parent, lot);
        this.scrapEditor = scrapEditor;
        this.operator = operator;
    }

    @Override
    public void createFrom(Composite composite, FormToolkit toolkit) {
        form = new ScrapComponentUnitForm(composite, SWT.FILL, lot);
        form.tableManager.addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (isExternal) {
					List<Object> elements = form.tableManager.getCheckedObject();
					BigDecimal scrapQty = BigDecimal.ZERO;
					for (Object element : elements) {
						ComponentUnit comp = (ComponentUnit) element;
						scrapQty = scrapQty.add(comp.getMainQty());
					}
					if (scrapQty.compareTo(lot.getMainQty()) == 0) {
						externalSplit.setEnabled(false);
					} else {
						externalSplit.setEnabled(true);
					}
				}
			}
		});
        form.setBackground(new Color(null, 255, 255, 255));
    }

    @Override
    public Point getMinSize() {
        return new Point(600, 400);
    }

    protected void okPressed() {
        if (form.validate()) {
            try {
            	List<ComponentUnit> scrapUnits = new ArrayList<ComponentUnit>();
            	List<Object> elements = form.tableManager.getCheckedObject();
                for (int i = 0; i < elements.size(); i++) {
                    ComponentUnit comp = (ComponentUnit) elements.get(i);
                    scrapUnits.add(comp);
                }
                if (scrapUnits.size() == 0) {
                    UI.showWarning(String.format(Message.getString("common.please_select"),
                            ComponentUnit.class.getSimpleName()));
                    return;
                }
                List<LotAction> scrapLotActions = new ArrayList<LotAction>();
                BigDecimal scrapQty = BigDecimal.ZERO;
                Map<String, LotAction> componentUnitActionMap = Maps.newHashMap();
                for (ComponentUnit componentUnit : scrapUnits) {
                	 LotAction lotAction = form.getScrapAction();
                     lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
                     lotAction.setLotRrn(lot.getObjectRrn());
                     lotAction.setActionCode(componentUnit.getActionCode());
                     lotAction.setActionUnits(Lists.newArrayList(componentUnit));
                     String reasonSector = DBUtil.toString(form.getScrapAction().getActionOperator());
                     String comment = form.getScrapAction().getActionComment() + HistoryUtil.buildHisComment("Reason sector", reasonSector);
                     lotAction.setActionComment(comment);
                     if (StringUtil.isEmpty(form.getScrapAction().getActionComment())) {
             			UI.showInfo(Message.getString("wip.abort_comments_null"));
             			return;
             	     }
                     if (StringUtil.isEmpty(form.getScrapAction().getActionOperator())) {
              			UI.showInfo(Message.getString("wip.abort_dept_null"));
              			return;
              	     }
                     scrapQty = scrapQty.add(componentUnit.getMainQty());
                     scrapLotActions.add(lotAction);
                     componentUnitActionMap.put(componentUnit.getComponentId() , lotAction);
                }
                LotAction lotAction = form.getScrapAction();
                LotManager lotManager = Framework.getService(LotManager.class);
                
                SessionContext sc = Env.getSessionContext();
                sc.setUserName(operator);
                
                if (isExternal) {
                	if (scrapQty.compareTo(lot.getMainQty()) == 0) {
                    	//整批报废
                        lotManager.scrapLot(lot, scrapLotActions, lotAction, false, sc);
                    } else {
                    	if (!validateExternal()) {
                        	return;
                        }
                    	//检查分批信息和报废信息是否一致
                    	List<ComponentUnit> sorterUnits = context.getChildUnitList().get(0);
                    	if (scrapUnits.size() == sorterUnits.size()) {
                    		for (ComponentUnit sorterUnit : sorterUnits) {
								if (!scrapUnits.contains(sorterUnit)) {
									UI.showWarning(String.format(Message.getString("wip.scrapList_is_not_equal_sorterList"),
											ComponentUnit.class.getSimpleName()));
									return;
								}
                    		}
                    	} else {
                    		UI.showWarning(Message.getString("wip.scrapList_is_not_equal_sorterList"));
							return;
                    	}
                    	
                    	LotSortingAction lotSortingAction = (LotSortingAction) context.getLotActions().get(0);
                    	String reasonSector = DBUtil.toString(form.getScrapAction().getActionOperator());
                        String comment = form.getScrapAction().getActionComment() + HistoryUtil.buildHisComment("Reason sector", reasonSector);
                    	lotSortingAction.setActionComment(comment);
                    	lotSortingAction.setSortingMode(LotSortingJob.SORTING_MODE_SPLIT_BYLOGIC);
                    	
                    	//部分报废
                    	if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
							return;
						}
        				lotManager.scrapLot(context.getSourceLot(), scrapLotActions, lotSortingAction, false, sc);
                    }
                } else {
                	if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
						return;
					}
                    lotManager.scrapLot(lot, scrapLotActions, lotAction, sc);
                }
                UI.showInfo(Message.getString("wip.scraplot_success"));
            } catch (Exception e) {
                logger.error("Scrap Lot Failure at ScrapLotDialog : " + e);
                ExceptionHandlerManager.asyncHandleException(e);
                return;
            }
            
            super.okPressed();
        }
    }

    @Override
    protected void buttonPressed(int buttonId) {
		if (IDialogConstants.OK_ID == buttonId) {
			okPressed();
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed();
		} else if (IDialogConstants.YES_ID == buttonId)  {
			splitPressed();
		}
	}
    
    protected boolean splitPressed() {
        if (form.validate()) {
            try {
            	List<ComponentUnit> scrapUnits = new ArrayList<ComponentUnit>();
            	List<Object> elements = form.tableManager.getCheckedObject();
            	BigDecimal scrapQty = BigDecimal.ZERO;
                for (int i = 0; i < elements.size(); i++) {
                    ComponentUnit comp = (ComponentUnit) elements.get(i);
                    scrapQty = scrapQty.add(comp.getMainQty());
                    scrapUnits.add(comp);
                }
                if (scrapUnits.size() == 0) {
                    UI.showWarning(String.format(Message.getString("common.please_select"),
                            ComponentUnit.class.getSimpleName()));
                    return false;
                }
                if (scrapQty.compareTo(lot.getMainQty()) != 0) {
                	Map<String, Object> initDatas = Maps.newHashMap();
                	initDatas.put(MesGlcEvent.PROPERTY_LOT_ID, lot.getLotId());
                	initDatas.put(CarrierLotSplitManagerEditor.IS_SORT, true);
                	initDatas.put(CarrierLotSplitManagerEditor.TARGET_TAB_SIZE, 1);
        			List<List<ComponentUnit>> scrapUnitLists = Lists.newArrayList();
        			scrapUnitLists.add(scrapUnits);
        			initDatas.put(CarrierLotSplitManagerEditor.IS_AUTO_TRANSFER, false);
        			initDatas.put(CarrierLotSplitManagerEditor.TARGET_COMPONENT_LIST, scrapUnitLists);
        			initDatas.put(CarrierLotSplitManagerEditor.IS_SHOW_MERGE_STEP, false);
        			CarrierLotSplitDialog dialog = new CarrierLotSplitDialog(CarrierLotSplitManagerEditor.ADFORM_NAME, CarrierLotSplitManagerEditor.AUTHORITY_NAME, scrapEditor.getEventBroker());
        			if (Dialog.OK == dialog.open(initDatas)) {
        				context = dialog.getContext();
        				return true;
        			}
                } else {
                	return true;
                }
            } catch (Exception e) {
                logger.error("Scrap Lot Failure at splitPressed : " + e);
                ExceptionHandlerManager.asyncHandleException(e);
            }
        }
        return false;
    }

	public boolean validateExternal() {
        if (isExternal) {
        	if (context == null) {
        		return splitPressed();
        	}
        }
        return true;
    }

    @Override
	protected void createButtonsForButtonBar(Composite parent) {
    	SquareButton ok = createSquareButton(parent, IDialogConstants.OK_ID,
				Message.getString(ExceptionBundle.bundle.CommonOk()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			isExternal = MesCfMod.isScrapSorter(Env.getOrgRrn(), sysParamManager);
			if (isExternal) {
				externalSplit = createSquareButton(parent, IDialogConstants.YES_ID,
						Message.getString("common.external_split"), false, UIControlsFactory.BUTTON_DEFAULT);
				
				fd = new FormData();
				fd.width = 90;
				fd.height = 35;
				fd.top = new FormAttachment(0, 15);
				fd.right = new FormAttachment(ok, -12, SWT.LEFT);
				fd.bottom = new FormAttachment(100, -15);
				externalSplit.setLayoutData(fd);
	    	}
		} catch (Exception e) {
			logger.error("Scrap Lot Failure at createButtonsForButtonBar : " + e);
            ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
    }
}
