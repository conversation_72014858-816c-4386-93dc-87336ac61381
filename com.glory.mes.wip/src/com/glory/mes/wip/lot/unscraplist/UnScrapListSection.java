package com.glory.mes.wip.lot.unscraplist;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.unscrapnew.UnScrapNewContext;
import com.glory.mes.wip.lot.unscrapnew.UnScrapNewDialog;
import com.glory.mes.wip.lot.unscrapnew.UnScrapNewWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.glory.framework.core.exception.ExceptionBundle;

public class UnScrapListSection extends QueryEntityListSection implements IRefresh {

	public static final String KEY_UNSCRAP = "unScrap";
	public static final String KEY_UNSCRAP_NEW = "unScrapNew";
	
	protected AuthorityToolItem itemUnScrap;
	protected AuthorityToolItem itemUnScrapNew;

	// 是否按片报废
	private boolean isUnScrapComponent = false;
	// 是否有子数量
	private boolean isUnScrapQty = false;

	public UnScrapListSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemUnScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnScrapNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemUnScrap(ToolBar tBar) {
		itemUnScrap = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_UNSCRAP);
		itemUnScrap.setText(Message.getString("wip.unscrap"));
		itemUnScrap.setImage(SWTResourceCache.getImage("unscrap"));
		itemUnScrap.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unScrapAdapter();
			}
		});
	}

	protected void createToolItemUnScrapNew(ToolBar tBar) {
		itemUnScrapNew = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_UNSCRAP_NEW);
		itemUnScrapNew.setText(Message.getString("unscrap.unscrapnew_new"));
		itemUnScrapNew.setImage(SWTResourceCache.getImage("unscrap-lot"));
		itemUnScrapNew.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unScrapNewAdapter();
			}
		});
	}

	protected void unScrapAdapter() {
		try {
			List<Object> objects = getCheckedObject();
			if (CollectionUtils.isNotEmpty(objects)) {
				List<LotScrap> lotScraps = objects.stream().map(l -> ((LotScrap) l)).collect(Collectors.toList());
				if (validate(lotScraps, false)) {
					// 判断是按批次还是按片的反报废
					for (LotScrap lotScrap : lotScraps) {
						if (isUnScrapComponent) {
							ComponentUnit componentUnit = new ComponentUnit();
							componentUnit.setObjectRrn(lotScrap.getComponentRrn());
							componentUnit = (ComponentUnit) adManager.getEntity(componentUnit);
							// 片当前批次RRN
							lotScrap.setAttribute1(componentUnit.getParentUnitRrn());
							// 带入原position
							lotScrap.setAttribute2(componentUnit.getPosition());
						} else {
							// 批次RRN
							lotScrap.setAttribute1(lotScrap.getLotRrn());
						}

						lotScrap.setSubQty(lotScrap.getSubQty() == null ? BigDecimal.ZERO : lotScrap.getSubQty());
						// 反报废数量
						lotScrap.setUnScrapMainQty(lotScrap.getMainQty());
						lotScrap.setUnScrapSubQty(lotScrap.getSubQty());
						// 反报废代码
						lotScrap.setUnScrapCode("");
						// 备注
						lotScrap.setUnScrapComment("");
					}
					
					LotManager lotManager = Framework.getService(LotManager.class);
					Lot lot = null;
					if (isUnScrapComponent) {
						lot = lotManager.getLotWithComponent(lotScraps.get(0).getLotRrn());
						
						if(LotStateMachine.STATE_RUN.equals(lot.getState()) || LotStateMachine.STATE_TERM.equals(lot.getState())) {
							UI.showError(Message.getString("wip.lot_state_is_run_cannot_unscarp"));
							return;
						} else {
							// 完全报废的批次
							if (LotStateMachine.COMCLASS_COM.equals(lot.getComClass())
									&& LotStateMachine.STATE_SCRAP.equals(lot.getState())) {
								for(LotScrap lotScarp : lotScraps) {
									if(!lotScarp.getStepName().equals(lot.getStepName())) {
										UI.showError(Message.getString("wip.component_state_or_stepname_inconsistent_with_lot"));
										return;
									}
								}
									
							} else {
								for(LotScrap lotScarp : lotScraps) {
									if((!lotScarp.getState().equals(lot.getState())) || 
											(!lotScarp.getStepName().equals(lot.getStepName()))) {
										UI.showError(Message.getString("wip.component_state_or_stepname_inconsistent_with_lot"));
										return;
									}
								}
							}
						}
					}
					
					UnScrapListDialog dialog = new UnScrapListDialog(UI.getActiveShell(), lot, lotScraps, isUnScrapComponent,
							getADManger());
					if (Dialog.OK == dialog.open()) {
						if (isUnScrapComponent) {
							List<ComponentUnit> unScrapUnits = Lists.newArrayList();
							ComponentManager componentManager = Framework.getService(ComponentManager.class);
							if (isUnScrapQty) {
								Map<String, List<LotUnScrapAction>> unScrapActionsMap = Maps.newHashMap();
								// 把scrap转成action
								for (LotScrap scrap : dialog.getLotScraps()) {
									LotUnScrapAction action = new LotUnScrapAction();
									action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
									action.setActionCode(scrap.getUnScrapCode());
									action.setActionComment(scrap.getUnScrapComment());
									if (StringUtil.isEmpty(scrap.getUnScrapComment())) {
										UI.showInfo(Message.getString("wip.abort_comments_null"));
										return;
									}
									action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
									action.setLotScrap(scrap);

									// 处理报废参数
									ComponentUnit unit = componentManager.getComponentByComponentId(Env.getOrgRrn(),
											scrap.getComponentId());
									if (!unScrapUnits.contains(unit)) {
										unit.setPosition(DBUtil.toString(scrap.getAttribute2()));
										unScrapUnits.add(unit);
									}

									if (!unScrapActionsMap.containsKey(scrap.getComponentId())) {
										unScrapActionsMap.put(scrap.getComponentId(), Lists.newArrayList());
									}

									unScrapActionsMap.get(scrap.getComponentId()).add(action);
								}
								if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
									return;
								}
								componentManager.unScrapComponentQty(unScrapUnits, unScrapActionsMap, true,
										Env.getSessionContext());
							} else {
								List<LotUnScrapAction> unScrapActions = Lists.newArrayList();
								Lot lastParentLot = null;
								// 把scrap转成action
								for (LotScrap scrap : dialog.getLotScraps()) {
									// 清空子数量
									scrap.setSubQty(null);
									scrap.setUnScrapSubQty(null);

									LotUnScrapAction action = new LotUnScrapAction();
									action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
									action.setActionCode(scrap.getUnScrapCode());
									action.setActionComment(scrap.getUnScrapComment());
									if (StringUtil.isEmpty(scrap.getUnScrapComment())) {
										UI.showInfo(Message.getString("wip.abort_comments_null"));
										return;
									}
									action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
									action.setLotScrap(scrap);

									// 处理报废参数
									ComponentUnit unit = componentManager.getComponentByComponentId(Env.getOrgRrn(),
											scrap.getComponentId());
									if (!unScrapUnits.contains(unit)) {
										unit.setPosition(DBUtil.toString(scrap.getAttribute2()));
										unScrapUnits.add(unit);
										unScrapActions.add(action);
									}

									if (lastParentLot == null) {
										lastParentLot = lotManager.getLotByLotId(Env.getOrgRrn(), scrap.getLotId(),
												true);
									}
								}
								if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
									return;
								}
								lastParentLot.setOperator1(Env.getUserName());
								if (itemUnScrap.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
									lastParentLot.setOperator1((String) itemUnScrap.getData(LotAction.ACTION_TYPE_OPERATOR));
								}
								componentManager.unScrapComponent(lastParentLot, unScrapUnits, unScrapActions, null,
										Env.getSessionContext());
							}
						} else {
							Map<Long, List<LotUnScrapAction>> unScrapActionsMap = Maps.newHashMap();
							// 把scrap转成action
							for (LotScrap scrap : dialog.getLotScraps()) {
								LotUnScrapAction action = new LotUnScrapAction();
								action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
								action.setActionCode(scrap.getUnScrapCode());
								action.setActionComment(scrap.getUnScrapComment());
								if (StringUtil.isEmpty(scrap.getUnScrapComment())) {
									UI.showInfo(Message.getString("wip.abort_comments_null"));
									return;
								}
								action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
								action.setLotScrap(scrap);

								if (!unScrapActionsMap.containsKey(action.getLotRrn())) {
									unScrapActionsMap.put(action.getLotRrn(), Lists.newArrayList());
								}

								unScrapActionsMap.get(action.getLotRrn()).add(action);
							}
							if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
								return;
							}
							SessionContext sc = Env.getSessionContext();
							if (itemUnScrap.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
								sc.setUserName((String) itemUnScrap.getData(LotAction.ACTION_TYPE_OPERATOR));
							}
							lotManager.unScrapLot(unScrapActionsMap, sc);
						}
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					}
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void unScrapNewAdapter() {
		try {
			List<Object> objects = getCheckedObject();
			if (CollectionUtils.isNotEmpty(objects)) {
				List<LotScrap> lotScraps = objects.stream().map(l -> ((LotScrap) l)).collect(Collectors.toList());
				if (validate(lotScraps, true)) {
					UnScrapNewContext context = new UnScrapNewContext();
					// 检查每片都是否与批次解绑
					for (LotScrap lotScrap : lotScraps) {
						ProcessUnit unit = null;
						if (isUnScrapComponent) {
							unit = new ComponentUnit();
							unit.setObjectRrn(lotScrap.getComponentRrn());
							unit = (ComponentUnit) getADManger().getEntity(unit);
							if (!LotStateMachine.STATE_SCRAP.equals(((ComponentUnit) unit).getState())) {
								UI.showError(String.format(Message.getString("wip.unscrap_use_unscrap"),
										((ComponentUnit) unit).getComponentId()));
								return;
							}
							lotScrap.setAttribute1(((ComponentUnit) unit).getPosition());
							lotScrap.setAttribute3(unit);
						}

						lotScrap.setSubQty(lotScrap.getSubQty() == null ? BigDecimal.ZERO : lotScrap.getSubQty());
						// 反报废数量
						lotScrap.setUnScrapMainQty(lotScrap.getMainQty());
						lotScrap.setUnScrapSubQty(lotScrap.getSubQty());
						// 反报废代码
						lotScrap.setUnScrapCode("");
						// 备注
						lotScrap.setUnScrapComment("");
					}
					context.setLotScraps(lotScraps);
					context.setLotAction(new LotAction());
					context.setUnScrapComponent(isUnScrapComponent);
					context.setUnScrapSubQty(isUnScrapQty);
					String operator = Env.getUserName();
					if (itemUnScrapNew.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
						operator = (String) itemUnScrapNew.getData(LotAction.ACTION_TYPE_OPERATOR);
					}
					context.setOperator(operator);

					UnScrapNewWizard wizard = new UnScrapNewWizard(context);
					UnScrapNewDialog dialog = new UnScrapNewDialog(Display.getCurrent().getActiveShell(), wizard);
					if (dialog.open() == Dialog.OK) {
						UI.showInfo(Message.getString("unscrap.unscrapnew_operate_success"));
					}
					refreshAdapter();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private boolean validate(List<LotScrap> lotScraps, boolean isNew) {
		// 不允许按片、按批次同时操作
		boolean isUnScrapComponent = false;
		boolean isUnScrapLot = false;

		boolean isUnScrapSubQty = false;
		boolean isUnScrapMainQty = false;
		Set<String> lotIds = Sets.newHashSet();
		for (LotScrap lotScrap : lotScraps) {
			if (lotScrap.getComponentId() != null) {
				isUnScrapComponent = true;
			} else {
				isUnScrapLot = true;
			}

			if (lotScrap.getSubQty() == null || BigDecimal.ZERO.compareTo(lotScrap.getSubQty()) == 0) {
				isUnScrapMainQty = true;
			} else {
				isUnScrapSubQty = true;
			}
			
			lotIds.add(lotScrap.getLotId());
		}

		if (isUnScrapComponent && isUnScrapLot) {
			UI.showError(Message.getString("wip.unscrap_new_panel_lot_not_allow"));
			return false;
		}

		if (isUnScrapMainQty && isUnScrapSubQty) {
			UI.showError(Message.getString("wip.unscrap_main_sub_not_allow"));
			return false;
		}

		if (!isNew) {
			if (isUnScrapComponent && lotIds.size() > 1) {
				UI.showError(Message.getString("wip.unscrap_comp_multiple_lot"));
				return false;
			}
		}

		this.isUnScrapComponent = isUnScrapComponent;
		this.isUnScrapQty = isUnScrapSubQty;
		return true;
	}

}
