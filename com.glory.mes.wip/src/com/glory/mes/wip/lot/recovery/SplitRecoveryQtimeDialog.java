package com.glory.mes.wip.lot.recovery;

import java.util.function.Consumer;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.future.FutureTimer;

/**
 * 分批Sorted情况下才使用
 * <AUTHOR>
 *
 */
public class SplitRecoveryQtimeDialog extends GlcBaseDialog {

    public static final String FIELD_QTIME_FORM = "stepQtime";

	private Consumer<SplitRecoveryQtimeDialog> closeAdaptor;
	
    protected EntityFormField  qtimeEntityField;

	protected FutureTimer newFutureTimer;

	protected FutureTimer futureTimer;
	
    public SplitRecoveryQtimeDialog(String adFormName, String authority, IEventBroker eventBroker,
			FutureTimer newFutureTimer) {
		super(adFormName, authority, eventBroker);
		this.setShellStyle(getShellStyle() | SWT.MODELESS);
		this.newFutureTimer = newFutureTimer;
        this.setBlockOnOpen(false);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

        qtimeEntityField = form.getFieldByControlId(FIELD_QTIME_FORM, EntityFormField.class);
		qtimeEntityField.setValue(newFutureTimer);
		qtimeEntityField.refresh();
	}
	
    public FutureTimer getFutureTimer() {
		return futureTimer;
    }
    
	@Override
	protected void okPressed() {
		if (qtimeEntityField.validate()) {
			futureTimer = (FutureTimer) qtimeEntityField.getValue();
			if (futureTimer.getIsAll()) {
				futureTimer.setProcedureVersion(null);
			}
			super.okPressed();
		}
	}

	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<SplitRecoveryQtimeDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<SplitRecoveryQtimeDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}

}
