package com.glory.mes.wip.lot.cutting;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.config.Config;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.cutting.ComponentCuttingComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.pp.wo.start.bylot.MLotComponentAssignComposite;
import com.glory.mes.ras.model.EquipmentLine;

public class CuttingSection extends EntitySection {
	private static final Logger logger = Logger.getLogger(CuttingSection.class);
	//public static String TABLE_NAME = "WIPByEqpRunningLot";

	private ComponentCuttingComposite componentCuttingComposite;
	
	protected Equipment currentEqp;

	public CuttingSection() {
		super();
	}

	public CuttingSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		this.form = form;
		// 外部最大的Composite;
		Composite client = form.getToolkit().createComposite(parent, SWT.NONE);
		GridLayout gl = new GridLayout(1, true);
		client.setLayout(gl);
		client.setLayoutData(new GridData(GridData.FILL_BOTH));
		// createSectionTitle(client);
		createSectionContent(client);
	}

	protected void createSectionContent(Composite client) {
		
		componentCuttingComposite = new ComponentCuttingComposite(client, SWT.NONE);

	}

	@Override
	public void createToolBar(Section section) {
	}
//
//	// 右边的composite;
//	protected void createTopSectionContent(FormToolkit toolkit, Composite parent) {
//		Composite top = toolkit.createComposite(parent, SWT.NONE);
//		GridLayout gl = new GridLayout(1, true);
//		top.setLayout(gl);
//		top.setLayoutData(new GridData(GridData.FILL_BOTH));
//
//		// createSelectedLotSectionContent(top);
//		createRunningJobSectionContent(top);
//	}
//
//	protected void createRunningJobSectionContent(Composite parent) {
//		try {
//			ADTable adTable = null;
//			ADManager adManager = Framework.getService(ADManager.class);
//			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
//
//			runSection = new RunningLotSection(adTable, this);
//			runSection.createContents(form, parent);
//		} catch (Exception e) {
//			logger.error("Error at ByEqpSection : createRunningJobSectionContent() : "
//							+ e);
//		}
//	}
//
//	// 左边的composite;
//	protected void createWaitingLotSectionContent(FormToolkit toolkit, Composite parent) {
//		Composite waitComp = toolkit.createComposite(parent, SWT.NONE);
//		GridLayout gl = new GridLayout(1, true);
//		waitComp.setLayout(gl);
//		waitComp.setLayoutData(new GridData(GridData.FILL_BOTH));
//
//		WaitingLotTableManager tableManager = new WaitingLotTableManager(table, true);
//		waitSection = new WaitingLotSection(tableManager, this);
//		waitSection.createContents(form, waitComp);
//		this.refresh();
//	}

	public void refresh() {
		componentCuttingComposite.refresh();
//		try {
//			if (this.currentEqp != null && this.currentEqp.getObjectRrn() != null) {
//				LotManager lotManager = Framework.getService(LotManager.class);
//				List<Lot> lots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(), 
//						LotStateMachine.STATE_WAIT, Env.getSessionContext());
//				waitSection.setWaitingLots(filterWaitingLots(lots));
//				waitSection.refresh();
//				runSection.setRunningLots(lotManager.getRunningLotsByEqp(Env.getOrgRrn(), currentEqp.getEquipmentId()));
//				runSection.refresh();
//				setHoldLots(filterHoldLots(lots));
//			}
//			form.getMessageManager().removeAllMessages();
//		} catch (Exception e) {
//			logger.error("Error at LocationFactory : getEquipments() ", e);
//		}
	}

//	public void addToWaitingSection(Lot lot) {
//		waitSection.addWaitLot(lot);
//	}

//	protected List<Lot> filterHoldLots(List<Lot> lots) {
//		List<Lot> holdLots = new ArrayList<Lot>();
//		if (this.currentEqp != null && this.currentEqp.getObjectRrn() != null) {
//			for (Lot lot : lots) {
//				if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
//				    holdLots.add(lot);
//				}
//			}
//		}
//		return holdLots;
//	}

	

	public Equipment getCurrentEqp() {
		return currentEqp;
	}
	
	public void setCurrentEqp(Equipment eqp) {
		this.currentEqp = eqp;
		this.refresh();
	}
}
