package com.glory.mes.wip.lot.changeprocess;


import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.TreeItem;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class ChangeProcessManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.changeprocess.ChangeProcessManagerEditor";

	protected static final String CONTROL_LOTID_ENTERPRESSED = "lotId-EnterPressed";
	
	private static final String FIELD_CHANGEPROCESSINFO = "changeProcessInfo";
	private static final String FIELD_COMMENT = "comment";
	private static final String FIELD_PART = "partInfo";
	private static final String FIELD_STATE = "state";
	private static final String FIELD_STEPNAME = "stepName";
	private static final String FIELD_CHANGEPROCESSLOT = "changeProcessLot";
	private static final String FIELD_CHANGEPROCESSPART = "changeProcessPart";

	private static final String BUTTON_CHANGEPROCESS = "changeProcess";
	private static final String BUTTON_REFRESH = "refresh";

	protected GlcFormField changeProcessInfoField;
	protected TextField commentField, partInfoField, stateField, stepNameField;
	protected CustomField changeProcessLotField;
	protected CustomField changeProcessPartField;
	protected FlowCustomComposite changeProcessLot;
	protected FlowCustomComposite changeProcessPart;
	
	protected AuthorityToolItem itemChangeProcess;
	
	protected List<Node> flowList = new ArrayList<Node>();

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		changeProcessInfoField = form.getFieldByControlId(FIELD_CHANGEPROCESSINFO, GlcFormField.class);
		commentField = form.getFieldByControlId(FIELD_COMMENT, TextField.class);
		partInfoField = form.getFieldByControlId(FIELD_PART, TextField.class);
		stateField = form.getFieldByControlId(FIELD_STATE, TextField.class);
		stepNameField = form.getFieldByControlId(FIELD_STEPNAME, TextField.class);
		changeProcessLotField = changeProcessInfoField.getFieldByControlId(FIELD_CHANGEPROCESSLOT, CustomField.class);
		changeProcessPartField = changeProcessInfoField.getFieldByControlId(FIELD_CHANGEPROCESSPART, CustomField.class);

		subscribeAndExecute(eventBroker, changeProcessLotField.getFullTopic(CONTROL_LOTID_ENTERPRESSED), this::enterPressedAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHANGEPROCESS), this::changeProcessAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		subscribeAndExecute(eventBroker, changeProcessInfoField.getFullTopic(FIELD_CHANGEPROCESSPART + "-" +GlcEvent.EVENT_CLICK), this::flowListAdapter);

		changeProcessLot = (FlowCustomComposite) changeProcessLotField.getCustomComposite();
		changeProcessPart = (FlowCustomComposite) changeProcessPartField.getCustomComposite();
		
		changeProcessPart.getTxtId().setEnabled(false);
		
		changeProcessLot.getLblId().setText(Message.getString("lot.lot_id"));
		changeProcessPart.getLblId().setText(Message.getString("wip.part"));
		
		itemChangeProcess = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_CHANGEPROCESS);
		itemChangeProcess.setEnabled(false);
	}
	
	private void enterPressedAdapter(Object object) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Event event = (Event) object;
			String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
			loadProcessTree(lot);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void loadProcessTree(Lot lot) {
		try {
			if (lot != null) {
				partInfoField.setText(lot.getPartId());
				stateField.setText(lot.getCstate());
				stepNameField.setText(lot.getHoldState());
				changeProcessLot.getTxtId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				changeProcessLot.loadFlowTreeByLot(lot);
				
				changeProcessPart.getTxtId().setText(lot.getPartName());
				changeProcessPart.loadFlowTreeByLot(lot);
				statusChanged(lot.getState());
			} else {
				partInfoField.setText("");
				stateField.setText("");
				stepNameField.setText("");
				changeProcessLot.getTxtId().selectAll();
				changeProcessLot.getTxtId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				changeProcessLot.loadFlowTreeByLot(null);
				changeProcessPart.getTxtId().setText("");
				changeProcessPart.loadFlowTreeByLot(null);
				statusChanged(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void flowListAdapter(Object object) {
		try {
			TreeItem[] items = changeProcessPart.getViewer().getTree().getSelection();
			if(items != null && items.length > 0) {
				flowList = new ArrayList<Node>();
				TreeItem item = items[0];
				while(item != null && (item.getData() instanceof Node)){
					flowList.add(0, (Node)item.getData());
					item = item.getParentItem();
				}	
			} else {
				flowList = null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void changeProcessAdapter(Object object) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			if (StringUtil.isEmpty(commentField.getText())) {
				UI.showInfo(Message.getString("wip.abort_comments_null"));
				return;
			}
			
			LotAction lotAction = new LotAction();
			lotAction.setActionComment(commentField.getText());
			Lot lot = (Lot) changeProcessLot.getTreeManager().getInput();
			List<Node> flowList = this.flowList;
			if (CollectionUtils.isEmpty(flowList) || !(flowList.get(flowList.size() - 1) instanceof StepState)) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.SelectNodeNotStepState()));
				return;
			}
			
			for (Node flow : flowList) {
				if (flow instanceof StepState) {
					((StepState)flow).setUsedStep(null);
				}
			}
			
			Process process = new Process();
			process.setObjectRrn(lot.getProcessRrn());
			process = (Process) prdManager.getSimpleProcessDefinition(process, false);

			lot.setOperator1(Env.getUserName());
			
			SessionContext sc = Env.getSessionContext();
			if (itemChangeProcess.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
				sc.setUserName((String) itemChangeProcess.getData(LotAction.ACTION_TYPE_OPERATOR));
			}
			
			ReworkState reworkState = prdManager.getCurrentReworkState(lot.getProcessInstanceRrn());
			if (reworkState != null) {		
				if (flowList.get(flowList.size() - 2) instanceof ReworkState) {		
					Long procedureStateObjectRrn = flowList.get(flowList.size() - 2).getObjectRrn();
					if (!reworkState.getObjectRrn().equals(procedureStateObjectRrn)) {
						//里层Rework往外层Rework跳
						if (UI.showConfirm(Message.getString("wip.lot_targetstep_not_in_rework_whether_to_continue"))) {
							//去除Rework上面的StepState
							List<Node> newFlowList = Lists.newArrayList();							
							for (int i = 0; i < flowList.size(); i++) {
								if (i + 1 < flowList.size()) {
									if (!(flowList.get(i + 1) instanceof ReworkState)) {
										newFlowList.add(flowList.get(i));
									}
								} else if (i + 1 == flowList.size()) {
									newFlowList.add(flowList.get(i));
								}
							}
							lot = lotManager.changeLotStep(lot, process.getObjectRrn(), newFlowList, lotAction, LotStateMachine.TRANS_CHANGESTEP, true, sc);
						} else {
							return;
						}
					} else {
						//在当前Rework中跳
						if (!UI.showConfirm(Message.getString("wip.lot_change_step_whether_to_continue"))) {
							return;
						}
						lot = lotManager.changeLotStep(lot, process.getObjectRrn(), flowList, lotAction, LotStateMachine.TRANS_CHANGESTEP, false, sc);
					}
				} else {
					//在Rework中往外层的Procedure跳
					if (!UI.showConfirm(Message.getString("wip.lot_change_step_whether_to_continue"))) {
						return;
					}
					lot = lotManager.changeLotStep(lot, process.getObjectRrn(), flowList, lotAction, LotStateMachine.TRANS_CHANGESTEP, false, sc);
				}
			} else {
				//在当前Procedure中跳，或往其的Procedure中跳
				if (!UI.showConfirm(Message.getString("wip.lot_change_step_whether_to_continue"))) {
					return;
				}
				lot = lotManager.changeLotStep(lot, process.getObjectRrn(), flowList, lotAction, LotStateMachine.TRANS_CHANGESTEP, false, sc);
			}			
			UI.showInfo(Message.getString("wip.change_process_successed"));// 弹出提示框
			loadProcessTree(lot);
			commentField.setText("");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void refreshAdapter(Object object) {
		try {
			if (!StringUtil.isEmpty(changeProcessLot.getTxtId().getText())) {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), changeProcessLot.getTxtId().getText());
				loadProcessTree(lot);
			} else {
				loadProcessTree(null);
				commentField.setText("");
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void statusChanged(String newStatus) {
		if (newStatus != null && !"".equals(newStatus.trim())) {
			if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus)) {
				itemChangeProcess.setEnabled(true);
			} else {
				Lot lot = (Lot) changeProcessLot.getTreeManager().getInput();
				if (lot != null) {
					if (LotStateMachine.STATE_WAIT.equals(lot.getState())) {
						itemChangeProcess.setEnabled(true);
					} else {
						itemChangeProcess.setEnabled(false);
					}
				} else {
					itemChangeProcess.setEnabled(false);
				}
			}
		} else {
			itemChangeProcess.setEnabled(false);
		}
	}
	
}