package com.glory.mes.wip.lot.componenteqp.hisquery;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.export.NatExporter;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;
import org.jboss.logging.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.query.DateTimeFromToField;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.his.ComponentEquipmentUnitHis;
import com.glory.mes.wip.his.ComponentUnitHis;

public class ComponentEqpHisEditor extends GlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.componenteqp.hisquery.ComponentEqpHisEditor";
	private static final Logger logger = Logger.getLogger(ComponentEqpHisEditor.class);
	private static final String CONTROL_EXPORT = "Export";
	private static final String CONTROL_REFRESH = "Refresh";
	private static final String CONTROL_COMPONENT_ID = "componentId";
	private static final String CONTROL_EQUIPMENT_ID = "equipmentId";
	private static final String CONTROL_STEP_NAME = "stepName";
	private static final String CONTROL_TRANS_TIME = "transTime";

	private static final String CONTROL_COMPONENT_HIS_TABLE = "ComponentHisTable";

	protected QueryFormField queryFormField;
	protected TextField componentIdField;
	protected RefTableField equipmentIdField;
	protected SearchField stepNameField;
	protected DateTimeFromToField transTimeField;

	protected NatTable natTable;
	protected TableViewerManager listTableManager;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		queryFormField = form.getFieldByControlId(CONTROL_COMPONENT_HIS_TABLE, QueryFormField.class);
		QueryTableForm queryForm = queryFormField.getQueryForm();
		queryFormField = form.getFieldByControlId(CONTROL_COMPONENT_HIS_TABLE, QueryFormField.class);
		componentIdField = queryForm.getFieldByControlId(CONTROL_COMPONENT_ID, TextField.class);
		transTimeField = queryForm.getFieldByControlId(CONTROL_TRANS_TIME, DateTimeFromToField.class);
		equipmentIdField = queryForm.getFieldByControlId(CONTROL_EQUIPMENT_ID, RefTableField.class);
		stepNameField = queryForm.getFieldByControlId(CONTROL_STEP_NAME, SearchField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_EXPORT), this::export);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_REFRESH), this::refresh);
		subscribeAndExecute(eventBroker, queryFormField.getFullTopic(GlcEvent.EVENT_QUERY), this::enterPressed);

		listTableManager = queryFormField.getQueryForm().getTableManager();
		listTableManager.addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				doubleClick();
			}
		});
	}

	@SuppressWarnings(value = "unchecked")
	public void enterPressed(Object object) {
		try {			
			if (!queryFormField.getQueryForm().getQueryForm().validate()) {
				return;
			}
			String whereClause = "1 = 1 ";
			whereClause = whereClause + queryFormField.getQueryForm().getQueryForm().createWhereClause(false);
			List<ComponentUnitHis> allHis = Lists.newArrayList();
			ADManager adManager = Framework.getService(ADManager.class);
			List<ComponentUnitHis> unitHisList = adManager.getEntityList(Env.getOrgRrn(), ComponentUnitHis.class, Integer.MIN_VALUE, Integer.MAX_VALUE, 
					whereClause, null, queryFormField.getQueryForm().getQueryForm().getParmaterMap());
			if (CollectionUtils.isNotEmpty(unitHisList)) {
				allHis.addAll(unitHisList);
			}

			List<ComponentEquipmentUnitHis> eqpUnitHisList = adManager.getEntityList(Env.getOrgRrn(), ComponentEquipmentUnitHis.class, Integer.MIN_VALUE,Integer.MAX_VALUE, 
					whereClause, null, queryFormField.getQueryForm().getQueryForm().getParmaterMap());

			if (CollectionUtils.isNotEmpty(eqpUnitHisList)) {
				//ComponentEquipmentUnitHis转为ComponentUnitHis
				for (ComponentEquipmentUnitHis eqpUnitHis : eqpUnitHisList) {
					ComponentUnitHis unitHis = new ComponentUnitHis();
					PropertyUtil.copyProperties(unitHis, eqpUnitHis);
					// 避免引起误会
					unitHis.setHoldState(null);
					allHis.add(unitHis);
				}
			}
			allHis = allHis.stream().sorted(Comparator.comparing(ComponentUnitHis::getTransTime).reversed()).collect(Collectors.toList());
			listTableManager.setInput(allHis);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("ComponentEqpHisEditor EnterPressed:", e);
		}
	}

	public void doubleClick() {
		try {
			ComponentUnitHis componentUnitHis = (ComponentUnitHis) listTableManager.getSelectedObject();
			ComponentHisDialog dialog = new ComponentHisDialog(null, null, eventBroker, componentUnitHis);
			dialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("ComponentEqpHisEditor DoubleClick:", e);
		}
	}

	public void export(Object object) {
		try {
			this.natTable = this.listTableManager.getNatTable();
			(new NatExporter(this.natTable.getShell())).exportSingleLayer(this.listTableManager.getLayer(),
					this.natTable.getConfigRegistry());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings(value = "unchecked")
	public void refresh(Object object) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<Object> objects = (List<Object>) listTableManager.getInput();
			if (CollectionUtils.isNotEmpty(objects)) {
				ComponentUnitHis componentEquipmentUnitHis = (ComponentUnitHis) objects.get(0);
				List<ComponentUnitHis> componentEquipmentUnitHiss = adManager.getEntityList(Env.getOrgRrn(),
						ComponentUnitHis.class, Integer.MAX_VALUE,
						"componentRrn = '" + componentEquipmentUnitHis.getComponentRrn() + "'", "");
				if (componentEquipmentUnitHiss.size() > 0) {
					listTableManager.setInput(componentEquipmentUnitHiss);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
