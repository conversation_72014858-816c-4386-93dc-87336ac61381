package com.glory.mes.wip.lot.detail;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.application.command.CommandFactory;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.future.FutureTimer;
import com.glory.mes.wip.lot.glc.bylot.LotGlcEditor;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotAttributeValue;
import com.glory.mes.wip.model.LotParameter;
import com.glory.mes.wip.model.LotRework;
import com.google.common.collect.Lists;

public class WIPLotDetailManagerEditor extends LotGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.detail.WIPLotDetailManagerEditor";
	public static final String AUHTORITY_ID = CommandFactory.getAutorityId(EDITOR_ID);
	
	private static final String FIELD_DETAIL = "detail";
	private static final String FIELD_BASICINFO = "basicInfo";
	private static final String FIELD_LOTPARAMETERS = "lotParameters";
	private static final String FIELD_SUBPROCESSUNIT = "subProcessUnit";
	private static final String FIELD_LOTATTRIBUTEVALUES = "lotAttributeValues";
	private static final String FIELD_LOTREWORKS = "lotReworks";
	private static final String FIELD_TIMERS = "timers";
	private static final String FIELD_ENTITYFORM = "entityForm";
	private static final String FIELD_PRDPROCEDURE = "prdProcedure";


	private static final String BUTTON_REFRESH = "refresh";

	protected GlcFormField detailField;
	protected GlcFormField basicInfoGlcField;
	protected ListTableManagerField lotParametersField;
	protected ListTableManagerField subProcessUnitField;
	protected ListTableManagerField lotAttributeValuesField;
	protected ListTableManagerField lotReworksField;
	protected ListTableManagerField timersField;
	protected EntityFormField entityFormField;
	protected EntityFormField prdProcedureField;
	
	private Lot adObject;

	@Override
	protected void createFormAction(GlcForm form) {
		detailField = form.getFieldByControlId(FIELD_DETAIL, GlcFormField.class);
		basicInfoGlcField = detailField.getFieldByControlId(FIELD_BASICINFO, GlcFormField.class);
		lotParametersField = detailField.getFieldByControlId(FIELD_LOTPARAMETERS, ListTableManagerField.class);
		subProcessUnitField = detailField.getFieldByControlId(FIELD_SUBPROCESSUNIT, ListTableManagerField.class);
		lotAttributeValuesField = detailField.getFieldByControlId(FIELD_LOTATTRIBUTEVALUES, ListTableManagerField.class);
		lotReworksField = detailField.getFieldByControlId(FIELD_LOTREWORKS, ListTableManagerField.class);
		timersField = detailField.getFieldByControlId(FIELD_TIMERS, ListTableManagerField.class);
		entityFormField = basicInfoGlcField.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		prdProcedureField = basicInfoGlcField.getFieldByControlId(FIELD_PRDPROCEDURE, EntityFormField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		//注销默认回车事件
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_LOTID_ENTERPRESSED));
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED));
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_LOTID_ENTERPRESSED), super::lotIdEnterpressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED), this::carrierIdEnterpressed);
		
		lotQtimeAndComponentForm = form.getFieldByControlId(FIELD_CUSTOM_CARRIERLOT, CustomField.class);
		carrierLotCustomComposite = (CarrierLotCustomComposite) lotQtimeAndComponentForm.getCustomComposite();
		
		txtLotId = carrierLotCustomComposite.getTxtLotId();
		txtCarrierId = carrierLotCustomComposite.getTxtCarrierId();
		
		carrierLotCustomComposite.getLotTableManager().addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				List<Object> objects = carrierLotCustomComposite.getLotTableManager().getCheckedObject();
				if (objects != null && objects.size() > 0) {
					List<Lot> checkLots = objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
					try {
						carrierLotCustomComposite.loadComponentUnits(checkLots);
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
					Lot lot = checkLots.get(0);
					if (lot != null) {
						lot = searchLot(lot.getLotId());
					}
					setAdObject(lot);
					refresh();
			}	
			}
		});
	}
	
	@Override
	public void initializeDatas(Map<String, Object> initDatas) {
		super.initializeDatas(initDatas);
		
		if (MapUtils.isNotEmpty(initDatas)) {
			String lotId = (String) initDatas.get("lotId");
			if (!StringUtil.isEmpty(lotId)) {
				txtLotId.setText(lotId);
				lotIdEnterpressed(null);
			}
		}
	}
	
	public void setAdObject(Lot lot) {
		this.adObject = lot;
		try {
			// 先清空所有数据
			entityFormField.setValue(new Lot());
			entityFormField.refresh();
			prdProcedureField.setValue(new Lot());
			prdProcedureField.refresh();
			lotParametersField.setValue(Lists.newArrayList());
			lotParametersField.refresh();
			subProcessUnitField.setValue(Lists.newArrayList());
			subProcessUnitField.refresh();
			lotAttributeValuesField.setValue(Lists.newArrayList());
			lotAttributeValuesField.refresh();
			lotReworksField.setValue(Lists.newArrayList());
			lotReworksField.refresh();
			timersField.setValue(Lists.newArrayList());
			timersField.refresh();
			
			if (lot != null && lot.getObjectRrn() != null) {	
				// 基本信息
				entityFormField.setValue(lot);
				entityFormField.refresh();
				prdProcedureField.setValue(lot);
				prdProcedureField.refresh();
				// 组件和Qtime信息
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
					if (carrierLotCustomComposite != null && carrierLotCustomComposite.getLotTableManager() != null) {
						carrierLotCustomComposite.getLotTableManager().update(lot);
					}
				}
				// 参数信息
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Map<String, Object> paramMap = new HashMap<String, Object>();
				if(lot.getProcessInstanceRrn() != null) {
					paramMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
					List<LotParameter> params = new ArrayList<LotParameter>();
					if(paramMap !=null && paramMap.size() > 0) {
						for (String key : paramMap.keySet()) {
							LotParameter param = new LotParameter();
							param.setVariableName(key);
							param.setDefaultValue(DBUtil.toString(paramMap.get(key)));
							params.add(param);
						}
						
						lotParametersField.setValue(params);
						lotParametersField.refresh();
					}
				}
				
				// 组件信息
				LotManager lotManager = Framework.getService(LotManager.class);
				lot = lotManager.getLotWithComponent(lot.getObjectRrn());
				if (CollectionUtils.isNotEmpty(lot.getSubProcessUnit())) {
					subProcessUnitField.setValue(lot.getSubProcessUnit());
					subProcessUnitField.refresh();
				}
				
				// 属性
				List<LotAttributeValue> attributes = new ArrayList<LotAttributeValue>();
				Map<String, LotAttributeValue> attributeValueMap = lotManager.getLotAttributeValues(lot.getObjectRrn());
				for (String name : attributeValueMap.keySet()) {
					attributes.add(attributeValueMap.get(name));
				}
				if (CollectionUtils.isNotEmpty(attributes)) {
					lotAttributeValuesField.setValue(attributes);
					lotAttributeValuesField.refresh();
				}
				
				// 返工信息
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotRework> lotReworks = adManager.getEntityList(Env.getOrgRrn(), LotRework.class, Integer.MAX_VALUE,
                         " lotRrn = " + lot.getObjectRrn(), null);
				if (CollectionUtils.isNotEmpty(lotReworks)) {
					lotReworksField.setValue(lotReworks);
					lotReworksField.refresh();
				}
				
				// 定时器
				String whereClause = " timerType = '" + FutureTimer.TIMERTYPE_MAXIMAL + "' and timerAction = '"
						+ FutureTimer.ACTION_NOTE + "' and timerFlag ='" + FutureTimerInstance.FLAG_DONE
						+ "' and lotRrn = " + lot.getObjectRrn();
				List<FutureTimerInstance> noteTimerInstances = adManager.getEntityList(
						Env.getOrgRrn(), FutureTimerInstance.class, Integer.MAX_VALUE, whereClause, "");
				if (CollectionUtils.isNotEmpty(noteTimerInstances)) {
					timersField.setValue(noteTimerInstances);
					timersField.refresh();
				}
			} 
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public Lot getAdObject() {
		return adObject;
	}

	private void refreshAdapter(Object object) {
		try {
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lot = lotManager.getLot(getAdObject().getObjectRrn());
				setAdObject(lot);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

}