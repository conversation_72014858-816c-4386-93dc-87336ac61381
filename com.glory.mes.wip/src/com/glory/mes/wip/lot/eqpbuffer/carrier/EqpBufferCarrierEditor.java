package com.glory.mes.wip.lot.eqpbuffer.carrier;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.jface.dialogs.Dialog;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.LotEquipmentUnit;
import com.google.common.collect.Lists;

public class EqpBufferCarrierEditor extends GlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.eqpbuffer.carrier.EqpBufferCarrierEditor";
	
	protected ListTableManagerField mainEqpListTableManagerField;
	protected GlcFormField bufferCarrierGlcFormField;
	protected EntityFormField bufferEntityFormField;	
	protected ListTableManagerField carrierListTableManagerField;
	
	private static final String FIELD_TABLEMANAGERFORM_MAINEQPLIST = "mainEqpList";
	private static final String FIELD_GLCFORM_BUFFEREQP_CARRIER = "eqpBufferCarrier";
	private static final String FIELD_ENTITYFORM_BUFFERINFO = "bufferInfo";
	private static final String FIELD_TABLEMANAGERFORM_CARRIERLIST = "carrierList";
		
	public static final String CONTROL_BUTTON_BUFFERIN = "bufferIn";
	public static final String CONTROL_BUTTON_BUFFEROUT = "bufferOut";
	
	public static final String BUTTON_LEFT_REFRESH = "refresh";
	public static final String BUTTON_RIGHT_REFRESH = "refresh";
	
	public static final String DIALOG_FORM_WAITLOT = "WIPEqpBufferWaitInCarrierDialog";
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		mainEqpListTableManagerField = form.getFieldByControlId(FIELD_TABLEMANAGERFORM_MAINEQPLIST, ListTableManagerField.class);
		bufferCarrierGlcFormField = form.getFieldByControlId(FIELD_GLCFORM_BUFFEREQP_CARRIER, GlcFormField.class);			
		bufferEntityFormField = bufferCarrierGlcFormField.getFieldByControlId(FIELD_ENTITYFORM_BUFFERINFO, EntityFormField.class);			
		carrierListTableManagerField = bufferCarrierGlcFormField.getFieldByControlId(FIELD_TABLEMANAGERFORM_CARRIERLIST, ListTableManagerField.class);
	
		subscribeAndExecute(eventBroker, mainEqpListTableManagerField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		//BufferIn事件
		subscribeAndExecute(eventBroker, bufferCarrierGlcFormField.getFullTopic(CONTROL_BUTTON_BUFFERIN), this::bufferInAdapter);
						
		//BufferOut事件
		subscribeAndExecute(eventBroker, bufferCarrierGlcFormField.getFullTopic(CONTROL_BUTTON_BUFFEROUT), this::bufferOutAdapter);
		
		//左刷新事件
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_LEFT_REFRESH), this::leftRefreshAdapter);
				
		//右刷新事件
		subscribeAndExecute(eventBroker, bufferCarrierGlcFormField.getFullTopic(BUTTON_RIGHT_REFRESH), this::rightRefreshAdapter);
			
		init();	
	}
	
	protected void init() {
		try {
			ADTable equipmentTable = mainEqpListTableManagerField.getListTableManager().getADTable();
			
			ADManager adManager = Framework.getService(ADManager.class);
			RASManager rasManager = Framework.getService(RASManager.class);
			List<ADBase> equipments = adManager.getEntityList(Env.getOrgRrn(), equipmentTable.getObjectRrn());
			//筛选出有buffer的设备
			Iterator<ADBase> iterator = equipments.iterator();
			while (iterator.hasNext()) {
				Equipment equipment = (Equipment) iterator.next();
//				List<Equipment> bufferEqps = rasManager.getSubEquipments(Env.getOrgRrn(), equipment.getEquipmentId());
//				List<String> eqpCategorys = bufferEqps.stream().map(e -> e.getCategory()).collect(Collectors.toList());
				if (!Equipment.CATEGORY_BUFFER.equals(equipment.getCategory())) {
					iterator.remove();
				}
			}
			equipments.forEach(equipment -> {
				Equipment subEqp = (Equipment) equipment;
				if (subEqp.getParentEqpRrn() != null) {
					Equipment mainEqp = new Equipment();
					mainEqp.setObjectRrn(subEqp.getParentEqpRrn());
					mainEqp = (Equipment) adManager.getEntity(mainEqp);
					subEqp.setAttribute2(mainEqp.getEquipmentId());
				}
			});
			
			mainEqpListTableManagerField.setValue(equipments);
			mainEqpListTableManagerField.refresh();
			
			bufferEntityFormField.setValue(new Equipment());
			bufferEntityFormField.refresh();		
			
			carrierListTableManagerField.setValue(new ArrayList<LotEquipmentUnit>());
			carrierListTableManagerField.refresh();
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
	}
	
	private void selectionChanged(Object obj) {
		try {
			if (mainEqpListTableManagerField.getListTableManager().getSelectedObject() != null) {
				bufferEntityFormField.setValue(new Equipment());
				bufferEntityFormField.refresh();					
				carrierListTableManagerField.setValue(new ArrayList<LotEquipmentUnit>());
				carrierListTableManagerField.refresh();
				
				Equipment mainEquipment = (Equipment) mainEqpListTableManagerField.getListTableManager().getSelectedObject();	
				
				RASManager rasManager = Framework.getService(RASManager.class);
				List<Equipment> bufferEqps = new ArrayList<>();
				if (mainEquipment.getParentEqpRrn() == null) {
					bufferEqps = rasManager.getSubEquipments(Env.getOrgRrn(), mainEquipment.getEquipmentId());
				} else {
					bufferEqps = Lists.newArrayList(mainEquipment);
				}
				
				if (bufferEqps != null && bufferEqps.size() > 0) {
					for (Equipment bufferEqp : bufferEqps) {
						if (Equipment.CATEGORY_BUFFER.equals(bufferEqp.getCategory())) {		
							LotManager lotManager = Framework.getService(LotManager.class);
							List<LotEquipmentUnit> lotEquipmentUnits = lotManager.getLotEquipmentUnits(Env.getOrgRrn(), bufferEqp.getEquipmentId(), null, true);
							if (lotEquipmentUnits != null && lotEquipmentUnits.size() > 0) {
								//按照posion升序降序
								Collections.sort(lotEquipmentUnits, new Comparator<LotEquipmentUnit>() {
									@Override
									public int compare(LotEquipmentUnit o1, LotEquipmentUnit o2) {
										double o1Seq = Double.parseDouble(stringToAscii(o1.getPosition()));
										double o2Seq = Double.parseDouble(stringToAscii(o2.getPosition()));
										if (o1Seq < o2Seq) {
											return -1;
										}
										if (o1Seq > o2Seq) {
											return 0;
										}
										return 1;
									}
								});
								carrierListTableManagerField.setValue(lotEquipmentUnits);
								carrierListTableManagerField.refresh();
							}	
							
							List<LotEquipmentUnit> lotEquipmentCarrierUnits = new ArrayList<>();
							lotEquipmentUnits.forEach(lotEquipmentUnit -> {
								if (!StringUtil.isEmpty(lotEquipmentUnit.getDurableId())) {
									lotEquipmentCarrierUnits.add(lotEquipmentUnit);
								}
							});
							bufferEqp.setAttribute1(lotEquipmentCarrierUnits.size());
							bufferEntityFormField.setValue(bufferEqp);
							bufferEntityFormField.refresh();
							break;
						}
					}
				}
			}					
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
	}
	
	protected void bufferInAdapter(Object object) {
        try {
    		Equipment mainEquipment = (Equipment) mainEqpListTableManagerField.getListTableManager().getSelectedObject();	

        	if (mainEquipment != null) {
        		Equipment bufferEqp = (Equipment) bufferEntityFormField.getValue();
        		if (bufferEqp == null || StringUtil.isEmpty(bufferEqp.getEquipmentId())) {
        			UI.showError(Message.getString("wip.main_eqp_buffer_not_exist"));
        			return;
        		}
        		
        		LotEquipmentUnit targetUnit = (LotEquipmentUnit) carrierListTableManagerField.getListTableManager().getSelectedObject();
        		if (targetUnit == null) {
        			UI.showError(Message.getString("wip.lot_byeqp_buffer_select_rack"));
        			return;
        		}
        		
        		if (!StringUtil.isEmpty(targetUnit.getDurableId())) {
        			UI.showError(Message.getString("wip.lot_byeqp_buffer_rack_stored"));
        			return;
        		}
        		
            	EqpBufferWaitCarrierListDialog glcDialog = new EqpBufferWaitCarrierListDialog(DIALOG_FORM_WAITLOT, null, eventBroker, mainEquipment, bufferEqp.getEquipmentId());			
            	if (glcDialog.open() == Dialog.OK) {
    				LotEquipmentUnit selectUnit = (LotEquipmentUnit) glcDialog.getSelectedObject();	
    				
    				targetUnit.setDurableId(selectUnit.getDurableId());
    				targetUnit.setLotId(selectUnit.getLotId());
    				targetUnit.setLotIdList(selectUnit.getLotIdList());
    				LotManager lotManager = Framework.getService(LotManager.class);
    				lotManager.lotEquipmentUnitIn(targetUnit, bufferEqp, true, true, Env.getSessionContext());
    				
    				UI.showInfo(Message.getString("wip.lot_buffer_in_success"));// 弹出提示框
    				selectionChanged(null);
    			}
        	} else {
        		UI.showError(Message.getString("wip.please_select_main_eqp"));
        		return;
        	}
        	
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            selectionChanged(null);
            return;
        }
    }
	
	protected void bufferOutAdapter(Object object) {
        try {       	
    		LotEquipmentUnit lotEquipmentUnit = (LotEquipmentUnit) carrierListTableManagerField.getListTableManager().getSelectedObject();
        	
    		LotEquipmentUnit targetUnit = (LotEquipmentUnit) carrierListTableManagerField.getListTableManager().getSelectedObject();
    		if (targetUnit == null) {
    			UI.showError(Message.getString("wip.lot_byeqp_buffer_select_rack"));
    			return;
    		}
    		
    		if (StringUtil.isEmpty(targetUnit.getDurableId())) {
    			UI.showError(Message.getString("wip.lot_byeqp_buffer_rack_no_stored"));
    			return;
    		}
    		
    		Equipment buffer = (Equipment) bufferEntityFormField.getValue();
    		
    		LotManager lotManager = Framework.getService(LotManager.class);
    		lotManager.lotEquipmentUnitOut(lotEquipmentUnit, buffer, true, true, Env.getSessionContext());
    		
    		UI.showInfo(Message.getString("wip.lot_buffer_out_success"));// 弹出提示框
    		selectionChanged(null);
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            selectionChanged(null);
            return;
        }
    }
	
	protected void leftRefreshAdapter(Object object) {
		init();
	}
	
	protected void rightRefreshAdapter(Object object) {
		selectionChanged(object);
	}
	
	public static String stringToAscii(String value)  
	{  
	    StringBuffer sbu = new StringBuffer();  
	    char[] chars = value.toCharArray();   
	    for (int i = 0; i < chars.length; i++) {  
	        if(i != chars.length - 1)  
	        {  
	            sbu.append((int)chars[i]);
	        }  
	        else {  
	            sbu.append((int)chars[i]);  
	        }  
	    }  
	    return sbu.toString();  
	}  
	
}
