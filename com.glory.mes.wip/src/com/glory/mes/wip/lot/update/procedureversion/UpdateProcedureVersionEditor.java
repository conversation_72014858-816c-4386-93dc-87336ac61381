package com.glory.mes.wip.lot.update.procedureversion;

import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.glory.framework.core.exception.ExceptionBundle;

public class UpdateProcedureVersionEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.update.procedureversion.UpdateProcedureVersionEditor";

	public static final String DIALOG_FORM_NAME = "WIPLotFutureHoldDialog";
	
	public static final String FIELD_LOTQUERY = "lotQuery";
	public static final String FIELD_UPDATEPROCEDURE = "updateProcedure";
	public static final String FIELD_LOTFLOW = "lotFlow";
	public static final String FIELD_NEWPROCEDUREINFO = "newProcedure";
	public static final String FIELD_SAMENAMEPROCEDURELIST = "sameNameProcedureList";

	public static final String BUTTON_FUTUREHOLD = "futureHold";
	public static final String BUTTON_UPDATEPROCEDURE = "updateProcedure";
	public static final String BUTTON_REFRESH = "refresh";

	protected QueryFormField lotQueryField;
	protected GlcFormField updateProcedureField;
	protected CustomField lotFlowField;
	protected CustomField newProcedureInfoField;
	protected RefTableField sameNameProcedureListField;
	
	protected FlowCustomComposite lotFlowCustomComposite;
	protected FlowCustomComposite newProcedureFlowCustomComposite;
	
	protected Procedure targetProcedure;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotQueryField = form.getFieldByControlId(FIELD_LOTQUERY, QueryFormField.class);
		updateProcedureField = form.getFieldByControlId(FIELD_UPDATEPROCEDURE, GlcFormField.class);
		
		lotFlowField = updateProcedureField.getFieldByControlId(FIELD_LOTFLOW, CustomField.class);
		lotFlowCustomComposite = (FlowCustomComposite) lotFlowField.getCustomComposite();
		
		sameNameProcedureListField = updateProcedureField.getFieldByControlId(FIELD_SAMENAMEPROCEDURELIST, RefTableField.class);
	
		sameNameProcedureListField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object sender, Object newValue) {
				procedureValueChanged(sender, newValue);		
			}			
		});
		
		newProcedureInfoField = updateProcedureField.getFieldByControlId(FIELD_NEWPROCEDUREINFO, CustomField.class);
		newProcedureFlowCustomComposite = (FlowCustomComposite) newProcedureInfoField.getCustomComposite();
		
//		subscribeAndExecute(eventBroker, updateProcedureField.getFullTopic(FIELD_NEWPROCEDUREINFO + 
//				GlcEvent.NAMESPACE_SEPERATOR + "procedureId"  + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::procedureSelectionChanged);
	
		subscribeAndExecute(eventBroker, lotQueryField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		subscribeAndExecute(eventBroker, lotQueryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_FUTUREHOLD), this::futureHoldAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UPDATEPROCEDURE), this::updateProcedureAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
	}
	
	private void queryAdapter(Object object) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> lots = lotManager.getLotsByCondition(Env.getOrgRrn(), lotQueryField.getQueryForm().getWhereClause(), null, true);
			lotQueryField.getQueryForm().getTableManager().setInput(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void selectionChanged(Object object) {
		try {
			Event event = (Event) object;
			Lot lot = (Lot)event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lot != null) {
				lotFlowCustomComposite.getTxtId().setText(lot.getLotId());
				lotFlowCustomComposite.loadFlowTreeByLot(lot);
				
				if (targetProcedure == null || (targetProcedure != null && !targetProcedure.getName().equals(lot.getProcedureName()))) {
					sameNameProcedureListField.setValue(null);
					sameNameProcedureListField.refresh();
					ADManager adManager = Framework.getService(ADManager.class);
					List<Procedure> procedureList = adManager.getEntityList(Env.getOrgRrn(), Procedure.class, Env.getMaxResult(),
							" name = '" + lot.getProcedureName() + "' and status in ('Active', 'InActive')", "version");
					sameNameProcedureListField.setInput(procedureList);
					
					newProcedureFlowCustomComposite.loadFlowTreeByProcedure(null, null);
					targetProcedure = null;
				}
				
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void procedureValueChanged(Object sender, Object newValue) {
		try {
			String procedureRrn = (String) newValue;
			if (!StringUtil.isEmpty(procedureRrn)) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Procedure procedure = new Procedure();
				procedure.setObjectRrn(Long.valueOf(procedureRrn));
				procedure = (Procedure) prdManager.getProcessDefinition(procedure);
				newProcedureFlowCustomComposite.loadFlowTreeByProcedure(procedure, null);
				targetProcedure = procedure;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}

//	private void procedureSelectionChanged(Object obj) {
//		try {
//			Event event = (Event) obj;
//			String procedureRrn = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
//			if (!StringUtil.isEmpty(procedureRrn)) {
//				PrdManager prdManager = Framework.getService(PrdManager.class);
//				Procedure procedure = new Procedure();
//				procedure.setObjectRrn(Long.valueOf(procedureRrn));
//				procedure = (Procedure) prdManager.getProcessDefinition(procedure);
//				newProcedureFlowCustomComposite.loadFlowTreeByProcedure(procedure, null);
//				targetProcedure = procedure;
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//			return;
//		}	
//	}
	
	private void futureHoldAdapter(Object object) {
		try {
			List<Object> checkedObjects = lotQueryField.getCheckedObjects();
			if (CollectionUtils.isNotEmpty(checkedObjects)) {
				List<Lot> lots = Lists.newArrayList();
				Set<Long> procedureRrnList = Sets.newHashSet();
				for (Object checkedObject : checkedObjects) {
					Lot lot = (Lot) checkedObject;
					procedureRrnList.add(lot.getProcedureRrn());
					lots.add(lot);
				}
				if (procedureRrnList.size() != 1) {
					UI.showError(Message.getString("wip.lot_procedure_is_not_same"));
					return;
				}
				LotFutureHoldDialog dialog = new LotFutureHoldDialog(DIALOG_FORM_NAME, null, eventBroker, lots);
				if (Dialog.OK == dialog.open()) {
					UI.showInfo(Message.getString("wip.processhold_holdsuccessed"));
					refreshAdapter(null);
				}
			} else {
				UI.showError(Message.getString("security.please_checked_table_datas"));
				return;
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void updateProcedureAdapter(Object object) {
		try {
			List<Object> checkedObjects = lotQueryField.getCheckedObjects();
			if (CollectionUtils.isNotEmpty(checkedObjects)) {
				List<Lot> lots = Lists.newArrayList();
				Set<Long> procedureRrnList = Sets.newHashSet();
				for (Object checkedObject : checkedObjects) {
					Lot lot = (Lot) checkedObject;
					if (!LotStateMachine.STATE_WAIT.equals(lot.getState())) {
						UI.showError(Message.getString("wip.lot_is_not_state_wait"));
						return;
					}
					procedureRrnList.add(lot.getProcedureRrn());
					lots.add(lot);
				}
				if (procedureRrnList.size() != 1) {
					UI.showError(Message.getString("wip.lot_procedure_is_not_same"));
					return;
				}
				
				if (targetProcedure == null) {
					UI.showError(Message.getString("wip.noflow_be_selected"));
					return;
				}
				
				if (!targetProcedure.getName().equals(lots.get(0).getProcedureName())) {
					UI.showError(Message.getString("wip.select_porcedure_and_lot_procedure_not_same"));
					return;
				}
				
				IStructuredSelection selection = (IStructuredSelection) newProcedureFlowCustomComposite.getViewer().getSelection();
				StepState targetStepState = (StepState)selection.getFirstElement();
				if (targetStepState == null) {
					UI.showError(Message.getString("wip.select_lot_procedure_step_change"));
					return;
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.updateLotProcedureStepNewVersion(lots, targetProcedure, targetStepState, null, true, true, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
				refreshAdapter(null);
			} else {
				UI.showError(Message.getString("security.please_checked_table_datas"));
				return;
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void refreshAdapter(Object object) {
		queryAdapter(object);
		
		lotFlowCustomComposite.getTxtId().setText("");
		lotFlowCustomComposite.loadFlowTreeByLot(null);
		
		sameNameProcedureListField.setValue(null);
		sameNameProcedureListField.refresh();
		
		newProcedureFlowCustomComposite.loadFlowTreeByProcedure(null, null);
		targetProcedure = null;
	}

	

}