package com.glory.mes.wip.lot.transferstate;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class TransferStateSection extends LotSection {
    private static final Logger logger = Logger.getLogger(EntitySection.class);

    protected ToolItem itemEnterBuffer;
    protected ToolItem itemOutBuffer;

    protected TransferStateForm itemForm;

    public TransferStateSection() {
        super();
    }

    public TransferStateSection(ADTable table) {
        super(table);
    }

    @Override
    public void createContents(IManagedForm form, Composite parent) {
        super.createContents(form, parent);
        section.setText(Message.getString("wip.transfetstate_sectiontitle"));
        initAdObject();
    }

    public void initAdObject() {
        setAdObject(new Lot());
        refresh();
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolitemEnterBuffer(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolitemOutBuffer(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    protected void createToolitemEnterBuffer(ToolBar tBar) {
        itemEnterBuffer = new ToolItem(tBar, SWT.PUSH);
        itemEnterBuffer.setText("Enter");
        itemEnterBuffer.setImage(SWTResourceCache.getImage("hold-lot"));
        itemEnterBuffer.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                enterAdapter(event);
            }
        });
    }

    protected void createToolitemOutBuffer(ToolBar tBar) {
        itemOutBuffer = new ToolItem(tBar, SWT.PUSH);
        itemOutBuffer.setText("Out");
        itemOutBuffer.setImage(SWTResourceCache.getImage("movenext"));
        itemOutBuffer.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                outAdapter(event);
            }
        });
    }

    protected void enterAdapter(SelectionEvent event) {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    String warehouseId = (String) getField("WarehouseId").getValue();
                    //String comment = (String) getField("Comment").getValue();

                    Lot lot = (Lot) this.getAdObject();
                    lot.setOperator1(Env.getUserName());

                    LotManager lotManager = Framework.getService(LotManager.class);
                    Lot newLot = lotManager.moveInWarehouse(lot, warehouseId, null, Env.getSessionContext());
                    setAdObject(newLot);
                    UI.showInfo(Message.getString("wip.transfetstate_enter"));// 弹出提示框
                    refresh();
                }
            }
            txtLot.setFocus();
        } catch (Exception e) {
            logger.error("TransferStateSection : holdAdapter()", e);
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    protected void outAdapter(SelectionEvent event) {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    Lot lot = (Lot) this.getAdObject();
                    lot.setOperator1(Env.getUserName());
                    //String comment = (String) getField("Comment").getValue();

                    LotManager lotManager = Framework.getService(LotManager.class);
                    Lot newLot = lotManager.moveOutWarehouse(lot, Env.getSessionContext());
                    setAdObject(newLot);
                    UI.showInfo(Message.getString("wip.transfetstate_out"));// 弹出提示框
                    refresh();
                }
            }
            txtLot.setFocus();
        } catch (Exception e) {
            logger.error("TransferStateSection : holdAdapter()", e);
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
        itemForm = new TransferStateForm(composite, SWT.NONE, tab, mmng);
        return itemForm;
    }

    @Override
    public void refresh() {
        try {
            Lot lot = (Lot) this.getAdObject();
            if (lot != null && lot.getObjectRrn() != null) {
                ADManager entityManager = Framework.getService(ADManager.class);
                setAdObject(entityManager.getEntity(lot));
                itemEnterBuffer.setEnabled(true);
                itemOutBuffer.setEnabled(true);
                if (Lot.TRANSFERSTATE_READYTOIN.equals(lot.getTransferState())) {
                    itemEnterBuffer.setEnabled(true);
                    itemOutBuffer.setEnabled(false);
                } else if (Lot.TRANSFERSTATE_IN.equals(lot.getTransferState())) {
                    itemEnterBuffer.setEnabled(false);
                    itemOutBuffer.setEnabled(true);
                } else {
                    itemEnterBuffer.setEnabled(false);
                    itemOutBuffer.setEnabled(false);
                }
            } else {
                itemEnterBuffer.setEnabled(false);
                itemOutBuffer.setEnabled(false);
            }
            form.getMessageManager().removeAllMessages();
        } catch (Exception e1) {
            ExceptionHandlerManager.asyncHandleException(e1);
            return;
        }
        super.refresh();
    }

}
