package com.glory.mes.wip.lot.production.offline;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADQuery;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.client.SecurityManager;
import com.glory.framework.security.model.ADUser;
import com.glory.framework.security.model.ADUserGroup;
import com.glory.mes.prd.model.UserGroupStep;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.flow.LotFlowSection;
import com.glory.mes.wip.lot.flow.LotFlowTreeManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotProduction;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotProductionInputSection extends LotSection {

	private static final Logger logger = Logger.getLogger(LotProductionInputSection.class);
	protected LotProductionInputMediator lotMediator;
	protected MDSashForm sashForm;

	protected LotFlowTreeManager lotFlowtreemanager;
	protected LotFlowSection lotFlowSection;

	protected ListTableManager inputTableManager;
	protected LotProductionInputAddFrom addFrom;

	public StepState state;
	public Lot lot;

	public LotProductionInputSection() {
		super();
	}

	public LotProductionInputSection(LotFlowTreeManager manager, ADTable table) {
		super(table);
		this.lotFlowtreemanager = manager;
		createMeditor();
	}

	protected void createMeditor() {
		lotMediator = new LotProductionInputMediator(this);
	}

	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		final IMessageManager mmng = form.getMessageManager();

		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;

		sashForm = new MDSashForm(client, SWT.NULL);
		toolkit.adapt(sashForm, false, false);
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);
		lotFlowSection = new LotFlowSection(table, lotMediator, lotFlowtreemanager);
		lotFlowSection.createContents(form, sashForm);

		addFrom = new LotProductionInputAddFrom(sashForm, SWT.NULL, null, mmng);
		detailForms.add(addFrom);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, 8388864);
		createToolItemSave(tBar);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void createSectionTitle(Composite client) {
	}

	@Override
	public void initAdObject() {
		try {
			lot = (Lot) lotFlowSection.getAdObject();
			addFrom.setObject(lot);
			addFrom.setCurrentStepState(state);
			addFrom.loadFromObject();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	protected void saveAdapter() {
		form.getMessageManager().removeAllMessages();

		if (!checkPermissions()) {
			UI.showError(Message.getString("error.no_step_permission"));
			refresh();
			return;
		}
		try {
			List<Object> editorObjects = addFrom.getEditorObject();
			List<Object> delectObjects = addFrom.getDeleteObject();
			if (editorObjects != null) {
				for (Object editor : editorObjects) {
					LotProduction production = (LotProduction) editor;
					if (production != null && production.getSubQty() != null) {
						production.setLotId(lot.getLotId());
						production.setOperator1(Env.getSessionContext().getUserName());
						production.setMainQty(lot.getMainQty());
						production.setStepName(state.getName());
						ADManager adManager = Framework.getService(ADManager.class);
						adManager.saveEntity(production, Env.getSessionContext());
					}
				}
			}
			if (delectObjects != null) {
				for (Object object : delectObjects) {
					LotProduction production = (LotProduction) object;
					if (production.getObjectRrn() != null) {
						ADManager adManager = Framework.getService(ADManager.class);
						ADBase base = adManager.getEntity(production);
						adManager.deleteEntity(base, Env.getSessionContext());
					}
				}
			}
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("Error at LotProductionInputSection : saveAdapter" + e.getStackTrace());
			return;
		}
		form.getMessageManager().setAutoUpdate(true);
	}

	// 检查记录人的工步权限
	public boolean checkPermissions() {
		Boolean flag = false;
		try {
			SecurityManager securityManager = Framework.getService(SecurityManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			ADUser user = securityManager.getUserByUserName(Env.getOrgRrn(), Env.getUserName());
			user = (ADUser) adManager.getEntity(user);
			List<ADUserGroup> adUserGroups = user.getUserGroups();

			String whereClause = "stepName = '" + state.getStepName() + "'";
			List<UserGroupStep> query = adManager.getEntityList(Env.getOrgRrn(), UserGroupStep.class, ADQuery.maxCount,
					whereClause, null);
			if (query != null && query.size() > 0 && adUserGroups != null && adUserGroups.size() > 0) {
				for (UserGroupStep userGroupStep : query) {
					for (ADUserGroup adUserGroup : adUserGroups) {
						if (adUserGroup.getObjectRrn().equals(userGroupStep.getUserGroupRrn())) {
							flag = true;
						}
					}
				}
			}
		} catch (Exception e) {
			// TODO: exception
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return flag;
	}

	@Override
	public void refresh() {
		// TODO Auto-generated method stub
		form.getMessageManager().removeAllMessages();
		super.refresh();
		initAdObject();
	}

	public StepState getState() {
		return state;
	}

	public void setState(StepState state) {
		this.state = state;
	}

}
