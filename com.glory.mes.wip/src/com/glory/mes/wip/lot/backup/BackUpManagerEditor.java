package com.glory.mes.wip.lot.backup;


import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.AuthorityToolItem;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.LotProcedure;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.base.Objects;

public class BackUpManagerEditor extends GlcEditor { 
	
	private static final Logger logger = Logger.getLogger(BackUpManagerEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.backup.BackUpManagerEditor";

	protected static final String CONTROL_LOTID_ENTERPRESSED = "backUpInfo-lotId-EnterPressed";
	protected static final String CONTROL_CARRIERID_ENTERPRESSED = "backUpInfo-carrierId-EnterPressed";
	
	private static final String FIELD_BACKUPINFO = "backUpInfo";
	private static final String FIELD_PROCEDUREINFO = "procedureInfo";
	private static final String FIELD_COMMENT = "comment";

	private static final String BUTTON_BACKUP = "backUp";
	private static final String BUTTON_REFRESH = "refresh";

	protected CustomField backUpInfoField;
	protected CustomField procedureInfoField;
	protected EntityFormField commentField;
	protected TextField commentField1;
	protected CarrierLotCustomComposite carrierLotCustomComposite;
	protected FlowCustomComposite flowCustomComposite;
	
	protected AuthorityToolItem itemBackUp;
	
	public HeaderText txtLotId, txtCarrierId;
	protected Boolean isCaseSensitive;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		//注销默认回车事件
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_LOTID_ENTERPRESSED));
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED));
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_LOTID_ENTERPRESSED), this::lotIdEnterpressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED), this::carrierIdEnterpressed);
		
		
		backUpInfoField = form.getFieldByControlId(FIELD_BACKUPINFO, CustomField.class);
		procedureInfoField = form.getFieldByControlId(FIELD_PROCEDUREINFO, CustomField.class);
		commentField = form.getFieldByControlId(FIELD_COMMENT, EntityFormField.class);
		commentField1 = commentField.getFieldByControlId(FIELD_COMMENT, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_BACKUP), this::backUpAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		carrierLotCustomComposite = (CarrierLotCustomComposite) backUpInfoField.getCustomComposite();
		flowCustomComposite = (FlowCustomComposite) procedureInfoField.getCustomComposite();
		
		txtLotId = carrierLotCustomComposite.getTxtLotId();
		txtCarrierId = carrierLotCustomComposite.getTxtCarrierId();
		
		itemBackUp = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_BACKUP);
		itemBackUp.setEnabled(false);
	}

	protected void lotIdEnterpressed(Object obj) {
		String lotId = txtLotId.getText();
		if (!StringUtil.isEmpty(lotId)) {
			if (!isLotIdCaseSensitive()) {
				lotId = lotId.toUpperCase();
			}
			getLotByLotId(lotId);
		}
	}
	
	protected void carrierIdEnterpressed(Object obj) {
		String carrierId = txtCarrierId.getText();
		if (!StringUtil.isEmpty(carrierId)) {
			getLotsByCarrierId(carrierId);
		}
	}
	
	public void getLotByLotId(String lotId) {
		try {
			Lot lot = searchLot(lotId);
			if (lot != null) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.add(lot);
				
				carrierLotCustomComposite.getLotTableManager().setInput(lots);
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				txtLotId.setText(lotId);
				// 默认全选
				if (carrierLotCustomComposite.isCheckFlag()) {
					carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
				}
				carrierLotCustomComposite.getLotTableManager().refresh();
				flowCustomComposite.loadFlowTreeByLot(lot);
				refresh();
				carrierLotCustomComposite.loadComponentUnits(lot);
				if(!StringUtil.isEmpty(lot.getDurable())) {
					txtCarrierId.setText(lot.getDurable());
				}else {
					txtCarrierId.setText("");
				}	
				txtLotId.focusing();
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.setText(lotId);
				flowCustomComposite.loadFlowTreeByLot(null);
				
				carrierLotCustomComposite.getLotTableManager().setSelection(new StructuredSelection(new Object[] {new Lot()}));
				
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtLotId.warning();
			}
			refreshAdapter(lot);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isNotEmpty(lots)) {
					// Load Current Step
					Lot lotInfo = searchLot(lots.get(0).getLotId());
					
					carrierLotCustomComposite.getLotTableManager().setInput(lots);
					// 默认全选
					if (carrierLotCustomComposite.isCheckFlag()) {
						for (Lot lot : lots) {
							carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
						}
					}
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					carrierLotCustomComposite.getLotTableManager().refresh();
					
					if (lots != null && lots.size() > 0) {
						flowCustomComposite.loadFlowTreeByLot(lotInfo);
						refresh();
					}
					
					carrierLotCustomComposite.loadComponentUnits(lots);	
					txtLotId.setText(lots.get(0).getLotId());
					txtCarrierId.focusing();
					refreshAdapter(lotInfo);
				} else {
					carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
					carrierLotCustomComposite.getLotTableManager().refresh();
				}
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				flowCustomComposite.loadFlowTreeByLot(null);
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtCarrierId.warning();
			}
			
			if (carrierLotCustomComposite.getLotDetailsForm() != null) {
				carrierLotCustomComposite.getLotDetailsForm().setObject(new Lot());
				carrierLotCustomComposite.getLotDetailsForm().loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public Lot searchLot(String lotId) {
		try {
			//按线别检查批次
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	private void backUpAdapter(Object object) {
		try {
			if (commentField.validate()) {
				Lot lot = LotProviderEntry.getLot(txtLotId.getText());
				LotManager lotManager = Framework.getService(LotManager.class);
				PrdManager prdManager = Framework.getService(PrdManager.class);
				String operator = Env.getUserName();
				if (itemBackUp.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
					operator = (String) itemBackUp.getData(LotAction.ACTION_TYPE_OPERATOR);
				}
				lot.setOperator1(operator);
				
				ReworkState reworkState = prdManager.getCurrentReworkState(lot.getProcessInstanceRrn());
				if (reworkState != null) {	
					String procedureStatePath = lot.getStepStack().substring(0, lot.getStepStack().length() -1);
					procedureStatePath = procedureStatePath.substring(0, procedureStatePath.lastIndexOf("/") + 1);
					ProcessDefinition procedure = prdManager.getLotProcedure(lot.getObjectRrn(), procedureStatePath, LotProcedure.TYPE_CHANGE, true);
					if(procedure == null) {
						procedure = new Procedure();
						procedure.setOrgRrn(lot.getOrgRrn());
						procedure.setName(lot.getProcedureName());
						procedure.setVersion(lot.getProcedureVersion());
					}
					
					PrdQueryAction queryAction = PrdQueryAction.newIntance();
					queryAction.setCopyNode(true);
					List<Node> nodes = prdManager.getProcessDefinitionChildern(procedure, queryAction);
					StepState currentStep = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
					Optional<StepState> firstStep = nodes.stream().filter(node -> node instanceof StepState).map(stepState -> (StepState)stepState).findFirst();
					if(Objects.equal(currentStep.getName(), firstStep.get().getName())) {
						UI.showError(Message.getString("wip.first_step_cannot_backup"));
						return;
					}
				}
				
	            if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
					return;
				}
	            
	            LotAction action = new LotAction();
	            action.setActionComment(commentField1.getText());
	            
	            lot = lotManager.backUpLot(lot, action, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.backup_successed"));// 弹出提示框
				commentField1.setText("");
				refresh();
				refreshAdapter(lot);
				flowCustomComposite.loadFlowTreeByLot(lot);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void refreshAdapter(Object object) {
		try {
			Lot lot = new Lot();
			if (object instanceof Lot) {
				lot = (Lot) object;
			} else {
				lot = LotProviderEntry.getLot(txtLotId.getText());
			}
			if (lot != null) {
				statusChanged(lot.getState());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh() {
		if (txtLotId != null) {
			txtLotId.selectAll();
		}
		if (txtCarrierId != null) {
			txtCarrierId.selectAll();
		}
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
	public void statusChanged(String newStatus){
		if (newStatus != null && !"".equals(newStatus.trim())) {
			if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus)) {
				itemBackUp.setEnabled(true);
			} else {
				itemBackUp.setEnabled(false);
			}
		} else {
			itemBackUp.setEnabled(false);
		}
	}

}