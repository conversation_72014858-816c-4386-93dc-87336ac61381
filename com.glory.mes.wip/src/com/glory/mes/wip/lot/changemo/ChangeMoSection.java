package com.glory.mes.wip.lot.changemo;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class ChangeMoSection extends LotSection {

	public static final String KEY_CHANGE_WO = "changeWo";
	
	protected AuthorityToolItem change; 
	protected ChangeMoForm changeMoForm;
	
	public ChangeMoSection() {
		super();
	}

	public ChangeMoSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.changemo_section"));
		initAdObject();
	}
	
	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		changeMoForm = new ChangeMoForm(composite, SWT.NONE, tab, mmng);
		return changeMoForm;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChange(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	public void createToolItemChange(ToolBar tBar) {
		change  = new AuthorityToolItem(tBar, SWT.NULL, getTable().getAuthorityKey() + "." + KEY_CHANGE_WO);
		change.setAuthEventAdaptor(this::changeAdapter);
		change.setText(Message.getString("wip.changemo_change"));
		change.setImage(SWTResourceCache.getImage("modify"));
//		change.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent e) {
//				changeAdapter();
//			}
//		});
	}
	
	public void changeAdapter(SelectionEvent event) {
		try {
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				if (null == getField(ChangeMoForm.FIELD_CHANGEMO).getData()) {
					UI.showInfo(Message.getString("wip.changemo_not_null"));
					return;
				}
				
				WorkOrder schedule = (WorkOrder)getField(ChangeMoForm.FIELD_CHANGEMO).getData();
				Lot lot = (Lot)this.getAdObject();
				LotManager lotManager = Framework.getService(LotManager.class);
				
				SessionContext sc = Env.getSessionContext();
				if (change.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
					sc.setUserName((String) change.getData(LotAction.ACTION_TYPE_OPERATOR));
				}
				
				lotManager.changeLotWo(lot.getLotId(), schedule.getDocId(), sc);
				UI.showInfo(Message.getString("wip.changemo_successed"));// 弹出提示框
				refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));				
				changeMoForm.setObject((Lot)getAdObject());
				changeMoForm.refresh();
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
	}
}
