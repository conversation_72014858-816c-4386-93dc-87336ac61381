package com.glory.mes.wip.lot.defect;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADOwnerRefList;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.OwnerCodeTableComposite;
import com.glory.framework.base.entitymanager.forms.OwnerCodeTableComposite.OwnerCode;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;


public class DefectQtyComposite extends Composite {
	
	protected Lot lot;
	protected TableViewerManager tableManager;
	protected ManagedForm mform;
	protected OwnerCodeTableComposite ownerCodeTableComposite;
	protected String referenceName;
	
	public DefectQtyComposite(Composite parent, int style, Lot lot, String referenceName) {
		super(parent, style);
		this.lot = lot;
		this.setLayoutData(new GridData(GridData.FILL_BOTH));
		this.referenceName = referenceName;
		createForm();
	}
	
	public void createForm() {
		FormToolkit toolkit = new FormToolkit(getDisplay());
		
		GridLayout layout = new GridLayout(1, false);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		
		ScrolledForm sform = toolkit.createScrolledForm(this);
		sform.setLayoutData(new GridData(GridData.FILL_BOTH));
		sform.setLayout(layout);
		mform = new ManagedForm(toolkit, sform);
		
		Composite body = sform.getBody();
		layout = new GridLayout();
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createViewerComponent(body, toolkit);
	}
	
	protected void createViewerComponent(Composite composite, FormToolkit toolkit) {
		//"缺陷码大类;缺陷码;数量"
		String[] labels = Message.getString("wip.qty_defect_code_label").split(";");
		ownerCodeTableComposite = new OwnerCodeTableComposite(composite, GridData.FILL_BOTH, referenceName, 1, 2, labels);
		//定义动态表
		ADTable ownerCodeTable = ownerCodeTableComposite.createADTable();
		ADField adField = new ADField();
		adField.setIsDisplay(true);
		adField.setIsEditable(true);
		adField.setIsMain(true);
		adField.setName("text3");
		adField.setDisplayType(FieldType.TEXT);
		adField.setDataType(DataType.DOUBLE);
		adField.setLabel(ownerCodeTableComposite.getLabel(3));
		adField.setLabel_zh(ownerCodeTableComposite.getLabel(3));
		ownerCodeTable.getFields().add(adField);
		ownerCodeTableComposite.setOwnerCodeTable(ownerCodeTable);
		ownerCodeTableComposite.setEditor(true);
		ownerCodeTableComposite.setMulti(true);
		
		ownerCodeTableComposite.createForm();
    	
		Composite scrapComp = toolkit.createComposite(ownerCodeTableComposite, SWT.NULL);
    	scrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
    	GridLayout gl = new GridLayout(5, false);
    	scrapComp.setLayout(gl);
    	
    	//获取缺陷码列表
    	tableManager = ownerCodeTableComposite.getTableManager();
	}
	
	public boolean validate() {
		IMessageManager mmng = mform.getMessageManager();
		mmng.removeAllMessages();
		boolean validateFlag = true;
		return validateFlag;
	}
	
	public List<ProcessUnit> getDedectUnits() {
		List<ProcessUnit> defectUnits = new ArrayList<ProcessUnit>();
		List<OwnerCode> ownerCodes = ownerCodeTableComposite.getCheckedDatas();
		for (OwnerCode ownerCode : ownerCodes) {
			QtyUnit qtyUnit = new QtyUnit();
			qtyUnit.setEquipmentId(lot.getEquipmentId());
			if(ownerCode.getText3() != null) {
				qtyUnit.setMainQty(new BigDecimal(ownerCode.getText3()));
			}
			qtyUnit.setActionCode(ownerCode.getValue());
			defectUnits.add(qtyUnit);
		}
		return defectUnits;
	}
	
	//获取code码对应的对象
	public ADOwnerRefList getActionCodeObject(String key) {
		ADOwnerRefList adOwnerRefList = new ADOwnerRefList();
		List<OwnerCode> ownerCodes = ownerCodeTableComposite.getDatas();
		for(OwnerCode ownerCode : ownerCodes) {
			if(ownerCode.getValue().equals(key)) {
				adOwnerRefList = ownerCode.getData();
			}
		}
		return adOwnerRefList;
		
	}
	
	//获取code码对应上一级代码
	public ADOwnerRefList getActionCodeGroup(String key) {
		ADOwnerRefList groupOwnerRefList = new ADOwnerRefList();
		List<OwnerCode> ownerCodes = ownerCodeTableComposite.getDatas();
		for(OwnerCode ownerCode : ownerCodes) {
			if(ownerCode.getValue().equals(key)) {
				groupOwnerRefList = ownerCode.getGroupData();
			}
		}
		return groupOwnerRefList;
	}
	
	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public Lot getLot() {
		return lot;
	}
}
