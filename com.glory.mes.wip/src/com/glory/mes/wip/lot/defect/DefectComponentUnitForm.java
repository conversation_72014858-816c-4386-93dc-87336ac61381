package com.glory.mes.wip.lot.defect;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADOwnerRefList;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.OwnerCodeComboComposite;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class DefectComponentUnitForm extends Composite {

	private static final Logger logger = Logger.getLogger(DefectComponentUnitForm.class);

    protected static String HEADER_DEFECT_CODE = Message.getString("wip.defect_code_label");
    public static final String TABLE_DEFECT= "WIPLotDefectunit";

    protected Lot lot;
    protected Text commentText;
    protected ManagedForm mform;
    protected CheckBoxTableViewerManager tableManager;
    protected OwnerCodeComboComposite codeComboComposite;
    protected String referenceName;
    protected Map<ComponentUnit, LotAction> componentActions = new HashMap<ComponentUnit, LotAction>();
    
    public DefectComponentUnitForm(Composite parent, int style, Lot lot,String referenceName) {
        super(parent, style);
        this.setLot(lot);
        this.referenceName = referenceName;
        try {
            LotManager manager = Framework.getService(LotManager.class);
            lot = manager.getLotWithComponent(lot.getObjectRrn());
            this.setLot(lot);
            createForm();
        } catch (Exception e) {
            logger.error("Constructor in ScrapIdentifiedLotDialog", e);
        }
    }

    public void createForm() {
        FormToolkit toolkit = new FormToolkit(getDisplay());

        this.setLayoutData(new GridData(GridData.FILL_BOTH));
        GridLayout layout = new GridLayout(1, false);
        layout.verticalSpacing = 0;
        layout.horizontalSpacing = 0;
        layout.marginWidth = 0;
        layout.marginHeight = 0;
        setLayout(layout);

        ScrolledForm sform = toolkit.createScrolledForm(this);
        sform.setLayoutData(new GridData(GridData.FILL_BOTH));
        mform = new ManagedForm(toolkit, sform);

        Composite body = sform.getBody();
        layout = new GridLayout();
        body.setLayout(layout);
        body.setLayoutData(new GridData(GridData.FILL_BOTH));

        createTableComponent(body, toolkit);
        createLowerComponent(body, toolkit);
    }

    protected void createTableComponent(Composite composite, FormToolkit toolkit) {
        Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_DEFECT);
			
			tableManager = new CheckBoxTableViewerManager(adTable);
			tableManager.newViewer(tableContainer);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

        List<ProcessUnit> subProcessUnit = getLot().getSubProcessUnit();
        tableManager.setInput(subProcessUnit);
        
        tableManager.addICheckChangedListener(new ICheckChangedListener() {
			
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (checked) {
					boolean flag = true;
					//获取最低一级decombox值
					String comboScrapCode = codeComboComposite.getValue();
					if(comboScrapCode == null || "".equals(comboScrapCode.trim())){
						flag = false;
						UI.getActiveShell().getDisplay().asyncExec(new Runnable() {
		                    @Override
		                    public void run() {
		                    	UI.showWarning(String.format(Message.getString("wip.defect_code_required"),
										HEADER_DEFECT_CODE));
								for (Object object : eventObjects) {
									ComponentUnit component = (ComponentUnit) object;
									tableManager.unCheckObject(object);
									component.setActionCode("");
									componentActions.remove(component);
								}
		                    }
		                });
					}
					for (Object object : eventObjects) {
						ComponentUnit component = (ComponentUnit) object;
						if (flag) {
							component.setActionCode(comboScrapCode);
							LotAction action = new LotAction();
							action.setActionCode(comboScrapCode);
							action.setActionCodeObject(codeComboComposite.getData());
							action.setActionCodeGroup(codeComboComposite.getGroupValue());
							componentActions.put(component, action);
						}else {
							tableManager.unCheckObject(object);
							component.setActionCode("");
							componentActions.remove(component);
						}
					}
				}else {
					for (Object object : eventObjects) {
						((ComponentUnit)object).setActionCode("");
					}
				}
			}
		});
    }

    protected void createLowerComponent(Composite composite, FormToolkit toolkit) {
    	//"缺陷码大类;缺陷码"
    	String[] labels = Message.getString("wip.comp_defect_code_label").split(";");
    	codeComboComposite = new OwnerCodeComboComposite(composite, SWT.NULL, null, referenceName, 1, 2, labels);
    	codeComboComposite.createForm();
    	codeComboComposite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
    	GridLayout gl = new GridLayout(5, false);
    	codeComboComposite.setLayout(gl);
    	
    }

//    public boolean validate() {
//        IMessageManager mmng = mform.getMessageManager();
//        mmng.removeAllMessages();
//        boolean validateFlag = true;
//        boolean sourceIsNull = GenericValidator.isBlankOrNull(codeComboComposite.getValue());
//        if (sourceIsNull) {
//            mmng.addMessage(HEADER_DEFECT_CODE,
//                    String.format(Message.getString("common.ismandatry"), HEADER_DEFECT_CODE), null,
//                    IMessageProvider.ERROR, null);
//            validateFlag = false;
//        }
//        return validateFlag;
//    }

    public List<ProcessUnit> getDefectUnits() {
        List<ProcessUnit> defectUnits = new ArrayList<ProcessUnit>();
        List<Object> objs = tableManager.getCheckedObject();
        for (Object obj : objs) {
            ProcessUnit unit = (ProcessUnit) obj;
            unit.setEquipmentId(lot.getEquipmentId());
            defectUnits.add(unit);
        }
        return defectUnits;
    }

    public LotAction getScrapAction() {
        LotAction lotAction = new LotAction();
        lotAction.setActionComment(commentText.getText());
        return lotAction;
    }
    
    
    //获取code码对应的对象
  	public ADOwnerRefList getActionCodeObject(ComponentUnit componentUnit) {
  		ADOwnerRefList adOwnerRefList = new ADOwnerRefList();
  		LotAction lotAction = componentActions.get(componentUnit);
  		if(lotAction != null) {
  			adOwnerRefList = (ADOwnerRefList) codeComboComposite.getData();
  		}
  		return adOwnerRefList;
  	}
  	
  	//获取code码对应上一级代码
  	public String getActionCodeGroup(ComponentUnit componentUnit) {
  		String groupKey = "";
  		LotAction lotAction = componentActions.get(componentUnit);
  		//comp已经有defect，就取已有的
  		if(lotAction != null) {
  			groupKey = lotAction.getActionCodeGroup();
  		}
  		return groupKey;
  	}
  	
    public void setLot(Lot lot) {
        this.lot = lot;
    }

    public Lot getLot() {
        return lot;
    }

}
