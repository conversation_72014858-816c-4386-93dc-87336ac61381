package com.glory.mes.wip.lot.ship;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class ShipSection extends LotSection {
	
	public static final String KEY_SHIP = "ship";
	
	protected AuthorityToolItem itemShip;
	protected ShipForm shipForm;
	
	public ShipSection() {
		super();
	}
	
	public ShipSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.ship_sectiontitle"));
		initAdObject();
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		if (adObject != null && adObject.getObjectRrn() != null) {
			for(IForm detailForm : this.getDetailForms()){
				((ShipForm)detailForm).addLot((Lot)adObject);
			}
		}
		refresh();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemShip(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemShip(ToolBar tBar) {
		itemShip = new AuthorityToolItem(tBar, SWT.PUSH,getTable().getAuthorityKey() + "." + KEY_SHIP);
		itemShip.setText(Message.getString("wip.ship"));
		itemShip.setImage(SWTResourceCache.getImage("ship-lot"));
		itemShip.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				shipAdapter(event);
			}
		});
	}

	protected void shipAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if(((ShipForm)detailForm).getLots().size() != 0){
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				} else {
					UI.showError(Message.getString("wip.ship_error_nolot_toship"));
					saveFlag = false;
				}
			}
			if (saveFlag) {
				for (IForm detailForm : getDetailForms()) {
					LotManager lotManager = Framework.getService(LotManager.class);
					LotAction lotAction = ((ShipForm) detailForm).getLotAction();
					List<Lot> shipLots = new ArrayList<Lot>();
					for(Lot lot : ((ShipForm) detailForm).getLots()) {
						lot.setOperator1(Env.getUserName());
						shipLots.add(lot);
					}
					lotManager.shipLot(shipLots, lotAction, Env.getSessionContext());
				}
				UI.showInfo(Message.getString("wip.ship_success"));// 弹出提示框
				for (IForm detailForm : getDetailForms()) {
					((ShipForm)detailForm).clearShipLot();
				}
				refresh();
				setFocus();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		shipForm = new ShipForm(composite, SWT.NONE, tab, mmng);
		return shipForm;
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				super.setAdObject(entityManager.getEntity(adBase));
				shipForm.setObject((Lot) getAdObject());//重置页面信息
				shipForm.loadFromObject();
				txtLot.setText(((Lot) getAdObject()).getLotId());//重置lotId
				shipForm.refresh();
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		super.refresh();
	}
	

}
