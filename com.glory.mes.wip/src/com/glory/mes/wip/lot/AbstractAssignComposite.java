package com.glory.mes.wip.lot;

import org.apache.log4j.Logger;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public abstract class AbstractAssignComposite extends Composite {
	
	private static final Logger logger = Logger.getLogger(AbstractAssignComposite.class);
	
	public Lot sourceLot;
	public Carrier targetCarrier;

	public AbstractAssignComposite(Composite parent, int style) {
		super(parent, style);
	}
	
	public void createForm() {
		try {
			GridLayout layout = new GridLayout(3, false);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(GridData.FILL_BOTH));
						
			createAssignComposite();
		} catch (Exception e) {
			logger.error("AbstractAssignComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected abstract void createAssignComposite();
	
	public Carrier searchCarrier(String carrierId, boolean isValid, boolean isThrowException) {
		try {
			if (!StringUtil.isEmpty(carrierId)) {
				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, isThrowException);
				if (isValid) {
					//进行有效性检查
					durableManager.checkCarrierAvailable(Env.getSessionContext(), targetCarrier);
					if (!targetCarrier.getIsAvailable()) {
						UI.showWarning(Message.getString(targetCarrier.getMessage()));
						return null;
					}
				}
				return targetCarrier;
			}
		} catch(Exception e) {
			logger.error("AbstractAssignComposite searchCarrier:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot sourceLot = LotProviderEntry.getLot(lotId);
			if (sourceLot == null) {
				return null;
			}
			
			if (ComponentUnit.getUnitType().equals(sourceLot.getSubUnitType())) {
				LotManager lotManager = Framework.getService(LotManager.class);
				sourceLot = lotManager.getLotWithComponentOrderByPosition(sourceLot.getObjectRrn(), true);
			}
			return sourceLot;
		} catch (Exception e) {
			logger.warn("AbstractAssignComposite searchLot(): Lot isn' t exsited!");
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	public void refresh() {
		setSourceLot(null);
		setTargetCarrier(null);
	}

	public Lot getSourceLot() {
		return sourceLot;
	}

	public void setSourceLot(Lot sourceLot) {
		this.sourceLot = sourceLot;
	}

	public Carrier getTargetCarrier() {
		return targetCarrier;
	}

	public void setTargetCarrier(Carrier targetCarrier) {
		this.targetCarrier = targetCarrier;
	}
	
}
