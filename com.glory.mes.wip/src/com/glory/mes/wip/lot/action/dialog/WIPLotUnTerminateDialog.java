package com.glory.mes.wip.lot.action.dialog;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class WIPLotUnTerminateDialog extends LotActionDialog{
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String ADFORM_NAME = "WIPLotActionUnTerminateDialog";
	
	private static final String FIELD_LOTUNTERMINATE = "lotUnTerminateTable";
	private static final String FIELD_LOTACTIONUNCODE = "lotUnTerminateAction";
	private static final String FIELD_ACTIONCODE = "actionCode";
	private static final String FIELD_COMMENT = "actionComment";

	private ListTableManagerField lotUnTerminateListTable;
	private EntityFormField lotUnActionCodeEntityForm;
	private RefTableField actionCodeField;
	private TextField commentField;
	
	private ListTableManager lotUnTerminateTableManager;
	
	
	public WIPLotUnTerminateDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotUnTerminateListTable = form.getFieldByControlId(FIELD_LOTUNTERMINATE, ListTableManagerField.class);
		lotUnActionCodeEntityForm = form.getFieldByControlId(FIELD_LOTACTIONUNCODE, EntityFormField.class);
		actionCodeField = lotUnActionCodeEntityForm.getFieldByControlId(FIELD_ACTIONCODE, RefTableField.class);
		commentField = lotUnActionCodeEntityForm.getFieldByControlId(FIELD_COMMENT, TextField.class);
		
		
		lotUnTerminateTableManager = lotUnTerminateListTable.getListTableManager();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		List<Lot> lots = getLotList();
		lotUnTerminateTableManager.setInput(lots);
	}
	
	@Override
	protected void okPressed() {
		try {
			if (!lotUnActionCodeEntityForm.validate()) {
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			LotAction action = new LotAction();
			action.setActionCode(actionCodeField.getText());
			action.setActionComment(commentField.getText());
			
			List<Lot> batchObject = (List<Lot>) lotUnTerminateTableManager.getInput();
			List<Lot> batchLots = new ArrayList<Lot>();
			if (batchObject.size() > 0) {
				for (int i = 0; i < batchObject.size(); i++) {
					Lot lot = batchObject.get(i);
					lot.setOperator1(Env.getSessionContext().getTransUser());
					batchLots.add(lot);
				}
			}
			for (Lot lot : batchLots) {
				lot.setOperator1(getOperator());
				lotManager.unTerminate(lot, action, Env.getSessionContext());
			}
			UI.showInfo(Message.getString("wip.unterminate_success"));// 弹出提示框
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			for(Lot lot : getLotList()) {
				if (!LotStateMachine.TRANS_TERMLOT.equalsIgnoreCase(lot.getPreTransType())) {
					UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}
