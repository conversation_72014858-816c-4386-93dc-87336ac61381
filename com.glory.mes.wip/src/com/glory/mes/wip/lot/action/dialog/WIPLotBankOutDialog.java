
package com.glory.mes.wip.lot.action.dialog;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.wip.action.LotBankAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotBankOutDialog extends LotActionDialog{
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String ADFORM_NAME = "WIPLotActionBankOutDialog";
	
	private static final String FIELD_LOTBANKOUT = "lotBankOutTable";
	private static final String FIELD_BANKOUTWAREHOUSE = "lotBankOutWarehouse";
	
	private static final String FIELD_WAREHOUSEID = "warehouseId";
	private static final String FIELD_LOCATORID = "locatorId";
	private static final String FIELD_LOTCOMMENT = "lotComment";
	
	private ListTableManagerField lotBankOutListTable;
	private EntityFormField lotBankOutWarehouseEntityForm;
	private RefTableField warehouseIdField;
	private RefTableField locatorIdField;
	private TextField lotCommentField;
	
	private ListTableManager lotBankOutTableManager;
	
	public WIPLotBankOutDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotBankOutListTable = form.getFieldByControlId(FIELD_LOTBANKOUT, ListTableManagerField.class);
		lotBankOutWarehouseEntityForm = form.getFieldByControlId(FIELD_BANKOUTWAREHOUSE, EntityFormField.class);
		warehouseIdField = lotBankOutWarehouseEntityForm.getFieldByControlId(FIELD_WAREHOUSEID, RefTableField.class);
		locatorIdField = lotBankOutWarehouseEntityForm.getFieldByControlId(FIELD_LOCATORID, RefTableField.class);
		lotCommentField = lotBankOutWarehouseEntityForm.getFieldByControlId(FIELD_LOTCOMMENT, TextField.class);
		
		
		lotBankOutTableManager = lotBankOutListTable.getListTableManager();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		List<Lot> lots = getLotList();
		lotBankOutTableManager.setInput(lots);
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton bankOut = this.createSquareButton(parent, 3, Message.getString("wip.bank_out"), false, null);
		SquareButton moveBank = this.createSquareButton(parent, 2, Message.getString("wip.bank_move"), false, null);
		SquareButton cancel = this.createSquareButton(parent, 1, Message.getString(ExceptionBundle.bundle.CommonCancel()), false, "Gray");
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
		
		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(moveBank, -12, 16384);
		fd.bottom = new FormAttachment(100, -15);
		bankOut.setLayoutData(fd);
		
		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, 16384);
		fd.bottom = new FormAttachment(100, -15);
		moveBank.setLayoutData(fd);
		
		bankOut.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				try {
					bankOutAdapter();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
		
		moveBank.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				try {
					moveBankAdapter();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
		
	}
	
	protected void bankOutAdapter() {
		try {	
			if (!lotBankOutWarehouseEntityForm.validate()) {
				return;
			}
			List<Lot> selectLots = (List<Lot>) lotBankOutTableManager.getInput();
			
			String lotComment = (String) lotCommentField.getValue();
			
			LotBankAction lotBankAction = new LotBankAction();
			lotBankAction.setActionComment(lotComment);
			
			List<Lot> lots = selectLots.stream().map(selectLot -> {
				Lot lot = (Lot) selectLot;
				lot.setWarehouseId(null);
				lot.setLocatorId(null);
				lot.setOperator1(getOperator());
				return lot;
			}).collect(Collectors.toList());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.bankOut(lots, lotBankAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.bank_out_successed"));// 弹出提示框
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void moveBankAdapter() {
		try {		
			if (!lotBankOutWarehouseEntityForm.validate()) {
				return;
			}
			List<Lot> selectLots = (List<Lot>) lotBankOutTableManager.getInput();

			String warehouseId = (String) warehouseIdField.getValue();
			String locatorId = (String) locatorIdField.getValue();
			String lotComment = (String) lotCommentField.getValue();
			
			LotBankAction lotBankAction = new LotBankAction();
			lotBankAction.setActionComment(lotComment);
			lotBankAction.setWarehouseId(warehouseId);
			lotBankAction.setLocatorId(locatorId);
			
			List<Lot> lots = selectLots.stream().map(selectLot -> {
				Lot lot = (Lot) selectLot;
				lot.setWarehouseId(warehouseId);
				lot.setLocatorId(locatorId);
				lot.setOperator1(getOperator());
				return lot;
			}).collect(Collectors.toList());
			
			ADManager adManager = Framework.getService(ADManager.class);
			// 判断仓库下库位是否存在
			List<Storage> storagelist = adManager.getEntityList(Env.getOrgRrn(), Storage.class, Env.getMaxResult(),
					" warehouseId = '" + lotBankAction.getWarehouseId() + "' AND name = '" + lotBankAction.getLocatorId() + "'", null);			
			// 如果能找到，对比录入库位ID
			if (!StringUtil.isEmpty(locatorId) && CollectionUtils.isEmpty(storagelist)) {
				UI.showError(Message.getString("mm.storage_no_exist"));// 弹出提示框
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.moveBank(lots, lotBankAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.bank_move_successed"));// 弹出提示框
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			for(Lot lot : getLotList()) {
				if (!LotStateMachine.STATE_BANK.equals(lot.getState())) {
					UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}