package com.glory.mes.wip.lot.action.dialog;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotUnScrapDialog extends LotActionDialog {
	private static final Logger logger = Logger.getLogger(WIPLotUnScrapDialog.class);

	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String FIELD_UNSCRAP_LIST = "unScrapList";
	private static final String FIELD_UNSCRAP_ACTION = "unScrapAction";
	
	public static final String AD_FROM_NAME_UNSCRAPQTY = "WIPLotActionUnScrapQtyDialog";
	public static final String AD_FROM_NAME_UNSCRAPCOMP = "WIPLotActionUnScrapCompDialog";
	
	protected static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");

	protected ListTableManagerField unScrapListField;
	protected EntityFormField unScrapActionField;
	protected ToolItem itemTrackIn;
	protected CheckBoxFixEditorTableManager checkBoxTableViewerManager;
	
	protected List<Lot> lots;
	protected List<LotScrap> lotScraps;

	// 是否按片报废
	private boolean isUnScrapComponent = false;
	// 是否按批次数量报废
	private boolean isUnScrapLot = false;
	// 是否有子数量
    private boolean isUnScrapQty = false;

	public WIPLotUnScrapDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
		this.eventId = LotStateMachine.TRANS_UNTERMLOT;
	}

	@Override
	public void initADFromName() throws Exception {
		if(CollectionUtils.isNotEmpty(getLotList())) {
			ADManager adManager = Framework.getService(ADManager.class);
			// 不允许按片、按批次同时操作
			boolean isUnScrapComponent = false;
			boolean isUnScrapLot = false;
			boolean isUnScrapSubQty = false;
			boolean isUnScrapMainQty = false;
			// 判断是否选择批次及，是否选择多个批次
			// 查询选择批次报废信息
			StringBuffer whereClause = new StringBuffer("lotRrn in (");
			for (int i = 0; i < getLotList().size(); i++) {
				if (i + 1 == getLotList().size()) {
					whereClause.append(getLotList().get(i).getObjectRrn() + ")");
				} else {
					whereClause.append(getLotList().get(i).getObjectRrn() + ",");
				}
			}
			lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class, Integer.MAX_VALUE, whereClause.toString(), "lotId desc");
			Set<String> lotIds = Sets.newHashSet();
			for (LotScrap lotScrap : lotScraps) {
				if (lotScrap.getComponentId() != null) {
					isUnScrapComponent = true;
				} else {
					isUnScrapLot = true;
				}

				// 判断是否主数量子数量同时反报废
				if (lotScrap.getSubQty() == null || BigDecimal.ZERO.compareTo(lotScrap.getSubQty()) == 0) {
					isUnScrapMainQty = true;
				} else {
					isUnScrapSubQty = true;
				}

				lotIds.add(lotScrap.getLotId());
			}
			// 判断是否混片qty反报废
			if (isUnScrapComponent && isUnScrapLot) {
				throw new ClientException("wip.unscrap_new_panel_lot_not_allow");
			}
			if (isUnScrapMainQty && isUnScrapSubQty) {
				throw new ClientException("wip.unscrap_main_sub_not_allow");
			}
			// 1, 找到合适的ADForm
			String adFormName = AD_FROM_NAME_UNSCRAPQTY;
			if (ComponentUnit.class.getSimpleName().equals(getLotList().get(0).getSubUnitType())) {
				adFormName = AD_FROM_NAME_UNSCRAPCOMP;
			}
			this.adFormName = adFormName;		
		}
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		try {
			super.createFormAction(form);
			initLot();
			ADManager adManager = Framework.getService(ADManager.class);

			unScrapListField = form.getFieldByControlId(FIELD_UNSCRAP_LIST, ListTableManagerField.class);
			unScrapActionField = form.getFieldByControlId(FIELD_UNSCRAP_ACTION, EntityFormField.class);
			unScrapActionField.setValue(new LotScrap());
			unScrapActionField.refresh();

			if (CollectionUtils.isNotEmpty(lots)) {
				//如果已找到批次报废信息则不进行重复查询
				if(CollectionUtils.isEmpty(lotScraps)) {
					StringBuffer whereClause = new StringBuffer("lotRrn in (");
					for (int i = 0; i < lots.size(); i++) {
						if (i + 1 == lots.size()) {
							whereClause.append(lots.get(i).getObjectRrn() + ")");
						} else {
							whereClause.append(lots.get(i).getObjectRrn() + ",");
						}
					}
					lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class, Integer.MAX_VALUE, whereClause.toString(), "lotId desc");
				}
				if(CollectionUtils.isNotEmpty(lotScraps)) {
					for (LotScrap lotScrap : lotScraps) {
						//判断是否片子数量报废
						if (!(lotScrap.getSubQty() == null) && !(BigDecimal.ZERO.compareTo(lotScrap.getSubQty()) == 0)) {
							isUnScrapQty = true;
						}
					}			
				}

				ListTableManager listTableManager = unScrapListField.getListTableManager();
				checkBoxTableViewerManager = (CheckBoxFixEditorTableManager)listTableManager.getTableManager();
				checkBoxTableViewerManager.addICheckChangedListener(checkChangedListener);
				// 写入已报废信息
				setTableList(form);
				
			}
		} catch (Exception e) {
			logger.error("WIPLotUnScrapDialog : Init tablelist", e);
		}
	}
	
	ICheckChangedListener checkChangedListener = new ICheckChangedListener() {
		@Override
		public void checkChanged(List<Object> eventObjects, boolean checked) {
			try {
				if (CollectionUtils.isEmpty(eventObjects)) {
					return;
				}
				if (unScrapActionField.validate()) {
					LotScrap lotScrap = (LotScrap)unScrapActionField.getValue();
					for(Object object : eventObjects) {
						LotScrap unit = (LotScrap) object;
						if (unit == null) {
							return;
						}
						if (checked) {
							if (StringUtil.isEmpty(lotScrap.getUnScrapCode())) {
								break;
							}
							unit.setUnScrapCode(lotScrap.getUnScrapCode());
							unit.setUnScrapComment(lotScrap.getUnScrapComment());
						} else {
							unit.setUnScrapCode("");
							unit.setUnScrapComment("");
						}
					}
				} else {
					UI.showWarning(String.format(Message.getString("wip.scrap_code_required"), HEADER_SCRAP_CODE));
					for(Object object : eventObjects) {
						checkBoxTableViewerManager.unCheckObject(object);
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	};

	public void setTableList(GlcForm form) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			// 写入已报废信息
			if (CollectionUtils.isNotEmpty(lotScraps)) {
				// 判断是按批次还是按片的反报废
				for (LotScrap lotScrap : lotScraps) {
					if (isUnScrapComponent) {
						ComponentUnit componentUnit = new ComponentUnit();
						componentUnit.setObjectRrn(lotScrap.getComponentRrn());
						componentUnit = (ComponentUnit) adManager.getEntity(componentUnit);
						// 片当前批次RRN
						lotScrap.setAttribute1(componentUnit.getParentUnitRrn());
						// 带入原position
						lotScrap.setAttribute2(componentUnit.getPosition());
					} else {
						// 批次RRN
						lotScrap.setAttribute1(lotScrap.getLotRrn());
					}

					lotScrap.setSubQty(lotScrap.getSubQty() == null ? BigDecimal.ZERO : lotScrap.getSubQty());
					// 反报废数量
					lotScrap.setUnScrapMainQty(lotScrap.getMainQty());
					lotScrap.setUnScrapSubQty(lotScrap.getSubQty());
					// 反报废代码
					lotScrap.setUnScrapCode("");
					// 备注
					lotScrap.setUnScrapComment("");
				}
			}
			
			unScrapListField.getListTableManager().setInput(lotScraps);
			unScrapListField.getListTableManager().refresh();
		} catch (Exception e) {
			logger.error("WIPLotUnScrapDialog : Init tablelist", e);
		}
	}
	
	@Override
	public void initLot() {
		lots = getLotList();
		if(adFormName.equals(AD_FROM_NAME_UNSCRAPQTY)) {
			isUnScrapLot = true;
		}else if (adFormName.equals(AD_FROM_NAME_UNSCRAPCOMP)) {
			isUnScrapComponent = true;
		}
	}

	@Override
	protected void okPressed() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			if (!unScrapActionField.validate()) {
				return;
			}
			// 检查数量
			List<Object> objects = unScrapListField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("wip.unscrap_no_units_selected"));
				return;
			}

			List<LotScrap> lotScraps = objects.stream().map(l -> ((LotScrap) l)).collect(Collectors.toList());

			// 反报废的主数量和字数量不能为空
			Optional<LotScrap> f = lotScraps.stream().filter(l -> l.getUnScrapMainQty() == null || l.getUnScrapSubQty() == null).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_qty_input"));
				return;
			}
			// 反报废发主数量和子数量不能都为0
			f = lotScraps.stream().filter(l -> {
				return BigDecimal.ZERO.compareTo(l.getUnScrapMainQty()) >= 0
						&& BigDecimal.ZERO.compareTo(l.getUnScrapSubQty()) >= 0;
			}).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_main_sub_qty_zero"));
				return;
			}
			// 反报废的主数量和子数量不能大于报废数量
			f = lotScraps.stream().filter(l -> {return l.getMainQty().compareTo(l.getUnScrapMainQty()) < 0 || l.getSubQty().compareTo(l.getUnScrapSubQty()) < 0;}).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_greater_than_scrapped"));
				return;
			}

			if (isUnScrapComponent) {
				if(!unScrapComponent(f, lotScraps)) {
					return;
				}
			} else if (isUnScrapLot) {
				Map<Long, List<LotUnScrapAction>> unScrapActionsMap = Maps.newHashMap();
				LotScrap lotScrap = (LotScrap)unScrapActionField.getValue();
				// 把scrap转成action
				for (LotScrap scrap : lotScraps) {
					LotUnScrapAction action = new LotUnScrapAction();
					action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
					action.setActionCode(scrap.getUnScrapCode());
					action.setActionComment(scrap.getUnScrapComment());
					if (StringUtil.isEmpty(lotScrap.getUnScrapComment())) {
						UI.showInfo(Message.getString("wip.abort_comments_null"));
						return;
					}
					action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
					action.setLotScrap(scrap);

					if (!unScrapActionsMap.containsKey(action.getLotRrn())) {
						unScrapActionsMap.put(action.getLotRrn(), Lists.newArrayList());
					}

					unScrapActionsMap.get(action.getLotRrn()).add(action);
				}
				if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
					return;
				}
				lotManager.unScrapLot(unScrapActionsMap, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	* 片反报废
	*/ 
	public Boolean unScrapComponent(Optional<LotScrap> f, List<LotScrap> lotScraps) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			
			List<ComponentUnit> unScrapUnits = Lists.newArrayList();
			// 位置不能为空
			f = lotScraps.stream().filter(l -> {return l.getAttribute2() == null || StringUtil.isEmpty(l.getAttribute2().toString());}).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_position_null"));
				return false;
			}

			// 检查一片是否输入了多个位置
			// 检查一个位置是否对应多片了
			Map<String, String> positionsMap = Maps.newHashMap();
			Map<String, String> unitsMap = Maps.newHashMap();
			for (LotScrap lotScrap : lotScraps) {
				String position = DBUtil.toString(lotScrap.getAttribute2());
				if (positionsMap.containsKey(lotScrap.getComponentId())) {
					if (!position.equals(positionsMap.get(lotScrap.getComponentId()))) {
						UI.showInfo(String.format(Message.getString("wip.unscrap_multiple_position"),
								lotScrap.getComponentId()));
						return false;
					}
				}

				if (unitsMap.containsKey(position)) {
					if (!lotScrap.getComponentId().equals(unitsMap.get(position))) {
						UI.showInfo(String.format(Message.getString("wip.unscrap_position_repeat"), lotScrap.getComponentId(), unitsMap.get(position)));
						return false;
					}
				}

				positionsMap.put(lotScrap.getComponentId(), position);
				unitsMap.put(position, lotScrap.getComponentId());
			}

			// 检查片位置是否已被占用
			Lot lot = lotManager.getLotWithComponent(lots.get(0).getObjectRrn());
			List<ProcessUnit> processUnits = lot.getSubProcessUnit();
			if (!CollectionUtils.isEmpty(processUnits)) {
				Map<String, String> poistions = Maps.newHashMap();
				for (ProcessUnit unit : processUnits) {
					ComponentUnit componentUnit = (ComponentUnit) unit;
					poistions.put(componentUnit.getComponentId(), componentUnit.getPosition());
				}

				for (LotScrap lotScrap : lotScraps) {
					String position = DBUtil.toString(lotScrap.getAttribute2());
					if (poistions.containsKey(lotScrap.getComponentId())) {
						if (!position.equals(poistions.get(lotScrap.getComponentId()))) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"), lotScrap.getComponentId(), poistions.get(lotScrap.getComponentId())));
							return false;
						}
					} else {
						Boolean judge = false;
						String repeatPosition = null;
						for (Map.Entry<String, String> entry : poistions.entrySet()) {
							if (position.equals(entry.getValue())) {
								repeatPosition = entry.getKey();
								judge = true;
								break;
							}
						}
						if (judge) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"), repeatPosition, poistions.get(repeatPosition)));
							return false;
						}
					}
				}
			}

			if (isUnScrapQty) {
				Map<String, List<LotUnScrapAction>> unScrapActionsMap = Maps.newHashMap();
				// 把scrap转成action
				for (LotScrap scrap : lotScraps) {
					LotUnScrapAction action = new LotUnScrapAction();
					action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
					action.setActionCode(scrap.getUnScrapCode());
					action.setActionComment(scrap.getUnScrapComment());
					if (StringUtil.isEmpty(scrap.getUnScrapComment())) {
						UI.showInfo(Message.getString("wip.abort_comments_null"));
						return false;
					}
					action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
					action.setLotScrap(scrap);

					// 处理报废参数
					ComponentUnit unit = componentManager.getComponentByComponentId(Env.getOrgRrn(),
							scrap.getComponentId());
					if (!unScrapUnits.contains(unit)) {
						unit.setPosition(DBUtil.toString(scrap.getAttribute2()));
						unScrapUnits.add(unit);
					}

					if (!unScrapActionsMap.containsKey(scrap.getComponentId())) {
						unScrapActionsMap.put(scrap.getComponentId(), Lists.newArrayList());
					}

					unScrapActionsMap.get(scrap.getComponentId()).add(action);
				}
				if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
					return false;
				}
				componentManager.unScrapComponentQty(unScrapUnits, unScrapActionsMap, true, Env.getSessionContext());
			} else {
				List<LotUnScrapAction> unScrapActions = Lists.newArrayList();
				Lot lastParentLot = null;
				List<LotScrap> lotscrapList = lotScraps.stream().map(selectObj -> (LotScrap) selectObj).collect(Collectors.toList());
				//List<LotScrap> lotscrapList = new ArrayList<LotScrap>();
				//lotscrapList.addAll(lotScraps);
				// 把scrap转成action
				for (LotScrap scrap : lotscrapList) {
					// 清空子数量
					scrap.setSubQty(BigDecimal.ZERO);
					scrap.setUnScrapSubQty(BigDecimal.ZERO);

					LotUnScrapAction action = new LotUnScrapAction();
					action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
					action.setActionCode(scrap.getUnScrapCode());
					action.setActionComment(((LotScrap)unScrapActionField.getValue()).getUnScrapComment());
					if (StringUtil.isEmpty(((LotScrap)unScrapActionField.getValue()).getUnScrapComment())) {
						UI.showInfo(Message.getString("wip.abort_comments_null"));
						return false;
					}
					action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
					action.setLotScrap(scrap);

					// 处理报废参数
					ComponentUnit unit = componentManager.getComponentByComponentId(Env.getOrgRrn(), scrap.getComponentId());
					if (!unScrapUnits.contains(unit)) {
						unit.setPosition(DBUtil.toString(scrap.getAttribute2()));
						unScrapUnits.add(unit);
						unScrapActions.add(action);
					}

					if (lastParentLot == null) {
						lastParentLot = lotManager.getLotByLotId(Env.getOrgRrn(), scrap.getLotId(), true);
					} 
				}
				if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
					return false;
				}
				lastParentLot.setOperator1(Env.getUserName());
				componentManager.unScrapComponent(lastParentLot, unScrapUnits, unScrapActions, null,Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}

	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			Boolean result = checkLotStateModel(getLotList());
			if (result == null) {
				for(Lot lot : getLotList()) {
					if (!(LotStateMachine.STATE_SCRAP.equals(lot.getState())
							|| LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState()) 
							|| LotStateMachine.STATE_RUN.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState()))) {
						UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
						return false;
					}
				}
				try {
					if(CollectionUtils.isNotEmpty(lotScraps)) {
						LotManager lotManager = Framework.getService(LotManager.class);
						Lot lot = lotManager.getLotWithComponent(lotScraps.get(0).getLotRrn());
						if(LotStateMachine.STATE_RUN.equals(lot.getState()) || LotStateMachine.STATE_TERM.equals(lot.getState())) {
							UI.showError(Message.getString("wip.lot_state_is_run_cannot_unscarp"));
							return false;
						} else {
							// 完全报废的批次
							if (LotStateMachine.COMCLASS_COM.equals(lot.getComClass())
									&& LotStateMachine.STATE_SCRAP.equals(lot.getState())) {
								for(LotScrap lotScarp : lotScraps) {
									if(!lotScarp.getStepName().equals(lot.getStepName())) {
										UI.showError(Message.getString("wip.component_state_or_stepname_inconsistent_with_lot"));
										return false;
									}
								}
									
							} else {
								for(LotScrap lotScarp : lotScraps) {
									if((!lotScarp.getState().equals(lot.getState())) || 
											(!lotScarp.getStepName().equals(lot.getStepName()))) {
										UI.showError(Message.getString("wip.component_state_or_stepname_inconsistent_with_lot"));
										return false;
									}
								}
							}
						}
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			} else {
				return result;
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}
