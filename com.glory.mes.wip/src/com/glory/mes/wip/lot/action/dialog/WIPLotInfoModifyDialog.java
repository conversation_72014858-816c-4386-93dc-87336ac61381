package com.glory.mes.wip.lot.action.dialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotInfoModifyDialog extends LotActionDialog{ 
	
	private static final Logger logger = Logger.getLogger(WIPLotInfoModifyDialog.class);

	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String ADFORM_NAME = "WIPLotActionLotInfoModifyDialog";
	
	private static final String FIELD_LOTINFOMODIFYTABLE = "lotInfoModifyTable";
	private static final String FIELD_LOTINFO = "lotInfo";
	private static final String FIELD_LOTCHANGEPROPERTY = "lotChangeProperty";
	private static final String FIELD_LOTCHANGEPRIORITY = "lotChangePriority";
	private static final String FIELD_LOTCHANGEPART = "lotChangePart";
	private static final String FIELD_LOTCHANGEWO = "lotChangeWo";
	private static final String FIELD_LOTCHANGECOMMENT = "lotChangeComment";
	
	private static final String FIELD_PRIORITY = "priority";
	private static final String FIELD_PARTNAME = "partName";
	private static final String FIELD_WOID = "woId";
	private static final String FIELD_LOTCOMMENT = "lotComment";
	
	private static final String BUTTON_PROPERTY = "saveProperty";
	private static final String BUTTON_PRIORITY = "savePriority";
	private static final String BUTTON_PART = "savePart";
	private static final String BUTTON_WO = "saveWo";
	private static final String BUTTON_COMMENT = "saveComment";

	protected ListTableManagerField lotInfoModifyTableField;
	protected EntityFormField lotInfoField;
	protected EntityFormField lotChangePropertyField;
	protected EntityFormField lotChangePriorityField;
	protected EntityFormField lotChangePartField;
	protected EntityFormField lotChangeWoField;
	protected EntityFormField lotChangeCommentField;
	
	protected RefTableField priorityField;
	protected RefTableField partNameField;
	protected RefTableField woIdField;
	protected TextField lotCommentField;
	
	protected List<Lot> lots;
	
	protected ListTableManager lotInfoModifyManager;
	
	public WIPLotInfoModifyDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotInfoModifyTableField = form.getFieldByControlId(FIELD_LOTINFOMODIFYTABLE, ListTableManagerField.class);
		lotInfoField = form.getFieldByControlId(FIELD_LOTINFO, EntityFormField.class);
		
		lotChangePropertyField = form.getFieldByControlId(FIELD_LOTCHANGEPROPERTY, EntityFormField.class);
		lotChangePriorityField = form.getFieldByControlId(FIELD_LOTCHANGEPRIORITY, EntityFormField.class);
		lotChangePartField = form.getFieldByControlId(FIELD_LOTCHANGEPART, EntityFormField.class);
		lotChangeWoField = form.getFieldByControlId(FIELD_LOTCHANGEWO, EntityFormField.class);
		lotChangeCommentField = form.getFieldByControlId(FIELD_LOTCHANGECOMMENT, EntityFormField.class);
		
		priorityField = lotChangePriorityField.getFieldByControlId(FIELD_PRIORITY, RefTableField.class);
		partNameField = lotChangePartField.getFieldByControlId(FIELD_PARTNAME, RefTableField.class);
		woIdField = lotChangeWoField.getFieldByControlId(FIELD_WOID, RefTableField.class);
		lotCommentField = lotChangeCommentField.getFieldByControlId(FIELD_LOTCOMMENT, TextField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PROPERTY), this::changeProperty);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PRIORITY), this::changePriority);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PART), this::changePart);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_WO), this::changeWo);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_COMMENT), this::changeComment);
		
		lotInfoModifyManager = lotInfoModifyTableField.getListTableManager();
		
		lotInfoModifyManager.addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Lot lot = (Lot) selection.getFirstElement();
				initLotInfo(lot);
			}
		});
		initLot();
	}

	@Override
	public void initLot() {
		lots = getLotList();
		lotInfoModifyManager.setInput(lots);
	}
	
	public void initLotInfo(Lot lot) {
		lotInfoField.setValue(lot);
		lotInfoField.refresh();
	}
	

	public void changeProperty(Object object) {
		try {
			if (lotChangePropertyField.validate()) {
				Lot changeLot = (Lot) lotChangePropertyField.getValue();
				LotManager lotManager = Framework.getService(LotManager.class);
				SessionContext sc = Env.getSessionContext();
				List<Lot> lots = this.lots.stream().map(selectLot -> {
					Lot lot = (Lot) selectLot;
					lot.setOperator1(getOperator());
					return lot;
				}).collect(Collectors.toList());
				
				//调用后台方法保存数据
				Map<String, String> changeMap = getPropertiesMap(changeLot);
				lotManager.changeLotInfo(lots, changeMap, sc);
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				refresh(lots);
				lotChangePropertyField.setValue(new Lot());
				lotChangePropertyField.refresh();
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void changePriority(Object object) {
		try {
			if (lotChangePriorityField.validate()) {
				Lot changeLot = (Lot) lotChangePriorityField.getValue();
				LotManager lotManager = Framework.getService(LotManager.class);
				SessionContext sc = Env.getSessionContext();
				
				List<Lot> lots = this.lots.stream().map(selectLot -> {
					Lot lot = (Lot) selectLot;
					lot.setOperator1(getOperator());
					return lot;
				}).collect(Collectors.toList());
				
				LotAction action = new LotAction();
				String comment = changeLot.getAttribute1() != null ? changeLot.getAttribute1().toString() : null;
				action.setActionComment(comment);
				//调用后台方法保存数据
				lotManager.changeLotPriority(lots, changeLot.getPriority(), action, sc);
				UI.showInfo(Message.getString("wip.priority_successed"));// 弹出提示框
				refresh(lots);
				lotChangePriorityField.setValue(new Lot());
				lotChangePropertyField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void changePart(Object object) {
		try {
			if (lotChangePartField.validate()) {
				LotManager lotManager = Framework.getService(LotManager.class);
				SessionContext sc = Env.getSessionContext();
				String newPartName = partNameField.getText();
				
				List<Lot> lots = this.lots.stream().map(selectLot -> {
					Lot lot = (Lot) selectLot;
					lot.setOperator1(getOperator());
					return lot;
				}).collect(Collectors.toList());
				
				lotManager.changeLotPart(lots, newPartName, sc);
				UI.showInfo(Message.getString("wip.newpart_successed"));
				refresh(lots);
				lotChangePartField.setValue(new Lot());
				lotChangePartField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void changeWo(Object object) {
		try {
			if (lotChangeWoField.validate()) {
				LotManager lotManager = Framework.getService(LotManager.class);
				String woId = woIdField.getText();
				
				List<Lot> lots = this.lots.stream().map(selectLot -> {
					Lot lot = (Lot) selectLot;
					lot.setOperator1(getOperator());
					return lot;
				}).collect(Collectors.toList());
				
				lotManager.changeLotWo(lots, woId, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.changemo_successed"));// 弹出提示框
				refresh(lots);
				lotChangeWoField.setValue(new Lot());
				lotChangeWoField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void changeComment(Object object) {
		try {
			if (lotChangeCommentField.validate()) {
				LotManager lotManager = Framework.getService(LotManager.class);
				//调用后台方法保存数据
				SessionContext sc = Env.getSessionContext();
				String comment = lotCommentField.getText();
				
				List<Lot> lots = this.lots.stream().map(selectLot -> {
					Lot lot = (Lot) selectLot;
					lot.setOperator1(getOperator());
					return lot;
				}).collect(Collectors.toList());
				
				lotManager.addLotComments(lots, comment, sc);
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				refresh(lots);
				lotChangeCommentField.setValue(new Lot());
				lotChangeCommentField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton cancel = this.createSquareButton(parent, 1, Message.getString(ExceptionBundle.bundle.CommonClose()), false, "Gray");
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);		
	}
	
	public void refresh(List<Lot> lots) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> inputLots = new ArrayList<Lot>(lots);
			List<String> lotIds = inputLots.stream().map(Lot::getLotId).collect(Collectors.toList());
			this.lots = lotManager.getLotsByLotId(Env.getOrgRrn(), lotIds, false);
			lotInfoModifyManager.setInput(this.lots);
			lotInfoField.setValue(this.lots.get(0));
			lotInfoField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public Map<String, String> getPropertiesMap(Lot lot){
		try {
			EntityForm entityForm = (EntityForm) lotChangePropertyField.getControls()[0];
			List<ADField> adFields = entityForm.getAllADfields();
			Map<String, String> map = new HashMap<String, String>();
			if (CollectionUtils.isNotEmpty(adFields)) {
				for (ADField adField : adFields) {
					Object o = PropertyUtil.getProperty(lot, adField.getName());
					String newValue = DBUtil.toString(o);
					if (!StringUtil.isEmpty(newValue)) {
						map.put(adField.getName(), newValue);
					}
				}
			}
			return map;
		} catch (Exception e) {
			logger.error("ChangeQtySection : getPropertiesMap()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
}