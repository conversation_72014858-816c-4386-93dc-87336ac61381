package com.glory.mes.wip.lot.action.dialog;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.rule.merge.BasicMergeRule;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotMergeDialog extends LotActionDialog{

    private static final Logger logger = Logger.getLogger(WIPLotMergeDialog.class);

    private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String AD_FROM_NAME = "WIPLotActionMergeDialog";
	
    private static final String FIELD_MERGE = "mergeList";
    private static final String FIELD_MERGE_INFO = "mergeInfo";
    protected ListTableManagerField  mergeListField;
    protected EntityFormField mergeInfo;
    protected Lot lot;
	protected Event event;
	protected List<Lot> searchedLots;
	private Consumer<WIPLotMergeDialog> closeAdaptor;
    
    public WIPLotMergeDialog(String adFormName, String authority, IEventBroker eventBroker){
		super(AD_FROM_NAME, authority, eventBroker);
	}
    
    public WIPLotMergeDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, Event event) {
		super(adFormName, authority, eventBroker);
		this.adFormName = adFormName;
		this.lot = lot;
        this.event = event;
        this.setBlockOnOpen(false);
        this.eventId = LotStateMachine.TRANS_MERGEIN;
        setLotList(Lists.newArrayList(lot));
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {
        	initLot();//获取批次查询勾选的批次信息
        	mergeListField = form.getFieldByControlId(FIELD_MERGE, ListTableManagerField.class);
        	mergeInfo = form.getFieldByControlId(FIELD_MERGE_INFO, EntityFormField.class);
        	searchedLots();
        	if(CollectionUtils.isNotEmpty(searchedLots)) {
        		mergeListField.getListTableManager().setInput(searchedLots);
        	}
        	mergeInfo.setValue(new LotAction());
        	mergeInfo.refresh();
        } catch (Exception e) {
            logger.error("WIPLotMergeDialog : Init tablelist", e);
        }
	}
	
	@Override
	protected void okPressed() {	
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Object> selectObjs = mergeListField.getListTableManager().getCheckedObject();
			if (!mergeInfo.validate()) {
				return;
			}
			if (CollectionUtils.isEmpty(selectObjs)) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}

			if (lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.input_lot_first"));
			} else {
				List<Lot> mergedLots = selectObjs.stream().map(selectObj -> (Lot) selectObj).collect(Collectors.toList());
				if (mergedLots == null || mergedLots.size() == 0) {
					UI.showError(Message.getString("wip.merge_nolot"));
					return;
				}

				LotAction lotAction = (LotAction) mergeInfo.getValue();
				lot.setOperator1(getOperator());
				lotManager.mergeLot(lot, mergedLots, lotAction, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.merge_success"));
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void searchedLots() {
		if (lot != null) {
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lt = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
				searchedLots = lotManager.getCanMergeLots(lt, BasicMergeRule.GET_SOURCE);
				//  根据线别过滤批次
				if(Env.isUseLine()){//如果设置线别进则行批次过滤,如果其他项目组没有线别概念则不进入循环
					for(Lot sLot : searchedLots){
						try {//根据线别重新获取批次，如果报线别不匹配异常则从集合中删除
							sLot = LotProviderEntry.getLotByLine(sLot.getLotId());
						} catch (Exception e) {
							searchedLots.remove(sLot);
						}
					}
				}
				
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		
			String ld = lot.getLotId();
			Lot removeLot = null;
			
			if (searchedLots.size() != 0) {
				for (Lot l : searchedLots) {
					if (ld.equals(l.getLotId())) {
						removeLot = l;
					}
				}
				if (removeLot != null) {
					searchedLots.remove(removeLot);
				}
			}
		}
	}
	
	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getLotList())) {
			lot = getLotList().get(0);
		}
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			Boolean result = checkLotStateModel(getLotList());
			if (result == null) {
				for(Lot lot : getLotList()) {
					if (!(LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState()) 
							|| LotStateMachine.STATE_RUN.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_DISP.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState()))) { 
						UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
						return false;
					}
				}
			} else {
				return result;
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<WIPLotMergeDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<WIPLotMergeDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}
