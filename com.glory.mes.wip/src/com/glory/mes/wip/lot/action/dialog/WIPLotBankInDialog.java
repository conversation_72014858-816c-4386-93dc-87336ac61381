package com.glory.mes.wip.lot.action.dialog;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.wip.action.LotBankAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class WIPLotBankInDialog extends LotActionDialog {
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String ADFORM_NAME = "WIPLotActionBankInDialog";
	
	private static final String FIELD_LOTBANKIN = "WIPLotBankIn";
	private static final String FIELD_BANKOPERATIONLOT = "WIPBankOperationLot";
	
	private static final String FIELD_WAREHOUSEID = "warehouseId";
	private static final String FIELD_LOCATORID = "locatorId";
	private static final String FIELD_LOTCOMMENT = "lotComment";
	
	private ListTableManagerField lotBankInListTable;
	private EntityFormField lotBankInWarehouseEntityForm;
	private RefTableField warehouseIdField;
	private RefTableField locatorIdField;
	private TextField lotCommentField;
	
	private ListTableManager lotBankInTableManager;
	
	public WIPLotBankInDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotBankInListTable = form.getFieldByControlId(FIELD_LOTBANKIN, ListTableManagerField.class);
		lotBankInWarehouseEntityForm = form.getFieldByControlId(FIELD_BANKOPERATIONLOT, EntityFormField.class);
		warehouseIdField = lotBankInWarehouseEntityForm.getFieldByControlId(FIELD_WAREHOUSEID, RefTableField.class);
		locatorIdField = lotBankInWarehouseEntityForm.getFieldByControlId(FIELD_LOCATORID, RefTableField.class);
		lotCommentField = lotBankInWarehouseEntityForm.getFieldByControlId(FIELD_LOTCOMMENT, TextField.class);
		
		lotBankInTableManager = lotBankInListTable.getListTableManager();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		List<Lot> lots = getLotList();
		lotBankInTableManager.setInput(lots);
	}
	
	@Override
	protected void okPressed() {
		try {
			if (!lotBankInWarehouseEntityForm.validate()) {
				return;
			}
			if (!checkWarehouseId()) {
				return;
			}
			List<Lot> lots = (List<Lot>) lotBankInTableManager.getInput();
			lots = lots.stream().map(lot -> {
				lot.setWarehouseId(warehouseIdField.getValue().toString());
				lot.setLocatorId(locatorIdField.getValue().toString());
				lot.setOperator1(getOperator());
				return lot;
			}).collect(Collectors.toList());
			
			LotBankAction lotBankAction = new LotBankAction();
			lotBankAction.setActionComment((String) lotCommentField.getValue());

			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.bankIn(lots, lotBankAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.bank_in_successed"));// 弹出提示框
		
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private boolean checkWarehouseId() throws ClientException, Exception {
		if (StringUtils.isEmpty((String) warehouseIdField.getValue())) {
			UI.showError(Message.getString("mm.warehouse_can_not_null"));
			return false;
		}
		if (StringUtils.isEmpty((String) locatorIdField.getValue())) {
			UI.showError(Message.getString("mm.storage_can_not_null"));
			return false;
		}
		// 判断仓库下库位是否存在
		ADManager adManager = Framework.getService(ADManager.class);
		List<Storage> storagelist = adManager.getEntityList(Env.getOrgRrn(), Storage.class, Env.getMaxResult(),
				" warehouseId = '" + warehouseIdField.getValue() + "' AND name = '" + locatorIdField.getValue() + "'", null);
		// 如果能找到，对比录入库位ID
		if (CollectionUtils.isEmpty(storagelist)) {
			UI.showError(Message.getString("mm.storage_no_exist"));
			return false;
		} else {
			return true;
		}
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			for(Lot lot : getLotList()) {
				if (!LotStateMachine.STATE_WAIT.equals(lot.getState())) {
					UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
