package com.glory.mes.wip.lot.batchjob.group;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.eclipse.swt.widgets.ExportToolItemGlc;
import org.eclipse.swt.widgets.IToolItemListener;
import org.eclipse.swt.widgets.ImportToolItemGlc;
import org.osgi.service.event.Event;

import com.glory.common.excel.upload.ExcelUpload;
import com.glory.edc.client.EDCManager;
import com.glory.edc.model.calculation.ProcessGroup;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class BatchJobMappingEditor extends GlcEditor{
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.batchjob.group.BatchJobMappingEditor";

	private QueryFormField queryFormField;
	private EntityFormField entityFormField;
	protected ImportToolItemGlc importToolItem;
	protected ExportToolItemGlc exportToolItem;

	private static final String FIELD_QUERY = "BatchJobMappingQuery";
	private static final String FIELD_FORM = "BatchJobhMappingGlc";

	@Override
	protected void createFormAction(GlcForm form) {

		super.createFormAction(form);

		queryFormField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);
		queryFormField.getQueryForm().setParmaterMap(getParameterMap());

		entityFormField = form.getFieldByControlId(FIELD_FORM, EntityFormField.class);
		
		// 新建按钮事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);

		// 保存事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);

		// 选择事件
		subscribeAndExecute(eventBroker, queryFormField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);

		// 删除事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);

		// 刷新事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_REFRESH), this::refreshAdapter);

		importToolItem = (ImportToolItemGlc)form.getButtonByControl(null, IToolItemListener.TYPE_IMPORT);
		exportToolItem = (ExportToolItemGlc)form.getButtonByControl(null, IToolItemListener.TYPE_EXPORT);
		if (importToolItem != null) {
			importToolItem.isBackend = true;
			Consumer<?> postImport = t -> refreshAdapter(null);
			importToolItem.postImportConsumer = postImport;
			importToolItem.cudConsumer = new Consumer<ExcelUpload>() {
				@Override
				public void accept(ExcelUpload t) {
					cudUploadEntityList(t);
				}
			};
		}
		if (exportToolItem != null) {
			exportToolItem.isBackend = true;
		}
	}
	
	public Map<String, Object> getParameterMap() {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("orgRrn", Env.getOrgRrn());
		return paramMap;
	}

	protected void newAdapter(Object object) {
		ProcessGroup newPlan = new ProcessGroup();
		entityFormField.setValue(newPlan);
		entityFormField.refresh();
		queryFormField.getQueryForm().queryAdapter();
	}

	protected void saveAdapter(Object object) {
		form.getMessageManager().removeAllMessages();
		try {
			if (entityFormField.validate()) {
				ProcessGroup processGroup = (ProcessGroup) entityFormField.getValue();
				EDCManager edcManager = Framework.getService(EDCManager.class);
				processGroup.setCategory(ProcessGroup.GROUPCATEGORY_BJ);
				if (StringUtil.isEmpty(processGroup.getNpwProcedureName())) {
					processGroup.setNpwProcedureName(processGroup.getNpwProcedureName());
				}
				edcManager.saveProcessGroup(processGroup, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				refresh(processGroup);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}

	protected void refresh(ProcessGroup processGroup) {
		try {
			if(processGroup != null && processGroup.getGroupId() != null) {
				entityFormField.setValue(processGroup);
				entityFormField.refresh();
			} else {
				entityFormField.setValue(new ProcessGroup());
				entityFormField.refresh();
			}
			queryFormField.getQueryForm().queryAdapter();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void selectAdapter(Object object) {
		Event event = (Event) object;
		Map map = (Map) event.getProperty("org.eclipse.e4.data");
		if (MapUtils.isEmpty(map)) {
			return;
		}
		
		ProcessGroup processGroup = new ProcessGroup();
		processGroup.setObjectRrn(DBUtil.toLong(map.get("PROCESSRRN")));
		processGroup = (ProcessGroup) getADManger().getEntity(processGroup);
		
		ProcessGroup measureProcessGroup = new ProcessGroup();
		measureProcessGroup.setObjectRrn(DBUtil.toLong(map.get("MEASURERRN")));
		measureProcessGroup = (ProcessGroup) getADManger().getEntity(measureProcessGroup);

		//插入entityForm
		processGroup.setNpwProcedureName(measureProcessGroup.getProcedureName());
		processGroup.setNpwStepName(measureProcessGroup.getStepName());
		entityFormField.setValue(processGroup);
		entityFormField.refresh();
	}

	protected void deleteAdapter(Object object) {
		try {
			ProcessGroup processGroup = (ProcessGroup) entityFormField.getValue();
			if (processGroup != null && processGroup.getGroupId() != null) {
				if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()))) {
					EDCManager edcManager = Framework.getService(EDCManager.class);
					edcManager.removeProcessGroup(processGroup.getGroupId(), Env.getSessionContext());	
	
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
					refresh(null);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void refreshAdapter(Object object) {
		refresh(null);
	}

	protected void cudUploadEntityList(ExcelUpload upload) {
		try {
			List<ADBase> uploadList = upload.getUploadList();
			List<ProcessGroup> futureActions = uploadList.stream().map(x -> {
				ProcessGroup group = (ProcessGroup)x;
				group.setCategory(ProcessGroup.GROUPCATEGORY_BJ);
				if (StringUtil.isEmpty(group.getNpwProcedureName())) {
					group.setNpwProcedureName(group.getNpwProcedureName());
				}
				return group;
			}).collect(Collectors.toList());
			EDCManager edcManager = Framework.getService(EDCManager.class);
			edcManager.saveProcessGroups(futureActions, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonImportSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
