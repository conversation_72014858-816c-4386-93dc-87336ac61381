package com.glory.mes.wip.lot.reserved;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.mes.wip.model.LotReserved;

public class LotReservedEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.reserved.ComponentReseredEditor";

	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected MPart mPart;

	protected SashForm sashForm;

	private QueryTableForm queryForm;
	private LotReservedSection section;
	
	public static final String RESERVED_SOURCE_MANUAL="MANUAL";

	@PostConstruct
	public void postConstruct(Composite parent) {
		ADTable adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);

		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);

		ManagedForm mform = new ManagedForm(toolkit, form);

		Composite body = form.getBody();
		configureBody(body);

		// 创建查询form
		createQueryForm(body, adTable, new FMessageManager());

		queryForm.getTableManager().addSelectionChangedListener(new ISelectionChangedListener() {

			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Object object = selection.getFirstElement();
				if (object instanceof LotReserved) {
					LotReserved reserved = (LotReserved) object;
					section.setObject(reserved, null);
				}
			}
		});
		// 未修改ad_table:WIPReserved防止影响其它功能，查询结果手动过滤，仅MANUAL，并按Reserved降序排序，后期支持其它条件
		queryForm.getQueryButton().addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				List<LotReserved> lotReservedList =  (List<LotReserved>) queryForm.getTableManager().getInput();
				if (CollectionUtils.isEmpty(lotReservedList)) return;
				List<LotReserved> manualLotReservedLot = lotReservedList.stream().filter(t->RESERVED_SOURCE_MANUAL.equals(t.getReservedSource()))
						.sorted(Comparator.comparing(LotReserved::getReserveTime,Comparator.nullsFirst(Date::compareTo)).reversed()).collect(Collectors.toList());
				queryForm.getTableManager().setInput(manualLotReservedLot);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
			
		});
		
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = 220;
		queryForm.setLayoutData(gd);
		
		Composite sectionComposite = toolkit.createComposite(body, SWT.NULL);
		GridLayout layout = new GridLayout(1, false);
		sectionComposite.setLayout(layout);
		sectionComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

		section = new LotReservedSection(adTable);
		section.createContents(mform, sectionComposite);
		section.setQueryTableForm(queryForm);

	}

	public void createQueryForm(Composite parent, ADTable adTable, IMessageManager manager) {
		queryForm = new QueryTableForm(parent, SWT.NONE, adTable, manager);
		GridData gridData = new GridData(GridData.FILL_BOTH);
		queryForm.setLayoutData(gridData);
		queryForm.setLayout(new GridLayout(1, false));
	}

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;

		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
