package com.glory.mes.wip.lot.revert;

import java.math.BigDecimal;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class RevertLotSection extends LotSection {
	
	private static final Logger logger = Logger.getLogger(LotSection.class);
	protected Text text;
	protected RevertLotForm revertLotForm;
	protected Lot lot;
//	protected List<StartSource> startSourceList;
	protected ToolItem revert; 
	public RevertLotSection(ADTable table) {
		super(table);
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		revertLotForm = new RevertLotForm(composite, SWT.NONE, tab, form);
		return revertLotForm;
	}
	
	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
		txtLot.setTextLimit(32);
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor("Black"));
				switch (event.keyCode) {
				case SWT.CR:
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					lot = searchLot(lotId);
					tLotId.selectAll();
					if (lot == null) {
						tLotId.setForeground(SWTResourceCache.getColor("Red"));
						try {
	        				setAdObject(createAdObject());		        			
	        			} catch(Exception en) {
	        				logger.error("createADObject error at searchEntity Method!");
	        			}
						txtLot.warning();
					} else {
						setAdObject(lot);
						try {
							ADManager adManager = Framework.getService(ADManager.class);
							String condition = "lotId='" + lot.getLotId() +"' ";
//							List<StartLot> startLot = adManager.getEntityList(Env.getOrgRrn(), StartLot.class, Env.getMaxResult(), condition, null);
//							if (startLot.size() != 0) {
//								condition = "startRrn='" + startLot.get(0).getStartRrn()+ "' ";
//								startSourceList = adManager.getEntityList(Env.getOrgRrn(), StartSource.class, Env.getMaxResult(), condition, null);
//								for (StartSource startSource : startSourceList) {
//									startSource.setPartSpec5(startSource.getMainQty().toString());
//								}
//								for (int i=0; i<startSourceList.size(); i++) {
//									if (i==0) {
//										startSourceList.get(i).setMainQty(lot.getMainQty());//初始时table中第一行的数量显示批次总数量的值，第二行后数量都显示为0
//										startSourceList.get(i).setPartSpec5(lot.getMainQty().toString());
//									} else { 
//										startSourceList.get(i).setMainQty(BigDecimal.ZERO);
//										startSourceList.get(i).setPartSpec5("0");
//									}
//								}
//								revertLotForm.getViewer().setInput(startSourceList);
//							} else {
//								revertLotForm.getViewer().setInput(null);
//							}
						} catch (Exception e) {
							e.printStackTrace();
						}
						
						txtLot.focusing();
					}
					refresh();
					break;
				}
			}
		});
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});
		
		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
		
	}
	
	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.revert_lot"));
		initAdObject();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRevert(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		this.createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}	
	
	public void createToolItemRevert(ToolBar tBar){
		revert = new ToolItem(tBar, SWT.NULL);
		revert.setText(Message.getString("wip.revert_revertButton"));
		revert.setImage(SWTResourceCache.getImage("return_lot"));
		revert.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				revertAdapter();
			}
		});
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Lot newBase = (Lot)this.getAdObject();
		if (newBase != null ){
			statusChanged(newBase.getState());
		} else {
			statusChanged("");
		}
	}
	
	@Override
	public void statusChanged(String newStatus) {
		if (newStatus == null || "".equals(newStatus.trim())) {
			revert.setEnabled(false);
		} else if (LotStateMachine.STATE_WAIT.equals(newStatus)) {
			revert.setEnabled(true);
		} else {
			revert.setEnabled(false);
		}
	}
	
	@Override
	protected void newAdapter() {
		super.newAdapter();
		revertLotForm.getViewer().getTable().removeAll();
		refresh();
	}

	protected void revertAdapter() {
		try {
			long qty = 0;
			if (revertLotForm.getViewer().getTable().getItems().length != 0) {
//				startSourceList = (List<StartSource>)revertLotForm.getViewer().getInput();
//				for (StartSource startSource : startSourceList) {
//					qty += Integer.parseInt(startSource.getPartSpec5());//partSpec5是个中间字段，它是显示在table中，但保存时把partSpec5的值传给mainQty字段保存
//					startSource.setMainQty(new BigDecimal(Integer.parseInt(startSource.getPartSpec5())));
//				}				
				if (qty != lot.getMainQty().intValue()) {
					UI.showError(Message.getString("wip.revert_error_qty"));//总数量之和和批次主数量不相等的错误信息
					return;
				} else {
//					ADManager adManager = Framework.getService(ADManager.class);
//					Schedule sc = new Schedule();
//					sc.setObjectRrn(lot.getWoRrn());
//					sc = (Schedule) adManager.getEntity(sc);
//					long startedMainQty = sc.getStartedMainQty().longValue();
//					sc.setStartedMainQty(BigDecimal.valueOf(startedMainQty-qty));
//					LotManager lotManager = Framework.getService(LotManager.class);
//					lotManager.revertLot(sc, lot, startSourceList, Env.getSessionContext());
				}
				UI.showInfo(Message.getString("wip.revert_successed"));
				refreshAdapter();
				revertLotForm.getViewer().getTable().removeAll();
			} else {
				UI.showInfo(Message.getString("wip.revert_not_need_revert"));
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	protected void refreshAdapter() {
		super.refreshAdapter();
//		startSourceList = (List<StartSource>)revertLotForm.getViewer().getInput();
//		for (StartSource startSource : startSourceList) {
//			startSource.setPartSpec5(startSource.getMainQty().toString());		
//		}
////		revertLotForm.getViewer().getTable().removeAll();
//		revertLotForm.getViewer().setInput(startSourceList);
		refresh();
	}
	
}
