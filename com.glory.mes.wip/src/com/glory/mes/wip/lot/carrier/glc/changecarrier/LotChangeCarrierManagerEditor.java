package com.glory.mes.wip.lot.carrier.glc.changecarrier;


import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.widgets.AuthoritySquareButton;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.comp.ComponentComposite.DiffType;
import com.glory.mes.wip.custom.CarrierAssignCustomComposite;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.custom.SortingCustomComposite;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.sorting.SortModel;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.collect.Lists;

public class LotChangeCarrierManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.changecarrier.LotChangeCarrierManagerEditor";
	
	public static final String FIELD_CARRIERLOTINFO = "carrierLotInfo";
	public static final String FIELD_CHANGECARRIERDETAILINFO = "changeCarrierDetailInfo";

	public static final String BUTTON_NAME = "lotChangeCarrier";

	protected CustomField carrierLotInfoField;
	protected CustomField changeCarrierDetailInfoField;
	protected CarrierAssignCustomComposite carrierAssignCustomComposite;
	protected CarrierLotCustomComposite carrierLotCustomComposite;
	protected ComponentAssignCustomComposite componentAssignCustomComposite;
	protected ListTableManager lotTableManager;
	protected AuthoritySquareButton itemChangeCarrier;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		carrierLotInfoField = form.getFieldByControlId(FIELD_CARRIERLOTINFO, CustomField.class);
		changeCarrierDetailInfoField = form.getFieldByControlId(FIELD_CHANGECARRIERDETAILINFO, CustomField.class);

		carrierAssignCustomComposite = (CarrierAssignCustomComposite) carrierLotInfoField.getCustomComposite();
		carrierLotCustomComposite = carrierAssignCustomComposite.getCarrierLotComposite();
		componentAssignCustomComposite = (ComponentAssignCustomComposite) changeCarrierDetailInfoField.getCustomComposite();
		componentAssignCustomComposite.getSourceComponentComposite().txtLotId.setEnabled(false);

		itemChangeCarrier = (AuthoritySquareButton) form.getButtonByControl(null, BUTTON_NAME);
		itemChangeCarrier.setEnabled(false);

        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME), this::changeCarrierAdapter);
		subscribeAndExecute(eventBroker, carrierLotInfoField.getFullTopic(CarrierAssignCustomComposite.ID_CARRIER_LOT + GlcEvent.NAMESPACE_SEPERATOR
				+ "lotTable" + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::lotSelectChangeAdapter);
	}

    @Override
    public void initializeDatas(Map<String, Object> initDatas) {
        try {
            // 如果有初始数据
            String lotId = (String) initDatas.get(MesGlcEvent.PROPERTY_LOT_ID);
            String carrierId = (String) initDatas.get(MesGlcEvent.PROPERTY_CARRIER_ID);
            String targetCarrierId = (String) initDatas.get(MesGlcEvent.PROPERTY_TARGET_CARRIER_ID);

            CarrierLotCustomComposite carrierLotComposite = carrierAssignCustomComposite.getCarrierLotComposite();
            carrierLotComposite.getTxtCarrierId().setEnabled(false);
            carrierLotComposite.getTxtLotId().setEnabled(false);
            if (!StringUtil.isEmpty(carrierId)) {
                try {
                    DurableManager durableManager = Framework.getService(DurableManager.class);
                    Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);

                    if (carrier != null) {
                        CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
                        List<Lot> carrierLots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
                        carrierLotComposite.getTxtCarrierId().setText(carrierId);
                        carrierLotComposite.getLotTableManager().setInput(carrierLots);
                        carrierLotComposite.getLotTableManager().refresh();
                    }
                } catch (Exception e) {
                    ExceptionHandlerManager.asyncHandleException(e);
                }
            } else if (!StringUtil.isEmpty(lotId)) {
                Lot lot = LotProviderEntry.getLot(lotId);
                if (lot != null) {
                    carrierLotComposite.getTxtLotId().setText(lotId);
                    carrierLotComposite.getLotTableManager().setInput(Lists.newArrayList(lot));
                    carrierLotComposite.getLotTableManager().refresh();
                }
            }

            if (!StringUtil.isEmpty(targetCarrierId)) {
                componentAssignCustomComposite.getTargetComponentComposite().setCarrier(carrierId);
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

	protected void lotSelectChangeAdapter(Object object) {
		try {
			Object selectedObject = carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().getSelectedObject();
			if (selectedObject == null) {
				return;
			}

			Lot lot = (Lot) selectedObject;
			componentAssignCustomComposite.getSourceComponentComposite().setLot(lot.getLotId());
			String lotId = componentAssignCustomComposite.getSourceComponentComposite().txtLotId.getText();
			if (!StringUtil.isEmpty(lotId)) {
				itemChangeCarrier.setEnabled(true);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

    public void changeCarrierAdapter(Object object) {
        try {
			// 变更载具
			DurableManager durableManager = Framework.getService(DurableManager.class);
			String targetCarrierId = componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.getText();
			Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), targetCarrierId, true);

			String lotId = componentAssignCustomComposite.getSourceComponentComposite().txtLotId.getText();
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, false);
			if (lot == null) {
				UI.showInfo(Message.getString("wip.carrier_assign_lot_scan"));
				return;
			} else if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
				UI.showInfo(Message.formatString("wip.changecarrier_state_run_not_allow" + "#" + lot.getLotId()));
				return;
			} else if (targetCarrier == null) {
				UI.showError(Message.getString("mm.durable_not_found"));
				return;
			}

			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			List<ComponentUnit> addComponents = componentAssignCustomComposite.getTargetComponentComposite().getComponentComposite().getComponentsDiff()
					.get(DiffType.ADD);
			if (CollectionUtils.isEmpty(addComponents)) {
				UI.showError(Message.getString("wip.need_add_assign_component"));
				return;
			}

			// 不允许两个以及多个批次同时绑定
			List<Long> parentUnitRrns = addComponents.stream().map(ComponentUnit::getParentUnitRrn).distinct().collect(Collectors.toList());
			if (parentUnitRrns.size() > 1) {
				UI.showInfo(Message.getString("wip.not_allow_more_than_one_lot"));
				return;
			}

			for (Long objectRrn : parentUnitRrns) {
				if (!lot.getObjectRrn().equals(objectRrn)) {
					UI.showInfo(Message.getString("wip.source_lot_component_not_match"));
					return;
				}
			}
			
			componentAssignCustomComposite.getSortingCustomComposite().getForm().saveToObject();
            SortModel sort = (SortModel) componentAssignCustomComposite.getSortingCustomComposite().getForm().getObject();
			// 是否sort
			if (sort.isSort()) {
				Equipment equipment = null;
				if (!StringUtil.isEmpty(sort.getEquipmentId())) {
					RASManager rasManager = Framework.getService(RASManager.class);
					RefTableField reftableField = (RefTableField)componentAssignCustomComposite.getSortingCustomComposite().getForm().getFields().get(SortingCustomComposite.FIELD_EQUIPMENTID);
					equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), reftableField.getComboControl().getText());
				}

				// port口要不都有要不都没有
				if ((!StringUtil.isEmpty(sort.getFromPortId()) && StringUtil.isEmpty(sort.getToPortId()))
						|| (StringUtil.isEmpty(sort.getFromPortId()) && !StringUtil.isEmpty(sort.getToPortId()))) {
					UI.showError(Message.getString("wip.sorting_port_incomplete"));
					return;
				}

				// 校验port口时候一样
				if (!StringUtil.isEmpty(sort.getFromPortId()) && !StringUtil.isEmpty(sort.getToPortId())) {
					if ((sort.getFromPortId()).equals(sort.getToPortId())) {
						UI.showError(Message.getString("wip.sorting_port_diverse"));
						return;
					}
				}

				boolean isAutoComplete = false;
				if ((Equipment.COMMUNICATION_STATE_OFFLINE.equals(sort.getCommunicationState()) || StringUtil.isEmpty(sort.getCommunicationState()))
						&& sort.isSort()) {
					if (UI.showConfirm(Message.getString("wip.auto_complete_sorting_job"))) {
						isAutoComplete = true;
					}
				}

				ADManager adManager = Framework.getService(ADManager.class);
				// 如果批次处于ExchangeSort类型工步，手动创建Exchange类型的SortingJob时，JobType为Auto, 方便自动完成工步
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) adManager.getEntity(step);

				String jobType = LotSortingJob.JOB_TYPE_MANUAL;
//				if (Step.USE_CATEGORY_EXCHANGESORT.equals(step.getUseCategory())) {
//					jobType = LotSortingJob.JOB_TYPE_AUTO;
//				}

				LotSortingAction lotSortingAction = new LotSortingAction();
				lotSortingAction.setSort(true);
				lotSortingAction.setToDurableId(componentAssignCustomComposite.getTargetCarrierId());
				if (equipment != null) {
					lotSortingAction.setEquipmentId(equipment.getEquipmentId());
					lotSortingAction.setFromPortId(sort.getFromPortId());
					lotSortingAction.setToPortId(sort.getToPortId());
				}
				lotSortingAction.setAutoComplete(isAutoComplete);

				Map<String, String> toPositionMap = addComponents.stream().collect(Collectors.toMap(ComponentUnit::getComponentId, ComponentUnit::getPosition));
				lotSortingAction.setToPositionMap(toPositionMap);

				// 目标载具物料类型和批次物料类型（批次所在工步的出站物料类型）不一致才提示是否强制改变批次物料类型
				Boolean isSave = true;
				String targetMainType = step != null && !StringUtil.isEmpty(step.getOutMainMatType()) ? step.getOutMainMatType() : lot.getMainMatType();
				if (!StringUtil.isEmpty(targetMainType) && !ObjectUtils.equals(targetCarrier.getMainMatType(), targetMainType)) {
					if (UI.showConfirm(
							String.format(Message.getString("wip.inconsistent_material_types"), targetCarrier.getMainMatType(), lot.getMainMatType()))) {
						isSave = true;
					} else {
						isSave = false;
					}
				}

				if (isSave) {
					SortingManager sortingManager = Framework.getService(SortingManager.class);
					sortingManager.saveExchangeSortingJob(addComponents, lotSortingAction, jobType, Env.getSessionContext());
					if (isAutoComplete) {
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					} else {
						UI.showInfo(Message.getString("wip.assign_sort_job_successed"));
					}
				}

			} else {
				if (!LotStateMachine.STATE_WAIT.equals(lot.getState()) && !LotStateMachine.STATE_FIN.equals(lot.getState())) {
					throw new ClientException("error.state_not_allow");
				}
				carrierLotManager.reassignCarrierLot(targetCarrier, false, lot.getLotId(), addComponents, false, true, Env.getSessionContext());

				UI.showInfo(Message.getString("wip.change_successed"));
			}
			refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

	public void refresh() {
		componentAssignCustomComposite.getSourceComponentComposite().getTableManager().setInput(Lists.newArrayList());
		componentAssignCustomComposite.getSourceComponentComposite().setCarrier(null);
		componentAssignCustomComposite.getSourceComponentComposite().setLot(null);
		componentAssignCustomComposite.getTargetComponentComposite().getTableManager().setInput(Lists.newArrayList());
		componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.setText("");
		componentAssignCustomComposite.getTargetComponentComposite().getComponentComposite().initComponents(Lists.newArrayList());
		componentAssignCustomComposite.getSortingCustomComposite().getForm().setObject(new SortModel());

		String carrierId = carrierLotCustomComposite.getTxtCarrierId().getText();
		String lotId = carrierLotCustomComposite.getTxtLotId().getText();
		if (StringUtils.isNotEmpty(lotId)) {
			carrierLotCustomComposite.getLotByLotId(lotId);
		} else if (StringUtils.isNotEmpty(carrierId)) {
			carrierLotCustomComposite.getLotsByCarrierId(carrierId);
		} else {
			carrierLotCustomComposite.getLotsByCarrierId(null);
		}
		itemChangeCarrier.setEnabled(false);
	}
}