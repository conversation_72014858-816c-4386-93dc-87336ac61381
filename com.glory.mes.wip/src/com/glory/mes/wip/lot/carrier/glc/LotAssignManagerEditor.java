package com.glory.mes.wip.lot.carrier.glc;


import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.widgets.AuthoritySquareButton;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentComposite.DiffType;
import com.glory.mes.wip.custom.CarrierAssignCustomComposite;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;

public class LotAssignManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.LotAssignManagerEditor";

	public static final String FIELD_ASSIGNINFO = "assignInfo";
	public static final String FIELD_ASSIGNDETAILINFO = "assignDetailInfo";
	
	public static final String BUTTON_NAME = "lotAssign";

	protected CustomField assignInfoField;
	protected CustomField assignDetailInfoField;
	protected CarrierAssignCustomComposite carrierAssignCustomComposite;
	protected ComponentAssignCustomComposite componentAssignCustomComposite;
	protected AuthoritySquareButton itemAssign;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		assignInfoField = form.getFieldByControlId(FIELD_ASSIGNINFO, CustomField.class);
		assignDetailInfoField = form.getFieldByControlId(FIELD_ASSIGNDETAILINFO, CustomField.class);
		
		carrierAssignCustomComposite = (CarrierAssignCustomComposite) assignInfoField.getCustomComposite();
		componentAssignCustomComposite = (ComponentAssignCustomComposite) assignDetailInfoField.getCustomComposite();
		
		componentAssignCustomComposite.getSourceComponentComposite().txtLotId.setEnabled(false);
		itemAssign = (AuthoritySquareButton) form.getButtonByControl(null, BUTTON_NAME);
		itemAssign.setEnabled(false);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME), this::assignAdapter);
		subscribeAndExecute(eventBroker, assignInfoField.getFullTopic(CarrierAssignCustomComposite.ID_CARRIER_LOT + 
				GlcEvent.NAMESPACE_SEPERATOR + "lotId"  + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::lotEnterPressedAdapter);
	}
	
	protected void assignAdapter(Object object) {
		try {
			// 绑定载具
			DurableManager durableManager = Framework.getService(DurableManager.class);
			String carrierId = componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.getText();
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, true);
			
			String lotId = componentAssignCustomComposite.getSourceComponentComposite().txtLotId.getText();
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, false);
			if (lot == null) {
				UI.showInfo(WipExceptionBundle.bundle.WipCarrierAssigenLotScan());
				return;
			}
			
			if (BigDecimal.ZERO.compareTo(lot.getMainQty()) >= 0) {
				UI.showInfo(WipExceptionBundle.bundle.WipCarrierAssigenLotQty());
				return;
			}
			
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
				carrierLotManager.assignCarrierLotOnly(carrier, null, Lists.newArrayList(lot), true, true, Env.getSessionContext());
			} else {
				List<ComponentUnit> addComponents = componentAssignCustomComposite.getTargetComponentComposite().getComponentComposite().getComponentsDiff().get(DiffType.ADD);
				if (CollectionUtils.isNotEmpty(addComponents)) {
					// 不允许两个以及多个批次同时绑定   
					List<Long> parentUnitRrns = addComponents.stream().map(ComponentUnit::getParentUnitRrn).distinct().collect(Collectors.toList());
					if (parentUnitRrns.size() > 1) {
						UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipSelectCompFromMultiLot()));
						return;
					}
					
					for (Long objectRrn : parentUnitRrns) {
						if (!lot.getObjectRrn().equals(objectRrn)) {
							UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipSourceComponentNotMatch()));
							return;
						}
					}
					carrierLotManager.assignCarrierLot(carrier, lotId, addComponents, false, false, Env.getSessionContext());
				} else {
					UI.showError(Message.getString(WipExceptionBundle.bundle.WipRequireAssignComponent()));
					return;
				}
			}
			
			UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipAssignSuccess()));
			refresh(lotId, carrierId);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void lotEnterPressedAdapter(Object object) {
		try {
			Event event = (Event) object;
			String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			
			HeaderText txtLotId = carrierAssignCustomComposite.getCarrierLotComposite().getTxtLotId();
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, false);
			if (lot == null) {
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.warning();
				return;
			}							
			if (!StringUtil.isEmpty(lot.getDurable())) {
				carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().getInput().clear();
				carrierAssignCustomComposite.getCarrierLotComposite().getLotDetailsForm().setObject(new Lot());
				carrierAssignCustomComposite.getCarrierLotComposite().getLotDetailsForm().loadFromObject();
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.warning();
				UI.showError(Message.getString(WipExceptionBundle.bundle.WipLotAssignedCarrier()));
				return;
			}
			componentAssignCustomComposite.getSourceComponentComposite().setLot(lotId);
			lotId = componentAssignCustomComposite.getSourceComponentComposite().txtLotId.getText();
			if (!StringUtil.isEmpty(lotId)) {
				itemAssign.setEnabled(true);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh(String lotId, String carrierId) {
		// 刷新绑定载具页面信息
		componentAssignCustomComposite.getSourceComponentComposite().setLot(null);
		componentAssignCustomComposite.getSourceComponentComposite().getTableManager().getInput().clear();
		componentAssignCustomComposite.getTargetComponentComposite().setCarrier(carrierId);
		
		// 刷新左边批次信息
		carrierAssignCustomComposite.getCarrierLotComposite().getLotByLotId(lotId);
		carrierAssignCustomComposite.getCarrierLotComposite().getLotDetailsForm().setObject(new Lot());
		carrierAssignCustomComposite.getCarrierLotComposite().getLotDetailsForm().loadFromObject();
		itemAssign.setEnabled(false);
	}
}