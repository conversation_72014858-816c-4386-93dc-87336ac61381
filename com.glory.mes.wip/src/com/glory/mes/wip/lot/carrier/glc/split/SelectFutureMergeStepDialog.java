package com.glory.mes.wip.lot.carrier.glc.split;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.viewers.IStructuredSelection;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.model.Lot;

public class SelectFutureMergeStepDialog extends GlcBaseDialog {

	public CustomField flowInfoCustomField;
	public FlowCustomComposite flowCustomComposite;
	
	public Lot lot;
	private StepState stepState;
	
	public SelectFutureMergeStepDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		flowInfoCustomField = form.getFieldByControlId("flowInfo", CustomField.class);
		flowCustomComposite =  (FlowCustomComposite)flowInfoCustomField.getCustomComposite();
		flowCustomComposite.loadFlowTreeByLot(lot);
	}
	
    @Override
    protected void okPressed() {
    	IStructuredSelection selection = (IStructuredSelection) flowCustomComposite.getViewer().getSelection();
		Object obj = selection.getFirstElement();
		if (obj instanceof StepState) {
			try {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				StepState state = (StepState) obj;
				int result = prdManager.compareToCurrentStep(lot.getProcessInstanceRrn(), state);
				if (result <= 0) {
					//请选择当前节点之后的工步
					UI.showError(Message.getString("wip.please_select_node_after_current_node"));
					return;
				}
				setStepState(state);
				super.okPressed();
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			//请选择正确的节点
			UI.showError(Message.getString("wip.select_node_is_incorrect"));
			return;
		}
    }

	public StepState getStepState() {
		return stepState;
	}

	public void setStepState(StepState stepState) {
		this.stepState = stepState;
	}
    
}
