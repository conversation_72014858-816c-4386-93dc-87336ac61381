package com.glory.mes.wip.lot.unterminate;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class UnTerminateSection extends LotSection {

	protected AuthorityToolItem itemUnTerminate;
	public static final String KEY_UNTERMINATE = "unTerminate";

	public UnTerminateSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.unterminate_sectiontitle"));
		initAdObject();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemUnTerminate(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemUnTerminate(ToolBar tBar) {
		itemUnTerminate = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() +"."+ KEY_UNTERMINATE);
		itemUnTerminate.setAuthEventAdaptor(this::unTerminateAdapter);
		itemUnTerminate.setText(Message.getString("wip.unterminate"));
		itemUnTerminate.setImage(SWTResourceCache.getImage("unterminate-lot"));
//		itemUnTerminate.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				unTerminateAdapter(event);
//			}
//		});
	}

	protected void unTerminateAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						UI.showWarning(Message.getString("warn.required_entry"));
						saveFlag = false;
					}
				}
				String operator = Env.getUserName();
				if (itemUnTerminate.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
					operator = (String) itemUnTerminate.getData(LotAction.ACTION_TYPE_OPERATOR);
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						LotManager lotManager = Framework
								.getService(LotManager.class);
						LotAction lotAction = ((UnTerminateForm) detailForm)
								.getLotAction();
						Lot lot = ((UnTerminateForm) detailForm).getLot();
						lot.setOperator1(operator);
						lotManager.unTerminate(lot,
								lotAction, Env.getSessionContext());//  修改的地方
					}
					UI.showInfo(Message.getString("wip.unterminate_success"));// 弹出提示框
					refresh();
					setFocus();
				}
			} else {
				UI.showError(Message.getString("wip.unterminate_error_nolot"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		UnTerminateForm itemForm = new UnTerminateForm(composite, SWT.NONE,
				tab, mmng);
		return itemForm;
	}

	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
				stateChange((Lot)adBase);
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		super.refresh();
	}
	
	public void stateChange(Lot lot) {
		if (lot != null && lot.getState() != null) {
			if (LotStateMachine.TRANS_TERMLOT.equalsIgnoreCase(lot.getPreTransType())) {
				itemUnTerminate.setEnabled(true);
			} else {
				itemUnTerminate.setEnabled(false);
			}
		} else {
			itemUnTerminate.setEnabled(false);
		}
	}
}
