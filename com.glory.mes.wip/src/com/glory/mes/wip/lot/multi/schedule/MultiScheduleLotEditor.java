package com.glory.mes.wip.lot.multi.schedule;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.ParameterDefinition;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotParameter;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiScheduleLotEditor extends GlcEditor{
	
	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.multi.schedule.MultiScheduleLotEditor";

	private QueryFormField queryFormField;
	private TableEditorField listTableManagerField;
	private EntityFormField entityFormField;
	private GlcFormField glcFormField;
	
	private static final String FIELD_QUERY = "lotQuery";
	private static final String FIELD_GLCFORM = "lotBaseGlc";
	private static final String FIELD_ENTITYFORM = "lotBase";
	private static final String FIELD_LISTTABLEMANGER = "lotTable";
	
	private static final String BUTTON_SCHEDULE = "schedule";
	private static final String BUTTON_UNSCHEDULE = "unschedule";
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_ADD = "add";
	private static final String BUTTON_REMOVE = "remove";
	private static final String BUTTON_REFRESH = "refresh";
	
	private Boolean isJoin = true;//当其他操作对entityFormField赋值时会触发refTableField的监听事件，isJoin参数避免其他操作误入监听
	
	
	@Override
	protected void createFormAction(GlcForm form) {		
		super.createFormAction(form);
		
		queryFormField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);
		
		glcFormField = form.getFieldByControlId(FIELD_GLCFORM, GlcFormField.class);
		
		entityFormField = glcFormField.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		
		listTableManagerField = glcFormField.getFieldByControlId(FIELD_LISTTABLEMANGER, TableEditorField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_QUERY + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);		
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SCHEDULE), this::scheduleAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNSCHEDULE), this::unScheduleAdapter);
		
		subscribeAndExecute(eventBroker, glcFormField.getFullTopic(BUTTON_ADD), this::addAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		subscribeAndExecute(eventBroker, glcFormField.getFullTopic(BUTTON_REMOVE), this::delAdapter);
		
		refresh(null);
		
	}
	
	private void newAdapter(Object obj) {
		try {
			refresh(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	private void scheduleAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Object> objects = (List<Object>) listTableManagerField.getTableManager().getInput();
			List<Lot> lots = objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
			if (lots.size() == 0) {
				UI.showInfo(Message.getString("wip.please_select_lot_first"));
				return;
			}
			lots = lotManager.scheduleLot(lots, Env.getSessionContext());
			this.createAdObject(); 
			refresh(lots.get(0));
			UI.showInfo(Message.getString("common.schedule_successed"));//弹出提示框
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	private void unScheduleAdapter(Object obj) {
		try {
			Lot lot = (Lot) queryFormField.getQueryForm().getTableManager().getSelectedObject();
			boolean confirmDelete = UI.showConfirm(Message.getString("common.confirm_unschedule"));
			if (confirmDelete) {
				if (lot.getObjectRrn() != null) {
					LotManager lotManager = Framework.getService(LotManager.class);
					lot.setOperator1(Env.getUserName());
					lotManager.deleteLot(lot, true, Env.getSessionContext());
					entityFormField.setValue(createAdObject());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				}
			}
			refresh(lot);
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}
	
	private void addAdapter(Object obj) {
		try {		
			Lot lot = (Lot) entityFormField.getValue();
			if (lot.getMainQty() != null && lot.getPartName() != null && lot.getSubUnitType() != null) {				
				List<Object> objects = (List<Object>) listTableManagerField.getTableManager().getInput();
				List<Lot> lines = new ArrayList<Lot>();
				for(Object object : objects) {
					Lot lotTable = (Lot) object;
					lines.add(lotTable);
				}	
				Lot newLot = new Lot();
				newLot.setOrgRrn(Env.getOrgRrn());
				newLot.setMainQty(lot.getMainQty());
				newLot.setPartName(lot.getPartName());
				newLot.setLotType(lot.getLotType());
				newLot.setPartDesc(lot.getPartDesc());
				newLot.setCustomerCode(lot.getCustomerCode());
				newLot.setPriority(lot.getPriority());
				newLot.setSubUnitType(lot.getSubUnitType());
				newLot.setLotComment(lot.getLotComment());
				newLot.setPartRrn(lot.getPartRrn());
				lines.add(newLot);				
				listTableManagerField.getTableManager().setInput(lines);
			
			}			
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void delAdapter(Object obj) {
		try {
			List<Lot> lines = (List<Lot>) listTableManagerField.getTableManager().getInput();
			List<Object> objects = listTableManagerField.getTableManager().getCheckedObject();
			List<Lot> lots = new ArrayList<>();
			if (lines.size() != 0) {
				for (Object object : objects) {
					Lot lot = (Lot) object;  
					lots.add(lot);
				}
			}
			listTableManagerField.getTableManager().getInput().removeAll(lots);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	private void refresh(Lot lot) {
		try {
			isJoin = false;		
			if(lot != null && lot.getObjectRrn() != null) {		
				entityFormField.refresh();
				entityFormField.setValue(new Lot());
				listTableManagerField.refresh();
				queryFormField.refresh();
			}else {
				entityFormField.setValue(new Lot());
				entityFormField.refresh();
				listTableManagerField.refresh();
			}
			isJoin = true;
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}			
	}
	
	private void selectionChanged(Object obj) {
		try {
			Event event = (Event) obj;
			Lot lot = (Lot)event.getProperty(GlcEvent.PROPERTY_DATA);
			if(lot != null) {
				isJoin = false;
				entityFormField.setValue(lot);
				listTableManagerField.refresh();
				entityFormField.refresh();
				isJoin = true;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	public ADBase createAdObject() throws Exception {
		Lot lot = new Lot();
		lot.setOrgRrn(Env.getOrgRrn());
		
		String whereClause = " isLotDefault = 'Y' ";
		ADManager adManager = Framework.getService(ADManager.class);
		List<ParameterDefinition> parameters = 
			adManager.getEntityList(Env.getOrgRrn(), ParameterDefinition.class, Env.getMaxResult(), whereClause, " name ASC ");
		if (parameters != null) {
			List<LotParameter> lotParamters = new ArrayList<LotParameter>();
			for (ParameterDefinition parameter : parameters) {
				LotParameter lotParamter = new LotParameter(parameter.getName(), null, null, parameter.getDefValue());
				lotParamter.setIsActive(true);
				lotParamter.setOrgRrn(parameter.getOrgRrn());
				lotParamter.setType(parameter.getType());
				lotParamters.add(lotParamter);
			}
			lot.setLotParameters(lotParamters);
		}	
		return lot;
	}
	
	public void refreshAdapter(Object object) {
		Event event = (Event) object;
	    Lot lot = (Lot)event.getProperty(GlcEvent.PROPERTY_DATA);
		refresh(lot);	
	};
	
}
		

