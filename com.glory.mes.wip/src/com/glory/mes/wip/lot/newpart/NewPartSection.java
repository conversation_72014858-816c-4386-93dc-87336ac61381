package com.glory.mes.wip.lot.newpart;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;
import org.eclipse.ui.forms.widgets.TableWrapData;
import org.eclipse.ui.forms.widgets.TableWrapLayout;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.LotProcedure;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotProcedureManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotProcedureChange;
import com.glory.mes.wip.model.LotProcedureLock;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

public class NewPartSection extends LotSection {

	protected AuthorityToolItem itemNewPart;
	protected ToolItem refreshItem;
	protected NewPartForm itemForm;

	public static String KEY_NEWPART = "newPart";
	
	public NewPartSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		this.form = form;
		final FormToolkit toolkit = form.getToolkit();
		section = toolkit.createSection(parent, Section.DESCRIPTION | Section.NO_TITLE | FFormSection.FFORM);
		section.setText(Message.getString("wip.newpart"));
		section.marginWidth = 0;
		section.marginHeight = 0;
		
		TableWrapLayout layout = new TableWrapLayout();
		layout.numColumns = 1;
		layout.topMargin = 0;
		layout.leftMargin = 5;
		layout.rightMargin = 2;
		layout.bottomMargin = 0;
		parent.setLayout(layout);
		
		section.setLayout(layout);
		TableWrapData td = new TableWrapData(TableWrapData.FILL, TableWrapData.FILL);
		td.grabHorizontal = true;
		td.grabVertical = false;
		section.setLayoutData(td);
		
		Composite client = toolkit.createComposite(section);

		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		client.setLayout(gridLayout);
		client.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createToolBar(section);
		createSectionContent(client);
		toolkit.paintBordersFor(section);
		section.setClient(client);
		statusChanged(null);
	}

	@Override
	protected void createSectionContent(Composite client) {		
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.verticalAlignment = SWT.TOP;
		final FormToolkit toolkit = form.getToolkit();
		Composite top = toolkit.createComposite(client);
		GridLayout layout = new GridLayout();
		layout.marginTop = -20;
		top.setLayout(layout);
		top.setLayoutData(gd);
		IMessageManager mmng = form.getMessageManager();
		itemForm = new NewPartForm(top, SWT.NONE, getTable(), this, mmng, form);
		itemForm.setLayoutData(gd);
		
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNewPart(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemNewPart(ToolBar tBar) {
		itemNewPart = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_NEWPART);
		itemNewPart.setText(Message.getString("wip.newpart_button"));
		itemNewPart.setImage(SWTResourceCache.getImage("newpart"));
		itemNewPart.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				newPartAdapter(event);
			}
		});
	}

	protected void newPartAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();

			boolean saveFlag = true;
			if (!itemForm.saveToObject()) {
				saveFlag = false;
			}

			if (saveFlag) {
				Lot lot = itemForm.getLot();
				Part newPart = itemForm.getNewPart();
				List<Node> flowList = itemForm.getFlowList();
				LotAction lotAction = itemForm.getLotAction();
				LotManager lotManager = Framework.getService(LotManager.class);
				List list = lotManager.getLotFutureActions(lot);
				List<Object> deleteFutureActions = new ArrayList<Object>();
				if (list.size() > 0) {	
					LotFutureActionListDialog dialog = new LotFutureActionListDialog(Display.getCurrent().getActiveShell(), list);
					if (dialog.open() == Dialog.OK) {
						deleteFutureActions.addAll(dialog.getSelectObjects());
					} else {
						return;
					}
				}
				
				String procedureStatePath = null;
				if (CollectionUtils.isNotEmpty(flowList)) {
					for (Node node : flowList) {
						if (node instanceof ProcedureState) {
							procedureStatePath = node.getName() + "/";
							break;
						}
					}
				}
											
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				boolean isOnlyCurrentProcedure = MesCfMod.isOnlyCancelTargetProcedureLockNewPart(Env.getOrgRrn(), sysParamManager);
				PrdManager prdManager = Framework.getService(PrdManager.class);
				List<LotProcedure> lotProcedures = null;
				List<LotProcedure> changeLotProcedures = null;
				List<LotProcedure> lockLotProcedures = null;
				if (isOnlyCurrentProcedure) {
					lotProcedures = prdManager.getLotProcedureList(lot.getObjectRrn(), procedureStatePath, null);
					changeLotProcedures = lotProcedures.stream().filter(p -> LotProcedure.TYPE_CHANGE.equals(p.getType())).collect(Collectors.toList());
					lockLotProcedures = lotProcedures.stream().filter(p -> LotProcedure.TYPE_LOCKVERSION.equals(p.getType())).collect(Collectors.toList());
					
				} else {
					String procedureStatePath1 = procedureStatePath;
					lotProcedures = prdManager.getLotProcedureList(lot.getObjectRrn(), null, null);
					changeLotProcedures = lotProcedures.stream().filter(p -> LotProcedure.TYPE_CHANGE.equals(p.getType()) && p.getReplacedProcedureStatePath().equals(procedureStatePath1)).collect(Collectors.toList());
					lockLotProcedures = lotProcedures.stream().filter(p -> LotProcedure.TYPE_LOCKVERSION.equals(p.getType())).collect(Collectors.toList());
				}
							
				if (CollectionUtils.isNotEmpty(changeLotProcedures) && CollectionUtils.isNotEmpty(lockLotProcedures)) {
					//Change与Lock都不为空的情况
					if (!UI.showConfirm(Message.getString("prd.change_part_contains_change_lock_flow"))) {
						return;
					}
				} else if (CollectionUtils.isNotEmpty(changeLotProcedures) && CollectionUtils.isEmpty(lockLotProcedures)) {
					//Change不为空，Lock为空的情况
					if (!UI.showConfirm(Message.getString("prd.change_part_contains_change_flow"))) {
						return;
					}
				} else if (CollectionUtils.isEmpty(changeLotProcedures) && CollectionUtils.isNotEmpty(lockLotProcedures)) {
					//Change为空，Lock不为空的情况
					if (!UI.showConfirm(Message.getString("prd.change_part_contains_lock_flow"))) {
						return;
					}
				}
						
				PrdManager pardManager = Framework.getService(PrdManager.class);
				Process process = new Process();
				process.setOrgRrn(Env.getOrgRrn());
				process.setName(newPart.getProcessName());
				process.setVersion(newPart.getProcessVersion());
				process = (Process)pardManager.getSimpleProcessDefinition(process);
				
				lot.setOperator1(Env.getUserName());
				lotManager.newPart(lot, newPart, process, flowList, deleteFutureActions, lotAction, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.newpart_successed"));// 弹出提示框
				refresh();
				setFocus();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void statusChanged(String newStatus){
		if (newStatus != null && !"".equals(newStatus.trim())) {
			if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus)) {
				itemNewPart.setEnabled(true);
			} else {
				Lot lot = itemForm.getLot();
				if (lot != null) {
					if (LotStateMachine.STATE_WAIT.equals(lot.getState())) {
						itemNewPart.setEnabled(true);
					} else {
						itemNewPart.setEnabled(false);
					}
				} else {
					itemNewPart.setEnabled(false);
				}
			}
		} else {
			itemNewPart.setEnabled(false);
		}
	}
	
	@Override
	protected void refreshAdapter() {
		refresh();
	}
	
	@Override
	public void refresh() {
		itemForm.refresh();
		Lot lot = (Lot) itemForm.getLot();
		statusChanged(lot.getState());
	}

	@Override
	public void setFocus() {
		itemForm.setFocus();
	}
}
