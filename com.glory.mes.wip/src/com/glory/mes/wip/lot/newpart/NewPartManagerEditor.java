package com.glory.mes.wip.lot.newpart;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.TreeItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.model.MaterialAltProcess;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.LotProcedure;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.custom.NewPartFlowCustomComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class NewPartManagerEditor extends GlcEditor { 
	
	private static final Logger logger = Logger.getLogger(NewPartManagerEditor.class);

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.newpart.NewPartManagerEditor";

	protected static final String CONTROL_LOTID_ENTERPRESSED = "lotId-EnterPressed";
	protected static final String CONTROL_PARTID_SELECTIONCHANGED = "partId-SelectionChanged";
	protected static final String CONTROL_ATLPROCESS_SELECTIONCHANGED = "atlProcess-SelectionChanged";
	
	private static final String FIELD_CHANGEPROCESSINFO = "changeProcessInfo";
	private static final String FIELD_PARTINFO = "partInfo";
	private static final String FIELD_STATE = "state";
	private static final String FIELD_STEPNAME = "stepName";
	private static final String FIELD_COMMENT = "comment";
	private static final String FIELD_CHANGEPROCESSLOT = "changeProcessLot";
	private static final String FIELD_CHANGEPROCESSPART = "changeProcessPart";

	private static final String BUTTON_NEWPART = "newPart";
	private static final String BUTTON_REFRESH = "refresh";

	protected GlcFormField changeProcessInfoField;
	protected TextField partInfoField;
	protected TextField stateField;
	protected TextField stepNameField;
	protected TextField commentField;
	protected CustomField changeProcessLotField;
	protected CustomField changeProcessPartField;
	
	protected FlowCustomComposite changeProcessLot;
	protected NewPartFlowCustomComposite changeProcessPart;
	
	protected AuthorityToolItem itemNewPart;
	
	protected List<Node> flowList = new ArrayList<Node>();

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		changeProcessInfoField = form.getFieldByControlId(FIELD_CHANGEPROCESSINFO, GlcFormField.class);
		partInfoField = form.getFieldByControlId(FIELD_PARTINFO, TextField.class);
		stateField = form.getFieldByControlId(FIELD_STATE, TextField.class);
		stepNameField = form.getFieldByControlId(FIELD_STEPNAME, TextField.class);
		commentField = form.getFieldByControlId(FIELD_COMMENT, TextField.class);
		changeProcessLotField = changeProcessInfoField.getFieldByControlId(FIELD_CHANGEPROCESSLOT, CustomField.class);
		changeProcessPartField = changeProcessInfoField.getFieldByControlId(FIELD_CHANGEPROCESSPART, CustomField.class);

		subscribeAndExecute(eventBroker, changeProcessLotField.getFullTopic(CONTROL_LOTID_ENTERPRESSED), this::enterPressedAdapter);
		subscribeAndExecute(eventBroker, changeProcessPartField.getFullTopic(CONTROL_PARTID_SELECTIONCHANGED), this::partValueChange);
		subscribeAndExecute(eventBroker, changeProcessPartField.getFullTopic(CONTROL_ATLPROCESS_SELECTIONCHANGED), this::altProcessValueChange);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEWPART), this::newPartAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		subscribeAndExecute(eventBroker, changeProcessInfoField.getFullTopic(FIELD_CHANGEPROCESSPART + "-" +GlcEvent.EVENT_CLICK), this::flowListAdapter);
		
		changeProcessLot = (FlowCustomComposite) changeProcessLotField.getCustomComposite();
		changeProcessPart = (NewPartFlowCustomComposite) changeProcessPartField.getCustomComposite();
		
		changeProcessPart.getRefTableField().setEnabled(false);
		
		itemNewPart = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_NEWPART);
		itemNewPart.setEnabled(false);
	}
	
	private void enterPressedAdapter(Object object) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Event event = (Event) object;
			String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
			loadProcessTree(lot);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void flowListAdapter(Object object) {
		try {
			TreeItem[] items = changeProcessPart.getViewer().getTree().getSelection();
			if(items != null && items.length > 0) {
				flowList = new ArrayList<Node>();
				TreeItem item = items[0];
				while(item != null && (item.getData() instanceof Node)){
					flowList.add(0, (Node)item.getData());
					item = item.getParentItem();
				}	
			} else {
				flowList = null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void loadProcessTree(Lot lot) {
		try {
			if (lot != null) {
				partInfoField.setText(lot.getPartId());
				stateField.setText(lot.getCstate());
				stepNameField.setText(lot.getHoldState());
				changeProcessLot.getTxtId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				changeProcessLot.loadFlowTreeByLot(lot);
				statusChanged(lot.getState());
			} else {
				partInfoField.setText("");
				stateField.setText("");
				stepNameField.setText("");
				changeProcessLot.getTxtId().selectAll();
				changeProcessLot.getTxtId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				changeProcessLot.loadFlowTreeByLot(null);
				statusChanged(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void partValueChange(Object object) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Part part = null;
			part = prdManager.getActivePart(Env.getOrgRrn(), changeProcessPart.getSearchField().getValue().toString(), true);
			if (part == null) {
				changeProcessPart.loadFlowTreeByPart(part, null);
				return;
			} else {
				initAlternateProcess(part);
			}
			changeProcessPart.loadFlowTreeByPart(part, null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void altProcessValueChange(Object object) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Event event = (Event) object;
			String newValue = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			MaterialAltProcess materialAltProcess = new MaterialAltProcess();
			materialAltProcess.setObjectRrn(Long.valueOf(newValue));
			materialAltProcess = getAlternateProcess(materialAltProcess);
			Part part = prdManager.getActivePart(Env.getOrgRrn(), changeProcessPart.getSearchField().getValue().toString(), true);
			if (materialAltProcess != null) {
				part.setProcessName(materialAltProcess.getProcessName());
				part.setProcessVersion(materialAltProcess.getProcessVersion());
			}
			changeProcessPart.loadFlowTreeByPart(part, null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected MaterialAltProcess getAlternateProcess(MaterialAltProcess materialAltProcess) {
		try {			
			ADManager entityManager = Framework.getService(ADManager.class);
			return (MaterialAltProcess) entityManager.getEntity(materialAltProcess);
		} catch (Exception e) {
			logger.error("PartFlowSection searchAlternateProcessEntity(): AlternateProcess isn' t exsited!");
		}
		return null;
	}
	
	protected void initAlternateProcess(Part part) {
		try {			
			if(part != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				List<MaterialAltProcess> list = entityManager.getEntityList(Env.getOrgRrn(), 
						MaterialAltProcess.class, Env.getMaxResult(), "partRrn = " + part.getObjectRrn(), "processName, processVersion");
				if (list != null && list.size() > 0) {
					changeProcessPart.getRefTableField().setEnabled(true);
				} else {
					changeProcessPart.getRefTableField().setEnabled(false);
				}
				changeProcessPart.getRefTableField().setValue(null);
				changeProcessPart.getTableManager().setInput(list);
			} else {			
				changeProcessPart.getRefTableField().setEnabled(false);
				changeProcessPart.getTableManager().getInput().clear();
			}
		} catch (Exception e) {
			logger.error("NewPartFlowSection searchAlternateProcessEntity(): AlternateProcess isn' t exsited!");
		}
	}
	
	private void newPartAdapter(Object object) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			if (StringUtil.isEmpty(commentField.getText())) {
				UI.showInfo(Message.getString("wip.abort_comments_null"));
				return;
			}
			if (CollectionUtils.isNotEmpty(flowList) && !(flowList.get(flowList.size() - 1) instanceof StepState)) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.SelectNodeNotStepState()));
				return;
			}
			if (changeProcessPart.getSearchField().getValue() == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.WoSelectPart()));
				return;
			}
			
			Part newPart = prdManager.getActivePart(Env.getOrgRrn(), changeProcessPart.getSearchField().getValue().toString(), true);
			LotAction lotAction = new LotAction();
			lotAction.setActionComment(commentField.getText());
			Lot lot = (Lot) changeProcessLot.getTreeManager().getInput();
			List<Node> flowList = this.flowList;
			
			List list = lotManager.getLotFutureActions(lot);
			List<Object> deleteFutureActions = new ArrayList<Object>();
			if (list.size() > 0) {	
				LotFutureActionListDialog dialog = new LotFutureActionListDialog(Display.getCurrent().getActiveShell(), list);
				if (dialog.open() == Dialog.OK) {
					deleteFutureActions.addAll(dialog.getSelectObjects());
				} else {
					return;
				}
			}
			
			String targetProcedureStatePath = null;
			if (CollectionUtils.isNotEmpty(flowList)) {
				for (Node node : flowList) {
					if (node instanceof ProcedureState) {
						targetProcedureStatePath = node.getName() + "/";
						break;
					}
				}
			}
										
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			boolean isOnlyTargetProcedure = MesCfMod.isOnlyCancelTargetProcedureLockNewPart(Env.getOrgRrn(), sysParamManager);
			List<LotProcedure> lotProcedures = prdManager.getLotProcedureList(lot.getObjectRrn(), null, null);
			String targetProcedureStatePath1 = targetProcedureStatePath;
			List<LotProcedure> changeLotProcedures = lotProcedures.stream().filter(p -> LotProcedure.TYPE_CHANGE.equals(p.getType()) && p.getReplacedProcedureStatePath().equals(targetProcedureStatePath1)).collect(Collectors.toList());
			List<LotProcedure> lockLotProcedures = null;
			if (isOnlyTargetProcedure) {
				lockLotProcedures = lotProcedures.stream().filter(p -> LotProcedure.TYPE_LOCKVERSION.equals(p.getType()) && p.getReplacedProcedureStatePath().equals(targetProcedureStatePath1)).collect(Collectors.toList());				
			} else {											
				lockLotProcedures = lotProcedures.stream().filter(p -> LotProcedure.TYPE_LOCKVERSION.equals(p.getType())).collect(Collectors.toList());
			}
					
			//如果显示过确认框，最下面逻辑就不显示了
			boolean showConfirm = true;
			if (CollectionUtils.isNotEmpty(changeLotProcedures) && CollectionUtils.isNotEmpty(lockLotProcedures)) {
				//Change与Lock都不为空的情况
				showConfirm = false;
				if (!UI.showConfirm(Message.getString("prd.change_part_contains_change_lock_flow"))) {
					return;
				}
			} else if (CollectionUtils.isNotEmpty(changeLotProcedures) && CollectionUtils.isEmpty(lockLotProcedures)) {
				//Change不为空，Lock为空的情况
				showConfirm = false;
				if (!UI.showConfirm(Message.getString("prd.change_part_contains_change_flow"))) {
					return;
				}
			} else if (CollectionUtils.isEmpty(changeLotProcedures) && CollectionUtils.isNotEmpty(lockLotProcedures)) {
				//Change为空，Lock不为空的情况
				showConfirm = false;
				if (!UI.showConfirm(Message.getString("prd.change_part_contains_lock_flow"))) {
					return;
				}
			}
					
			PrdManager pardManager = Framework.getService(PrdManager.class);
			Process process = new Process();
			process.setOrgRrn(Env.getOrgRrn());
			process.setName(newPart.getProcessName());
			process.setVersion(newPart.getProcessVersion());
			process = (Process)pardManager.getSimpleProcessDefinition(process);
			
			lot.setOperator1(Env.getUserName());
			if (showConfirm) {
				if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotNewPartWhetherToContinue()))) {
					return;
				}
			}
			lot = lotManager.newPart(lot, newPart, process, flowList, deleteFutureActions, lotAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.newpart_successed"));// 弹出提示框
			loadProcessTree(lot);
			commentField.setText("");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void refreshAdapter(Object object) {
		try {
			if (!StringUtil.isEmpty(changeProcessLot.getTxtId().getText())) {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), changeProcessLot.getTxtId().getText());
				loadProcessTree(lot);
			} else {
				loadProcessTree(null);
				commentField.setText("");
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void statusChanged(String newStatus){
		if (newStatus != null && !"".equals(newStatus.trim())) {
			if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus)) {
				itemNewPart.setEnabled(true);
			} else {
				Lot lot = (Lot) changeProcessLot.getTreeManager().getInput();
				if (lot != null) {
					if (LotStateMachine.STATE_WAIT.equals(lot.getState())) {
						itemNewPart.setEnabled(true);
					} else {
						itemNewPart.setEnabled(false);
					}
				} else {
					itemNewPart.setEnabled(false);
				}
			}
		} else {
			itemNewPart.setEnabled(false);
		}
	}
	
}