package com.glory.mes.wip.lot.newpart;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;
import org.eclipse.ui.forms.widgets.TableWrapData;
import org.eclipse.ui.forms.widgets.TableWrapLayout;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.model.MaterialAltProcess;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.part.PartFlowSection;

public class NewPartFlowSection extends PartFlowSection {
	
	private static final Logger logger = Logger.getLogger(NewPartFlowSection.class);
	protected IField processNameRefTableField;
	
	protected static final String FIELD_ID_PROCESSNAME = "processName";
	protected ADField processNameField;
	protected ListTableManager processTableManager;
	
	public NewPartFlowSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		this.form = form;
		final FormToolkit toolkit = form.getToolkit();
		section = toolkit.createSection(parent, Section.NO_TITLE);
		section.marginWidth = 0;
		section.marginHeight = 0;
        try {
			ADManager adManager = Framework.getService(ADManager.class);
			
			for(ADTab tab : table.getTabs()) {
				tab =(ADTab)adManager.getADTab(tab.getObjectRrn());
				for (ADField adField : tab.getFields()) {
					if(FIELD_ID_PARTNAME.equals(adField.getName())) {
						partNameField = adField;
					} else if(FIELD_ID_PROCESSNAME.equals(adField.getName())) {
						processNameField = adField;
					}
				}
			}
		} catch (ClientException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}	
		
		TableWrapLayout layout = new TableWrapLayout();
		layout.topMargin = 0;
		layout.leftMargin = 5;
		layout.rightMargin = 2;
		layout.bottomMargin = 0;
		parent.setLayout(layout);

		section.setLayout(layout);
		
		TableWrapData td = new TableWrapData(TableWrapData.CENTER, TableWrapData.MIDDLE);
		td.grabHorizontal = true;
		td.grabVertical = false;
		section.setLayoutData(td);

		Composite client = toolkit.createComposite(section);
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		client.setLayout(gridLayout);

		createSectionTitle(client);
		createSectionContent(client);

		toolkit.paintBordersFor(section);
		section.setClient(client);
	}

	public void initAdObject() {
		Part part = new Part();
		part.setVersion(0L);
		setAdObject(part);
		refresh();
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.BEGINNING);
		gd.verticalAlignment = SWT.TOP;
		gd.widthHint = 542;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(4, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("wip.part"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));		

		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			ADRefTable refTable = new ADRefTable();
			refTable.setObjectRrn(partNameField.getReftableRrn());
			refTable = (ADRefTable)entityManager.getEntity(refTable);
			ADTable adTable = entityManager.getADTable(refTable.getTableRrn());
			
			ListTableManager tableManager = new ListTableManager(adTable);
			if (refTable.getWhereClause() == null || "".equalsIgnoreCase(refTable.getWhereClause().trim())
					|| StringUtil.parseClauseParam(refTable.getWhereClause()).size() == 0) {
				List<ADBase> list = entityManager.getEntityList(Env.getOrgRrn(), 
						adTable.getObjectRrn(), Env.getMaxResult(), refTable.getWhereClause(), refTable.getOrderByClause());
				tableManager.setInput(list);
			}
			int mStyle = SWT.READ_ONLY | SWT.BORDER;
			partRefTableField = new RefTableField("", tableManager, refTable, mStyle);
			partRefTableField.setLabel(null);  //
			partRefTableField.addValueChangeListener(this);
			partRefTableField.createContent(top, toolkit);
			
			// 可选流程
			Label labelProcess = toolkit.createLabel(top, Message.getString("wip.alternate.process"));
			labelProcess.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
			
			ADRefTable refProcessTable = new ADRefTable();
			refProcessTable.setObjectRrn(processNameField.getReftableRrn());
			refProcessTable = (ADRefTable)entityManager.getEntity(refProcessTable);
			ADTable adProcessTable = entityManager.getADTable(refProcessTable.getTableRrn());
			
			processTableManager = new ListTableManager(adProcessTable);
			processNameRefTableField = new RefTableField("", processTableManager, refProcessTable, mStyle);
			processNameRefTableField.setLabel(null);  //
			processNameRefTableField.addValueChangeListener(this);	
			processNameRefTableField.createContent(top, toolkit);
			processNameRefTableField.setEnabled(false);
		} catch (Exception e1) {
			logger.error("PartFlowSection : creatSectionTitle",e1);
		}	
	}

	@Override
	public void valueChanged(Object sender, Object newValue) {
		if (((RefTableField)sender).getComboControl().getData() instanceof MaterialAltProcess) {
			MaterialAltProcess materialAltProcess = new MaterialAltProcess();
			materialAltProcess.setObjectRrn(Long.valueOf((String)newValue));
			materialAltProcess = getAlternateProcess(materialAltProcess);
			Part part = (Part) getAdObject();
			if (materialAltProcess != null) {
				part.setProcessName(materialAltProcess.getProcessName());
				part.setProcessVersion(materialAltProcess.getProcessVersion());
			}
		} else {
			Part part = null;
			part = searchPart(Long.valueOf((String)newValue));
			if (part == null) {
				initAdObject();
			} else {
				setAdObject(part);
				initAlternateProcess();
			}
		}
		refresh();
	}
	
	protected MaterialAltProcess getAlternateProcess(MaterialAltProcess materialAltProcess) {
		try {			
			ADManager entityManager = Framework.getService(ADManager.class);
			return (MaterialAltProcess) entityManager.getEntity(materialAltProcess);
		} catch (Exception e) {
			logger.error("PartFlowSection searchAlternateProcessEntity(): AlternateProcess isn' t exsited!");
		}
		return null;
	}
	
	protected void initAlternateProcess() {
		try {			
			if(getAdObject() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				List<MaterialAltProcess> list = entityManager.getEntityList(Env.getOrgRrn(), 
						MaterialAltProcess.class, Env.getMaxResult(), "partRrn = " + getAdObject().getObjectRrn(), "processName, processVersion");
				if (list != null && list.size() > 0) {
					processNameRefTableField.setEnabled(true);
				} else {
					processNameRefTableField.setEnabled(false);
				}
				processTableManager.setInput(list);
			} else {			
				processNameRefTableField.setEnabled(false);
				processTableManager.getInput().clear();
			}
		} catch (Exception e) {
			logger.error("NewPartFlowSection searchAlternateProcessEntity(): AlternateProcess isn' t exsited!");
		}
	}
}
