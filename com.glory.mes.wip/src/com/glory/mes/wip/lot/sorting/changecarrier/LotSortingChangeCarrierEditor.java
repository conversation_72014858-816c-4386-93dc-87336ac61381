package com.glory.mes.wip.lot.sorting.changecarrier;

import com.glory.mes.wip.lot.reassign.LotReassignEditor;

/**
 * 用于批次载具变更,将将载具中的批次的Component全部移动到另外一个载具中
 * 批次与Component关系不变,不涉及任何分批和合批作业
 */
public class LotSortingChangeCarrierEditor extends LotReassignEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.sorting.changecarrier.LotSortingChangeCarrierEditor";



}
