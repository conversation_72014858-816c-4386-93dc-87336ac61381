package com.glory.mes.wip.lot.sorting.job.history;

import java.util.LinkedHashMap;
import java.util.List;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.sorting.LotSortingJobHis;

public class SortingJobHistoryEditor extends GlcEditor{

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.sorting.job.history.SortingJobHistoryEditor";

	public QueryFormField queryCustomField;

	public static final String CONTROL_COMPONETN_QUERY = "query";
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);	
		//获取查询界面
		queryCustomField = form.getFieldByControlId(CONTROL_COMPONETN_QUERY, QueryFormField.class);
		
		//查询表格选择事件
		subscribeAndExecute(eventBroker, queryCustomField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
	}
	
	public void queryAdapter(Object object) {
		try {
			queryCustomField.getQueryForm().getQueryForm().removeAllMessages();
			if(!queryCustomField.getQueryForm().getQueryForm().validate()) {
				return;
			}
			LinkedHashMap<String, IField> fields = queryCustomField.getQueryForm().getFields();
			LotManager lotManager = Framework.getService(LotManager.class);
			StringBuffer whereClause = new StringBuffer("");
			int num = 0;
			if(fields != null) {
				for(IField f : fields.values()) {
					Object value = f.getValue();
					if(value != null && !StringUtil.isEmpty(value.toString())) {
						if(num > 0) {
							whereClause.append(" AND ");
						}
						if("lotRrn".equals(f.getId())) {
							Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), f.getValue().toString());
							Long lotRrn = lot != null ? lot.getObjectRrn() : -1;
							whereClause.append(f.getId() + " = '" + lotRrn + "'");
						} else {
							whereClause.append(f.getId() + " = '" + f.getValue() + "'");
						}
						num ++;
					}
				}
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<LotSortingJobHis> sortingJobHis = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobHis.class, Integer.MAX_VALUE, whereClause.toString(), "");
			
			//插入表格
			queryCustomField.getQueryForm().getTableManager().setInput(sortingJobHis);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
