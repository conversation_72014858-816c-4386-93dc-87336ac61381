package com.glory.mes.wip.source.iqc;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.framework.core.exception.ExceptionBundle;

public class IQCWaferSelectListDialog extends BaseTitleDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 500;
	
	public ListTableManager tableManager;
	public List<MComponentUnit> selectComponentUnits = new ArrayList<MComponentUnit>();
	private MLot mLot;
	
	protected IQCWaferSelectListDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public IQCWaferSelectListDialog(Shell parentShell, MLot mLot) {
		this(parentShell);
		this.mLot = mLot;
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setText(Message.getString("mm.iqc_lot_wafer_list"));
	}
	
	@Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }
	
	protected void createFormContent(Composite parent) {
		Composite content = new Composite(parent, SWT.NONE);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setBackground(new Color(Display.getCurrent(), 255, 255, 255));

		try {
			ADManager entityManager = (ADManager)Framework.getService(ADManager.class);	
			ADTable passAdTable = entityManager.getADTable(Env.getOrgRrn(), "MMQueryMComponentUnit");
			tableManager = new ListTableManager(passAdTable, true);
			tableManager.newViewer(content);
			
			String whereCause = " parentMLotRrn = " + mLot.getObjectRrn();
			List<MComponentUnit> compontents = entityManager.getEntityList(Env.getOrgRrn(), MComponentUnit.class, Env.getMaxResult(), whereCause, "");
			tableManager.setInput(compontents);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void okPressed() {
		try {
			List<Object> os = tableManager.getCheckedObject();//获取选中的圆片号
			if (os.size() > 0) {
				for (Object o : os) {
					MComponentUnit mComponentUnit = (MComponentUnit)o;	
					selectComponentUnits.add(mComponentUnit);	
				}
			}				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		close();
	}

	public List<MComponentUnit> getSelectComponentUnits() {
		return selectComponentUnits;
	}

	public void setSelectComponentUnits(List<MComponentUnit> selectComponentUnits) {
		this.selectComponentUnits = selectComponentUnits;
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(Message.getString(ExceptionBundle.bundle.CommonAdd()));
		createFormContent(parent);
		return parent;
	}

	
}
