package com.glory.mes.wip.pp.changewo;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.query.SearchDialog;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class ChangeWorkOrderManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.changewo.ChangeWorkOrderManagerEditor";
	
	public static final String TABLE_NAME = "WIPChangeLot";
	public static final String FIELD_PARTNAME = "partName";
	public static final String FIELD_OLDPARENTID = "oldparentId";
	public static final String FIELD_OLDLOTS = "oldLots";
	public static final String FIELD_ORDERLOTS = "orderLots";

	public static final String BUTTON_NEW = "new";
	public static final String BUTTON_SAVE = "save";
	public static final String BUTTON_NEWPART = "newPart";
	public static final String BUTTON_DELETE = "delete";
	public static final String BUTTON_REFRESH = "refreshEntity";
	public static final String BUTTON_ADD = "add";
	public static final String BUTTON_REMOVE = "remove";
	public static final String BUTTON_CHANGEPART = "changePart";
	
	public static final String BLOCK_REFRESH = "refresh";

	protected RefTableField partNameField;
	protected RefTableField oldparentIdField;
	protected GlcFormField oldlotsField;
	protected ListTableManagerField orderLotsField;

	protected EntityBlock block;
	protected EntityForm basicEntityForm, parentEntityForm;
	protected ToolItem itemNewPart, itemSave, itemDelete;
	protected ListTableManager listTableManager;
	
	protected String newPartId;
    protected String newPartName;
    protected List<Node> lstNode;
	protected BigDecimal generationLotQty = BigDecimal.ZERO;
	protected List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
	protected Map<String,List<Node>> wflistLot = new HashMap<String, List<Node>>();
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		partNameField = form.getFieldByControlId(FIELD_PARTNAME, RefTableField.class);
		
		oldlotsField = form.getFieldByControlId(FIELD_OLDLOTS, GlcFormField.class);
		orderLotsField = oldlotsField.getFieldByControlId(FIELD_ORDERLOTS, ListTableManagerField.class);
		oldparentIdField = oldlotsField.getFieldByControlId(FIELD_OLDPARENTID, RefTableField.class);
		parentEntityForm = (EntityForm) oldlotsField.getForm().getSubForms().get(0);
		parentEntityForm.setObject(new WorkOrder());
		parentEntityForm.loadFromObject();

		listTableManager = orderLotsField.getListTableManager();
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEWPART), this::newPartAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ADD), this::addAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHANGEPART), this::changePartAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BLOCK_REFRESH), this::newAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		init();
	}
	
	private void init() {
		block = (EntityBlock) form.getBlock();
		basicEntityForm = (EntityForm) form.getSubForms().get(0);
		
		itemNewPart = (ToolItem) form.getButtonByControl(null, BUTTON_NEWPART);
		itemSave = (ToolItem) form.getButtonByControl(null, BUTTON_SAVE);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
	}
	
	private void selectionChanged(Object object) {
		try {
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			WorkOrder workOrder = (WorkOrder)selection.getFirstElement();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				workOrder = (WorkOrder) entityManager.getEntity((WorkOrder) workOrder);
				refresh(workOrder);
				
				ADManager adManager = Framework.getService(ADManager.class);
				List<Lot> chageWoLots = new ArrayList<Lot>();
				List<WorkOrderLot> workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(),  " workOrderRrn = " + workOrder.getObjectRrn(), null);
			    BigDecimal generationLotQty = BigDecimal.ZERO;
				for(WorkOrderLot wol :workOrderLots){
					generationLotQty = generationLotQty.add(wol.getMainQty());
					Lot lot = new Lot();
					//Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), wol.getLotId());
					//即使批次对象发生变化（不存在），也显示工单批次信息
					List<Lot> lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, 1, " lotId = '" + wol.getLotId() + "'", null);
					if(lots != null && lots.size() > 0) {
						lot = lots.get(0);
					}else {
						lot.setLotId(wol.getLotId());
					}
					chageWoLots.add(lot);
				}
				setGenerationLotQty(generationLotQty);
				setWorkOrderLots(workOrderLots);
				workOrder.setWorkOrderLots(workOrderLots);
				listTableManager.setInput(chageWoLots);
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}

	private void newAdapter(Object object) {
		statusChanged(null, null);
		basicEntityForm.setObject(new WorkOrder());
		basicEntityForm.loadFromObject();
		parentEntityForm.setObject(new WorkOrder());
		parentEntityForm.loadFromObject();
		listTableManager.setInput(new ArrayList<Object>());
		listTableManager.refresh();
		setWflistLot(new HashMap<String, List<Node>>());
		setWorkOrderLots(new ArrayList<WorkOrderLot>());
	}

	private void saveAdapter(Object object) {
		try {
			basicEntityForm.getMessageManager().removeAllMessages();
			parentEntityForm.getMessageManager().removeAllMessages();
			if (basicEntityForm.saveToObject() && parentEntityForm.saveToObject()) {
				ADBase oldBase = (ADBase) basicEntityForm.getObject();
				ADBase obj = save((ADBase) basicEntityForm.getObject());
				if(obj == null){
					return;
				}
				ADManager entityManager = getADManger();
				ADBase newBase = entityManager.getEntity(obj);
				if (basicEntityForm.getADTable() != null && basicEntityForm.getADTable().isContainAttribute()) {
					newBase.setAttributeValues(entityManager.getEntityAttributeValues(basicEntityForm.getADTable().getModelName(),
							newBase.getObjectRrn().longValue()));
				}
				if (oldBase.getObjectRrn() == null)
					block.refreshAdd(newBase);
				else {
					block.refreshUpdate(newBase);
				}

				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
				refreshAdapter(object);
				newAdapter(object);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public ADBase save(ADBase obj) throws Exception {
		PpManager ppManager = Framework.getService(PpManager.class);
		BigDecimal totalMainQty = BigDecimal.ZERO; 
		List<WorkOrderLot> workOrderLots = (List<WorkOrderLot>)getWorkOrderLots();
		if(workOrderLots != null && workOrderLots.size() > 0){
			for (WorkOrderLot woLot : workOrderLots) {
				totalMainQty = totalMainQty.add(woLot.getMainQty());
			}
		} else {
			UI.showInfo(Message.getString(WipExceptionBundle.bundle.WoLotsNull()));
			return null;
		}
		WorkOrder workOrder = (WorkOrder) obj;
		workOrder.setOrgRrn(Env.getOrgRrn());
		workOrder.setParentId(oldparentIdField.getValue().toString());
		// 判断数量
		if (totalMainQty.compareTo(workOrder.getMainQty()) != 0) {
			UI.showError(Message.getString(WipExceptionBundle.bundle.WoMainQtyNotMatch()));
			return null;
		}
		workOrder.setWorkOrderLots(workOrderLots);
		obj =  ppManager.saveWorkOrder(workOrder, null, null, false, true, false, false, Env.getSessionContext());
		return obj;
		
	}

	private void newPartAdapter(Object object) {
		try {
			ADBase adBase = (ADBase) basicEntityForm.getObject();
			if(adBase != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				PrdManager prdManager = Framework.getService(PrdManager.class);
				PpManager ppManager = Framework.getService(PpManager.class);
				WorkOrder workOrder = (WorkOrder) adBase;
				//改产品的工艺流程
				Map<String,List<Node>> wflistLot = getWflistLot();
				if(wflistLot.isEmpty() || wflistLot.size() == 0) {
					UI.showError(Message.getString(WipExceptionBundle.bundle.ChangeWoNoSelectProduce()));
					return;
				}
				//获取转头工单转投批次信息
				List<WorkOrderLot> workOrderLots = workOrder.getWorkOrderLots();
				if(workOrderLots != null && workOrderLots.size() > 0) {
					List<Lot> changePartLots = new ArrayList<Lot>();
					for(WorkOrderLot workOrderLot : workOrderLots) {
						Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), workOrderLot.getLotId());
						if(lot != null) {
							lot.setOperator1(Env.getUserName());
							
							Part newPart = prdManager.getPartById(Env.getOrgRrn(), workOrder.getPartName(), workOrder.getPartVersion());
							lot.setAttribute1(newPart);
							
							//获取每个转投工单批次的工艺流程信息[ProcedureState(ZXMODEL2), StepState(ZX_STEP_01)]
							List<Node> flowList = wflistLot.get(lot.getLotId());
							lot.setAttribute2(flowList);
							changePartLots.add(lot);
						}else {
							UI.showError(Message.getString(WipExceptionBundle.bundle.ChangeWoLotIsNotExit()));
							return;
						}
					}
					workOrder = ppManager.changeWorkOrder(workOrder, changePartLots, true, Env.getSessionContext());
					UI.showInfo(Message.getString(WipExceptionBundle.bundle.ChangeWoLotSuccess()));
					
					block.refreshUpdate(workOrder);
					newAdapter(object);
				}else {
					UI.showError(Message.getString(WipExceptionBundle.bundle.ChangeWoLotIsNotExit()));
					return;
				}
			}else {
				UI.showError(Message.getString(WipExceptionBundle.bundle.ChangeWoPleaseSelectWorkOrder()));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    
	}

	private void deleteAdapter(Object object) {
        try {
            WorkOrder workOrder = (WorkOrder) basicEntityForm.getObject();
            if (workOrder != null && workOrder.getObjectRrn() != null) {
            	boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
    			if (confirmDelete) {
    				PpManager ppManager = Framework.getService(PpManager.class);
                    ppManager.deleteWorkOrder(workOrder, Env.getSessionContext());
                    UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
                    block.refreshDelete(workOrder);
                    newAdapter(object);
    			}
            } else {
                UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
	}

	private void refreshAdapter(Object object) {
		WorkOrder workOrder = (WorkOrder) basicEntityForm.getObject();
		if (workOrder != null && workOrder.getObjectRrn() != null) {
			statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
		} else {
			statusChanged(null, null);
		}
	}
	
	private void refresh(WorkOrder workOrder) {
		basicEntityForm.setObject(workOrder);
		basicEntityForm.loadFromObject();
		parentEntityForm.setObject(workOrder);
		parentEntityForm.loadFromObject();
		oldparentIdField.setValue(workOrder.getParentId());
		oldparentIdField.refresh();
	}

	private void addAdapter(Object object) {
		try {
			List<ADBase> values = (List<ADBase>) listTableManager.getInput();
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ListTableManager queryTableManager = new ListEditorTableManager(adTable, true);
			
			if (StringUtil.isEmpty(oldparentIdField.getText())) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.SelectOldWorkOrder()));
				return;
			}
			
			String whereClause = " state = '" + LotStateMachine.STATE_WAIT + "' and holdState = 'Off' and woId = '" + oldparentIdField.getText() +"'";
		    List<Lot> getAvailableMLots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Integer.MIN_VALUE, Integer.MAX_VALUE, whereClause, " updated desc");
		    if (getAvailableMLots == null || getAvailableMLots.isEmpty()) {
		    	UI.showError(Message.getString(WipExceptionBundle.bundle.WorkOrderNotHaveFinLot()));
				return;
		    }		
		    
		    queryTableManager.getInput().clear();
		    queryTableManager.setInput(getAvailableMLots);
		    
			SearchDialog searchDialog = new ChangeWorkOrderLotSearchDialog(queryTableManager, queryTableManager.getADTable().getInitWhereClause(), whereClause);
			if (searchDialog.open() == 0) {
				List<ADBase> adBases = searchDialog.getSelectionItems();
				for (ADBase adBase : adBases) {
					Lot lot = (Lot) adBase;
					if (isAddLot(values, lot)) {
						// 创建 wip_wo_lot 
						WorkOrderLot workOrderLot = new WorkOrderLot();
						
						workOrderLot.setLotId(lot.getLotId());
						workOrderLot.setLotType(lot.getLotType());
						workOrderLot.setDurableId(lot.getDurable());
						
						workOrderLot.setMainQty(lot.getMainQty());
						workOrderLot.setSubQty(lot.getSubQty());
						
						workOrderLot.setReserved1(lot.getWoId());// 父工单
						workOrderLot.setReserved2(lot.getState());// 状态
						workOrderLot.setReserved3(lot.getPartName());// 产品
						workOrderLots.add(workOrderLot);
						
						values.add(lot);
						
						List<Lot> lots = new ArrayList<Lot>();
						for (ADBase base : values) {
							lots.add((Lot)base);
						}
						listTableManager.setInput(lots);
					} else {
						UI.showInfo(lot.getLotId() + Message.getString(WipExceptionBundle.bundle.WoLotIdRepeat()));
						break;
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	
	}

	private void changePartAdapter(Object object) {
    	try {
    		//获取勾选的转投批次
	        List<Object> os = listTableManager.getCheckedObject();//WIPNewPart
	        if (CollectionUtils.isEmpty(os)) {
	        	UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
	        newPartName = !StringUtil.isEmpty(partNameField.getValue().toString()) ? partNameField.getValue().toString(): "";
	        if(StringUtil.isEmpty(newPartName) || newPartName == null) {
	        	UI.showError(Message.getString(WipExceptionBundle.bundle.ChangeWoNoSelectPart()));
				return;
	        }
			Lot lot = new Lot();
			lot.setPartName(newPartName);
			ChangeWorkOrderPartDialog dialog = new ChangeWorkOrderPartDialog("ChangeWorkOrderPartDialog", null, eventBroker, lot);
			if(dialog.open() == Dialog.OK) {
				lstNode = dialog.getFlowList();
				if(lstNode != null) {
					for(Object object1 : os) {
						Lot newPartLot = (Lot) object1;
						wflistLot.put(newPartLot.getLotId(), lstNode);
						//填充目标产品、工序号到表格中栏位中
						newPartLot.setAttribute1(newPartName);
						for(Node node : lstNode) {
							if(node instanceof StepState) {
								StepState stepState = (StepState) node;
								newPartLot.setAttribute2(stepState.getStepName() + "." + stepState.getUsedStepVersion());
							}
						}
					}
					listTableManager.refresh();
				}else {
					UI.showError(Message.getString(WipExceptionBundle.bundle.ChangeWoNoSelectProduce()));
					return;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    
	}
	
	private void removeAdapter(Object object) {
        List<ADBase> list = (List<ADBase>) listTableManager.getInput();
        List<Object> os = listTableManager.getCheckedObject();
        for (Object o : os) {
            ADBase pe = (ADBase) o;
            Lot lot = (Lot) pe;
            List<WorkOrderLot> workLots = workOrderLots.stream().filter(t -> lot.getLotId().equals(t.getLotId())).collect(Collectors.toList());
            if(workLots != null && workLots.size() > 0) {
            	 workOrderLots.removeAll(workLots);
            }
            list.remove(pe);
        }
        List<Lot> lots = new ArrayList<Lot>();
        for (ADBase adBase : list) {
        	lots.add((Lot)adBase);
        }
        listTableManager.setInput(lots);
	}
	
	public void statusChanged(String docStatus, String holdStatus) {
		if (Documentation.STATUS_CREATED.equals(docStatus)) {
            itemSave.setEnabled(true);
            itemDelete.setEnabled(true);
        } else if (Documentation.STATUS_APPROVED.equals(docStatus)) {
        	itemSave.setEnabled(false);
            itemDelete.setEnabled(false);
        } else if (WorkOrder.STATUS_STARTED.equals(docStatus)) {
        	itemSave.setEnabled(false);
            itemDelete.setEnabled(false);
        } else if (WorkOrder.STATUS_CLOSED.equals(docStatus)) {
        	itemSave.setEnabled(false);
            itemDelete.setEnabled(false);
        } else {
        	itemSave.setEnabled(true);
            itemDelete.setEnabled(false);
        }
		if (Documentation.STATUS_CREATED.equals(docStatus) || 
			Documentation.STATUS_APPROVED.equals(docStatus)) {
			itemNewPart.setEnabled(true);
		}else {
			itemNewPart.setEnabled(false);
		}
	}
	
	 /**
     * 判断是否有重复的批次
     * */
    public boolean isAddLot(List<ADBase> values, Lot lot){
    	for (ADBase val : values) {
			if(lot.getLotId().equals(((Lot)val).getLotId())){
				return false;
			}
		}
    	return true;
    }

	public BigDecimal getGenerationLotQty() {
		return generationLotQty;
	}

	public void setGenerationLotQty(BigDecimal generationLotQty) {
		this.generationLotQty = generationLotQty;
	}

	public List<WorkOrderLot> getWorkOrderLots() {
		return workOrderLots;
	}

	public void setWorkOrderLots(List<WorkOrderLot> workOrderLots) {
		this.workOrderLots = workOrderLots;
	}

	public Map<String, List<Node>> getWflistLot() {
		return wflistLot;
	}

	public void setWflistLot(Map<String, List<Node>> wflistLot) {
		this.wflistLot = wflistLot;
	}

}