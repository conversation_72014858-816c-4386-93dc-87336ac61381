package com.glory.mes.wip.pp.wo.form;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;

/**
 * 显示工单所对应的批次信息
 * 列表可勾选
 */
public class WorkOrderLotSelectForm extends WorkOrderLotForm {

	private static String TABLE_NAME = "PPWorkOrderLotsSelect";	

	private ListTableManager tableManager;
	
	public WorkOrderLotSelectForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}	

	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		ADManager adManager;
		try {
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
	        tableManager = new ListTableManager(adTable, true);
	        tableManager.setIndexFlag(true);
	        tableManager.newViewer(body);
	        tableManager.addICheckChangedListener(new ICheckChangedListener() {
				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if (checked) {
						if(null == eventObjects || eventObjects.isEmpty()) {
							return;
						}
						for (Object eventObject: eventObjects) {
							WorkOrderLot workOrderLot = (WorkOrderLot) eventObject;
							if (WorkOrder.STATUS_STARTED.equals(workOrderLot.getState())) {
								tableManager.setCheckedObject(workOrderLot);
							} else {
								tableManager.getCheckedObject().remove(workOrderLot);
							}
						}
					}
				}			
			});	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}	
	
	@Override
	public void loadFromObject() {
		if (object != null && tableManager != null) {
			tableManager.setInput(buildWorkOrderLots());
			tableManager.refresh();
		}
	}
	
	public List<WorkOrderLot> getWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (tableManager != null) {
			@SuppressWarnings("unchecked")
			List<Object> workOrderLotObjs = (List<Object>) tableManager.getInput();;
			for (Object obj : workOrderLotObjs) {
				WorkOrderLot workOrderLot = (WorkOrderLot) obj;
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
	}
	
	public List<WorkOrderLot> getCheckedWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (tableManager != null) {
			List<Object> workOrderLotObjs = (List<Object>) tableManager.getCheckedObject();
			for (Object obj : workOrderLotObjs) {
				WorkOrderLot workOrderLot = (WorkOrderLot) obj;
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
	}
	
	@Override
	public void setInput(List<? extends Object> input, boolean isReMap) {
		if (tableManager != null) {
			tableManager.setInput(input);
			tableManager.refresh();
		}
	}
	
	@Override
	public void setInput(List<? extends Object> input) {
		tableManager.setInput(input);
		tableManager.refresh();
	}
}
