package com.glory.mes.wip.pp.wo.form;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;

/**
 * 批次投料,在投料时同时需输入每个批次的载具
 */
public class WorkOrderLotStartByDurableForm extends EntityForm {
	
    private static final String TABLE_NAME = "PPWorkOrderLotStartByDurable";

	private ListTableManager tableManager;
	
	public WorkOrderLotStartByDurableForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}
	
	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		ADManager adManager;
		try {
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
	        tableManager = new ListEditorTableManager(adTable, false);
	        tableManager.setIndexFlag(true);
	        tableManager.newViewer(body);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void loadFromObject() {
		if (object != null && tableManager != null) {
			tableManager.setInput(buildWorkOrderLots());
			tableManager.refresh();
		}
	}
	
	protected List<WorkOrderLot> buildWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<>();
		WorkOrder workOrder = (WorkOrder) object;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(), 
	                " (durableId IS NULL or durableId = '') AND workOrderRrn = " + workOrder.getObjectRrn(), null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return workOrderLots; 
	}
	
	public List<WorkOrderLot> getWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (tableManager != null) {
			@SuppressWarnings("unchecked")
			List<Object> workOrderLotObjs = (List<Object>) tableManager.getInput();;
			for (Object obj : workOrderLotObjs) {
				WorkOrderLot workOrderLot = (WorkOrderLot) obj;
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public boolean saveToObject() {
		if (object != null) {
			WorkOrder workOrder = (WorkOrder) object;
			if (workOrder.getObjectRrn() != null) {
				if (!validate()) {
					return false;
				}
				List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
				if (tableManager.getInput() != null) {
					startLots.addAll((List<WorkOrderLot>) tableManager.getInput());
				}

				if (workOrder.getMainQty() == null) {
					return false;
				}
				workOrder.setWorkOrderLots(startLots);
			}
		}
		return true;
	}
}
