package com.glory.mes.wip.pp.wo.sub;

import java.awt.Toolkit;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADAttributeValue;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * 生成子工单批次
 */
public class GeneratorWoLotForm extends EntityForm {

	protected ADTable lotAdTable;
	protected ListTableManager lotTableManager;
	protected Text txtLotSize;
	protected Text txtLotCount;
	protected Text lotIdText;
	
	public static final String TABLE_NAME = "WIPGeneratorWorkOrderLot";

	protected static String regEx = "^[A-Za-z0-9-_]+$";

	public GeneratorWoLotForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);

		Composite top = toolkit.createComposite(body);
		top.setLayout(new GridLayout(8, false));
		GridData gd = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		top.setLayoutData(gd);

		GridData gText = new GridData(GridData.FILL_HORIZONTAL);
		gText.widthHint = 30;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			toolkit.createLabel(top, Message.getString("common.lotqty"));
			txtLotSize = toolkit.createText(top, "", SWT.NONE);
			txtLotSize.setLayoutData(gText);
			txtLotSize.setTextLimit(32);
			txtLotSize.setText("25");

			gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 30;
			toolkit.createLabel(top, Message.getString("common.lot_number"));
			txtLotCount = toolkit.createText(top, "", SWT.NONE);
			txtLotCount.setLayoutData(gText);
			txtLotCount.setTextLimit(32);
			txtLotCount.setText("1");

			gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 120;
			toolkit.createLabel(top, Message.getString("wip.lot_id"));
			lotIdText = toolkit.createText(top, "", SWT.NONE);
			lotIdText.setLayoutData(gText);
			lotIdText.setTextLimit(32);

			lotIdText.addVerifyListener(new VerifyListener() {
				public void verifyText(VerifyEvent e) {
					// 正则表达式验证
					Pattern pattern = Pattern.compile(regEx);
					Matcher matcher = pattern.matcher(e.text);
					if (matcher.matches()) {
						// 允许大小写字母、中划线、下划线、数字
						e.doit = true;
					} else if (e.text.length() > 0) {
						// 其它情况屏蔽
						e.doit = false;
					} else {
						// 控制键
						e.doit = true;
					}
				}
			});
			
			SquareButton addLotBtn =  UIControlsFactory.createButton(top, Message.getString(ExceptionBundle.bundle.CommonAdd()), "");
			GridData gBtn = new GridData(GridData.FILL_VERTICAL);
			gBtn.heightHint = 30;
			addLotBtn.setLayoutData(gBtn);
			addLotBtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					addLotAdapter();
				}
			});

			SquareButton removeLotbtn =  UIControlsFactory.createButton(top, Message.getString(ExceptionBundle.bundle.CommonDelete()), "");
			removeLotbtn.setLayoutData(gBtn);
			removeLotbtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					List<Object> removeWLots = lotTableManager.getCheckedObject();
					for (Object removeWoLot : removeWLots) {
						WorkOrderLot pre = (WorkOrderLot) removeWoLot;
						((List<WorkOrderLot>) lotTableManager.getInput()).remove(pre);
					}
				}
			});
			Composite tableCom = toolkit.createComposite(body);
			tableCom.setLayout(new GridLayout(1, false));
			GridData tablegd = new GridData(GridData.FILL_BOTH);
			tableCom.setLayoutData(tablegd);

			lotAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotTableManager = new ListTableManager(lotAdTable, true);
			lotTableManager.setIndexFlag(true);
			lotTableManager.newViewer(tableCom);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void addLotAdapter() {
		try {
			boolean haveSubQty = false;
			WorkOrder workOrder = (WorkOrder) getObject();
			if (workOrder.getSubQty() != null && BigDecimal.ZERO.compareTo(workOrder.getSubQty()) < 0) {
				haveSubQty = true;
			}
			
			if (lotTableManager.getInput() != null) {
				for (WorkOrderLot woLot : ((List<WorkOrderLot>) lotTableManager.getInput())) {
					if (lotIdText.getText().equalsIgnoreCase(woLot.getLotId())) {
						UI.showError(Message.getString("wip.lotid_repeat"));
						return;
					}
				}
			}
			List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
			if (lotTableManager.getInput() != null) {
				workOrderLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
			}
			long lotNumber = Long.parseLong(txtLotCount.getText());
			BigDecimal mainQty = new BigDecimal(txtLotSize.getText());
			for (int i = 0; i < lotNumber; i++) {
				WorkOrderLot workOrderLot = new WorkOrderLot();
				workOrderLot.setOrgRrn(Env.getOrgRrn());
				workOrderLot.setIsActive(true);
				if (lotIdText.getText() != null && !"".equals(lotIdText.getText())) {
					String lotId = lotIdText.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					workOrderLot.setLotId(lotId);
				}

				workOrderLot.setMainQty(mainQty);
				if (haveSubQty) {
					// 根据工单比例得出批次子数量
					workOrderLot.setSubQty(workOrder.getSubQty().divide(workOrder.getMainQty(), 0, BigDecimal.ROUND_UP).multiply(mainQty));
				}
				
				workOrderLots.add(workOrderLot);
			}
			
			lotTableManager.setInput(workOrderLots);
		} catch (Exception e) {
			e.printStackTrace();
			UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInvalidNumber()));
		}
	
	}
	
	@Override
	public List<String> getCopyProperties() {
		List<String> properties = new ArrayList<String>();
		properties.add("workOrderLots");
		return properties;
	}
	
	@Override
	public boolean saveToObject() {
		if (object != null){
			WorkOrder workOrder = (WorkOrder) object;
			if (!validate()){
				return false;
			}
			 List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
             if (lotTableManager.getInput() != null) {
                 startLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
             }
             BigDecimal generationLotQty = BigDecimal.ZERO;
             for (WorkOrderLot woLot : startLots) {
                 generationLotQty = generationLotQty.add(woLot.getMainQty());
             }
             //检查工单数量和批次总数量是否一致
             if (workOrder.getMainQty() != null && generationLotQty.compareTo(workOrder.getMainQty()) != 0) {
                 UI.showError(Message.getString("wip.wo_qty_match_total_lot_qty"));
                 return false;
             }                        
             workOrder.setWorkOrderLots(startLots);
		}
		return true;
    }
	
	@Override
	public void loadFromObject() {
		lotTableManager.getInput().clear();
		
		if (object != null) {
			try {
				WorkOrder order = (WorkOrder) object;
				ADManager adManager = Framework.getService(ADManager.class);
				List<WorkOrderLot> workOrderLots = adManager.getEntityList(Env.getOrgRrn(),
	                    WorkOrderLot.class, Env.getMaxResult(),
	                    " workOrderRrn = " + order.getObjectRrn(), null);
	            lotTableManager.setInput(workOrderLots);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	public List<WorkOrderLot> getWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (lotTableManager != null) {
			List<WorkOrderLot> lots = (List<WorkOrderLot>) lotTableManager.getInput();
			for (WorkOrderLot workOrderLot : lots) {
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
	}
	
	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
