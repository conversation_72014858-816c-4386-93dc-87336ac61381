package com.glory.mes.wip.pp.wo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.query.SearchDialog;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.mm.MaterialRequisitionLine;

public class WorkOrderMLotTableSelectField extends TableSelectField {

    private BigDecimal generationLotQty = BigDecimal.ZERO;
    private static final String TABLE_NAME = "PPWorkOrderMLotSelectDialog";
    protected String whereClause;
    private boolean isVerQty = false;
    private List<MaterialRequisitionLine> materialRequisitionLines = null;
    
    public WorkOrderMLotTableSelectField(String id, ListTableManager tableManager, String whereClause) {
        super(id, tableManager, whereClause);
        this.whereClause = whereClause;
    }

    @SuppressWarnings("unchecked")
	public void add() {
		try {
			if (BigDecimal.ZERO.compareTo(generationLotQty) == 0) {
				UI.showInfo(Message.getString("wip.wo_add_lot"));
				return;
			}
			if (materialRequisitionLines == null) {
				UI.showInfo(Message.getString("wip.wo_create_bom"));
				return;
			}

			List<ADBase> values = (List<ADBase>) getValue() == null ? new ArrayList<ADBase>() : (List<ADBase>) getValue();
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ListTableManager listTableManager = new ListEditorTableManager(adTable, true);

			SearchDialog searchDialog = new WorkOrderMLotSearchDialog(listTableManager, adTable.getInitWhereClause(), whereClause);
			if (searchDialog.open() == 0) {
				List<ADBase> adBases = searchDialog.getSelectionItems();
				for (ADBase adBase : adBases) {
					MLot mLot = (MLot) adBase;
					if (isAddMLot(values, mLot)) {
						if(!isVerQty){
							mLot.setTransMainQty(getMaxQty(materialRequisitionLines, mLot));
						}else{
							// 最大用量
							BigDecimal maxQty = getMaxQty(materialRequisitionLines, mLot);
							// 现有用量
							BigDecimal hasQtye = getHasQty(values, mLot);
							BigDecimal subQty = maxQty.subtract(hasQtye);
							if(subQty.compareTo(BigDecimal.ZERO)==0){
								UI.showInfo(mLot.getMaterialName() + Message.getString("wip.wo_lot_qty_enough"));
								break;
							}
							if (mLot.getMainQty().compareTo(subQty) == -1) {
								mLot.setTransMainQty(mLot.getMainQty());
							} else {
								mLot.setTransMainQty(maxQty.subtract(hasQtye));
							}
						}
						values.add(mLot);
						this.setValue(values);
					}else{
						UI.showInfo(mLot.getmLotId() + Message.getString("wip.wo_lotid_repeat"));
						break;
					}
				}
				this.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
    
    /**
     * 判断是否有重复的MaterialRrn
     * */
    public boolean isAddMLot(List<ADBase> values, MLot mLot){
    	for (ADBase val : values) {
			if(mLot.getmLotId().equals(((MLot)val).getmLotId())){
				return false;
			}
		}
    	return true;
    }
    
    /**
     * 获得现用数量
     * */
    public BigDecimal getHasQty(List<ADBase> values, MLot mLot){
    	BigDecimal hasQtye = BigDecimal.ZERO;
    	for(ADBase adBase : values){
    		MLot ml = (MLot)adBase;
    		if(mLot.getMaterialName().equals(ml.getMaterialName())){
    			hasQtye = hasQtye.add(ml.getTransMainQty());
    		}
    	}
    	return hasQtye;
    }

    /**
     * 获得最大用量
     * */
	public BigDecimal getMaxQty(List<MaterialRequisitionLine> materialRequisitionLines, MLot mLot){
    	for(MaterialRequisitionLine materialRequisitionLine : materialRequisitionLines){
    		if(materialRequisitionLine.getMaterialName().equals(mLot.getMaterialName())){
    			return materialRequisitionLine.getLineMaxQty();
    		}
    	}
    	return BigDecimal.ZERO;
    }

    @SuppressWarnings("unchecked")
    public void delete() {
        List<ADBase> list = (List<ADBase>) getValue();
        List<Object> os = this.getTableManager().getCheckedObject();
        for (Object o : os) {
            ADBase pe = (ADBase) o;
            list.remove(pe);
        }
        filter(list);
    }

    public BigDecimal getGenerationLotQty() {
        return generationLotQty;
    }

    public void setGenerationLotQty(BigDecimal generationLotQty) {
        this.generationLotQty = generationLotQty;
    }
    
    public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	public boolean isVerQty() {
		return isVerQty;
	}

	public void setVerQty(boolean isVerQty) {
		this.isVerQty = isVerQty;
	}

	public List<MaterialRequisitionLine> getMaterialRequisitionLines() {
		return materialRequisitionLines;
	}

	public void setMaterialRequisitionLines(List<MaterialRequisitionLine> materialRequisitionLines) {
		this.materialRequisitionLines = materialRequisitionLines;
	}
}
