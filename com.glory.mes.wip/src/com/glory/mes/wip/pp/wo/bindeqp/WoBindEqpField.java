package com.glory.mes.wip.pp.wo.bindeqp;

import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrderEqp;
import com.glory.mes.ras.eqp.Equipment;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class WoBindEqpField extends TableSelectField {

	protected List<WorkOrderEqp> values;
	private Equipment equipment;

	private SquareButton active, inActive, up, down;

	public WoBindEqpField(String id, ListTableManager tableManager) {
		super(id, tableManager);
		statusChange();
	}

	private void statusChange() {
		getTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				IStructuredSelection ss = event.getStructuredSelection();
				WorkOrderEqp orderEqp = (WorkOrderEqp) ss.getFirstElement();
				if (orderEqp == null) {
					active.setEnabled(false);
					inActive.setEnabled(false);
					up.setEnabled(false);
					down.setEnabled(false);
					delete.setEnabled(false);
					return;
				}
				
				if (WorkOrderEqp.STATUS_ACTIVE.equals(orderEqp.getStatus())) {
					active.setEnabled(false);
					inActive.setEnabled(true);
					up.setEnabled(true);
					down.setEnabled(true);
					delete.setEnabled(false);
				} else {
					active.setEnabled(true);
					inActive.setEnabled(false);
					up.setEnabled(true);
					down.setEnabled(true);
					delete.setEnabled(true);
				}
			}
		});
	}

	public void createButtons(FormToolkit toolkit, Composite composite) {
		Composite bn = toolkit.createComposite(composite, SWT.NULL);
		bn.setLayout(new GridLayout(7, false));
		GridData g = new GridData();
		g.horizontalAlignment = GridData.END;
		bn.setLayoutData(g);
		add = UIControlsFactory.createButton(bn, Message.getString("wip.assign"), UIControlsFactory.BUTTON_DEFAULT);
		delete = UIControlsFactory.createButton(bn, Message.getString("wip.deassign"), UIControlsFactory.BUTTON_DEFAULT);

		active = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonActive()), UIControlsFactory.BUTTON_DEFAULT);
		inActive = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonInActive()), UIControlsFactory.BUTTON_DEFAULT);

		up = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonUp()), UIControlsFactory.BUTTON_DEFAULT);
		down = UIControlsFactory.createButton(bn, Message.getString(ExceptionBundle.bundle.CommonDown()), UIControlsFactory.BUTTON_DEFAULT);

		active.setEnabled(false);
		inActive.setEnabled(false);
		up.setEnabled(false);
		down.setEnabled(false);
		add.setEnabled(false);
		delete.setEnabled(false);
		
		decorateButton(add);
		decorateButton(delete);
		decorateButton(active);
		decorateButton(inActive);
		decorateButton(up);
		decorateButton(down);

		add.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				add();

			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);

			}
		});

		delete.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				delete();
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);

			}
		});

		active.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				active();

			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);

			}
		});

		inActive.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				inActive();

			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);

			}
		});

		up.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				up();

			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);

			}
		});

		down.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				down();

			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);

			}
		});

	}
	
	protected void active() {
		Object obj = getTableManager().getSelectedObject();
		if (obj != null) {
			try {
				WorkOrderEqp orderEqp = (WorkOrderEqp) obj;

				PpManager ppManager = Framework.getService(PpManager.class);
				ppManager.activeWorkOrderEqp(orderEqp, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
				
				orderEqpFilter();
			} catch (Exception e1) {
				e1.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e1);
			}
		}

	}

	protected void inActive() {
		Object obj = getTableManager().getSelectedObject();
		if (obj != null) {
			try {
				WorkOrderEqp orderEqp = (WorkOrderEqp) obj;

				PpManager ppManager = Framework.getService(PpManager.class);
				ppManager.inActiveWorkOrderEqp(orderEqp, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框
				
				orderEqpFilter();
			} catch (Exception e1) {
				e1.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e1);
			}
		}
	}

	public void up() {
		Object obj = getTableManager().getSelectedObject();
		if (obj != null) {
			try {
				WorkOrderEqp orderEqp = (WorkOrderEqp) obj;

				PpManager ppManager = Framework.getService(PpManager.class);
				ppManager.moveWorkOrderEqp(orderEqp, -1, Env.getSessionContext());
				
				orderEqpFilter();
			} catch (Exception e1) {
				e1.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e1);
			}
		}

	}

	public void down() {
		Object obj = getTableManager().getSelectedObject();
		if (obj != null) {
			try {
				WorkOrderEqp orderEqp = (WorkOrderEqp) obj;

				PpManager ppManager = Framework.getService(PpManager.class);
				ppManager.moveWorkOrderEqp(orderEqp, 1, Env.getSessionContext());

				orderEqpFilter();
			} catch (Exception e1) {
				e1.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e1);
			}
		}
	}

	@Override
	public void doubleClick() {
	}

	@Override
	public void add() {
		if (equipment == null) {
			return;
		}
		
		WorkOrderEqp workOrderEqp = new WorkOrderEqp();
		workOrderEqp.setEquipmentId(equipment.getEquipmentId());
		workOrderEqp.setEquipmentRrn(equipment.getObjectRrn());
		WoBindEqpEditDialog editManager = new WoBindEqpEditDialog(getTableManager().getADTable(), workOrderEqp, values);
		if (editManager.open() == IDialogConstants.OK_ID) {
			orderEqpFilter();
		}

	}

	@Override
	public void delete() {
		Object obj = getTableManager().getSelectedObject();
		if (obj != null) {
			try {
				if (UI.showConfirm(Message.getString("ras.tool_detach_confirm"))) {
					WorkOrderEqp orderEqp = (WorkOrderEqp) obj;

					PpManager ppManager = Framework.getService(PpManager.class);
					ppManager.unBindingWorkOrderEqp(orderEqp.getWoId(), orderEqp.getEquipmentId(),
							Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// 弹出提示框

					orderEqpFilter();
				}
			} catch (Exception e1) {
				e1.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e1);
			}
		}
	}

	public void orderEqpFilter() {
		try {
			// 重新从后台获取数据
			PpManager manager = Framework.getService(PpManager.class);
			values = manager.getWorkOrderEqpsByEquipmentId(Env.getOrgRrn(), equipment.getEquipmentId());
			values = values == null ? Lists.newArrayList() : values;
			setValue(values);
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public Equipment getEquipment() {
		return equipment;
	}

	public void setEquipment(Equipment equipment) {
		this.equipment = equipment;
		this.add.setEnabled(true);
		orderEqpFilter();
	}

}
