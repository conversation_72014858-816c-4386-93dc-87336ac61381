package com.glory.mes.wip.pp.wo.bom;

import java.util.List;

import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.mm.MaterialRequisitionLine;

public class WorkOrderBomContext {
	
	/**
	 * 是否展开半成品
	 * 即在多级BOM时(半成品BOM),是否展开到最底层的原物料
	 * 默认为false,即只展开到下级物料
	 */
	private boolean isExpandProduct = false;

	private WorkOrder workOrder;
	
	private List<WorkOrderBomLine> workOrderBomLines;
	
	private List<MaterialRequisitionLine> materialRequisitionLines;
	
	public WorkOrderBomContext() {}

	public boolean isExpandProduct() {
		return isExpandProduct;
	}

	public void setExpandProduct(boolean isExpandProduct) {
		this.isExpandProduct = isExpandProduct;
	}
	
	public WorkOrder getWorkOrder() {
		return workOrder;
	}

	public void setWorkOrder(WorkOrder workOrder) {
		this.workOrder = workOrder;
	}

	public List<WorkOrderBomLine> getWorkOrderBomLines() {
		return workOrderBomLines;
	}

	public void setWorkOrderBomLines(List<WorkOrderBomLine> workOrderBomLines) {
		this.workOrderBomLines = workOrderBomLines;
	}

	public List<MaterialRequisitionLine> getMaterialRequisitionLines() {
		return materialRequisitionLines;
	}

	public void setMaterialRequisitionLines(List<MaterialRequisitionLine> materialRequisitionLines) {
		this.materialRequisitionLines = materialRequisitionLines;
	}

}
