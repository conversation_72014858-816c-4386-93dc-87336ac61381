package com.glory.mes.wip.pp.wo.sub;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.pp.wo.WorkOrderProperties;

public class SubWorkOrderProperties extends WorkOrderProperties {
	
	private static final Logger logger = Logger.getLogger(SubWorkOrderProperties.class);

	public SubWorkOrderProperties() {
		super();
	}
    
	@Override
	public ADBase save(ADBase obj) throws Exception {
		PpManager ppManager = Framework.getService(PpManager.class);
		WorkOrder workOrder = (WorkOrder) obj;
		workOrder.setDocStatus(WorkOrder.STATUS_CREATED);
		workOrder.setDocType(WorkOrder.DOC_TYPE_WO);
		
		//更父工单id获得WorkOrder
		WorkOrder parentWorkOrder = new WorkOrder();
		parentWorkOrder.setDocId(workOrder.getParentId());
		parentWorkOrder = ppManager.getWorkOrder(parentWorkOrder, Env.getSessionContext());
		
		//获得BomLine
		List<WorkOrderBomLine> parentBomLines = new ArrayList<WorkOrderBomLine>();
		parentBomLines = ppManager.getWorkOrderBomLines(parentWorkOrder, Env.getSessionContext());

		obj = ppManager.saveSubWorkOrder(workOrder, parentBomLines, Env.getSessionContext());
		return obj;
	}
    
}
