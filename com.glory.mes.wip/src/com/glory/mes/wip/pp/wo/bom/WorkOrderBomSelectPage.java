package com.glory.mes.wip.pp.wo.bom;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLineTree;
import com.glory.mes.mm.bom.tree.BomTreeForm;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;

public class WorkOrderBomSelectPage extends FlowWizardPage {

	private static String BOM_LINE_TREE_TABLE_NAME = "MMBomLineTree";
	protected WorkOrderBomContext context;
	protected BomTreeForm bomTreeForm;
	
	public WorkOrderBomSelectPage() {
		super();
	}
	
	public WorkOrderBomSelectPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		context = (WorkOrderBomContext) ((WorkOrderBomWizard) getWizard()).getContext();

		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		
        ScrolledForm sForm = toolkit.createScrolledForm(composite);
        ManagedForm managedForm = new ManagedForm(toolkit, sForm);
        final IMessageManager mmng = managedForm.getMessageManager();
        sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        Composite body = sForm.getForm().getBody();
        configureBody(body);

        try {
            ADManager adManager = Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), BOM_LINE_TREE_TABLE_NAME);
            PrdManager prdManager = Framework.getService(PrdManager.class);
           
            MMManager mmManager = Framework.getService(MMManager.class);
            List<BomLineTree> bomLineTrees = mmManager.getBomTree(Env.getOrgRrn(), 
            		context.getWorkOrder().getPartName(), context.getWorkOrder().getPartVersion(), Bom.BOMUSE_MANUFACTURING, null, context.isExpandProduct(), false);
            
            //当bom存在时获取工单产品
            Part part = new Part();
            //如果有版本获取名称版本对应产品，如果没有则获取active的版本信息
            if(context.getWorkOrder().getPartVersion() != null) {
                part = prdManager.getPartById(Env.getOrgRrn(), context.getWorkOrder().getPartName(), context.getWorkOrder().getPartVersion());
            }else {
            	part = prdManager.getActivePart(Env.getOrgRrn(), context.getWorkOrder().getPartName(), false);
			}
            if(part == null || part.getObjectRrn() == null) {
            	UI.showInfo(Message.getString("wip.component_is_not_exist"));
            	return;
            }
            
			Process process = new Process();
			process.setOrgRrn(Env.getOrgRrn());
			process.setName(part.getProcessName());
			process.setVersion(part.getProcessVersion());
			process = (Process) prdManager.getSimpleProcessDefinition(process, false);
			//查询产品下工步信息
			
			PrdQueryAction queryAction = PrdQueryAction.newIntance();
//			queryAction.setParallel(true);
			queryAction.setCopyNode(true);
            List<StepState> stepStates = prdManager.getStepChildren(process, null, queryAction);
            
            //循环工单bom信息，获得对应工步描述
            if(stepStates != null && stepStates.size() > 0) {
            	for (BomLineTree bomLineTree: bomLineTrees) {
            		for (StepState stepState : stepStates) {
            			if (stepState.getUsedStep().getName().equals(bomLineTree.getStepName()) 
            					&& stepState.getUsedStep().getVersion() == bomLineTree.getStepVersion()) {
                			bomLineTree.setStepDesc(stepState.getUsedStep().getDescription());
                			break;
    					}
            		}
				} 
            }
            
            bomTreeForm = new BomTreeForm(body, SWT.NONE, context.getWorkOrder(), adTable, mmng, bomLineTrees, getSelectedBomLineTrees());
            bomTreeForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
        
        setControl(composite);
        setTitle(Message.getString("wip.wo_bom_select_title"));
		setMessage(Message.getString("wip.wo_bom_select_message"));
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	public List<BomLineTree> getSelectedBomLineTrees() {
        try {
            MMManager mmManager = Framework.getService(MMManager.class);
            List<BomLineTree> bomLineTrees = mmManager.getBomTree(Env.getOrgRrn(), context.getWorkOrder().getPartName(), 
            		context.getWorkOrder().getPartVersion(), Bom.BOMUSE_MANUFACTURING, null, context.isExpandProduct(), false);
            List<BomLineTree> lineTrees = new ArrayList<>();
            lineTrees.addAll(bomLineTrees);
			for (int i = bomLineTrees.size() - 1; i >= 0; i--) {
				BomLineTree bomLineTree = bomLineTrees.get(i);
				if (null == bomLineTree.getBomRrn()) {
					bomLineTrees.remove(bomLineTrees.get(i));
				}
				if (bomLineTree.getIsAlternate()) {
					for (BomLineTree LineTree : lineTrees) {
						if (bomLineTree.getPath().equals(LineTree.getPath())) {
							if(bomLineTree.getAlternateGroup() != null ) {
								if (bomLineTree.getAlternateGroup().equals(LineTree.getAlternateGroup())) {
									if (bomLineTree.getSeqNo() > LineTree.getSeqNo()) {
										bomLineTrees.remove(bomLineTree);
									}
								}
							}
						}
					}
				}
			}
            
            return bomLineTrees;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
        return null;
    }
	
	@Override
	public String doNext() {
		if(bomTreeForm == null) {
			UI.showInfo(Message.getString("mm.bom_not_found"));
			return null;
		}
		
        if (!bomTreeForm.validate()) {
			return null;
		}
        List<BomLineTree> bomLineTrees = bomTreeForm.getCheckedList();
        List<WorkOrderBomLine> workOrderBomLines = new ArrayList<WorkOrderBomLine>();

        for (BomLineTree line : bomLineTrees) { 
            WorkOrderBomLine workOrderBomLine = new WorkOrderBomLine();
            workOrderBomLine.setIsActive(true);
            workOrderBomLine.setOrgRrn(Env.getOrgRrn());
             
            workOrderBomLine.setBomRrn(line.getBomRrn());
            workOrderBomLine.setValidFrom(line.getValidFrom());
            workOrderBomLine.setValidTo(line.getValidTo());
            workOrderBomLine.setSeqNo(line.getSeqNo());
                             
            workOrderBomLine.setStepName(line.getStepName());
            workOrderBomLine.setStepVersion(line.getStepVersion());
             
            workOrderBomLine.setIsMain(line.getIsMain());
            workOrderBomLine.setIsOptional(line.getIsOptional());
            workOrderBomLine.setIsAssembly(line.getIsAssembly());
            workOrderBomLine.setIsCritical(line.getIsCritical());
            workOrderBomLine.setFlushType(line.getFlushType());
             
            workOrderBomLine.setItemCategory(line.getItemCategory());
            workOrderBomLine.setMaterialRrn(line.getMaterialRrn());
            workOrderBomLine.setMaterialName(line.getMaterialName());
            workOrderBomLine.setMaterialVersion(line.getMaterialVersion());
            workOrderBomLine.setMaterialDesc(line.getMaterialDesc());
            workOrderBomLine.setMaterialType(line.getMaterialType());
            workOrderBomLine.setUomId(line.getUomId());
            workOrderBomLine.setUnitQty(line.getUnitQty());
            workOrderBomLine.setLossRate(line.getLossRate());
            workOrderBomLine.setFixedQty(line.getFixedQty());
            workOrderBomLine.setTreeUnitQty(line.getTreeUnitQty());
             
            workOrderBomLine.setIsAlternate(line.getIsAlternate());
            workOrderBomLine.setAlternateStrategy(line.getAlternateStrategy());
            workOrderBomLine.setAlternateGroup(line.getAlternateGroup());
            workOrderBomLine.setAlternatePercent(line.getAlternatePercent());
            workOrderBomLine.setAlternatePriority(line.getAlternatePriority());
            workOrderBomLine.setComments(line.getComments());

            workOrderBomLine.setPath(line.getPath());
            workOrderBomLine.setPathLevel(line.getPathLevel());
            workOrderBomLine.setStepPath(line.getStepPath());
            workOrderBomLine.setIsProduction(line.getIsProduction());
            
            workOrderBomLine.setReserved1(line.getReserved1());
            workOrderBomLine.setReserved2(line.getReserved2());
            workOrderBomLine.setReserved3(line.getReserved3());
            workOrderBomLine.setReserved4(line.getReserved4());
            workOrderBomLine.setReserved5(line.getReserved5());
            workOrderBomLine.setReserved6(line.getReserved6());
            workOrderBomLine.setReserved7(line.getReserved7());
            workOrderBomLine.setReserved8(line.getReserved8());
            workOrderBomLine.setReserved9(line.getReserved9());
            workOrderBomLine.setReserved10(line.getReserved10());
            
            workOrderBomLines.add(workOrderBomLine);
        }
        context.setWorkOrderBomLines(workOrderBomLines);
      
		return getDefaultDirect();
	}
	
	@Override
	public boolean canFlipToNextPage() {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public String doPrevious() {
		return null;
	}

}
