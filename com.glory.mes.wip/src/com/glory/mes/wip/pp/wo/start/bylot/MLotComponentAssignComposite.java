package com.glory.mes.wip.pp.wo.start.bylot;

import java.awt.Toolkit;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.mes.wip.comp.ComponentAssignComposite;
import com.glory.mes.wip.comp.ComponentComposite;
import com.glory.mes.wip.model.ComponentUnit;

public class MLotComponentAssignComposite extends ComponentAssignComposite {

	protected Text txtMLotId;

	private Map<String, MComponentUnit> targetMComponentUnitMap;
	
	public MLotComponentAssignComposite(Composite parent, int style) {
		super(parent, style, true, false, true);
		targetMComponentUnitMap = new LinkedHashMap<String, MComponentUnit>();
	}
	
	@Override
	protected void createSourceComponent(final Composite parent) {
		Composite sourceComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = (int) ((Toolkit.getDefaultToolkit().getScreenSize().height) / 1.8);
		sourceComp.setLayoutData(gd);

		if (sourceIsCarrier) {
			Composite labelCompsite = new Composite(sourceComp, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(3, false));
			
			Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
			lblCarrierId.setText(Message.getString("mm.mlot_id"));
			
			GridData gText = new GridData();
			gText.widthHint = 216;
			
			txtMLotId = new Text(labelCompsite, SWT.BORDER);
			txtMLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtMLotId.setLayoutData(gText);
			txtMLotId.setTextLimit(32);
			txtMLotId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					Text tLotId = ((Text) event.widget);
					tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
					case SWT.KEYPAD_CR:
						queryAdapter();
					}
				}
			});
			
//			Button btnQuery = new Button(labelCompsite, SWT.PUSH);
//			btnQuery.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));
//			btnQuery.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
//			btnQuery.addSelectionListener(new SelectionAdapter() {
//				@Override
//				public void widgetSelected(SelectionEvent e) {
//					queryAdapter();
//				}
//			});
			
			sourceComponentComposite = new ComponentComposite(sourceComp, 0, false, true);
			sourceComponentComposite.init();
			
			sourceComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter(false);
				}
			});
		} else {
			sourceTableManager = new ListTableManager(ComponentComposite.getDefaultADTable(), true);
			sourceTableManager.setSortFlag(true);
			sourceTableManager.newViewer(sourceComp);
			sourceTableManager.addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter(false);
				}
			});
		}
	}
	
	private void queryAdapter() {
		try {
			String mlotId = txtMLotId.getText();
			if (!isMLotIdCaseSensitive()) {
				mlotId = mlotId.toUpperCase();
			}
			if (StringUtil.isEmpty(mlotId)) {
				sourceComponentComposite.initComponents(new ArrayList<ComponentUnit>());
				return;
			}
			MLot mlot = searchMLot(mlotId);
			if (mlot == null) {
				return;
			}
			
			List<MComponentUnit> mComponents = mlot.getSubMComponentUnit();
			List<MComponentUnit> usableMComponents = new ArrayList<MComponentUnit>();
			
			//保留可用的批次Component
			for (MComponentUnit comp : mComponents) {
				if (comp.getMainQty().compareTo(BigDecimal.ZERO) > 0) {
					targetMComponentUnitMap.put(comp.getmComponentId(), comp);
					usableMComponents.add(comp);
				}
			}
			
			//物料Component转批次Component，再加载到控件上
			List<ComponentUnit> components = convertMComponent2Component(usableMComponents);
			if (components != null && components.size() > 0) {
				for (ComponentUnit component : components) {
					component.setLotId(mlot.getmLotId());
				}
				
				sourceComponentComposite.initComponents(components);
			} else {
				// 每次都初始化基本的Map
				sourceComponentComposite.initComponents(new ArrayList<ComponentUnit>());
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public MLot searchMLot(String mLotId) throws Exception {

		MMManager mmManager = Framework.getService(MMManager.class);
		MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
		if (mLot == null) {
			UI.showWarning(Message.getString("mm.mlot_is_not_found2"));
			sourceComponentComposite.initComponents(new ArrayList<ComponentUnit>());
			return null;
		}
		if (!MaterialState.STATE_IN.equals(mLot.getState())) {
			UI.showWarning(Message.getString("mm.material_state_not_allow"));
			sourceComponentComposite.initComponents(new ArrayList<ComponentUnit>());
			return null;
		}
		
		mLot = mmManager.getMLotWithComponent(mLot.getObjectRrn());
		return mLot;
	}
	
	public List<ComponentUnit> convertMComponent2Component(List<MComponentUnit> mComponentUnits) {
		List<ComponentUnit> componentUnits = new ArrayList<ComponentUnit>();
		for (MComponentUnit mComponentUnit : mComponentUnits) {
			ComponentUnit componentUnit = new ComponentUnit();
			componentUnit.setComponentId(mComponentUnit.getmComponentId());
			componentUnit.setLotId(mComponentUnit.getParentMLotId());
			componentUnit.setPosition(mComponentUnit.getPosition());
			componentUnit.setMainQty(mComponentUnit.getMainQty());
			
			componentUnits.add(componentUnit);
		}
		return componentUnits;
	}
	
	public List<ComponentUnit> getTargetComponentUnit() throws CloneNotSupportedException {
		List<ComponentUnit> componentUnitList = targetComponentComposite.getCarrierComponents();
		List<ComponentUnit> retComponentUnitList = new ArrayList<ComponentUnit>();
		for (ComponentUnit comp : componentUnitList) {
			ComponentUnit retComp = (ComponentUnit)comp.clone();
			if (!StringUtil.isEmpty(retComp.getComponentId())) {
				retComponentUnitList.add(retComp);
			}
		}
		
		return retComponentUnitList;
	}
	
	public List<MComponentUnit> getTargetMComponentUnit() {
		List<MComponentUnit> mComponentUnits = new ArrayList<MComponentUnit>();
		List<ComponentUnit> componentUnitList = targetComponentComposite.getCarrierComponents();
		if (componentUnitList.size() == 0) {
			return null;
		}
		
		for (ComponentUnit comp : componentUnitList) {
			if (!StringUtil.isEmpty(comp.getComponentId())) {
				mComponentUnits.add(targetMComponentUnitMap.get(comp.getComponentId()));
			}
		}
		
		return mComponentUnits;
	}
	
	public String getTargetCarrierId() {
		return txtTargetCarrierId.getText();
	}
	
	@Override
	public void refresh() {
		txtMLotId.setText("");
		queryAdapter();
		
		super.refresh();
	}
	
	private Boolean isCaseSensitive;
	public boolean isMLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isMLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
