package com.glory.mes.wip.pp.wo.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.listener.IFormDataChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pp.model.WorkOrderSource;
import com.glory.mes.wip.pp.wo.WorkOrderMLotTableSelectField;

/**
 * 根据工单选择源物料批次
 * 不检查BOM,仅检查源物料批次数量是否等于生产批次数量
 */
public class WorkOrderMLotSelectForm extends EntityForm implements IFormDataChangeListener {
	
	private static final String TABLE_NAME = "PPWorkOrderMLotSelect";
	
	protected  WorkOrderMLotTableSelectField lotTableSelectField;

	public WorkOrderMLotSelectForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	@Override
	protected void createContent() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
	        ListTableManager sourceTableManager = new ListTableManager(adTable,true);
	        
			lotTableSelectField = new WorkOrderMLotTableSelectField("", sourceTableManager, "");
	        lotTableSelectField.createContent(body, toolkit);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean saveToObject() {
		return true;
	}
	
	@Override
	public void loadFromObject() {
		if (object == null) {
			return;
		}
		WorkOrder workOrder = (WorkOrder) object;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<WorkOrderLot> workOrderLot = adManager.getEntityList(Env.getOrgRrn(),WorkOrderLot.class, Env.getMaxResult(),  " workOrderRrn = " + workOrder.getObjectRrn(), null);
		    BigDecimal generationLotQty = BigDecimal.ZERO;
			for(WorkOrderLot wol :workOrderLot){
				generationLotQty = generationLotQty.add(wol.getMainQty());
			}
			lotTableSelectField.setGenerationLotQty(generationLotQty);
			List<WorkOrderSource> workOrderSources = adManager.getEntityList(Env.getOrgRrn(), WorkOrderSource.class, 
                    Env.getMaxResult(), "woRrn = " + workOrder.getObjectRrn(), null);
            List<MLot> mlots = new ArrayList<MLot>();
            for (WorkOrderSource workOrderSource : workOrderSources) {
                MLot mlot = new MLot();
                mlot.setObjectRrn(workOrderSource.getSourceMLotRrn());
                mlot = (MLot) adManager.getEntity(mlot);
                mlot.setTransMainQty(workOrderSource.getMainQty());
                mlots.add(mlot);
            }
            lotTableSelectField.setValue(mlots);
            refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void refresh() {
		super.refresh();
		lotTableSelectField.refresh();
	}
	
	public Object getValue() {
        return lotTableSelectField.getValue();
    }
	
	public void setGenerationLotQty(BigDecimal generationLotQty) {
       lotTableSelectField.setGenerationLotQty(generationLotQty);
    }
    
    public void setWhereClause(String whereClause) {
    	lotTableSelectField.setWhereClause(whereClause);
	}

	@Override
	public void dataChanged(Object sender, Object newValue) {
		if (newValue instanceof BigDecimal) {
			lotTableSelectField.setGenerationLotQty((BigDecimal) newValue);
		}
	}
}
