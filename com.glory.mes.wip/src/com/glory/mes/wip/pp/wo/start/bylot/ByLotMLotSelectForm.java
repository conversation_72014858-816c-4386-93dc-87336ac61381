package com.glory.mes.wip.pp.wo.start.bylot;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.listener.IFormDataChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;

/**
 * 根据Lot选择源物料批次
 * 不检查BOM,仅检查源物料批次数量是否等于生产批次数量
 * 
 * 与WorkOrderMLotSelectForm的区别在于按照单个Lot选择源物料批
 */
public class ByLotMLotSelectForm extends EntityForm implements IFormDataChangeListener {
	private static final String TABLE_NAME = "PPWorkOrderMLotSelect";
	private boolean isVerQty = false;
	protected  WoStartByLotMLotTableSelectField mlotTableSelectField;

    private List<WorkOrderBomLine> workOrderBomLines = null;
    
	public ByLotMLotSelectForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng, boolean isVerQty) {
		super(parent, style, object, tab, mmng);
		this.isVerQty = isVerQty;
	}
	
	@Override
	protected void createContent() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
	        ListTableManager sourceTableManager = new ListEditorTableManager(adTable,true);
	        
	        mlotTableSelectField = new WoStartByLotMLotTableSelectField("", sourceTableManager, getWhereClause());
	        mlotTableSelectField.createContent(body, toolkit);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean saveToObject() {
		return true;
	}
	
	@Override
	public void loadFromObject() {
		try {
			if (object == null) {
				return;
			}
			
			if (!(object instanceof WorkOrderLot)) {
				return;
			}
			
			WorkOrderLot workOrderLot = (WorkOrderLot) object;
		    BigDecimal generationLotQty = workOrderLot.getMainQty();
			
			mlotTableSelectField.setVerQty(isVerQty);
			PpManager ppManager = Framework.getService(PpManager.class);
			WorkOrder workOrder = new WorkOrder();
			workOrder.setObjectRrn(workOrderLot.getWorkOrderRrn());
			this.setWorkOrderBomLines(ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext()));
			mlotTableSelectField.setWorkOrderBomLines(this.getWorkOrderBomLines());
			mlotTableSelectField.setGenerationLotQty(generationLotQty);
			
//			List<WorkOrderSource> workOrderSources = adManager.getEntityList(Env.getOrgRrn(), WorkOrderSource.class, 
//                    Env.getMaxResult(), 
//                    "woRrn = " + workOrderLot.getWorkOrderRrn(), 
//                    null);
            List<MLot> mlots = new ArrayList<MLot>();
//            for (WorkOrderSource workOrderSource : workOrderSources) {
//                MLot mlot = new MLot();
//                mlot.setObjectRrn(workOrderSource.getSourceMLotRrn());
//                mlot = (MLot) adManager.getEntity(mlot);
//                mlot.setTransMainQty(workOrderSource.getMainQty());
//                mlots.add(mlot);
//            }
            mlotTableSelectField.setValue(mlots);
            setWhereClause(getWhereClause());
            refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void refresh() {
		super.refresh();
		mlotTableSelectField.setValue(null);
		mlotTableSelectField.refresh();

		if (object instanceof WorkOrderLot && object != null) {
			WorkOrderLot workOrderLot = (WorkOrderLot) object;
			mlotTableSelectField.setGenerationLotQty(workOrderLot.getMainQty());
		} else {
			mlotTableSelectField.setGenerationLotQty(BigDecimal.ZERO);
		}
	}
	
    public void setWhereClause(String whereClause) {
    	mlotTableSelectField.setWhereClause(whereClause);
	}
	
	protected String getWhereClause() {
		StringBuffer whereClause = new StringBuffer();
		
		if (object == null) {
			return whereClause.toString();
		}
		
		WorkOrderLot workOrderLot = (WorkOrderLot) object;
		WorkOrder workOrder = new WorkOrder();
		workOrder.setObjectRrn(workOrderLot.getWorkOrderRrn());
		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
			if (bomLines == null || bomLines.size() == 0) {
				return whereClause.toString();
			}
			whereClause.append(" materialName in (");
			for (WorkOrderBomLine workOrderBomLine : bomLines) {
				whereClause.append("'"+workOrderBomLine.getMaterialName()+"',");
			}
			whereClause.replace(0, whereClause.length(), whereClause.substring(0, whereClause.length()-1));
			whereClause.append(")");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return whereClause.toString();
	}

	public Object getValue() {
        return mlotTableSelectField.getValue();
    }
	
	@Override
	public void dataChanged(Object sender, Object newValue) {
		if (newValue instanceof BigDecimal) {
			mlotTableSelectField.setGenerationLotQty((BigDecimal) newValue);
		}
	}

	public List<WorkOrderBomLine> getWorkOrderBomLines() {
		return workOrderBomLines;
	}

	public void setWorkOrderBomLines(List<WorkOrderBomLine> workOrderBomLines) {
		this.workOrderBomLines = workOrderBomLines;
	}
}
