package com.glory.mes.wip.pp.wo.mr;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.query.QueryDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.exception.MMExceptionBundle;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.Material;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.wip.mm.MaterialRequisition;
import com.glory.mes.wip.mm.MaterialRequisitionDetail;
import com.glory.mes.wip.mm.MaterialRequisitionLine;

public class WoMaterialRequestManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.mr.WoMaterialRequestManagerEditor";

	public static final String FIELD_WORKINFO = "workInfo";
	public static final String FIELD_MATERIALINFO = "materialInfo";
	public static final String FIELD_WORKLIST = "workList";
	public static final String FIELD_WORKDETAIL = "workDetail";
	public static final String FIELD_MATERIALLINE = "materialLine";
	public static final String FIELD_MATERIALHIS = "materialHis";
	public static final String FIELD_MLOTINFO = "mLotInfo";
	public static final String FIELD_MLOTDETAIL = "mLotDetail";
	
	public static final String FIELD_MATERIALNAME = "materialName";

	public static final String BUTTON_PICKING = "picking";
	public static final String BUTTON_REFRESH = "refresh";
	public static final String BUTTON_QUERY = "query";
	public static final String BUTTON_NEW = "new";
	public static final String BUTTON_ADD = "add";
	public static final String BUTTON_REMOVE = "remove";

	protected GlcFormField workInfoField;
	protected GlcFormField materialInfoField;
	protected ListTableManagerField workListField;
	protected EntityFormField workDetailField;
	protected ListTableManagerField materialLineField;
	protected ListTableManagerField materialHisField;
	protected ListTableManagerField mLotInfoField;
	protected EntityFormField mLotDetailField;
	
	protected ListTableManager workListManager, materialLineManager, materialHisManager, mLotInfoManager;
	protected RefTableField materialNameField;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		workInfoField = form.getFieldByControlId(FIELD_WORKINFO, GlcFormField.class);
		materialInfoField = form.getFieldByControlId(FIELD_MATERIALINFO, GlcFormField.class);
		workListField = workInfoField.getFieldByControlId(FIELD_WORKLIST, ListTableManagerField.class);
		workDetailField = workInfoField.getFieldByControlId(FIELD_WORKDETAIL, EntityFormField.class);
		materialLineField = materialInfoField.getFieldByControlId(FIELD_MATERIALLINE, ListTableManagerField.class);
		materialHisField = materialInfoField.getFieldByControlId(FIELD_MATERIALHIS, ListTableManagerField.class);
		mLotInfoField = materialInfoField.getFieldByControlId(FIELD_MLOTINFO, ListTableManagerField.class);
		mLotDetailField = materialInfoField.getFieldByControlId(FIELD_MLOTDETAIL, EntityFormField.class);
		
		workListManager = workListField.getListTableManager();
		materialLineManager = materialLineField.getListTableManager();
		materialHisManager = materialHisField.getListTableManager();
		mLotInfoManager = mLotInfoField.getListTableManager();
		materialNameField = mLotDetailField.getFieldByControlId(FIELD_MATERIALNAME, RefTableField.class);

		subscribeAndExecute(eventBroker, workListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::workListSelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PICKING), this::pickingAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, workInfoField.getFullTopic(BUTTON_QUERY), this::queryAdapter);
		subscribeAndExecute(eventBroker, workInfoField.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, materialInfoField.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, materialInfoField.getFullTopic(BUTTON_ADD), this::addAdapter);
		subscribeAndExecute(eventBroker, materialInfoField.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
		
		init(null);
	}
	
	public void init(String whereClause) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			if (StringUtil.isEmpty(whereClause)) {
				List<WorkOrder> workOrders = adManager.getEntityList(Env.getOrgRrn(), WorkOrder.class, Integer.MAX_VALUE, "docStatus in ('STARTED','APPROVED')", "");
				workListManager.setInput(workOrders);
			} else {
				List<WorkOrder> workOrders = adManager.getEntityList(Env.getOrgRrn(), WorkOrder.class, Integer.MAX_VALUE, "docStatus in ('STARTED','APPROVED') and " + whereClause, "");
				workListManager.setInput(workOrders);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}

	private void pickingAdapter(Object object) {
		try {
			List<MLot> mLots = new ArrayList<MLot>();
			List<MLot> pickMLots = (List)mLotInfoManager.getInput();
			WorkOrder workOrder = (WorkOrder) workDetailField.getValue();
			PpManager ppManager = Framework.getService(PpManager.class);
			MaterialRequisition requisition = ppManager.getMaterialRequisition(workOrder, Env.getSessionContext());
			if (pickMLots == null || pickMLots.size() == 0) {
				UI.showError(Message.getString(MMExceptionBundle.bundle.MainLotIsNotFound()));
				return;
			}
			if (requisition == null) {
				UI.showError(Message.getString(MMExceptionBundle.bundle.BomMaterialNotFound()));
				return;
			}
			for (MLot mLot : pickMLots) {
				mLot.setTransTargetWarehouseRrn(mLot.getTransTargetWarehouseRrn());
				mLot.setTransTargetStorageId(mLot.getTransTargetStorageId());
				mLot.setTransTargetStorageType(mLot.getTransTargetStorageType());
				mLots.add(mLot);
			}
			ppManager.reveiveMaterialRequisition(requisition, mLots, false, Env.getSessionContext());
			UI.showInfo(Message.getString(MMExceptionBundle.bundle.BomRequistionSuccess()));
			refreshAdapter(object);
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}

	private void refreshAdapter(Object object) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<WorkOrder> workOrders = adManager.getEntityList(Env.getOrgRrn(), WorkOrder.class, Integer.MAX_VALUE, "docStatus in ('STARTED','APPROVED')", "");
			workListManager.setInput(workOrders);
			workListManager.refresh();
			materialLineManager.setInput(new ArrayList<MaterialRequisitionLine>());
			materialLineManager.refresh();
			materialHisManager.setInput(new ArrayList<MaterialRequisitionDetail>());
			materialHisManager.refresh();
			mLotInfoManager.setInput(new ArrayList<MLot>());
			mLotInfoManager.refresh();
			mLotDetailField.setValue(new MLot());
			mLotDetailField.refresh();
			workDetailField.setValue(new WorkOrder());
			workDetailField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}
	
	private void queryAdapter(Object object) {
		try {
			QueryDialog queryDialog = new WoMaterialRequestQueryDialog(UI.getActiveShell(), workListManager, null, this);
			queryDialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}

	private void workListSelectionChanged(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder)workListManager.getSelectedObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				workDetailField.setValue(entityManager.getEntity((WorkOrder) workOrder));
				workDetailField.refresh();
			}
			//领料单行
			if (workDetailField.getValue() != null && materialLineManager != null) {
				materialLineManager.setInput(buildMaterialLineRequest());
				materialLineManager.refresh();
			}
			
			//领料历史
			if (workDetailField.getValue() != null){
				materialHisManager.setInput(buildMaterialDetailRequest());
				materialHisManager.refresh();
			}
			
			//领料物料批
			if (workDetailField.getValue() != null){
				mLotInfoManager.setInput(new ArrayList<MLot>());
			}
			
			//筛选物料
			PpManager ppManager = Framework.getService(PpManager.class);
			MMManager mmManager = Framework.getService(MMManager.class);
			List<MaterialRequisitionLine> requisitionLines = ppManager.getMaterialRequisitionLines(workOrder, Env.getSessionContext());
			List<Material> materials = new ArrayList<Material>();
			if (CollectionUtils.isNotEmpty(requisitionLines)) {
				for (MaterialRequisitionLine materialRequisitionLine : requisitionLines) {
					Material material = mmManager.getMaterial(Env.getOrgRrn(), materialRequisitionLine.getMaterialName());
					materials.add(material);
				}
			} 
			materialNameField.setInput(materials);
			mLotDetailField.setValue(new MLot());
			mLotDetailField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}
	
	private void newAdapter(Object object) {
		try {
			mLotDetailField.setValue(new MLot());
			mLotDetailField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}

	private void addAdapter(Object object) {
		try {
			if (mLotDetailField.validate()) {
				MMManager mmManager = Framework.getService(MMManager.class);
				List<MLot> mLots = (List<MLot>) mLotInfoManager.getInput();
				MLot mLot = (MLot) mLotDetailField.getValue();
				MLot existMLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLot.getmLotId());
				if (mLot.getBatchType().equals(Material.BATCH_TYPE_LOT)) {
					//必须输入批次
					if (StringUtil.isEmpty(mLot.getmLotId())) {
						UI.showError(Message.getString(MMExceptionBundle.bundle.PleaseInputMlotId()));
						return;
					}
					//判断已存在的批次是否有库存，L类型的批次不允许多库存
					if (existMLot != null) {
						List<MLot> storageMLots = mmManager.getMLotStorageByMLots(Env.getOrgRrn(), Arrays.asList(existMLot));
						if (CollectionUtils.isNotEmpty(storageMLots)) {
							UI.showError(Message.formatString(MMExceptionBundle.bundle.MLotWasStored2(storageMLots.get(0).getTransWarehouseId(), storageMLots.get(0).getTransStorageId())));
							return;
						}
					}
					//判断列表中是否已存在
					for (MLot mLot2 : mLots) {
						if (!StringUtil.isEmpty(mLot2.getmLotId()) && mLot2.getmLotId().equals(mLot.getmLotId())) {
							UI.showError(Message.getString(MMExceptionBundle.bundle.BoxAlreadyExist()));
							return;
						}
					}
				} else if (mLot.getBatchType().equals(Material.BATCH_TYPE_BATCH)) {
					if (StringUtil.isEmpty(mLot.getmLotId())) {
						UI.showError(Message.getString(MMExceptionBundle.bundle.PleaseInputMlotId()));
						return;
					}
				} else if (mLot.getBatchType().equals(Material.BATCH_TYPE_MATERIAL)) {
					if (!StringUtil.isEmpty(mLot.getmLotId())) {
						UI.showError(Message.getString(MMExceptionBundle.bundle.MaterialLotNotRequiredMLotId()));
						return;
					}
				}
				List<MLot> inpuMLots = new ArrayList<MLot>();
				if (!StringUtil.isEmpty(mLot.getmLotId())) {
					if (existMLot != null) {
						existMLot.setTransMainQty(mLot.getTransMainQty());
						existMLot.setTransTargetWarehouseRrn(mLot.getTransTargetWarehouseRrn());
						existMLot.setTransTargetStorageId(mLot.getTransTargetStorageId());
						existMLot.setTransTargetStorageType(mLot.getTransTargetStorageType());
						inpuMLots.add(existMLot);
					} else {
						mLot.setMainQty(BigDecimal.ZERO);
						inpuMLots.add(mLot);
					}
				} else {
					mLot.setMainQty(BigDecimal.ZERO);
					inpuMLots.add(mLot);
				}
				inpuMLots.addAll(mLots);
				mLotInfoManager.setInput(inpuMLots);
				mLotDetailField.setValue(new MLot());
				mLotDetailField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}

	private void removeAdapter(Object object) {
		try {
			List<ADBase> list = (List<ADBase>) mLotInfoManager.getInput();
			List<Object> os = mLotInfoManager.getCheckedObject();
			for (Object o : os) {
				ADBase pe = (ADBase) o;
				list.remove(pe);
			}
			List<MLot> lots = new ArrayList<MLot>();
			for (ADBase adBase : list) {
				lots.add((MLot)adBase);
			}
			mLotInfoManager.setInput(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}
	
	protected List<MaterialRequisitionLine> buildMaterialLineRequest() {
		List<MaterialRequisitionLine> materialRequisitionLines = new ArrayList<MaterialRequisitionLine>();
		WorkOrder workOrder = (WorkOrder) workDetailField.getValue();
		if (workOrder != null && workOrder.getObjectRrn() != null) {
			try {
				PpManager ppManager = Framework.getService(PpManager.class);
				materialRequisitionLines = ppManager.getMaterialRequisitionLines(workOrder, Env.getSessionContext());
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return materialRequisitionLines;
    }
	
	protected List<MaterialRequisitionDetail> buildMaterialDetailRequest() {
		List<MaterialRequisitionDetail> materialRequisitionDetails = new ArrayList<MaterialRequisitionDetail>();
		WorkOrder workOrder = (WorkOrder) workDetailField.getValue();
		if (workOrder != null && workOrder.getObjectRrn() != null) {
			try {
				PpManager ppManager = Framework.getService(PpManager.class);
				materialRequisitionDetails = ppManager.getMaterialRequisitionDetails(workOrder, Env.getSessionContext());
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return materialRequisitionDetails;
    }

}