package com.glory.mes.wip.pp.wo.form;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;

/**
 * 显示工单所对应的子工单
 */
public class WorkOrderSubWoForm extends EntityForm {
	
	private TableViewerManager tableManager;
    private static final String TABLE_NAME = "WIPSubByBomWorkOrder";

	public WorkOrderSubWoForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}
	
	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		Composite tableCom = toolkit.createComposite(body);
		tableCom.setLayout(new GridLayout(1, false));
		GridData tablegd = new GridData(GridData.FILL_BOTH);
		tablegd.grabExcessHorizontalSpace = true;
		tablegd.heightHint = 300;
		tableCom.setLayoutData(tablegd);
		
		ADManager adManager;
		try {
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
	        tableManager = new TableViewerManager(adTable);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
        tableManager.newViewer(tableCom);
	}

	protected List<WorkOrder> buildSubWorkOrder() {
		WorkOrder workOrder = (WorkOrder) object;
		List<WorkOrder> workOrders = new ArrayList<WorkOrder>();
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			workOrders = adManager.getEntityList(Env.getOrgRrn(), WorkOrder.class, Env.getMaxResult(),
					"parentId = '" + workOrder.getDocId() + "'", "");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return workOrders;
	}
	
	@Override
	public void loadFromObject() {
		if (object != null && tableManager != null) {
			tableManager.setInput(buildSubWorkOrder());
			tableManager.refresh();
		}
	}
	
	public void setInput(List<? extends Object> input) {
		tableManager.setInput(input);
		tableManager.refresh();
	}
	
	@Override
	public boolean saveToObject() {
		return true;
	}
}
