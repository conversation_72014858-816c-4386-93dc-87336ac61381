package com.glory.mes.wip.pp.wo.form;


import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.part.PartFlowForm;
import com.glory.mes.prd.viewer.ProcessFlowTreeManager;
import com.glory.mes.prd.workflow.graph.def.Node;

public class WorkOrderFlowForm extends PartFlowForm {
	
	private static final Logger logger = Logger.getLogger(PartFlowForm.class);

	public WorkOrderFlowForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}
	
	@Override
	public void addFields() {
		try {
			ProcessFlowTreeManager treeManager = new ProcessFlowTreeManager();
			field = new WorkOrderFlowPartTreeField(FIELD_ID, "", treeManager);
			addField(FIELD_ID, field);
		} catch (Exception e) {
			logger.error("NewPartForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			WorkOrder wo = (WorkOrder) object;
			try {
				if(wo.getObjectRrn() != null) {
					Part curPart = (Part) field.getValue();
					((WorkOrderFlowPartTreeField) field).setAltProcessName(wo.getReworkProcessName());
					((WorkOrderFlowPartTreeField) field).setAltProcessVersion(wo.getReworkProcessVersion());
					
					ADManager adManager = Framework.getService(ADManager.class);
					WorkOrder tempWordOrder = new WorkOrder();
					tempWordOrder.setObjectRrn(wo.getObjectRrn());
					WorkOrder nextWordOrder = (WorkOrder) adManager.getEntity(tempWordOrder);
					
					String partName = nextWordOrder.getPartName();
					Long partVersion = nextWordOrder.getPartVersion();
					
					if(curPart != null && curPart.getName().equals(partName) && curPart.getVersion().equals(partVersion)) {
						//相同产品版本，界面不做重复加载
					}else {
						if (partName != null) {
							if (partVersion != null) {
								// 获取指定版本产品的信息
								List<Part> verisonParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
										" name = '" + partName + "' and version = " + partVersion, "");
								if (verisonParts != null && verisonParts.size() > 0) {
									Part versionPart = verisonParts.get(0);
									field.setValue(versionPart);
								} else {
									field.setValue(null);
								}
							} else {
								// 版本号为空则取激活状态的产品流程
								List<Part> activeParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
										" name = '" + partName + "' and status = 'Active' ", "");
								if (activeParts != null && activeParts.size() > 0) {
									Part activePart = activeParts.get(0);
									field.setValue(activePart);
								}else {
									field.setValue(null);
								}
							}
						}
					}
				}
				refresh();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public void internalRefresh(String partName, Long partVersion) {
		if (object != null) {
			try {
				ADManager adManager = Framework.getService(ADManager.class);
				Part curPart = (Part) field.getValue();
				if(curPart != null && curPart.getName().equals(partName) && curPart.getVersion().equals(partVersion)) {
					//相同产品版本，界面不做重复加载
				} else {
					if (partName != null) {
						if (partVersion != null) {
							// 获取指定版本产品的信息
							List<Part> verisonParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
									" name = '" + partName + "' and version = " + partVersion, "");
							if (verisonParts != null && verisonParts.size() > 0) {
								Part versionPart = verisonParts.get(0);
								field.setValue(versionPart);
							} else {
								field.setValue(null);
							}
						} else {
							// 版本号为空则取激活状态的产品流程
							List<Part> activeParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
									" name = '" + partName + "' and status = 'Active' ", "");
							if (activeParts != null && activeParts.size() > 0) {
								Part activePart = activeParts.get(0);
								field.setValue(activePart);
							}else {
								field.setValue(null);
							}
						}
					}
				}
				refresh();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public void internalRefresh(String partName, Long partVersion, String processName, Long processVersion) {
		((WorkOrderFlowPartTreeField) field).setAltProcessName(processName);
		((WorkOrderFlowPartTreeField) field).setAltProcessVersion(processVersion);
		internalRefresh(partName, partVersion);
	}
	
	public List<Node> getFlowList() {
		return ((WorkOrderFlowPartTreeField)field).getFlowList();
	}
	
	public void addSelectionChangedListener(ISelectionChangedListener listener) {
		((WorkOrderFlowPartTreeField)field).addSelectionChangedListener(listener);
	}
}
