package com.glory.mes.wip.pp.wo.start.bylot.glc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.jface.dialogs.Dialog;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentComposite;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.custom.SortingCustomComposite;
import com.glory.mes.wip.lot.sorting.SortModel;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.sorting.LotSortingAction;

public class WoStartByLotGlcEditor extends GlcEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.start.bylot.glc.WoStartByLotGlcEditor";
	
	public static final String WORKORDER_PREFIX = "workOrder.";
	public static final String WORKORDERLOT_PREFIX = "WorkOrderLot.";
	public static final String SCHEDULELOT_PREFIX = "Lot.";
	
	protected GlcFormField queryGlcFormField;
	protected GlcFormField selectWaferSortGlcFormField;	
	protected QueryFormField workOrderLotQueryFormField;
	protected QueryFormField sourceLotQueryFormField;
	protected CustomField selectWaferCustomField;
	protected CustomField sortCustomField;
	protected BooleanField pilotFlagBooleanField;
	
	protected ComponentAssignCustomComposite componentAssignCustomComposite;
	protected SortingCustomComposite sortingCustomComposite;
	
	private static final String FIELD_QUERYFORM_WORKORDERLOT = "workOrderLotQuery";
	private static final String FIELD_GLCFORM_SOURCEWAFER = "selectWafer";
	private static final String FIELD_QUERYFORM_SOURCELOT = "sourceLotQuery";
	private static final String FIELD_GLCFORM_WAFERSORT = "selectWaferSortCustom";
	private static final String FIELD_CUSTOM_SELECTWAFER = "selectWaferCustom";
	private static final String FIELD_CUSTOM_SORT = "sortCustom";
	private static final String FIELD_PILOT_FLAG = "pilotFlag";
	
	private static final String BUTTON_START = "start";
	private static final String BUTTON_REFRESH = "refresh";
	
	private boolean showScheduleLots = true;
		
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		workOrderLotQueryFormField = form.getFieldByControlId(FIELD_QUERYFORM_WORKORDERLOT, QueryFormField.class);
		
		queryGlcFormField = form.getFieldByControlId(FIELD_GLCFORM_SOURCEWAFER, GlcFormField.class);			
		sourceLotQueryFormField = queryGlcFormField.getFieldByControlId(FIELD_QUERYFORM_SOURCELOT, QueryFormField.class);		
		
		selectWaferSortGlcFormField = queryGlcFormField.getFieldByControlId(FIELD_GLCFORM_WAFERSORT, GlcFormField.class);
		
		selectWaferCustomField = selectWaferSortGlcFormField.getFieldByControlId(FIELD_CUSTOM_SELECTWAFER, CustomField.class);
		componentAssignCustomComposite = (ComponentAssignCustomComposite) selectWaferCustomField.getCustomComposite();
		
		sortCustomField = selectWaferSortGlcFormField.getFieldByControlId(FIELD_CUSTOM_SORT, CustomField.class);
		sortingCustomComposite = (SortingCustomComposite) sortCustomField.getCustomComposite();
		
		pilotFlagBooleanField = selectWaferSortGlcFormField.getFieldByControlId(FIELD_PILOT_FLAG, BooleanField.class);
		
		subscribeAndExecute(eventBroker, workOrderLotQueryFormField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		subscribeAndExecute(eventBroker, sourceLotQueryFormField.getFullTopic(GlcEvent.EVENT_QUERY), this::sourceLotQueryAdapter);
		
		subscribeAndExecute(eventBroker, queryGlcFormField.getFullTopic(FIELD_QUERYFORM_SOURCELOT + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_START), this::startAdapter);	
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);	
		
		if (componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId != null) {
			componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId.setEnabled(false);
		}
		EntityForm sortingForm = sortingCustomComposite.getForm();	
		sortingForm.setObject(new SortModel());
		sortingForm.loadFromObject();
	}
	
	private void refreshAdapter(Object obj) {
		try {
			queryAdapter(obj);
			
			ComponentComposite sourceComponentComposite = componentAssignCustomComposite.getSourceComponentComposite().getComponentComposite();
			ComponentComposite targetComponentComposite = componentAssignCustomComposite.getTargetComponentComposite().getComponentComposite();
			sourceComponentComposite.initComponents(new ArrayList<ComponentUnit>());
			targetComponentComposite.initComponents(new ArrayList<ComponentUnit>());
			
		
			sourceLotQueryFormField.getQueryForm().getTableManager().setInput(new ArrayList<MLot>());
			sourceLotQueryFormField.getQueryForm().getTableManager().refresh();
			componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId.setText("");
			componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.setText("");
			
			EntityForm sortingForm = sortingCustomComposite.getForm();	
			sortingForm.setObject(new SortModel());
			sortingForm.loadFromObject();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void queryAdapter(Object obj) {
		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			String[] whereClauses = createWhereClause();
			List<WorkOrderLot> workOrderLotlist = ppManager.queryWorkOrderLot(Env.getOrgRrn(), whereClauses[0], whereClauses[1], getParmaterMap());
			
			if (showScheduleLots) {
				List<Lot> lots = getADManger().getEntityList(Env.getOrgRrn(), Lot.class, Env.getMaxResult(), whereClauses[2], " priority DESC");
				// 转换成Order Lot 在列表中显示
				for (Lot lot : lots) {
					WorkOrderLot orderLot = new WorkOrderLot();
					orderLot.setLotId(lot.getLotId());
					orderLot.setLotType(lot.getLotType());
					orderLot.setMainQty(lot.getMainQty());
					orderLot.setPlanStartDate(lot.getPlanStartDate());
					orderLot.setPlanEndDate(lot.getPlanEndDate());
					
					WorkOrder workOrder = new WorkOrder();
					workOrder.setPartName(lot.getPartName());
					workOrder.setPartVersion(lot.getPartVersion());
					workOrder.setPriority(lot.getPriority());
					workOrder.setCustomerCode(lot.getCustomerCode());
					workOrder.setGrade1(lot.getGrade1());
					orderLot.setWorkOrder(workOrder);
					
					// 非工单批次显示在前面
					workOrderLotlist.add(0, orderLot);
				}
			}
			
			workOrderLotQueryFormField.getQueryForm().getTableManager().setInput(workOrderLotlist);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void sourceLotQueryAdapter(Object obj) {
		try {
			if (workOrderLotQueryFormField.getSelectedObject() != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				
				String where = "";
				WorkOrderLot workOrderLot = (WorkOrderLot) workOrderLotQueryFormField.getSelectedObject();	
				if (workOrderLot.getObjectRrn() == null) {
					WorkOrder workOrder = workOrderLot.getWorkOrder();
					
					MMManager mmManager = Framework.getService(MMManager.class);
		        	Bom bom = mmManager.getActiveBom(Env.getOrgRrn(), workOrder.getPartName(), null, Bom.BOMUSE_MANUFACTURING, null);
					if (bom == null || bom.getBomLines() == null) {
						throw new ClientException("mm.bom_not_found_or_bomline_is_null");
					}
					List<BomLine> partBomLines = bom.getBomLines();
		        	where = partBomLines.stream().filter(b -> b.getIsMain()).map(BomLine :: getMaterialName).collect(Collectors.joining("','"));
				} else {
					WorkOrder workOrder = new WorkOrder();
					workOrder.setObjectRrn(workOrderLot.getWorkOrderRrn());
					workOrder = (WorkOrder) adManager.getEntity(workOrder);
					
					SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			        if (MesCfMod.isCheckUseBom(Env.getOrgRrn(), sysParamManager)) {
			        	MMManager mmManager = Framework.getService(MMManager.class);
			        	Bom bom = mmManager.getActiveBom(Env.getOrgRrn(), workOrder.getPartName(), null, Bom.BOMUSE_MANUFACTURING, null);
						if (bom == null || bom.getBomLines() == null) {
							throw new ClientException("mm.bom_not_found_or_bomline_is_null");
						}
						List<BomLine> partBomLines = bom.getBomLines();
			        	where = partBomLines.stream().filter(b -> b.getIsMain()).map(BomLine :: getMaterialName).collect(Collectors.joining("','"));
			        } else {
			        	PpManager ppManager = Framework.getService(PpManager.class);
			        	List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
			        	if (bomLines == null || bomLines.size() == 0) {
			        		throw new ClientException("pp.workorder_bom_is_not_exist");
			        	}
			        	where = bomLines.stream().filter(b -> b.getIsMain()).map(WorkOrderBomLine :: getMaterialName).collect(Collectors.joining("','"));
			        }
				}
				
				if (StringUtil.isEmpty(where)) {
		        	sourceLotQueryFormField.getQueryForm().getTableManager().setInput(new ArrayList<MLot>());
		        	UI.showInfo(Message.getString("mm.bom_not_have_is_main"));
		        } else {
		        	String whereClauses = createSourceLotWhereClause(where);
					List<MLot> mLots = adManager.getEntityList(Env.getOrgRrn(), MLot.class, Integer.MIN_VALUE, Integer.MAX_VALUE, whereClauses, "");
					sourceLotQueryFormField.getQueryForm().getTableManager().setInput(mLots);
		        }
			} else {
				UI.showWarning(Message.getString("wip_not_select_lot"));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public Map<String, Object> getParmaterMap() {
		QueryForm queryForm = workOrderLotQueryFormField.getQueryForm().getQueryForm();
		return queryForm.getParmaterMap();
	}
	
	public void clearParmaterMap() {
		QueryForm queryForm = workOrderLotQueryFormField.getQueryForm().getQueryForm();
		queryForm.getParmaterMap().clear();;
	}
	
	public String createSourceLotWhereClause(String where) {
		try {
			QueryForm queryForm = sourceLotQueryFormField.getQueryForm().getQueryForm();
			LinkedHashMap<String, IField> fields = sourceLotQueryFormField.getQueryForm().getFields();
			long objectRrn = sourceLotQueryFormField.getQueryForm().getTableManager().getADTable().getObjectRrn();
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(objectRrn);
			
			StringBuffer whereClauses = new StringBuffer(" 1=1 ");
			if(table.getWhereClause() != null) {
				whereClauses.append(" AND ");
				whereClauses.append(table.getWhereClause());
			}
			if(where.length() > 0) {
				whereClauses.append(" AND ");
				whereClauses.append(" materialName in ('" + where + "')");
			}
			for (IField f : fields.values()) {
				queryForm.createFieldWhereClause(whereClauses, f,  "");
			}
			return whereClauses.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
		
	}
		
	public String[] createWhereClause() {
		clearParmaterMap();

		QueryForm queryForm = workOrderLotQueryFormField.getQueryForm().getQueryForm();
		LinkedHashMap<String, IField> fields = workOrderLotQueryFormField.getQueryForm().getFields();
		StringBuffer workOrderSb = new StringBuffer();
		workOrderSb.append(" AND workOrder.docStatus in ('APPROVED','STARTED')");
		StringBuffer workOrderLotSb = new StringBuffer();
		workOrderLotSb.append(" AND WorkOrderLot.mainQty > 0 and WorkOrderLot.state = 'SCHEDULE'");

		StringBuffer scheduleLotSb = new StringBuffer();
		scheduleLotSb.append(" Lot.mainQty > 0 and Lot.state = 'SCHD'");
		for (IField f : fields.values()) {
			if (f.getId().startsWith(WORKORDER_PREFIX)) {
				queryForm.createFieldWhereClause(workOrderSb, f, "");
			} else {
				queryForm.createFieldWhereClause(workOrderLotSb, f, WORKORDERLOT_PREFIX);
			}

			// ScheduleLot只按批次类型、产品名称查询
			if (f.getId().contains("partName")) {
				AbstractField field = (AbstractField) f;
				String oldId = field.getId();
				field.setId("partName");
				queryForm.createFieldWhereClause(scheduleLotSb, f, SCHEDULELOT_PREFIX);

				field.setId(oldId);
			}

			if (f.getId().contains("lotType")) {
				AbstractField field = (AbstractField) f;
				String oldId = field.getId();
				field.setId("lotType");
				queryForm.createFieldWhereClause(scheduleLotSb, f, SCHEDULELOT_PREFIX);

				field.setId(oldId);
			}
		}
		String[] whereClauses = new String[3];
		whereClauses[0] = workOrderSb.toString();
		whereClauses[1] = workOrderLotSb.toString();
		whereClauses[2] = scheduleLotSb.toString();
		return whereClauses;
	}
	
	private void selectionChanged(Object obj) {
		try {
			WorkOrder workOrder = null;
			if (workOrderLotQueryFormField.getSelectedObject() != null) {
				WorkOrderLot workOrderLot = (WorkOrderLot) workOrderLotQueryFormField.getSelectedObject();	
				if (workOrderLot.getObjectRrn() == null) {
					workOrder = workOrderLot.getWorkOrder();
				} else {
					workOrder = new WorkOrder();
					workOrder.setObjectRrn(workOrderLot.getWorkOrderRrn());
					ADManager adManager = Framework.getService(ADManager.class);
					workOrder = (WorkOrder) adManager.getEntity(workOrder);
				}
			} else {
				return;
			}
			
			if (sourceLotQueryFormField.getSelectedObject() != null) {
				ComponentComposite sourceComponentComposite = componentAssignCustomComposite.getSourceComponentComposite().getComponentComposite();					
				MLot mLot = (MLot) sourceLotQueryFormField.getSelectedObject();
				//检查物料是否为工单Bom中一种			
//				if (!validateMaterial(workOrder, mLot)) {
//					return;
//				}			
				List<MLot> mLots = new ArrayList<MLot>();
				mLots.add(mLot);
				
				if (componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId != null) {
					componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId.setText(mLot.getDurable() != null ? mLot.getDurable() : "");
				}
				MMManager mLotManager = Framework.getService(MMManager.class);
				List<MComponentUnit> selectMComponents = mLotManager.getMComponentUnitByMLots(mLots, MComponentUnit.STATE_AVAIL);
				List<ComponentUnit> sourceComponents = new ArrayList<ComponentUnit>();
				List<ComponentUnit> targetComponents = (List<ComponentUnit>) componentAssignCustomComposite.getTargetComponentComposite().getTableManager().getInput();
				for (MComponentUnit selectMComponent : selectMComponents) {				
					List<ComponentUnit> existSourceComponents = new ArrayList<ComponentUnit>();		
					existSourceComponents = targetComponents.stream()
							.filter(o -> !StringUtil.isEmpty(o.getFromPosition()) 
									&& selectMComponent.getPosition().equals(o.getFromPosition())
									&& mLot.getmLotId().equals(o.getLotId())).collect(Collectors.toList());
					if (existSourceComponents.isEmpty()) {
						ComponentUnit sourceComponent = new ComponentUnit();				
						sourceComponent.setComponentId(selectMComponent.getmComponentId());
						sourceComponent.setPosition(selectMComponent.getPosition());
						sourceComponent.setLotId(mLot.getmLotId());
						sourceComponent.setMainQty(selectMComponent.getMainQty());
						sourceComponent.setSubQty(selectMComponent.getSubQty());	
						sourceComponent.setAttribute1(selectMComponent);
						sourceComponents.add(sourceComponent);			
					}							
				}
				sourceComponentComposite.initComponents(sourceComponents);
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	//检查物料是否为工单Bom中一种
	public boolean validateMaterial(WorkOrder workOrder, MLot mLot) throws Exception {
		boolean flag = false;
		if (workOrder.getObjectRrn() == null) {
			MMManager mmManager = Framework.getService(MMManager.class);
        	Bom bom = mmManager.getActiveBom(Env.getOrgRrn(), workOrder.getPartName(), null, Bom.BOMUSE_MANUFACTURING, null);
			if (bom == null || bom.getBomLines() == null) {
				throw new ClientException("mm.bom_not_found_or_bomline_is_null");
			}
			List<BomLine> partBomLines = bom.getBomLines();
			for (BomLine bomLine : partBomLines) {
				if (bomLine.getMaterialName().equals(mLot.getMaterialName())) {
					flag = true;
					break;
				}
			}
		} else {
			PpManager ppManager = Framework.getService(PpManager.class);
			List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
			if (bomLines == null || bomLines.size() == 0) {
				throw new ClientException("pp.workorder_bom_is_not_exist");
			}
			
			for (WorkOrderBomLine bomLine : bomLines) {
				if (bomLine.getMaterialName().equals(mLot.getMaterialName())) {
					flag = true;
					break;
				}
			}
		}
		
		if (!flag) {
			throw new ClientParameterException("mm.mlot_not_in_bom", mLot.getmLotId());
		}
		return true;
	}

	private void startAdapter(Object obj) {
		try {
			if (workOrderLotQueryFormField.getSelectedObject() != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				DurableManager durableManager = Framework.getService(DurableManager.class);
				List<ComponentUnit> sourceComponents = new ArrayList<ComponentUnit>();
				List<ComponentUnit> inputComponents = (List<ComponentUnit>) componentAssignCustomComposite.getTargetComponentComposite().getTableManager().getInput();
				
				//不是源载具不是AVL的载具不能投料，如果是源载具就没关系
				String targetDurableId = componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.getText();
				String sourceDurableId = componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId.getText();
				if (!sourceDurableId.equals(targetDurableId)) {
					Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), targetDurableId);
					if(carrier != null && !Carrier.EVENT_AVL.equals(carrier.getState())) {
						UI.showInfo(Message.getString("mm.target_carrier_state_not_allow"));
						return;
					}
				}

				sourceComponents = inputComponents.stream()
						.filter(o -> !StringUtil.isEmpty(o.getComponentId())).collect(Collectors.toList());
				if (sourceComponents.isEmpty()) {
					UI.showWarning(Message.getString("npw.there_are_no_components_in_the_table"));
					return;
				}
				
				Map<String, String> toPositionMap = new HashMap<String, String>();		
				List<MComponentUnit> sourceMComponents = new ArrayList<MComponentUnit>();
				for (ComponentUnit sourceComponent : sourceComponents) {
					MComponentUnit sourceMComponent = (MComponentUnit) sourceComponent.getAttribute1();	
					sourceMComponent.setPosition(sourceComponent.getPosition());																					
					sourceMComponents.add(sourceMComponent);
					
					toPositionMap.put(sourceComponent.getComponentId(), sourceComponent.getPosition());
				}
				
				EntityForm targetExtendForm = sortingCustomComposite.getForm();			
				LotSortingAction lotSortingAction = new LotSortingAction();
				if (targetExtendForm.saveToObject()) {
					SortModel sortModel = (SortModel) targetExtendForm.getObject();
					if (sortModel.isSort()) {
						if (StringUtil.isEmpty(sortModel.getEquipmentId()) 
								|| StringUtil.isEmpty(sortModel.getFromPortId())
								|| StringUtil.isEmpty(sortModel.getToPortId())) {
							//Sort信息不完整
							UI.showError(Message.getString("wip.sort_incomplete_information"));
							return;
						}
						if (componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId != null) {
							if (StringUtil.isEmpty(componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.getText())) {
								UI.showError(Message.getString("wip.sort_please_enter_target_durable"));
								return;	
							}
						}
						
						//sortModel.getEquipmentId()存的是设备主键
						lotSortingAction.setSort(sortModel.isSort());
						Equipment equipment = new Equipment();
						equipment.setObjectRrn(Long.valueOf(sortModel.getEquipmentId()));
						equipment = (Equipment) adManager.getEntity(equipment);
						lotSortingAction.setEquipmentId(equipment.getEquipmentId());
						lotSortingAction.setFromPortId(sortModel.getFromPortId());
						lotSortingAction.setToPortId(sortModel.getToPortId());
					}
					
					if (componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId != null) {
						lotSortingAction.setToDurableId(componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.getText());
					}
					lotSortingAction.setToPositionMap(toPositionMap);
				} else {
					//Sort信息不完整
					UI.showError(Message.getString("wip.sort_incomplete_information"));
					return;
				}
				
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				if (!lotSortingAction.isSort() && MesCfMod.isLotStartSetupComponentCode(Env.getOrgRrn(), sysParamManager)) {
					MComponentAliasDialog dialog = new MComponentAliasDialog(sourceMComponents);
					if (dialog.open() == Dialog.OK) {
						sourceMComponents = dialog.getSelectComponentUnits();
					} else {
						return;
					}
				}
				
				boolean pilotFlag = false;
				if (pilotFlagBooleanField != null) {
					pilotFlag = (boolean) pilotFlagBooleanField.getValue();
				} 
				
				WorkOrderLot workOrderLot = (WorkOrderLot) workOrderLotQueryFormField.getSelectedObject();
				
				if (workOrderLot.getMainQty().compareTo(new BigDecimal(sourceMComponents.size())) != 0) {
					UI.showError(Message.getString("wip.workorderlot_mainqty_sourcecomponent_size_unequal"));
					return;
				}
				
				PpManager ppManager = Framework.getService(PpManager.class);
				if (workOrderLot.getObjectRrn() != null) {
					WorkOrder workOrder = new WorkOrder();
					workOrder.setObjectRrn(workOrderLot.getWorkOrderRrn());
					workOrder = (WorkOrder) adManager.getEntity(workOrder);
					// workOrderLot.setDurableId(lotSortingAction.getToDurableId());
					ppManager.startLotByWorkOrder(workOrder, workOrderLot, sourceMComponents, lotSortingAction, pilotFlag, Env.getSessionContext());
					UI.showInfo(Message.getString("common.start_successed"));
					refreshAdapter(obj);
				} else {
					LotManager lotManamer = Framework.getService(LotManager.class);
					Lot scheduleLot = lotManamer.getLotByLotId(Env.getOrgRrn(), workOrderLot.getLotId());
					ppManager.startLotWithoutWorkOrder(scheduleLot, sourceMComponents, lotSortingAction, pilotFlag, Env.getSessionContext());
					UI.showInfo(Message.getString("common.start_successed"));
					refreshAdapter(obj);
				}
				
			} else {
				UI.showWarning(Message.getString("wip_not_select_lot"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
}
