package com.glory.mes.wip.pp.wo.subbybom;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

/*
 * 多级工单功能
 */
public class SubByBomWorkOrderEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.subbybom.SubByBomWorkOrderEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new SubByBomWorkOrderBlock(new ListTableManager(adTable));
	}
	
}
