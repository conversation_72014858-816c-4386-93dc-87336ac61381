package com.glory.mes.wip.pp.wo.start.bylot;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityAttributeForm;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLineTree;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.pp.wo.WorkOrderProperties;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class WoStartByLotProperties extends WorkOrderProperties {
	public static String TAB_LOT_START_SELECT_COMPONENT = "WIPWOLotStartSelectComponent";
	public static String TAB_LOT_START_SELECT_MLOT = "WIPWOLotStartSelectMLot";
	 
	private static final String TABLE_NAME_WO_LOTS = "PPWorkOrderLots";
	
	public ByLotMLotComponentSelectForm formByLotComponent;
	public ByLotMLotSelectForm formByLotMLot;
	
	protected ToolItem itemStart;
	
	protected ListEditorTableManager lotTableManager;
	
	private boolean isGenNewCompId;

	public WoStartByLotProperties() {
		super();
	}
	
	public WoStartByLotProperties(boolean isGenNewCompId) {
		super();
		this.isGenNewCompId = isGenNewCompId;
	}

	@Override
	protected void createBasicSection(Composite client) {
		super.createBasicSection(client);
		createWoLotViewer(client);
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		if (tab.getName().equals(EntityAttributeForm.NAME)) {
			return new EntityAttributeForm(composite, SWT.NONE, table.getModelName(), this.getAdObject(), tab.getGridY().intValue(), mmng);
		} else if (tab.getName().equals(TAB_LOT_START_SELECT_COMPONENT)) {
			formByLotComponent = new ByLotMLotComponentSelectForm(composite, SWT.NONE, this.getAdObject(), null, mmng);
			return formByLotComponent;
		} else if (tab.getName().equals(TAB_LOT_START_SELECT_MLOT)) {
			formByLotMLot = new ByLotMLotSelectForm(composite, SWT.NONE, this.getAdObject(), tab, mmng, true);
			return formByLotMLot;
		} else {
			EntityForm entityFrom = new EntityForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
			entityFrom.setADManager(getADManger());
			return entityFrom;
		}
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemStart(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemStart(ToolBar tBar) {
		itemStart = new ToolItem(tBar, SWT.PUSH);
		itemStart.setText(Message.getString("common.start"));
		itemStart.setImage(SWTResourceCache.getImage("newlot_start"));
		itemStart.setEnabled(false);
		itemStart.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				startAdapter();
			}
		});
	}
	
	@Override
	public void refresh() {
		super.refresh();
		
		//加载工单批次
		if (this.getAdObject() != null && lotTableManager != null) {
			lotTableManager.setInput(buildWorkOrderLots());
			lotTableManager.refresh();
		}
		
		if (formByLotComponent != null)
			formByLotComponent.refresh();
		
		if (formByLotMLot != null)
			formByLotMLot.refresh();
	}

	protected void startAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			WorkOrder workOrder = (WorkOrder) getAdObject();
			if (workOrder == null || workOrder.getObjectRrn() == null) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
			WorkOrderLot workOrderLot = (WorkOrderLot)lotTableManager.getSelectedObject();
			if (workOrderLot == null) {
				UI.showWarning(Message.getString("wip_not_select_lot"));
				return;
			}

			PpManager ppManager = Framework.getService(PpManager.class);
				
			if (formByLotComponent != null) {
				String carrierId = formByLotComponent.getCarrierId();
				if (StringUtil.isEmpty(carrierId)) {
					UI.showWarning(Message.getString("mm.durable_is_not_found"));
					return;
				}
				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, true);
				if (carrier == null) {
					UI.showWarning(Message.getString("mm.durable_is_not_found"));
					return;
				}
				if (!Carrier.STATE_AVAILABLE.equals(carrier.getState())) {
					UI.showWarning(Message.getString("mm.carrier_not_available"));
					return;
				}
				workOrderLot.setDurableId(carrierId);
				
				List<ComponentUnit> componentUnitList = formByLotComponent.getTargetComponentUnit();
				if (componentUnitList == null || componentUnitList.size() == 0 ||
						workOrderLot.getMainQty().compareTo(BigDecimal.valueOf(componentUnitList.size())) != 0) {
					UI.showWarning(Message.getString("wip.lot_and_comp_qty_different"));
					return;
				}
				
				for (ComponentUnit comp : componentUnitList) {
					comp.setSubstrateId1(comp.getComponentId()); //物料组件号记录到SubstrateId1
					if (isGenNewCompId) {
						comp.setComponentId(""); //清空ID，后台会按ID规则自动生成
					}
				}
				
				//效验物料是否存在产品bom
				if(!checkMaterialPartBom(workOrder,componentUnitList.get(0))) {
					return;
				}
				
				List<MComponentUnit> mComponentUnitList = formByLotComponent.getTargetMComponentUnit();
				if (mComponentUnitList == null || mComponentUnitList.size() == 0) {
					return;
				}
				

				ppManager.startLotByWorkOrder(workOrder, workOrderLot, componentUnitList, mComponentUnitList,
						Boolean.TRUE, Env.getSessionContext());
			} else if (formByLotMLot != null) {
				List<MLot> sourceMLots = (List<MLot>)formByLotMLot.getValue();
				if(isVerQty){
			    	if(!checkMaterialQty(workOrderLot, formByLotMLot.getWorkOrderBomLines(), sourceMLots)){
			    		return;
			    	}
		    	}
				
				List<WorkOrderLot> startWorkOrderLots = new ArrayList<WorkOrderLot>();
				startWorkOrderLots.add(workOrderLot);
				
				ppManager.startLotByWorkOrder(workOrder, startWorkOrderLots, sourceMLots, Lot.UNIT_TYPE_QTY,
						Boolean.TRUE, Env.getSessionContext());
			}
			
			UI.showInfo(Message.getString("common.start_successed"));
			setAdObject(new WorkOrder());
			refresh();
			getMasterParent().refresh();
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean checkMaterialPartBom(WorkOrder workOrder, ComponentUnit componentUnit){
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), componentUnit.getLotId());
			
			List<BomLineTree> partBomLines = mmManager.getBomTree(Env.getOrgRrn(),workOrder.getPartName(), workOrder.getPartVersion(), Bom.BOMUSE_MANUFACTURING, null, false, false);
			if (partBomLines !=null && partBomLines.size() > 0) {
				Map<String, BomLineTree> mainBomLineMaps = Maps.newHashMap();
				Map<String,List<BomLineTree>> groupMap = getDetailAlternateGroup(partBomLines);
				for(BomLineTree partBomLine : partBomLines) { 
					if(partBomLine.getObjectRrn() != null && partBomLine.getIsMain()) {
						mainBomLineMaps.put(partBomLine.getMaterialName(), partBomLine);
					}
				}
				//如果产品bom主物料中没有
				if(!mainBomLineMaps.containsKey(mLot.getMaterialName())) {
					Boolean flag = true;
					//如果设置了替代料
					if(mainBomLineMaps != null) {
						for(String Name: mainBomLineMaps.keySet()) {
							//如果主物料设置了替代料
							if(mainBomLineMaps.get(Name).getAlternateGroup() != null && !mainBomLineMaps.get(Name).getAlternateGroup().equals("")) {
								//获取替代料判断是否存在于主物料替代料
								List<BomLineTree> grouplist = groupMap.get(mainBomLineMaps.get(Name).getAlternateGroup());
								for(BomLineTree group : grouplist) {
									if(group.getMaterialName().equals(mLot.getMaterialName())) {
										flag = false;
									}
								}
							}
						}
					}
					//不存在则卡住
					if(flag) {
						UI.showWarning(Message.getString("wms.wrong_material_type"));
						return false;
					}
            	}
			}else {
				UI.showWarning(Message.getString("pp.part_bom_is_not_exist"));
				return false;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}
	
	private Map<String, List<BomLineTree>> getDetailAlternateGroup(List<BomLineTree> list) {
		Map<String, List<BomLineTree>> map = new HashMap<>();
		for (BomLineTree detail : list) {
			if (detail.getAlternateGroup() != null && !detail.getAlternateGroup().equals("")) {
				String Group = detail.getAlternateGroup();// 按id分组

				List<BomLineTree> groupByIdList;

				if (map.containsKey(Group)) {
					groupByIdList = map.get(Group);

				} else {
					groupByIdList = new ArrayList<>();

				}
				groupByIdList.add(detail);
				map.put(Group, groupByIdList);
			}
		}

		return map;
	}

	public Map<String, BigDecimal> getMLotRequiredQty(BigDecimal startLotTotalQty, List<MaterialRequisitionLine> materialRequisitionLines) {
		Map<String, BigDecimal> requiredQtyMap = new HashMap<String, BigDecimal>();
		for (MaterialRequisitionLine mrl : materialRequisitionLines) {
			BigDecimal subMaterialQty = mrl.getUnitQty().multiply(startLotTotalQty);
			requiredQtyMap.put(mrl.getMaterialName(), subMaterialQty);
		}
		return requiredQtyMap;
	}
	
	@Override
	public void statusChanged(String newStatus, String holdStatus) {
		if (newStatus == null || "".equals(newStatus.trim())) {
			itemStart.setEnabled(false);
		} else if (Documentation.STATUS_APPROVED.equals(newStatus.trim())
				|| WorkOrder.STATUS_STARTED.equals(newStatus.trim())) {
			itemStart.setEnabled(true);
			checkHoldStatus();
		} else {
			itemStart.setEnabled(false);
		}
	}

	public void checkHoldStatus() {
		try {
			WorkOrder workOrder = (WorkOrder) getAdObject();
			if (workOrder == null || workOrder.getObjectRrn() == null) {
				return;
			}
			if (WorkOrder.HOLDSTATE_ON.equals(workOrder.getHoldState())) {
				itemStart.setEnabled(false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void createWoLotViewer(Composite parent) {
		try {
			Composite tableCom = new Composite(parent, SWT.NONE);
			tableCom.setLayout(new GridLayout(1, false));
			GridData tablegd = new GridData(GridData.FILL_HORIZONTAL);
			tablegd.grabExcessHorizontalSpace = true;
			tablegd.heightHint = 200;
			tableCom.setLayoutData(tablegd);
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_WO_LOTS);
			lotTableManager = new ListEditorTableManager(adTable, false);
			lotTableManager.setIndexFlag(true);
			lotTableManager.newViewer(tableCom);
			lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					selectedChanged(event);
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void selectedChanged(SelectionChangedEvent event) {
		IStructuredSelection selection = (IStructuredSelection) event.getSelection();
		WorkOrderLot workOrderLot = (WorkOrderLot) selection.getFirstElement();
		if (workOrderLot == null) {
			return;
		}
		
		if (formByLotMLot != null) {
			formByLotMLot.setObject(workOrderLot);
			formByLotMLot.loadFromObject();
		}
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	protected List<WorkOrderLot> buildWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<>();
		try {
			
			WorkOrder workOrder = (WorkOrder) this.getAdObject();
			if (workOrder.getObjectRrn() == null) { 
				return workOrderLots;
			}
		
			ADManager adManager = Framework.getService(ADManager.class);
			workOrderLots = adManager.getEntityList(Env.getOrgRrn(),
	                WorkOrderLot.class, Env.getMaxResult(),
	                " workOrderRrn = " + workOrder.getObjectRrn() +
	                " AND state = '" + WorkOrderLot.STATUS_SCHEDULE + "'",
	                null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return workOrderLots;
	}
	
	public boolean checkMaterialQty(WorkOrderLot workOrderLot, List<WorkOrderBomLine> workOrderBomLines, List<MLot> mLots){
		try {
			if(workOrderBomLines != null && workOrderBomLines.size() > 0 &&
					(mLots == null || mLots.size() == 0)){
				UI.showInfo(Message.getString("common.lot.can.not.be.empity"));
    			return false;
			}
			
			for(WorkOrderBomLine bomLine : workOrderBomLines){
	    		BigDecimal hasQtye = BigDecimal.ZERO;
	    		for(MLot ml : mLots){
		    		if(bomLine.getMaterialName().equals(ml.getMaterialName())){
		    			
		    			hasQtye = hasQtye.add(ml.getTransMainQty());
		    		}
	    		}
	    		
	    		BigDecimal needQty = bomLine.getUnitQty().multiply(workOrderLot.getMainQty());
	    		if(needQty.compareTo(hasQtye) == -1){
	    			UI.showInfo(Message.getString("wip.wo_lot_qty_more_than"));
	    			return false;
	    		}
	    	}
	    	return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
    }
}