package com.glory.mes.wip.pp.wo.start.glc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.exception.MMExceptionBundle;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.model.Lot;

public class WoStartGlcEditor extends GlcEditor { 
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.start.glc.WoStartGlcEditor";

	private static final String FIELD_QUERY = "query";
	private static final String FIELD_DETAIL = "detail";
	private static final String FIELD_MATERIALREQUISITIONBASIC = "materialRequisitionBasic";
	private static final String FIELD_WORKORDERLOTS = "workOrderLots";
	private static final String FIELD_WORKORDERMLOTS = "workOrderMlots";
	
	private static final String BUTTON_ADD = "add";
	private static final String BUTTON_REMOVE = "remove";
	private static final String BUTTON_START = "start";
	private static final String BUTTON_REFRESH = "refresh";

	protected QueryFormField queryField;
	protected GlcFormField detailField;
	protected ListTableManagerField materialRequisitionBasicField;
	protected ListTableManagerField workOrderLotsField;
	protected ListTableManagerField workOrderMlotsField;

	private boolean isVerQty = true;
	private boolean isByMLot = true;
	private List<WorkOrderBomLine> workOrderBomLines = null;
	private BigDecimal generationLotQty = BigDecimal.ZERO;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		queryField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);
		detailField = form.getFieldByControlId(FIELD_DETAIL, GlcFormField.class);
		materialRequisitionBasicField = detailField.getFieldByControlId(FIELD_MATERIALREQUISITIONBASIC, ListTableManagerField.class);
		workOrderLotsField = detailField.getFieldByControlId(FIELD_WORKORDERLOTS, ListTableManagerField.class);
		workOrderMlotsField = detailField.getFieldByControlId(FIELD_WORKORDERMLOTS, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, queryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::querySelectionChanged);
		subscribeAndExecute(eventBroker, workOrderLotsField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::workOrderLotsSelectionChanged);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_START), this::startAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, detailField.getFullTopic(BUTTON_ADD), this::addAdapter);
		subscribeAndExecute(eventBroker, detailField.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
		
		init();
	}

	private void init() {
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			if (!MesCfMod.isWoStartByMlot(Env.getOrgRrn(), sysParamManager)) {
				Control detailControl = detailField.getControls()[0];
				Control childrenControl = ((Composite) detailControl).getChildren()[0];
				((Composite)childrenControl).getChildren()[2].dispose();
				isByMLot = false;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void querySelectionChanged(Object obj) {
		WorkOrder workOrder = (WorkOrder) queryField.getSelectedObject();
		if(workOrder != null) {
			buildMaterialRequest(workOrder);
			buildWorkOrderLots(workOrder);
			workOrderMlotsField.getListTableManager().setInput(new ArrayList<Object>());
		}
		generationLotQty = BigDecimal.ZERO;
		workOrderBomLines = null;
	}
	
	private void workOrderLotsSelectionChanged(Object obj) {
		try {
			Object object =  workOrderLotsField.getListTableManager().getSelectedObject();
			if(object != null) {
				PpManager ppManager = Framework.getService(PpManager.class);
				WorkOrder workOrder = new WorkOrder();
				workOrder.setObjectRrn(((WorkOrderLot)object).getWorkOrderRrn());
				workOrderBomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
				generationLotQty = ((WorkOrderLot)object).getMainQty();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void buildWorkOrderLots(WorkOrder workOrder) {
		List<WorkOrderLot> workOrderLots = new ArrayList<>();
		try {
			if(workOrder.getObjectRrn() != null) {
				StringBuffer whereClause = new StringBuffer("");
				whereClause.append(" workOrderRrn = ");
				whereClause.append(workOrder.getObjectRrn());
				if (isByMLot) {
					whereClause.append(" AND state = '");
					whereClause.append(WorkOrderLot.STATUS_SCHEDULE);
					whereClause.append("'");
				}
				ADManager adManager = Framework.getService(ADManager.class);
				workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(), whereClause.toString(), null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		workOrderLotsField.getListTableManager().setInput(workOrderLots);; 
	}
	
	private void buildMaterialRequest(WorkOrder workOrder) {
		List<MaterialRequisitionLine> materialRequisitionLines = new ArrayList<MaterialRequisitionLine>();
		if (workOrder != null && workOrder.getObjectRrn() != null) {
			try {
				PpManager ppManager = Framework.getService(PpManager.class);
				materialRequisitionLines = ppManager.getMaterialRequisitionLines(workOrder, Env.getSessionContext());
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		materialRequisitionBasicField.getListTableManager().setInput(materialRequisitionLines);
    }
	
	private void addAdapter(Object object) {
		try {
			if (generationLotQty == null || BigDecimal.ZERO.compareTo(generationLotQty) == 0) {
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WoAddLot()));
				return;
			}
			if (StringUtil.isEmpty(getWhereClause())) {
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.PPWorkOrderBomNotExist()));
				return;
			}
			WoMLotQueryDialog searchDialog = new WoMLotQueryDialog("WoMLotQueryDialog", "", eventBroker, getWhereClause(), workOrderBomLines, this);
			searchDialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@SuppressWarnings("unchecked")
	public Boolean validateMlot(List<MLot> mLots) {
		List<ADBase> values = (List<ADBase>) workOrderMlotsField.getListTableManager().getInput();
		for(MLot mLot : mLots) {
			if (isAddMLot(values, mLot)) {
				if(!isVerQty){
					mLot.setTransMainQty(getMaxQty(workOrderBomLines, mLot));
				} else {
					// 最大用量
					BigDecimal maxQty = getMaxQty(workOrderBomLines, mLot);
					// 现有用量
					BigDecimal hasQtye = getHasQty(values, mLot);
					BigDecimal subQty = maxQty.subtract(hasQtye);
					if(subQty.compareTo(BigDecimal.ZERO)==0) {
						UI.showInfo(mLot.getMaterialName() + Message.getString(WipExceptionBundle.bundle.WoLotQtyEnough()));
						return false;
					}
					if (mLot.getMainQty().compareTo(subQty) == -1) {
						mLot.setTransMainQty(mLot.getMainQty());
					} else {
						mLot.setTransMainQty(maxQty.subtract(hasQtye));
					}
				}
				values.add(mLot);
			} else {
				UI.showInfo(mLot.getmLotId() + Message.getString(WipExceptionBundle.bundle.WoLotIdRepeat()));
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 根据工单的BOMLines 生成sql条件，只显示满足条件的物料批次、
	 * */
	protected String getWhereClause() {
		StringBuffer whereClause = new StringBuffer();
		Object object = queryField.getSelectedObject();
		if (object == null) {
			return whereClause.toString();
		}
		
		WorkOrder workOrder = (WorkOrder) object;
		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
			if (bomLines == null || bomLines.size() == 0) {
				return whereClause.toString();
			}
			whereClause.append(" materialName in ( ");
			for (WorkOrderBomLine workOrderBomLine : bomLines) {
				whereClause.append( "'" + workOrderBomLine.getMaterialName() + "',");
			}
			whereClause.replace(0, whereClause.length(), whereClause.substring(0, whereClause.length()-1));
			whereClause.append(")");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return whereClause.toString();
	}

	/**
     * 判断是否有重复的MaterialRrn
     * */
    public boolean isAddMLot(List<ADBase> values, MLot mLot) {
    	for (ADBase val : values) {
			if(mLot.getmLotId().equals(((MLot)val).getmLotId())) {
				return false;
			}
		}
    	return true;
    }
	
    /**
     * 获得现用数量
     * */
    public BigDecimal getHasQty(List<ADBase> values, MLot mLot) {
    	BigDecimal hasQtye = BigDecimal.ZERO;
    	for (ADBase adBase : values) {
    		MLot ml = (MLot)adBase;
    		if (mLot.getMaterialName().equals(ml.getMaterialName())) {
    			hasQtye = hasQtye.add(ml.getTransMainQty());
    		}
    	}
    	return hasQtye;
    }

    /**
     * 获得最大用量
     * */
	public BigDecimal getMaxQty(List<WorkOrderBomLine> workOrderBomLines, MLot mLot) {
    	for (WorkOrderBomLine workOrderBomLine : workOrderBomLines){
    		if (workOrderBomLine.getMaterialName().equals(mLot.getMaterialName())) {
    			return workOrderBomLine.getUnitQty().multiply(generationLotQty);
    		}
    	}
    	return BigDecimal.ZERO;
    }
    
	@SuppressWarnings("unchecked")
	private void removeAdapter(Object object) {
		List<ADBase> list = (List<ADBase>) workOrderMlotsField.getListTableManager().getInput();
        List<Object> os = workOrderMlotsField.getListTableManager().getCheckedObject();
        for (Object o : os) {
            ADBase pe = (ADBase) o;
            list.remove(pe);
        }
	}
	
	@SuppressWarnings("unchecked")
	private void startAdapter(Object object) {
		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			if(isByMLot) {
				WorkOrder workOrder = (WorkOrder) queryField.getSelectedObject();
				List<MLot> sourceMLots = (List<MLot>)workOrderMlotsField.getListTableManager().getInput();
				List<MLot> mlotList = new ArrayList<MLot>();
				mlotList.addAll(sourceMLots);
				WorkOrderLot workOrderLot = (WorkOrderLot) workOrderLotsField.getListTableManager().getSelectedObject();
				if (generationLotQty == null || BigDecimal.ZERO.compareTo(generationLotQty) == 0) {
					UI.showInfo(Message.getString(WipExceptionBundle.bundle.WoAddLot()));
					return;
				}
				if (isVerQty) {
					if(!checkMaterialQty(workOrderLot, workOrderBomLines, mlotList)) {
						return;
					}
				}
				List<WorkOrderLot> startWorkOrderLots = new ArrayList<WorkOrderLot>();
				startWorkOrderLots.add(workOrderLot);
				ppManager.startLotByWorkOrder(workOrder, startWorkOrderLots, mlotList, Lot.UNIT_TYPE_QTY, Boolean.TRUE, Env.getSessionContext());
			} else {
				Object workOrder = queryField.getSelectedObject();
				ppManager.startWorkOrder((WorkOrder)workOrder, null, false, Env.getSessionContext());
			
			}
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public boolean checkMaterialQty(WorkOrderLot workOrderLot, List<WorkOrderBomLine> workOrderBomLines, List<MLot> mLots){
		try {
			if(workOrderBomLines != null && workOrderBomLines.size() > 0 &&
					(mLots == null || mLots.size() == 0)){
				UI.showInfo(Message.getString(MMExceptionBundle.bundle.MLotCanNotBeEmpty()));
    			return false;
			}
			
			for (WorkOrderBomLine bomLine : workOrderBomLines){
	    		BigDecimal hasQtye = BigDecimal.ZERO;
	    		for (MLot ml : mLots) {
		    		if (bomLine.getMaterialName().equals(ml.getMaterialName())) {
		    			hasQtye = hasQtye.add(ml.getTransMainQty());
		    		}
	    		}
	    		
	    		BigDecimal needQty = bomLine.getUnitQty().multiply(workOrderLot.getMainQty());
	    		if (needQty.compareTo(hasQtye) == -1) {
	    			UI.showInfo(Message.getString(WipExceptionBundle.bundle.WoLotQtyMoreThan()));
	    			return false;
	    		}
	    	}
	    	return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
    }
	
	private void refreshAdapter(Object object) {
		queryField.refresh();
		materialRequisitionBasicField.getListTableManager().setInput(new ArrayList<Object>());
		workOrderLotsField.getListTableManager().setInput(new ArrayList<Object>());
		if (workOrderMlotsField != null) {
			workOrderMlotsField.getListTableManager().setInput(new ArrayList<Object>());
		}
		generationLotQty = BigDecimal.ZERO;
		workOrderBomLines = null;
	}
	
	 public boolean isVerQty() {
			return isVerQty;
	}

	public void setVerQty(boolean isVerQty) {
		this.isVerQty = isVerQty;
	}

}