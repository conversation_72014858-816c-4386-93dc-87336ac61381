package com.glory.mes.wip.pp.schedule;

import com.glory.framework.base.ui.nattable.FreezeSummaryListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.mes.pp.model.DailyScheduleTable;

/**
 * 按天显示天计划
 */
public class DailyScheduleTableManager extends FreezeSummaryListTableManager {

	public DailyScheduleTableManager(DailyScheduleEditorTableManager tableManager, String[] summaryColumns, int columnPosition, int rowPosition) {
		super(tableManager, summaryColumns, columnPosition, rowPosition);
	}
	
	@Override
	public ItemAdapterFactory createAdapterFactory() {
		ItemAdapterFactory factory = new ItemAdapterFactory();
		factory.registerAdapter(DailyScheduleTable.class, new DailyScheduleAdapter());
		return factory;
	}

}
