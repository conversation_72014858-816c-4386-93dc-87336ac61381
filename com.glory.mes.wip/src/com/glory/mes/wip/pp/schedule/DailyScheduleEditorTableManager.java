package com.glory.mes.wip.pp.schedule;

import org.eclipse.nebula.widgets.nattable.config.IConfigRegistry;
import org.eclipse.nebula.widgets.nattable.layer.cell.ILayerCell;
import org.eclipse.nebula.widgets.nattable.painter.cell.TextPainter;
import org.eclipse.nebula.widgets.nattable.style.IStyle;
import org.eclipse.nebula.widgets.nattable.util.GUIHelper;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.graphics.Rectangle;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.nattable.editor.FixTextCellEditor;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.pp.model.DailyScheduleTable;

/**
 * 按天显示天计划
 */
public class DailyScheduleEditorTableManager extends FixEditorTableManager {
	
	public DailyScheduleEditorTableManager(ADTable adTable) {
		super(adTable);
	}

	@Override
	public boolean validate(VerifyEvent e) {	
		ADField adField = (ADField)e.data;
		ILayerCell layerCell = (ILayerCell)e.widget.getData(FixTextCellEditor.TEXTDATA_CELL);
		int rowIndex = layerCell.getRowIndex();
		DailyScheduleTable scheduleTable = (DailyScheduleTable)getLayer().getBodyDataProvider().getRowObject(rowIndex);
		if (adField != null && scheduleTable != null) {
			if (adField.getName().startsWith("scheduleQty")) {
				int index = Integer.parseInt(adField.getName().substring(11));
				String woId = scheduleTable.getGenerateWoId(index);
				if (!StringUtil.isEmpty(woId)) {
					//如果已经生成Wo,不能再进行修改
					return false;
				} else {
					scheduleTable.setIsDirty(true, index);
				}
			}
		} 
		
		
		
		return true;
	}
	
	@Override
	public TextPainter createTextPaint(String fieldName) {
		if (fieldName.startsWith("scheduleQty")) {
			return new ScheduleTextPainter(fieldName);
		}
		return super.createTextPaint(fieldName);
	}
	
	private class ScheduleTextPainter extends TextPainter {

		private String fieldName;
		private boolean woFlag = false;
		
		public ScheduleTextPainter(String fieldName) {
			super();
			this.fieldName = fieldName;
		}
		
		@Override
		public void paintCell(ILayerCell cell, GC gc, Rectangle rectangle, IConfigRegistry configRegistry) {	
			int index = Integer.parseInt(fieldName.substring(11));
			int rowIndex = cell.getRowIndex();
			DailyScheduleTable scheduleTable = (DailyScheduleTable)getLayer().getBodyDataProvider().getRowObject(rowIndex);
			if (scheduleTable != null) {
				String woId = scheduleTable.getGenerateWoId(index);
				if (!StringUtil.isEmpty(woId)) {
					//如果已经生成Wo,不能再进行修改
					woFlag = true;
				}
			}
			super.paintCell(cell, gc, rectangle, configRegistry);
		}
		
		public void setupGCFromConfig(GC gc, IStyle cellStyle) {
			super.setupGCFromConfig(gc, cellStyle);
			if (woFlag) {
				gc.setForeground(GUIHelper.COLOR_GRAY);
			}
			woFlag = false;
		}
	}
}
