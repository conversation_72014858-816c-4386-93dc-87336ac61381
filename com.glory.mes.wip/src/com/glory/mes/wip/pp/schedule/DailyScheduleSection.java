package com.glory.mes.wip.pp.schedule;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.DailySchedule;
import com.glory.mes.pp.model.DailyScheduleLine;
import com.glory.mes.pp.model.DailyScheduleTable;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.framework.core.exception.ExceptionBundle;

public class DailyScheduleSection extends QueryEntityListSection {
		
	public static final int ROLL_DAY = 31;
	public static final int FIXED_FILEDS_SIZE = 4;
	
	public static final String TABLE_NAME = "PPDailyScheduleTable";
	public static final String TABLE_WONAME = "PPDailyScheduleTableWorkOrder";
	public static final String TABLE_ORDER_NAME = "PPDailyScheduleOrder";
	
	public Date nowDate = new Date();
	public ADTable adTable;
	public DailySchedule dailySchedule;
	protected ListTableManager tableManagerBot;
	public ToolItem itemSave;
	public List<DailyScheduleTable>  workOrders = new ArrayList<DailyScheduleTable>();
	private static final String INIT_WHERE_CLAUSE=" 1 = 1 and docType= '" + WorkOrder.DOC_TYPE_PO + "' AND docStatus = '" + WorkOrder.STATUS_APPROVED + "' and  parentId = null ";
	
	private String whereClause = INIT_WHERE_CLAUSE;
	
	public DailyScheduleSection(ADTable adTable) {
		super(new ListTableManager(adTable, true));
		this.adTable = adTable;
	}
	
	public void createContents(IManagedForm form, Composite parent) {
		createContents(form, parent, 384);
	}

	protected void createNewViewer(Composite client, IManagedForm form)          
	{                                                                            
	  tableManager.setIndexFlag(isIndexFlag());     
	  try {
			//列名改变
	        SimpleDateFormat dateFormater = new SimpleDateFormat("MM/dd");
			
			ADTable cloneTable = (ADTable) adTable.clone();
			cloneTable.setObjectRrn(adTable.getObjectRrn());
			List<ADField> cloneFields = new ArrayList<ADField>();
			int a = 0;
			for (ADField adField : adTable.getFields()) {
				cloneFields.add(adField);
				a++;
				if (a == FIXED_FILEDS_SIZE ) {
					break;
				}
			}
			//DailyScheduletable部分
			Calendar calendar = Calendar.getInstance();
			for (int i = FIXED_FILEDS_SIZE; i < adTable.getFields().size(); i++) {
				ADField itemField = adTable.getFields().get(i);
				int day = calendar.get(Calendar.DATE);
				itemField.setLabel(dateFormater.format(calendar.getTime()));
				itemField.setLabel_zh(dateFormater.format(calendar.getTime()));
				cloneFields.add(itemField);
				calendar.set(Calendar.DATE, day + 1);
			}
			
			cloneTable.setFields(cloneFields);
			DailyScheduleEditorTableManager editorTableManager = new DailyScheduleEditorTableManager(cloneTable);
			tableManager = new DailyScheduleTableManager(editorTableManager, null, FIXED_FILEDS_SIZE, 0);
			tableManager.setAutoSizeFlag(false);
			FormToolkit toolkit = form.getToolkit();
			Composite managerComposite = toolkit.createComposite(client);
			GridData gd1 = new GridData(GridData.FILL_BOTH);
			gd1.heightHint = 240;
			managerComposite.setLayoutData(gd1);
			tableManager.newViewer(managerComposite);
		
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_WONAME);
			tableManagerBot = new ListTableManager(adTable, true);
			tableManagerBot.newViewer(managerComposite);

			tableManager.addDoubleClickListener(new AbstractMouseSelectionAction(){
				public void run(NatTable natTable, MouseEvent event) {
					doubleClick();
				}
			});
			
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	  natTable = tableManager.getNatTable();                                     
	  tableManager.addDoubleClickListener(getDoubleClickListener());             
	  tableManager.addSelectionChangedListener(getSelectChangeListener());       
	}                                                                            
	
	public void doubleClick() {
		DailyScheduleTable dailyScheduleTable = (DailyScheduleTable) getSelectedObject();
		if(dailyScheduleTable!=null){
			List<WorkOrder> wos = adManager.getEntityList(Env.getOrgRrn(), WorkOrder.class, Env.getMaxResult(), 
					" parentId ='"+dailyScheduleTable.getRefDocId()+"' ", " docId DESC ");
			tableManagerBot.setInput(wos);
		}
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolGenerateWorkOrders(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolGenerateWorkOrders(ToolBar tBar) {
		itemSave = new ToolItem(tBar, 8);
		itemSave.setText(Message.getString("common.generate_work_orders"));
		itemSave.setImage(SWTResourceCache.getImage("new"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				generateWorkOrders();
			}
		});
	}
	
	protected void generateWorkOrders() {
		try {
			if (this.getSelectedObject() == null) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			DailyScheduleTable dailyScheduleTable = (DailyScheduleTable) getSelectedObject();
			if(dailyScheduleTable!=null){
				DailyScheduleWoDialog dialog = new DailyScheduleWoDialog(UI.getActiveShell(), adTable, dailyScheduleTable);
				if (Dialog.OK == dialog.open()) {
					refresh();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new ToolItem(tBar, 8);
		itemSave.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}

	protected void saveAdapter() {
		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			List<DailyScheduleTable> dailyScheduleTables = new ArrayList<DailyScheduleTable>();
			List<? extends Object> input = tableManager.getInput();
			for (Object object : input) {
				dailyScheduleTables.add((DailyScheduleTable) object);
			}
			ppManager.saveDailyScheduleByTable(dailySchedule, dailyScheduleTables, Env.getSessionContext());
			UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected QueryForm createQueryFrom(IManagedForm form, Composite queryComp) { 
		ADManager adManager;
		ADTable adTable = null;
		try {
			adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_ORDER_NAME);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return new QueryForm(getADManger(), queryComp, 0, adTable, form.getMessageManager());  
	} 
	
	@Override
	protected void queryAdapter()                                                   
	{                                                                               
	  managedForm.getMessageManager().removeAllMessages();                          
	  if (!getQueryForm().validate()) {                                             
	    return;                                                                     
	  }                                                                             
	                                                                                
	  whereClause = whereClause + getQueryForm().createWhereClause();          
	  whereClause = StringUtil.relpaceWildcardCondition(whereClause);
	  setWhereClause(whereClause);                                                  
	  refresh();   
	  whereClause=INIT_WHERE_CLAUSE;
	}                                                                               

	@Override
	public void refresh() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			//获得所有已通过审核的生产订单
			List<WorkOrder> refWos = adManager.getEntityList(Env.getOrgRrn(), WorkOrder.class, Env.getMaxResult(), 
					whereClause , " docId DESC ");

			//获得当前生效计划
			List<DailySchedule> dailySchedules = adManager.getEntityList(Env.getOrgRrn(), DailySchedule.class, 
					Env.getMaxResult(), "docStatus = '" + WorkOrder.STATUS_APPROVED + "' ", "");
			if (dailySchedules.size() != 1) {
				UI.showError(Message.getString("pp.dailyschedule_is_not_exist"));
				return;
			}
			dailySchedule = dailySchedules.get(0);
			PpManager ppManager = Framework.getService(PpManager.class);
			List<String> status = Arrays.asList(WorkOrder.STATUS_APPROVED);
			List<DailyScheduleLine> dailyScheduleLines = ppManager.getDailyScheduleLineByRefWo(Env.getOrgRrn(), WorkOrder.DOC_TYPE_PO, status);
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			String dateString = formatter.format(nowDate);
			ParsePosition pos = new ParsePosition(0);
			Date newDate = formatter.parse(dateString, pos);
			List<DailyScheduleTable> dailyScheduleTables = DailyScheduleTable.buildDayScheduleDetailByWo(dailySchedule, refWos, newDate, ROLL_DAY, dailyScheduleLines);
			
			tableManager.setInput(dailyScheduleTables);

		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

}