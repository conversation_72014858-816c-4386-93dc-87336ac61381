package com.glory.mes.wip.mm.warehousepart;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.prd.model.Part;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.mm.WareHousePart;
import com.glory.framework.core.exception.ExceptionBundle;

public class WarehousePartProperties extends EntityProperties {

    private WarehousePartForm warehousePartForm;

    public WarehousePartProperties() {
        super();
    }

    @Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
        EntityForm itemForm;
        String tabName = tab.getName();
        if ("WareHousePartRelation".equalsIgnoreCase(tabName)) {
            warehousePartForm = new WarehousePartForm(composite, SWT.NONE, tab, mmng);
            return warehousePartForm;
        } else {
            itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
        }
        return itemForm;
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemSave(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void saveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            boolean saveFlag = true;
            for (IForm detailForm : getDetailForms()) {
                if (!detailForm.saveToObject()) {
                    saveFlag = false;
                }
            }
            if (saveFlag) {
                Warehouse warehouse = (Warehouse) getAdObject();
                if (warehouse == null || warehouse.getObjectRrn() == null) {
                    UI.showInfo(Message.getString("mm.warehouse.part.select.warehouse"));
                    return;
                }

                List<Part> selectActiveParts = (List<Part>) getField(WarehousePartForm.FIELD_ID_PART).getValue();

                List<WareHousePart> wareHouseParts = new ArrayList<WareHousePart>();
                for (Part part : selectActiveParts) {
                    WareHousePart wareHousePart = new WareHousePart();
                    wareHousePart.setPartName(part.getName());
                    wareHousePart.setPartDesc(part.getDescription());
                    wareHouseParts.add(wareHousePart);
                }

                MLotManager mlotManager = Framework.getService(MLotManager.class);
                mlotManager.saveWarehousePart(warehouse, wareHouseParts, Env.getSessionContext());

                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
                refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
    public void refresh() {
        super.refresh();
        try {
            Warehouse warehouse = (Warehouse) getAdObject();
            TableSelectField partList = (TableSelectField) getField("attributeValues");

            if (warehouse != null && warehouse.getObjectRrn() != null) {
                ADManager adManager = Framework.getService(ADManager.class);

                List<Part> parts = new ArrayList<Part>();

                List<WareHousePart> wareHouseParts = adManager.getEntityList(Env.getOrgRrn(), WareHousePart.class, Env.getMaxResult(), 
                        "wareHouseRrn = " + warehouse.getObjectRrn(), "partName");
                for (WareHousePart wareHousePart : wareHouseParts) {
                    List<Part> activeParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Env.getMaxResult(), 
                            "name = '" + wareHousePart.getPartName() + "'", "");
                    if (activeParts != null && activeParts.size() > 0) {
                        parts.add(activeParts.get(0));
                    }
                }
                partList.setValue(parts);

                partList.refresh();
            } else {
                partList.setValue(null);
                partList.refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
}
