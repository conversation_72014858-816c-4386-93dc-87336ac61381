package com.glory.mes.wip.edc.offlinelot;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFComment;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.nebula.widgets.nattable.export.FileOutputStreamProvider;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.IToolItemListener;

import com.glory.common.excel.upload.MapUpload;
import com.glory.edc.EdcEntry;
import com.glory.edc.collection.EdcDataTableComposite;
import com.glory.edc.generaledc.EDCLineImportDialog;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.AbstractEdcSetLine;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADImpExp;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.excel.UploadErrorDialog;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

@SuppressWarnings("restriction")
public class OffLineLotGlcEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.edc.offlinelot.OffLineLotGlcEditor";
	
	public static final String DEFAULT_EXPORT_FILE_NAME = "table_export.xlsx";
	public static final String[] DEFAULT_EXPORT_FILE_TYPE = new String[] { "Excel Workbook (*.xls)", "Excel Workbook (*.xlsx)" };
	public static final String[] DEFAULT_EXPORT_FILE_EXT = new String[] { "*.xls", "*.xlsx" };

	private static final Logger logger = Logger.getLogger(OffLineLotGlcEditor.class);
	
	protected static final String CONTROL_LOTID_ENTERPRESSED = "lotAndComponent-lotId-EnterPressed";
	protected static final String CONTROL_CARRIERID_ENTERPRESSED = "lotAndComponent-carrierId-EnterPressed";
	
	private static final String FIELD_LOTANDCOMPONENT = "lotAndComponent";
	private static final String FIELD_EQPID = "eqpId";
	private static final String FIELD_EQUIPMENINFO = "equipmenInfo";
	private static final String FIELD_ITEMSETNAME = "itemSetName";
	private static final String FIELD_ITEMSET = "itemSet";

	private static final String BUTTON_EXPORT = "export";
	private static final String BUTTON_DCOP = "dcop";
	private static final String BUTTON_REFRESH = "refresh";
	
	protected static final String SELECT_COMPONENT_DALOG = "OffLineComponentDialog";
	protected static final String UPLOAD_TABLE = "OffLineEdcDataUpload";
	

	protected CustomField lotAndComponentField;
	protected EntityFormField lotBasicField;
	protected SearchField eqpIdField;
	protected EntityFormField equipmenInfoField;
	protected SearchField itemSetNameField;
	protected ListTableManagerField itemSetField;
	protected TextField partNameField;
	protected TextField cstateField;
	protected TextField equipmentIdField;
	protected TextField descriptionField;
	protected TextField holdStateField;
	protected RefTableField locationField;
	protected RefTableField subLocationField;
	protected RefTableField stateField;
	
	public CarrierLotCustomComposite carrierLotCustomComposite;
	
	public HeaderText txtLotId, txtCarrierId;
	
	protected Boolean isCaseSensitive;
	protected Lot lot;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		//注销默认回车事件
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_LOTID_ENTERPRESSED));
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED));
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_LOTID_ENTERPRESSED), this::lotIdEnterpressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED), this::carrierIdEnterpressed);
		
		lotAndComponentField = form.getFieldByControlId(FIELD_LOTANDCOMPONENT, CustomField.class);
		carrierLotCustomComposite = (CarrierLotCustomComposite) lotAndComponentField.getCustomComposite();
		
		eqpIdField = form.getFieldByControlId(FIELD_EQPID, SearchField.class);
		equipmenInfoField = form.getFieldByControlId(FIELD_EQUIPMENINFO, EntityFormField.class);
		itemSetNameField = form.getFieldByControlId(FIELD_ITEMSETNAME, SearchField.class);
		itemSetField = form.getFieldByControlId(FIELD_ITEMSET, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(IToolItemListener.TYPE_IMPORT), this::importAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EXPORT), this::exportAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DCOP), this::dcopAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		init();
		
		txtLotId = carrierLotCustomComposite.getTxtLotId();
		txtCarrierId = carrierLotCustomComposite.getTxtCarrierId();

		carrierLotCustomComposite.getLotTableManager().addICheckChangedListener(checkChangedListener);
		eqpIdField.addValueChangeListener(queryEquipmentListener);
		itemSetNameField.addValueChangeListener(queryItemSetListener);
	}
	
	// 设定SearchField栏位宽度
	private void init() {
		GridData gText = new GridData();
		gText.horizontalIndent = DPIUtil.autoScaleUpUsingNativeDPI(23);
		gText.widthHint = DPIUtil.autoScaleUpUsingNativeDPI(200);
		eqpIdField.getControls()[0].setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		eqpIdField.getControls()[1].setLayoutData(gText);
		eqpIdField.refresh();
		
		GridData gText1 = new GridData();
		gText1.widthHint = DPIUtil.autoScaleUpUsingNativeDPI(200);
		itemSetNameField.getControls()[0].setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		itemSetNameField.getControls()[1].setLayoutData(gText1);
		itemSetNameField.refresh();
	}
	
	ICheckChangedListener checkChangedListener = new ICheckChangedListener() {
		@Override
		public void checkChanged(List<Object> eventObjects, boolean checked) {
			List<Object> objects = carrierLotCustomComposite.getLotTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<Lot> checkLots = objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
				try {
					carrierLotCustomComposite.loadComponentUnits(checkLots);
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
				lot = checkLots.get(0);
				if (lot != null) {
					lot = searchLot(lot.getLotId());
				}
				setAdObject(lot);
				refresh();
			}
		}
	};
	
	IValueChangeListener queryEquipmentListener = new IValueChangeListener() {
		public void valueChanged(Object arg0, Object arg1) {
			if (eqpIdField.getValue() != null
					&& ((String) eqpIdField.getValue()).trim()
							.length() > 0) {
				try {
					RASManager rasManager = Framework.getService(RASManager.class);
					Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(),
							(String) eqpIdField.getValue());
					equipmenInfoField.setValue(equipment);
					equipmenInfoField.refresh();
				}catch(Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	};
	
	IValueChangeListener queryItemSetListener = new IValueChangeListener() {
		public void valueChanged(Object arg0, Object arg1) {
			if (itemSetNameField.getValue() != null
					&& ((String) itemSetNameField.getValue()).trim()
							.length() > 0) {
				try {
					AbstractEdcSet ItemSet = (AbstractEdcSet) itemSetNameField.getData();
					StringBuffer whereClause = new StringBuffer(" 1 = 1 and edcSetRrn = '" + ItemSet.getObjectRrn() +"'");
					
					ADManager adManager = Framework.getService(ADManager.class);
					List<AbstractEdcSetLine> itemSetLines = adManager.getEntityList(Env.getOrgRrn(), AbstractEdcSetLine.class,
							Integer.MIN_VALUE, Integer.MAX_VALUE, whereClause.toString(), null);
					for (AbstractEdcSetLine line : itemSetLines) {
					    String lsl = (line.getLslString() == null || "".equals(line.getLslString().trim())) ? "-" : line.getLslString();
			            String sl = (line.getSlString() == null || "".equals(line.getSlString().trim())) ? "-" : line.getSlString();
			            String usl = (line.getUslString() == null || "".equals(line.getUslString().trim())) ? "-" : line.getUslString();
			            if (StringUtil.isEmpty(line.getLslString()) && StringUtil.isEmpty(line.getUslString()))
			            	continue;
			            
			            line.setFormula(lsl + "/" + sl + "/" + usl);
					}
					itemSetField.getListTableManager().setInput(itemSetLines);
				}catch(Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	};
	
	private void lotIdEnterpressed(Object obj) {
		String lotId = txtLotId.getText();
		if (!StringUtil.isEmpty(lotId)) {
			if (!isLotIdCaseSensitive()) {
				lotId = lotId.toUpperCase();
			}
			getLotByLotId(lotId);
		}
	}
	
	private void carrierIdEnterpressed(Object obj) {
		String carrierId = txtCarrierId.getText();
		if (!StringUtil.isEmpty(carrierId)) {
			getLotsByCarrierId(carrierId);
		}
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
	public void getLotByLotId(String lotId) {
		try {
			lot = searchLot(lotId);
			if (lot != null) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.add(lot);
				
				carrierLotCustomComposite.getLotTableManager().setInput(lots);
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				txtLotId.setText(lotId);
				// 默认全选
				if (carrierLotCustomComposite.isCheckFlag()) {
					carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
				}
				carrierLotCustomComposite.getLotTableManager().refresh();
				setAdObject(lot);
				refresh();
				carrierLotCustomComposite.loadComponentUnits(lot);
				if(!StringUtil.isEmpty(lot.getDurable())) {
					txtCarrierId.setText(lot.getDurable());
				}else {
					txtCarrierId.setText("");
				}	
				txtLotId.focusing();
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.setText(lotId);
				
				carrierLotCustomComposite.getLotTableManager().setSelection(new StructuredSelection(new Object[] {new Lot()}));
				
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtLotId.warning();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isNotEmpty(lots)) {
					// Load Current Step
					lot = searchLot(lots.get(0).getLotId());
					
					carrierLotCustomComposite.getLotTableManager().setInput(lots);
					// 默认只选择一条
					if (carrierLotCustomComposite.isCheckFlag()) {
//						for (Lot lot : lots) {
//							carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
//						}
						carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
					}
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					carrierLotCustomComposite.getLotTableManager().refresh();
					
					if (lots != null && lots.size() > 0) {
						setAdObject(lot);
						refresh();
					}
					
					carrierLotCustomComposite.loadComponentUnits(lots);	
					txtLotId.setText(lots.get(0).getLotId());
					txtCarrierId.focusing();
				} else {
					carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
					carrierLotCustomComposite.getLotTableManager().refresh();
				}
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtCarrierId.warning();
			}
			
			if (carrierLotCustomComposite.getLotDetailsForm() != null) {
				carrierLotCustomComposite.getLotDetailsForm().setObject(new Lot());
				carrierLotCustomComposite.getLotDetailsForm().loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public Lot searchLot(String lotId) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			return lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	public void setAdObject(Lot lot) {
		try {
			if (lot != null) {	
				try {
					if (lot.getObjectRrn() != null) {
						//组件和批次信息
						SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
						if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
							if (carrierLotCustomComposite != null && carrierLotCustomComposite.getLotTableManager() != null) {
								carrierLotCustomComposite.getLotTableManager().update(lot);
							}
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			} 
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh() {
		if (txtLotId != null) {
			txtLotId.selectAll();
		}
		if (txtCarrierId != null) {
			txtCarrierId.selectAll();
		}
	}

	private void importAdapter(Object object) {
		if(lot != null && eqpIdField.getValue() != null && itemSetNameField.getData() != null) {
			String equipment = ((String)this.eqpIdField.getValue()).trim().toUpperCase();
			AbstractEdcSet itemSet = (AbstractEdcSet) itemSetNameField.getData();
			
			//获取采集项
			AbstractEdcSetLine edcSetLine = (AbstractEdcSetLine) itemSetField.getListTableManager().getSelectedObject();
			if(itemSet instanceof EdcItemSet && edcSetLine == null) {
				UI.showError(Message.getString("wip.line_is_not_select"));
				return;
			}
			
			//获取导入数据map
			MapUpload mapUpload = new MapUpload();
			List<Map> valueMap = mapUpload.run();
			OffLineEdcDataUpload upload = new OffLineEdcDataUpload(form.getAuthority(), UPLOAD_TABLE, itemSet, lot, equipment, edcSetLine);
			if(upload.preCheck(valueMap)) {
				if(CollectionUtils.isNotEmpty(upload.uploadTemps)) {
					List<EdcData> edcDatas = processList(upload.uploadTemps);
					//弹出显示框
					EDCLineImportDialog dialog = new EDCLineImportDialog(edcDatas, itemSet, edcSetLine);
					if (Dialog.OK == dialog.open()) {
						upload.cudEntityList();
					}
				}
			}else {
				UploadErrorDialog dialog = new UploadErrorDialog(upload.progress.getErrLogs());
				if(upload.progress.getErrLogs() != null && upload.progress.getErrLogs().size() > 0) {
		        	dialog.open();
	        	}
			}
		}
	}

	private void dcopAdapter(Object object) {
		if (this.eqpIdField.getValue() == null
				|| "".equals(((String)this.eqpIdField.getValue()).trim())) {
			UI.showError(Message.getString("edc.equipmentNumber_cannot_null"),
					Message.getString("edc.alert_message_title"));
			return;
		} else if (this.itemSetNameField.getValue() == null
				|| "".equals(((String)this.itemSetNameField.getValue()).trim())) {
			UI.showError(Message.getString("edc.data_set_cannot_null"), Message
					.getString("edc.alert_message_title"));
			return;
		} else if (this.lot == null) {
			UI.showError(Message.getString("edc.lot_cannot_null"), Message
					.getString("edc.alert_message_title"));
		}

		try {
			RASManager rasManager = Framework.getService(RASManager.class);
			Equipment eqp = rasManager.getEquipmentByEquipmentId(Env
					.getOrgRrn(), ((String)this.eqpIdField.getValue()).toUpperCase());
			if (eqp == null) {
				UI.showError(Message.getString("edc.eqp_not_exist"), Message
						.getString("edc.alert_message_title"));
			}
		} catch (Exception e) {
			UI.showError(Message.getString("edc.eqp_not_exist"), Message
					.getString("edc.alert_message_title"));
			return;
		}

		try {
			String lotId = lot.getLotId();
			if (!isLotIdCaseSensitive()) {
				lotId = lotId.toUpperCase();
			}
			lot = LotProviderEntry.getLot(lotId);
			if (lot == null) {
				UI.showError(Message.getString("edc.lot_not_exist"), Message
						.getString("edc.alert_message_title"));
				return;
			}
		} catch (Exception e) {
			UI.showError(Message.getString("edc.lot_not_exist"), Message.getString("edc.alert_message_title"));
			return;
		}

		try {
			AbstractEdcSet abstractEdcSet = (AbstractEdcSet) itemSetNameField.getData();
			AbstractEdcSet itemSet = null;
			BASManager basManager = Framework.getService(BASManager.class);
			if (abstractEdcSet instanceof EdcItemSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcItemSet.class, abstractEdcSet.getName());
			} else if (abstractEdcSet instanceof EdcTextSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcTextSet.class, abstractEdcSet.getName());
			} else if (abstractEdcSet instanceof EdcBinSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcBinSet.class, abstractEdcSet.getName());
			}

			if (itemSet == null) {
				UI.showError(Message.getString("edc.data_set_cannot found"), Message.getString("edc.alert_message_title"));
			}  else {
				lot.setAttribute1(((String)this.eqpIdField.getValue()).trim().toUpperCase());
				EdcSetCurrent edcCurrent = new EdcSetCurrent();
				edcCurrent.setItemSetRrn(itemSet.getObjectRrn());
				
				EdcEntry.open(EdcData.EDCFROM_OFFLINELOT, edcCurrent, null, lot);
			}
		} catch (Exception e) {
			UI.showError(Message.getString("edc.data_set_cannot found"),
					Message.getString("edc.alert_message_title"));
		}
	}
	
	private void exportAdapter(Object object) {
		try {
			AbstractEdcSet itemSet = null;
			BASManager basManager = Framework.getService(BASManager.class);
			AbstractEdcSet abstractEdcSet = (AbstractEdcSet) itemSetNameField.getData();
			AbstractEdcSetLine edcSetLine = (AbstractEdcSetLine) itemSetField.getListTableManager().getSelectedObject();
			List<ComponentUnit> components = (List<ComponentUnit>) carrierLotCustomComposite.getCompTableManager().getInput();
			
			List<String> selectedComponentIds = new ArrayList<String>();
			boolean isComponnet = false;// 是否组件
			int itemNumber = 0;// 采集片数
			String type = "";// 类型
			
			if (abstractEdcSet instanceof EdcItemSet) {// item类型
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcItemSet.class,abstractEdcSet.getName());
				if(edcSetLine == null) {
					UI.showError(Message.getString("wip.line_is_not_select"));
					return;
				}
				// 获取采集项
				EdcItemSetLine line = (EdcItemSetLine) edcSetLine;
				// 类型
				type = "ITEM";
				itemNumber = line.getItem().intValue();
				if (line.getIsItemUsePercent()) {
					// 使用百分比
					itemNumber = EdcItemSetLine.getActualItemNumber(lot.getMainQty(), new BigDecimal(itemNumber));
					if (itemNumber < 1) {
						// 不允许小于1
						itemNumber = 1;
					}
				}
				// 判断是否Componnet
				isComponnet = isComponentUnitType() && !EdcItem.SAMPLETYPE_ITEM.equals(line.getSampleType());
			} else if (abstractEdcSet instanceof EdcTextSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcTextSet.class,abstractEdcSet.getName());
				// 类型
				type = "TEXT";
				
				isComponnet = false;//选择组件
			} else if (abstractEdcSet instanceof EdcBinSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcBinSet.class,abstractEdcSet.getName());
				// 类型
				type = "BIN";

				itemNumber = Integer.MAX_VALUE;
				
				if(lot.getSubUnitType().equals(MLot.UNIT_TYPE_QTY)) {
					isComponnet = false;//不选择组件
				}else {
					isComponnet = true;//选择组件
				}
			}
			
			//是否需要显示组件选择
			if(isComponnet) {
				OffLineComponentDialogEditor baseDialog = new OffLineComponentDialogEditor(SELECT_COMPONENT_DALOG, null, eventBroker, components, itemNumber);
				if (Dialog.OK == baseDialog.open()) {
					List<Object> objects = (List<Object>) baseDialog.lotListField.getListTableManager().getCheckedObject();
					if (CollectionUtils.isNotEmpty(objects)) {
						for (Object obj : objects) {
							selectedComponentIds.add(((ComponentUnit) obj).getComponentId());
						}
					}
				} else {
					UI.showError(Message.getString("wip.not_select_component"));
					return;
				}
			}

			if (itemSet == null) {
				UI.showError(Message.getString("edc.data_set_cannot found"), Message.getString("edc.alert_message_title"));
			} else {
				exportTemplate(form.getAuthority(), type, itemSet, edcSetLine, selectedComponentIds, isComponnet, itemNumber);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	

	/**
	 * 导出模版
	 * authorityName 菜单名称
	 * itemSet 采集项集
	 * edcSetLine 采集项
	 * selectedComponentIds 选择组件号
	 * isComponnet 是否组件
	 * itemNumber item数
	 * @param adAuthorityName
	 */
	public void exportTemplate(String authorityName,String type, AbstractEdcSet itemSet, AbstractEdcSetLine edcSetLine, List<String> selectedComponentIds, boolean isComponnet, int itemNumber) {
		BufferedOutputStream bufferedOutputStream = null;
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			String fileName = StringUtil.isEmpty(authorityName) ? DEFAULT_EXPORT_FILE_NAME : authorityName.replace(".", "") + ".xlsx";
			FileOutputStreamProvider provider = new FileOutputStreamProvider(fileName, DEFAULT_EXPORT_FILE_TYPE, DEFAULT_EXPORT_FILE_EXT);
			OutputStream outputStream = provider.getOutputStream(UI.getActiveShell());
			if (outputStream == null) {
				return;
			}
			bufferedOutputStream = new BufferedOutputStream(outputStream);

			XSSFSheet sheet = workbook.createSheet();

			// 从ADImpExpFieldMap表中导出
			ADManager adManager = Framework.getService(ADManager.class);
			ADImpExp adImpExp = adManager.getADImpExpByAuthorityName(Env.getOrgRrn(), authorityName, null, true);
			if (adImpExp != null) {
				List<ADField> exportFields = adManager.buildADFieldByADImpExp(adImpExp, true, Env.getSessionContext());
				if (CollectionUtils.isNotEmpty(exportFields)) {
					XSSFDrawing draw = sheet.createDrawingPatriarch();
					XSSFComment comment = null;
					XSSFRow firstRow = sheet.createRow(0);// 第一行表头
					int i = 0;
					for (ADField exportField : exportFields) {
						XSSFCell cell = firstRow.createCell(i);
						if (!StringUtil.isEmpty(exportField.getColumnName())) {
							cell.setCellValue(exportField.getColumnName());
						} else {
							cell.setCellValue(exportField.getName().toUpperCase());
						}
						comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
						comment.setString(DefaultDownloadWriter.getCellComment(exportField));
						cell.setCellComment(comment);
						i++;
					}
					
					int v = 5;
					//根据采集点位生成列
					if(type.equals("ITEM")) {
						EdcItemSetLine line = (EdcItemSetLine)edcSetLine;
						String[] itemDescs = EdcDataTableComposite.createIds(line, selectedComponentIds, isComponnet,itemNumber);
						if(itemDescs != null && itemDescs.length > 0) {//插入列
							for(int c = 0; c < itemDescs.length; c++) {
								XSSFCell cell = firstRow.createCell(i);
								cell.setCellValue(itemDescs[c]);
								comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
								comment.setString(itemDescs[c]);
								cell.setCellComment(comment);
								sheet.autoSizeColumn(i, true);
								i++;
								v++;
							}
						}
					}else if(type.equals("BIN")) {
						List<EdcBinSetLine> binSetLines = (List<EdcBinSetLine>) itemSetField.getListTableManager().getInput();
						if(CollectionUtils.isNotEmpty(selectedComponentIds)) {
							for(String componentId : selectedComponentIds) {
								for(EdcBinSetLine binSetLine : binSetLines) {
									XSSFCell cell = firstRow.createCell(i);
									cell.setCellValue(componentId + "-" + binSetLine.getName());
									comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
									comment.setString(componentId + "-" + binSetLine.getName());
									cell.setCellComment(comment);
									sheet.autoSizeColumn(i, true);
									i++;
									v++;
								}
							}
						}else {
							if(CollectionUtils.isNotEmpty(binSetLines)) {
								for(EdcBinSetLine binSetLine : binSetLines) {
									XSSFCell cell = firstRow.createCell(i);
									cell.setCellValue(itemSet.getName() + "-" + binSetLine.getName());
									comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
									comment.setString(itemSet.getName() + "-" + binSetLine.getName());
									cell.setCellComment(comment);
									sheet.autoSizeColumn(i, true);
									i++;
									v++;
								}
							}
						}
					}else if(type.equals("TEXT")) {
						List<EdcTextSetLine> textSetLines = (List<EdcTextSetLine>) itemSetField.getListTableManager().getInput();
						for (EdcTextSetLine textSetLine : textSetLines) {
							XSSFCell cell = firstRow.createCell(i);
							cell.setCellValue(itemSet.getName() + "-" + textSetLine.getName());
							comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
							comment.setString(itemSet.getName() + "-" + textSetLine.getName());
							cell.setCellComment(comment);
							sheet.autoSizeColumn(i, true);
							i++;
							v++;
						}
					}
					
					//写入组件信息
					String componentIds = "";
					if(CollectionUtils.isNotEmpty(selectedComponentIds)) {
						int n = 1;
						for(String componentId : selectedComponentIds) {
							if(n == selectedComponentIds.size()) {
								componentIds += componentId;
							}else {
								componentIds += componentId + ";"; 
							}
							n++;
						}
					}
					
					//写入导出模板值
					XSSFRow row = sheet.createRow(1);
					row.createCell(0).setCellValue(lot.getLotId());
					
					row.createCell(1).setCellValue(((String)this.eqpIdField.getValue()).trim().toUpperCase());
					
					row.createCell(2).setCellValue(itemSet.getName());
					
					row.createCell(3).setCellValue(itemSet.getEdcType());
					
					if(type.equals("BIN") || type.equals("TEXT")) {
						row.createCell(4).setCellValue("");
					}else {
						row.createCell(4).setCellValue(edcSetLine.getName());
					}
					
					//修复值compomtUnit,itemName会被冲突问题
					row.createCell(5).setCellValue(componentIds);
					
					//写入默认空的值，方法拿不到空值列
					for(int k = 6; k < v; k++) {
						row.createCell(k).setCellValue("");
					}
				}
				// 设置列宽自适应
				int columnNum = 0;
				for (ADField adField : exportFields) {
					sheet.autoSizeColumn(columnNum, true);
					columnNum++;
				}
			}

			workbook.write(bufferedOutputStream);
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonExportSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			try {
				if (bufferedOutputStream != null) {
					bufferedOutputStream.close();
				}
				workbook.close();
			} catch (IOException e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	protected boolean isComponentUnitType() {
		if (lot != null) {
			if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				return true;
			} else {
				return false;
			}
		}
		return false;
	}
	
	/*
	 * 特殊处理EdcData数据，实际测量的数据超过设置的EdcSet时：
	 * 例如：设置测量点数384点，测量片数1片，实际测量片数2片 
	 * 数据存储为：ComponentList(片1；片2) DcData(384 * 2) 界面查询数据显示wafer两片，data数只显示片1数据。
	 * 数据显示异常，做如下处理：
	 * 1.当DcData数能被SubgroupSize整除且结果超过1时，将EdcData数据一分为多
	 * 2.不满足以上条件保留当前数据显示
	*/
	private List<EdcData> processList(List<EdcData> adList){
		List<EdcData> newEdcDatas = new ArrayList<>();
		try {
			for(EdcData object : adList) {
				EdcData edcData = (EdcData) object;
				String [] data = edcData.getDcData().split(";",-1);
				String [] dataName = edcData.getDcName().split(";",-1);
				List<String> dataList = Arrays.asList(data);
				List<String> dataNameList = Arrays.asList(dataName);
				List<String> currList = null;
				List<String> dcNameCurrList = null;
				if(edcData.getSubgroupSize() != null && edcData.getSubgroupSize() != 0 
						&& data.length % edcData.getSubgroupSize() == 0 && edcData.getComponentList() != null) {
					int subgroupSize = edcData.getDcData().split(";",-1).length / edcData.getComponentList().split(";",-1).length;
					edcData.setSubgroupSize((long)subgroupSize);
					int length = (int) (data.length / edcData.getSubgroupSize());
					String [] component = edcData.getComponentList().split(";",-1);
					if(length > 1 && component.length == length) {
						for(int i = 0; i < length; i++) {
							EdcData cloneData = (EdcData) edcData.clone();
							currList = dataList.subList(i * edcData.getSubgroupSize().intValue(), (i + 1) * edcData.getSubgroupSize().intValue());
							dcNameCurrList = dataNameList.subList(i * edcData.getSubgroupSize().intValue(), (i + 1) * edcData.getSubgroupSize().intValue());
							cloneData.setComponentList(component[i]);
							cloneData.setDcData(currList.stream().map(String :: valueOf).collect(Collectors.joining(";")));
							cloneData.setDcName(dcNameCurrList.stream().map(String :: valueOf).collect(Collectors.joining(";")));
							cloneData.setObjectRrn(edcData.getObjectRrn());
							newEdcDatas.add(cloneData);
						}
					}else {
						newEdcDatas.add(edcData);
					}
				} else {
					newEdcDatas.add(edcData);
				}
			}
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
		}
		return newEdcDatas;
	}

	private void refreshAdapter(Object object) {
		eqpIdField.setValue(null);
		itemSetNameField.setValue(null);
		txtLotId.setText("");
		txtCarrierId.setText("");	
	}

}