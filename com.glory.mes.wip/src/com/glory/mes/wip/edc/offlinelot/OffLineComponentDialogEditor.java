package com.glory.mes.wip.edc.offlinelot;


import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.ComponentUnit;

public class OffLineComponentDialogEditor extends GlcBaseDialog { 

	public static final String EDITOR_ID = "bundleclass://com.glory.wip/com.glory.mes.wip.OffLineComponentDialogEditor";

	private static final String FIELD_LOTLIST = "lotList";

	public ListTableManagerField lotListField;
	
	private static int MIN_DIALOG_WIDTH = 800;
	private static int MIN_DIALOG_HEIGHT = 350;
	
	private List<ComponentUnit> componentUnits = new ArrayList<ComponentUnit>();
	
	private int itemNumber = 0;
	
	public OffLineComponentDialogEditor(String adFormName, String authority, IEventBroker eventBroker, List<ComponentUnit> componentUnits, int itemNumber) {
		super(adFormName, authority, eventBroker);
		this.componentUnits = componentUnits;
		this.itemNumber = itemNumber;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotListField = form.getFieldByControlId(FIELD_LOTLIST, ListTableManagerField.class);

		lotListField.getListTableManager().setInput(componentUnits);
	}
	
	@Override
	protected void okPressed() {
		try {
			// 处理componentId
			if (itemNumber < lotListField.getListTableManager().getCheckedObject().size()) {
				UI.showError(Message.getString("edc_waferId_toomuch") + itemNumber);
				return;
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}


	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}