package com.glory.mes.wip.edc.tecn;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.common.context.client.ContextManager;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextValue;
import com.glory.edc.client.EDCManager;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class EdcTecnContextActiveDialog extends BaseTitleDialog {
	
    private static final Logger logger = Logger.getLogger(EdcTecnContextActiveDialog.class);

    private static int MIN_DIALOG_WIDTH = 600;
    private static int MIN_DIALOG_HEIGHT = 500;
    
    private static final String CONTEXT_NAME = "EDCTECN";
    
    private static final String TABLE_NAME = "BASInactiveContextValues";
    private ListTableManager tableManager;
    
    public EdcTecnContextActiveDialog(Shell parentShell) {
        super(parentShell);
    }

	@Override
	protected Control buildView(Composite parent) {
        setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(String.format(Message.getString(ExceptionBundle.bundle.CommonEditor()),
                Message.getString(ExceptionBundle.bundle.CommonActive())));
         FormToolkit toolkit = new FormToolkit(parent.getDisplay());
        Composite tableCom = toolkit.createComposite(parent, SWT.NULL);
        tableCom.setLayout(new GridLayout(4, false));
        tableCom.setLayoutData(new GridData(GridData.FILL_BOTH));
        try {
            ADManager adManager = Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
            ContextManager contextManager = Framework.getService(ContextManager.class);

            Context context = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME);

            // 获取该Context所有的失效的Context Value
            List<ContextValue> contextValues = adManager.getEntityList(Env.getOrgRrn(),
                    ContextValue.class, Env.getMaxResult(),
                    " status = 'InActive' AND contextRrn = " + context.getObjectRrn(), "");

            List<ADField> contextAdFields = contextManager.getContextADTable(Env.getOrgRrn(), context.getName()).getFields();

            for (ADField adField : adTable.getFields()) {
                if (adField.getLabel().contains("Inactive Time")) {
                    // 将失效时间添加到列表显示
                    contextAdFields.add(adField);
                }
                if (adField.getLabel().contains("Operator")) {
                    // 将操作员添加到列表显示
                    contextAdFields.add(adField);
                }
            }
            adTable.setFields(contextAdFields);

            tableManager = new ListTableManager(adTable, true);
            tableManager.newViewer(tableCom);
            tableManager.setInput(contextValues);
        } catch (Exception e) {
        	ExceptionHandlerManager.asyncHandleException(e);
        }
        return parent;
    }

    @Override
    protected void okPressed() {
        try {
        	EDCManager edcManager = Framework.getService(EDCManager.class);
            List<Object> checkedObjects = tableManager.getCheckedObject();
            if (checkedObjects == null || checkedObjects.size() == 0) {
                super.okPressed();
                return;
            }

            List<ContextValue> contextValues = new ArrayList<ContextValue>();
            for (Object object : checkedObjects) {
                ContextValue contextValue = (ContextValue) object;
                contextValues.add(contextValue);
            }
            edcManager.activeEdcTecn(contextValues, Env.getSessionContext());         
            UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonActiveSuccess()));
        } catch (Exception e) {
        	ExceptionHandlerManager.asyncHandleException(e);
            logger.error("ContextSetActiveDialog okPressed()", e);
        }
        super.okPressed();
    }

    @Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }
}
