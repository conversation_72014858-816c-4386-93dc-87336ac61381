package com.glory.mes.wip.flag.step;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.WipFlagDef;
import com.glory.mes.wip.model.WipFlagStep;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPFlagStepEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.flag.step.WIPFlagStepEditor";

	private static final String FIELD_FLAGSTEPQUERY = "flagStepQuery";
	private static final String FIELD_FLAGSTEPSETUPGLC = "flagStepSetupGlc";
	private static final String FIELD_PARTCATEGORYINFO = "partCategoryInfo";
	private static final String FIELD_FLAGLIST = "flagList";
	private static final String FIELD_FLAGSTEPSETUP = "flagStepSetup";
	private static final String FIELD_STEPLIST = "stepList";
	private static final String FIELD_CATEGORY = "category";
	private static final String FIELD_PARTNAME = "partName";

	private static final String BUTTON_COPYFROM = "copyFrom";
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_ADD = "add";
	private static final String BUTTON_REMOVE = "remove";

	protected QueryFormField flagStepQueryField;
	protected GlcFormField flagStepSetupGlcField;
	protected EntityFormField partCategoryInfoField;
	protected ListTableManagerField flagListField;
	protected EntityFormField flagStepSetupField;
	protected ListTableManagerField stepListField;
	protected RefTableField categoryField;
	protected RefTableField partNameField;
	protected TextField partNameLikeField;

	private Map<Integer, List<WipFlagStep>> flagStepMap = Maps.newHashMap();
	private Integer currentFlagBit;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		flagStepQueryField = form.getFieldByControlId(FIELD_FLAGSTEPQUERY, QueryFormField.class);
		partNameLikeField = flagStepQueryField.getQueryForm().getFieldByControlId(FIELD_PARTNAME, TextField.class);
		
		subscribeAndExecute(eventBroker, flagStepQueryField.getFullTopic(GlcEvent.EVENT_QUERY), this::flagStepQuery);
		subscribeAndExecute(eventBroker, flagStepQueryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::flagStepQuerySelectionChanged);
		
		flagStepSetupGlcField = form.getFieldByControlId(FIELD_FLAGSTEPSETUPGLC, GlcFormField.class);
		
		partCategoryInfoField = flagStepSetupGlcField.getFieldByControlId(FIELD_PARTCATEGORYINFO, EntityFormField.class);
		categoryField = partCategoryInfoField.getFieldByControlId(FIELD_CATEGORY, RefTableField.class);
		subscribeAndExecute(eventBroker, categoryField.getFullTopic(GlcEvent.EVENT_VALUECHANGE), this::categoryValueChange);
		partNameField = partCategoryInfoField.getFieldByControlId(FIELD_PARTNAME, RefTableField.class);
		
		flagListField = flagStepSetupGlcField.getFieldByControlId(FIELD_FLAGLIST, ListTableManagerField.class);
		flagStepSetupField = flagStepSetupGlcField.getFieldByControlId(FIELD_FLAGSTEPSETUP, EntityFormField.class);
		subscribeAndExecute(eventBroker, flagListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::flagListSelectionChanged);
		stepListField = flagStepSetupGlcField.getFieldByControlId(FIELD_STEPLIST, ListTableManagerField.class);
	
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_COPYFROM), this::copyFromAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, flagStepSetupGlcField.getFullTopic(BUTTON_ADD), this::addAdapter);
		subscribeAndExecute(eventBroker, flagStepSetupGlcField.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
		
		init();
	}

	/**
	 * 初始化
	 */
	private void init() {
		currentFlagBit = null;
		flagStepMap.clear();
		
		partCategoryInfoField.setValue(new WipFlagStep());
		partCategoryInfoField.refresh();
		
		flagListField.setValue(new ArrayList<WipFlagDef>());
		flagListField.refresh();
		
		flagStepSetupField.setValue(new WipFlagStep());
		flagStepSetupField.refresh();
		
		stepListField.setValue(new ArrayList<WipFlagStep>());
		stepListField.refresh();
	}
	
	/**
	 * 客制化查询
	 * @param object
	 */
	private void flagStepQuery(Object object) {
		try {		
			String whereClause = "1=1";
			if (StringUtils.isNotEmpty(partNameLikeField.getText())) {
				whereClause += " AND partName like '" + partNameLikeField.getText() + "'";
			}
		
			ADManager adManager = Framework.getService(ADManager.class);
			List<WipFlagStep> wipFlagSteps = adManager.getEntityList(Env.getOrgRrn(), WipFlagStep.class, Env.getMaxResult(), whereClause, "");	
			if (CollectionUtils.isNotEmpty(wipFlagSteps)) {
				Map<String, List<WipFlagStep>> flagStepMap = new HashMap<String, List<WipFlagStep>>();
				for (WipFlagStep wipFlagStep : wipFlagSteps) {
					if (flagStepMap.containsKey(wipFlagStep.getCategory() + wipFlagStep.getPartName())) {
						List<WipFlagStep> flagSteps = flagStepMap.get(wipFlagStep.getCategory() + wipFlagStep.getPartName());
						flagSteps.add(wipFlagStep);
						flagStepMap.put(wipFlagStep.getCategory() + wipFlagStep.getPartName(), flagSteps);
					} else {
						List<WipFlagStep> flagSteps = new ArrayList<WipFlagStep>();
						flagSteps.add(wipFlagStep);
						flagStepMap.put(wipFlagStep.getCategory() + wipFlagStep.getPartName(), flagSteps);
					}
				}
				
				List<WipFlagStep> queryWipFlagSteps = Lists.newArrayList();
				for (String key : flagStepMap.keySet()) {
					WipFlagStep wipFlagStep = WipFlagStep.buildProductFlag(flagStepMap.get(key));
					wipFlagStep.setCategory(flagStepMap.get(key).get(0).getCategory());
					wipFlagStep.setPartName(flagStepMap.get(key).get(0).getPartName());
					queryWipFlagSteps.add(wipFlagStep);
				}				
				flagStepQueryField.getQueryForm().getTableManager().setInput(queryWipFlagSteps);
			} else {
				flagStepQueryField.getQueryForm().getTableManager().setInput(null);	
			}
			flagStepQueryField.getQueryForm().getTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;																																																												
		}
	}

	/**
	 * 选中查询行
	 * @param object
	 */
	private void flagStepQuerySelectionChanged(Object object) {
		try {
			if (object == null) {
				return;
			}
			org.osgi.service.event.Event event = (org.osgi.service.event.Event) object;		
			WipFlagStep wipFlagStep = (WipFlagStep) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (wipFlagStep == null) {
				return;
			}
			
			partCategoryInfoField.setValue(wipFlagStep);
			partCategoryInfoField.refresh();
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<WipFlagDef> wipFlagDefs = adManager.getEntityList(Env.getOrgRrn(), WipFlagDef.class, Env.getMaxResult(), 
					" category = '" + wipFlagStep.getCategory() + "'", " flagBit ");
			flagListField.setValue(wipFlagDefs);
			flagListField.refresh();
			
			currentFlagBit = null;
			flagStepMap.clear();
			
			List<WipFlagStep> wipFlagSteps = null;
			if (StringUtil.isEmpty(wipFlagStep.getPartName())) {
				wipFlagSteps = adManager.getEntityList(Env.getOrgRrn(), WipFlagStep.class, Env.getMaxResult(), 
						" category = '" + wipFlagStep.getCategory() + "' AND (partName is null OR partName = '')", " flagBit ");
			} else {
				wipFlagSteps = adManager.getEntityList(Env.getOrgRrn(), WipFlagStep.class, Env.getMaxResult(), 
						" category = '" + wipFlagStep.getCategory() + "' AND partName = '" + wipFlagStep.getPartName() + "'", " flagBit ");
			}			
			if (CollectionUtils.isNotEmpty(wipFlagSteps)) {
				for (WipFlagStep flagStep : wipFlagSteps) {
					if (flagStepMap.containsKey(flagStep.getFlagBit())) {
						List<WipFlagStep> flagSteps = flagStepMap.get(flagStep.getFlagBit());
						flagSteps.add(flagStep);
						flagStepMap.put(flagStep.getFlagBit(), flagSteps);
					} else {
						List<WipFlagStep> flagSteps = new ArrayList<WipFlagStep>();
						flagSteps.add(flagStep);
						flagStepMap.put(flagStep.getFlagBit(), flagSteps);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void categoryValueChange(Object object) {
		try {
			if (object == null) {
				return;
			}
			org.osgi.service.event.Event event = (org.osgi.service.event.Event) object;		
			String category = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (category == null) {
				return;
			}
			ADManager adManager = Framework.getService(ADManager.class);
			List<WipFlagDef> wipFlagDefs = adManager.getEntityList(Env.getOrgRrn(), WipFlagDef.class, Env.getMaxResult(), 
					" category = '" + category + "'", " flagBit ");
			flagListField.setValue(wipFlagDefs);
			flagListField.refresh();
			
			flagStepSetupField.setValue(new WipFlagStep());
			flagStepSetupField.refresh();
			
			stepListField.getListTableManager().setInput(null);
			stepListField.getListTableManager().refresh();
			
			currentFlagBit = null;
			flagStepMap.clear();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 添加
	 * @param object
	 * @throws Exception 
	 */
	private void addAdapter(Object object) {
		try {
			((EntityForm)flagStepSetupField.getControls()[0]).getMessageManager().removeAllMessages();
			if (currentFlagBit != null) {
				boolean saveFlag = true;
				if (!flagStepSetupField.validate()) {
					saveFlag = false;
				}
				if (saveFlag) {
					WipFlagStep newWipFlagStep = (WipFlagStep) flagStepSetupField.getValue();
					
					List<WipFlagStep> tableWipFlagSteps = (List<WipFlagStep>)(List) stepListField.getListTableManager().getInput();
					
					Optional<WipFlagStep> optWipFlagStep = tableWipFlagSteps.stream().filter(p -> p.getStepName().equals(newWipFlagStep.getStepName())).findFirst();
					if (optWipFlagStep.isPresent()) {
						return;
					}
					
					if (flagStepMap.containsKey(currentFlagBit)) {
						List<WipFlagStep> newWipFlagSteps = flagStepMap.get(currentFlagBit);
						newWipFlagSteps.add(newWipFlagStep);
						flagStepMap.put(currentFlagBit, newWipFlagSteps);
					} else {
						List<WipFlagStep> newWipFlagSteps = new ArrayList<WipFlagStep>();
						newWipFlagSteps.add(newWipFlagStep);
						flagStepMap.put(currentFlagBit, newWipFlagSteps);
					}
					
					List<WipFlagStep> flagSteps = flagStepMap.get(currentFlagBit);
					stepListField.getListTableManager().setInput(flagSteps);
					stepListField.getListTableManager().refresh();
					
					WipFlagStep wipFlagStep = new WipFlagStep();
					wipFlagStep.setFlagBit(currentFlagBit);
					wipFlagStep.setFlagName(newWipFlagStep.getFlagName());
					wipFlagStep.setFlagDesc(newWipFlagStep.getFlagDesc()); 
					flagStepSetupField.setValue(wipFlagStep);
					flagStepSetupField.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			((EntityForm)flagStepSetupField.getControls()[0]).getMessageManager().setAutoUpdate(false);
		}
	}

	/**
	 * 移除
	 * @param object
	 */
	private void removeAdapter(Object object) {
		if (currentFlagBit != null) {
			if (CollectionUtils.isNotEmpty(flagStepMap.get(currentFlagBit))) {	
				List<WipFlagStep> existWipFlagSteps = flagStepMap.get(currentFlagBit);
				WipFlagStep selectedWipFlagStep = (WipFlagStep) stepListField.getListTableManager().getSelectedObject();
				if (selectedWipFlagStep != null) {
					Optional<WipFlagStep> optWipFlagStep = existWipFlagSteps.stream().filter(p -> p.getStepName().equals(selectedWipFlagStep.getStepName())).findFirst();
					if (optWipFlagStep.isPresent()) {
						existWipFlagSteps.remove(optWipFlagStep.get());
					}
				}
			}
			
			List<WipFlagStep> flagSteps = flagStepMap.get(currentFlagBit);
			stepListField.getListTableManager().setInput(flagSteps);
			stepListField.getListTableManager().refresh();
		}
	}

	private void flagListSelectionChanged(Object object) {
		if (object == null) {
			return;
		}
		org.osgi.service.event.Event event = (org.osgi.service.event.Event) object;		
		WipFlagDef wipFlagDef = (WipFlagDef) event.getProperty(GlcEvent.PROPERTY_DATA);
		if (wipFlagDef == null) {
			return;
		}
		
		currentFlagBit = wipFlagDef.getFlagBit();
		
		WipFlagStep wipFlagStep = new WipFlagStep();
		wipFlagStep.setFlagBit(wipFlagDef.getFlagBit());
		wipFlagStep.setFlagName(wipFlagDef.getFlagName());
		wipFlagStep.setFlagDesc(wipFlagDef.getFlagDesc()); 
		flagStepSetupField.setValue(wipFlagStep);
		flagStepSetupField.refresh();
		
		List<WipFlagStep> flagSteps = flagStepMap.get(wipFlagDef.getFlagBit());
		stepListField.getListTableManager().setInput(flagSteps);
		stepListField.getListTableManager().refresh();
	}
	
	/**
	 * 拷贝从
	 * @param object
	 */
	private void copyFromAdapter(Object object) {
		WipFlagStepCopyFromDialog dialog = new WipFlagStepCopyFromDialog(Display.getCurrent().getActiveShell());
		if (dialog.open() == Dialog.OK) {
			Long wipFlagStepRrn = (Long.valueOf(dialog.getValue().toString()));
			try {
				if (wipFlagStepRrn == null) {
					return;
				}
				ADManager adManager = Framework.getService(ADManager.class);
				WipFlagStep wipFlagStep = new WipFlagStep();
				wipFlagStep.setObjectRrn(wipFlagStepRrn);
				wipFlagStep = (WipFlagStep) adManager.getEntity(wipFlagStep);
				
				WipFlagStep cloneWipFlagStep = (WipFlagStep) wipFlagStep.clone();
				cloneWipFlagStep.setPartName(null);
				partCategoryInfoField.setValue(cloneWipFlagStep);
				partCategoryInfoField.refresh();
				
				List<WipFlagDef> wipFlagDefs = adManager.getEntityList(Env.getOrgRrn(), WipFlagDef.class, Env.getMaxResult(), 
						" category = '" + wipFlagStep.getCategory() + "'", " flagBit ");
				flagListField.setValue(wipFlagDefs);
				flagListField.refresh();
				
				currentFlagBit = null;
				flagStepMap.clear();
				List<WipFlagStep> wipFlagSteps = null;
				if (StringUtil.isEmpty(wipFlagStep.getPartName())) {
					wipFlagSteps = adManager.getEntityList(Env.getOrgRrn(), WipFlagStep.class, Env.getMaxResult(), 
							" category = '" + wipFlagStep.getCategory() + "' AND (partName is null OR partName = '')", " flagBit ");
				} else {
					wipFlagSteps = adManager.getEntityList(Env.getOrgRrn(), WipFlagStep.class, Env.getMaxResult(), 
							" category = '" + wipFlagStep.getCategory() + "' AND partName = '" + wipFlagStep.getPartName() + "'", " flagBit ");
				}			
				if (CollectionUtils.isNotEmpty(wipFlagSteps)) {
					for (WipFlagStep flagStep : wipFlagSteps) {
						if (flagStepMap.containsKey(flagStep.getFlagBit())) {
							List<WipFlagStep> flagSteps = flagStepMap.get(flagStep.getFlagBit());
							flagSteps.add(flagStep);
							flagStepMap.put(flagStep.getFlagBit(), flagSteps);
						} else {
							List<WipFlagStep> flagSteps = new ArrayList<WipFlagStep>();
							flagSteps.add(flagStep);
							flagStepMap.put(flagStep.getFlagBit(), flagSteps);
						}
					}
				}	
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	}

	/**
	 * 新建
	 * @param object
	 */
	private void newAdapter(Object object) {
		init();
	}

	/**
	 * 保存
	 * @param object
	 */
	private void saveAdapter(Object object) {
		try {
			((EntityForm)partCategoryInfoField.getControls()[0]).getMessageManager().removeAllMessages();
			
			boolean saveFlag = true;
			if (!partCategoryInfoField.validate()) {
				saveFlag = false;
			}
	
			if (saveFlag) {
				if (flagStepMap == null || flagStepMap.size() <= 0) {
					UI.showError(Message.getString("wip.step_not_found"));
					return;
				}
				WipFlagStep wipFlagStep = (WipFlagStep) partCategoryInfoField.getValue();
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.saveWipFlagStep(wipFlagStep.getCategory(), wipFlagStep.getPartName(), flagStepMap, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
				refreshAdapter(object);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			((EntityForm)partCategoryInfoField.getControls()[0]).getMessageManager().setAutoUpdate(false);
		}
	}

	/**
	 * 删除
	 * @param object
	 */
	private void deleteAdapter(Object object) {
		try {
			WipFlagStep wipFlagStep = (WipFlagStep) partCategoryInfoField.getValue();
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteWipFlagStep(wipFlagStep.getCategory(), wipFlagStep.getPartName(), Env.getSessionContext());
				refreshAdapter(object);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}

	/**
	 * 刷新
	 * @param object
	 */
	private void refreshAdapter(Object object) {
		flagStepQuery(object);
		init();
	}
	
}