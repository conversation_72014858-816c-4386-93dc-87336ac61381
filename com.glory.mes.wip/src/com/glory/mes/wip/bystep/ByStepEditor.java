package com.glory.mes.wip.bystep;

import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.GlcUtil;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.prd.model.Step;
import com.google.common.collect.Maps;

public class ByStepEditor extends GlcEditor {

	private static final Logger logger = Logger.getLogger(ByStepEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.glc.bystep.ByStepEditor";
	
	public static final String FIELD_STEPTREE = "stepTree";
	protected CustomField stepTreeField;
	
	public static final String BUTTON_REFRESH = "refresh";
	
	public Step currentStep;
	
	public ADManager adManager;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		partService.addPartListener(this);
		
		configureBody(parent);
		parent.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		
		ADAuthority authority = (ADAuthority)mPart.getTransientData().get(CommandParameter.PARAM_ADAUTHORIY);
		String adFormName = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR)).getPARAM2();
		if (!StringUtil.isEmpty(adFormName)) {
			try {
				ADForm adForm = GlcUtil.getADForm(getClass(), adFormName, getADManger());	
				ADTable adTable = adForm.getAdTable();
				adTable.setAuthorityKey(authority.getName());
				form = new ByStepForm(adForm, adTable);
				form.setAuthority(authority.getName());
				form.setEventBroker(eventBroker);
				//AuthorityKey后面加上UUID,是为了避免当页面关闭后,重新打开时Event可能被重新订阅问题(页面销毁时未及时取消)
				form.setTopicPrefix(authority.getName().replaceAll("\\.", GlcEvent.NAMESPACE_SEPERATOR) + UUIDUtil.base58Uuid());
				form.setTopicId(form.getTopicPrefix());
				form.setFormAttributes(adForm.getFormAttributes());
				form.setPostAddFields(this::postAddFields);
				form.createForm(parent, toolkit);
				createFormAction(form);
				
				focusControl = form.getFocusControl();
				registerAccelerator();
			} catch (Exception e) {
				logger.error(e);
			}
		}
		
		try {
			adManager = Framework.getService(ADManager.class);		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createFormAction(GlcForm form) {
		stepTreeField = form.getFieldByControlId(FIELD_STEPTREE, CustomField.class);		
		subscribeAndPost(eventBroker, stepTreeField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), form.getTopicId());
			
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);		
	}
	
	public EventHandler subscribeAndPost(IEventBroker eventBroker, String fullEventId, String postFullEventId) {
		EventHandler handler = subscribeForwardPost(eventBroker, fullEventId, postFullEventId);
		subscribleHandlers.add(handler);
		return handler;
	}
	
	/**
	 * 执行消息转发,注意要在preDestroy中销返回的EventHandler
	 * @param eventBroker
	 * @param fullEventId 订阅事件号
	 * @param postFullEventId 转发
	 * 
	 */
	public EventHandler subscribeForwardPost(IEventBroker eventBroker, String fullEventId, String postFullEventId) {
		EventHandler subscribeHandler = new EventHandler() {
			@Override
			public void handleEvent(Event event) {
				//点击设备切换右边Page
				String postFull = postFullEventId + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_GLC_MDFIRESECTION;;
				Map<String, Object> map = Maps.newHashMap();
				for (String name : event.getPropertyNames()) {
					map.put(name, event.getProperty(name));
					if (GlcEvent.PROPERTY_DATA.equals(name)) {
						Step step = (Step) map.get(name);
						if (step != null) {
							currentStep = step;
							
							//不同的设备ProcessMode对应相应的Page，设备没有维护ProcessMode，打开默认的Page
							if (StringUtil.isEmpty(step.getUseCategory())) {
								map.put(GlcEvent.PROPERTY_PAGE_KEY, ByStepPage.DEFAULT_STEP_USE_CATEGORY);
							} else {
								map.put(GlcEvent.PROPERTY_PAGE_KEY, step.getUseCategory());
							}
						} else {
							currentStep = null;
						}
					}
				}
				eventBroker.post(postFull, map);		
			}
		};
		eventBroker.subscribe(fullEventId, subscribeHandler);
		
		return subscribeHandler;
	}
	
	
	/**
	 * EqpTree刷新
	 * @param object
	 */
	protected void refreshAdapter(Object object) {
		stepTreeField.refresh();
		currentStep = null;

		//点击刷新打开默认的Page
		String postFull = form.getTopicId() + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_GLC_MDFIRESECTION;;
		Map<String, Object> map = Maps.newHashMap();
		map.put(GlcEvent.PROPERTY_PAGE_KEY, ByStepPage.DEFAULT_STEP_USE_CATEGORY);	
		eventBroker.post(postFull, map);				
	}
	
}
