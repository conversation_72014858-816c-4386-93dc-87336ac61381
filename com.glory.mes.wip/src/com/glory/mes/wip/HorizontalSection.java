package com.glory.mes.wip;

import org.apache.log4j.Logger;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;

/**
 * 左右对称布局Section
 * <AUTHOR>
 *
 */
public abstract class HorizontalSection {
	
	private static final Logger logger = Logger.getLogger(HorizontalSection.class);
	
	private ADManager adManager;
	private String rightTitle;
	private String leftTitle;
	
	private ADTable adTable;
	private IManagedForm managedForm;
	
	public HorizontalSection(ADTable adTable, String rightTitle, String leftTitle) {
		this.adTable = adTable;
		this.rightTitle = rightTitle;
		this.leftTitle = leftTitle;
	}

	public void createContents(IManagedForm managedForm, Composite parent) {
		this.managedForm = managedForm;
		final FFormToolKit toolkit = (FFormToolKit) managedForm.getToolkit();
		Composite content = toolkit.createComposite(parent);

		parent.setLayout(new GridLayout(1, false));
		content.setLayout(new GridLayout(2, true));

		GridData td = new GridData(GridData.FILL_BOTH);
		content.setLayoutData(td);

		toolkit.paintBordersFor(content);
		createLeftSection(content, toolkit);
		createRightSecion(content, toolkit);
	}

	protected void createRightSecion(Composite content, FormToolkit toolkit) {
		Section section = toolkit.createSection(content, Section.NO_TITLE | FFormSection.FFORM);
		section.setText(rightTitle);
		section.marginWidth = 3;
		section.marginHeight = 4;
		
		section.setLayout(new GridLayout(1, true));
		section.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		Composite client = toolkit.createComposite(section);
		client.setLayout(new GridLayout(1, false));
		client.setLayoutData(new GridData(GridData.FILL_BOTH));
		

		createRightContent(client, toolkit);
		
		section.setClient(client);
	}

	protected abstract void createRightContent(Composite client, FormToolkit toolkit);

	protected void createLeftSection(Composite content, FormToolkit toolkit) {
		Section section = toolkit.createSection(content, Section.NO_TITLE | FFormSection.FFORM);
		section.setText(leftTitle);
		section.marginWidth = 3;
		section.marginHeight = 4;
		
		section.setLayout(new GridLayout(1, true));
		section.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite client = toolkit.createComposite(section);
		client.setLayout(new GridLayout(1, false));
		client.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createLeftContent(client, toolkit);
		
		section.setClient(client);
	}
	
	protected abstract  void createLeftContent(Composite client, FormToolkit toolkit);

	protected SquareButton createButton(Composite bodyBtn, String buttonId) {
		SquareButton btn = UIControlsFactory.createButton(bodyBtn, UIControlsFactory.BUTTON_DEFAULT);
		btn.setText(buttonId);
		btn.setLayoutData(new GridData(GridData.CENTER));
		btn.setSize(getSizeByResolution(70), getSizeByResolution(28));
		btn.setInactiveColors(new Color(Display.getCurrent(), new RGB(146, 163, 178), 255));
		btn.setInactiveFontColors(new Color(Display.getCurrent(), new RGB(255, 255, 255), 255));
		btn.setHoverColors(new Color(Display.getCurrent(), new RGB(1, 115, 199), 255));
		return btn;
	}
	

	public int getSizeByResolution(int num) {
 		Rectangle area = Display.getDefault().getClientArea();
 		return area.height * num / 1000;
 	}
	
	/**
	 * 根据批次、载具ID获取批次
	 * @param lotId
	 * @return
	 */
	protected Lot searchLot(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			
			if (lot == null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				lot = carrierLotManager.getLotByCarrierId(Env.getOrgRrn(), lotId);
			}
			
			return lot;
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	/**
	 * 根据名称获取动态表
	 * @param tableName
	 * @return
	 */
	protected ADTable searchADTable(String tableName) {
		try {
			return getADManger().getADTable(Env.getOrgRrn(), tableName);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	public ADManager getADManger() {
		if (adManager == null) {
			try {
				adManager = Framework.getService(ADManager.class);
			} catch (Exception e) {
				logger.error(e);
			}
		}
		return adManager;
	}

	public ADTable getAdTable() {
		return adTable;
	}

	public void setAdTable(ADTable adTable) {
		this.adTable = adTable;
	}

	public IManagedForm getManagedForm() {
		return managedForm;
	}

	public void setManagedForm(IManagedForm managedForm) {
		this.managedForm = managedForm;
	}
	
}
