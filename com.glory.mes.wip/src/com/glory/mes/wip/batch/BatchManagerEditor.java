package com.glory.mes.wip.batch;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.apache.log4j.Logger;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotPrepareManager;
import com.glory.mes.wip.lot.LotInputComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class BatchManagerEditor {

	private static final Logger logger = Logger.getLogger(BatchManagerEditor.class);

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.batch.BatchManagerEditor";

	public BatchLotComposite batchLotComposite;

	private LotInputComposite lotInputComposite;
	
	public static final String KEY_NEW = "new";
	
	public static final String KEY_MERGE = "merge";
	
	public static final String KEY_SPLIT_MERGE = "splitMerge";
	
	public static final String KEY_CANCEL = "cancel";

	@Inject
	protected MPart mPart;

	private MDSashForm sashForm;

	private ADTable adTable;

	private AuthorityToolItem itemNew;
	
	private AuthorityToolItem itemMerge;
	
	private AuthorityToolItem itemSplitMerge;

	private AuthorityToolItem itemCancel;

	private boolean checkFlag = true;

	private boolean showLotFlag = true;

	@PostConstruct
	public void postConstruct(Composite parent) {
		try {
			adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
			
			FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
			Section section = toolkit.createSection(parent, Section.DESCRIPTION | Section.NO_TITLE | FFormSection.FFORM);
			section.setText(String.format(Message.getString(ExceptionBundle.bundle.CommonDetail()),
					new Object[] { I18nUtil.getI18nMessage(adTable, "label") }));
			section.setLayoutData(new GridData(GridData.FILL_BOTH));
			section.marginWidth = 3;
			section.marginHeight = 4;
			
			Composite client = toolkit.createComposite(section);  
		    GridLayout layout = new GridLayout(1, false);    
		    client.setLayout(layout);
		    client.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
		    Composite body = toolkit.createComposite(client);  
		    layout = new GridLayout(1, false);    
		    body.setLayout(layout);
		    body.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			sashForm = new MDSashForm(body, SWT.NONE);
			sashForm.setMenu(body.getMenu());
			layout = new GridLayout(2, false);
			sashForm.setLayout(layout);
			sashForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			createToolBar(section);
			createLeftViewContent(sashForm);
			createRightSectionContent(sashForm);

			sashForm.setOrientation(SWT.HORIZONTAL);
			sashForm.setWeights(new int[] { 5, 5 });
			section.setClient(client);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createLeftViewContent(Composite parent) {
		batchLotComposite = new BatchLotComposite(parent, SWT.NONE, checkFlag, showLotFlag);
		batchLotComposite.createPartControl();
	}

	protected void createRightSectionContent(Composite parent) {
		lotInputComposite = new LotInputComposite(parent, SWT.NONE, checkFlag);
		lotInputComposite.createPartControl();
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		
		createToolItemMerge(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		
		createToolItemSplitMerge(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemNew(ToolBar tBar) {
		itemNew = new AuthorityToolItem(tBar, 8, adTable.getAuthorityKey() + "." + KEY_NEW);
		itemNew.setText(Message.getString("pp.new_batch"));
		itemNew.setImage(SWTResourceCache.getImage("new"));
		itemNew.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				newAdapter();
			}
		});
	}
	
	protected void createToolItemSplitMerge(ToolBar tBar) {
		itemSplitMerge = new AuthorityToolItem(tBar, 8, adTable.getAuthorityKey() + "." + KEY_SPLIT_MERGE);
		itemSplitMerge.setText(Message.getString("pp.batch_split_merge"));
		itemSplitMerge.setImage(SWTResourceCache.getImage("merge-lot"));
		itemSplitMerge.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				splitMergeAdapter();
			}
		});
	}

	protected void createToolItemMerge(ToolBar tBar) {
		itemMerge = new AuthorityToolItem(tBar, 8, adTable.getAuthorityKey() + "." + KEY_MERGE);
		itemMerge.setText(Message.getString("common.batchmerge"));
		itemMerge.setImage(SWTResourceCache.getImage("merge-lot"));
		itemMerge.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				mergeAdapter();
			}
		});
	}

	protected void createToolItemRefresh(ToolBar tBar) {
		itemCancel = new AuthorityToolItem(tBar, 8, adTable.getAuthorityKey() + "." + KEY_CANCEL);
		itemCancel.setText(Message.getString(ExceptionBundle.bundle.CommonDelete()));
		itemCancel.setImage(SWTResourceCache.getImage("delete"));
		itemCancel.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				cancelAdapter();
			}
		});
	}

	public void newAdapter() {
		List<Lot> selectLots = new ArrayList<Lot>();
		
		ListTableManager lotTableManager = lotInputComposite.getLotTableManager();
		if (checkFlag) {
			selectLots = (List) lotTableManager.getCheckedObject();
		} else {
			if (lotTableManager.getSelectedObject() != null) {
				selectLots.add((Lot) lotTableManager.getSelectedObject());
			}
		}
		if (selectLots != null && selectLots.size() > 0) {
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.createBatchLot(selectLots, Env.getSessionContext());
				batchLotComposite.refresh();
				lotInputComposite.refresh();
				UI.showWarning(Message.getString("pp.batch_new_success"));
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		} else {
			UI.showWarning(Message.getString("pp.select_lot"));
		}
	}
	
	// 分batch 
	public void splitMergeAdapter() {
		ListTableManager batchLotTableManager = lotInputComposite.getLotTableManager();
		List<Lot> batchLots = new ArrayList<Lot>();
		batchLots = (List) batchLotTableManager.getCheckedObject();
		if (batchLots != null && batchLots.size() > 0) {
			if (UI.showConfirm(Message.getString("common.submit_confirm"))) { 
				try {
					LotManager lotManager = Framework.getService(LotManager.class);
					// 清除 batch 
					List<Lot> newBatchLot = lotManager.cancelBatchLot(batchLots, Env.getSessionContext());
					batchLotTableManager.setInput(newBatchLot);
					batchLotTableManager.refresh();
					lotInputComposite.refresh();
					UI.showInfo(Message.getString("wip.batch_split_success"));
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		} else {
			UI.showWarning(Message.getString("pp.select_batch_lot"));
		}
	}

	public void mergeAdapter() {
		ListTableManager lotTableManager = lotInputComposite.getLotTableManager();
		ListTableManager batchLotTableManager = batchLotComposite.getLotTableManager();
		List<Lot> batchLots = new ArrayList<Lot>();
		batchLots = (List<Lot>) batchLotTableManager.getInput();
		if (batchLots != null && batchLots.size() > 0) {
			Lot blot = (Lot) batchLotTableManager.getInput().get(0);
			String batchId = blot.getBatchId();
			List<Lot> selectLots = new ArrayList<Lot>();
			if (checkFlag) {
				selectLots = (List) lotTableManager.getCheckedObject();
			} else {
				if (lotTableManager.getSelectedObject() != null) {
					selectLots.add((Lot) lotTableManager.getSelectedObject());
				}
			}
			selectLots.addAll(batchLots);
			if (selectLots != null && selectLots.size() > 0) {
				for (Lot lot : selectLots) {
					lot.setBatchId(batchId);
				}
				try {
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.mergeBatchLot(selectLots, Env.getSessionContext());
					batchLotComposite.refresh();
					lotInputComposite.refresh();
					UI.showWarning(Message.getString("pp.batch_merge_success"));
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			} else {
				UI.showWarning(Message.getString("pp.select_lot"));
			}
		} else {
			UI.showWarning(Message.getString("pp.select_batch_lot"));
		}
	}

	public void cancelAdapter() {
		ListTableManager batchLotTableManager = batchLotComposite.getLotTableManager();
		List<Lot> batchLots = new ArrayList<Lot>();
		List<Lot> dispLots = Lists.newArrayList();
		for(Object lot : batchLotTableManager.getCheckedObject()){
			Lot lotObj = (Lot) lot;
			batchLots.add(lotObj);
			
			if (LotStateMachine.STATE_DISP.equals(lotObj.getState())) {
				dispLots.add(lotObj);
			}
		}
		if (batchLots != null && batchLots.size() > 0) {
			if (UI.showConfirm(Message.getString("common.delete.not"))) {
				try {
					LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
					
					boolean prepareFlag = false;
					for (Lot lot : dispLots) {
						LotPrepare lotPrepare = prepareManager.getPrepareJobs(Env.getOrgRrn(), null, lot.getLotId());
						if (lotPrepare != null) {
							prepareFlag = true;
							break;
						}
					}
					
					if (prepareFlag) {
						UI.showInfo(Message.getString("pp.batch_cancel_prepare_first"));
						return;
					}
					
					
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.cancelBatchLot(batchLots, Env.getSessionContext());
					batchLotComposite.refresh();
					lotInputComposite.refresh();
					UI.showWarning(Message.getString("pp.batch_cancel_success"));
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		} else {
			UI.showWarning(Message.getString("pp.select_batch_lot"));
		}
	}

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
