package com.glory.mes.wip.batch;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.apache.log4j.Logger;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class BatchSplitMergeManagerEditor {
	
	private static final Logger logger = Logger.getLogger(BatchSplitMergeManagerEditor.class);

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.batch.BatchSplitMergeManagerEditor";

	public BatchLotComposite batchLotRightComposite;
	
	public BatchLotEditComposite batchLotLeftComposite;
	
	public static final String KEY_SPLIT = "split";
	
	public static final String KEY_MERGE = "merge";
	
	@Inject
	protected MPart mPart;

	private MDSashForm sashForm;

	private ADTable adTable;
	
	private AuthorityToolItem itemSplit;
	
	private AuthorityToolItem itemMerge;
	
	private boolean checkFlag = true;

	private boolean showLotFlag = true;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		try {
            adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
			
			FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
			Section section = toolkit.createSection(parent, Section.DESCRIPTION | Section.NO_TITLE | FFormSection.FFORM);
			section.setText(String.format(Message.getString(ExceptionBundle.bundle.CommonDetail()),
					new Object[] { I18nUtil.getI18nMessage(adTable, "label") }));
			section.setLayoutData(new GridData(GridData.FILL_BOTH));
			section.marginWidth = 3;
			section.marginHeight = 4;
			
			Composite client = toolkit.createComposite(section);  
		    GridLayout layout = new GridLayout(1, false);    
		    client.setLayout(layout);
		    client.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
		    Composite body = toolkit.createComposite(client);  
		    layout = new GridLayout(1, false);    
		    body.setLayout(layout);
		    body.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			sashForm = new MDSashForm(body, SWT.NONE);
			sashForm.setMenu(body.getMenu());
			layout = new GridLayout(2, false);
			sashForm.setLayout(layout);
			sashForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			createToolBar(section);
			createLeftViewContent(sashForm);
			createRightSectionContent(sashForm);

			sashForm.setOrientation(SWT.HORIZONTAL);
			sashForm.setWeights(new int[] { 5, 5 });
			section.setClient(client);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createLeftViewContent(Composite parent) {
		batchLotLeftComposite = new BatchLotEditComposite(parent, SWT.NONE, checkFlag, showLotFlag);
		batchLotLeftComposite.createPartControl();
	}
	
	protected void createRightSectionContent(Composite parent) {
		batchLotRightComposite = new BatchLotComposite(parent, SWT.NONE, checkFlag, showLotFlag);
		batchLotRightComposite.createPartControl();
	}
	
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSplit(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemMerge(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemMerge(ToolBar tBar) {
		itemMerge = new AuthorityToolItem(tBar, 8, adTable.getAuthorityKey() + "." + KEY_MERGE);
		itemMerge.setText(Message.getString("common.batchmerge"));
		itemMerge.setImage(SWTResourceCache.getImage("merge-lot"));
		itemMerge.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				mergeAdapter();
			}
		});
	}
	
	protected void createToolItemSplit(ToolBar tBar) {
		itemSplit = new AuthorityToolItem(tBar, 8, adTable.getAuthorityKey() + "." + KEY_SPLIT);
		itemSplit.setText(Message.getString("common.batchsplit"));
		itemSplit.setImage(SWTResourceCache.getImage("split-lot"));
		itemSplit.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				splitAdapter();
			}
		});
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public void splitAdapter() {
		/**
		 * 1.拿到打钩的 父批
		 * 2.生成子批
		 * 3.掉后台方法
		 */
		List<Lot> batchLots = (List)batchLotLeftComposite.getLotTableManager().getCheckedObject();
		if(batchLots == null || batchLots.size() == 0){
			UI.showInfo(Message.getString("pp.select_left_batch_lot"));
			return;
		}
		
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> parentLots = new ArrayList<Lot>(); 
			for(Lot parentLot : batchLots) {
				if (parentLot.getAttribute1() == null || StringUtil.isEmpty(parentLot.getAttribute1().toString()) ) {
					UI.showError(Message.getString("pp.please_intput_split_number"));
					return;
				}
				
				// 数量检测  
				BigDecimal childMainQty= new BigDecimal(parentLot.getAttribute1() + "");
				if (parentLot.getMainQty().compareTo(childMainQty) < 0) {
					UI.showConfirm(Message.getString("pp.split_num_not_more_than_mother_num"));
					return;
				}
				
				Lot child = (Lot)parentLot.clone();
				child.setMainQty(childMainQty);
				child.setOperator1(Env.getUserName());
				child.setLotId(null);	
				
				List<Lot> childrens = new ArrayList<Lot>();
				childrens.add(child);
				parentLot.setChildrenLots(childrens);
				
				parentLots.add(parentLot);
			}
			
			String batchId = lotManager.batchSplitLot(parentLots, Env.getSessionContext());
			
			List<Lot> batchLotViews = batchLotRightComposite.getLotsByBatchIdOrLotId(null, batchId);
			batchLotRightComposite.getLotTableManager().addList(batchLotViews);
			batchLotRightComposite.getLotTableManager().refresh();
			
			List<Lot> batchLotEditViews = batchLotLeftComposite.getLotsByBatchIdOrLotId(null, ((Lot)parentLots.get(0)).getBatchId());
			batchLotLeftComposite.getLotTableManager().setInput(batchLotEditViews);
			batchLotLeftComposite.getLotTableManager().refresh();
		} catch(Exception e) {
			logger.error("Split Lot : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public void mergeAdapter() {
		List<Lot> lots = (List) batchLotLeftComposite.getLotTableManager().getCheckedObject();
		if(lots == null || lots.size() != 1){
			UI.showInfo(Message.getString("pp.select_left_batch_one_lot"));
			return;
		}
		
		Lot parent = lots.get(0);
		
		List<Lot> leftBatchLots = (List) batchLotRightComposite.getLotTableManager().getCheckedObject();
		if(leftBatchLots == null || leftBatchLots.size() == 0){
			UI.showInfo(Message.getString("pp.select_right_batch_lot"));
			return;
		}
		
		if(leftBatchLots.stream().filter(item -> item.getLotId().equals(parent.getLotId())).findAny().isPresent()) {
			UI.showInfo(Message.getString("wip.can_not_single_merge"));
			return;
		}
		
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.mergeLot(parent, leftBatchLots, null, Env.getSessionContext());
	
			batchLotRightComposite.refresh();
			batchLotLeftComposite.refresh();
			
			UI.showInfo(Message.getString("wip.merge_success"));
		} catch (Exception e) {
			logger.error("Merge Lot : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
