package com.glory.mes.wip.batch;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;

public class BatchLotComposite extends Composite {

	private static final Logger logger = Logger.getLogger(BatchLotComposite.class);

	private static final String TABLE_NAME = "WIPLotByBatch";

	public boolean checkFlag;
	public boolean showLotFlag;

	public ListTableManager lotTableManager;
	public String lblBatch;
	public HeaderText txtBatchId;
	public HeaderText txtLotId;

	public int tableHeigthHint = 900;

	public BatchLotComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag) {
		super(parent, style);
		this.checkFlag = checkFlag;
		this.showLotFlag = showLotFlag;
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite carrierComposite = new Composite(this, SWT.NONE);
			int gridY = 2;
			if (showLotFlag) {
				gridY += 2;
			}

			carrierComposite.setLayout(new GridLayout(gridY, false));

			Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
			if (StringUtil.isEmpty(lblBatch)) {
				lblCarrierId.setText(Message.getString("common.batch_id"));
			} else {
				lblCarrierId.setText(lblBatch);
			}
			lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

			txtBatchId = new HeaderText(carrierComposite);
			txtBatchId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtBatchId.setTextLimit(32);

			txtBatchId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// 回车事件
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String batchId = ((Text) event.widget).getText();
						List<Lot> queryLots = getLotsByBatchIdOrLotId(null, batchId);
						if (CollectionUtils.isEmpty(queryLots)) {
							txtBatchId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							txtBatchId.warning();
						} else {
							lotTableManager.setInput(queryLots);
							txtBatchId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
							txtBatchId.focusing();
							txtBatchId.selectAll();
						}
						
						if(txtLotId != null) {
							txtLotId.setText("");
						}
						lotTableManager.refresh();
					}
				}
			});
			if (showLotFlag) {
				Label lblLotId = new Label(carrierComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtLotId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-lot"));
				txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtLotId.setTextLimit(64);

				txtLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// 回车事件
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText();
							List<Lot> queryLots = getLotsByBatchIdOrLotId(lotId, null);
							
							if (CollectionUtils.isEmpty(queryLots)) {
								txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
								txtLotId.warning();
							} else {
								lotTableManager.setInput(queryLots);
								txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
								txtLotId.focusing();
								txtLotId.selectAll();
							}
							
							if(txtBatchId != null) {
								txtBatchId.setText("");
							}
							lotTableManager.refresh();
						}
					}
				});
			}

//			Composite lotComposite = new Composite(this, SWT.NONE);
//			lotComposite.setLayout(new GridLayout(1, false));
//
//			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
//			gridData.heightHint = tableHeigthHint;
//			lotComposite.setLayoutData(gridData);
//			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));

			Composite resultComp = new Composite(this, SWT.NONE);
			GridLayout layout = new GridLayout(); 
	        layout.verticalSpacing = 0;
	        layout.marginHeight = 0;
	        resultComp.setLayout(layout);
	        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
	        
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotTableManager = new ListTableManager(lotTable, checkFlag);
			lotTableManager.setAutoSizeFlag(true);
			lotTableManager.newViewer(resultComp);
			lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
//					StructuredSelection selection = (StructuredSelection) event.getSelection();
//					Lot lot = (Lot) selection.getFirstElement();

				}
			});

		} catch (Exception e) {
			logger.error("BatchLotComposite createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// 查询方法
	public List<Lot> getLotsByBatchIdOrLotId(String lotId, String batchId) {
		try {
			if (StringUtil.isEmpty(lotId) && StringUtil.isEmpty(batchId)) {
				return new ArrayList<Lot>();
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			StringBuffer whereClause = new StringBuffer(" 1 = 1 and comClass != 'COM' ");
			
			List<Lot> lots = new ArrayList<Lot>();
			if (!StringUtil.isEmpty(lotId)) {
				whereClause.append(" and lotId ='" + lotId + "' ");
				// 如果是lotId 查询还要显示出相应的 batch lot
				lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Integer.MIN_VALUE, Integer.MAX_VALUE, whereClause.toString() , null);
				if (lots !=null && !lots.isEmpty()) {
					Lot lot = lots.get(0);
					lots = getLotsByBatchIdOrLotId(null, lot.getBatchId());
				}
			} else if (!StringUtil.isEmpty(batchId)) {
				whereClause.append(" and batchId ='" + batchId + "' ");
			    lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Integer.MIN_VALUE, Integer.MAX_VALUE, whereClause.toString() , null);
			}
			
			return lots;
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return new ArrayList<Lot>();
	}

	public HeaderText getTxtBatchId() {
		return txtBatchId;
	}

	public void setTxtBatchId(HeaderText txtCarrierId) {
		this.txtBatchId = txtCarrierId;
	}

	public ListTableManager getLotTableManager() {
		return lotTableManager;
	}

	public void setLotTableManager(ListTableManager lotTableManager) {
		this.lotTableManager = lotTableManager;
	}

	public String getLblCarrier() {
		return lblBatch;
	}

	public void setLblCarrier(String lblCarrier) {
		this.lblBatch = lblCarrier;
	}

	public int getTableHeigthHint() {
		return tableHeigthHint;
	}

	public void setTableHeigthHint(int tableHeigthHint) {
		this.tableHeigthHint = tableHeigthHint;
	}

	public void refresh() {
		try {
			List<Lot> lots = (List) lotTableManager.getInput();
			if (lots != null && !lots.isEmpty()) {
				Lot lot = lots.get(0);
				List<Lot> lotViews = getLotsByBatchIdOrLotId(null, lot.getBatchId());
				lotTableManager.setInput(lotViews);
				lotTableManager.refresh();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
