package com.glory.mes.wip.carrier;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;

public class CarrierLotComposite extends Composite {

	private static final Logger logger = Logger.getLogger(CarrierLotComposite.class);
	
	private static final String TABLE_NAME = "WIPLotByCarrier";
	
	protected boolean checkFlag;
	protected boolean showLotFlag;
	protected boolean showDetailFlag = false;
	protected boolean showOperatorFlag = false;
	
	protected ListTableManager lotTableManager;
	protected EntityForm lotDetailsForm;
	protected String lblCarrier;
	protected HeaderText txtCarrierId;
	protected HeaderText txtLotId;
	protected HeaderText txtOperator;

	protected int tableHeigthHint = Toolkit.getDefaultToolkit().getScreenSize().height / 6;

	public CarrierLotComposite(Composite parent, int style, boolean checkFlag) {
		this(parent, style, checkFlag, false, false, false);
	}
	
	public CarrierLotComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag, boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style);
		this.checkFlag = checkFlag;
		this.showLotFlag = showLotFlag;
		this.showDetailFlag = showDetailFlag;
		this.showOperatorFlag = showOperatorFlag;
		
		this.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		this.setBackgroundMode(SWT.INHERIT_FORCE);
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite carrierComposite = new Composite(this, SWT.NONE);
			int gridY = 2;
			if (showLotFlag) {
				gridY += 2;
			}
			if (showOperatorFlag) {
				gridY += 2;
			}

			carrierComposite.setLayout(new GridLayout(gridY, false));

	        Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
	        if (StringUtil.isEmpty(lblCarrier)) {
				lblCarrierId.setText(Message.getString("wip.carrier_id"));
	        } else {
	        	lblCarrierId.setText(lblCarrier);
	        }
			lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

			txtCarrierId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-carrier"));
			txtCarrierId.setTextLimit(32);
			
			if (showLotFlag) {
				Label lblLotId = new Label(carrierComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtLotId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-lot"));
				txtLotId.setTextLimit(64);
				
				txtLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// 回车事件
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText();
							if (!StringUtil.isEmpty(lotId)) {
								getLotByLotId(lotId);
							}
						}
					}
				});
			}
			if (showOperatorFlag) {
	    		Label lblOperatorId = new Label(carrierComposite, SWT.NONE);
	    		lblOperatorId.setText(Message.getString("wip.operator"));
	    		lblOperatorId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
				
	    		txtOperator = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-op"));
	    		txtOperator.setTextLimit(32);
	    		
	    		txtOperator.addKeyListener(new KeyAdapter() {
	    			@Override
	    			public void keyPressed(KeyEvent event) {
	    				
	    			}

	    		});
	    		txtOperator.addFocusListener(new FocusListener() {
	    			public void focusGained(FocusEvent e) {
	    			}

	    			public void focusLost(FocusEvent e) {
	    			}
	    		});
			}
			
			txtCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// 回车事件
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String carrierId = ((Text) event.widget).getText();
						if (!StringUtil.isEmpty(carrierId)) {
							getLotsByCarrierId(carrierId);
						}
					}
				}
			});
			
			Composite lotComposite = new Composite(this, SWT.NONE);
			lotComposite.setLayout(new GridLayout(1, false));
			
			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
			gridData.heightHint = tableHeigthHint;
			lotComposite.setLayoutData(gridData);
			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), getTableName());
			lotTableManager = new ListTableManager(lotTable, checkFlag);
			lotTableManager.setAutoSizeFlag(true);
			lotTableManager.newViewer(lotComposite);
			lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					StructuredSelection selection = (StructuredSelection) event.getSelection();
					Lot lot = (Lot) selection.getFirstElement();
					if (lotDetailsForm != null) { 
						lotDetailsForm.setObject(lot);
						lotDetailsForm.loadFromObject();
					}
				}
			});
			
			if (showDetailFlag) {
				Composite lotDetailsComposite = new Composite(this, SWT.NONE);
				lotDetailsComposite.setLayout(new GridLayout(1, false));
				lotDetailsForm = new EntityForm(lotDetailsComposite, SWT.NONE, lotTable, null);
			}
		} catch (Exception e) {
			logger.error("StepTreeView createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void getLotByLotId(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			if (lot != null) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.add(lot);
				
				lotTableManager.setInput(lots);
				
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				lotTableManager.refresh();
				// 默认全选
				if (checkFlag) {
					lotTableManager.setCheckedObject(lot);
				}
				lotTableManager.setSelection(new StructuredSelection(new Object[] {lot}));
				txtLotId.focusing();
			} else {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();

				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				
				lotTableManager.setSelection(new StructuredSelection(new Object[] {new Lot()}));
				txtLotId.warning();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);

				lotTableManager.setInput(lots);
				// 默认全选
				if (checkFlag) {
					for (Lot lot : lots) {
						lotTableManager.setCheckedObject(lot);
					}
				}
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				lotTableManager.refresh();
				txtCarrierId.focusing();
			} else {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtCarrierId.warning();
			}
			
			if (lotDetailsForm != null) {
				lotDetailsForm.setObject(new Lot());
				lotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public HeaderText getTxtCarrierId() {
		return txtCarrierId;
	}

	public void setTxtCarrierId(HeaderText txtCarrierId) {
		this.txtCarrierId = txtCarrierId;
	}

	public HeaderText getTxtLotId() {
		return txtLotId;
	}

	public void setTxtLotId(HeaderText txtLotId) {
		this.txtLotId = txtLotId;
	}

	public ListTableManager getLotTableManager() {
		return lotTableManager;
	}

	public void setLotTableManager(ListTableManager lotTableManager) {
		this.lotTableManager = lotTableManager;
	}

	public String getLblCarrier() {
		return lblCarrier;
	}

	public void setLblCarrier(String lblCarrier) {
		this.lblCarrier = lblCarrier;
	}
	
	public int getTableHeigthHint() {
		return tableHeigthHint;
	}

	public void setTableHeigthHint(int tableHeigthHint) {
		this.tableHeigthHint = tableHeigthHint;
	}
	
	public HeaderText getTxtOperator() {
		return txtOperator;
	}

	public void setTxtOperator(HeaderText txtOperator) {
		this.txtOperator = txtOperator;
	}
	
	public String getTableName() {
		return TABLE_NAME;
	}
}
