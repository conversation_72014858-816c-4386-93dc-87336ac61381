package com.glory.mes.wip.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import javax.inject.Inject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.application.command.CommandUtil;
import com.glory.framework.base.application.command.OpenEditorCommand;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.excel.download.Download;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.base.idquery.IdQueryUpload;
import com.glory.mes.base.model.IdObject;
import com.glory.mes.mm.carrier.query.MMCarrierActionQueryEditor;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.lot.detail.WIPLotDetailManagerEditor;
import com.glory.mes.wip.lot.mylotlist.select.group.SelectMyLotGroupDialog;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class WIPQueryGlcEditor extends GlcEditor {
	
	public static final Logger logger = Logger.getLogger(WIPQueryGlcEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.query.WIPQueryGlcEditor";

	private static final String FORM_NAME = "MyGroupLotDialog";
	
	public static final String TEMPLATE_FILE_NAME = "id_template.xlsx";
	
	private static final String FIELD_CUSTOMTABLE = "lotQuery";
	public static final String UPLOAD_NAME = "WIPObjectIdQuery";
	
	private static final String BUTTON_MYLOT = "myLot";
	private static final String BUTTON_DETAIL = "detail";
	private static final String BUTTON_EXPORT = "export";
	private static final String BUTTON_IMPORT = "import";
	private static final String BUTTON_REFRESH = "refresh";
	
	private CustomField customField;
	
	private LotListComposite lotListComposite;
	
	protected NatTable natTable;
	
	protected List<String> lotIdList = new ArrayList<String>();
	
	@Inject
	EPartService partService;
	
	@Inject
	EModelService modelService;
	/**
	 * 批次单次查询数量
	 */
	protected int querySize = 20;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		customField = form.getFieldByControlId(FIELD_CUSTOMTABLE, CustomField.class);
		lotListComposite = (LotListComposite) customField.getCustomComposite();
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MYLOT), this::myLotAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DETAIL), this::detailAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_IMPORT), this::importAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EXPORT), this::exportAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_CUSTOMTABLE, GlcEvent.EVENT_DOUBLE_CLICK), this::lotListDoubleClickAdaptor);
	}
	
	protected void lotListDoubleClickAdaptor(Object obj) {
		try {
			Event event = (Event) obj;
			Object columnValue = event.getProperty(GlcEvent.PROPERTY_TABLE_COLUMN_VALUE);
			String columnName = (String) event.getProperty(GlcEvent.PROPERTY_TABLE_COLUMN_NAME);
			if (MesGlcEvent.PROPERTY_LOT_ID.equals(columnName)) {
				if (columnValue != null) {
					String lotId = DBUtil.toString(columnValue);
					if (!StringUtil.isEmpty(lotId)) {
						Map<String, Object> initDatas = Maps.newHashMap();
						initDatas.put(MesGlcEvent.PROPERTY_LOT_ID, lotId);
						CommandUtil.executeByAuthority(WIPLotDetailManagerEditor.AUHTORITY_ID, initDatas, true);
					}
				}
			}
			
			if ("durable".equals(columnName)) {
				if (columnValue != null) {
					String carrierId = DBUtil.toString(columnValue);
					if (!StringUtil.isEmpty(carrierId)) {
						Map<String, Object> initDatas = Maps.newHashMap();
						initDatas.put(MesGlcEvent.PROPERTY_CARRIER_ID, carrierId);
						CommandUtil.executeByAuthority(MMCarrierActionQueryEditor.AUHTORITY_ID, initDatas, true);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void myLotAdapter(Object object) {
		try {						
            SelectMyLotGroupDialog dialog = new SelectMyLotGroupDialog(FORM_NAME, "common.search_Title", eventBroker, lotIdList);
            if (dialog.open() == Dialog.OK) {
            	lotIdList = dialog.getLotIdList();
            	refreshAdapter(object);
            }
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void detailAdapter(Object object) {
		try {
			Lot lot = (Lot) lotListComposite.getTableManager().getSelectedObject();
			if (lot != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<ADAuthority> authority = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + "Wip.LotDetail" + "'", "");
				if (authority.size() != 1) {
					return;
				}
//				authority.get(0).setAttribute1(lot.getLotId());
//				OpenEditorCommand.open(authority.get(0), partService, modelService);
				
				Map<String, Object> initDatas = new HashMap<String, Object>();
				initDatas.put("lotId", lot.getLotId());				
				OpenEditorCommand.open(authority.get(0), partService, modelService, eventBroker, initDatas);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}
	
	private void refreshAdapter(Object object) {
		lotListComposite.getQueryForm().refresh();
		lotListComposite.refresh();
		List<Lot> querylots = (List<Lot>)lotListComposite.getTableManager().getInput();
		  if(CollectionUtils.isNotEmpty(querylots)  && CollectionUtils.isNotEmpty(lotIdList)) {
			  List<Lot> newlots = new ArrayList<Lot>();
			  for(Lot lot : querylots) {
				  if(lotIdList.contains(lot.getLotId())) {
					  newlots.add(lot);
				  }
			  }
			  lotListComposite.getTableManager().setInput(newlots);
		  }
	}
	
	private void exportAdapter(Object object) {
		try {
			 List<Lot> lots = new ArrayList<>();
			 if (lotListComposite.getTableManager().getCheckedObject() != null) {
				 lots = Lists.newArrayList(lotListComposite.getTableManager().getCheckedObject())
						.stream().map(lot -> ((Lot)lot)).collect(Collectors.toList());
			 }
			 List<IdObject> idObjects = new ArrayList<IdObject>();
			 lots.stream().forEach(lot ->{
				 IdObject idObject = new IdObject();
				 idObject.setId(lot.getLotId());
				 idObjects.add(idObject);
			 });
			if (CollectionUtils.isNotEmpty(idObjects)) {
				 Download download = new Download(form.getAuthority(), null);
				 if (download.getDownloadProgress().init()) {
					 download.run(idObjects);
				 }
			 } else {
				 DefaultDownloadWriter.exportTemplate(form.getAuthority(), null);
			 }
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}
	
	private void importAdapter(Object object) {
		IdQueryUpload upload = new IdQueryUpload(form.getAuthority(), null, UPLOAD_NAME);
		if (upload.getUploadProgress().init()) {
			if (upload.run()) {
				List<String> ids = upload.getIds();
				List<Object> objects = queryObjectByIds(ids);
				lotListComposite.getTableManager().setInput(objects);
			}
		}
	}
	
	public List<Object> queryObjectByIds(List<String> ids) {
		//按size进行分组
		List<List<String>> idGroups = Lists.partition(ids, querySize);
		
		List<Object> results = new ArrayList<Object>();
		if (idGroups.size() > 3) {
			//采用并发方式
			List<Callable> tasks = new ArrayList<Callable>();
			for (List<String> idGroup : idGroups) {
				QueryTask task = new QueryTask(idGroup);
				tasks.add(task);
			}
			
			ExecutorService executor = null;
			try { 
				executor = Executors.newFixedThreadPool(idGroups.size());
				//构建完成服务
				CompletionService completionService = new ExecutorCompletionService(executor);
				List<Future> futureList = new ArrayList<Future>();
				for (Callable task : tasks) {
					//向线程池提交任务
					futureList.add(completionService.submit(task));
				}
				
				for (int i = 0; i < tasks.size(); i++) {
					//阻塞,等待返回结果
					completionService.take();
				}
				
				//按Future顺序返回结果
				for (Future future : futureList) {
					List<Object> result = (List<Object>)future.get();
					results.addAll(result);
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			} finally { 
		        //关闭线程池
				if (executor != null) {
					executor.shutdown(); 
				}
		    }
		} else {
			//采用循环方式
			for (List<String> idGroup : idGroups) {
				List<Object> result = getObjectsByInClause(idGroup);
				results.addAll(result);
			}
		}
		return results;
	}
	
	public List<Object> getObjectsByInClause(List<String> ids) {
		try {
			Set idSet = new HashSet(ids);
			Map<String, Object> fieldMap = new HashMap<String, Object>();
			fieldMap.put("lotIds", idSet);
			ADManager adManager = Framework.getService(ADManager.class);
			List<Lot> lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, 
					Integer.MIN_VALUE, Integer.MAX_VALUE, " lotId in (:lotIds) ", "", fieldMap);
			return (List<Object>)(List)lots;
		} catch (Exception e) {
			logger.error("LotProcessorLotListSection getObjectsByInClause error:", e);
		}
		return null;
	}
	
	class QueryTask implements Callable<List<Object>> {
		private List<String> ids;
		
		public QueryTask(List<String> ids) {
			this.ids = ids;
		}
		
		@Override
		public List<Object> call() throws Exception {
			List<Object> result = getObjectsByInClause(ids);
			return result;
		}
	}
}
