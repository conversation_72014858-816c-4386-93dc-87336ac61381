package com.glory.mes.pvc.ras.eqp;

import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.EquipmentCurrent;
import com.glory.mes.ras.eqp.EquipmentUpload;

public class PvcEquipmentManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.ras.eqp.PvcEquipmentManagerEditor";
	
	public static final String TEMPLATE_FILE_NAME = "equipment_template.xlsx";
	public static final String TABLE_NAME_IMP = "RASEquipmentManagerImp";
	
	private static final String FIELD_EQUIPMENTQUERY = "equipmentQuery";
	private static final String FIELD_EQUIPMENTINFO = "equipmentInfo";
	private static final String FIELD_EQUIPMENTBASEINFO = "equipmentBaseInfo";

	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_IMPORT = "import";

	protected QueryFormField equipmentQueryField;
	protected GlcFormField equipmentInfoField;
	protected EntityFormField equipmentBaseInfoField;
	
	protected NatTable natTable;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		equipmentQueryField = form.getFieldByControlId(FIELD_EQUIPMENTQUERY, QueryFormField.class);
		equipmentInfoField = form.getFieldByControlId(FIELD_EQUIPMENTINFO, GlcFormField.class);
		equipmentBaseInfoField = equipmentInfoField.getFieldByControlId(FIELD_EQUIPMENTBASEINFO, EntityFormField.class);

		subscribeAndExecute(eventBroker, equipmentQueryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::equipmentQuerySelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_IMPORT), this::importAdapter);
		
		newAdapter(null);
	}
	

	private void equipmentQuerySelectionChanged(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			RASManager rasManager = Framework.getService(RASManager.class);
			boolean isSelection = equipmentQueryField.getSelectedObject() != null;		
			if (isSelection) {
				Equipment equipment = (Equipment) equipmentQueryField.getSelectedObject();
				equipment = (Equipment) rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipment.getEquipmentId());
				loadFromObject(equipment);
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void loadFromObject(Equipment equipment) {
		try {
			form.getMessageManager().removeAllMessages();
			if (equipment != null && equipment.getObjectRrn() != null) {
				equipmentBaseInfoField.setValue(equipment);
				equipmentBaseInfoField.refresh();
			} else {
				equipmentQueryField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void newAdapter(Object object) {
		form.getMessageManager().removeAllMessages();
		equipmentQueryField.refresh();
		equipmentBaseInfoField.setValue(new Equipment());
		equipmentBaseInfoField.refresh();
	}

	private void saveAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			Equipment equipment = (Equipment) equipmentBaseInfoField.getValue();
			if (equipment != null) {
				if (equipmentBaseInfoField.validate()) {
					//设备Batch栏位
					
					if (equipment.getObjectRrn() != null) {
						if (equipment.getParentEqpRrn() != null && equipment.getObjectRrn() - equipment.getParentEqpRrn() == 0) {
							UI.showError(Message.getString("ras.equipment_subeqp"));
							return;
						}
					}			
					
					boolean editFlag = false;
					if (equipment.getObjectRrn() != null) {
						editFlag = true;
					}
					
					RASManager rasManager = Framework.getService(RASManager.class);
					ADTable adTable = adManager.getADTable(Env.getOrgRrn(), equipmentBaseInfoField.getAdTable().getName());
					equipment = rasManager.saveEquipment(adTable.getObjectRrn(), equipment, Env.getSessionContext());
					UI.showInfo(Message.getString("common.save_successed"));//弹出提示框
					
					equipment = (Equipment) adManager.getEntity(equipment);
					EquipmentCurrent equipmentCurrent = rasManager.getEquipmentCurrent(equipment.getObjectRrn(), Env.getSessionContext());
					equipment.setEquipmentCurrent(equipmentCurrent);
					refresh(equipment);
					if (editFlag) {
						equipmentQueryField.getQueryForm().getTableManager().update(equipment);
					} else {
						equipmentQueryField.getQueryForm().getTableManager().add(equipment);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void deleteAdapter(Object object) {
		form.getMessageManager().removeAllMessages();
		Equipment equipment = (Equipment) equipmentBaseInfoField.getValue();
		try {
			if (equipment == null || equipment.getObjectRrn() == null) {
				return;
			}
			boolean confirmDelete = UI.showConfirm(Message.getString("common.confirm_delete"));
			if (confirmDelete) {
				if (equipment != null && equipment.getObjectRrn() != null) {
					equipment = (Equipment) adManager.getEntity(equipment);
					
					List<Equipment> equipments = adManager.getEntityList(
							Env.getOrgRrn(), Equipment.class, Env.getMaxResult(), " parentEqpRrn = " + equipment.getObjectRrn(), "");
					
					if (equipments != null && equipments.size() > 0) {
						UI.showError(Message.getString("ras.equipment_subeqp_cannot"));
						return;
					}
					
					RASManager rasManager = Framework.getService(RASManager.class);
					rasManager.deleteEquipment(equipment, Env.getSessionContext());
					UI.showInfo(Message.getString("common.delete_successed"));
					equipmentQueryField.getQueryForm().getTableManager().remove(equipment);
					newAdapter(null);
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	
	}

	private void refreshAdapter(Object object) {
		refresh(null);
	}
	
	private void refresh(Equipment equipment) {
		form.getMessageManager().removeAllMessages();
		if (equipment == null) {
			equipment = (Equipment) equipmentBaseInfoField.getValue();
		} 
		loadFromObject(equipment);
	}
	
	private void importAdapter(Object object) {
		try {
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "RASEquipment");
			EquipmentUpload uploads = new EquipmentUpload(form.getAuthority(), null, getAdTable().getName(), adTable.getObjectRrn());
			if (uploads.getUploadProgress().init()) {
				if (uploads.run()) {
					equipmentQueryField.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public ADTable getAdTable() {
		try {
			 ADTable adTable = adManager.getADTableDeep(Env.getOrgRrn(), TABLE_NAME_IMP);
			 return adTable;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

}
