package com.glory.mes.pvc.ras.port;

import java.util.List;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.port.Port;

public class PvcPortUpload extends Upload {

	public PvcPortUpload(String name) {
		super(name);
	}

	@Override
	protected void cudEntityList() {
		try {
			List<ADBase> uploadList = progress.getUploadList();
			List<ADBase> deleteList = progress.getDeleteList();
			RASManager rasManager = Framework.getService(RASManager.class);

			// 当前只支持一个记录一个事务，后续会增加要么全部成功要么全部失败的事务处理
			if (uploadList != null && uploadList.size() > 0) {
				for (int i = 0; i < uploadList.size(); i++) {
					Port port = (Port) uploadList.get(i);
					rasManager.savePort(port, Env.getSessionContext());
				}
			}

			if (deleteList != null && deleteList.size() > 0) {
				for (int i = 0; i < uploadList.size(); i++) {
					rasManager.deletePort((Port) uploadList.get(i), Env.getSessionContext());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

	}
}
