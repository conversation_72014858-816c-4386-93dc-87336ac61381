package com.glory.mes.pvc.wip.lot.pack.query;


import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ExportToolItemGlc;
import org.eclipse.swt.widgets.ImportToolItemGlc;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.idquery.IdQueryEntityQueryListSection;
import com.glory.mes.base.idquery.IdQueryUpload;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.mm.PackageObjectDetail;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

public class PvcPackQuerySection extends IdQueryEntityQueryListSection implements IRefresh {
	
	public String packageHierarchyName;
	
	protected ImportToolItemGlc itemImport;
	
	public static final String TEMPLATE_FILE_NAME = "id_template.xlsx";
	public static final String UPLOAD_NAME = "PVCObjectIdQuery";
	
	public PvcPackQuerySection(ListTableManager tableManager, String packageHierarchyName) {
		super(tableManager);
		this.packageHierarchyName = packageHierarchyName;
	}
	
	protected void createSectionDesc(Section section) {}
	
	protected QueryForm createQueryFrom(final IManagedForm form, Composite queryComp) {
		QueryForm queryForm = super.createQueryFrom(managedForm, queryComp);
		queryForm.setObject(new PackageObjectDetail());
		return queryForm;
	}
	
	@Override
	protected void queryAdapter() {
		managedForm.getMessageManager().removeAllMessages();
		if (!getQueryForm().saveToObject()){
			return;
		}
		refresh();
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemExportTemplate(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	private ADTable getImportADTable() {
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), UPLOAD_NAME);
			return adTable;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
    }

	protected void createToolItemImport(ToolBar tBar) {
		itemImport = new ImportToolItemGlc(tBar, null, getImportADTable().getAuthorityKey(), null, null);
		itemImport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				importObjectByIds();
			}
		});
	}
	
	protected void createToolItemExportTemplate(ToolBar tBar) {
		itemExport = new ExportToolItemGlc(tBar, null, getImportADTable().getAuthorityKey(), null, null);
		itemExport.setText(Message.getString("common.export_template"));
		itemExport.setImage(SWTResourceCache.getImage("export"));
		itemExport.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				exportTemplateAdapter();
			}
		});
	}
	
	protected void exportTemplateAdapter() { 
		try {
			DefaultDownloadWriter.exportTemplate("PVC.Wip.PackQuery", null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}	
	
	public void importObjectByIds() {
		IdQueryUpload upload = new IdQueryUpload(getADTable().getAuthorityKey(), null, UPLOAD_NAME);
		if (upload.getUploadProgress().init()) {
			if (upload.run()) {
				List<String> ids = upload.getIds();
				List<Object> objects = queryObjectByIds(ids);
				tableManager.setInput(objects);
			}
		}
	}
	
	public void refresh() {
		try {
			List<PackageObjectDetail> details = Lists.newArrayList();
			PackageObjectDetail lot = (PackageObjectDetail) getQueryForm().getObject();	
			//先查询输入LotId是小包条码还是箱条码；若已包装小包条码还需查询出其他小包
			String level1Id = (String) lot.getObjectId1();

			if (StringUtil.isEmpty(level1Id)) {
				UI.showInfo(Message.getString("wip.pack_query_need_id"));
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot searchLot = lotManager.getLotByLotId(Env.getOrgRrn(), level1Id, true);
			String queryId = null;
			int packLevel = 0;
			if ("SMALLBOX".equals(searchLot.getMainMatType())) {
				if ("PACKED".equals(searchLot.getState()) && searchLot.getParentLotRrn() != null) {
					Lot boxLot = lotManager.getLot(searchLot.getParentLotRrn());
					queryId = boxLot.getLotId();
					packLevel = 1;
				} else {
					queryId = level1Id;
					packLevel = 0;
				}
			} else if ("BOX".equals(searchLot.getMainMatType())) {
				queryId = level1Id;
				packLevel = 1;
			}
			
			MLotManager mLotManager = Framework.getService(MLotManager.class);
			details = mLotManager.getPackageObjectDetailsById(Env.getOrgRrn(), packageHierarchyName, packLevel, queryId, null);
			tableManager.setInput(details);
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public String getPackageHierarchyName() {
		return packageHierarchyName;
	}

	public void setPackageHierarchyName(String packageHierarchyName) {
		this.packageHierarchyName = packageHierarchyName;
	}
	
	@Override
	public List<Object> getObjectsByInClause(List<String> ids) {
		try {
			//此处重写逻辑，导入的数据需要先判断是大箱还是小包，然后根据类型走不同的查询逻辑，判断之前先抽出存在的数据
			if(ids != null) {
				List<String> lotIds = new ArrayList<String>();
				String mainMatType = null;
				MLotManager mLotManager = Framework.getService(MLotManager.class);
				LotManager lotManager = Framework.getService(LotManager.class);
				for(String id : ids ) {
					Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(),id);
					if (lot != null) {
						lotIds.add(lot.getLotId());
						mainMatType = lot.getMainMatType();
					}
				}
				
				List<PackageObjectDetail> packageObjectDetails = Lists.newArrayList();
				if("SMALLBOX".equals(mainMatType)) {
					//小包查询
					for(String id : lotIds ) {
						PackageObjectDetail packageObject = new PackageObjectDetail();
						//此处查询还需要判断做过包装与否，做过包装的需要查询箱号和数量
						Lot smallLot = lotManager.getLotByLotId(Env.getOrgRrn(),id);
						if("PACKED".equals(smallLot.getState())) {
							List<PackageObjectDetail> smalls = mLotManager.getPackageObjectDetailsById(Env.getOrgRrn(), packageHierarchyName, 0, id, null);
							if(CollectionUtils.isNotEmpty(smalls)) {
								packageObjectDetails.add(smalls.get(0));
							}
						}else{
							packageObject.setLotObject(0, smallLot);	
							packageObjectDetails.add(packageObject);
						}
					}
				}else if("BOX".equals(mainMatType)){
					//大包查询
					for(String id : lotIds ) {
						List<PackageObjectDetail> boxs = mLotManager.getPackageObjectDetailsById(Env.getOrgRrn(), packageHierarchyName, 1, id, null);
						if(CollectionUtils.isNotEmpty(boxs)) {
							for(PackageObjectDetail packageObject : boxs) {
								packageObjectDetails.add(packageObject);
							}
						}	
					}
				}
				return (List<Object>)(List)packageObjectDetails;
			}
		} catch (Exception e) {
			logger.error("PackQuerySection getObjectsByInClause error:", e);
		}
		return null;
	}

}
