package com.glory.mes.pvc.mm.carrier;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.carrier.query.MMCarrierActionQueryEditor;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.Durable;

public class PvcCarrierActionEditor extends MMCarrierActionQueryEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.mm.carrier.PvcCarrierActionEditor";
	
	private static final String BUTTON_HOLD = "hold";
	private static final String BUTTON_RELEASE = "release";
	private static final String BUTTON_CLEAN = "clean";
	private static final String BUTTON_HISQUERY = "hisquery";
	private static final String BUTTON_CHANGELOCATION = "changeLoaction";
	
	private static final String BUTTON_AVL = "avl";
	private static final String BUTTON_UVL = "uvl";
	private static final String BUTTON_SCR = "scr";
	private static final String BUTTON_UNSCR = "unscr";
	private static final String BUTTON_UNRES = "unres";
	private static final String BUTTON_RETURN = "return";
	private static final String BUTTON_UPDATESPEC = "updatespec";
	
	private static final String FIELD_QUERYFORM = "carrierQuery";

	private QueryFormField queryFormField;
	
	private ToolItem itemHold;
	private ToolItem itemRelease;
	private ToolItem itemClean;
	private ToolItem itemHisQuery;
	
	private ToolItem itemAvl;
	private ToolItem itemUvl;
	private ToolItem itemScr;
	private ToolItem itemUNScr;
	private ToolItem itemUNRes;
	private ToolItem itemReturn;
	private ToolItem itemUpdateSpec;
	private ToolItem itemChangeLocation;
	
	protected NatTable natTable;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_QUERYFORM, GlcEvent.EVENT_TABLE_CHECK),
				this::checkAdaptor);
		
		init();
	}
	
	private void init() {
		queryFormField = form.getFieldByControlId(FIELD_QUERYFORM, QueryFormField.class);
		
		itemClean = (ToolItem) form.getButtonByControl(null, BUTTON_CLEAN);
		itemHold = (ToolItem) form.getButtonByControl(null, BUTTON_HOLD);
		itemRelease = (ToolItem) form.getButtonByControl(null, BUTTON_RELEASE);
		itemHisQuery = (ToolItem) form.getButtonByControl(null, BUTTON_HISQUERY);
		itemAvl = (ToolItem) form.getButtonByControl(null, BUTTON_AVL);
		itemUvl = (ToolItem) form.getButtonByControl(null, BUTTON_UVL);
		itemScr = (ToolItem) form.getButtonByControl(null, BUTTON_SCR);
		itemUNScr = (ToolItem) form.getButtonByControl(null, BUTTON_UNSCR);
		itemUNRes = (ToolItem) form.getButtonByControl(null, BUTTON_UNRES);
		itemReturn = (ToolItem) form.getButtonByControl(null, BUTTON_RETURN);
		itemUpdateSpec = (ToolItem) form.getButtonByControl(null, BUTTON_UPDATESPEC);
		itemChangeLocation = (ToolItem) form.getButtonByControl(null, BUTTON_CHANGELOCATION);
		
		itemClean.setEnabled(false);
		itemHold.setEnabled(false);
		itemRelease.setEnabled(false);
		itemHisQuery.setEnabled(false);
		itemAvl.setEnabled(false);
		itemUvl.setEnabled(false);
		itemScr.setEnabled(false);
		itemUNScr.setEnabled(false);
		itemUNRes.setEnabled(false);
		itemReturn.setEnabled(false);
		itemUpdateSpec.setEnabled(false);
		itemChangeLocation.setEnabled(false);
	}
	
	private void checkAdaptor(Object obj) {
		try {			
			List<Object> objs = queryFormField.getCheckedObjects();
			boolean isChecked = CollectionUtils.isNotEmpty(objs);
			
			if (isChecked) {
				itemClean.setEnabled(true);
				itemUpdateSpec.setEnabled(true);
				
				List<Durable> carriers = objs.stream().map(
						o -> ((Durable)o)).collect(Collectors.toList());
				
				// 检查是否可以暂停
				Optional<Durable> f = carriers.stream().filter(r -> Carrier.HOLDSTATE_ON.equals(r.getHoldState())).findFirst();
				if (f.isPresent()) {
					itemHold.setEnabled(false);
					// Hold后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemHold.setEnabled(true);
				}
				
				// 检查是否可以Release
				f = carriers.stream().filter(r -> Carrier.HOLDSTATE_OFF.equals(r.getHoldState())).findFirst();
				if (f.isPresent()) {
					itemRelease.setEnabled(false);
				} else {
					itemRelease.setEnabled(true);
				}
				
				DurableManager durableManager = Framework.getService(DurableManager.class);
				// 检查是否可以报废
				boolean isCanScrap = durableManager.checkDurableState(carriers, Carrier.EVENT_SCR, Env.getSessionContext());
				if (isCanScrap) {
					itemScr.setEnabled(true);
				} else {
					itemScr.setEnabled(false);
				}
				// 检查是否可以取消报废
				boolean isCanUnScrap = durableManager.checkDurableState(carriers, Carrier.EVENT_UNSCR, Env.getSessionContext());
				if (isCanUnScrap) {
					itemUNScr.setEnabled(true);
					// 报废后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemUNScr.setEnabled(false);
				}
				// 检查是否可以使可用
				boolean isCanAvl = durableManager.checkDurableState(carriers, Carrier.EVENT_AVL, Env.getSessionContext());
				if (isCanAvl) {
					itemAvl.setEnabled(true);
				} else {
					itemAvl.setEnabled(false);
				}
				// 检查是否可以使不可用
				boolean isCanUvl = durableManager.checkDurableState(carriers, Carrier.EVENT_UVL, Env.getSessionContext());
				if (isCanUvl) {
					itemUvl.setEnabled(true);
				} else {
					itemUvl.setEnabled(false);
				}
				// 检查是否可以客返
				boolean isCanReturn = true;
				for (Durable durable : carriers) {
					if (!Carrier.EVENT_SHIP.equals(durable.getState())) {
						isCanReturn = false;
						break;
					}
				}
				if (isCanReturn) {
					itemReturn.setEnabled(true);
					// 发出后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemReturn.setEnabled(false);
				}
				// 检查是否可以取消预留
				boolean isCanUnRes = durableManager.checkDurableState(carriers, Carrier.EVENT_UNRES, Env.getSessionContext());
				if (isCanUnRes) {
					itemUNRes.setEnabled(true);
					// 预留后不能清洗
					itemClean.setEnabled(false);
				} else {
					itemUNRes.setEnabled(false);
				}
			} else {
				itemClean.setEnabled(false);
				itemHold.setEnabled(false);
				itemRelease.setEnabled(false);
				itemScr.setEnabled(false);
				itemUNScr.setEnabled(false);
				itemAvl.setEnabled(false);
				itemUvl.setEnabled(false);
				itemReturn.setEnabled(false);
				itemUNRes.setEnabled(false);
			}
			itemChangeLocation.setEnabled(false);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

}
