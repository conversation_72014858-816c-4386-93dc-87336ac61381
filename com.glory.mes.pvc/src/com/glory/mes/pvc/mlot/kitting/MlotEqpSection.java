package com.glory.mes.pvc.mlot.kitting;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityListSection;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcManager;
import com.glory.mes.ras.eqp.Equipment;

public class MlotEqpSection extends EntityListSection {

	private static final Logger logger = Logger.getLogger(MlotEqpSection.class);

	private static final String JL_MATERIAL_TYPE = "JL";

	private static final String WB_MATERIAL_TYPE = "WB";

	private static final String filterSql = " equipmentId like 'SY-YS-B1%' OR equipmentId like 'SY-YS-B2%' OR equipmentId like 'SY-YS-B3%' OR equipmentId like 'SY-YS-B4%' ";

	protected CheckBoxTableViewerManager eqpInfoManger;

	protected ToolItem itemSave;

	public Text txtMLot;

	public MLot mLot;

	protected Button add, remove;

	public MlotEqpSection(ListTableManager listTableManager) {
		super(listTableManager);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new ToolItem(tBar, 8);
		itemSave.setText(Message.getString("common.save"));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("mm.mlot_id") + "：");
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtMLot = toolkit.createText(top, "", SWT.BORDER);
		GridData gText = new GridData();
		gText.widthHint = 216;
		txtMLot.setLayoutData(gText);
		txtMLot.setTextLimit(32);
		txtMLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					// MLot mLot = null;
					String lotId = tLotId.getText();
					tLotId.setText(lotId);
					try {
						mLot = searchMLot(lotId);
						tLotId.selectAll();
						if (mLot == null) {
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							getTableManager().setInput(null);
						} else {
							PvcManager manager = Framework.getService(PvcManager.class);
							List<EquipmentMaterial> list = manager.getEquipmentMaterialsByMLot(Env.getOrgRrn(),
									mLot.getObjectRrn());
							getTableManager().setInput(list);
						}
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
					}
					// refresh();
					break;
				}
			}

		});
		txtMLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				tLotId.setText(tLotId.getText());
			}
		});

		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
	}

	@Override
	public void createContents(IManagedForm form, Composite client, int sectionStyle) {
		super.createContents(form, client, sectionStyle);
		try {
			FormToolkit toolkit = form.getToolkit();
			GridLayout layout = new GridLayout(1, true);
			layout.horizontalSpacing = 0;
			layout.verticalSpacing = 0;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			client.setLayout(layout);
			client.setLayoutData(new GridData(GridData.FILL_BOTH));

			Section receiveSection = toolkit.createSection(client, Section.TITLE_BAR | Section.EXPANDED);
			receiveSection.setText(Message.getString("ras.recipe_attach_unit"));
			receiveSection.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			receiveSection.setLayoutData(gd);

			Composite parameterComp = toolkit.createComposite(receiveSection, SWT.NONE);
			gd = new GridData(GridData.FILL_BOTH);
			parameterComp.setLayoutData(gd);
			receiveSection.setClient(parameterComp);

			gd.heightHint = 5000;
			parameterComp.setLayout(layout);
			parameterComp.setLayoutData(gd);

			final ADTable adTable = getADTable("RASEquipment");
			eqpInfoManger = new CheckBoxTableViewerManager(adTable);
			eqpInfoManger.setIndexFlag(false);
			eqpInfoManger.newViewer(parameterComp);

			Composite btnComposite = toolkit.createComposite(client, SWT.NONE);
			GridLayout layoutBtn = new GridLayout(5, false);
			btnComposite.setLayout(layoutBtn);
			GridData gd1 = new GridData(GridData.FILL_BOTH);
			gd1.horizontalAlignment = SWT.RIGHT;
			btnComposite.setLayoutData(gd1);

			add = toolkit.createButton(btnComposite, "  " + Message.getString("common.add") + "  ", SWT.NONE);
			add.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						addAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});

			remove = toolkit.createButton(btnComposite, "  " + Message.getString("common.delete") + "  ", SWT.NONE);
			remove.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						removeAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public void setFocus() {
		txtMLot.setFocus();
	}

	private MLot searchMLot(String mLotId) {
		try {
			MMManager manager = Framework.getService(MMManager.class);
			MLot mLot = manager.getMLotByMLotId(Env.getOrgRrn(), mLotId,true);
			if (!StringUtils.equals(JL_MATERIAL_TYPE, mLot.getMaterialType())
					&& !StringUtils.equals(WB_MATERIAL_TYPE, mLot.getMaterialType())) {
				UI.showError(Message.getString("wms.wrong_material_type"));
				return null;
			}
			return mLot;
		} catch (Exception e) {
			logger.error("Cannot find this MLot by this mLotId!");
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	@Override
	public void refresh() {
		if (txtMLot != null) {
			txtMLot.setText("");
		}
		mLot = null;
		this.tableManager.setInput(null);
		eqpInfoManger.setInput(null);
	}

	protected void saveAdapter() {
		try {
			List<Object> obs = (List<Object>) eqpInfoManger.getInput();
			List<Equipment> selectEqps = new ArrayList<>();
			for (Object obj : obs) {
				Equipment eqp = (Equipment) obj;
				selectEqps.add(eqp);
			}
			if (CollectionUtils.isEmpty(selectEqps)) {
				UI.showInfo(Message.getString("ras.equipment_select_eqp"));
				return;
			}
			if (mLot == null || mLot.getObjectRrn() == null) {
				UI.showInfo(Message.getString("mm.please_enter_mlot_id"));
				return;
			}
			mLot = searchMLot(mLot.getmLotId());
			PvcManager pvcManager = Framework.getService(PvcManager.class);
			pvcManager.kittingEquipmentMaterial(mLot, selectEqps, Env.getSessionContext());
			UI.showInfo(Message.getString("common.save_successed"));// 弹出提示框
			saveafter();
			// refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void addAdapter() {
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "PvcRASEquipment");
			MlotEqpDialog dialog = new MlotEqpDialog(new ListTableManager(adTable, true), filterSql, "");
			if (dialog.open() == IDialogConstants.OK_ID) {
				List<Equipment> equipments = (List<Equipment>) (List) dialog.getSelectionList();
				eqpInfoManger.setInput(equipments);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void removeAdapter() {
		try {
			List<Equipment> selectEqps = (List<Equipment>) (List) eqpInfoManger.getInput();
			List<Equipment> lines = new ArrayList<Equipment>();
			for (Equipment temp : selectEqps) {
				lines.add(temp);
			}
			List<Object> os = eqpInfoManger.getCheckedObject();
			if (os.size() != 0) {
				for (Object o : os) {
					Equipment eqp = (Equipment) o;
					lines.remove(eqp);
				}
			}
			eqpInfoManger.setInput(lines);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected ADTable getADTable(String tableName) {
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), tableName);
			return adTable;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	protected void saveafter() {
		try {
			PvcManager manager = Framework.getService(PvcManager.class);
			List<EquipmentMaterial> list = manager.getEquipmentMaterialsByMLot(Env.getOrgRrn(), mLot.getObjectRrn());
			getTableManager().setInput(list);
			eqpInfoManger.setInput(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
