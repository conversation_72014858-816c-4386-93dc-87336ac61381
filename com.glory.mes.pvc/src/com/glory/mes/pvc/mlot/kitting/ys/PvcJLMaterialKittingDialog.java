package com.glory.mes.pvc.mlot.kitting.ys;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.mes.pvc.client.PvcMLotManager;
import com.glory.mes.ras.eqp.PositionSet;

public class PvcJLMaterialKittingDialog extends GlcBaseDialog { 
	
	private static final String FIELD_JLMATERIALINFO = "jlMaterialInfo";
	private static final String FIELD_EQUIPMENTID = "equipmentId";
	private static final String FIELD_MLOT = "mLotId";
	private static final String FIELD_BEFOREWEIGHT = "beforeWeight";
	private static final String FIELD_AFETRWEIGHT = "afetrWeight";
	private static final String FIELD_ADDWEIGHT = "attachMainQty";

	protected EntityFormField jlMaterialInfoField;
	protected RefTableField equipmentIdField;
	protected RefTableField mLotIdField;
	protected TextField beforeWeightField;
	protected TextField afetrWeightField;
	protected TextField addWeightField;
	
	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;
	
	public PvcJLMaterialKittingDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		jlMaterialInfoField = form.getFieldByControlId(FIELD_JLMATERIALINFO, EntityFormField.class);
		equipmentIdField = jlMaterialInfoField.getFieldByControlId(FIELD_EQUIPMENTID, RefTableField.class);
		mLotIdField = jlMaterialInfoField.getFieldByControlId(FIELD_MLOT, RefTableField.class);
		beforeWeightField = jlMaterialInfoField.getFieldByControlId(FIELD_BEFOREWEIGHT, TextField.class);
		afetrWeightField = jlMaterialInfoField.getFieldByControlId(FIELD_AFETRWEIGHT, TextField.class);
		addWeightField = jlMaterialInfoField.getFieldByControlId(FIELD_ADDWEIGHT, TextField.class);
		
		afetrWeightField.addValueChangeListener(afterWeightListener);
	}
	
	IValueChangeListener afterWeightListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			if (beforeWeightField.getValue() == null) {
				UI.showError(Message.getString("pvc.input_before_weight"));
			}
			double weightValue = Double.valueOf((String) beforeWeightField.getValue()) - Double.valueOf((String) afetrWeightField.getValue());
			addWeightField.setValue(weightValue);
			addWeightField.refresh();
		}
	};
	
	@Override
	protected void okPressed() {
		try {
			if (jlMaterialInfoField.validate()) {
				boolean flag = UI.showConfirm(String.format(Message.getString("pvc.confirm_add_jl"), equipmentIdField.getValue().toString(), addWeightField.getValue().toString()));
				if (!flag) {
					return;
				}
				String equipmentId = equipmentIdField.getValue().toString();
				MLot mlot = (MLot) mLotIdField.getData();
				BigDecimal transMainQty = new BigDecimal(addWeightField.getValue().toString());
				if (mlot.getMainQty().compareTo(transMainQty) < 0) {
					UI.showError(Message.getString("ras.mainQty_over_warehouse_qty"));
					return;
				}
				mlot.setTransMainQty(transMainQty);
				MMManager mmManager = Framework.getService(MMManager.class);
				PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
				//物料Kitting时支持Kitting到多台设备，不移动物料位置，kitting时消耗数量
				pvcMLotManager.kittingEquipmentMaterialNoMove(equipmentId, mlot.getMaterialType(), mlot, null, transMainQty, false, PositionSet.REPLENISH_TYPE_REPLACE, Env.getSessionContext());
				List<MLotStorage> storages = mmManager.getLotStorages(mlot.getObjectRrn());
        		if (CollectionUtils.isNotEmpty(storages)) {
    				mmManager.consumeMLot(mlot.getObjectRrn(), transMainQty, storages.get(0).getWarehouseRrn(), null, storages.get(0).getStorageType(), storages.get(0).getStorageId(), false, Env.getSessionContext());
        		}
        		pvcMLotManager.saveKittingMaterialHis(MaterialEvent.EVENT_KITTING, equipmentId, mlot.getmLotId(), Env.getSessionContext());
//				mlotManager.kittingEquipmentMaterial(equipmentId, null, Lists.newArrayList(mlot), PositionSet.CONSUME_TYPE_KITTING, 
//						PositionSet.REPLENISH_TYPE_REPLACE, null, false, Env.getSessionContext());
				UI.showInfo(Message.getString("common.operation_successed"));
			} else {
				return;
			}
		} catch (Exception e) {
            e.printStackTrace();
        }
		super.okPressed();
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}