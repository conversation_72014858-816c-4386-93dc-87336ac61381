package com.glory.mes.pvc.mlot;

import java.math.BigDecimal;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.mlot.action.MLotActionDialog;
import com.google.common.collect.Lists;

public class PvcMaterialSplitActionDialog extends MLotActionDialog {
	
	private static int DIALOG_WIDTH = 600;
	private static int DIALOG_HEIGHT = 350;
	
	private static final String FIELD_MLOTSPILTLIST = "mLotSpiltList";
	private static final String FIELD_MLOTSPLITQTY = "mLotSplitQty";
	private static final String FIELD_MLOTID = "mLotId";
	private static final String FIELD_MAINQTY = "mainQty";

	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_ADD = "add";

	protected ListTableManagerField mLotSpiltListField;
	protected EntityFormField mLotSplitQtyField;
	protected TextField mLotIdField;
	protected TextField mainQtyField;
	
	protected List<MLot> mLots;
	protected MLot lot;
	
	protected ListTableManager listTableManager;
	protected CheckBoxFixEditorTableManager tableManager;
	
	public PvcMaterialSplitActionDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots) {
		super(adFormName, authority, eventBroker);
		this.mLots = mLots;
		setmLotList(mLots);
		setBlockOnOpen(false);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mLotSpiltListField = form.getFieldByControlId(FIELD_MLOTSPILTLIST, ListTableManagerField.class);
		mLotSplitQtyField = form.getFieldByControlId(FIELD_MLOTSPLITQTY, EntityFormField.class);
		mLotIdField = mLotSplitQtyField.getFieldByControlId(FIELD_MLOTID, TextField.class);
		mainQtyField = mLotSplitQtyField.getFieldByControlId(FIELD_MAINQTY, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ADD), this::addAdapter);
		
		listTableManager = mLotSpiltListField.getListTableManager();
		
		tableManager = (CheckBoxFixEditorTableManager) listTableManager.getTableManager();
		
		mainQtyField.getTextControl().addModifyListener(getModifyListener("double"));
		
		changeMLotIdEvent();
		
		initLot();
	}

	@Override
	public void initLot() {
		mLots = getmLotList();
		lot = mLots.get(0);
	}
	
	protected void changeMLotIdEvent() {
		mLotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text mLotIdText = ((Text) event.widget);
				mLotIdText.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String mLotId = mLotIdText.getText();
					if (StringUtil.isEmpty(mLotId)) {
						UI.showError(Message.getString("mm.please_enter_mlot_id"));
						return;
					} else {
						addAdapter(null);
					}
					break;
				}
			}
		});
	}
	
	private void deleteAdapter(Object object) {
		List<Object> checkedList = tableManager.getCheckedObject();
		tableManager.getInput().removeAll(checkedList);
		tableManager.refresh();
	}

	@SuppressWarnings("unchecked")
	private void addAdapter(Object object) {
		BigDecimal qty = getInputQty();
		String mlotId = getInputMLotId();
		if (qty == null) {
			UI.showWarning(Message.getString("mm.enter_qty"));
			return;
		}
		
		// 校验是否已经在列表中
		List<MLot> mlots = (List<MLot>) tableManager.getInput();
		for (MLot mlot : mlots) {
			if (mlot.getmLotId().equals(mlotId)) {
				UI.showWarning(Message.getString("wip.box_already_exists"));
				return;
			}
		}
		
		BigDecimal reservedQty = lot.getReservedMainQty() == null ? BigDecimal.ZERO : lot.getReservedMainQty();
		BigDecimal totalQty = lot.getMainQty().subtract(reservedQty);
		
		if (qty.compareTo(totalQty) > 0) {
			if (!UI.showConfirm(Message.getString("mm.qty_greater_than_parent"))) {
				return;
			}
		}
		BigDecimal total = BigDecimal.ZERO;
		for (Object obj : tableManager.getInput()) {
			MLot mLot = (MLot) obj;
			if (BigDecimal.ZERO.compareTo(mLot.getMainQty()) >= 0) {
				UI.showWarning(Message.getString("mm.split_qty_greater0"));
				return;
			}
			total = total.add(mLot.getMainQty());
		}
		BigDecimal getInputQty = qty.add(total);
		if (getInputQty.compareTo(totalQty) > 0) {
			if (!UI.showConfirm(Message.getString("mm.qty_greater_than_parent"))) {
				return;
			}
		}
		
		MLot mLot = new MLot();
		mLot.setMainQty(qty);
		mLot.setmLotId(mlotId);
		getTableManager().add(mLot);
		getTableManager().refresh();
	}

	public ModifyListener getModifyListener(final String dataType) {
		return new ModifyListener() {
			public void modifyText(ModifyEvent e) {
				Text text = (Text)e.widget;
				String value = text.getText().trim();
				if ("".equalsIgnoreCase(value.trim())) {
					return;
				}
				if (!discernQty(value)) {
					text.setText("");
					text.setFocus();
				}
			}
			public boolean discernQty(String value) {
				if (!ValidatorFactory.isValid(dataType, value)) {
					UI.showError(Message.getString("common.input_error"), Message.getString("common.inputerror_title"));
					return false;
				}
				return true;
			}
		};
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	public CheckBoxFixEditorTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(CheckBoxFixEditorTableManager tableManager) {
		this.tableManager = tableManager;
	}

	@SuppressWarnings("unchecked")
	public List<MLot> getSubLots() {
		return (List<MLot>) Lists.newArrayList(tableManager.getInput());
	}

	public BigDecimal getInputQty() {
		String numStr = mainQtyField.getTextControl().getText();
		if (StringUtil.isEmpty(numStr)) {
			return null;
		}
		return new BigDecimal(numStr);
	}
	
	public String getInputMLotId() {
		String mLotIdStr = mLotIdField.getTextControl().getText();
		if (StringUtil.isEmpty(mLotIdStr)) {
			return null;
		}
		return mLotIdStr;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}
