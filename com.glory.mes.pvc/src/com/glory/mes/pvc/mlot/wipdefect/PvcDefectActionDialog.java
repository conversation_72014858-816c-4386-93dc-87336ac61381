package com.glory.mes.pvc.mlot.wipdefect;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang.StringUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.common.context.client.ContextManager;
import com.glory.common.context.model.ContextValue;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcPpManager;
import com.glory.mes.pvc.model.DefectHis;

public class PvcDefectActionDialog extends GlcBaseDialog { 
	
	private static final String CONTEXT_NAME = "WEIGHTCONVERSIONRULE";
	private static final String CONTEXT_PARA1 = "partName";
	private static final String CONTEXT_PARA2 = "equipmentId";
	private static final String FIELD_DEFECTINPUT = "defectInput";
	private static final String SCRAP_TRANS_TYPE = "SCRAP";

	protected EntityFormField defectInputField;
	
	private static int DIALOG_WIDTH = 400;
	private static int DIALOG_HEIGHT = 300;

	public PvcDefectActionDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		defectInputField = form.getFieldByControlId(FIELD_DEFECTINPUT, EntityFormField.class);
	}
	
	@Override
	protected void okPressed() {
		try {
			if (defectInputField.validate()) {
				DefectHis defectHis = (DefectHis) defectInputField.getValue();
				defectHis.setIsRework("Y");
				boolean scrapFlag = false;
				BigDecimal defectQty = defectHis.getDefectQty();
				if (SCRAP_TRANS_TYPE.equals(defectHis.getTransType())) {
					scrapFlag = true;
					ContextManager contextManager = Framework.getService(ContextManager.class);
					Map<String, String> map = new HashMap<String, String>();
					map.put(CONTEXT_PARA1, defectHis.getPartName());
					map.put(CONTEXT_PARA2, defectHis.getEquipmentId());
					List<ContextValue> list = contextManager.getContextValues(Env.getOrgRrn(), CONTEXT_NAME, map);
					if (list.size() < 1) {
						UI.showInfo(Message.getString("pvc.conversion_rate_not_found"));
						return;
					}

					// 转化率
					String rate = list.get(0).getResultValue1();
					BigDecimal conversionrate = new BigDecimal(rate);

					// 转化后数量
					BigDecimal qty = defectQty.multiply(conversionrate);

					defectHis.setDefectQty(qty);
					defectHis.setWeightQtyRate(Double.valueOf(rate));
					defectHis.setIsRework("N");
				}
				defectHis.setCreated(new Date());
				defectHis.setCreatedBy(Env.getUserName());
				defectHis.setTransTime(new Date());
				
				ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
				adManager.saveEntity(defectHis, Env.getSessionContext());
				
				if (StringUtils.isNotEmpty(defectHis.getWoId())) {
					PpManager ppManager = Framework.getService(PpManager.class);
					PvcPpManager pvcPpManager = Framework.getService(PvcPpManager.class);
					WorkOrder wo = pvcPpManager.getWorkOrderByDocId(defectHis.getWoId(), false, Env.getSessionContext());
					if (!Objects.isNull(wo)) {
						if (scrapFlag) {
							if (StringUtils.isEmpty(wo.getReserved1())) {
								wo.setReserved1(defectQty.toString());
							} else {
								wo.setReserved1(new BigDecimal(wo.getReserved1()).add(defectQty).toString());
							}
						} else {
							if (StringUtils.isEmpty(wo.getReserved2())) {
								wo.setReserved2(defectQty.toString());
							} else {
								wo.setReserved2(new BigDecimal(wo.getReserved2()).add(defectQty).toString());
							}
						}
						ppManager.saveWorkOrder(wo, Env.getSessionContext());
					}
				}
				
				UI.showInfo(Message.getString("common.operation_successed"));
			} else {
				return;
			}
		} catch (Exception e) {
            e.printStackTrace();
        }
		super.okPressed();
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}