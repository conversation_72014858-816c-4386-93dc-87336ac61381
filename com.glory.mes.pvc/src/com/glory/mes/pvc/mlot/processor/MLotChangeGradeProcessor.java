package com.glory.mes.pvc.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.processor.AbstractMLotProcessor;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcManager;

public class MLotChangeGradeProcessor extends AbstractMLotProcessor {

	private static final String TABLE_NAME = "MLotChangeGradeProcessor";

	private static final String TABLE_NAME_MLOT_LIST = "MMLotListChangeGradeProcessor";
	
	private static final String MLOT_DEFECT_CODE = "DefectCode";

	private IMessageManager mmng;
	private EntityForm entityForm;

	public MLotChangeGradeProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			mmng.setAutoUpdate(false);
			mmng.removeAllMessages();

			if (entityForm.saveToObject()) {
				MLot mLot = (MLot) entityForm.getObject();

				PvcManager pvcManager = Framework.getService(PvcManager.class);
				for (MLot lot : lots) {
					if (mLot.getMainQty().compareTo(lot.getMainQty()) == 1) {
						UI.showError(Message.getString("mm.qty_must_less_than_mainqty"));
						return false;
					}
					lot.setTransMainQty(mLot.getTransMainQty());
					lot.setTransWarehouseRrn(mLot.getTransWarehouseRrn());
					lot.setTransStorageType(mLot.getTransStorageType());
					lot.setTransStorageId(mLot.getTransStorageId());
					String defectCode = mLot.getReserved1();
					String defectComment = mLot.getReserved2();

					MLotAction action = new MLotAction();
					action.setActionCode(defectCode);
					action.setActionComment(defectComment);
					pvcManager.defectMLots2Warehouse(lot, action, Env.getSessionContext());
				}

				UI.showInfo(Message.getString("common.operation_successed"));// 弹出提示框
			} else {
				return false;
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			mmng.setAutoUpdate(true);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		if (MLot.STATE_COM.equals(mLot.getComClass())) {
			return false;
		}
		return true;
	}

	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
		try {
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			ManagedForm mform = new ManagedForm(toolkit, form);
			mmng = mform.getMessageManager();

			Composite body = form.getBody();
			configureBody(body);
			entityForm = new EntityForm(body, SWT.NONE, new MLot(), getADTable(), mmng);
			entityForm.setLayout(new GridLayout(1, false));
			entityForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	public ADTable getADTable() {
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			logger.error("MLotChangeGradeProcessor getADTable error:", e);
		}
		if (adTable == null) {
			adTable = getDefaultTable();
		}
		return adTable;
	}

	public ADTable getDefaultTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();

		ADField adFieldWarehouse = new ADField();
		adFieldWarehouse.setName("transWarehouseRrn");
		adFieldWarehouse.setIsMain(true);
		adFieldWarehouse.setIsDisplay(true);
		adFieldWarehouse.setIsEditable(true);
		adFieldWarehouse.setLabel(Message.getString("mm.target_warehouse"));
		adFieldWarehouse.setLabel_zh(Message.getString("mm.target_warehouse"));
		adFieldWarehouse.setDataType("integer");
		adFieldWarehouse.setDisplayType("reftable");	
		adFieldWarehouse.setReftableRrn(4350820l);
		adFieldWarehouse.setIsMandatory(true);
		adFields.add(adFieldWarehouse);

		ADField adFieldWarehouseId = new ADField();
		adFieldWarehouseId.setName("transWarehouseId");
		adFieldWarehouseId.setIsMain(true);
		adFieldWarehouseId.setIsDisplay(true);
		adFieldWarehouseId.setIsEditable(true);
		adFieldWarehouseId.setLabel(Message.getString("mm.target_storage"));
		adFieldWarehouseId.setLabel_zh(Message.getString("mm.target_storage"));
		adFieldWarehouseId.setDataType("string");
		adFieldWarehouseId.setDisplayType("hidden");	
		adFieldWarehouseId.setReferenceRule("transWarehouseRrn.warehouseId");
		adFieldWarehouseId.setIsMandatory(false);
		adFields.add(adFieldWarehouseId);
		
		ADField adFieldStorageId = new ADField();
		adFieldStorageId.setName("transStorageId");
		adFieldStorageId.setIsMain(true);
		adFieldStorageId.setIsDisplay(true);
		adFieldStorageId.setIsEditable(true);
		adFieldStorageId.setLabel(Message.getString("mm.target_storage"));
		adFieldStorageId.setLabel_zh(Message.getString("mm.target_storage"));
		adFieldStorageId.setDataType("string");
		adFieldStorageId.setDisplayType("reftable");	
		adFieldStorageId.setReftableRrn(93547900l);
		adFieldStorageId.setIsMandatory(false);
		adFields.add(adFieldStorageId);
		
		ADField adFieldStorageType = new ADField();
		adFieldStorageType.setName("transStorageType");
		adFieldStorageType.setIsMain(true);
		adFieldStorageType.setIsDisplay(true);
		adFieldStorageType.setIsEditable(true);
		adFieldStorageType.setLabel(Message.getString("mm.target_storage_type"));
		adFieldStorageType.setLabel_zh(Message.getString("mm.target_storage_type"));
		adFieldStorageType.setDataType("string");
		adFieldStorageType.setDisplayType("hidden");
		adFieldStorageType.setReferenceRule("transStorageId.category");
		adFields.add(adFieldStorageType);
		adTable.setFields(adFields);
		
		//不良代码
		ADField adFieldActionCode = new ADField();
		adFieldActionCode.setName("reserved1");
		adFieldActionCode.setIsMain(true);
		adFieldActionCode.setIsDisplay(true);
		adFieldActionCode.setIsEditable(true);
		adFieldActionCode.setDisplayLength(15l);
		adFieldActionCode.setLabel(Message.getString("pvc.bad_code"));
		adFieldActionCode.setLabel_zh(Message.getString("pvc.bad_code"));
		adFieldActionCode.setDataType("string");
		adFieldActionCode.setDisplayType("userreflist");
		adFieldActionCode.setReftableRrn(14479l);
		adFieldActionCode.setUreflistName(MLOT_DEFECT_CODE);
		adFieldActionCode.setIsMandatory(true);
		adFields.add(adFieldActionCode);
		
		//不良描述
		ADField adFieldDesc = new ADField();
		adFieldDesc.setName("reserved2");
		adFieldDesc.setIsMain(true);
		adFieldDesc.setIsDisplay(true);
		adFieldDesc.setIsEditable(true);
		adFieldDesc.setLabel(Message.getString("pvc.bad_reason"));
		adFieldDesc.setLabel_zh(Message.getString("pvc.bad_reason"));
		adFieldDesc.setDataType("string");
		adFieldDesc.setDisplayType("text");
		adFieldDesc.setIsMandatory(false);
		adFields.add(adFieldDesc);
		
		ADField adFieldQty = new ADField();
		adFieldQty.setName("transMainQty");
		adFieldQty.setIsMain(true);
		adFieldQty.setIsDisplay(true);
		adFieldQty.setIsEditable(true);
		adFieldQty.setLabel(Message.getString("mm.iqc_bad_qty"));
		adFieldQty.setLabel_zh(Message.getString("mm.iqc_bad_qty"));
		adFieldQty.setDataType("integer");
		adFieldQty.setDisplayType("text");
		adFieldQty.setIsMandatory(true);
		adFields.add(adFieldQty);
								
		adTable.setFields(adFields);

		return adTable;
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("MLotChangeGradeProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}

	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> fields = adTable.getFields();

		ADField fieldTransWarehouseId = new ADField();
		fieldTransWarehouseId.setName("transWarehouseId");
		fieldTransWarehouseId.setIsMain(true);
		fieldTransWarehouseId.setIsDisplay(true);
		fieldTransWarehouseId.setSeqNo(75l);
		fieldTransWarehouseId.setLabel(Message.getString("mm.warehouse.list"));
		fieldTransWarehouseId.setLabel_zh(Message.getString("mm.warehouse.list"));
		fields.add(fieldTransWarehouseId);

		ADField fieldStorageId = new ADField();
		fieldStorageId.setName("transStorageId");
		fieldStorageId.setIsMain(true);
		fieldStorageId.setIsDisplay(true);
		fieldStorageId.setIsEditable(true);
		fieldStorageId.setLabel(Message.getString("wip.position"));
		fieldStorageId.setLabel_zh(Message.getString("wip.position"));
		fieldStorageId.setDataType("string");
		fieldStorageId.setIsMandatory(false);
		fields.add(fieldStorageId);

		/*ADField adFieldActionCode = new ADField();
		adFieldActionCode.setName("grade1");
		adFieldActionCode.setIsMain(true);
		adFieldActionCode.setIsDisplay(true);
		adFieldActionCode.setIsEditable(true);
		adFieldActionCode.setLabel(Message.getString("mm.mlot_grade"));
		adFieldActionCode.setLabel_zh(Message.getString("mm.mlot_grade"));
		adFieldActionCode.setDataType("string");
		adFieldActionCode.setIsMandatory(false);
		fields.add(adFieldActionCode);*/
		return adTable;
	}

}
