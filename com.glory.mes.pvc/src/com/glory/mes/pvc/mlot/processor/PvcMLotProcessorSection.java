package com.glory.mes.pvc.mlot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.mlot.processor.MLotProcessorLotListSection;

public class PvcMLotProcessorSection extends MLotProcessorLotListSection {
	
	public static final String KEY_CHANGEGRADE = "ChangeGrade";
	public static final String KEY_RETURNTOTHRID = "ReturnToThrid";

	private ToolItem itemChangeGrade;
	private ToolItem itemReturnToThrid;
	
	public PvcMLotProcessorSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemIn(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemOnShelf(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemOffShelf(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTransfer(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemOut(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemReturn(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemReturnToThrid(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemChangeShelfLife(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemChangeGrade(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRelease(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSearch(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		// 根据系统参数判定时都需要展示该按钮
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			boolean isShowSvg = MesCfMod.isShowSvgButton(Env.getOrgRrn(), sysParamManager);
			if (isShowSvg) {
				new ToolItem(tBar, SWT.SEPARATOR);
				createToolItemSvg(tBar);
			}
		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : createToolItemSvg ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);

	}

	protected void createToolItemChangeGrade(ToolBar tBar) {
		itemChangeGrade = new AuthorityToolItem(tBar, SWT.PUSH,
				tableManager.getADTable().getAuthorityKey() + "." + KEY_CHANGEGRADE);
		itemChangeGrade.setText(Message.getString("pvc.defect_in"));
		itemChangeGrade.setImage(SWTResourceCache.getImage("edit"));
		itemChangeGrade.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				changeGradeAdapter();
			}
		});
	}

	protected void changeGradeAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot) object);
				}
				if (checkLots.size() > 1) {
					UI.showInfo(Message.getString("common.only_allow_select_one_object"));
					return;
				}
				MLotChangeGradeProcessor processor = new MLotChangeGradeProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}

		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : changeShelfLifeAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void createToolItemReturnToThrid(ToolBar tBar) {
		itemReturnToThrid = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_RETURNTOTHRID);
		itemReturnToThrid.setText(Message.getString("pvc.mlot_return"));
		itemReturnToThrid.setImage(SWTResourceCache.getImage("sell_return"));
		itemReturnToThrid.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				returnToThirdAdapter();
			}
		});
	}

	protected void returnToThirdAdapter() {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<MLot> checkLots = new ArrayList<MLot>();
				for (Object object : objects) {
					checkLots.add((MLot) object);
				}
				MLotReturnToThirdProcessor processor = new MLotReturnToThirdProcessor(true);
				processor.open(checkLots);
				refresh();
			} else {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}

		} catch (Exception e) {
			logger.error("MLotProcessorLotListSection : changeShelfLifeAdapter()", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}		
	
}
