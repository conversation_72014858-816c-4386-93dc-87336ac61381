package com.glory.mes.pvc.mlot.processor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.mlot.processor.AbstractMLotProcessor;
import com.glory.mes.pvc.client.PvcADManager;

public class MLotReturnToThirdProcessor extends AbstractMLotProcessor {

	private static final String TABLE_NAME_MLOT_LIST = "MMLotListChangeGradeProcessor";

	public MLotReturnToThirdProcessor(boolean isBatch) {
		super(isBatch);
	}

	@Override
	public boolean process(List<MLot> lots) {
		try {
			// 检查批次状态是否允许
			Optional<MLot> f = lots.stream().filter(m -> !StringUtil.isEmpty(m.getMessageString())).findFirst();
			if (f.isPresent()) {
				UI.showError(Message.getString("error.state_not_allow"));
				return false;
			}

			// 检查输入数量是否正确
			f = lots.stream()
					.filter(m -> m.getTransMainQty() == null || m.getTransMainQty().compareTo(BigDecimal.ZERO) <= 0
							|| m.getMainQty().compareTo(m.getTransMainQty()) < 0)
					.findFirst();
			if (f.isPresent()) {
				UI.showError(Message.getString("wip.scrap_check_qty"));
				return false;
			}
			
			MMManager mmManager = Framework.getService(MMManager.class);
			for (MLot mlot : lots) {
				/*mmManager.inMLot(mlot, null, mlot.getTransMainQty(), new MLotAction(),
						mlot.getTransWarehouseRrn(), null, mlot.getTransStorageType(), mlot.getTransStorageId(),
						Env.getSessionContext());*/
				mmManager.unConsumeMLot(mlot.getObjectRrn(), mlot.getTransMainQty(), Env.getSessionContext());
			}
			UI.showInfo(Message.getString("common.operation_successed"));// 弹出提示框
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}

	@Override
	public boolean checkMLotState(MLot mLot) {
		if (MLot.STATE_COM.equals(mLot.getComClass())) {
			return false;
		}
		return true;
	}
	
	public void openLotProcessorDialog(List<MLot> lots) {	
		MLotReturnToThirdDialog dialog = new MLotReturnToThirdDialog(this, lots);
		if (dialog.open() == Dialog.OK) {}
	}

	@Override
	public void buildProcessForm(Composite parent, FormToolkit toolkit) {
	}

	/**
	 * 获得显示选中的批次信息动态表
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT_LIST);
		} catch (Exception e) {
			logger.error("MLotChangeGradeProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}

	/**
	 * 生成默认查询动态表
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = super.getDefaultListADTable();
		List<ADField> fields = adTable.getFields();
		for (ADField field : fields) {
			field.setIsEditable(false);
		}

		ADField fieldTransWarehouseId = new ADField();
		fieldTransWarehouseId.setName("transWarehouseId");
		fieldTransWarehouseId.setIsMain(true);
		fieldTransWarehouseId.setIsDisplay(true);
		fieldTransWarehouseId.setSeqNo(75l);
		fieldTransWarehouseId.setLabel(Message.getString("mm.warehouse.list"));
		fieldTransWarehouseId.setLabel_zh(Message.getString("mm.warehouse.list"));
		fields.add(fieldTransWarehouseId);

		ADField fieldStorageId = new ADField();
		fieldStorageId.setName("transStorageId");
		fieldStorageId.setIsMain(true);
		fieldStorageId.setIsDisplay(true);
		fieldStorageId.setIsEditable(false);
		fieldStorageId.setLabel(Message.getString("wip.position"));
		fieldStorageId.setLabel_zh(Message.getString("wip.position"));
		fieldStorageId.setDataType("string");
		fieldStorageId.setIsMandatory(false);
		fields.add(fieldStorageId);

		ADField adFieldActionCode = new ADField();
		adFieldActionCode.setName("transMainQty");
		adFieldActionCode.setIsMain(true);
		adFieldActionCode.setIsDisplay(true);
		adFieldActionCode.setIsEditable(true);
		adFieldActionCode.setLabel(Message.getString("pvc.mlot_qty_return"));
		adFieldActionCode.setLabel_zh(Message.getString("pvc.mlot_qty_return"));
		adFieldActionCode.setDataType("integer");
		adFieldActionCode.setDisplayType("text");
		adFieldActionCode.setIsMandatory(true);
		fields.add(adFieldActionCode);
		return adTable;
	}

}
