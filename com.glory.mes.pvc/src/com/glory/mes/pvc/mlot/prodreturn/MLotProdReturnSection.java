package com.glory.mes.pvc.mlot.prodreturn;


import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.mlot.MLotSection;

public class MLotProdReturnSection extends MLotSection{
	
	public MLotProdReturnSection(ADTable adTable) {
		super(adTable);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemShip(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemShip(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString("mm.unship_warehouse"));
		itemSave.setImage(SWTResourceCache.getImage("warehouse_area"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				returnAdapter();
			}
		});
	}
	
	private void returnAdapter() {
		form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
                if (!detailForm.saveToObject()) {
                	saveFlag = false;
                }
            }
			if (saveFlag) {
				MLot mLot = (MLot) getAdObject();
				if (mLot == null) {
					return;
				}
				try {
					MMManager mmManager = Framework.getService(MMManager.class);
					MLotAction mLotAction = new MLotAction();
					mLotAction.setActionComment(mLot.getLotComment());
					
					//批次直接入库(批次已经存在)
					mLot = mmManager.returnMLot(mLot, MLot.RECEIVE_TYPE_IN, mLot.getTransMainQty(), new MLotAction(), null, 
							null, mLot.getTransStorageType(), mLot.getTransStorageId(), Env.getSessionContext());
					UI.showInfo(Message.getString("common.return.success"));
					setAdObject(mLot);
					refresh();
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	}
	
}
