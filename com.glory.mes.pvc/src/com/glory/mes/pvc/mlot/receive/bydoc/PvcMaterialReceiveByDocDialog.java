package com.glory.mes.pvc.mlot.receive.bydoc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.RandomStringUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.model.Material;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.wip.mm.MaterialRequisition;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.google.common.collect.Lists;

public class PvcMaterialReceiveByDocDialog extends GlcBaseDialog {//extends PvcMLotReceiveDialog { 

	private static final String FIELD_WOID = "woId";
	private static final String FIELD_DOCID = "requisitionId";


	protected RefTableField woIdField;

	public PvcMaterialReceiveByDocDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	
	private static final String FIELD_SOURCEMLOTINFO = "sourceMLotInfo";
	private static final String FIELD_MLOTID = "mLotId";
	private static final String FIELD_TRANSQTY = "transMainQty";
	private static final String FIELD_TRANSWAREHOUSEID = "transWarehouseId";
	private static final String FIELD_TRANSSTORAGEID = "transStorageId";
	
	private static final String FIELD_RECEIVEMLOTACTION = "receiveMLotAction";

	protected QueryFormField materialRequisitionLineField;
	
	protected TextField mLotIdField;
	protected TextField transMainQtyField;
	protected RefTableField transWarehouseIdField;
	protected RefTableField transStorageIdField;
	
	protected ListTableManagerField receiveMLotActionField;
	
	protected List<MLot> mLotList = Lists.newArrayList();
	

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		materialRequisitionLineField = form.getFieldByControlId(FIELD_SOURCEMLOTINFO, QueryFormField.class);
		
		mLotIdField = form.getFieldByControlId(FIELD_MLOTID, TextField.class);
		transMainQtyField = form.getFieldByControlId(FIELD_TRANSQTY, TextField.class);
		transWarehouseIdField = form.getFieldByControlId(FIELD_TRANSWAREHOUSEID, RefTableField.class);
		transStorageIdField = form.getFieldByControlId(FIELD_TRANSSTORAGEID, RefTableField.class);
		
		receiveMLotActionField = form.getFieldByControlId(FIELD_RECEIVEMLOTACTION, ListTableManagerField.class);
		changeMLotIdEvent();
		
		subscribeAndExecute(eventBroker, materialRequisitionLineField.getFullTopic(GlcEvent.EVENT_QUERY), this::sourceMLotInfoQuery);
		
	}
	
	
	protected void sourceMLotInfoQuery(Object object) {
		try {
			QueryTableForm tableForm = materialRequisitionLineField.getQueryForm();
			RefTableField woIdField = (RefTableField) tableForm.getFields().get(FIELD_WOID);
			RefTableField docField = (RefTableField) tableForm.getFields().get(FIELD_DOCID);

			PpManager ppManager = Framework.getService(PpManager.class);

			MaterialRequisition materialRequisition = ppManager.getMaterialRequisition((String) docField.getValue(),
					true, true, Env.getSessionContext());
			if (materialRequisition == null) {
				tableForm.getTableManager().setInput(Lists.newArrayList());
				return;
			}
			if (woIdField.getValue() != null
					&& !StringUtils.equals(materialRequisition.getWoId(), (String) woIdField.getValue())) {
				tableForm.getTableManager().setInput(Lists.newArrayList());
				return;
			}

			tableForm.getTableManager().setInput(materialRequisition.getMrLines());

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	// 注册组件号控件enter事件
	protected void changeMLotIdEvent() {
		mLotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@SuppressWarnings("unchecked")
			@Override
			public void keyPressed(KeyEvent event) {
				Text mLotIdText = ((Text) event.widget);
				String mainQty =  transMainQtyField.getText();
				
				
				
				mLotIdText.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String mLotId = mLotIdText.getText();
					
					
					MMManager mmManager = null;
					try {
						mmManager = Framework.getService(MMManager.class);
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
					}
					
					if (transWarehouseIdField.getValue() == null) {
						UI.showError(Message.getString("mm.please_enter_warehouse"));
						return;
					}
					
					if (transStorageIdField.getValue() == null) {
						UI.showError(Message.getString("mm.please_enter_storage"));
						return;
					}
					
					Long warehouseRrn  = Long.valueOf( (String)transWarehouseIdField.getValue());
					Warehouse warehouse = new Warehouse();
					warehouse.setObjectRrn(warehouseRrn);
					warehouse =  mmManager.getWarehouse(warehouse);
					
					String storageId = (String)transStorageIdField.getValue();
					Storage storage = new Storage();
					storage.setOrgRrn(Env.getOrgRrn());
					storage.setName(storageId);
					storage = mmManager.getStorage(storage);
					
					
					if (StringUtil.isEmpty(mLotId)) {
						UI.showError(Message.getString("mm.please_enter_mlot_id"));
						return;
					}
					
					if (StringUtil.isEmpty(mainQty)) {
						UI.showError(Message.getString("mm.please_enter_main_qty"));
						return;
					}
					
					
					try {
						// 校验是否已经在列表中
						List<MLot> mlots = (List<MLot>) (Object) receiveMLotActionField.getListTableManager()
								.getInput();
						Boolean flag = false;
						for (MLot mlot : mlots) {
							if (mlot.getmLotId().equals(mLotId)) {
								flag = true;
								break;
							}
						}
						if (flag) {
							UI.showError(Message.getString("wip.box_already_exists"));
							mLotIdField.setText("");
							mLotIdField.getTextControl().forceFocus();
							return;
						}


						MaterialRequisitionLine maLine = (MaterialRequisitionLine) materialRequisitionLineField
								.getSelectedObject();
						if (maLine == null) {
							UI.showError(Message.getString("pvc.please_check_material_requisition"));
							mLotIdField.setText("");
							mLotIdField.getTextControl().forceFocus();
							return;
						}
						// 防止物料批号重复
						Material material = mmManager.getMaterial(Env.getOrgRrn(), maLine.getMaterialName());
						if (material == null) {
							UI.showError(Message.getString("mm.material_not_found"));
							return;
						}

						MLot existMLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
						if (existMLot != null) {
							if (Material.BATCH_TYPE_LOT.equals(material.getBatchType())
									|| Material.BATCH_TYPE_LOT.equals(existMLot.getBatchType())) {
								UI.showError(Message.getString("wip.changeId_have"));
								mLotIdField.setText("");
								return;
							}
						}
						
						

						MLot mlot = new MLot();
						mlot.setmLotId(mLotId);
						mlot.setMainQty(new BigDecimal(mainQty));
						
						// 校验领料单数量
						if (mlot.getMainQty()
								.add(maLine.getRealTotalQty() == null ? BigDecimal.ZERO : maLine.getRealTotalQty())
								.compareTo(maLine.getLineQty() == null ? BigDecimal.ZERO : maLine.getLineQty()) > 0) {
							UI.showError(Message.getString("mm.pick_qty_more_than_line_qty"));
							return;
						}
						
						mlot.setTransMainQty(mlot.getMainQty());
						mlot.setTransWarehouseId(warehouse.getWarehouseId());
						mlot.setTransTargetWarehouseRrn(warehouse.getObjectRrn());
						mlot.setTransTargetWarehouseId(warehouse.getWarehouseId());
						mlot.setTransTargetStorageId(storage.getName());
						mlot.setTransTargetStorageType(storage.getCategory());
						mlot.setTransStorageId(storage.getName());
						mlot.setTransStorageType(storage.getCategory());
						mlot.setMaterialName(material.getName());
						mlot.setMaterialRrn(material.getObjectRrn());
						mlot.setMaterialDesc(material.getDescription());
						mlot.setMaterialType(material.getMaterialType());
						mlot.setMaterialVersion(material.getVersion());
						mlot.setUomId(material.getUomId());
						
						mLotList.add(mlot);
						

						BigDecimal totalQty = mLotList.stream().map(p -> p.getMainQty()).reduce(BigDecimal.ZERO,
								BigDecimal::add);

						if (totalQty.add(maLine.getRealTotalQty() == null ? BigDecimal.ZERO : maLine.getRealTotalQty())
								.compareTo(maLine.getLineQty() == null ? BigDecimal.ZERO : maLine.getLineQty()) > 0) {
							UI.showError(Message.getString("mm.pick_qty_more_than_line_qty"));
							return;
						}
						
						receiveMLotActionField.getListTableManager().setInput(mLotList);
						mLotIdField.setText("");
						mLotIdField.getTextControl().forceFocus();
						
						
					} catch (Exception e) {
						UI.showError(Message.getString("common.system_occur_error"));
					}
					break;
				}
			}
		});
	}
	
	@SuppressWarnings("unchecked")
	protected void okPressed() {
		try {
			List<Object> objects = (List<Object>) receiveMLotActionField.getListTableManager().getInput();
			if (objects == null || objects.size() == 0) {
				UI.showInfo(Message.getString("mm.please_add_mlot"));
				return;
			}
			
			if (!UI.showConfirm(Message.getString("common.submit_confirm"))) {
				return;
			}
			MMManager mmManager = Framework.getService(MMManager.class);
			PpManager ppManager = Framework.getService(PpManager.class);
			
			MaterialRequisitionLine maLine = (MaterialRequisitionLine) materialRequisitionLineField.getSelectedObject();
			if (maLine == null) {
				UI.showError(Message.getString("pvc.please_check_material_requisition"));
				mLotIdField.setText("");
				mLotIdField.getTextControl().forceFocus();
				return;
			}
			MaterialRequisition materialRequisition = ppManager.getMaterialRequisition(maLine.getRequisitionId(), true, Env.getSessionContext());
			
			for (Object object : objects) {
				MLot mlot = (MLot) object;
				
				MLotAction mLotAction = new MLotAction();
				mLotAction.setMainQty(mlot.getTransMainQty());
				List<MLot> receiveMLotList = new ArrayList<MLot>();
				receiveMLotList.add(mlot);
				//mmManager.receiveMLots2Warehouse(receiveMLotList, mLotAction, Env.getSessionContext());
				ppManager.reveiveMaterialRequisition(materialRequisition, receiveMLotList, true, Env.getSessionContext());
			}

			UI.showInfo(Message.getString("common.receive.success"));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

}