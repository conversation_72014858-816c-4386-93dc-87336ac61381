package com.glory.mes.pvc.mlot.receive;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.AbstractBomLine;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.model.Material;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pvc.PvcGlcEditor;

public class ReceiveMLotForm extends PvcGlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.mlot.receive.ReceiveMLotForm";

	// 界面上面部分
	private GlcFormField receiveUpSection;

	// 界面下面部分
	private ListTableManagerField mLotListField;

	// 输入部分
	private EntityFormField inputEntityField;

	// 界面左边部分最上面的组件号输入框
	private TextField mLotIdField,mainQty;
	
	// 工单编号
	private RefTableField woIdField,transWarehouseId;
	
	// 物料编码
	private RefTableField materialNameField;

	protected List<MLot> mLotList = new ArrayList<MLot>();
	
	List<Material> bomLineMaterias = new ArrayList<Material>();

	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		receiveUpSection = form.getFieldByControlId("PvcReceiveMLotUp", GlcFormField.class);

		mLotListField = form.getFieldByControlId("PvcReceiveMLotDown", ListTableManagerField.class);
		
		inputEntityField = receiveUpSection.getFieldByControlId("PvcReceiveMLot", EntityFormField.class);
		
		mLotIdField = inputEntityField.getFieldByControlId("mLotId", TextField.class);
		
		woIdField = inputEntityField.getFieldByControlId("woId", RefTableField.class);

		mainQty = inputEntityField.getFieldByControlId("transMainQty", TextField.class);
		
//		reserved1 = inputEntityField.getFieldByControlId("reserved1", RefTableField.class);
		transWarehouseId = inputEntityField.getFieldByControlId("transWarehouseRrn", RefTableField.class);
		
		String woIdChangeTopicID = woIdField.getFullTopic(GlcEvent.EVENT_VALUECHANGE);
		subscribeAndExecute(eventBroker, woIdChangeTopicID, this::woIdSelectedChange);
		
		materialNameField = inputEntityField.getFieldByControlId("materialName", RefTableField.class);
		materialNameField.setInput(Collections.EMPTY_LIST);

		subscribeAndExecute(eventBroker, form.getFullTopic("save"), this::receive);
		
		subscribeAndExecute(eventBroker, form.getFullTopic("remove"), this::remove);

		changeMLotIdEvent();
	}

	// 注册组件号控件enter事件
	protected void changeMLotIdEvent() {
		// mLotIdField = inputEntityField.getFieldByControlId("lotId", TextField.class);
		mLotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@SuppressWarnings("unchecked")
			@Override
			public void keyPressed(KeyEvent event) {
				Text mLotIdText = ((Text) event.widget);
				mLotIdText.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String mLotId = mLotIdText.getText();
					if (StringUtil.isEmpty(mLotId)) {
						UI.showError(Message.getString("mm.please_enter_mlot_id"));
						return;
					} else {
						try {
							if (inputEntityField.validate()) {

								// 校验是否已经在列表中
								List<MLot> mlots = (List<MLot>) (Object) mLotListField.getListTableManager().getInput();
								Boolean flag = false;
								for (MLot mlot : mlots) {
									if (mlot.getmLotId().equals(mLotId)) {
										flag = true;
										break;
									}
								}
								if (flag) {
									UI.showError(Message.getString("wip.box_already_exists"));
									mLotIdField.setText("");
									return;
								}

								MMManager mmManager = Framework.getService(MMManager.class);
								MLot entityField = (MLot) inputEntityField.getValue();
								
								// 防止物料批号重复
								Material material = mmManager.getMaterial(Env.getOrgRrn(), entityField.getMaterialName());
								if (material == null) {
									UI.showError(Message.getString("mm.material_not_found"));
									return;
								}
								entityField.setBatchType(material.getBatchType());
								
								MLot existMLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
								if (existMLot != null) {
									if (Material.BATCH_TYPE_LOT.equals(material.getBatchType()) || Material.BATCH_TYPE_LOT.equals(existMLot.getBatchType())) {
										UI.showError(Message.getString("wip.changeId_have"));
										mLotIdField.setText("");
										return;
									}
									if(!existMLot.getWoId().equals(entityField.getWoId())) {
										UI.showError(Message.getString("error.work_order_cannot_received"));
										return;
									}
								}
								
			                    MLot mlot = new MLot();
			                    PropertyUtil.copyProperties(mlot, entityField);
			                    mlot.setMainQty(entityField.getMainQty());
			                    mlot.setTransMainQty(entityField.getTransMainQty());
								entityField.setTransMainQty(entityField.getMainQty());
								mLotList.add(mlot);
								mLotListField.getListTableManager().setInput(mLotList);
								inputEntityField.setValue(entityField);
								inputEntityField.refresh();
								mLotIdField.setText("");
							}
						} catch (Exception e) {
							UI.showError(Message.getString("common.system_occur_error"));
						}
					}
					break;
				}
			}
		});
	}

	/**
	 * 物料批接收
	 * 
	 * @param obj
	 */
	@SuppressWarnings("unchecked")
	private void receive(Object obj) {
		try {
			List<Object> objects = (List<Object>) mLotListField.getListTableManager().getInput();
			if (objects == null || objects.size() == 0) {
				UI.showInfo(Message.getString("mm.please_add_mlot"));
				return;
			}
			
			if (!UI.showConfirm(Message.getString("common.submit_confirm"))) {
				return;
			}
			MMManager mmManager = Framework.getService(MMManager.class);
			
			for (Object object : objects) {
				MLot mlot = (MLot) object;
				
				MLotAction mLotAction = new MLotAction();
				mLotAction.setMainQty(mlot.getTransMainQty());
				List<MLot> receiveMLotList = new ArrayList<MLot>();
				receiveMLotList.add(mlot);
				mmManager.receiveMLots2Warehouse(receiveMLotList, mLotAction, Env.getSessionContext());
			}

			UI.showInfo(Message.getString("common.receive.success"));
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * 移除功能
	 * 
	 * @param obj
	 */
	@SuppressWarnings("unchecked")
	private void remove(Object obj) {
		try {
			List<MLot> deleteMLot = (List<MLot>) (Object) mLotListField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isNotEmpty(deleteMLot)) {
				for (MLot mlot : deleteMLot) {
					if (mLotList.contains(mlot)) {
						mLotList.remove(mlot);
					}
				}
			}
			mLotListField.getListTableManager().setInput(mLotList);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 工单号变更,带出BOM里的物料编码
	 * 
	 * @param obj
	 */
	private void woIdSelectedChange(Object obj) {
		try {
			materialNameField.setInput(Collections.EMPTY_LIST);
			WorkOrder wo = (WorkOrder)woIdField.getData();
			if (wo != null) {
				materialNameField.setValue(null);
				materialNameField.refresh();
				
				PpManager ppManager = Framework.getService(PpManager.class);
				MMManager mmManager = Framework.getService(MMManager.class);
				
				List<AbstractBomLine> bomLines = new ArrayList<AbstractBomLine>();
				// 工单BOM
				List<WorkOrderBomLine> woBomLines = ppManager.getWorkOrderBomLines(wo, Env.getSessionContext());
				if (!CollectionUtils.isEmpty(woBomLines)) {
					bomLines = woBomLines.stream().map( line -> (AbstractBomLine)line).collect(Collectors.toList());
				} else {
					// 产品BOM
					Bom partBom = mmManager.getActiveBom(Env.getOrgRrn(), wo.getPartName(), null, null);
					if (partBom != null) {
						List<BomLine> partBomLines = partBom.getBomLines();
						bomLines = partBomLines.stream().map( line -> (AbstractBomLine)line).collect(Collectors.toList());
					}
				}
				
				if (!CollectionUtils.isEmpty(bomLines)) {
					 bomLineMaterias = new ArrayList<Material>();
					bomLineMaterias = bomLines.stream().map( line -> {
						return mmManager.getMaterial(Env.getOrgRrn(), line.getMaterialName());
					}).collect(Collectors.toList());
					materialNameField.setInput(bomLineMaterias);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void refresh() {
		try {
//			MLot entityField = (MLot) inputEntityField.getValue();
			
//			inputEntityField.refresh();
//			reserved1.setValue(null);
//			reserved1.refresh();
			mainQty.setText("0");
			transWarehouseId.setValue(null);
			transWarehouseId.refresh();
			woIdField.setValue(null);
			woIdField.refresh();
			mLotList.clear();
			mLotListField.getListTableManager().setInput(mLotList);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
