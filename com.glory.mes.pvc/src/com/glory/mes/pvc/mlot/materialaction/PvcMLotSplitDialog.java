package com.glory.mes.pvc.mlot.materialaction;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pvc.mlot.PvcMaterialSplitActionDialog;

public class PvcMLotSplitDialog extends PvcMaterialSplitActionDialog {
	
	protected PvcMaterialActionManagerEditor materialGlcEditor;
	
	public PvcMLotSplitDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots, PvcMaterialActionManagerEditor materialGlcEditor) {
		super(adFormName, authority, eventBroker, mLots);
		this.materialGlcEditor = materialGlcEditor;
		setBlockOnOpen(false);
	}
	
	@Override
	protected void okPressed() {
		// 检查重复批号
		Set<String> mLotIds = new HashSet<String>();
		for (Object object : tableManager.getInput()) {
			MLot mLot = (MLot) object;
			if (StringUtil.isEmpty(mLot.getmLotId())) {
				continue;
			}
			
			if (mLotIds.contains(mLot.getmLotId())) {
				UI.showWarning(String.format(Message.getString("mm.lot_id_repeat"), mLot.getmLotId()));
				return;
			}
			
			mLotIds.add(mLot.getmLotId());
		}
		
		// 检查母批数量是否足够
		BigDecimal total = BigDecimal.ZERO;
		for (Object object : tableManager.getInput()) {
			MLot mLot = (MLot) object;
			if (BigDecimal.ZERO.compareTo(mLot.getMainQty()) >= 0) {
				UI.showWarning(Message.getString("mm.split_qty_greater0"));
				return;
			}
			total = total.add(mLot.getMainQty());
		}
		
		BigDecimal reservedQty = lot.getReservedMainQty() == null ? BigDecimal.ZERO : lot.getReservedMainQty();
		if (total.compareTo(lot.getMainQty().subtract(reservedQty)) > 0) {
			UI.showWarning(Message.getString("mm.split_parent_not_enough"));
			return;
		}
		
		materialGlcEditor.split(lot, getSubLots());
		super.okPressed();
	}

}
