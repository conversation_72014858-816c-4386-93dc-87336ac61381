package com.glory.mes.pvc.mlot.compmaterialaction;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.his.model.MLotHis;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotScrap;
import com.glory.mes.mm.mlot.action.dialog.MMLotCompScrapDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotCompUnScrapDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotHoldDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotReleaseDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotScrapDialog;
import com.glory.mes.mm.mlot.action.dialog.MMLotUnScrapDialog;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcMLotManager;

public class PvcCompMaterialActionManagerEditor extends PvcGlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.mlot.compmaterialaction.PvcCompMaterialActionManagerEditor";

	private static final String ASSIGN_DIALOG_FORM = "AtesiMLotBindDialog";
	private static final String RECEIVE_DIALOG_FORM = "PvcCompMLotReceiveDialog";
	private static final String START_DIALOG_FORM = "PvcCompMLotStartDialog";
	private static final String HIS_QUERY_DIALOG_FORM = "PvcCompHisQueryDialog";
	private static final String SPLIT_DIALOG_NAME = "PvcCompMLotSplitDialog";
	private static final String MERGE_DIALOG_NAME = "MMLotMergeDialog";

	private static final String FIELD_MLOTQUERY = "mlotQuery";

	private static final String BUTTON_ASSIGN = "assign";
	private static final String BUTTON_DEASSIGN = "deassign";
	private static final String BUTTON_RECEIVE = "receive";
	private static final String BUTTON_START = "start";
	private static final String BUTTON_HOLD = "hold";
	private static final String BUTTON_RELEASE = "release";
	private static final String BUTTON_SCRAP = "scrap";
	private static final String BUTTON_UNSCRAP = "unScrap";
	private static final String BUTTON_SPLIT = "split";
	private static final String BUTTON_MERGE = "merge";
	private static final String BUTTON_HISQUERY = "hisQuery";

	protected QueryFormField mlotQueryField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mlotQueryField = form.getFieldByControlId(FIELD_MLOTQUERY, QueryFormField.class);

		subscribeAndExecute(eventBroker, mlotQueryField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
//		subscribeAndExecute(eventBroker, mlotQueryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::mlotQuerySelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ASSIGN), this::assignAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DEASSIGN), this::deassignAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RECEIVE), this::receiveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_START), this::startAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HOLD), this::holdAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RELEASE), this::releaseAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SCRAP), this::scrapAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNSCRAP), this::unScrapAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SPLIT), this::splitAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MERGE), this::mergeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HISQUERY), this::hisQueryAdapter);
	}

	public void queryAdapter(Object object) {
		try {
			String whereClause = mlotQueryField.getQueryForm().getTableManager().getADTable().getWhereClause();
			String orderClause = mlotQueryField.getQueryForm().getTableManager().getADTable().getOrderByClause();
			Map<String, Object> paramMap = mlotQueryField.getQueryForm().getQueryForm().getParmaterMap();
			PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
			List<MLot> inputs = adManager.getEntityList(Env.getOrgRrn(), MLot.class, 0, Env.getMaxResult(), whereClause,
					orderClause, paramMap);
			inputs = pvcMLotManager.getMLotStorageByMLots(Env.getOrgRrn(), inputs);
			mlotQueryField.getQueryForm().getTableManager().setInput(inputs);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void receiveAdapter(Object object) {
		PvcCompMLotReceiveDialog dialog = new PvcCompMLotReceiveDialog(RECEIVE_DIALOG_FORM, null, eventBroker);
		if (Dialog.OK == dialog.open()) {
			refreshAdapter(object);
		}
	}

	private void assignAdapter(Object object) {
		try {
			List<Object> objs = mlotQueryField.getCheckedObjects();
			if (CollectionUtils.isEmpty(objs)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<MLot> mLots = (List) objs.stream().map(o -> (MLot) o).collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(mLots)) {
				MLot tempMLot = mLots.get(0);
				for (MLot currentMLot : mLots) {

					// 检查勾选的物料批的物料是否一致
					if (!tempMLot.getMaterialName().equals(currentMLot.getMaterialName())) {
						UI.showError(Message.getString("cust.material_not_same"));
						return;
					}

					// 检查勾选的物料批是否都是已绑定的状态
					if ("Y".equals(currentMLot.getUdfValue("reserved17"))) {
						UI.showError(currentMLot.getmLotId() + ":" + Message.getString("mm.attach_repeatedly"));
						return;
					}

					// 检查勾选的物料批是否都是虚拟批次
					if (!"Y".equals(currentMLot.getUdfValue("isVirtual"))) {
						UI.showError(Message.getString("mm-2054: mm.merege_unsupport_object_type"));
						return;
					}
				}
			}

			AtesiMLotBindDialog baseDialog = new AtesiMLotBindDialog(ASSIGN_DIALOG_FORM, null, eventBroker, mLots);
			baseDialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void deassignAdapter(Object object) {
		try {
			List<Object> objs = mlotQueryField.getCheckedObjects();
			if (CollectionUtils.isEmpty(objs)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<MLot> mLots = (List) objs.stream().map(o -> (MLot) o).collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(mLots)) {
				MLot tempMLot = mLots.get(0);
				for (MLot currentMLot : mLots) {

					// 检查勾选的物料批是否都是虚拟批次
					if (!"Y".equals(currentMLot.getUdfValue("isVirtual"))) {
						UI.showError(currentMLot.getmLotId() + ":"
								+ Message.getString("mm-2054: mm.merege_unsupport_object_type"));
						return;
					}

					// 检查勾选的物料批是否都是已绑定的状态
					if (!"Y".equals(currentMLot.getUdfValue("reserved17"))) {
						UI.showError(currentMLot.getmLotId() + ":" + Message.getString("cust.batch_is_not_bound"));
						return;
					}

				}
				PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
				MMManager mmManager = Framework.getService(MMManager.class);
				for (MLot mLot : mLots) {
					String sapMLotId = mLot.getLotComment();
					mLot.putUdfValue("reserved17", "N");
					mLot.putUdfValue("erpMlot", null);
					pvcMLotManager.saveMLot(mLot);
					MLotHis mLotHis = new MLotHis(mLot, Env.getSessionContext());
					mLotHis.setTransType("DEASSIGN");
					adManager.saveEntity(mLotHis, Env.getSessionContext());

					MLot sapMLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), sapMLotId);
					sapMLot.setMainQty(sapMLot.getMainQty().add(mLot.getMainQty()));
					pvcMLotManager.saveMLot(sapMLot);
				}
			}
			UI.showInfo(Message.getString("common.operation_successed"));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void startAdapter(Object object) {
		List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
		List<MLot> checkMlots = new ArrayList<MLot>();
		if (CollectionUtils.isNotEmpty(objects)) {
			checkMlots = (List) objects.stream().map(selectObj -> (MLot) selectObj).collect(Collectors.toList());
		}
		PvcCompMLotStartDialog dialog = new PvcCompMLotStartDialog(START_DIALOG_FORM, null, eventBroker, checkMlots,
				this);
		if (Dialog.OK == dialog.open()) {
			refreshAdapter(object);
		}
	}

	private void holdAdapter(Object object) {
		try {
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<MLot> checkMlots = new ArrayList<MLot>();
			for (Object obj : objects) {
				MLot mlot = (MLot) obj;
				if (MLot.STATE_COM.equals(mlot.getComClass())) {
					UI.showError(String.valueOf(mlot.getmLotId()) + Message.getString("mm.mlot_state_not_allowed"));
					return;
				}
				if ("On".equals(mlot.getHoldState())) {
					UI.showError(String.valueOf(mlot.getmLotId()) + Message.getString("mm.mlots_already_hold"));
					return;
				}
				checkMlots.add(mlot);
			}

			MMLotHoldDialog baseDialog = new MMLotHoldDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (baseDialog.open() == 0) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void releaseAdapter(Object object) {
		try {
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));

				return;
			}
			List<MLot> checkMlots = new ArrayList<MLot>();
			for (Object obj : objects) {
				MLot mlot = (MLot) obj;
				if (!"On".equals(mlot.getHoldState())) {
					UI.showError(String.valueOf(mlot.getmLotId()) + Message.getString("mm.mlot_state_not_allowed"));
					return;
				}
				checkMlots.add(mlot);
			}

			MMLotReleaseDialog baseDialog = new MMLotReleaseDialog(null, null, eventBroker, checkMlots);
			if (!baseDialog.preValidate()) {
				return;
			}
			if (baseDialog.open() == 0) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void scrapAdapter(Object object) {
		try {
			MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<MLot> checkMlots = new ArrayList<MLot>();
			for (Object obj : objects) {
				MLot mlot = (MLot) obj;
				if (MLot.STATE_COM.equals(mlot.getComClass())) {
					UI.showError(String.valueOf(mlot.getmLotId()) + Message.getString("mm.mlot_state_not_allowed"));
					return;
				}
				checkMlots.add(mlot);
			}
			if ("QtyUnit".equals(((MLot) checkMlots.get(0)).getSubUnitType())) {
				MMLotScrapDialog baseDialog = new MMLotScrapDialog(null, null, eventBroker, checkMlots);
				if (!baseDialog.preValidate()) {
					return;
				}
				if (baseDialog.open() == 0) {
					refreshAdapter(object);
				}
			} else {
				MLot mLotComponent = mmManager
						.getMLotWithComponent(((MLot) checkMlots.get(0)).getObjectRrn().longValue());
				MMLotCompScrapDialog dialog = new MMLotCompScrapDialog("MMLotCompScrapDialog", null, eventBroker,
						checkMlots, mLotComponent.getSubMComponentUnit());
				if (dialog != null && dialog.open() == 0) {
					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void unScrapAdapter(Object object) {
		try {
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<MLot> checkMlots = (List) objects.stream().map(o -> (MLot) o).collect(Collectors.toList());
			MLot mlot = (MLot) checkMlots.get(0);
			Boolean isUnScrapComponent = Boolean.valueOf(true);
			if ("QtyUnit".equals(mlot.getSubUnitType())) {
				isUnScrapComponent = Boolean.valueOf(false);
			}
			List<MLotScrap> mLotScraps = new ArrayList<MLotScrap>();
			if (mlot != null && mlot.getObjectRrn() != null) {

				mLotScraps = adManager.getEntityList(Env.getOrgRrn(), MLotScrap.class, 2147483647,
						"mLotRrn = '" + mlot.getObjectRrn() + "'", "");
				if (CollectionUtils.isEmpty(mLotScraps)) {
					UI.showInfo(Message.getString("mm.mlot_scarp_record_not_found"));
					return;
				}
				for (MLotScrap mLotScrap : mLotScraps) {
					if (isUnScrapComponent.booleanValue()) {
						MComponentUnit componentUnit = new MComponentUnit();
						componentUnit.setObjectRrn(mLotScrap.getmComponentRrn());
						componentUnit = (MComponentUnit) adManager.getEntity(componentUnit);

						mLotScrap.setAttribute1(componentUnit.getParentMLotRrn());

						mLotScrap.setAttribute2(componentUnit.getPosition());
					} else {

						mLotScrap.setAttribute1(mLotScrap.getmLotRrn());
					}
					mLotScrap.setSubQty((mLotScrap.getSubQty() == null) ? BigDecimal.ZERO : mLotScrap.getSubQty());

					mLotScrap.setUnScrapMainQty(mLotScrap.getMainQty());
					mLotScrap.setUnScrapSubQty(mLotScrap.getSubQty());

					mLotScrap.setUnScrapCode("");

					mLotScrap.setUnScrapComment("");
				}
			}
			if (!isUnScrapComponent.booleanValue()) {
				MMLotUnScrapDialog baseDialog = new MMLotUnScrapDialog("MMLotUnScrapDialog", null, eventBroker,
						checkMlots, mLotScraps, isUnScrapComponent);
				if (baseDialog.open() == 0) {
					refreshAdapter(object);
				}
			} else {
				MMLotCompUnScrapDialog baseDialog = new MMLotCompUnScrapDialog("MMLotCompUnScrapDialog", null,
						eventBroker, checkMlots, mLotScraps, isUnScrapComponent);
				if (baseDialog.open() == 0) {
					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void splitAdapter(Object object) {
		try {
			List<Object> objs = mlotQueryField.getCheckedObjects();
			if (CollectionUtils.isEmpty(objs)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<MLot> mLots = (List) objs.stream().map(o -> (MLot) o).collect(Collectors.toList());
			if (mLots.get(0) == null || BigDecimal.ZERO.compareTo(((MLot) mLots.get(0)).getMainQty()) >= 0) {
				UI.showInfo(Message.getString("mm.mlot_qty_less_than_zero"));
				return;
			}
			PvcCompMLotSplitDialog baseDialog = new PvcCompMLotSplitDialog(SPLIT_DIALOG_NAME, null, eventBroker, mLots,
					this);
			baseDialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void mergeAdapter(Object object) {
		try {
			List<Object> objs = mlotQueryField.getCheckedObjects();
			if (CollectionUtils.isEmpty(objs)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<MLot> mLots = (List) objs.stream().map(o -> (MLot) o).collect(Collectors.toList());
			PvcCompMLotMergeDialog baseDialog = new PvcCompMLotMergeDialog(MERGE_DIALOG_NAME, null, eventBroker, mLots,
					this);
			baseDialog.open();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void hisQueryAdapter(Object obj) {
		try {
			List<Object> objs = mlotQueryField.getCheckedObjects();
			List<MLot> mLots = objs.stream().map(o -> ((MLot) o)).collect(Collectors.toList());
			PvcCompHisQueryDialog baseDialog = new PvcCompHisQueryDialog(HIS_QUERY_DIALOG_FORM, null, eventBroker,
					mLots);
			if (Dialog.OK == baseDialog.open()) {
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public void split(MLot mLot, List<MLot> subLots) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			mmManager.splitMLot(mLot, subLots, Env.getSessionContext());
			UI.showInfo(Message.getString("common.operation_successed"));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	public void refreshAdapter(Object object) {
		mlotQueryField.getQueryForm().refresh();
		mlotQueryField.refresh();
	}

//	private void mlotQueryQuery(Object object) {
//
//	}

//	private void mlotQuerySelectionChanged(Object object) {
//
//	}

}