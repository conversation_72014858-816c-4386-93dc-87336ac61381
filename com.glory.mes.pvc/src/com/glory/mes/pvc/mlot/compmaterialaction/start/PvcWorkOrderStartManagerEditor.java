package com.glory.mes.pvc.mlot.compmaterialaction.start;

import java.math.BigDecimal;
import java.util.Date;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcMLotManager;
import com.glory.mes.pvc.client.PvcPpManager;
import com.glory.mes.pvc.model.BoxInfo;
import com.glory.mes.pvc.model.wip.WorkOrderMLotHis;

public class PvcWorkOrderStartManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.mlot.compmaterialaction.start.PvcWorkOrderStartManagerEditor";

	public static final String FIELD_BASICINFO = "basicInfo";
	public static final String FIELD_MATERIALDISTRIBUTOR = "materialDistributor";
	public static final String FIELD_WOID = "woId";
	public static final String FIELD_MLOTID = "mLotId";
	public static final String FIELD_STARTQTY = "startQty";

	public static final String BUTTON_START = "start";
	public static final String BUTTON_REFRESH = "refresh";
	
	protected EntityFormField basicInfoField;
	protected RefTableField materialDistributorField;
	protected RefTableField woIdField;
	protected RefTableField mLotIdField;
	protected TextField startQtyField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		basicInfoField = form.getFieldByControlId(FIELD_BASICINFO, EntityFormField.class);
		materialDistributorField = basicInfoField.getFieldByControlId(FIELD_MATERIALDISTRIBUTOR, RefTableField.class);
		woIdField = basicInfoField.getFieldByControlId(FIELD_WOID, RefTableField.class);
		mLotIdField = basicInfoField.getFieldByControlId(FIELD_MLOTID, RefTableField.class);
		startQtyField = basicInfoField.getFieldByControlId(FIELD_STARTQTY, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_START), this::startAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
	}

	private void startAdapter(Object object) {
		try {
			if (basicInfoField.validate()) {
				String materialDistributor = materialDistributorField.getValue().toString();
				WorkOrder wo = (WorkOrder) woIdField.getData();
				MLot mLot = (MLot) mLotIdField.getData();
				BigDecimal startQty = new BigDecimal(startQtyField.getValue().toString());
				if (mLot.getMainQty().compareTo(startQty) < 0) {
					UI.showError(String.format(Message.getString("wms.available_qty_error"), mLot.getmLotId(), startQty, mLot.getMainQty()));
					return;
				}
				
				MMManager mmManager = Framework.getService(MMManager.class);
				PvcMLotManager mLotManager = Framework.getService(PvcMLotManager.class);
				PvcPpManager pvcPpManager = Framework.getService(PvcPpManager.class);
				
				BoxInfo info = new BoxInfo();
				info.setEquipmentId(materialDistributor);
				info.setmLotId(mLot.getmLotId());
				info.setBindTime(new Date());
				
				//绑定料盒物料批
				info.setWaferQty(startQty.toString());
				mLotManager.saveBoxInfo(info, Env.getSessionContext());
				
				//将BoxId记录到MLot预留栏位 -> 历史查询关联信息
				mLot.setWoId(wo.getDocId());
				adManager.saveEntity(mLot, Env.getSessionContext());
				//消耗MLot库存数量
				mmManager.consumeMLot(mLot.getObjectRrn(), startQty, false, Env.getSessionContext());
				//增加工单完成数量
				pvcPpManager.increaseWorkOrderStartQty(wo, startQty, Env.getSessionContext());
				
				WorkOrderMLotHis woMLotHis = new WorkOrderMLotHis();
				woMLotHis.setTransType(WorkOrderMLotHis.TRANS_TYPE_START);
				woMLotHis.setTransTime(new Date());
				woMLotHis.setEquipmentId(materialDistributor);
				woMLotHis.setWoId(wo.getDocId());
				woMLotHis.setmLotId(mLot.getmLotId());
				woMLotHis.setmLotRrn(mLot.getObjectRrn());
				woMLotHis.setMaterialName(mLot.getMaterialName());
				woMLotHis.setMaterialRrn(mLot.getMaterialRrn());
				woMLotHis.setMaterialDesc(mLot.getMaterialDesc());
				woMLotHis.setMainQty(startQty);
				
				adManager.saveEntity(woMLotHis, Env.getSessionContext());
				
				UI.showInfo(Message.getString("common.starts_successed"));
				refreshAdapter(object);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	private void refreshAdapter(Object object) {
		try {
			materialDistributorField.setValue(null);
			materialDistributorField.refresh();
			woIdField.setValue(null);
			woIdField.refresh();
			mLotIdField.setValue(null);
			mLotIdField.refresh();
			startQtyField.setValue(null);
			startQtyField.refresh();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

}