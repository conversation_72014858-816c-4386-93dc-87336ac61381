package com.glory.mes.pvc.mlot.compmaterialaction;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcMLotManager;
import com.glory.mes.pvc.client.PvcPpManager;
import com.glory.mes.pvc.model.BoxInfo;
import com.google.common.collect.Lists;

public class PvcCompMLotStartDialog extends GlcBaseDialog { 

	private static final String FIELD_MLOTID = "mLotId";
	private static final String FIELD_MLOTLIST = "mlotList";
	private static final String FIELD_TRANSFERACTION = "transferAction";
	private static final String FIELD_BOXID = "boxId";
	private static final String FIELD_WAFERQTY = "waferQty";
	private static final String FIELD_WOID = "woId";

	private static final String BUTTON_DELETE = "delete";

	protected TextField mLotIdField;
	protected ListTableManagerField mlotListField;
	protected EntityFormField transferActionField;
	protected TextField boxIdField;
	protected TextField waferQtyField;
	protected RefTableField woIdField;
	
	protected List<MLot> mLotList = Lists.newArrayList();
	
	private PvcCompMaterialActionManagerEditor editor;

	public PvcCompMLotStartDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mlots, PvcCompMaterialActionManagerEditor editor) {
		super(adFormName, authority, eventBroker);
		this.editor = editor;
		this.mLotList = mlots;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mLotIdField = form.getFieldByControlId(FIELD_MLOTID, TextField.class);
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		transferActionField = form.getFieldByControlId(FIELD_TRANSFERACTION, EntityFormField.class);
		boxIdField = transferActionField.getFieldByControlId(FIELD_BOXID, TextField.class);
		waferQtyField = transferActionField.getFieldByControlId(FIELD_WAFERQTY, TextField.class);
		woIdField = transferActionField.getFieldByControlId(FIELD_WOID, RefTableField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);

		woIdField.addValueChangeListener(woIdListener);
		
		changeMLotIdEvent();
		
		initLot();
	}
	
	IValueChangeListener woIdListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			try {
				if (CollectionUtils.isNotEmpty(mLotList) && !Objects.isNull(woIdField.getValue())) {
					List<String> woIds = mLotList.stream().filter(m -> StringUtils.isNotEmpty(m.getWoId())).map(MLot::getWoId).distinct().toList();
					String newWoId = woIdField.getValue().toString();
					woIds.forEach(id -> {
						if (!StringUtils.equals(id, newWoId)) {
							UI.showError(Message.getString("pvc.mlots_belongs_to_different_wo"));
							return;
						}
					});
				}
			} catch (Exception e) {
	            e.printStackTrace();
	        }
		}
	};
	
	public void initLot() {
		try {
			if (CollectionUtils.isNotEmpty(mLotList)) {
				PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
				mLotList = pvcMLotManager.getMLotStorageByMLots(Env.getOrgRrn(), mLotList);
				List<String> woIds = mLotList.stream().filter(m -> StringUtils.isNotEmpty(m.getWoId())).map(MLot::getWoId).distinct().toList();
				if (woIds.size() > 1) {
					UI.showError(Message.getString("pvc.mlots_belongs_to_different_wo"));
					return;
				}
				woIdField.setValue(mLotList.get(0).getWoId());
				woIdField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		mlotListField.getListTableManager().setInput(mLotList);
	}
	
	// 注册组件号控件enter事件
	protected void changeMLotIdEvent() {
		mLotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@SuppressWarnings("unchecked")
			@Override
			public void keyPressed(KeyEvent event) {
				Text mLotIdText = ((Text) event.widget);
				mLotIdText.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String mLotId = mLotIdText.getText();
					if (StringUtil.isEmpty(mLotId)) {
						UI.showError(Message.getString("mm.please_enter_mlot_id"));
						return;
					} else {
						try {
							// 校验是否已经在列表中
							List<MLot> mlots = (List<MLot>) (Object) mlotListField.getListTableManager().getInput();
							Boolean flag = false;
							for (MLot mlot : mlots) {
								if (mlot.getmLotId().equals(mLotId)) {
									flag = true;
									break;
								}
							}
							if (flag) {
								UI.showError(Message.getString("wip.box_already_exists"));
								mLotIdField.setText("");
								mLotIdField.getTextControl().forceFocus();
								return;
							}

							MMManager mmManager = Framework.getService(MMManager.class);
							PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
							MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
							if (Objects.isNull(mLot)) {
								UI.showError(String.format(Message.getString("mm.mlot_is_not_found"), mLotId));
								mLotIdField.setText("");
								mLotIdField.getTextControl().forceFocus();
								return;
							}
							if (!Objects.isNull(woIdField.getValue()) && !StringUtils.equals(mLot.getWoId(), woIdField.getValue().toString())) {
								UI.showError(Message.getString("pvc.mlots_belongs_to_different_wo"));
								return;
							} else {
								woIdField.setValue(mLot.getWoId());
								woIdField.refresh();
							}
							List<MLot> addMLotList = pvcMLotManager.getMLotStorageByMLots(Env.getOrgRrn(), Lists.newArrayList(mLot));
							mLotList.addAll(addMLotList);
							mlotListField.getListTableManager().setInput(mLotList);
							mLotIdField.setText("");
							mLotIdField.getTextControl().forceFocus();
						} catch (Exception e) {
							UI.showError(Message.getString("common.system_occur_error"));
						}
					}
					break;
				}
			}
		});
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void deleteAdapter(Object object) {
		List<Object> allMLots = (List<Object>) mlotListField.getListTableManager().getInput();
		List<Object> selectMLots = mlotListField.getListTableManager().getCheckedObject();
		if (CollectionUtils.isNotEmpty(allMLots) && CollectionUtils.isNotEmpty(selectMLots)) {
			allMLots.removeAll(selectMLots);
			
			List<MLot> selectMLotList = (List) selectMLots.stream().map(selectObj -> (MLot) selectObj)
					.collect(Collectors.toList());
			mLotList.removeAll(selectMLotList);
			
			List<MLot> checkMlots = (List) allMLots.stream().map(selectObj -> (MLot) selectObj)
					.collect(Collectors.toList());
			mlotListField.getListTableManager().setInput(checkMlots);
		}
	}
	
	@SuppressWarnings("unchecked")
	protected void okPressed() {
		try {
			List<MLot> mLots = (List<MLot>) mlotListField.getListTableManager().getInput();
			if (CollectionUtils.isEmpty(mLots)) {
				UI.showError(Message.getString("mm_material_information"));
				return;
			}
			String boxId = boxIdField.getValue().toString();
			String startMainQty = waferQtyField.getValue().toString();
			String woId = woIdField.getValue() == null ? "" : woIdField.getValue().toString();
			if (StringUtils.isEmpty(startMainQty) || StringUtils.isEmpty(boxId) || StringUtils.isEmpty(woId)) {
				UI.showWarning(Message.getString("warn.required_entry"));
				return;
			}
			
			//检查可用数量
			BigDecimal mainQty = BigDecimal.ZERO;
			for (int i = 0; i < mLots.size(); i++) {
				mainQty = mainQty.add(mLots.get(i).getMainQty());
			}
			List<String> mLotIds = mLots.stream().map(MLot::getmLotId).collect(Collectors.toList());
			if (mainQty.compareTo(new BigDecimal(startMainQty)) < 0) {
				UI.showError(String.format(Message.getString("wms.available_qty_error"), mLotIds.toString(), startMainQty, mainQty));
				return;
			} 
			
			//若选择的批次大于2 则需要区分主物料批次; 默认光标选择的为主物料批
			MLot mlot = (MLot) mlotListField.getListTableManager().getSelectedObject();
			if (mLots.size() > 1 && !Objects.isNull(mlot)) {
				mLots.remove(mlot);
				//将主物料批放在第一位
				mLots.add(0, mlot);
			}
			
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			MMManager mmManager = Framework.getService(MMManager.class);
			PpManager ppManager = Framework.getService(PpManager.class);
			PvcMLotManager mLotManager = Framework.getService(PvcMLotManager.class);
			PvcPpManager pvcPpManager = Framework.getService(PvcPpManager.class);
			
			BigDecimal startQty = new BigDecimal(startMainQty);
			boolean completeFlag = false;
			for (MLot mLot : mLots) {
				BoxInfo info = new BoxInfo();
				info.setBoxId(boxId);
				info.setmLotId(mLot.getmLotId());
				info.setBindTime(new Date());
				
				WorkOrder wo = new WorkOrder();
				wo.setDocId(woId);
				wo = ppManager.getWorkOrder(wo, false, true, Env.getSessionContext());
				
				BigDecimal comsumeMainQty = BigDecimal.ZERO;
				if (mLot.getMainQty().compareTo(startQty) < 0) {
					comsumeMainQty = mLot.getMainQty();
					startQty = startQty.subtract(mLot.getMainQty());
				} else {
					comsumeMainQty = startQty;
					completeFlag = true;
				}
				
				//绑定料盒物料批
				info.setWaferQty(comsumeMainQty.toString());
				mLotManager.saveBoxInfo(info, Env.getSessionContext());
				
				//将BoxId记录到MLot预留栏位 -> 历史查询关联信息
				mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLot.getmLotId(), true);
				mLot.setReserved1(boxId);
				mLot.setWoId(woId);
				adManager.saveEntity(mLot, Env.getSessionContext());
				//消耗MLot库存数量
				mmManager.consumeMLot(mLot.getObjectRrn(), comsumeMainQty, false, Env.getSessionContext());
				//增加工单完成数量
				pvcPpManager.completeWorkOrder(wo, comsumeMainQty, Env.getSessionContext());
				
				//已完成发料数量退出循环
				if (completeFlag) {
					break;
				}
			}
			
			UI.showInfo(Message.getString("common.starts_successed"));
			setReturnCode(0);
			clearFields();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void clearFields() {
		editor.refreshAdapter(null);
		mLotList.removeAll(mLotList);
		mlotListField.getListTableManager().setInput(Lists.newArrayList());
		boxIdField.setValue(null);
		waferQtyField.setValue(null);
		woIdField.setValue(null);
		mlotListField.refresh();
		boxIdField.refresh();
		waferQtyField.refresh();
		woIdField.refresh();
	}

}