package com.glory.mes.pvc.mlot.compmaterialaction;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.his.model.MLotHis;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pvc.client.PvcMLotManager;
import com.google.common.collect.Lists;

/**
 * @ClassName: AtesiMLotBindDialog
 * @Description: 虚拟批次绑定SAP批次。绑定时，将真实批次的数据赋给虚拟批，但保留虚拟批号虚拟批次数量，并对应减少真实批次的数量
 * <AUTHOR>
 * @date 2024-09-05 14:21:30
 */
public class AtesiMLotBindDialog extends GlcBaseDialog {

	public static final String FIELD_MLOTLIST = "mlotList";
	public static final String FIELD_ENTITYFORM = "entityForm";
	public static final String FIELD_UDF_ERPMLOT = "udf.erpMlot";

	protected ListTableManagerField mlotListField;
	protected EntityFormField entityFormField;
	protected RefTableField udf_erpMlotField;

	protected List<MLot> mLots = Lists.newArrayList();

	public AtesiMLotBindDialog(String adFormName, String authority, IEventBroker eventBroker, List<MLot> mLots) {
		super(adFormName, authority, eventBroker);
		this.mLots = mLots;
		setBlockOnOpen(false);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);
		entityFormField = form.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		udf_erpMlotField = entityFormField.getFieldByControlId(FIELD_UDF_ERPMLOT, RefTableField.class);

		subscribeAndExecute(eventBroker, mlotListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED),
				this::mlotListSelectionChanged);

		init();
	}

	private void mlotListSelectionChanged(Object object) {

	}

	private void init() {
		try {
			mlotListField.getListTableManager().setInput(mLots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// 确定按钮
	protected void okPressed() {
		try {
			MLot sapMLot = (MLot) udf_erpMlotField.getData();

			List<Object> selectLots = (List<Object>) mlotListField.getListTableManager().getInput();
			List<MLot> checkMlots = (List) selectLots.stream().map(selectObj -> (MLot) selectObj)
					.collect(Collectors.toList());

			// 检查SAP批次栏位是否为空
			if (udf_erpMlotField.getText().isBlank()) {
				UI.showError(Message.getString("pcb.wo_start_not_select_mlot"));
				return;
			}

			// 检查所选批次和虚拟批次的物料是否一致
			if (!sapMLot.getMaterialName().equals(checkMlots.get(0).getMaterialName())) {
				UI.showError(Message.getString("cust.material_not_same"));
				return;
			}

			BigDecimal virtualQty = BigDecimal.ZERO;
			// 检查虚拟批次的总数量是否大于真实批次的数量
			BigDecimal totalQty = checkMlots.stream().map(MLot::getMainQty).reduce(BigDecimal.ZERO, BigDecimal::add);
			if (totalQty.compareTo(sapMLot.getMainQty()) == 1) {
				UI.showInfo(Message.getString("mm.split_parent_not_enough"));
				return;
			}
			PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
			for (MLot mLot : checkMlots) {
				virtualQty = virtualQty.add(mLot.getMainQty());

//				BeanUtils.copyProperties(mLot, sapMLot);

				mLot.setUpdatedBy(Env.getUserName());
				mLot.setUpdated(new Date());
				mLot.setTransferState(sapMLot.getTransferState());
				mLot.setGrade1(sapMLot.getGrade1());
				mLot.setGrade2(sapMLot.getGrade2());
				mLot.setRootMLotId(sapMLot.getRootMLotId());
				mLot.setPartnerCode(sapMLot.getPartnerCode());
				mLot.setPartnerOrder(sapMLot.getPartnerOrder());
				mLot.setPartnerMaterialId(sapMLot.getPartnerMaterialId());
				mLot.setPartnerLotId(sapMLot.getPartnerLotId());
				mLot.setInDate(sapMLot.getInDate());
				mLot.setOutDate(sapMLot.getOutDate());
				mLot.setDurable(sapMLot.getDurable());
				mLot.setSubMatType(sapMLot.getSubMatType());
				mLot.setReserved1(sapMLot.getReserved1());
				mLot.setReserved2(sapMLot.getReserved2());
				mLot.setReserved3(sapMLot.getReserved3());
				mLot.setReserved4(sapMLot.getReserved4());
				mLot.setReserved5(sapMLot.getReserved5());
				mLot.setReserved6(sapMLot.getReserved6());
				mLot.setReserved7(sapMLot.getReserved7());
				mLot.setReserved8(sapMLot.getReserved8());
				mLot.putUdfValue("reserved9", sapMLot.getUdfValue("reserved9"));
				mLot.putUdfValue("reserved10", sapMLot.getUdfValue("reserved10"));
				mLot.putUdfValue("reserved11", sapMLot.getUdfValue("reserved11"));
				mLot.putUdfValue("reserved12", sapMLot.getUdfValue("reserved12"));
				mLot.putUdfValue("reserved13", sapMLot.getUdfValue("reserved13"));
				mLot.putUdfValue("reserved14", sapMLot.getUdfValue("reserved14"));
				mLot.putUdfValue("reserved15", sapMLot.getUdfValue("reserved15"));
				mLot.putUdfValue("reserved16", sapMLot.getUdfValue("reserved16"));

				mLot.setLotComment(sapMLot.getmLotId());
				mLot.putUdfValue("reserved17", "Y");
				mLot.putUdfValue("isVirtual", "Y");

				pvcMLotManager.saveMLot(mLot);
				MLotHis mLotHis = new MLotHis(mLot, Env.getSessionContext());
				mLotHis.setTransType("ASSIGN");
				adManager.saveEntity(mLotHis, Env.getSessionContext());

			}
			sapMLot.putUdfValue("reserved17", "Y");
			sapMLot.putUdfValue("isVirtual", "N");
			sapMLot.setMainQty(sapMLot.getMainQty().subtract(virtualQty));
			pvcMLotManager.saveMLot(sapMLot);
			MLotHis sapMLotHis = new MLotHis(sapMLot, Env.getSessionContext());
			sapMLotHis.setTransType("ASSIGN");
			adManager.saveEntity(sapMLotHis, Env.getSessionContext());

			UI.showInfo(Message.getString("mm.assign_success"));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

}