package com.glory.mes.pvc.label;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.common.label.client.LabelManager;
import com.glory.common.label.model.Label;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.forms.EntityAttributeForm;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.model.BinBoxSet;
import com.glory.mes.pvc.model.BinBoxSetLine;

public class PvcLabelManagerEditor extends PvcGlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.label.PvcLabelManagerEditor";

	private EntityBlock entityBlock;
	private EntitySection entitySection;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		entityBlock = (EntityBlock) form.getBlock();
		entitySection = entityBlock.getCurrentPage();

		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);
	}

	private void saveAdapter(Object object) {
		try {
			entitySection.getMessageManager().setAutoUpdate(true);
			entitySection.getMessageManager().removeAllMessages();

			Label label = (Label) entitySection.getAdObject();
			if (label != null) {
				boolean saveFlag = true;
				for (IForm detailForm : entitySection.getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : entitySection.getDetailForms()) {
						if (detailForm instanceof EntityAttributeForm) {
							label.setAttributeValues(((EntityAttributeForm) detailForm).getAttributeValues());
						}
					}

					LabelManager labelManager = Framework.getService(LabelManager.class);
					if (label.getObjectRrn() == null) {
						Label existLabel = labelManager.getLabelByName(Env.getOrgRrn(), label.getName(), false, false);
						if (existLabel != null) {
							UI.showError(Message.getString("error.object_ishave"));
							return;
						}
					}

					ADBase obj = adManager.saveEntity(entitySection.getTable(), label, Env.getSessionContext());

					ADBase newBase = adManager.getEntity(obj);
					if (entitySection.getTable().isContainAttribute()) {
						newBase.setAttributeValues(adManager.getEntityAttributeValues(
								entitySection.getTable().getModelName(), newBase.getObjectRrn()));
					}
					entitySection.setAdObject(newBase);
					UI.showInfo(Message.getString("common.save_successed"));
					entitySection.refresh();
					entityBlock.refreshAdd(newBase);
					entityBlock.getCurrentPage().getMasterParent().refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			entitySection.getMessageManager().setAutoUpdate(false);
		}
	}

	private void deleteAdapter(Object object) {
		try {
			if (entitySection.getAdObject() != null && entitySection.getAdObject().getObjectRrn() != null) {
				if (!UI.showConfirm(Message.getString("common.confirm_delete"))) {
					return;
				}
				Label label = (Label) entitySection.getAdObject();

				List<Label> activceLabels = adManager.getEntityList(Env.getOrgRrn(), Label.class, Integer.MAX_VALUE,
						" name = '" + label.getName() + "' and state = 'ACTIVE'", "");
				if (CollectionUtils.isNotEmpty(activceLabels)) {
					// 有激活小标签使用这个父标签不能删除
					List<Label> smallLabels = adManager.getEntityList(Env.getOrgRrn(), Label.class, Integer.MAX_VALUE,
							" udf.parentLabel = '" + label.getName() + "' and state = 'ACTIVE'", "");
					if (smallLabels != null && smallLabels.size() > 0) {
						UI.showInfo(Message.getString("pvc.label_is_used_by_other_label"));
						return;
					}
					// 有激活方案使用这个标签不能删除
					List<BinBoxSet> binBoxSets = adManager.getEntityList(Env.getOrgRrn(), BinBoxSet.class,
							Integer.MAX_VALUE, " smallLabel = '" + label.getName() + "' and status = 'Active'", "");
					if (binBoxSets != null && binBoxSets.size() > 0) {
						UI.showInfo(Message.getString("pvc.label_is_used_by_bin_box_set"));
						return;
					}
					List<BinBoxSetLine> binBoxSetLines = adManager.getEntityList(Env.getOrgRrn(), BinBoxSetLine.class,
							Integer.MAX_VALUE, " smallLabel = '" + label.getName() + "'", "");
					if (binBoxSetLines != null && binBoxSetLines.size() > 0) {
						for (BinBoxSetLine binBoxSetLine : binBoxSetLines) {
							BinBoxSet boxSet = new BinBoxSet();
							boxSet.setObjectRrn(binBoxSetLine.getSetRrn());
							boxSet = (BinBoxSet) adManager.getEntity(boxSet);
							if (VersionControl.STATUS_ACTIVE.equals(boxSet.getStatus())) {
								UI.showInfo(Message.getString("pvc.label_is_used_by_bin_box_set_line"));
								return;
							}
						}
					}
				}
				adManager.deleteEntity(label, Env.getSessionContext());
				entitySection.setAdObject(new Label());
				entitySection.refresh();
				entityBlock.refreshDelete(label);
				UI.showInfo(Message.getString("common.delete_successed"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

}