package com.glory.mes.pvc.label.template;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.common.label.client.LabelManager;
import com.glory.common.label.model.LabelTemplate;
import com.glory.common.label.model.LabelTemplateVariable;
import com.glory.common.label.template.LabelTemplateVariableDialog;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.variable.model.ADVariable;
import com.glory.mes.pvc.PvcGlcEditor;
import com.google.common.collect.Lists;

public class PvcLabelTemplateEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.label.template.PvcLabelTemplateEditor";

	private static final String FIELD_TEMPLATEQUERY = "templateQuery";
	private static final String FIELD_TEMPLATEINFO = "templateInfo";
	private static final String FIELD_TEMPLATEVARIABLE = "templateVariable";

	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_APPROVE = "approve";
	private static final String BUTTON_UNAPPROVE = "unapprove";
	private static final String BUTTON_CLOSE = "close";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_ADD = "add";

	protected QueryFormField templateQueryField;
	protected GlcFormField templateInfoField;
	protected EntityFormField templateInfoField1;
	protected ListTableManagerField templateVariableField;
	
	private ToolItem itemNew;
	private ToolItem itemSave;
	private ToolItem itemApprove;
	private ToolItem itemUnApprove;
	private ToolItem itemClose;
	private ToolItem itemDelete;
	private ToolItem itemRefresh;
	
	private SquareButton itemAddVariable;
	private SquareButton itemDeleteVariable;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		templateQueryField = form.getFieldByControlId(FIELD_TEMPLATEQUERY, QueryFormField.class);
		templateInfoField = form.getFieldByControlId(FIELD_TEMPLATEINFO, GlcFormField.class);
		templateInfoField1 = templateInfoField.getFieldByControlId(FIELD_TEMPLATEINFO, EntityFormField.class);
		templateVariableField = templateInfoField.getFieldByControlId(FIELD_TEMPLATEVARIABLE, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, templateQueryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::templateQuerySelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_APPROVE), this::approveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNAPPROVE), this::unapproveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLOSE), this::closeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		subscribeAndExecute(eventBroker, templateInfoField.getFullTopic(BUTTON_ADD), this::addVariableAdapter);
		subscribeAndExecute(eventBroker, templateInfoField.getFullTopic(BUTTON_DELETE), this::deleteVariableAdapter);
		
		init();
	}

	private void init() {
		itemNew = (ToolItem) form.getButtonByControl(null, BUTTON_NEW);
		itemSave = (ToolItem) form.getButtonByControl(null, BUTTON_SAVE);
		itemApprove = (ToolItem) form.getButtonByControl(null, BUTTON_APPROVE);
		itemUnApprove = (ToolItem) form.getButtonByControl(null, BUTTON_UNAPPROVE);
		itemClose = (ToolItem) form.getButtonByControl(null, BUTTON_CLOSE);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
		itemRefresh = (ToolItem) form.getButtonByControl(null, BUTTON_REFRESH);
		itemAddVariable = (SquareButton) templateInfoField.getButtonByControl(BUTTON_ADD);
		itemDeleteVariable = (SquareButton) templateInfoField.getButtonByControl(BUTTON_DELETE);
		
		templateQueryField.getQueryForm().getTableManager().setAutoSizeFlag(true);
		templateQueryField.getQueryForm().getTableManager().refresh();
		statusChanged(null);
	}
	
	private void newAdapter(Object object) {
		try {
			LabelTemplate labelTemplate = new LabelTemplate();
			labelTemplate.setOrgRrn(Env.getOrgRrn());
			templateInfoField1.setValue(labelTemplate);
			templateInfoField1.refresh();
			((EntityForm) templateInfoField1.getControls()[0]).removeAllMessages();
			templateQueryField.refresh();
			templateVariableField.getListTableManager().setInput(Lists.newArrayList());
			statusChanged(labelTemplate.getState());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void saveAdapter(Object object) {
		EntityForm entityForm = (EntityForm) templateInfoField1.getControls()[0];
		try {
			if (templateInfoField1.validate()) {
				LabelTemplate oldTemplate = (LabelTemplate) templateInfoField1.getValue();
				
				List<? extends Object> objects = templateVariableField.getListTableManager().getInput();
				List<LabelTemplateVariable> templateVariables = objects.stream().map(o -> (LabelTemplateVariable)o).collect(Collectors.toList());
				oldTemplate.setLabelTemplateVariables(templateVariables);
				
				LabelManager labelManager = Framework.getService(LabelManager.class);
				LabelTemplate newTemplate = labelManager.saveLabelTemplate(oldTemplate, Env.getSessionContext());
				newTemplate = (LabelTemplate) adManager.getEntity(newTemplate);
				UI.showInfo(Message.getString("common.save_successed"));// 弹出提示框
				queryFormUpdate(oldTemplate, newTemplate);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			entityForm.getMessageManager().setAutoUpdate(false);
		}
	}

	private void approveAdapter(Object object) {
		try {
			LabelTemplate labelTemplate = (LabelTemplate) templateQueryField.getSelectedObject();
			if (labelTemplate == null) {
				UI.showWarning(Message.getString("common.select_object"));
				return;
			}
			
			LabelManager labelManager = Framework.getService(LabelManager.class);
			labelTemplate = labelManager.approveLabelTemplate(labelTemplate, Env.getSessionContext());
			UI.showInfo(Message.getString("common.approve_successed"));// 弹出提示框
			queryFormUpdate(labelTemplate, labelTemplate);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void unapproveAdapter(Object object) {
		try {
			LabelTemplate labelTemplate = (LabelTemplate) templateQueryField.getSelectedObject();
			if (labelTemplate == null) {
				UI.showWarning(Message.getString("common.select_object"));
				return;
			}			
			
			LabelManager labelManager = Framework.getService(LabelManager.class);
			labelTemplate = labelManager.unApproveLabelTemplate(labelTemplate, Env.getSessionContext());
			UI.showInfo(Message.getString("common.unapprove_successed"));// 弹出提示框
			queryFormUpdate(labelTemplate, labelTemplate);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void closeAdapter(Object object) {
		try {
			LabelTemplate labelTemplate = (LabelTemplate) templateQueryField.getSelectedObject();
			if (labelTemplate == null) {
				UI.showWarning(Message.getString("common.select_object"));
				return;
			}			
			
			LabelManager labelManager = Framework.getService(LabelManager.class);
			labelTemplate = labelManager.closeLabelTemplate(labelTemplate, Env.getSessionContext());
			UI.showInfo(Message.getString("common.close.sucess"));
			queryFormUpdate(labelTemplate, labelTemplate);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void deleteAdapter(Object object) {
		try {
			LabelTemplate labelTemplate = (LabelTemplate) templateQueryField.getSelectedObject();
			if (labelTemplate == null) {
				UI.showWarning(Message.getString("common.select_object"));
				return;
			}
			
			if (UI.showConfirm(Message.getString("common.comfirm_delete"))) {
				LabelManager labelManager = Framework.getService(LabelManager.class);
				labelManager.deleteLabelTemplate(labelTemplate, Env.getSessionContext());
				UI.showInfo(Message.getString("common.delete_successed"));
				templateQueryField.getQueryForm().getTableManager().remove(labelTemplate);
				templateQueryField.getQueryForm().getTableManager().setSelectedObject(null);
				newAdapter(object);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void refreshAdapter(Object object) {
		try {
			newAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void templateQuerySelectionChanged(Object object) {
		try {
			LabelTemplate labelTemplate = (LabelTemplate) templateQueryField.getSelectedObject();
			if (labelTemplate == null) {
				return;
			}
			LabelManager labelManager = Framework.getService(LabelManager.class);
			LabelTemplate newLabelTemplate = labelManager.getLabelTemplateByName(Env.getOrgRrn(), labelTemplate.getName(), true);
			templateInfoField1.setValue(newLabelTemplate);
			templateInfoField1.refresh();
			templateVariableField.getListTableManager().setInput(newLabelTemplate.getLabelTemplateVariables());
			statusChanged(newLabelTemplate.getState());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void addVariableAdapter(Object object) {
		try {
			LabelTemplateVariableDialog dialog = new LabelTemplateVariableDialog("LabelTemplateVariableDialog", form.getAuthority(), eventBroker);
			if (Dialog.OK == dialog.open()) {
				int i = 0;
				List<ADVariable> addVariable = dialog.getAdVariables();
				List<LabelTemplateVariable> templateVariables = Lists.newArrayList();
				
				List<? extends Object> objects = templateVariableField.getListTableManager().getInput();
				if (CollectionUtils.isNotEmpty(objects)) {
					List<LabelTemplateVariable> variables = objects.stream().map(o -> (LabelTemplateVariable)o).collect(Collectors.toList());
					templateVariables.addAll(variables);
					i = templateVariables.size();
					
//					List<ADVariable> passVariable = Lists.newArrayList();
//					List<String> existName = variables.stream().map(LabelTemplateVariable :: getVariableName).collect(Collectors.toList());
//					addVariable.forEach(variable -> {
//						if (!existName.contains(variable.getName())) {
//							passVariable.add(variable);
//						}
//					});
//					addVariable = passVariable;
				}
				
				for (ADVariable adVariable : addVariable) {
					LabelTemplateVariable labelTemplateVariable = new LabelTemplateVariable();
					labelTemplateVariable.setOrgRrn(Env.getOrgRrn());
					labelTemplateVariable.setVariableName(adVariable.getName());
					labelTemplateVariable.setVariableDesc(adVariable.getDescription());
					labelTemplateVariable.setSeqNo(Long.valueOf(i));
					templateVariables.add(labelTemplateVariable);
					i++;
				}
				templateVariableField.getListTableManager().setInput(templateVariables);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void deleteVariableAdapter(Object object) {
		try {
			List<? extends Object> objects = templateVariableField.getListTableManager().getInput();
			List<Object> deleteTemplateVariable = templateVariableField.getListTableManager().getCheckedObject();
			
			List<LabelTemplateVariable> inputTemplateVariable = objects.stream().map(o -> (LabelTemplateVariable)o).collect(Collectors.toList());
			for (Object o : deleteTemplateVariable) {
				LabelTemplateVariable variable = (LabelTemplateVariable) o;
				inputTemplateVariable.remove(variable);
			}
			
			int i = 0;
			for (LabelTemplateVariable variable : inputTemplateVariable) {
				variable.setSeqNo(Long.valueOf(i));
				i++;
			}
			templateVariableField.getListTableManager().setInput(inputTemplateVariable);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void queryFormUpdate(LabelTemplate oldTemplate, LabelTemplate newTemplate) {
		try {
			if (oldTemplate.getObjectRrn() != null) {
				templateQueryField.getQueryForm().getTableManager().update(newTemplate);
				templateQueryField.getQueryForm().getTableManager().setSelectedObject(newTemplate);
			} else {
				templateQueryField.getQueryForm().getTableManager().add(newTemplate);
				templateQueryField.getQueryForm().getTableManager().setSelectedObject(newTemplate);
			}
			templateInfoField1.setValue(newTemplate);
			templateInfoField1.refresh();
			statusChanged(newTemplate.getState());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void statusChanged(String newStatus) {
		if (LabelTemplate.STATUS_CREATED.equals(newStatus)) {
			itemNew.setEnabled(true);
			itemSave.setEnabled(true);
			itemApprove.setEnabled(true);
			itemUnApprove.setEnabled(false);
			itemClose.setEnabled(false);
			itemDelete.setEnabled(true);
			itemRefresh.setEnabled(true);
			itemAddVariable.setEnabled(true);
			itemDeleteVariable.setEnabled(true);
		} else if (LabelTemplate.STATUS_APPROVED.equals(newStatus)) {
			itemNew.setEnabled(true);
			itemSave.setEnabled(false);
			itemApprove.setEnabled(false);
			itemUnApprove.setEnabled(true);
			itemClose.setEnabled(true);
			itemDelete.setEnabled(false);
			itemRefresh.setEnabled(true);
			itemAddVariable.setEnabled(false);
			itemDeleteVariable.setEnabled(false);
		} else if (LabelTemplate.STATUS_CLOSED.equals(newStatus)) {
			itemNew.setEnabled(true);
			itemSave.setEnabled(false);
			itemApprove.setEnabled(false);
			itemUnApprove.setEnabled(false);
			itemClose.setEnabled(false);
			itemDelete.setEnabled(false);
			itemRefresh.setEnabled(true);
			itemAddVariable.setEnabled(false);
			itemDeleteVariable.setEnabled(false);
		} else {
			itemNew.setEnabled(true);
			itemSave.setEnabled(true);
			itemApprove.setEnabled(true);
			itemUnApprove.setEnabled(false);
			itemClose.setEnabled(false);
			itemDelete.setEnabled(true);
			itemRefresh.setEnabled(true);
			itemAddVariable.setEnabled(true);
			itemDeleteVariable.setEnabled(true);
		}
	}

}