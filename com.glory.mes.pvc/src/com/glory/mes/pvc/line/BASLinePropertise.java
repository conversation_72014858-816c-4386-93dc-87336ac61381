package com.glory.mes.pvc.line;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.Line;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcManager;
import com.glory.mes.pvc.model.LineChildren;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;

public class BASLinePropertise extends EntityProperties {
	public CheckBoxFixEditorTableManager manager;
	public static String TABLE_NAME = "PVCLineChildren";
	public SquareButton add;
	public SquareButton delete;

	@Override
	protected void createSectionContent(Composite client) {
		try {
			final FormToolkit toolkit = form.getToolkit();
			mmng = form.getMessageManager();
			createBasicSection(client);

			Section section = toolkit.createSection(client, Section.TITLE_BAR | Section.EXPANDED);
			section.setText("子线");
			GridData gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 10;
			section.setLayoutData(gd);
			section.setLayout(new GridLayout(1, true));

			Composite parameterComp = toolkit.createComposite(client, SWT.BORDER);
			GridLayout layout = new GridLayout(1, true);
			parameterComp.setLayout(layout);
			gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 400;
			parameterComp.setLayoutData(gd);
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			manager = new CheckBoxFixEditorTableManager(adTable);
			manager.newViewer(parameterComp);
			manager.setSortFlag(true);
			manager.addDoubleClickListener(new AbstractMouseSelectionAction() {
				public void run(NatTable natTable, MouseEvent event) {
					LineChildren object = (LineChildren) manager.getSelectedObject();
					ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "PVCLineChildrenEdit");
					EntityDialog dialog = new BASLineDialog(adTable, object);
					if (dialog.open() == IDialogConstants.OK_ID) {

					}
				}
			});

			Composite btnComposite = toolkit.createComposite(client, SWT.NONE);
			GridLayout layoutBtn = new GridLayout(4, false);
			btnComposite.setLayout(layoutBtn);
			GridData gd1 = new GridData(GridData.FILL_BOTH);
			gd1.horizontalAlignment = SWT.RIGHT;
			btnComposite.setLayoutData(gd1);
			add = UIControlsFactory.createButton(btnComposite, Message.getString("common.add"), null);
			add.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						addAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			delete = UIControlsFactory.createButton(btnComposite, Message.getString("common.delete"), null);
			delete.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						delAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();

				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
								detailForm.getCopyProperties());
					}
					Line line = (Line) getAdObject();
					List<LineChildren> lines = (List<LineChildren>) (List<? extends Object>) manager.getInput();
		        	List<LineChildren> allLines = new ArrayList<LineChildren>();
					Long seqNo = 0l;
					for (LineChildren linec : lines) {
						linec.setObjectRrn(null);
						linec.setOrgRrn(Env.getOrgRrn());
						allLines.add(linec);
						seqNo++;
					}
					PvcManager pvcManager = Framework.getService(PvcManager.class);				
					ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
					line = pvcManager.saveLine(line,allLines, Env.getSessionContext());
					UI.showInfo(Message.getString("common.save_successed"));// 弹出提示框
					setAdObject(adManager.getEntity(line));
					this.refresh();
					ADBase newBase = getAdObject();
					if (oldBase.getObjectRrn() == null) {
						getMasterParent().refreshAdd(newBase);
					} else {
						getMasterParent().refreshUpdate(newBase);
					}
				}
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected void addAdapter() {
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			EntityDialog dialog = new BASLineDialog(adTable, new LineChildren());
			if (dialog.open() == IDialogConstants.OK_ID) {
				LineChildren lineChildren = (LineChildren) dialog.getAdObject();
				List<LineChildren> allLines = new ArrayList<LineChildren>();
				List<LineChildren> lines = (List<LineChildren>) (List) manager.getInput();
				allLines.addAll(lines);
				allLines.add(lineChildren);
				manager.setInput(allLines);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings("unchecked")
	protected void delAdapter() {
		try {
			List<LineChildren> allLines = (List<LineChildren>) (List<? extends Object>) manager.getInput();
			List<LineChildren> lines = new ArrayList<LineChildren>();
			for (LineChildren allLine : allLines) {
				lines.add(allLine);
			}
			List<Object> os = manager.getCheckedObject();
			if (os.size() != 0) {
				for (Object o : os) {
					LineChildren line = (LineChildren) o;
					lines.remove(line);
				}
			}
			manager.setInput(lines);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	protected void deleteAdapter() {
		try {
			Line line = (Line) getAdObject();
			if (line != null && line.getObjectRrn() != null) {
				MBASManager mbasManager = Framework.getService(MBASManager.class);
				RASManager rasManager = Framework.getService(RASManager.class);
				List<ADUser> adUsers = mbasManager.getLineUsers(line.getObjectRrn());
				List<Equipment> equipments = rasManager.getLineEquipments(line.getObjectRrn());
				if (adUsers.size() > 0 || equipments.size() > 0) {
					UI.showConfirm(Message.getString("pvc.please_delete_sub_line"));
					return;
				}
				PvcManager pvcManager = Framework.getService(PvcManager.class);
				pvcManager.deleteLineChildren(line, Env.getSessionContext());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		super.deleteAdapter();	
	}

	@Override
	public void refresh() {
		super.refresh();
		if (getAdObject() != null) {
			for (IForm detailForm : getDetailForms()) {
				detailForm.setObject(getAdObject());
				detailForm.loadFromObject();
			}

			Line line = (Line) getAdObject();

			try {
				List<LineChildren> lineChildrens = null;
				if (line.getObjectRrn() != null) {
					ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
					lineChildrens = adManager.getEntityList(Env.getOrgRrn(), LineChildren.class, Integer.MAX_VALUE,
							" lineRrn = " + line.getObjectRrn(), "");
				} 
				if (lineChildrens != null && lineChildrens.size() > 0) {
					manager.setInput(lineChildrens);
				} else {
					manager.setInput(new ArrayList<LineChildren>());
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
		form.getMessageManager().removeAllMessages();
	}
}
