//package com.glory.mes.pvc.pp.reworkwo.start;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//
//import org.apache.commons.collections.CollectionUtils;
//import org.eclipse.swt.SWT;
//import org.eclipse.swt.events.SelectionAdapter;
//import org.eclipse.swt.events.SelectionEvent;
//import org.eclipse.swt.widgets.Composite;
//import org.eclipse.swt.widgets.ToolBar;
//import org.eclipse.swt.widgets.ToolItem;
//import org.eclipse.ui.forms.widgets.Section;
//
//import com.glory.framework.activeentity.client.ADManager;
//import com.glory.framework.activeentity.model.ADBase;
//import com.glory.framework.activeentity.model.ADTab;
//import com.glory.framework.base.entitymanager.forms.EntityAttributeForm;
//import com.glory.framework.base.entitymanager.forms.EntityForm;
//import com.glory.framework.base.model.Documentation;
//import com.glory.framework.base.ui.forms.IForm;
//import com.glory.framework.base.ui.util.Env;
//import com.glory.framework.base.ui.util.Message;
//import com.glory.framework.base.ui.util.SWTResourceCache;
//import com.glory.framework.base.ui.util.UI;
//import com.glory.framework.core.util.StringUtil;
//import com.glory.framework.runtime.Framework;
//import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
//import com.glory.framework.security.model.ADAuthority;
//import com.glory.mes.mm.client.MMManager;
//import com.glory.mes.mm.inv.model.Warehouse;
//import com.glory.mes.mm.lot.model.MLot;
//import com.glory.mes.mm.lot.model.MLotStorage;
//import com.glory.mes.pp.model.WorkOrder;
//import com.glory.mes.pp.model.WorkOrderLot;
//import com.glory.mes.pvc.client.PvcManager;
//import com.glory.mes.pvc.pp.wo.mr.PvcMaterialRequestDetailForm;
//import com.glory.mes.wip.pp.wo.WorkOrderProperties;
//import com.glory.mes.wip.pp.wo.mr.MaterialRequestDetailForm;
//
//public class PvcReworkWoStartProperties extends WorkOrderProperties {
//	
//	private static final String KEY_REWORK_PICK = "REWORKPICK";
//    
//	public static String TAB_REWORK_LOT = "PPWorkOrderReworkLot";
//	
//	public static String TAB_REWORK_MR = "PPWorkOrderReworkMR";
//	
//	protected ToolItem itemAssign;
//	protected ToolItem itemDeassign;	
//	protected ToolItem itemStart;
//	protected ToolItem itemPicking;
//	
//	public PvcReworkWorkOrderLotGenerateForm workOrderLotForm;
//	
//	public MaterialRequestDetailForm detailForm;
//
//	public PvcReworkWoStartProperties() {
//		super();
//	}
//	
//	@Override
//	protected IForm getForm(Composite composite, ADTab tab) {
//		if (tab.getName().equals(EntityAttributeForm.NAME)) {
//			return new EntityAttributeForm(composite, SWT.NONE, table.getModelName(), this.getAdObject(), tab.getGridY().intValue(), mmng);
//		} else if (tab.getName().equals(TAB_REWORK_LOT)) {
//			workOrderLotForm = new PvcReworkWorkOrderLotGenerateForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
//			return workOrderLotForm;
//		}  else if (tab.getName().equals(TAB_REWORK_MR)) {
//			detailForm = new PvcMaterialRequestDetailForm(composite, SWT.NONE, tab, mmng);
//			return detailForm;
//		}  
//
//		EntityForm entityFrom = new EntityForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
//		entityFrom.setADManager(getADManger());
//		return entityFrom;
//	}
//	
//	@Override
//	public void createToolBar(Section section) {
//		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
//		createToolItemPicking(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemStart(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemRefresh(tBar);
//		section.setTextClient(tBar);
//	}
//	
//	//领料
//	protected void createToolItemPicking(ToolBar tBar) {
//		try {
//			ADManager adManager = Framework.getService(ADManager.class);
//			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), 
//					ADAuthority.class, 1, " name = '" + getTable().getAuthorityKey() + "." + KEY_REWORK_PICK + "'", "");
//			if (authorities.isEmpty()) {
//				return;
//			}
//			itemPicking = new ToolItem(tBar, SWT.PUSH);
//			itemPicking.setText(Message.getString("mm.mr_picking"));
//			itemPicking.setImage(SWTResourceCache.getImage("receive"));
//			itemPicking.addSelectionListener(new SelectionAdapter() {
//				@Override
//				public void widgetSelected(SelectionEvent event) {
//					pickingAdapter();
//				}
//			});
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//	        return;
//		}
//	}
//
//	protected void createToolItemStart(ToolBar tBar) {
//		itemStart = new ToolItem(tBar, SWT.PUSH);
//		itemStart.setText(Message.getString("common.start"));
//		itemStart.setImage(SWTResourceCache.getImage("newlot_start"));
//		itemStart.setEnabled(false);
//		itemStart.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				startAdapter();
//			}
//		});
//	}
//	
//	protected void pickingAdapter() {
//		try {
//			WorkOrder workOrder = (WorkOrder) getAdObject();
//			if (workOrder.getObjectRrn() != null) {
//				PvcManager pvcManager = Framework.getService(PvcManager.class);
//				String needQty = workOrderLotForm.getQty();
//				String regex = "^\\+?[1-9][0-9]*$";
//				Pattern p = Pattern.compile(regex);
//				Matcher m = p.matcher(needQty);
//				if (!m.find()) {
//					UI.showError(Message.getString("edc.edcset_attribute_format"));
//					return;
//				}
//				BigDecimal qty = new BigDecimal(needQty);
//				/*if (qty.compareTo(workOrder.getMainQty()) > 0) {
//					UI.showError(Message.getString("pvc.pickqty_can_not_more_workqty"));
//					return;
//				}*/
//				pvcManager.generateReworkWorkOrderMaterialRequisition(workOrder, qty, Env.getSessionContext());
//				UI.showInfo(Message.getString("common.operation_successed"));
//				refresh();
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//	
//	protected void startAdapter() {
//		try {
//			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
//				if (workOrderLotForm.saveToObject()) {
//					/*String warehouseId = workOrderLotForm.getWarehouseId();
//					if (StringUtil.isEmpty(warehouseId)) {
//						//error.no_warehouse_input
//						UI.showWarning(Message.getString("error.no_warehouse_input"));
//					} else {*/
//						WorkOrder workOrder = (WorkOrder) getAdObject();
//						if (CollectionUtils.isNotEmpty(workOrder.getWorkOrderLots())) {
//							PvcManager pvcManager = Framework.getService(PvcManager.class);
//							MMManager mmManger = Framework.getService(MMManager.class);
//							
//							/*Warehouse warehouse = new Warehouse();
//							warehouse.setOrgRrn(Env.getSessionContext().getOrgRrn());
//							warehouse.setWarehouseId(warehouseId);
//							warehouse = mmManger.getWarehouse(warehouse);
//							
//							List<MLotStorage> mLotStorages = getADManger().getEntityList(
//									Env.getSessionContext().getOrgRrn(), MLotStorage.class, Integer.MAX_VALUE,
//									" warehouseRrn = " + warehouse.getObjectRrn(), null);
//							
//							MLot defectMLot = null;
//							for (MLotStorage mLotStorage : mLotStorages) {
//								List<MLot> mLots = getADManger().getEntityList(
//									Env.getSessionContext().getOrgRrn(), MLot.class, Integer.MAX_VALUE,
//									" objectRrn = " + mLotStorage.getMLotRrn(), null);
//								
//								if (!CollectionUtils.isEmpty(mLots) && "REWORK_MATERIAL_LOT".equals(mLots.get(0).getmLotId())) {
//									defectMLot = mLots.get(0);
//									break;
//								}
//							}
//							if (defectMLot ==null) {
//								//仓库中无不良品物料批次
//								UI.showWarning(Message.getString("error.no_defect_mlot"));
//								return;
//							}*/
//							String mLotId="*R_"+workOrder.getPartName();
//							MLot defectMLot = mmManger.getMLotByMLotId(Env.getOrgRrn(), mLotId);
//							List<MLot> defectMLots = new ArrayList<MLot>();
//							defectMLots.add(defectMLot);
//							
//							List<WorkOrderLot> orderLots = workOrderLotForm.getCheckedStartLots();
//							//List<WorkOrderLot> orderLots = workOrder.getWorkOrderLots();
//							orderLots = orderLots.stream().filter(
//									w -> WorkOrderLot.STATUS_SCHEDULE.equals(w.getState())).collect(Collectors.toList());
//							pvcManager.startReworkLot(workOrder, orderLots, defectMLots, null, Env.getSessionContext());
//							
//							UI.showInfo(Message.getString("common.operation_successed"));
//							ADBase adBase = getADManger().getEntity(getAdObject());
//							setAdObject(adBase);
//							refresh();
//							getMasterParent().refreshUpdate(adBase);
//						} else {
//							UI.showWarning(Message.getString("wip_not_select_lot"));
//						}
//					}
//				}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//			return;
//		}
//	}
//	
//	@Override
//	public void statusChanged(String newStatus, String holdStatus) {
//		if (newStatus == null || "".equals(newStatus.trim())) {
//			itemStart.setEnabled(false);
//		} else if (Documentation.STATUS_APPROVED.equals(newStatus.trim())
//				|| WorkOrder.STATUS_STARTED.equals(newStatus.trim())) {
//			itemStart.setEnabled(true);
//			checkHoldStatus();
//		} else {
//			itemStart.setEnabled(false);
//		}
//	}
//
//	public void checkHoldStatus() {
//		try {
//			WorkOrder workOrder = (WorkOrder) getAdObject();
//			if (workOrder == null || workOrder.getObjectRrn() == null) {
//				return;
//			}
//			if (WorkOrder.HOLDSTATE_ON.equals(workOrder.getHoldState())) {
//				itemStart.setEnabled(false);
//			}
//		} catch (Exception e1) {
//			ExceptionHandlerManager.asyncHandleException(e1);
//		}
//	}
//	
//}