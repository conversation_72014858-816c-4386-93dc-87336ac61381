package com.glory.mes.pvc.pp.reworkwo.start;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IFormDataChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.wip.pp.wo.form.WorkOrderLotGenerateForm;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 生成工单批次
 */
public class PvcReworkWorkOrderLotGenerateForm extends WorkOrderLotGenerateForm {
	
	protected Text durableIdText;
	protected Label qtyLabel;
	protected Text count;
	protected RefTableField warehouseField;

	public PvcReworkWorkOrderLotGenerateForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}
	
	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		Composite top = toolkit.createComposite(body);
		top.setLayout(new GridLayout(12, false));
		GridData gd = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		top.setLayoutData(gd);
		
		/*Composite buttom = toolkit.createComposite(body);
		buttom.setLayout(new GridLayout(12, false));
		GridData gdButtom = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		buttom.setLayoutData(gdButtom);

		GridData gText = new GridData(GridData.FILL_HORIZONTAL);
		gText.widthHint = 100;*/
		try {
			
//			toolkit.createLabel(top, Message.getString("common.lotqty"));
//			txtLotSize = toolkit.createText(top, "", SWT.BORDER);
//			txtLotSize.setLayoutData(gText);
//			txtLotSize.setTextLimit(32);
//			txtLotSize.setText("100");
//
//			gText = new GridData(GridData.FILL_HORIZONTAL);
//			gText.widthHint = 60;
//			toolkit.createLabel(top, Message.getString("common.lot_number"));
//			txtLotCount = toolkit.createText(top, "", SWT.BORDER);
//			txtLotCount.setLayoutData(gText);
//			txtLotCount.setTextLimit(32);
//			txtLotCount.setText("1");
//
//			gText = new GridData(GridData.FILL_HORIZONTAL);
//			gText.widthHint = 60;
//			toolkit.createLabel(top, Message.getString("wip.lot_id"));
//			lotIdText = toolkit.createText(top, "", SWT.BORDER);
//			lotIdText.setLayoutData(gText);
//			lotIdText.setTextLimit(32);
//
//			lotIdText.addVerifyListener(new VerifyListener() {
//				public void verifyText(VerifyEvent e) {
//					// 正则表达式验证
//					Pattern pattern = Pattern.compile(regEx);
//					Matcher matcher = pattern.matcher(e.text);
//					if (matcher.matches()) {
//						// 允许大小写字母、中划线、下划线、数字
//						e.doit = true;
//					} else if (e.text.length() > 0) {
//						// 其它情况屏蔽
//						e.doit = false;
//					} else {
//						// 控制键
//						e.doit = true;
//					}
//				}
//			});
//			
//			toolkit.createLabel(top, Message.getString("wip.durable"));
//			durableIdText = toolkit.createText(top, "", SWT.BORDER);
//			durableIdText.setLayoutData(gText);
//			durableIdText.setTextLimit(32);
//
//			durableIdText.addVerifyListener(new VerifyListener() {
//				public void verifyText(VerifyEvent e) {
//					// 正则表达式验证
//					Pattern pattern = Pattern.compile(regEx);
//					Matcher matcher = pattern.matcher(e.text);
//					if (matcher.matches()) {
//						// 允许大小写字母、中划线、下划线、数字
//						e.doit = true;
//					} else if (e.text.length() > 0) {
//						// 其它情况屏蔽
//						e.doit = false;
//					} else {
//						// 控制键
//						e.doit = true;
//					}
//				}
//			});
			
			/*toolkit.createLabel(top, Message.getString("mm.target_warehouse"));
			warehouseField = RCPUtil.createRefTableField(top, null, "MMWarehouseNameList", "", true);
	        gText = new GridData();
	        gText.widthHint = 200;
	        gText.heightHint = 26;
	        warehouseField.getComboControl().setLayoutData(gText);*/
	        
/*	        SquareButton addLotBtn = UIControlsFactory.createButton(buttom, UIControlsFactory.BUTTON_DEFAULT);
			addLotBtn.setText(Message.getString("common.add"));
			addLotBtn.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
			addLotBtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					addAdaptor();
				}
			});

			SquareButton removeLotbtn = UIControlsFactory.createButton(buttom, UIControlsFactory.BUTTON_DEFAULT);
			removeLotbtn.setText(Message.getString("common.delete"));
			removeLotbtn.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
			removeLotbtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					removeAdaptor();
				}
			});*/
			
	
			
		/*	qtyLabel = toolkit.createLabel(qtyCom, String.format(Message.getString("pcb.wo_remaining_quantity"), "0"));
	        qtyLabel.setLayoutData(gText);
			
			Composite tableCom = toolkit.createComposite(body);
			tableCom.setLayout(new GridLayout(1, false));
			GridData tablegd = new GridData(GridData.FILL_BOTH);
			tableCom.setLayoutData(tablegd);*/
			
			GridData gText = new GridData();
	        gText.widthHint = 200;
	        gText.heightHint = 26;
			
			toolkit.createLabel(top, Message.getString("pvc.pick_qty"));
			count = toolkit.createText(top, "", SWT.BORDER);
			count.setLayoutData(gText);
			count.setTextLimit(10);
			
			Composite lineComposite = toolkit.createComposite(body);
			GridData lineGridData = new GridData(GridData.FILL_BOTH);
			lineGridData.grabExcessHorizontalSpace = true;
			lineGridData.heightHint = 200;
			lineComposite.setLayoutData(lineGridData);

			ADManager adManager = Framework.getService(ADManager.class);
			lotAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotTableManager = new ListTableManager(lotAdTable, true);
			lotTableManager.setIndexFlag(true);
			lotTableManager.newViewer(lineComposite);
			
			/*this.addFormDataChanged(new IFormDataChangeListener() {
				
				@Override
				public void dataChanged(Object paramObject1, Object paramObject2) {
					if (paramObject1 instanceof PvcReworkWorkOrderLotGenerateForm && paramObject2 instanceof BigDecimal) {
						leftAdaptor((BigDecimal) paramObject2);
					}
				}
			});*/

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void addAdaptor() {
		try {
			if (lotTableManager.getInput() != null) {
				for (WorkOrderLot woLot : ((List<WorkOrderLot>) lotTableManager.getInput())) {
					if (lotIdText.getText().equalsIgnoreCase(woLot.getLotId())) {
						UI.showError(Message.getString("wip.lotid_repeat"));
						return;
					}
				}
			}
			
			String durableId = durableIdText.getText();
			if (StringUtil.isEmpty(durableId)) {
				UI.showError(Message.getString("common.select_durable"));
				return;
			}
			List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
			if (lotTableManager.getInput() != null) {
				workOrderLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
			}
			long lotNumber = Long.parseLong(txtLotCount.getText());
			BigDecimal mainQty = new BigDecimal(txtLotSize.getText());
			for (int i = 0; i < lotNumber; i++) {
				WorkOrderLot workOrderLot = new WorkOrderLot();
				workOrderLot.setOrgRrn(Env.getOrgRrn());
				workOrderLot.setIsActive(true);
				if (lotIdText.getText() != null && !"".equals(lotIdText.getText())) {
					String lotId = lotIdText.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					workOrderLot.setLotId(lotId);
				}
				workOrderLot.setDurableId(durableId);
				workOrderLot.setMainQty(mainQty);
				
				Map<String, String> dynamicComponent = Maps.newHashMap();
				workOrderLot.setUdf(dynamicComponent);
				workOrderLots.add(workOrderLot);
			}
			
			lotTableManager.setInput(workOrderLots);
			lotQtyChange();
		} catch (Exception e) {
			e.printStackTrace();
			UI.showError(Message.getString("common.invalid_number"));
		}
	}
	
	public void removeAdaptor() {
		List<Object> removeWLots = lotTableManager.getCheckedObject();
		// 检查是否已经投料
		Optional<Object> f = removeWLots.stream().filter(o 
				-> !StringUtil.isEmpty(((WorkOrderLot)o).getLotId())).findFirst();
		if (f.isPresent()) {
			UI.showError(Message.getString("pcb.wo_lot_started_cannot_remove"));
			return;
		}
		
		for (Object removeWoLot : removeWLots) {
			WorkOrderLot pre = (WorkOrderLot) removeWoLot;
			((List<WorkOrderLot>) lotTableManager.getInput()).remove(pre);
		}
		
		lotQtyChange();
	}
	
	public void leftAdaptor(BigDecimal currentQty) {
		WorkOrder workOrder = (WorkOrder) object;
		if (workOrder == null || workOrder.getObjectRrn() == null) {
			return;
		}
		BigDecimal leftQty = workOrder.getMainQty().subtract(currentQty);
		qtyLabel.setText(String.format(Message.getString("pcb.wo_remaining_quantity"), leftQty));
		if (leftQty.compareTo(BigDecimal.ZERO) < 0) {
			qtyLabel.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_RED));
		} else {
			qtyLabel.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		}
	}
	
	@Override
	public boolean saveToObject() {
		if (object != null) {
			WorkOrder workOrder = (WorkOrder) object;
			//if (workOrder.getObjectRrn() != null) {
				if (!validate()) {
					return false;
				}
				List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
				if (lotTableManager.getInput() != null) {
					startLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
				}

				if (workOrder.getMainQty() == null) {
					return false;
				}
				// 检查工单数量和批次总数量是否一致
				if (getLotQty().compareTo(workOrder.getMainQty()) > 0) {
					UI.showError(Message.getString("pcb.wo_lot_qty_greater_than_wo_qty"));
					return false;
				}
				workOrder.setWorkOrderLots(startLots);
			//}
		}
		return true;
	}
	
	@Override
	public void loadFromObject() {
		count.setText("");
		lotTableManager.getInput().clear();
		if (object != null) {
			try {
				WorkOrder workOrder = (WorkOrder) object;
				List<WorkOrderLot> workOrderLots = workOrder.getWorkOrderLots();
				if (workOrderLots == null) {
					ADManager adManager = Framework.getService(ADManager.class);
					workOrderLots = adManager.getEntityList(Env.getOrgRrn(),
		                    WorkOrderLot.class, Env.getMaxResult(), " workOrderRrn = " + workOrder.getObjectRrn(), null);
				}
				lotTableManager.setInput(workOrderLots);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	public String getWarehouseId() {
		return warehouseField.getText();
	}
	
	public String getQty() {
		return count.getText();
	}

	public List<WorkOrderLot> getCheckedStartLots() {
		return (List) Lists.newArrayList(lotTableManager.getCheckedObject());
	}
}
