package com.glory.mes.pvc.pp.wo.start;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.query.SearchDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.pp.wo.WorkOrderMLotTableSelectField;

public class PvcWoMLotTableSelectField extends WorkOrderMLotTableSelectField {

	private static final String TABLE_NAME = "PPWorkOrderMLotSelectDialog";
	private List<String> mLotIds = null;

	public PvcWoMLotTableSelectField(String id, ListTableManager tableManager, String whereClause) {
		super(id, tableManager, whereClause);
	}

	/**
	 * 获得最大用量
	 */
	public BigDecimal getMaxQty(List<MaterialRequisitionLine> materialRequisitionLines, MLot mLot) {
		for (MaterialRequisitionLine materialRequisitionLine : materialRequisitionLines) {
			if (materialRequisitionLine.getMaterialName().equals(mLot.getMaterialName())) {
				return getGenerationLotQty();
			}
		}
		return BigDecimal.ZERO;
	}

	@SuppressWarnings("unchecked")
	public void add() {
		try {
			if (BigDecimal.ZERO.compareTo(getGenerationLotQty()) == 0) {
				UI.showInfo(Message.getString("wip.wo_add_lot"));
				return;
			}
			if (getMaterialRequisitionLines() == null) {
				UI.showInfo(Message.getString("wip.wo_create_bom"));
				return;
			}
			if (mLotIds == null || mLotIds.size() < 1) {
				UI.showInfo(Message.getString("bom.material_not_found"));
				return;
			}

			List<ADBase> values = (List<ADBase>) getValue() == null ? new ArrayList<ADBase>()
					: (List<ADBase>) getValue();
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ListTableManager listTableManager = new ListEditorTableManager(adTable, true);

			SearchDialog searchDialog = new PvcWorkOrderMLotSearchDialog(listTableManager, adTable.getInitWhereClause(),
					whereClause, mLotIds);

			if (searchDialog.open() == 0) {
				List<ADBase> adBases = searchDialog.getSelectionItems();
				for (ADBase adBase : adBases) {
					MLot mLot = (MLot) adBase;

					if (isAddMLot(values, mLot)) {
						if (!isVerQty()) {
							mLot.setTransMainQty(getMaxQty(getMaterialRequisitionLines(), mLot));
						} else {
							// 最大用量
							BigDecimal maxQty = getMaxQty(getMaterialRequisitionLines(), mLot);
							// 现有用量
							BigDecimal hasQtye = getHasQty(values, mLot);
							BigDecimal subQty = maxQty.subtract(hasQtye);
							if (subQty.compareTo(BigDecimal.ZERO) == 0) {
								UI.showInfo(mLot.getMaterialName() + Message.getString("wip.wo_lot_qty_enough"));
								break;
							}
							if (mLot.getMainQty().compareTo(subQty) == -1) {
								mLot.setTransMainQty(mLot.getMainQty());
							} else {
								mLot.setTransMainQty(maxQty.subtract(hasQtye));
							}
						}
						values.add(mLot);
						this.setValue(values);
					} else {
						UI.showInfo(mLot.getmLotId() + Message.getString("wip.wo_lotid_repeat"));
						break;
					}
				}
				this.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public List<String> getmLotIds() {
		return mLotIds;
	}

	public void setmLotIds(List<String> mLotIds) {
		this.mLotIds = mLotIds;
	}

}
