package com.glory.mes.pvc.pp.wo.form;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.wip.pp.wo.form.WorkOrderLotForm;
import com.google.common.collect.Lists;

/**
 * 显示工单所对应的批次信息 勾选需要投料的批次
 */
public class PvcWorkOrderLotForm extends WorkOrderLotForm {

	public PvcWorkOrderLotForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);

		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListTableManager(adTable, true);

			CheckBoxTableViewerManager manager = (CheckBoxTableViewerManager) tableManager.getTableManager();
			manager.setCheckBoxDisableListener(new ICheckBoxDisableListener() {
				public boolean isDisable(Object object) {
					WorkOrderLot lot = (WorkOrderLot) object;
					if (WorkOrderLot.STATUS_STARTED.equals(lot.getState())) {
						return true;
					}
					return false;
				}
			});

			tableManager.setIndexFlag(true);
			tableManager.newViewer(body);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null && tableManager != null) {
			tableManager.setInput(buildWorkOrderLots());
			for (Object obj : tableManager.getInput()) {
				WorkOrderLot lot = (WorkOrderLot) obj;
				if (StringUtil.isEmpty(lot.getLotId())) {
					tableManager.setCheckedObject(obj);
				}
			}
			tableManager.refresh();
		}
	}

	@Override
	protected List<WorkOrderLot> buildWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<>();
		WorkOrder workOrder = (WorkOrder) object;
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			workOrderLots = adManager
					.getEntityList(
							Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(), " workOrderRrn = "
									+ workOrder.getObjectRrn() + " and state = '" + WorkOrderLot.STATUS_SCHEDULE + "'",
							null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return workOrderLots;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public List<WorkOrderLot> getCheckedStartLots() {
		return (List) Lists.newArrayList(tableManager.getCheckedObject());
	}

}
