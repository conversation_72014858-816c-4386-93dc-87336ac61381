package com.glory.mes.pvc.pp.wo.mlotquery;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.query.CalendarFromToField;
import com.glory.framework.base.ui.forms.field.query.DateTimeFromToField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcMLotManager;
import com.glory.mes.pvc.model.wip.WorkOrderMLotHis;
import com.glory.mes.wip.mm.MaterialRequisitionDetail;

public class WoMlotQuerySection extends QueryEntityListSection {

	private static final Logger logger = Logger.getLogger(WoMlotQuerySection.class);

	protected ListTableManager receiveTableManager;

	protected ListTableManager startTableManager;

	public ADBase prestoreObject;
	public String eventId;

	public WoMlotQuerySection(ListTableManager listTableManager) {
		super(listTableManager);
	}

	@Override
	public void createContents(IManagedForm form, Composite client, int sectionStyle) {
		super.createContents(form, client, sectionStyle);
		try {
			FormToolkit toolkit = form.getToolkit();
			Composite content = toolkit.createComposite(client);
			content.setLayout(new GridLayout(2, true));
			content.setLayoutData(new GridData(GridData.FILL_BOTH));
			// 领料历史
			Section receiveSection = toolkit.createSection(content, Section.TITLE_BAR | Section.EXPANDED);
			receiveSection.setText(Message.getString("pvc.mlot_req_his"));
			GridData gd = new GridData(GridData.FILL_BOTH);
			receiveSection.setLayoutData(gd);
			receiveSection.setLayout(new GridLayout(1, true));
			Composite parameterComp = toolkit.createComposite(receiveSection, SWT.NONE);
			GridLayout layout = new GridLayout(1, true);
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			parameterComp.setLayout(layout);
			gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 190;
			parameterComp.setLayoutData(gd);
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			final ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "PvcWoMLotReceiveHis");
			receiveTableManager = new ListTableManager(adTable);
			receiveTableManager.setIndexFlag(true);
			receiveTableManager.newViewer(parameterComp);
			receiveSection.setClient(parameterComp);

			// 发料历史
			Section startSection = toolkit.createSection(content, Section.TITLE_BAR | Section.EXPANDED);
			startSection.setText(Message.getString("pvc.mlot_start_his"));
			GridData gd1 = new GridData(GridData.FILL_BOTH);
			startSection.setLayoutData(gd1);
			startSection.setLayout(new GridLayout(1, true));
			Composite parameterComp1 = toolkit.createComposite(startSection, SWT.NONE);
			GridLayout layout1 = new GridLayout(1, true);
			layout1.marginHeight = 0;
			layout1.marginWidth = 0;
			parameterComp1.setLayout(layout1);
			gd1 = new GridData(GridData.FILL_BOTH);
			gd1.heightHint = 190;
			parameterComp1.setLayoutData(gd1);
			final ADTable adTable2 = adManager.getADTable(Env.getOrgRrn(), "PvcWoMLotHis");
			startTableManager = new ListTableManager(adTable2);
			startTableManager.setIndexFlag(true);
			startTableManager.newViewer(parameterComp1);
			startSection.setClient(parameterComp1);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void queryAdapter() {
		try {
			Map<String, String> whereClauseMap = new HashMap<String, String>();
			Date from = null;
			Date to = null;
			List<ADField> listFields = tableManager.getADTable().getFields();
			LinkedHashMap fields = getQueryForm().getFields();
			for (ADField adField : listFields) {
				if (adField.getIsQuery()) {
					for (Object f : fields.values()) {
						IField field = (IField) f;
						Object t = field.getValue();
						if (f instanceof CalendarFromToField || f instanceof DateTimeFromToField) {
							Map m = (Map) t;
							from = (Date) m.get("from");
							to = (Date) m.get("to");
						} else {

							if (t != null) {
								if (!"".equals(t.toString())) {
									if (field.getId().equals(adField.getName())) {
										whereClauseMap.put(adField.getName(), t.toString());
									}
								}
							}

						}
					}
				}
			}
			String woId = whereClauseMap.get("reserved1");
			Map<String, Object> parmaterMap = new HashMap<String, Object>();
			StringBuffer whereClause = new StringBuffer();
			whereClause.append("woId ='" + woId + "' ");
			if (from != null) {
				if (to == null) {
					parmaterMap.put("from", from);
					whereClause.append(" AND ");
					whereClause.append("transTime ");
					whereClause.append(" >= ");
					whereClause.append(":from ");
				} else {
					parmaterMap.put("from", from);
					to = DateUtils.addDays(to, 1);
					parmaterMap.put("to", to);
					whereClause.append(" AND ");
					whereClause.append("transTime ");
					whereClause.append(" >= ");
					whereClause.append(":from ");
					whereClause.append(" AND ");
					whereClause.append("transTime ");
					whereClause.append(" < ");
					whereClause.append(":to ");
				}

			} else if (to != null) {
				to = DateUtils.addDays(to, 1);
				parmaterMap.put("to", to);
				whereClause.append(" AND ");
				whereClause.append("transTime ");
				whereClause.append(" < ");
				whereClause.append(":to ");
			}

			PpManager ppManager = Framework.getService(PpManager.class);
			MMManager mmManager = Framework.getService(MMManager.class);
			PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
			// 获取工单
			WorkOrder workOrder = new WorkOrder();
			workOrder.setDocId(woId);
			workOrder = ppManager.getWorkOrder(workOrder, Env.getSessionContext());

			// 获取工单领到的物料批
			List<MLot> mlots = new ArrayList<MLot>();
			List<MaterialRequisitionDetail> details = ppManager.getMaterialRequisitionDetails(workOrder,
					Env.getSessionContext());
			if (details.size() > 0) {

				List<String> mlotIds = details.stream().map(x -> x.getmLotId()).collect(Collectors.toList());
				for (String mlotId : mlotIds) {
					MLot mlot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mlotId, true);
					mlots.add(mlot);
				}
				mlots = pvcMLotManager.getMLotStorageByMLots(Env.getOrgRrn(), mlots);

				// 获取物料批历史
				List<WorkOrderMLotHis> allHis = new ArrayList<WorkOrderMLotHis>();
				// 领料历史
				List<WorkOrderMLotHis> pickhis = new ArrayList<WorkOrderMLotHis>();
				// 发料历史
				List<WorkOrderMLotHis> starthis = new ArrayList<WorkOrderMLotHis>();
				ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
				allHis = adManager.getEntityList(Env.getOrgRrn(), WorkOrderMLotHis.class, 0, Integer.MAX_VALUE,
						whereClause.toString(), "transTime", parmaterMap);
				pickhis = allHis.stream().filter(o -> o.getTransType().equals(WorkOrderMLotHis.TRANS_TYPE_PICK))
						.collect(Collectors.toList());
				starthis = allHis.stream().filter(o -> o.getTransType().equals(WorkOrderMLotHis.TRANS_TYPE_START))
						.collect(Collectors.toList());

				tableManager.setInput(mlots);
				receiveTableManager.setInput(pickhis);
				startTableManager.setInput(starthis);
			}
		} catch (Exception e) {
			logger.error("Error SotckTakingQueryForm : refresh() " + e.getMessage(), e);
		}
	}

}
