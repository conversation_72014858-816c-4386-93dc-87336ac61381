package com.glory.mes.pvc.pp.wo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.GlcUtil;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomContext;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomDialog;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomWizard;

public class PvcWorkOrderGlcEditor extends PvcGlcEditor{
	
	public static final Logger logger = Logger.getLogger(PvcWorkOrderGlcEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.pp.wo.PvcWorkOrderGlcEditor";
	
	private static final String FIELD_LOTLIST = "lotList";
	private static final String FIELD_MATERIALIST = "materialList";
	
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_UNAPPROVE = "unApprove";
	private static final String BUTTON_HOLD = "hold";
	private static final String BUTTON_BOM = "bom";
	private static final String BUTTON_CLOSE = "close";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_REFRESH = "entityRefresh";
	public static final String BUTTON_NAME_ADD = "add"; 
	public static final String BUTTON_NAME_DELETE = "delete"; 
	
	private static final String FORM_BASIC_INFO = "BasicInfo";
	private static final String FORM_LOT_ID = "lotId";
	private static final String FORM_LOT_QTY = "lotQty";
	private static final String FORM_LOT_NUMBER = "lotNumber";
	private static final String FORM_PARTFLOW = "partFlow";
	private static final String FIELD_PARTVERSION = "partVersion";
	private static final String FIELD_PARTNAME = "partName";
	private static final String FIELD_REWORKPROCESSNAME = "reworkProcessName";
	private static final String FIELD_ISLOCKVERSION = "isLockVersion";
	
	private ListTableManagerField lotListField;
	private ListTableManagerField materialListField;
	private CustomField flowTreeCustomField;
	//private FlowCustomComposite flowCustomComposite;
	
	private EntityBlock block;
	private EntityForm entityForm;
	
	private TextField lotIdField;
	private TextField lotQtyField;
	private TextField lotNumberQtyField;
	private SearchField partNameField;
	private RefTableField partVersionField;
	private RefTableField reworkProcessNameField;
	
	private ToolItem itemSave;
	private ToolItem itemApprove;
	private ToolItem itemHold;
	private ToolItem itemBom;
	private ToolItem itemClose;
	private ToolItem itemDelete;
	
	private Part part = null;
	
	public boolean isVerQty = true;
	
	public boolean isitemBom = true;
	
	public static String KEY_BOM = "bom";
	
	public static String WORKORDER_NAME = "Wip.WorkOrder";
	
	public boolean isLoading = false;
	
	private Boolean isCaseSensitive;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		partService.addPartListener(this);
		
		configureBody(parent);
		parent.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		
		ADAuthority authority = (ADAuthority)mPart.getTransientData().get(CommandParameter.PARAM_ADAUTHORIY);
		String adFormName = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR)).getPARAM2();
		if (!StringUtil.isEmpty(adFormName)) {
			try {
				ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
				ADForm adForm = GlcUtil.getADForm(getClass(), adFormName, adManager);	
				ADTable adTable = adForm.getAdTable();
				//通过系统参数控制是否锁版本栏位是否显示
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				boolean isDisplayLockVersion = MesCfMod.isUseIsLockVersion(Env.getOrgRrn(), sysParamManager);
				if (!isDisplayLockVersion) {
					if (adTable.getFields() != null && adTable.getFields().size() > 0) {
						for (ADField adField : adTable.getFields()) {
							if (FIELD_ISLOCKVERSION.equals(adField.getName())) {
								adField.setIsDisplay(false);
							}
						}
					}
				}
				
				adTable.setAuthorityKey(authority.getName());
				form = new GlcForm(adForm, adTable);
				form.setAuthority(authority.getName());
				form.setEventBroker(eventBroker);
				//AuthorityKey后面加上UUID,是为了避免当页面关闭后,重新打开时Event可能被重新订阅问题(页面销毁时未及时取消)
				form.setTopicPrefix(authority.getName().replaceAll("\\.", GlcEvent.NAMESPACE_SEPERATOR) + UUIDUtil.base58Uuid());
				form.setTopicId(form.getTopicPrefix());
				form.setFormAttributes(adForm.getFormAttributes());
				form.setPostAddFields(this::postAddFields);
				form.createForm(parent, toolkit);
				createFormAction(form);
				
				focusControl = form.getFocusControl();
				registerAccelerator();
			} catch (Exception e) {
				logger.error(e);
			}
		}
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotListField = form.getFieldByControlId(FIELD_LOTLIST, ListTableManagerField.class);
		flowTreeCustomField = form.getFieldByControlId(FORM_PARTFLOW, CustomField.class);
		//flowCustomComposite = (FlowCustomComposite) flowTreeCustomField.getCustomComposite();
		materialListField = form.getFieldByControlId(FIELD_MATERIALIST, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNAPPROVE), this::approveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HOLD), this::holdAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_BOM), this::bomAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLOSE), this::closeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		lotIdField = form.getFieldByControlId(FORM_LOT_ID, TextField.class);
		lotQtyField = form.getFieldByControlId(FORM_LOT_QTY, TextField.class);
		lotNumberQtyField = form.getFieldByControlId(FORM_LOT_NUMBER, TextField.class);
		
		subscribeAndExecute(eventBroker, lotIdField.getFullTopic(BUTTON_NAME_ADD), this::addAdapter);
    	subscribeAndExecute(eventBroker, lotIdField.getFullTopic(BUTTON_NAME_DELETE), this::removeAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		init();
	}
	
	private void init() {
		block = (EntityBlock) form.getBlock();
		entityForm = (EntityForm) form.getSubFormById(FORM_BASIC_INFO);
		 
		itemSave = (ToolItem) form.getButtonByControl(null, BUTTON_SAVE);
		itemApprove = (ToolItem) form.getButtonByControl(null, BUTTON_UNAPPROVE);
		itemHold = (ToolItem) form.getButtonByControl(null, BUTTON_HOLD);
		itemBom = (ToolItem) form.getButtonByControl(null, BUTTON_BOM);
		itemClose = (ToolItem) form.getButtonByControl(null, BUTTON_CLOSE);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
		
		partNameField = entityForm.getFieldByControlId(FIELD_PARTNAME, SearchField.class);
		partVersionField = entityForm.getFieldByControlId(FIELD_PARTVERSION, RefTableField.class);
		reworkProcessNameField = entityForm.getFieldByControlId(FIELD_REWORKPROCESSNAME, RefTableField.class);
		//产品版本修改
		partVersionField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object arg0, Object arg1) {
				try {
					if (flowTreeCustomField != null && !isLoading) {
						if (partVersionField != null && !StringUtil.isEmpty(partVersionField.getText()) && partVersionField.getText().matches("^[0-9]*$")) {
							partInternalRefresh(partNameField.getText(), Long.valueOf(partVersionField.getText()));
							reworkProcessNameField.refresh();
						}
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
			}
		});
		
		//可选流程
		reworkProcessNameField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object arg0, Object arg1) {
				try {
					if (flowTreeCustomField != null && !isLoading) {
						if (reworkProcessNameField.getData() != null) {
//							Part curPart = (Part) partNameField.getData();
//							MaterialAltProcess altProcess = (MaterialAltProcess)reworkProcessNameField.getData();
//							loadFlowTreeByPartAltProcess(curPart, null, altProcess.getProcessName(), altProcess.getProcessVersion());
						} else {
							partInternalRefresh(partNameField.getText(), null);
						} 
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
			}
		});
		
		statusChanged(null, null);
		
		try {
			//检查在权限表中有没有bom权限的设置
			//如果没有设置则表示工单不需要BOM,则不创建BOM Item
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), 
					ADAuthority.class, 1, " name = '" + WORKORDER_NAME + "." + KEY_BOM + "'", "");
			if (authorities.isEmpty()) {
				isitemBom = false;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		
		if(!isitemBom) {
			itemBom = null;
		}
		WorkOrder workOrder =  new WorkOrder();
		workOrder.setOrgRrn(Env.getOrgRrn());
		entityForm.setObject(workOrder);
		
		lotNumberQtyField.setText("1");
		lotQtyField.setText("25");
	}
	
	//选择
	protected void selectionChanged(Object object) {
		try {
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			WorkOrder workOrder = (WorkOrder)selection.getFirstElement();
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				entityForm.setObject(adManager.getEntity((WorkOrder) workOrder));
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
			}
			isLoading = true;
			
			//插入entityForm
			entityForm.loadFromObject();
			
			//lotList列表
			lotListLoadFromObject();
			
			//partFlow
//			partFlowLoadFromObject();
			
			//bomLine列表
			bomLinesLoadFromObject();
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			isLoading = false;
		}
	}
	
	//状态修改
	public void statusChanged(String docStatus, String holdStatus) {
		if (Documentation.STATUS_CREATED.equals(docStatus)) {
            itemSave.setEnabled(true);
            if (itemApprove != null) {
            	itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText("  " + Message.getString("common.approve") + "  ");
     			itemApprove.setEnabled(true);
            }
            itemDelete.setEnabled(true);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(true);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(false);
            }
        } else if (Documentation.STATUS_APPROVED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if (itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString("common.unapprove"));
    			itemApprove.setEnabled(true);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(true);
            }
        } else if (WorkOrder.STATUS_STARTED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if (itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString("common.unapprove"));
      			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(true);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(true);
            }
        } else if (WorkOrder.STATUS_CLOSED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if (itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString("common.unapprove"));
    			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(false);
            }
        } else {
        	itemSave.setEnabled(true);
        	if(itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString("common.unapprove"));
     			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(false);
            }
        }
		
        if (WorkOrder.HOLDSTATE_OFF.equals(holdStatus)) {
        	if (itemHold != null) {
        		itemHold.setImage(SWTResourceCache.getImage("hold"));
        		itemHold.setText(Message.getString("wip.hold"));
        	}
        } else {
        	if(itemHold != null) {
        		itemHold.setImage(SWTResourceCache.getImage("release"));
        		itemHold.setText(Message.getString("wip.release"));
        	}
        }
    }
	
	//新增批次按钮
	@SuppressWarnings("unchecked")
	private void addAdapter(Object object) {
		try {
			if (lotListField.getListTableManager().getInput() != null) {
				for (WorkOrderLot woLot : ((List<WorkOrderLot>) lotListField.getListTableManager().getInput())) {
					if (lotIdField.getText().equalsIgnoreCase(woLot.getLotId())) {
						UI.showError(Message.getString("wip.lotid_repeat"));
						return;
					}
				}
			}
			List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
			if (lotListField.getListTableManager().getInput() != null) {
				workOrderLots.addAll((List<WorkOrderLot>) lotListField.getListTableManager().getInput());
			}
			long lotNumber = Long.parseLong(lotNumberQtyField.getText());
			BigDecimal mainQty = new BigDecimal(lotQtyField.getText());
			for (int i = 0; i < lotNumber; i++) {
				WorkOrderLot workOrderLot = new WorkOrderLot();
				workOrderLot.setOrgRrn(Env.getOrgRrn());
				workOrderLot.setIsActive(true);
				if (lotIdField.getText() != null && !"".equals(lotIdField.getText())) {
					String lotId = lotIdField.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					workOrderLot.setLotId(lotId);
				}
				workOrderLot.setMainQty(mainQty);
				workOrderLots.add(workOrderLot);
			}
			
			lotListField.getListTableManager().setInput(workOrderLots);
		} catch (Exception e) {
			e.printStackTrace();
			UI.showError(Message.getString("common.invalid_number"));
		}
	}
	
	//移除批次按钮
	@SuppressWarnings("unchecked")
	private void removeAdapter(Object object) {
		try {		
			List<Object> removeWLots = lotListField.getListTableManager().getCheckedObject();
			for (Object removeWoLot : removeWLots) {
				WorkOrderLot pre = (WorkOrderLot) removeWoLot;
				((List<WorkOrderLot>) lotListField.getListTableManager().getInput()).remove(pre);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 新增工单
	 * @param object
	 */
	protected void newAdapter(Object object) {
		try {			
			WorkOrder workOrder = new WorkOrder();
			workOrder.setOrgRrn(Env.getOrgRrn());
			entityForm.setObject(workOrder);
			entityForm.loadFromObject();
			statusChanged(null, null);
			block.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 保存工单
	 * @param object
	 */
	@SuppressWarnings("unchecked")
	protected void saveAdapter(Object object) {
		try {
			entityForm.removeAllMessages();
			if (entityForm.saveToObject()) {
				WorkOrder workOrder = (WorkOrder) entityForm.getObject();

				// 效验批次表
				List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
				if (lotListField.getListTableManager().getInput() != null) {
					startLots.addAll((List<WorkOrderLot>) lotListField.getListTableManager().getInput());
				}

				if (workOrder.getMainQty() == null) {
					return;
				}
				// 检查工单数量和批次总数量是否一致
//				if (getLotQty().compareTo(workOrder.getMainQty()) != 0) {
//					UI.showError(Message.getString("wip.wo_qty_match_total_lot_qty"));
//					return;
//				}
				workOrder.setWorkOrderLots(startLots);
				workOrder.setStartedMainQty(BigDecimal.ZERO);
				
				boolean isSaveLot = false;
		    	boolean isSaveSource = false;
		    	if (CollectionUtils.isNotEmpty(lotListField.getListTableManager().getInput())) {
		    		isSaveLot = true;
		    	}
		    	
		    	ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
				PpManager ppManager = Framework.getService(PpManager.class);
				ADBase obj = ppManager.saveWorkOrder(workOrder, null, null, false, isSaveLot, isSaveSource, false, Env.getSessionContext());
				if (obj == null) {
					return;
				}
				ADBase newBase = adManager.getEntity(obj);
				if (form.getAdForm().getAdTable().isContainAttribute()) {
					newBase.setAttributeValues(adManager.getEntityAttributeValues(
							form.getAdForm().getAdTable().getModelName(), newBase.getObjectRrn().longValue()));
				}
				if (workOrder.getObjectRrn() == null)
					block.refreshAdd(newBase);
				else {
					block.refreshUpdate(newBase);
				}
				entityForm.setObject(newBase);
				refreshAdapter(object);
				UI.showInfo(Message.getString("common.save_successed"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
    
	/**
	 * 审核工单
	 * @param object
	 */
	protected void approveAdapter(Object object) {
		try {
			entityForm.removeAllMessages();
			PpManager ppManager = Framework.getService(PpManager.class);
			if (entityForm.saveToObject()) {
				WorkOrder workOrder = (WorkOrder) entityForm.getObject();
				if (workOrder != null && workOrder.getObjectRrn() != null) {
					if (Documentation.STATUS_APPROVED.equals(workOrder.getDocStatus())) {
						if (UI.showConfirm(Message.getString("common.confirm_unapprove"))) {
							workOrder = ppManager.unApproveWorkOrder(workOrder, Env.getSessionContext());
							UI.showInfo(Message.getString("common.unapprove_successed"));
						}
					} else {
						List<WorkOrderBomLine> bomLines = null;
						if (itemBom != null) {
							// 检查BOM有没有设置
							bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
							if (bomLines == null || bomLines.isEmpty()) {
								UI.showWarning(Message.getString("pp.workorder_bom_is_not_exist"));
								return;
							}
						}
						// 暂时没有打开这些Tab未编写
						if (CollectionUtils.isNotEmpty(lotListField.getListTableManager().getInput())) {
							// 检查有没有生成批次
							ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
							List<WorkOrderLot> workOrderLot = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class,
									Env.getMaxResult(), " workOrderRrn = " + workOrder.getObjectRrn(), null);
							if (workOrderLot == null) {
								UI.showWarning(Message.getString("pp.workorder_lot_is_not_exist"));
								return;
							}
						}
						workOrder = ppManager.approveWorkOrder(workOrder, Env.getSessionContext());
						UI.showInfo(Message.getString("common.approve_successed"));
					}
					statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
					entityForm.setObject(workOrder);
					entityForm.loadFromObject();
					block.refreshUpdate(workOrder);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 暂停工单
	 * @param object
	 */
	protected void holdAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				PpManager ppManager = Framework.getService(PpManager.class);
				if (WorkOrder.HOLDSTATE_OFF.equals(workOrder.getHoldState())) {
					workOrder = ppManager.holdWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.hold_successed"));
				} else {
					workOrder = ppManager.releaseWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.release_successed"));
				}
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
				entityForm.setObject(workOrder);
				entityForm.loadFromObject();
				block.refreshUpdate(workOrder);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	/**
	 * bom
	 * @param object
	 */
	protected void bomAdapter(Object object) {
		try {
			if (((WorkOrder) entityForm.getObject()).getObjectRrn() != null) {
	            WorkOrderBomContext context = new WorkOrderBomContext();
	            context.setWorkOrder((WorkOrder) entityForm.getObject());
	            WorkOrderBomWizard orderBomWizard = new WorkOrderBomWizard(context, "WorkOrderBom");
	            WorkOrderBomDialog dialog = new WorkOrderBomDialog(Display.getCurrent().getActiveShell(), orderBomWizard);
	            if (dialog.open() == Dialog.OK) {
	            	ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
	            	entityForm.setObject(adManager.getEntity((WorkOrder) entityForm.getObject()));
	            	refreshAdapter(object);
				}
            }
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 关闭工单
	 * @param object
	 */
	protected void closeAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				if (UI.showConfirm(Message.getString("common.comfirm.close"))) {
					PpManager ppManager = Framework.getService(PpManager.class);
					// COMPLETE
					WorkOrder newWorkOrder = (WorkOrder) ppManager.completedWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString("common.close_successed"));
					entityForm.setObject(newWorkOrder);
					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 删除
	 * @param object
	 */
	protected void deleteAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				boolean confirmDelete = UI.showConfirm(Message.getString("wip.wo_sure_delete"));
				if (confirmDelete) {
					PpManager ppManager = Framework.getService(PpManager.class);
					ppManager.deleteWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString("common.delete_successed"));
					entityForm.setObject(new WorkOrder());
					refreshAdapter(object);
				}
			} else {
				UI.showError(Message.getString("common.select_object"));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 刷新
	 * @param object
	 */
	protected void refreshAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				block.refreshUpdate(workOrder);
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
				//插入entityForm
				entityForm.loadFromObject();
				
				//lotList列表
				lotListLoadFromObject();
				
				//partFlow
				//partFlowLoadFromObject();
				
				//bomLine列表
				bomLinesLoadFromObject();
			} else {
				entityForm.setObject(new WorkOrder());
				entityForm.loadFromObject();
				statusChanged(null, null);
				block.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 工单批次列表加载
	 */
	protected void lotListLoadFromObject() {
		lotListField.getListTableManager().getInput().clear();
		try {
			if (entityForm.getObject() != null 
					&& ((WorkOrder) entityForm.getObject()).getObjectRrn() != null) {
				WorkOrder workOrder = (WorkOrder) entityForm.getObject();
				List<WorkOrderLot> workOrderLots = workOrder.getWorkOrderLots();
				ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
				if (workOrderLots == null) {
					workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(), 
							" workOrderRrn = " + workOrder.getObjectRrn(), null);
				}
				lotListField.getListTableManager().setInput(workOrderLots);
				
				if (StringUtils.isEmpty(workOrder.getPartName())) {
					return;
				}
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Part part = null;
				if (workOrder.getPartVersion() == null) {
					part = prdManager.getActivePart(Env.getOrgRrn(), workOrder.getPartName(), true);
				} else {
					part = prdManager.getPartById(Env.getOrgRrn(), workOrder.getPartName(), workOrder.getPartVersion());
				}
				if (part != null && part.getLotSize() != null) {
					lotQtyField.setText(String.valueOf(part.getLotSize()));
				} else {
					lotQtyField.setText("25");
				}
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	/**
	 * 加载产品流程, 刷新与初始化时加载
	 */
//	protected void partFlowLoadFromObject() {
//		try {
//			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
//			if (workOrder != null && workOrder.getObjectRrn() != null) {
//				ADManager adManager = Framework.getService(ADManager.class);
//				WorkOrder tempWordOrder = new WorkOrder();
//				tempWordOrder.setObjectRrn(workOrder.getObjectRrn());
//				WorkOrder nextWordOrder = (WorkOrder) adManager.getEntity(tempWordOrder);
//
//				String partName = nextWordOrder.getPartName();
//				Long partVersion = nextWordOrder.getPartVersion();
//				//相同产品版本，界面不做重复加载
//				Boolean isNewPart = true;
//				if (part !=null 
//						&& part.getName().equals(workOrder.getPartName()) 
//						&& part.getVersion().equals(workOrder.getPartVersion())) {
//						isNewPart = false;
//				}
//				if (StringUtils.isNotEmpty(partName) && isNewPart) {
//					if (partVersion != null) {
//						// 获取指定版本产品的信息
//						List<Part> verisonParts = adManager.getEntityList(Env.getOrgRrn(), Part.class,
//								Integer.MAX_VALUE, " name = '" + partName + "' and version = " + partVersion, "");
//						if (verisonParts != null && verisonParts.size() > 0) {
//							part = verisonParts.get(0);
//							flowCustomComposite.loadFlowTreeByPart(part, null);
//						} else {
//							part = null;
//							flowCustomComposite.loadFlowTreeByPart(null, null);
//						}
//					} else {
//						// 版本号为空则取激活状态的产品流程
//						List<Part> activeParts = adManager.getEntityList(Env.getOrgRrn(), Part.class,
//								Integer.MAX_VALUE, " name = '" + partName + "' and status = 'Active' ", "");
//						if (activeParts != null && activeParts.size() > 0) {
//							part = activeParts.get(0);
//							flowCustomComposite.loadFlowTreeByPart(part, null);
//						} else {
//							part = null;
//							flowCustomComposite.loadFlowTreeByPart(null, null);
//						}
//					}
//				}
//			} else {
//				part = null;
//				flowCustomComposite.loadFlowTreeByPart(null, null);
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
	
	/**
	 * 下选选择可选流程时加载产品流程
	 * @param part
	 * @param stepPath
	 * @param altProcessName
	 * @param altProcessVersion
	 */
//	protected void loadFlowTreeByPartAltProcess(Part part, String stepPath, String altProcessName, Long altProcessVersion) {
//		try {
//			PrdManager prdManager = Framework.getService(PrdManager.class);
//			if (part != null) {
//				Process process = new Process();
//				if (part.getIsAlternateProcess() && !StringUtil.isEmpty(altProcessName) && altProcessVersion != null) {
//        			process.setOrgRrn(Env.getOrgRrn());
//    				process.setName(altProcessName);
//    				process.setVersion(altProcessVersion);
//					process = (Process) prdManager.getSimpleProcessDefinition(process);
//        		} else {	
//        			process = (Process)prdManager.getPartProcess(part);
//        		}
//				process = (Process)prdManager.getProcessDefinition(process);
//				List<Process> processes = new ArrayList<Process>();
//				processes.add(process);
//				flowCustomComposite.getTreeManager().setInput(processes);
//				if (stepPath != null) {
//					List<ADBase> processNodes = new ArrayList<ADBase>();
//					processNodes.add(process);
//					List<Node> nodes = process.getNodes().stream().filter(s -> s.getPath().equals(stepPath)).collect(Collectors.toList());
//					processNodes.addAll(nodes);
//					flowCustomComposite.setCurrentFlow((List)processNodes);	
//				} else {
//					flowCustomComposite.getViewer().expandToLevel(2);
//				}
//			} else {
//				flowCustomComposite.getTreeManager().setInput(null);
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//			return;
//		}
//	}
	
	/**
	 * 选择产品时，加载产品流程
	 * @param partName
	 * @param partVersion
	 */
	protected void partInternalRefresh(String partName, Long partVersion) {
		if (entityForm.getObject() != null) {
			try {
				Part curPart = (Part) partNameField.getData();
				if (part != null 
						&& curPart != null 
						&& curPart.getName().equals(part.getName()) 
						&& curPart.getVersion().equals(part.getVersion())) {
					//相同产品版本，界面不做重复加载
				} else {
					if (partName != null) {
						ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
						if (partVersion != null) {
							// 获取指定版本产品的信息
							List<Part> verisonParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
									" name = '" + partName + "' and version = " + partVersion, "");
							if (verisonParts != null && verisonParts.size() > 0) {
								Part versionPart = verisonParts.get(0);
								part = versionPart;
								//flowCustomComposite.loadFlowTreeByPart(part, null);
							} else {
								part = null;
								//flowCustomComposite.loadFlowTreeByPart(null,null);
							}
						} else {
							// 版本号为空则取激活状态的产品流程
							List<Part> activeParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
									" name = '" + partName + "' and status = 'Active' ", "");
							if (activeParts != null && activeParts.size() > 0) {
								Part activePart = activeParts.get(0);
								part = activePart;
								//flowCustomComposite.loadFlowTreeByPart(part, null);
							}else {
								part = null;
								//flowCustomComposite.loadFlowTreeByPart(null, null);
							}
						}
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	}
	
	/**
	 * 加载BomLines
	 */
	protected void bomLinesLoadFromObject() {	
		try {
			if (entityForm.getObject() != null && ((WorkOrder) entityForm.getObject()).getObjectRrn() != null) {
				List<WorkOrderBomLine> bomLines = new ArrayList<WorkOrderBomLine>();
				PpManager ppManager = Framework.getService(PpManager.class);
				bomLines = ppManager.getWorkOrderBomLines((WorkOrder) entityForm.getObject(), Env.getSessionContext());
				materialListField.getListTableManager().setInput(bomLines);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}

}
