//package com.glory.mes.pvc.pp.wo.mr;
//
//import java.util.List;
//
//import org.eclipse.swt.SWT;
//import org.eclipse.swt.events.SelectionAdapter;
//import org.eclipse.swt.events.SelectionEvent;
//import org.eclipse.swt.widgets.Composite;
//import org.eclipse.swt.widgets.ToolBar;
//import org.eclipse.swt.widgets.ToolItem;
//import org.eclipse.ui.forms.widgets.Section;
//
//import com.glory.framework.activeentity.client.ADManager;
//import com.glory.framework.activeentity.model.ADTab;
//import com.glory.framework.base.entitymanager.forms.EntityForm;
//import com.glory.framework.base.ui.util.Env;
//import com.glory.framework.base.ui.util.Message;
//import com.glory.framework.base.ui.util.SWTResourceCache;
//import com.glory.framework.base.ui.util.UI;
//import com.glory.framework.runtime.Framework;
//import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
//import com.glory.framework.security.model.ADAuthority;
//import com.glory.mes.pp.model.WorkOrder;
//import com.glory.mes.pvc.client.PvcManager;
//import com.glory.mes.pvc.pp.wo.mr.processor.PvcGenerateMaterialRequisitionProcessor;
//import com.glory.mes.pvc.pp.wo.mr.processor.PvcReceiveByMaterialRequisitionProcessor;
//import com.glory.mes.wip.mm.MaterialRequisition;
//import com.glory.mes.wip.pp.wo.mr.MaterialRequestProperties;
//
//public class PvcMaterialRequestProperties extends MaterialRequestProperties {
//
//	/**
//	 * 领料单信息
//	 */
//	public static final String TABNAME_MR_PICKING = "PVCWorkOrderMaterialRequstion";
//	public static final String TABNAME_MR_DETAIL = "PVCWorkOrderMRDetail";
//	
//	private static final String KEY_PICK = "PICK";
//	private static final String KEY_REQ_PICK = "REQPICK";
//	private static final String KEY_GENERATE = "GENERATE";
//	
//	protected PvcMaterialRequestPickingForm pickingForm;
//	
//	protected ToolItem itemReqPicking;
//	
//	public PvcMaterialRequestProperties() {
//		super();
//	}
//	
//	public void createToolBar(Section section) {
//    	ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
//    	createToolItemGenerateMaterialRequisition(tBar);
//    	createToolItemRequestPicking(tBar);
//    	createToolItemPicking(tBar);
//		createToolItemRefresh(tBar);
//		section.setTextClient(tBar);
//    }
//	
//	//根据领料单领料
//	protected void createToolItemPicking(ToolBar tBar) {
//		try {
//			ADManager adManager = Framework.getService(ADManager.class);
//			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), 
//					ADAuthority.class, 1, " name = '" + getTable().getAuthorityKey() + "." + KEY_PICK + "'", "");
//			if (authorities.isEmpty()) {
//				return;
//			}
//			itemPicking = new ToolItem(tBar, SWT.PUSH);
//			itemPicking.setText(Message.getString("mm.mr_picking"));
//			itemPicking.setImage(SWTResourceCache.getImage("receive"));
//			itemPicking.addSelectionListener(new SelectionAdapter() {
//				@Override
//				public void widgetSelected(SelectionEvent event) {
//					pickingAdapter();
//				}
//			});
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//	        return;
//		}
//	}
//	
//	// 申请领料
//	protected void createToolItemRequestPicking(ToolBar tBar) {
//		try {
//			ADManager adManager = Framework.getService(ADManager.class);
//			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, 1,
//					" name = '" + getTable().getAuthorityKey() + "." + KEY_REQ_PICK + "'", "");
//			if (authorities.isEmpty()) {
//				return;
//			}
//			itemReqPicking = new ToolItem(tBar, SWT.PUSH);
//			itemReqPicking.setText(Message.getString("pvc.request_requisition"));
//			itemReqPicking.setImage(SWTResourceCache.getImage("receive"));
//			itemReqPicking.addSelectionListener(new SelectionAdapter() {
//				@Override
//				public void widgetSelected(SelectionEvent event) {
//					reqPickingAdapter();
//				}
//			});
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//			return;
//		}
//	}
//	
//	//创建领料单
//	protected void createToolItemGenerateMaterialRequisition(ToolBar tBar) {
//		try {
//			ADManager adManager = Framework.getService(ADManager.class);
//			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), 
//					ADAuthority.class, 1, " name = '" + getTable().getAuthorityKey() + "." + KEY_GENERATE + "'", "");
//			if (authorities.isEmpty()) {
//				return;
//			}
//			itemPicking = new ToolItem(tBar, SWT.PUSH);
//			itemPicking.setText(Message.getString("mm.material_request_build"));
//			itemPicking.setImage(SWTResourceCache.getImage("receive"));
//			itemPicking.addSelectionListener(new SelectionAdapter() {
//				@Override
//				public void widgetSelected(SelectionEvent event) {
//					generateMaterialRequisitionAdapter();
//				}
//			});
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//	        return;
//		}
//	}
//	
//	@Override
//	protected EntityForm getForm(Composite composite, ADTab tab) {
//		EntityForm itemForm;
//		String tabName = tab.getName();
//		if (tabName.equalsIgnoreCase(TABNAME_MR_PICKING)) {
//			//领料单详情
//			pickingForm = new PvcMaterialRequestPickingForm(composite, SWT.NONE, tab, mmng);
//			return pickingForm;
//		} /*else if (tabName.equalsIgnoreCase(TABNAME_MR_LINE)) {
//			lineForm = new MaterialRequestLineForm(composite, SWT.NONE, tab, mmng);
//			return lineForm;
//		} */else if (tabName.equalsIgnoreCase(TABNAME_MR_DETAIL)) {
//			//领料历史
//			detailForm = new PvcMaterialRequestDetailForm(composite, SWT.NONE, tab, mmng);
//			return detailForm;
//		} else {
//			itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
//		}
//		return itemForm;
//	}
//	
//	protected void generateMaterialRequisitionAdapter() {
//		try {
//			WorkOrder workOrder = (WorkOrder) getAdObject();
//			if (workOrder.getObjectRrn() != null) {
//				PvcGenerateMaterialRequisitionProcessor processor = new PvcGenerateMaterialRequisitionProcessor(workOrder);
//				processor.open();
//				refresh();
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		} 
//	}
//	
//	
//	protected void pickingAdapter() {
//		try {
//			WorkOrder workOrder = (WorkOrder) getAdObject();
//			MaterialRequisition materialRequisition = pickingForm.getMaterialRequisition();
//			if (workOrder.getObjectRrn() != null && materialRequisition != null) {
//				PvcReceiveByMaterialRequisitionProcessor processor = new PvcReceiveByMaterialRequisitionProcessor(workOrder, materialRequisition);
//				processor.open();
//				refresh();
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		} 
//	}
//	
//	protected void reqPickingAdapter() {
//		try {
//			List<MaterialRequisition> reqList = (List) pickingForm.getTableManager().getCheckedObject();
//			PvcManager pvcManager = Framework.getService(PvcManager.class);
//			pvcManager.receiveMLotsRequestByMaterialRequisition(reqList, Env.getSessionContext());
//			UI.showInfo(Message.getString("common.operation_successed"));
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//	
//	@Override
//	public void refresh() {
//		pickingForm.refresh();
//		super.refresh();
//	}
//}
