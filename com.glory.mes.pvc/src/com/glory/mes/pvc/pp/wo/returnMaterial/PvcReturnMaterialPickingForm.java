//package com.glory.mes.pvc.pp.wo.returnMaterial;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
//import org.apache.commons.collections.CollectionUtils;
//import org.eclipse.swt.SWT;
//import org.eclipse.swt.events.SelectionAdapter;
//import org.eclipse.swt.events.SelectionEvent;
//import org.eclipse.swt.layout.GridData;
//import org.eclipse.swt.layout.GridLayout;
//import org.eclipse.swt.widgets.Composite;
//import org.eclipse.swt.widgets.SquareButton;
//import org.eclipse.swt.widgets.Text;
//import org.eclipse.ui.forms.IMessageManager;
//import org.eclipse.ui.forms.widgets.FormToolkit;
//
//import com.glory.framework.activeentity.client.ADManager;
//import com.glory.framework.activeentity.model.ADTab;
//import com.glory.framework.activeentity.model.ADTable;
//import com.glory.framework.base.ui.forms.field.RefTableField;
//import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
//import com.glory.framework.base.ui.swt.UIControlsFactory;
//import com.glory.framework.base.ui.util.Env;
//import com.glory.framework.base.ui.util.Message;
//import com.glory.framework.base.ui.util.RCPUtil;
//import com.glory.framework.base.ui.util.SWTResourceCache;
//import com.glory.framework.base.ui.util.UI;
//import com.glory.framework.core.util.StringUtil;
//import com.glory.framework.runtime.Framework;
//import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
//import com.glory.mes.mm.client.MMManager;
//import com.glory.mes.mm.lot.model.MLot;
//import com.glory.mes.pp.client.PpManager;
//import com.glory.mes.pp.model.WorkOrder;
//import com.glory.mes.pp.model.WorkOrderLot;
//import com.glory.mes.pvc.client.PvcManager;
//import com.glory.mes.wip.client.MLotManager;
//import com.glory.mes.wip.mm.MaterialRequisitionDetail;
//import com.glory.mes.wip.mm.MaterialRequisitionLine;
//import com.glory.mes.wip.pp.wo.mr.MaterialRequestPickingForm;
//
//public class PvcReturnMaterialPickingForm extends MaterialRequestPickingForm {
//
//	protected RefTableField mLotIdText;
//	protected Text mLotQtyText;
//	protected Text mLotQtyvendor;
//	protected Text mLotQtysquareResistance;
//	protected RefTableField materialNameField;
//	private ListEditorTableManager pickingTableManager;
//
//	private static String TABLE_NAME = "PvcMLotWorkReceive";
//
//	public PvcReturnMaterialPickingForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
//		super(parent, style, tab, mmng);
//	}
//
//	protected void createContent() {
//		toolkit = new FormToolkit(getDisplay());
//
//		GridLayout layout = new GridLayout();
//		layout.verticalSpacing = 0;
//		layout.horizontalSpacing = 0;
//		layout.marginWidth = 0;
//		layout.marginHeight = 0;
//		setLayout(new GridLayout(1, true));
//
//		toolkit.setBackground(getBackground());
//		form = toolkit.createScrolledForm(this);
//		form.setLayoutData(new GridData(GridData.FILL_BOTH));
//
//		Composite body = getForm().getBody();
//		layout = new GridLayout();
//		layout.verticalSpacing = mVertSpacing;
//		layout.horizontalSpacing = mHorizSpacing;
//		layout.marginWidth = mMarginWidth;
//		layout.marginHeight = mMarginHeight;
//		layout.marginLeft = mLeftPadding;
//		layout.marginRight = mRightPadding;
//		layout.marginTop = mTopPadding;
//		layout.marginBottom = mBottomPadding;
//		body.setLayout(layout);
//
//		Composite top = toolkit.createComposite(body);
//		top.setLayout(new GridLayout(2, false));
//		GridData gd = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
//		top.setLayoutData(gd);
//
//		GridData gText = new GridData(GridData.FILL_HORIZONTAL);
//		gText.widthHint = 60;
//		toolkit.createLabel(top, Message.getString("mm.mlot_id"));
//
//		mLotIdText = RCPUtil.createRefTableField(top, null, "MMMLotList", null, true);
//		mLotIdText.getComboControl().setLayoutData(gText);
//
//		gText = new GridData(GridData.FILL_HORIZONTAL);
//		gText.widthHint = 140;
//		toolkit.createLabel(top, Message.getString("mm.qty"));
//		mLotQtyText = toolkit.createText(top, "", SWT.BORDER);
//		mLotQtyText.setLayoutData(gText);
//		mLotQtyText.setTextLimit(32);
//
//		SquareButton addLotBtn = UIControlsFactory.createButton(top, UIControlsFactory.BUTTON_DEFAULT);
//		addLotBtn.setText(Message.getString("common.add"));
//		addLotBtn.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
//		addLotBtn.addSelectionListener(new SelectionAdapter() {
//			public void widgetSelected(SelectionEvent event) {
//				addAdaptor();
//			}
//		});
//
//		SquareButton removeLotbtn = UIControlsFactory.createButton(top, UIControlsFactory.BUTTON_DEFAULT);
//		removeLotbtn.setText(Message.getString("common.delete"));
//		removeLotbtn.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
//		removeLotbtn.addSelectionListener(new SelectionAdapter() {
//			public void widgetSelected(SelectionEvent event) {
//				removeAdaptor();
//			}
//		});
//
//		Composite tableCom = toolkit.createComposite(body);
//		tableCom.setLayout(new GridLayout(1, false));
//		GridData tablegd = new GridData(GridData.FILL_BOTH);
//		tablegd.grabExcessHorizontalSpace = true;
//		tablegd.heightHint = 300;
//		tableCom.setLayoutData(tablegd);
//		try {
//			ADManager adManager = Framework.getService(ADManager.class);
//			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
//			pickingTableManager = new ListEditorTableManager(adTable, true);
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//		pickingTableManager.newViewer(tableCom);
//	}
//
//	@SuppressWarnings("unchecked")
//	public void addAdaptor() {
//		try {
//			if (StringUtil.isEmpty(mLotIdText.getText())) {
//				UI.showError(Message.getString("mm.please_enter_mlot_id"));
//				return;
//			}
//			if (StringUtil.isEmpty(mLotQtyText.getText())) {
//				UI.showError(Message.getString("mm.enter_qty"));
//				return;
//			}
//
//			// 检查领料单
//			WorkOrder wo = (WorkOrder) object;
//			if (pickingTableManager.getInput() != null) {
//				for (MLot mLot : ((List<MLot>) pickingTableManager.getInput())) {
//					if (mLotIdText.getText().equalsIgnoreCase(mLot.getmLotId())) {
//						UI.showError(Message.getString("mm.mlot_id_repeat"));
//						return;
//					}
//				}
//			}
//
//			List<MLot> mLots = new ArrayList<MLot>();
//			if (pickingTableManager.getInput() != null) {
//				mLots.addAll((List<MLot>) pickingTableManager.getInput());
//			}
//			BigDecimal mainQty = new BigDecimal(mLotQtyText.getText());
//
//			// 检查物料批次是否存在,检查物料名称
//			MMManager mmManager = Framework.getService(MMManager.class);
//			MLot mLot = mmManager.getMLotByMLotId(Env.getSessionContext().getOrgRrn(), mLotIdText.getText());
//
//			mLot.setTransMainQty(mainQty);
//			mLots.add(mLot);
//
//			pickingTableManager.setInput(mLots);
//		} catch (Exception e) {
//			e.printStackTrace();
//			UI.showError(Message.getString("common.invalid_number"));
//		}
//	}
//
//	public void removeAdaptor() {
//		List<Object> mLots = pickingTableManager.getCheckedObject();
//		for (Object removeWoLot : mLots) {
//			MLot pre = (MLot) removeWoLot;
//			((List<WorkOrderLot>) pickingTableManager.getInput()).remove(pre);
//		}
//	}
//
//	public MLot searchMLot(String mLotId) {
//		try {
//			MLotManager mLotManager = Framework.getService(MLotManager.class);
//			MMManager mmManager = Framework.getService(MMManager.class);
//			MLot mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
//			if (mLot == null) {
//				return null;
//			}
//			// 1,执行Lot有效性校验
//			MLot.checkMLotIsAvailable(mLot);
//			// 2,检查物料是否在领料单中
//			WorkOrder wo = (WorkOrder) object;
//			PpManager ppManager = Framework.getService(PpManager.class);
//			List<MaterialRequisitionLine> requisitionLines = ppManager.getMaterialRequisitionLines(wo,
//					Env.getSessionContext());
//			boolean flag = true;
//			for (MaterialRequisitionLine materialRequisitionLine : requisitionLines) {
//				if (materialRequisitionLine.getMaterialName().equals(mLot.getMaterialName())) {
//					flag = false;
//				}
//			}
//			if (flag) {
//				UI.showError(Message.getString("wip.lot_invalid"));
//				return null;
//			}
//			return mLot;
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//		return null;
//	}
//
//	public String getSelectedMaterialName() {
//		return materialNameField.getText();
//	}
//
//	public ListEditorTableManager getTableManager() {
//		return pickingTableManager;
//	}
//
//	@Override
//	public void refresh() {
//		if (mLotIdText != null) {
//			mLotIdText.setValue("");
//		}
//		if (mLotQtyText != null) {
//			mLotQtyText.setText("");
//		}
//		if (materialNameField != null) {
//			materialNameField.setValue("");
//		}
//
//		List<MLot> mLots = (List<MLot>) pickingTableManager.getInput();
//		mLots.clear();
//	}
//
//	@Override
//	public void loadFromObject() {
//		if (object != null) {
//			List<MLot> mlots = getDetails();
//			mLotIdText.setInput(mlots);
//		}
//		refresh();
//	}
//
//	public List<MLot> getDetails() {
//		try {
//			List<MLot> mlots = new ArrayList<MLot>();
//			WorkOrder workOrder = (WorkOrder) object;
//			if (workOrder.getObjectRrn() == null) {
//				return mlots;
//			}
//			PvcManager pvcManager = Framework.getService(PvcManager.class);
//			MMManager mmManager = Framework.getService(MMManager.class);
//			List<MaterialRequisitionDetail> details = pvcManager.getMaterialRequisitionDetailsByWorkOrder(workOrder,
//					Env.getSessionContext());
//			
//			if (!CollectionUtils.isEmpty(details)) {
//				List<String> mlotIds = details.stream().map(x -> x.getmLotId()).collect(Collectors.toList());
//				for (String mlotId : mlotIds) {
//					MLot mlot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mlotId, true);
//					//已经退料的不需要
//					if(!mlot.getState().equals("OUT")) {
//						mlots.add(mlot);
//					}
//				}
//			}
//			return mlots;
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//		return null;
//	}
//}
