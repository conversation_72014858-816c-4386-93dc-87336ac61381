package com.glory.mes.pvc.pp.wo.start;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcManager;
import com.glory.mes.wip.mm.MaterialRequisition;
import com.glory.mes.wip.mm.MaterialRequisitionDetail;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.pp.wo.WorkOrderMLotTableSelectField;

public class PvcWoStartDialog extends BaseTitleDialog {
	
	private List<WorkOrderLot> workOrderLots;
	private ListTableManager tableManager;
	private WorkOrder workOrder;
	private WorkOrderMLotTableSelectField lotTableSelectField;
	
	private List<Lot> startLots;

	public PvcWoStartDialog(Shell parentShell, WorkOrder workOrder, List<WorkOrderLot> orderLots) {
		super(parentShell);
		this.workOrderLots = orderLots;
		this.workOrder = workOrder;
	}

	@Override
	protected Control buildView(Composite parent) {
		try {
			setTitle(Message.getString("common.lotstart"));
			setMessage(Message.getString("mm.please_add_mlot"));
			FormToolkit toolkit = new FormToolkit(parent.getDisplay());
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			final ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "PPWorkOrderLots");
			tableManager = new ListTableManager(adTable);
			tableManager.setIndexFlag(true);
			tableManager.newViewer(parent);
			tableManager.setInput(workOrderLots);

			ADTable mlotadTable = adManager.getADTable(Env.getOrgRrn(), "PPWorkOrderMLotSelect");
			ListTableManager sourceTableManager = new ListEditorTableManager(mlotadTable, true);

			lotTableSelectField = new PvcWoMLotTableSelectField("", sourceTableManager, getWhereClause());
			
			BigDecimal generationLotQty = workOrderLots.stream()
					.map(WorkOrderLot::getMainQty).reduce(BigDecimal.ZERO, BigDecimal::add);
			
			lotTableSelectField.setGenerationLotQty(generationLotQty);
			lotTableSelectField.setMaterialRequisitionLines(buildMaterialRequest());
			lotTableSelectField.createContent(parent, toolkit);
			((PvcWoMLotTableSelectField)lotTableSelectField).setmLotIds(buildMLotIds());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return parent;
	}
	
	protected List<MaterialRequisitionLine> buildMaterialRequest() {
		List<MaterialRequisitionLine> materialRequisitionLines = new ArrayList<MaterialRequisitionLine>();
		if (workOrder != null && workOrder.getObjectRrn() != null) {
			try {
				PvcManager pvcManager = Framework.getService(PvcManager.class);
				ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
				
				List<MaterialRequisition> materialRequisitions = pvcManager.getMaterialRequisitions(workOrder, Env.getSessionContext());
				for (MaterialRequisition materialRequisition : materialRequisitions) {
					List<MaterialRequisitionLine> lines = adManager.getEntityList(Env.getSessionContext().getOrgRrn(),
							MaterialRequisitionLine.class, Integer.MAX_VALUE, "requisitionRrn = "+ materialRequisition.getObjectRrn(), "");
					materialRequisitionLines.addAll(lines);
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return materialRequisitionLines;
    }
	
	protected List<String> buildMLotIds() {
		List<String> mlotIds= new ArrayList<>();
		try {
			PvcManager pvcManager = Framework.getService(PvcManager.class);
			List<MaterialRequisitionDetail> details = pvcManager.getMaterialRequisitionDetailsByWorkOrder(workOrder, Env.getSessionContext());
			if (!CollectionUtils.isEmpty(details)) {
				 mlotIds = details.stream().map(x -> x.getmLotId()).collect(Collectors.toList());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return mlotIds;
	}
	

	@Override
	protected void okPressed() {
		try {
			List<? extends Object> workOrderLots = tableManager.getInput();
				
			if (CollectionUtils.isEmpty(getValue())) {
				UI.showError(Message.getString("pcb.wo_start_not_select_mlot"));
				return;
			}
			
			List<WorkOrderLot> orderLots = Lists.newArrayList();
			for (Object object : workOrderLots) {
				orderLots.add((WorkOrderLot) object);
			}
			
			// 拿到选择的物料批
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			List<MLot> sourceMLots = new ArrayList<MLot>();
			for (MLot workOrderSource : (List<MLot>) getValue()) {
				MLot sourceMLot = new MLot();
				sourceMLot.setObjectRrn(workOrderSource.getObjectRrn());
				sourceMLot = (MLot) adManager.getEntity(sourceMLot);
				sourceMLot.setTransMainQty(workOrderSource.getTransMainQty());
				sourceMLots.add(sourceMLot);
			}

			// 校验每种物料个数是否不足
			if (!isQtyCheckOut(orderLots, sourceMLots)) {
				UI.showError(Message.getString("pcb.wo_start_mlot_qty_error"));
				return;
			}
			
			PvcManager pvcManager = Framework.getService(PvcManager.class);
			List<Lot> lots = pvcManager.startWorkOrder(workOrder, orderLots, sourceMLots, null, Env.getSessionContext());
			setStartLots(lots);
			
			super.okPressed();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected String getWhereClause() {
		StringBuffer whereClause = new StringBuffer();

		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
			if (bomLines == null || bomLines.size() == 0) {
				return whereClause.toString();
			}
			whereClause.append(" materialName in (");
			for (WorkOrderBomLine workOrderBomLine : bomLines) {
				whereClause.append("'" + workOrderBomLine.getMaterialName() + "',");
			}
			whereClause.replace(0, whereClause.length(), whereClause.substring(0, whereClause.length() - 1));
			whereClause.append(")");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return whereClause.toString();
	}

	private boolean isQtyCheckOut(List<WorkOrderLot> checkworkOrderLots, List<MLot> mlots) {
		boolean isOk = true;
		try {
			BigDecimal needQty = BigDecimal.ZERO;
			Map<String, BigDecimal> mQty = new HashMap<String, BigDecimal>();
			//计算出勾选批次需要的数量
			for (WorkOrderLot workOrderLot : checkworkOrderLots) {
				needQty = needQty.add(workOrderLot.getMainQty());
			}

			//将选择物料批按物料类型计算个数
			for (MLot mlot : mlots) {
				boolean isAdd = false;
				if (mQty.size() == 0) {
					if (mlot != null) {
						mQty.put(mlot.getMaterialName(), mlot.getTransMainQty());
					}
				} else {
					for (String materialName : mQty.keySet()) {
						if (mlot.getMaterialName().equals(materialName)) {
							isAdd = true;
							mQty.put(materialName, mQty.get(materialName).add(mlot.getTransMainQty()));
						}
					}
					if (!isAdd) {
						mQty.put(mlot.getMaterialName(), mlot.getTransMainQty());
					}
				}
			}

			//如果存在某种物料个数少于或大于需要数量返回false
			for (String key : mQty.keySet()) {
				if (mQty.get(key).subtract(needQty).compareTo(BigDecimal.ZERO) != 0) {
					isOk = false;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return isOk;
	}
	
	public List<Lot> getStartLots() {
		return startLots;
	}

	public void setStartLots(List<Lot> startLots) {
		this.startLots = startLots;
	}

	@SuppressWarnings("unchecked")
	private List<MLot> getValue() {
		return (List<MLot>) lotTableSelectField.getValue();
	}

	@Override
	protected Point getInitialSize() {
		return new Point(1200, 800);
	}

	@Override
	protected Point getInitialLocation(Point initialSize) {
		Point shellSize = Display.getCurrent().getActiveShell().getSize();
		return new Point((shellSize.x - initialSize.x) / 3 * 2, (shellSize.y - initialSize.y) / 2);
	}
}
