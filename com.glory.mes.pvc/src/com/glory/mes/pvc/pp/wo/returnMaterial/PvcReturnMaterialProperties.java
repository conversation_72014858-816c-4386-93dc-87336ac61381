//package com.glory.mes.pvc.pp.wo.returnMaterial;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import org.eclipse.swt.SWT;
//import org.eclipse.swt.events.SelectionAdapter;
//import org.eclipse.swt.events.SelectionEvent;
//import org.eclipse.swt.widgets.Composite;
//import org.eclipse.swt.widgets.ToolBar;
//import org.eclipse.swt.widgets.ToolItem;
//import org.eclipse.ui.forms.widgets.Section;
//
//import com.glory.framework.activeentity.client.ADManager;
//import com.glory.framework.activeentity.model.ADTab;
//import com.glory.framework.base.entitymanager.forms.EntityForm;
//import com.glory.framework.base.ui.forms.IForm;
//import com.glory.framework.base.ui.util.Env;
//import com.glory.framework.base.ui.util.Message;
//import com.glory.framework.base.ui.util.SWTResourceCache;
//import com.glory.framework.base.ui.util.UI;
//import com.glory.framework.runtime.Framework;
//import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
//import com.glory.framework.security.model.ADAuthority;
//import com.glory.mes.mm.lot.model.MLot;
//import com.glory.mes.pp.model.WorkOrder;
//import com.glory.mes.pvc.client.PvcManager;
//import com.glory.mes.wip.mm.MaterialRequisition;
//import com.glory.mes.wip.pp.wo.mr.MaterialRequestLineForm;
//import com.glory.mes.wip.pp.wo.mr.MaterialRequestProperties;
//
//public class PvcReturnMaterialProperties extends MaterialRequestProperties {
//	
//	 public static final String TABNAME_MR_RETURN = "WIPMRReturn";
//	 
//	 public static final String TABNAME_MR_DETAIL = "WIPMRDetail";
//	 
//	 private static final String KEY_REQ_RETURN = "REQRETURN";
//	 protected ToolItem itemReqRetun;
//
//	protected PvcReturnMaterialPickingForm pickingForm;
//	
//	protected PvcMaterialReturnDetailForm detailForm;
//	
//	public PvcReturnMaterialProperties() {
//		super();
//	}
//	
//	@Override
//	public void createToolBar(Section section) {
//		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
//		createToolItemReqReturn(tBar);
//		createToolItemPicking(tBar);
//		createToolItemRefresh(tBar);
//		section.setTextClient(tBar);
//	}
//	
//	@Override
//	protected EntityForm getForm(Composite composite, ADTab tab) {
//		EntityForm itemForm;
//		String tabName = tab.getName();
//		if (tabName.equalsIgnoreCase(TABNAME_MR_RETURN)) {
//			pickingForm = new PvcReturnMaterialPickingForm(composite, SWT.NONE, tab, mmng);
//			return pickingForm;
//		} else if (tabName.equalsIgnoreCase(TABNAME_MR_LINE)) {
//			lineForm = new MaterialRequestLineForm(composite, SWT.NONE, tab, mmng);
//			return lineForm;
//		} else if (tabName.equalsIgnoreCase(TABNAME_MR_DETAIL)) {
//			detailForm = new PvcMaterialReturnDetailForm(composite, SWT.NONE, tab, mmng);
//			return detailForm;
//		} else {
//			itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
//		}
//		return itemForm;
//	}
//	
//	// 申请退料
//	protected void createToolItemReqReturn(ToolBar tBar) {
//		try {
//			ADManager adManager = Framework.getService(ADManager.class);
//			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, 1,
//					" name = '" + getTable().getAuthorityKey() + "." + KEY_REQ_RETURN + "'", "");
//			if (authorities.isEmpty()) {
//				return;
//			}
//			itemReqRetun = new ToolItem(tBar, SWT.PUSH);
//			itemReqRetun.setText(Message.getString("pvc.mlot_req_return"));
//			itemReqRetun.setImage(SWTResourceCache.getImage("receive"));
//			itemReqRetun.addSelectionListener(new SelectionAdapter() {
//				@Override
//				public void widgetSelected(SelectionEvent event) {
//					requestReturnAdapter();
//				}
//			});
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//			return;
//		}
//	}
//	
//	protected void createToolItemPicking(ToolBar tBar) {
//		itemPicking = new ToolItem(tBar, SWT.PUSH);
//		itemPicking.setText(Message.getString("pvc.mlot_return"));
//		itemPicking.setImage(SWTResourceCache.getImage("receive"));
//		itemPicking.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				returnAdapter();
//			}
//		});
//	}
//	
//	protected void requestReturnAdapter() {
//		try {
//			WorkOrder workOrder = (WorkOrder) getAdObject();
//			List<MLot> pickMLots = (List) pickingForm.getTableManager().getCheckedObject();
//			// List<MLot> pickMLots = obs;
//			PvcManager pvcManager = Framework.getService(PvcManager.class);
//			if (pickMLots.size() < 1) {
//				UI.showError(Message.getString("com.data_transfer_data_no_selected"));
//				return;
//			}
//			pvcManager.returnRequestMLot2Third(workOrder, pickMLots, Env.getSessionContext());
//			UI.showInfo(Message.getString("common.operation_successed"));
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//	
//	protected void returnAdapter() {
//		try {
//			form.getMessageManager().removeAllMessages();
//			if (getAdObject() != null) {
//				boolean saveFlag = true;
//				for (IForm detailForm : getDetailForms()) {
//					if (!detailForm.saveToObject()) {
//						saveFlag = false;
//					}
//				}
//				if (saveFlag) {
//					List<MLot> mLots=new ArrayList<>();
//					List<MLot> pickMLots = (List) pickingForm.getTableManager().getInput();
//					WorkOrder workOrder = (WorkOrder) getAdObject();
//
//					if (pickMLots == null || pickMLots.size() == 0) {
//						UI.showError(Message.getString("mm.mainmlot_is_not_found"));
//						return;
//					}
//					mLots.addAll(pickMLots);
//					PvcManager pvcManager = Framework.getService(PvcManager.class);
//					pvcManager.returnMLotsToThird(workOrder, mLots, Env.getSessionContext());
//					UI.showInfo(Message.getString("common.operation_successed"));
//					refresh();
//				}
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//}
