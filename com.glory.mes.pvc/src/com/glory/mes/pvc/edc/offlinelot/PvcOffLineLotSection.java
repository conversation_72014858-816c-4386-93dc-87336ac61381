package com.glory.mes.pvc.edc.offlinelot;

import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.FontMetrics;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.EdcEntry;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.model.Step;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcManager;
import com.glory.mes.pvc.model.EdcDataMappingRule;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.ConstraintManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

public class PvcOffLineLotSection extends EntitySection {

	protected ToolItem itemDcop;
	protected ToolItem refersh;

//	protected SearchField txtDataSet;
	protected Text txtlotId;
	protected Lot lot;
	protected ADField eqpNameField;
	protected SearchField searchField;
	private RefTableField edcSetField;
	private SearchField txtEquipment;
	private static final String REFTABLE_ID = "PVCEdcDataMappingRule";
	private static final String FIELD_ID_EQUIPMENT = "equipmentId";
	private static final String FIELD_DISPLAY_TYPE = "reftable";

	public PvcOffLineLotSection(ADTable table) {
		super(table);
	}

	protected void createSectionContent(Composite client) {}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();

		GridLayout gl = new GridLayout(2, true);
		gl.horizontalSpacing = 20;
		gl.marginWidth = 0;
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.CENTER;
		gd.horizontalAlignment = SWT.CENTER;
		gd.horizontalSpan = 1;

		Composite top = toolkit.createComposite(client, SWT.NONE);
		top.setLayoutData(new GridData(GridData.CENTER));
		top.setLayout(gl);

		Label labLotId = toolkit.createLabel(top, Message
				.getString("wip.lot_id")
				+ "*");
		labLotId.setForeground(SWTResourceCache
				.getColor(SWTResourceCache.COLOR_TITLE));
		labLotId.setLayoutData(new GridData(100, 21));

		Color background = new Color(Display.getCurrent(), 255, 255, 255);

		txtlotId = toolkit.createText(top, "", SWT.BORDER);
		txtlotId.setBackground(background);
		
		gd = new GridData();
        gd.horizontalAlignment = SWT.FILL;
        gd.grabExcessHorizontalSpace = true;
        FontMetrics fm = AbstractField.getFontMetric(txtlotId);
        gd.widthHint = Dialog.convertWidthInCharsToPixels(fm, 32);
        txtlotId.setLayoutData(gd);
		txtlotId.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});

		Label labEqpId = toolkit.createLabel(top, Message
				.getString("edc.generalEdc_equipment_id")
				+ "*");
		labEqpId.setForeground(SWTResourceCache
				.getColor(SWTResourceCache.COLOR_TITLE));
		labEqpId.setLayoutData(new GridData(100, 21));

		txtEquipment = createCom(top, toolkit);
		
		Label lblOperator = toolkit.createLabel(top, Message
				.getString("edc.generalEdc_data_set")+ "*");//参数集名称
		lblOperator.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		lblOperator.setLayoutData(new GridData(100, 21));
		edcSetField = createEdcSetField(top,toolkit);
		// txtEquipment.setLayoutData(new GridData(200, 13));
		// txtEquipment.addFocusListener(new FocusListener() {
		// public void focusGained(FocusEvent e) {
		// }
		//
		// public void focusLost(FocusEvent e) {
		// Text tEqp = ((Text) e.widget);
		// tEqp.setText(tEqp.getText().toUpperCase());
		// }
		// });

//		Label lblEdcSet = toolkit.createLabel(top, Message
//				.getString("edc.generalEdc_data_set")
//				+ "*");
//		lblEdcSet.setForeground(SWTResourceCache
//				.getColor(SWTResourceCache.COLOR_TITLE));
//		lblEdcSet.setLayoutData(new GridData(100, 21));
//		
//		txtDataSet = createEdcSetField(top, toolkit);
//		txtDataSet.setLayoutData(new GridData(200, 13));
//		txtDataSet.addFocusListener(new FocusListener() {
//			public void focusGained(FocusEvent e) {
//			}
//
//			public void focusLost(FocusEvent e) {
//				Text tDataSet = ((Text) e.widget);
//				tDataSet.setText(tDataSet.getText().toUpperCase());
//			}
//		});
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		section.setText(Message.getString("edc.offline_collection_title"));
		createToolItemDcop(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void createToolItemRefresh(ToolBar tBar) {
		refersh = new ToolItem(tBar, SWT.PUSH);
		refersh.setText(Message.getString("common.refresh"));
		refersh.setImage(SWTResourceCache.getImage("refresh"));
		refersh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				txtlotId.setText("");
				txtEquipment.setValue(null);
//				txtDataSet.setValue(null);
			}
		});
	}

	protected void createToolItemDcop(ToolBar tBar) {
		itemDcop = new ToolItem(tBar, SWT.PUSH);
		itemDcop.setText(Message.getString("wip.dcop"));
		itemDcop.setImage(SWTResourceCache.getImage("dcop"));
		itemDcop.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				dcopAdapter(event);
			}
		});
	}

	protected void dcopAdapter(SelectionEvent event) {
		if (this.txtEquipment.getText() == null
				|| "".equals(this.txtEquipment.getText().toString().trim())) {
			UI.showError(Message.getString("edc.equipmentNumber_cannot_null"),
					Message.getString("edc.alert_message_title"));
			return;
		} /*else if (this.txtDataSet.getText() == null
				|| "".equals(this.txtDataSet.getText().trim())) {
			UI.showError(Message.getString("edc.data_set_cannot_null"), Message
					.getString("edc.alert_message_title"));
			return;
		} */else if (this.txtlotId.getText() == null
				|| "".equals(this.txtlotId.getText().trim())) {
			UI.showError(Message.getString("edc.lot_cannot_null"), Message
					.getString("edc.alert_message_title"));
		} else if (this.edcSetField.getText() == null || "".equals(this.edcSetField.getText().trim())) {
			UI.showError(Message.getString("edc.data_set_cannot_null"), Message.getString("edc.alert_message_title"));
			return;
		}

		Equipment eqp = null;
		try {
			RASManager rasManager = Framework.getService(RASManager.class);
			eqp = rasManager.getEquipmentByEquipmentId(Env
					.getOrgRrn(), this.txtEquipment.getText().toUpperCase());
			if (eqp == null) {
				UI.showError(Message.getString("edc.eqp_not_exist"), Message
						.getString("edc.alert_message_title"));
			}
		} catch (Exception e) {
			UI.showError(Message.getString("edc.eqp_not_exist"), Message
					.getString("edc.alert_message_title"));
			return;
		}

		try {
			String lotId = txtlotId.getText().trim();
			if (!isLotIdCaseSensitive()) {
				lotId = lotId.toUpperCase();
			}
			lot = LotProviderEntry.getLot(lotId);
			if (lot == null) {
				UI.showError(Message.getString("edc.lot_not_exist"), Message
						.getString("edc.alert_message_title"));
				return;
			}
		} catch (Exception e) {
			UI.showError(Message.getString("edc.lot_not_exist"), Message
					.getString("edc.alert_message_title"));
			return;
		}

		try {
			//检查工序权限
			PvcManager pvcManager = Framework.getService(PvcManager.class);
			ConstraintManager constraintManager = Framework.getService(ConstraintManager.class);
			
			Step step = pvcManager.getStepByEquipmentIdCache(Env.getOrgRrn(), eqp.getEquipmentId());
			Lot checkLot = new Lot();
			checkLot.setStepName(step.getName());
			constraintManager.checkStepAuthority(Env.getOrgRrn(), Env.getUserName(), Lists.newArrayList(checkLot), true);
			
			//检查设备对应的采集项
//			List<EdcDataMappingRule> rules = pvcManager.getEdcDataMappingRule(Env.getOrgRrn(), eqp.getEquipmentId(),
//					EdcDataMappingRule.DATA_TYPE_MEASURE, EdcDataMappingRule.EDCFROM_OFFLINELOT);
//			if (CollectionUtils.isEmpty(rules)) {
//				UI.showError(Message.getString("edc.data_no_mapping_equipment"));
//				return;
//			}
			
			BASManager basManager = Framework.getService(BASManager.class);
//			AbstractEdcSet abstractEdcSet = (AbstractEdcSet) edcSetField.getData();
			AbstractEdcSet itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcItemSet.class,
					edcSetField.getValue().toString());
//			if (abstractEdcSet instanceof EdcItemSet) {
//				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcItemSet.class, abstractEdcSet.getName());
//			} else if (abstractEdcSet instanceof EdcTextSet) {
//				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcTextSet.class, abstractEdcSet.getName());
//			} else if (abstractEdcSet instanceof EdcBinSet) {
//				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcBinSet.class, abstractEdcSet.getName());
//			}
			if (itemSet == null) {
				UI.showError(Message.getString("edc.data_set_cannot found"), Message.getString("edc.alert_message_title"));
			}  else {
				lot.setAttribute1(txtEquipment.getText().trim().toUpperCase());
				EdcSetCurrent edcCurrent = new EdcSetCurrent();
				edcCurrent.setItemSetRrn(itemSet.getObjectRrn());
				
				EdcEntry.open(EdcData.EDCFROM_OFFLINELOT, edcCurrent, null, lot);
			}
		} catch (Exception e) {
			UI.showError(Message.getString("edc.data_set_cannot found"),
					Message.getString("edc.alert_message_title"));
		}
	}

	public SearchField createCom(Composite top, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADRefTable refTable = new ADRefTable();
			for (ADField adField : table.getFields()) {
				if (FIELD_ID_EQUIPMENT.equals(adField.getName())
						&& FIELD_DISPLAY_TYPE.equals(adField
								.getDisplayType())) {
					eqpNameField = adField;
				}
			}
			refTable.setObjectRrn(eqpNameField.getReftableRrn());
			refTable = (ADRefTable) adManager.getEntity(refTable);
//			if (Env.isUseLine()) {
//				List<EquipmentLine> eLines = new ArrayList<EquipmentLine>();
//				List<EquipmentLine> list = entityManager.getEntityList(Env
//						.getOrgRrn(), EquipmentLine.class, Env.getMaxResult(),
//						" lineId " + Env.getLineClause(), "");
//				eLines.addAll(list);
//				String condition = "";
//				for (EquipmentLine el : eLines) {
//					condition = condition + " '" + el.getEquipmentRrn() + "',";
//				}
//				if (condition.length() > 0) {
//					condition = condition.substring(0, condition.length() - 1);
//					condition = " objectRrn in(" + condition + ")";
//					refTable.setWhereClause(condition);
//				}
//			}

			ADTable adTable = adManager.getADTable(refTable.getTableRrn());
			int mStyle = SWT.BORDER;
			searchField = new SearchField("", adTable, refTable, "", mStyle);
			searchField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					if (searchField.getValue() != null
							&& ((String) searchField.getValue()).trim()
									.length() > 0) {
						setEdcSetValue(searchField.getValue().toString());
						refresh();
					}
				}
			});
			searchField.createContent(top, toolkit);
		} catch (Exception e1) {
		}
		return searchField;
	}

	public RefTableField createEdcSetField(Composite top , FormToolkit toolkit){
		try{
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), REFTABLE_ID);
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("edcSetName");
			adRefTable.setTextField("edcSetName");	
			ListTableManager tableManager = new ListTableManager(adTable);
			edcSetField = new RefTableField("", tableManager, adRefTable);
			edcSetField.setADManager(adManager);
		} catch(Exception e) {
		}
		edcSetField.createContent(top, toolkit);
		return edcSetField;
	}
	
	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
	public void setEdcSetValue(String equipmentId) {
		try{
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			List<EdcDataMappingRule> rules = adManager.getEntityList(Env.getOrgRrn(), EdcDataMappingRule.class, Integer.MAX_VALUE, "equipmentId = '" + equipmentId + "'", "");
			edcSetField.setInput(rules);
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
}
