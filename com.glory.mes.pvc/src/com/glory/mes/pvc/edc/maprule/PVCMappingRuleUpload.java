package com.glory.mes.pvc.edc.maprule;

import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.glory.edc.model.EdcItemSet;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.model.EdcDataMappingRule;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.port.Port;
import com.google.common.collect.Lists;

public class PVCMappingRuleUpload extends Upload {
	
	public PVCMappingRuleUpload(String name) {
		super(name);
	}

	@Override
	protected void cudEntityList() {
		try {
			List<ADBase> uploadList = progress.getUploadList();
			List<ADBase> deleteList = progress.getDeleteList();
			RASManager rasManager = Framework.getService(RASManager.class);
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");

			// 当前只支持一个记录一个事务，后续会增加要么全部成功要么全部失败的事务处理
			List<EdcDataMappingRule> rules = Lists.newArrayList();
			if (uploadList != null && uploadList.size() > 0) {
				for (int i = 0; i < uploadList.size(); i++) {
					EdcDataMappingRule rule = (EdcDataMappingRule) uploadList.get(i);
					if (rule.getEquipmentId() == null || StringUtils.isEmpty(rule.getEquipmentId().trim())
							|| rule.getEdcSetName() == null || StringUtils.isEmpty(rule.getEdcSetName())) {
						UI.showError(Message.getString("prd.transition_name_null"));
						return;
					}
//					List<Equipment> equipments = adManager.getEntityList(Env.getOrgRrn(), Equipment.class,
//							Integer.MAX_VALUE, " equipmentId = '" + rule.getEquipmentId() + "' ", null);
					List<EdcItemSet> items = adManager.getEntityList(Env.getOrgRrn(), EdcItemSet.class,
							Integer.MAX_VALUE, " NAME = '" + rule.getEdcSetName() + "' ", null);
					if (items == null || items.size() < 1) {
						UI.showError(Message.getString("common.line_is_not_null") + ": " + rule.getEdcSetName());
						return;
					}
//					Equipment equipment = equipments.get(0);
					EdcItemSet item =items.get(0);
//					rule.setEquipmentDesc(equipment.getDescription());
					rule.setEdcSetRrn(item.getObjectRrn());
					rules.add(rule);
//					adManager.saveEntity(rule, Env.getSessionContext());
				}
			}
			adManager.saveEntityList(rules, Env.getSessionContext());

			if (deleteList != null && deleteList.size() > 0) {
				for (int i = 0; i < uploadList.size(); i++) {
					rasManager.deletePort((Port) uploadList.get(i), Env.getSessionContext());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

}
