package com.glory.mes.pvc.andon;

import java.util.List;
import java.util.Optional;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pvc.andon.model.AndonAlarmSetup;
import com.glory.mes.pvc.client.PvcADManager;

public class PvcAndonSetupCreateDialog extends GlcBaseDialog { 
	
	private static final String ANDON_CATEGORY_MANUAL_TRIGGER = "ManualTrigger";

	private static final String FIELD_ANDONSETUPQUERY = "andonSetupQuery";
	private static final String FIELD_CATEGORY = "category";
	private static final String FIELD_EQUIPMENTTYPE = "equipmentType";
	private static final String FIELD_EQUIPMENTID = "equipmentId";
	private static final String FIELD_ALARMCATEGORY = "alarmCategory";
	private static final String FIELD_ALARMTYPE = "alarmType";
	private static final String FIELD_ISTRIGGERALARM = "isTriggerAlarm";
	private static final String FIELD_ISTRIGGERANDON = "isTriggerAndon";
	private static final String FIELD_ISPUSHPDA = "isPushPDA";
	private static final String FIELD_PUSHUSER = "pushUser";
	private static final String FIELD_PUSHUSERGROUP = "pushUserGroup";

	protected EntityFormField andonSetupQueryField;
	protected RefTableField categoryField;
	protected RefTableField equipmentTypeField;
	protected RefTableField equipmentIdField;
	protected TextField alarmCategoryField;
	protected TextField alarmTypeField;
	protected BooleanField isTriggerAlarmField;
	protected BooleanField isTriggerAndonField;
	protected BooleanField isPushPDAField;
	protected RefTableField pushUserField;
	protected RefTableField pushUserGroupField;
	
	private List<AndonAlarmSetup> andonSetups;
	
	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;

	public PvcAndonSetupCreateDialog(String adFormName, String authority, IEventBroker eventBroker, List<AndonAlarmSetup> andonSetups) {
		super(adFormName, authority, eventBroker);
		this.andonSetups = andonSetups;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		andonSetupQueryField = form.getFieldByControlId(FIELD_ANDONSETUPQUERY, EntityFormField.class);
		categoryField = andonSetupQueryField.getFieldByControlId(FIELD_CATEGORY, RefTableField.class);
		equipmentTypeField = andonSetupQueryField.getFieldByControlId(FIELD_EQUIPMENTTYPE, RefTableField.class);
		equipmentIdField = andonSetupQueryField.getFieldByControlId(FIELD_EQUIPMENTID, RefTableField.class);
		alarmCategoryField = andonSetupQueryField.getFieldByControlId(FIELD_ALARMCATEGORY, TextField.class);
		alarmTypeField = andonSetupQueryField.getFieldByControlId(FIELD_ALARMTYPE, TextField.class);
		isTriggerAlarmField = andonSetupQueryField.getFieldByControlId(FIELD_ISTRIGGERALARM, BooleanField.class);
		isTriggerAndonField = andonSetupQueryField.getFieldByControlId(FIELD_ISTRIGGERANDON, BooleanField.class);
		isPushPDAField = andonSetupQueryField.getFieldByControlId(FIELD_ISPUSHPDA, BooleanField.class);
		pushUserField = andonSetupQueryField.getFieldByControlId(FIELD_PUSHUSER, RefTableField.class);
		pushUserGroupField = andonSetupQueryField.getFieldByControlId(FIELD_PUSHUSERGROUP, RefTableField.class);
		
		categoryField.addValueChangeListener(categoryListener);
		isPushPDAField.addValueChangeListener(isPushPDAListener);
		init();
	}
	
	private void init() {
		pushUserField.setEnabled(false);
		pushUserGroupField.setEnabled(false);
		pushUserField.refresh();
		pushUserGroupField.refresh();
	}
	
	IValueChangeListener categoryListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			try {
				if (StringUtils.equals(categoryField.getValue().toString(), ANDON_CATEGORY_MANUAL_TRIGGER)) {
					alarmCategoryField.setEnabled(true);
					alarmTypeField.setEnabled(true);
					alarmCategoryField.refresh();
					alarmTypeField.refresh();
				} else {
					alarmCategoryField.setValue(null);
					alarmTypeField.setValue(null);
					alarmCategoryField.setEnabled(false);
					alarmTypeField.setEnabled(false);
					alarmCategoryField.refresh();
					alarmTypeField.refresh();
				}
			} catch (Exception e) {
	            e.printStackTrace();
	        }
		}
	};
	
	IValueChangeListener isPushPDAListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			try {
				boolean isPushPDA = Boolean.valueOf(isPushPDAField.getValue().toString());
				if (isPushPDA) {
					pushUserField.setEnabled(true);
					pushUserGroupField.setEnabled(true);
					pushUserField.refresh();
					pushUserGroupField.refresh();
				} else {
					pushUserField.setValue(null);
					pushUserGroupField.setValue(null);
					pushUserField.setEnabled(false);
					pushUserGroupField.setEnabled(false);
					pushUserField.refresh();
					pushUserGroupField.refresh();
				}
			} catch (Exception e) {
	            e.printStackTrace();
	        }
		}
	};
	
	@Override
	protected void okPressed() {
		try {
			if (!andonSetupQueryField.validate()) {
				UI.showWarning(Message.getString("warn.required_entry"));
				return;
			}
			boolean isTriggerAlarm = Boolean.valueOf(isTriggerAlarmField.getValue().toString());
			boolean isTriggerAndon = Boolean.valueOf(isTriggerAndonField.getValue().toString());
			boolean isPushPDA = Boolean.valueOf(isPushPDAField.getValue().toString());
			if (!isTriggerAlarm && !isTriggerAndon && !isPushPDA) {
				UI.showError(Message.getString("pvc.please_select_at_least_one_action"));
				return;
			}
			//检查不能重复定义(安灯类型、设备类型、设备ID相同，包括null的情况)
			String eqpType = equipmentTypeField.getValue() == null ? null : equipmentTypeField.getValue().toString();
			String eqpId = equipmentIdField.getValue() == null ? null : equipmentIdField.getValue().toString();
			Optional<AndonAlarmSetup> andonSetupOptional = null;
			if (StringUtils.equals(ANDON_CATEGORY_MANUAL_TRIGGER, categoryField.getValue().toString())) {
				String alarmCategory = alarmCategoryField.getValue() == null ? null : alarmCategoryField.getValue().toString();
				String alarmType = alarmTypeField.getValue() == null ? null : alarmTypeField.getValue().toString();
				if (StringUtils.isEmpty(alarmCategory)) {
					UI.showWarning("<AlarmType>" + Message.getString("warn.required_entry"));
					return;
				}
				//若是手动触发异常类型还需加上警报类别和警报类型校验重复
				andonSetupOptional = andonSetups.stream().filter(a -> StringUtils.equals(categoryField.getValue().toString(), a.getCategory()) 
						&& StringUtils.equals(eqpType, a.getEquipmentType()) && StringUtils.equals(eqpId, a.getEquipmentId()) 
						&& StringUtils.equals(alarmCategory, a.getAlarmCategory()) && StringUtils.equals(alarmType, a.getAlarmType())).findAny();
			} else {
				andonSetupOptional = andonSetups.stream().filter(a -> StringUtils.equals(categoryField.getValue().toString(), a.getCategory()) 
						&& StringUtils.equals(eqpType, a.getEquipmentType()) && StringUtils.equals(eqpId, a.getEquipmentId())).findAny();
			}
			if (andonSetupOptional.isPresent()) {
				UI.showError(Message.getString("common.duplicate_definition"));
				return;
			}
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			AndonAlarmSetup setup = (AndonAlarmSetup) andonSetupQueryField.getValue();
			adManager.saveEntity(setup, Env.getSessionContext());
			UI.showInfo(Message.getString("common.save_successed"));
		} catch (Exception e) {
            e.printStackTrace();
        }
		super.okPressed();
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
}