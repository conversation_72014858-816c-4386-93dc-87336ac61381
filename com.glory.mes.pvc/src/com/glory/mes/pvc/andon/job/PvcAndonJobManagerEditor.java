package com.glory.mes.pvc.andon.job;

import java.util.Objects;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.andon.client.PvcAnDonManager;
import com.glory.mes.pvc.andon.model.AndonJob;

public class PvcAndonJobManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.andon.job.PvcAndonJobManagerEditor";
	
	private static final String AD_FORM_NAME = "PvcAndonJobDialog";

	private static final String FIELD_ANDONJOBQUERY = "andonJobQuery";

	private static final String BUTTON_RESPOND = "respond";
	private static final String BUTTON_CONFIRM = "confirm";
	private static final String BUTTON_COMPLETE = "complete";

	protected QueryFormField andonJobQueryField;
	
	private ToolItem itemRespond;
	private ToolItem itemConfirm;
	private ToolItem itemComplete;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		andonJobQueryField = form.getFieldByControlId(FIELD_ANDONJOBQUERY, QueryFormField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RESPOND), this::respondAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CONFIRM), this::confirmAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_COMPLETE), this::completeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_REFRESH), this::refreshAdapter);
		
		subscribeAndExecute(eventBroker, andonJobQueryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		initItem();
	}
	
	private void initItem() {
		itemRespond = (ToolItem) form.getButtonByControl(null, BUTTON_RESPOND);
		itemConfirm = (ToolItem) form.getButtonByControl(null, BUTTON_CONFIRM);
		itemComplete = (ToolItem) form.getButtonByControl(null, BUTTON_COMPLETE);
	}

	private void respondAdapter(Object object) {
		try {
			AndonJob job = (AndonJob) andonJobQueryField.getQueryForm().getSelectedObject();
			if (Objects.isNull(job)) {
				UI.showError(Message.getString("common.select_record"));
				return;
			}
			job.setRespondOperator(Env.getUserName());
			PvcAnDonManager andonManager = Framework.getService(PvcAnDonManager.class);
			andonManager.respondAndonJob(job, Env.getSessionContext());
			//发送给DC关闭安灯
			andonManager.sendAndonMessage2DC(job, Env.getSessionContext());
			UI.showInfo(Message.getString("com_success"));
			refreshAdapter(object);
		} catch (Exception e) {
            e.printStackTrace();
        }
	}

	private void confirmAdapter(Object object) {
		try {
			AndonJob job = (AndonJob) andonJobQueryField.getQueryForm().getSelectedObject();
			if (Objects.isNull(job)) {
				UI.showError(Message.getString("common.select_record"));
				return;
			}
			
			PvcAndonJobDialog dialog = new PvcAndonJobDialog(AD_FORM_NAME, null, eventBroker);
			if (Dialog.OK == dialog.open()) {
				String confirmOperator = dialog.operatorField.getValue() == null ? null : dialog.operatorField.getValue().toString();
				String confirmComment = dialog.commentField.getValue() == null ? null : dialog.commentField.getValue().toString();
				job.setConfirmOperator(confirmOperator);
				job.setConfirmComment(confirmComment);
				
				PvcAnDonManager andonManager = Framework.getService(PvcAnDonManager.class);
				andonManager.confirmAndonJob(job, Env.getSessionContext());
				UI.showInfo(Message.getString("com_success"));
				refreshAdapter(object);
			}
		} catch (Exception e) {
            e.printStackTrace();
        }
	}

	private void completeAdapter(Object object) {
		try {
			AndonJob job = (AndonJob) andonJobQueryField.getQueryForm().getSelectedObject();
			if (Objects.isNull(job)) {
				UI.showError(Message.getString("common.select_record"));
				return;
			}
			
			PvcAndonJobDialog dialog = new PvcAndonJobDialog(AD_FORM_NAME, null, eventBroker);
			if (Dialog.OK == dialog.open()) {
				String completeOperator = dialog.operatorField.getValue() == null ? null : dialog.operatorField.getValue().toString();
				String completeComment = dialog.commentField.getValue() == null ? null : dialog.commentField.getValue().toString();
				job.setCompleteOperator(completeOperator);
				job.setCompleteComment(completeComment);
				
				PvcAnDonManager andonManager = Framework.getService(PvcAnDonManager.class);
				andonManager.completeAndonJob(job, Env.getSessionContext());
				UI.showInfo(Message.getString("com_success"));
				refreshAdapter(object);
			}
		} catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	private void refreshAdapter(Object object) {
		andonJobQueryField.refresh();
	}
	
	private void selectionChanged(Object object) {
		AndonJob job = (AndonJob) andonJobQueryField.getQueryForm().getSelectedObject();
		if (Objects.isNull(job)) {
			return;
		}
		if (AndonJob.ANDO_JOB_STATUS_START.equals(job.getStatus())) {
			itemRespond.setEnabled(true);
			itemConfirm.setEnabled(false);
			itemComplete.setEnabled(false);
		} else if (AndonJob.ANDO_JOB_STATUS_RESPOND.equals(job.getStatus())) {
			itemRespond.setEnabled(false);
			itemConfirm.setEnabled(true);
			itemComplete.setEnabled(false);
		} else if (AndonJob.ANDO_JOB_STATUS_CONFIRM.equals(job.getStatus())) {
			itemRespond.setEnabled(false);
			itemConfirm.setEnabled(false);
			itemComplete.setEnabled(true);
		} else if (AndonJob.ANDO_JOB_STATUS_COMPLETE.equals(job.getStatus())) {
			itemRespond.setEnabled(false);
			itemConfirm.setEnabled(false);
			itemComplete.setEnabled(false);
		} else {
			itemRespond.setEnabled(true);
			itemConfirm.setEnabled(true);
			itemComplete.setEnabled(true);
		}
	}

}