package com.glory.mes.pvc.sample.manual.data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IFormPart;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.ResultDialog;
import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcResult;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.sampling.ManualSamplingData;
import com.glory.edc.model.sampling.ManualSamplingPlan;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.entitymanager.interceptor.SectionInterceptor;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Maps;


public class PvcManualSampleDataProperties extends EntityProperties {
	
	public static final String TABLE_NAME_PREFIX = "EDCManualSP";
	public static final String DEFAULT_TABLE_NAME = TABLE_NAME_PREFIX + "Default";

	protected Lot lot;
	protected AbstractEdcSet edcSet;
	protected EdcResult dcResult;
	
	private Composite child;
	private EntityForm dataForm;
	private ADTable dataTable;
	private ToolItem itemDcop;
	private ToolItem itemHis;
	
	private Section extendSection;

	public PvcManualSampleDataProperties() {
        super();
    }
	
	public void createToolBar(Section section) {
		if (interceptor != null) {
			if (interceptor.process(SectionInterceptor.ITEM_TOOLBAR, this)) {
				return;
			}
		}
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDcop(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemHis(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemDcop(ToolBar tBar) {
		itemDcop = new ToolItem(tBar, SWT.PUSH);
		itemDcop.setText(Message.getString("wip.dcop"));
		itemDcop.setImage(SWTResourceCache.getImage("dcop"));
		itemDcop.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				dcopAdapter();
			}
		});
	}
	
	protected void dcopAdapter() {
		try {
			dataForm.removeAllMessages();
			if (dataForm.saveToObject()) {
				ManualSamplingPlan samplingPlan = (ManualSamplingPlan) getAdObject();
				if (samplingPlan == null || samplingPlan.getObjectRrn() == null) {
					UI.showError(Message.getString("common.select_left_object"));
					return;
				}
				ManualSamplingData samplingData = (ManualSamplingData) dataForm.getObject();
				samplingData.setSamplePlanName(samplingPlan.getName());
				samplingData.setSamplePlanRrn(samplingPlan.getObjectRrn());
				samplingData.setSamplePlanDesc(samplingPlan.getDescription());
				samplingData.setSamplePlanType(samplingPlan.getSampleType());
				samplingData.setSampleSize(samplingPlan.getSampleSize());
				
				EdcSetCurrent setCurrent = new EdcSetCurrent();
				LotManager lotManager = Framework.getService(LotManager.class);
				ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
				if(StringUtils.isNotEmpty(samplingData.getLotId())) {
					lot = lotManager.getLotByLotId(Env.getOrgRrn(), samplingData.getLotId(), true);
					setCurrent.setLotRrn(lot.getObjectRrn());
				}else {
					lot = new Lot();
				}
				lot.setEquipmentId(samplingData.getEquipmentId());
				setCurrent.setItemSetRrn(edcSet.getObjectRrn());
				setCurrent.setItemSetName(edcSet.getName());
				
				EdcEvent edcEvent = new EdcEvent();
				edcEvent.setLot(lot);
				edcEvent.setEdcFrom(EdcData.EDCFROM_OFFLINELOT);
				edcEvent.setEdcSet(edcSet);
				
				//防止懒加载异常
				if (edcSet != null) {
					if (edcSet instanceof EdcItemSet) {
						EdcItemSet itemSet = (EdcItemSet) edcSet;
						if (itemSet.getItemSetLines() != null) {
							for (EdcItemSetLine line1 : itemSet.getItemSetLines()) {
								//多对一关系，防止重复查询
								Map<String, EdcItem> edcItemMap = Maps.newHashMap();
								if (line1.getEdcItem() != null && !edcItemMap.containsKey(line1.getItemRrn().toString())) {
									EdcItem edcItem = new EdcItem();
									edcItem.setObjectRrn(line1.getItemRrn());
									edcItem = (EdcItem) adManager.getEntity(edcItem);
									edcItemMap.put(edcItem.getObjectRrn().toString(), edcItem);
									line1.setEdcItem(edcItem);
								} else {
									if (line1.getItemRrn() != null) {
										line1.setEdcItem(edcItemMap.get(line1.getItemRrn().toString()));
									}
								}
							}
						}
					}
				}
				PvcManualSampleDataDialog jobDialog = new PvcManualSampleDataDialog(
						UI.getActiveShell(), edcSet, edcEvent, samplingData, samplingPlan.getIsManualJudge());
				if (jobDialog.open() == Dialog.OK){
					//显示结果
					if (jobDialog != null && jobDialog.getReturnCode() == Dialog.OK && !jobDialog.getIsTemp()) {
						ResultDialog spcDialog = new ResultDialog(UI.getActiveShell(), 
								edcEvent.getEdcFrom(), edcEvent.getEdcSet(), jobDialog.getDcDatas(), jobDialog.getDcResult());
						spcDialog.open();
						
						newAdapter();
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			form.getMessageManager().setAutoUpdate(false);
		}
	}
	
	protected void createToolItemHis(ToolBar tBar) {
		itemHis = new ToolItem(tBar, SWT.PUSH);
		itemHis.setText(Message.getString("common.result_query"));
		itemHis.setImage(SWTResourceCache.getImage("search"));
		itemHis.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				hisAdapter();
			}
		});
	}
	
	protected void hisAdapter() {
		try {
			ManualSamplingPlan plan = (ManualSamplingPlan) getAdObject();
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			if (plan != null && plan.getObjectRrn() != null) {
				PvcManualSampleDataQueryDialog dialog = new PvcManualSampleDataQueryDialog(
						UI.getActiveShell(), adManager, plan.getName());
				dialog.open();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void selectionChanged(IFormPart part, ISelection selection) {
		super.selectionChanged(part, selection);
		try {
			ManualSamplingPlan samplingPlan = (ManualSamplingPlan) getAdObject();
			if (samplingPlan == null || samplingPlan.getObjectRrn() == null) {
				return;
			}
			EDCManager edcManager = Framework.getService(EDCManager.class);
			if(samplingPlan.getEdcSetRrn() != null) {
				edcSet = edcManager.getActualEdcSet(samplingPlan.getEdcSetRrn(), null, null);
			}
			if (edcSet == null) {
				UI.showError(Message.getString("edc.manual_sampling_edc_set_error"));
				return;
			}
			
			// 不能为null（需要先设置默认动态表）
			dataTable = getDataTable(samplingPlan.getName());
			buildView();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private ADTable getDataTable(String planName) {
		ADTable adTable = null;
    	try {
    		String tableName = TABLE_NAME_PREFIX + planName;
    		adTable  = getADManger().getADTable(Env.getOrgRrn(), tableName);
		} catch (Exception e) {
		}
    	
    	if (adTable == null) {
    		try {
        		adTable  = getADManger().getADTable(Env.getOrgRrn(), DEFAULT_TABLE_NAME);
    		} catch (Exception e) {
    		}
    	}
    	return adTable;
    }

	public void createSectionTab(Composite client, ADTab tab) {
		List<ADField> adfields = tab.getFields();
		if (adfields.size() > 0) {
			final FormToolkit toolkit = form.getToolkit(); 
			
			Composite composite = toolkit.createComposite(client);
			GridLayout layout = new GridLayout();
			layout.horizontalSpacing = 0;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			layout.verticalSpacing = 0;
			composite.setLayout(layout);
			
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			if (tab.getHeightHint() != null) {
				gd.heightHint = tab.getHeightHint().intValue();
			} 
			gd.grabExcessHorizontalSpace = true;
			composite.setLayoutData(gd);
			
			Section section = toolkit.createSection(client, Section.NO_TITLE | FFormSection.EXPANDED);
			section.setText(I18nUtil.getI18nMessage(tab, "label"));
			gd = new GridData(GridData.FILL_BOTH);
			section.setLayoutData(gd);
			
			if ("ActionForm".equals(tab.getName())) {
				GridData data = new GridData(GridData.FILL_BOTH);
				data.heightHint = 500;
				section.setLayoutData(data);
				this.extendSection = section;
				buildView();
			} else {
				Composite sectionClient = toolkit.createComposite(section);
				sectionClient.setLayout(new FillLayout());
				gd = new GridData(GridData.FILL_BOTH);
				gd.grabExcessHorizontalSpace = true;
				sectionClient.setLayoutData(gd);
				
				IForm itemForm = getForm(sectionClient, tab);
				getDetailForms().add(itemForm);
				
				section.setClient(sectionClient);
			}
		}
	}
    
    protected void buildView() {
    	if (child != null) {
    		child.dispose();
    	}
    	final FormToolkit toolkit = form.getToolkit();
    	child = toolkit.createComposite(extendSection);
    	
		GridLayout gd = new GridLayout(1, true);
		gd.horizontalSpacing = 0;
    	
		child.setLayout(gd);
		child.setLayoutData(new GridData(GridData.FILL_BOTH));

		createForm();
		
		toolkit.paintBordersFor(extendSection);
		extendSection.setClient(child);
		
		extendSection.redraw();
		extendSection.layout();
    }
    
    @SuppressWarnings("unchecked")
	protected void createForm() {
    	dataForm = new EntityForm(child, SWT.NONE, new ManualSamplingData(), dataTable, mmng);
    	if(getAdObject() != null) {
    		ManualSamplingPlan samplingPlan = (ManualSamplingPlan) getAdObject();
    		if(samplingPlan.getReserved1() != null) {
    			try {
    				List<Equipment> eqpList =  ((RefTableField)dataForm.getFields().get("equipmentId")).getInput();
    				eqpList = eqpList.stream().filter(p -> samplingPlan.getReserved1().equals(p.getEqpType())).collect(Collectors.toList());
    				((RefTableField)dataForm.getFields().get("equipmentId")).setInput(eqpList);
    			}catch (Exception e) {
    				
    			}
    			
    		}
    	}
    	
		dataForm.setLayoutData(new GridData(GridData.FILL_BOTH));
    }
    
    @Override
    protected void newAdapter() {
    	dataForm.removeAllMessages();
    	dataForm.setObject(new ManualSamplingData());
    	dataForm.loadFromObject();
    	dataForm.refresh();
    	dataForm.getMessageManager().setAutoUpdate(false); 
    }
}
