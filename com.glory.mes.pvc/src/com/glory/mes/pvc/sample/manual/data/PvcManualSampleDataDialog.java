package com.glory.mes.pvc.sample.manual.data;

import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Shell;

import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.sampling.ManualSamplingData;
import com.glory.edc.offlinelot.OffLineLotDialog;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.andon.client.PvcAnDonManager;
import com.glory.mes.pvc.andon.model.AndonAlarmSetup;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.client.PvcLotManager;
import com.glory.mes.pvc.model.PvcCode;

public class PvcManualSampleDataDialog extends OffLineLotDialog {
	
	private static final String JUDGE_TABLE_NAME = "EDCManualSampleDataJudge";
	
	private ManualSamplingData samplingData;
	private boolean isManualJudge;
	

	public PvcManualSampleDataDialog(Shell parent, AbstractEdcSet itemSet, EdcEvent event,
			ManualSamplingData samplingData, Boolean isManualJudge) {
		super(parent, itemSet, event);
		this.samplingData = samplingData;
		this.isManualJudge = isManualJudge;
	}

	@Override
	public void saveData(List<EdcData> datas) throws Exception {
		try {
			if (isManualJudge) {
				PvcManualSampleDataJudgeDialog judgeDialog = new PvcManualSampleDataJudgeDialog(
						getJudgeTable(), new ManualSamplingData());
				if (judgeDialog.open() == Dialog.OK) {
					ManualSamplingData judgeData = (ManualSamplingData) judgeDialog.getAdObject();
					samplingData.setJudge1(judgeData.getJudge1());
					samplingData.setComments(judgeData.getComments());
				} else {
					return;
				}
			}
			PvcLotManager pvcLotManager = Framework.getService(PvcLotManager.class);
			dcResult = pvcLotManager.manualSampeling(samplingData, lot, datas, Env.getSessionContext());
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			if (PvcCode.isTriggerAndon(Env.getOrgRrn(), sysParamManager) && (dcResult.getIsOoc() || dcResult.getIsOos())) {
				//触发OOS/OOC需触发安灯
				PvcAnDonManager andonManager = Framework.getService(PvcAnDonManager.class);
				andonManager.triggerAndonRequest(samplingData.getEquipmentId(), AndonAlarmSetup.CATEGORY_OFFLINE_DATA_EXCEED_LIMIT, lot.getLotId(), Env.getSessionContext());
			}
			
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void saveAdapter() {
		try {
			isTemp = false;
			
			if (!edcForm.validate()) {
				return;
			}
			
			List<EdcData> datas = edcForm.getEdcDatas();
			//检查强制输入
			if (!checkMandatory(datas)) {
				return;
			}
			
			if((edcSet instanceof EdcItemSet) || (edcSet instanceof EdcTextSet)){
				this.dcDatas = removeEmptyEdcData(datas);
			} else {
				this.dcDatas = datas;
			}
		
			if (dcDatas == null || dcDatas.size() < 1){
				//不允许所有采集项采集数据为空
				UI.showError(Message.getString("edc.cannot_save"), Message.getString("edc.alert_message_title"));
				return;
			}
			
			for (EdcData data: dcDatas) {
				data.setIsTemp(false);
				data.setObjectRrn(null);
				if (edcCurrent != null) {
					if (EdcSetCurrent.TEST_TYPE_RETEST.equals(edcCurrent.getTestType())) {
						data.setIsRetest(EdcSetCurrent.TEST_TYPE_RETEST);
					}
				} else {
					data.setIsRetest(EdcSetCurrent.TEST_TYPE_NORMAL);
				}
			}  
			saveData(dcDatas);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private ADTable getJudgeTable() {
		ADTable adTable = null;
    	try {
    		ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
    		adTable  = adManager.getADTable(Env.getOrgRrn(), JUDGE_TABLE_NAME);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	
    	return adTable;
    }
}
