package com.glory.mes.pvc.tool;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.pvc.model.PvcCode;
import com.glory.mes.ras.consumable.tool.action.ToolActionDialog;

public class PvcToolUnshipDialog extends ToolActionDialog {
	
	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;

	private static final String FIELD_TOOLACTION = "toolAction";
	private static final String FIELD_TOOLMULITLIST = "toolMulitList";
	private static final String FIELD_TARGETWAREHOUSERRN = "targetWarehouseRrn";

	protected EntityFormField toolActionField;
	protected ListTableManagerField toolMulitListField;
	protected RefTableField targetWarehouseRrnField;
	
	protected List<Tool> tools;
	
	private static final String EVENT_ID_RETURN = "CONFIRMRETURN";

	public PvcToolUnshipDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}
	
	public PvcToolUnshipDialog(String adFormName, String authority, IEventBroker eventBroker, List<Tool> tools) {
		super(adFormName, authority, eventBroker);
		this.tools = tools;
		setToolsList(tools);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		toolActionField = form.getFieldByControlId(FIELD_TOOLACTION, EntityFormField.class);
		toolMulitListField = form.getFieldByControlId(FIELD_TOOLMULITLIST, ListTableManagerField.class);
		
		targetWarehouseRrnField = toolActionField.getFieldByControlId(FIELD_TARGETWAREHOUSERRN, RefTableField.class);
		
		initLot();
	}

	@Override
	public void initLot() {
		try {
			((CheckBoxTableViewerManager)toolMulitListField.getListTableManager().getTableManager()).setCheckBoxDisableListener(new ICheckBoxDisableListener() {
    			public boolean isDisable(Object object) {
    				MLot lot = (MLot)object;
    				if (lot.getConstraintFlag()) {
    					return true;
    				}
    				return false;
    			}
    		});
			toolMulitListField.getListTableManager().setInput(tools);
      		if (tools != null && tools.size() > 0) {
      			List<MLot> validLots = new ArrayList<MLot>();
      			for (MLot lot : tools) {
      				if (!lot.getConstraintFlag()) {
      					validLots.add(lot);
    				}
          		}
      			toolMulitListField.getListTableManager().getCheckedObject().addAll(validLots);
      			toolMulitListField.getListTableManager().refresh();
      		}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	protected void okPressed() {
		try {
			List<Object> objects = toolMulitListField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showError(Message.getString("ras.not_select_tool"));
				return;
			}
			tools = objects.stream().map(o -> ((Tool)o)).collect(Collectors.toList());
			if (toolActionField.validate()) {
				MMManager mmManager = Framework.getService(MMManager.class);
				for(MLot mLot : tools) {
					Tool tool = (Tool) mLot;
					tool.setTransWarehouseRrn(Long.parseLong(targetWarehouseRrnField.getValue().toString()));
					
					// 不支持多储位
					List<MLotStorage> storages = mmManager.getLotStorages(tool.getObjectRrn());
					
					if (CollectionUtils.isNotEmpty(storages)) {
						if (storages.size() > 1) {
							UI.showWarning(Message.getString("wms.lot_in_multi_warehouse_or_storage"));
							return;
						}
						MLotStorage storage = storages.get(0);
						
						String storageKey = storage.getWarehouseRrn() + storage.getStorageType()
						+ storage.getStorageId();
						String returnKey = tool.getTransWarehouseRrn() + tool.getTransStorageType()
						+ tool.getTransStorageId();
						
						// 检查与当前储位是否一致
						if (!storageKey.equals(returnKey)) {
							Warehouse warehouse = new Warehouse();
							warehouse.setObjectRrn(storage.getWarehouseRrn());
							warehouse = mmManager.getWarehouse(warehouse);
							if (!StringUtil.isEmpty(storage.getStorageType())) {
								// 当前没有处理库房
								if (Storage.CATEGORY_RACK.equals(storage.getStorageType())) {
									UI.showWarning(String.format(Message.getString("mm.mlot_was_stored2"),
											warehouse.getWarehouseId(), storage.getStorageId()));
									return;
								} else {
									UI.showWarning(String.format(Message.getString("mm.mlot_was_stored3"),
											warehouse.getWarehouseId(), storage.getStorageId()));
									return;
								}
							} else {
								UI.showWarning(String.format(Message.getString("mm.mlot_was_stored1"),
										warehouse.getWarehouseId()));
								return;
							}
						}
					}
				}
				SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
				boolean confirmReturnFlag = PvcCode.isConfirmToReturn(Env.getOrgRrn(), sysParameterManager);
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				if (confirmReturnFlag) {
					for (Tool tool : tools) {
						tool.setLotComment(String.valueOf(tool.getTransWarehouseRrn()));
						tool = (Tool) consumableManager.saveConsumable(tool, Env.getSessionContext());
						
						mmManager.changeMLotState(tool, EVENT_ID_RETURN, null, Env.getSessionContext());
					}
				} else {
					consumableManager.returnTool((List)tools, Tool.RECEIVE_TYPE_IN, new ConsumableAction(), Env.getSessionContext());
				}
				
				UI.showInfo(Message.getString("common.operation_successed"));// 弹出提示框
			} else {
				return;
			}
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} 
		super.okPressed();
	}
	
	@Override
	public boolean checkMLotState(Tool tool) {
		if(Tool.HOLDSTATE_ON.equals(tool.getHoldState())) {
			return false;
		} else {
			return true;
		}
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}
