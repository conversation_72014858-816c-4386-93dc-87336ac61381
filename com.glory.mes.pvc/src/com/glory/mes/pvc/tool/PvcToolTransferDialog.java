package com.glory.mes.pvc.tool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.consumable.model.ConsumableAction;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.mes.ras.consumable.tool.action.ToolActionDialog;

public class PvcToolTransferDialog extends ToolActionDialog { 
	
	private static int DIALOG_WIDTH = 800;
	private static int DIALOG_HEIGHT = 300;

	private static final String FIELD_TOOLACTION = "toolAction";
	private static final String FIELD_TOOLMULITLIST = "toolMulitList";
	private static final String FIELD_TARGETWAREHOUSERRN = "targetWarehouseRrn";
	private static final String FIELD_TARGETRACKRRN = "targetRackRrn";

	protected EntityFormField toolActionField;
	protected ListTableManagerField toolMulitListField;
	protected RefTableField targetWarehouseRrnField;
	protected RefTableField targetRackRrnField;
	
	protected List<Tool> tools;

	public PvcToolTransferDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	public PvcToolTransferDialog(String adFormName, String authority, IEventBroker eventBroker, List<Tool> tools) {
		super(adFormName, authority, eventBroker);
		this.tools = tools;
		setToolsList(tools);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		toolActionField = form.getFieldByControlId(FIELD_TOOLACTION, EntityFormField.class);
		toolMulitListField = form.getFieldByControlId(FIELD_TOOLMULITLIST, ListTableManagerField.class);
		
		targetWarehouseRrnField = toolActionField.getFieldByControlId(FIELD_TARGETWAREHOUSERRN, RefTableField.class);
		targetRackRrnField = toolActionField.getFieldByControlId(FIELD_TARGETRACKRRN, RefTableField.class);
		
		initLot();
	}

	@Override
	public void initLot() {
		try {
			((CheckBoxTableViewerManager)toolMulitListField.getListTableManager().getTableManager()).setCheckBoxDisableListener(new ICheckBoxDisableListener() {
    			public boolean isDisable(Object object) {
    				MLot lot = (MLot)object;
    				if (lot.getConstraintFlag()) {
    					return true;
    				}
    				return false;
    			}
    		});
			toolMulitListField.getListTableManager().setInput(tools);
      		if (tools != null && tools.size() > 0) {
      			List<MLot> validLots = new ArrayList<MLot>();
      			for (MLot lot : tools) {
      				if (!lot.getConstraintFlag()) {
      					validLots.add(lot);
    				}
          		}
      			toolMulitListField.getListTableManager().getCheckedObject().addAll(validLots);
      			toolMulitListField.getListTableManager().refresh();
      		}
      		MLotStorage storage = new MLotStorage();
      		for(Tool tool : tools) {
      			storage = getMLotStorage(tool.getObjectRrn());
        		if (storage == null) {
        			return;
        		}
      		}
      		targetWarehouseRrnField.setValue(storage.getWarehouseRrn(), true);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	protected void okPressed() {
		try {
			List<Object> objects = toolMulitListField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showError(Message.getString("ras.not_select_tool"));
				return;
			}
			tools = objects.stream().map(o -> ((Tool)o)).collect(Collectors.toList());
			if (toolActionField.validate()) {
				Map<String, MLotStorage> mLotStorageMap = new HashMap<String, MLotStorage>();
				for(Tool mLot : tools) {
					Tool tool = (Tool) mLot;
					MMManager mmManager = Framework.getService(MMManager.class);
					List<MLotStorage> storages = mmManager.getLotStorages(tool.getObjectRrn());
					if (CollectionUtils.isEmpty(storages)) {
						throw new ClientException("mm.mlot_must_specify_lotstorage");
					}
					
					if (storages.size() > 1) {
						throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
					}
					MLotStorage storage = storages.get(0);
					mLotStorageMap.put(tool.getmLotId(), storage);
					
					tool.setTransWarehouseRrn(storage.getWarehouseRrn());
					tool.setTransStorageType(storage.getStorageType());
					tool.setTransStorageId(storage.getStorageId());
					
					tool.setTransTargetWarehouseRrn(Long.parseLong(targetWarehouseRrnField.getValue().toString()));
					
					if (targetRackRrnField.getValue() != null) {
						tools.get(0).setTransTargetStorageType(Storage.CATEGORY_RACK);
						
						Storage rackStorage = getStorage(Long.parseLong(targetRackRrnField.getValue().toString()));
						tools.get(0).setTransTargetStorageId(rackStorage.getName());
					}
					
					String storageKey = storage.getWarehouseRrn() + storage.getStorageType() + storage.getStorageId();
					String changeKey = tool.getTransTargetWarehouseRrn() + tool.getTransTargetStorageType()
					+ tool.getTransTargetStorageId();
					
					// 未做改变，提示后返回
					if (storageKey.equals(changeKey)) {
						UI.showInfo(tool.getmLotId() + Message.getString("mm.transfer_no_change"));
						return;
					}
				}
				ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
				consumableManager.transferTools((List)tools, new ConsumableAction(), mLotStorageMap,
						tools.get(0).getTransTargetWarehouseRrn(), null, tools.get(0).getTransTargetStorageType(),
						tools.get(0).getTransTargetStorageId(), Env.getSessionContext());
				UI.showInfo(Message.getString("common.operation_successed"));
			} else {
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} 
		super.okPressed();
	}
	
	private Storage getStorage(Long storageRrn) throws Exception {
		MMManager mmManager = Framework.getService(MMManager.class);
		Storage storage = new Storage();
		storage.setObjectRrn(storageRrn);
		return mmManager.getStorage(storage);
	}
	
	private MLotStorage getMLotStorage(Long mLotRrn) {
		try {
			MMManager mmManager = Framework.getService(MMManager.class);
			List<MLotStorage> storages = mmManager.getLotStorages(mLotRrn);

			if (CollectionUtils.isEmpty(storages)) {
				targetWarehouseRrnField.setValue(null, true); 
				throw new ClientException("mm.mlot_must_specify_lotstorage");
			}else {
				targetRackRrnField.setValue(null, true);
			}

			if (storages.size() > 1) {
				throw new ClientException("wms.lot_in_multi_warehouse_or_storage");
			}
			return storages.get(0);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}
	
	@Override
	public boolean checkMLotState(Tool tool) {
		if(!MaterialState.STATE_IN.equals(tool.getState())) {
			return false;
		} else {
			return true;
		}	
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	 protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}

}