package com.glory.mes.pvc.lot.run.bylot;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.client.EDCManager;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.extensionpoints.WizardPageExtensionPoint;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.pvc.client.PvcManager;
import com.glory.mes.pvc.context.PvcInContext;
import com.glory.mes.pvc.lot.run.processor.PvcLotTrackInProcessor;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.detail.DetailParameterForm;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveDialog;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveWizard;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalDialog;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalWizard;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.google.common.collect.Lists;

public class PvcByLotSection extends LotSection {

	private static final Logger logger = Logger.getLogger(EntitySection.class);

	protected boolean trackMoveFlag = false;
	protected Step currentStep; 
	
	protected ToolItem itemTrackIn;
	protected ToolItem itemTrackOut;
	protected ToolItem itemTrackMove;
	protected ToolItem itemDcop;
	protected ToolItem itemAbort;	
	protected ToolItem itemTrackTHold;

	public PvcByLotSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		initAdObject();
		registerAccelerator();
	}

	@Override
	public Lot searchLot(String lotId) {
		try {
			//按线别检查批次
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			if (lot != null) {
				loadCurrentStep(lot);
				lotChanged(lot);
			}
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	protected void loadCurrentStep(Lot lot) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			setCurrentStep(step);
		} catch (Exception e) {
			logger.warn("LotSection loadCurrentStep(): Step isn' t exsited!");
		}
	}
	
	@Override
	protected void refreshAdapter() {
		super.refreshAdapter();
		loadCurrentStep((Lot) getAdObject());
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		if ("Parameters".equals(tab.getName())) {
			return new DetailParameterForm(composite, SWT.NONE, tab, form.getMessageManager());
		} else {
			return new EntityForm(composite, SWT.NONE, tab, form.getMessageManager());
		}
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
//		createToolItemTrackMove(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		
		createToolItemTrackIn(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		
		createToolItemTrackOut(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);		

		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemTrackMove(ToolBar tBar) {
		itemTrackMove = new ToolItem(tBar, SWT.PUSH);
		itemTrackMove.setText(Message.getString("wip.trackmove") + "(F2)");
		itemTrackMove.setImage(SWTResourceCache.getImage("trackmove"));
		itemTrackMove.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackMoveAdapter();
			}
		});
	}
	
	protected void createToolItemTrackIn(ToolBar tBar) {
		itemTrackIn = new ToolItem(tBar, SWT.PUSH);
		itemTrackIn.setText(Message.getString("wip.trackin") + "(F3)");
		itemTrackIn.setImage(SWTResourceCache.getImage("trackin"));
		itemTrackIn.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackInAdapter();
			}
		});
	}

	protected void createToolItemTrackOut(ToolBar tBar) {
		itemTrackOut = new ToolItem(tBar, SWT.PUSH);
		itemTrackOut.setText(Message.getString("wip.trackout") + "(F4)");
		itemTrackOut.setImage(SWTResourceCache.getImage("trackout"));
		itemTrackOut.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackOutAdapter();
			}
		});
	}

	protected void createToolItemRefresh(ToolBar tBar) {
		itemRefresh = new ToolItem(tBar, SWT.PUSH);
		itemRefresh.setText(Message.getString("common.refresh") + "(F5)");
		itemRefresh.setImage(SWTResourceCache.getImage("refresh"));
		itemRefresh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				refreshAdapter();
			}
		});
	}
	
	protected void trackInAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			Step step = getCurrentStep();
			
			Lot lot = (Lot) getAdObject();
			PvcLotTrackInProcessor processor = new PvcLotTrackInProcessor(lot);
			processor.open();
			
			refreshAdapter();
			
			if (txtLot != null) {
				txtLot.selectAll();
			}
			
			refresh();
			
			lotChanged(processor.getResultLot());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected TrackInWizard getTrackInWizard(TrackInContext context, String wizardName) {
		context.setOperator1(Env.getUserName());
		TrackInWizard wizard = (TrackInWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected void trackOutAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			Lot lot = (Lot) getAdObject();
			
			PvcInContext inContext = new PvcInContext();
			inContext.setLots(Lists.newArrayList(lot));
			
			PvcManager pvcManager = Framework.getService(PvcManager.class);
			Lot trackOutLot = pvcManager.trackOutLot(inContext, Env.getSessionContext());
			
			refreshAdapter();
			
			if (txtLot != null) {
				txtLot.selectAll();
			}
			UI.showInfo(Message.getString("common.operation_successed"));
			
			refresh();
			
			lotChanged(trackOutLot);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected TrackOutWizard getTrackOutWizard(TrackOutContext context, String wizardName) {
		context.setOperator1(Env.getUserName());
		TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected List<Lot> getRunningLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}
	
	protected void trackMoveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackMoveContext context = new TrackMoveContext();
			context.setTrackMoveType(TrackMoveContext.TRACK_MOVE_DEFAULT);
			List<Lot> lots = new ArrayList<Lot>();
			Lot lot = (Lot) getAdObject();
			lots.add(lot);
			context.setLots(lots);

			Step step = getCurrentStep();
			context.setStep(step);
			
			FlowWizard wizard = getTrackMoveWizard(context, step.getMoveNextFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackInConstraint(
					inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			if (wizard instanceof TrackMoveWizard) {
				TrackMoveDialog dialog = new TrackMoveDialog(UI.getActiveShell(), wizard);
				int result = dialog.open();
				if (result == Dialog.OK || result == TrackMoveDialog.FIN) {
					refreshAdapter();
					//只有在TrackOut或TrackMove时才需要检查批次Step变化
					lotChanged((Lot)getAdObject());
					UI.showInfo(Message.getString("wip.trackmove_success"));
				}
			} else if (wizard instanceof TrackMoveModalWizard) {
				//以固定窗口的形式过站
				((TrackMoveContext)((TrackMoveModalWizard)wizard).getContext()).setTrackMoveType(TrackMoveContext.TRACK_MOVE_MODAL);
				TrackMoveModalDialog dialog = new TrackMoveModalDialog(UI.getActiveShell(), wizard);
				dialog.open();
				refreshAdapter();
			}
			
			if (txtLot != null) {
				txtLot.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected TrackOutWizard getTrackMoveWizard(TrackMoveContext context, String wizardName) {
		context.setOperator1(Env.getUserName());
		TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected List<EdcSetCurrent> getEdcSetCurrent() {
		try {
			Lot lot = (Lot) getAdObject();
			EDCManager edcManager = Framework.getService(EDCManager.class);
			return edcManager.getItemSetCurrents(Env.getOrgRrn(), lot.getBatchId(), lot.getObjectRrn(), null);
		} catch (ClientException e) {
			return null;
		} catch (Exception e) {
			return null;
		}
	}
	
	public void lotChanged(Lot lot) {
		try {
			if (lot != null) {
				if (LotStateMachine.STATE_WAIT.equals(lot.getState())) {
					itemTrackIn.setEnabled(true);
					itemTrackOut.setEnabled(false);
				} else if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
					itemTrackIn.setEnabled(false);
					itemTrackOut.setEnabled(true);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void registerAccelerator() {
		this.txtLot.addListener(SWT.KeyDown, acceleratorListener());
	}

	protected Listener acceleratorListener() {
		return new Listener() {
			public void handleEvent(Event e) {
				if (getAdObject() != null) {
					switch (e.keyCode) {
					case SWT.F2:
						if (itemTrackMove != null && itemTrackMove.isEnabled()) {
							trackMoveAdapter();
						}
						break;
					case SWT.F3:
						if (itemTrackIn != null && itemTrackIn.isEnabled()) {
							trackInAdapter();
						}
						break;
					case SWT.F4:
						if (itemTrackOut != null && itemTrackOut.isEnabled()) {
							trackOutAdapter();
						}
						break;
					case SWT.F5:
						if (itemRefresh != null && itemRefresh.isEnabled()) {
							refreshAdapter();
						}
						break;
					}
				}
			}
		};
	}

	public Step getCurrentStep() {
		return currentStep;
	}

	public void setCurrentStep(Step currentStep) {
		this.currentStep = currentStep;
	}
	
}
