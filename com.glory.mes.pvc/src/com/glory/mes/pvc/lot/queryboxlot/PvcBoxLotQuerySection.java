package com.glory.mes.pvc.lot.queryboxlot;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.export.NatExporter;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.pvc.model.PvcCode;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class PvcBoxLotQuerySection extends EntitySection {
	
	public static String TABLE_NAME_SOURCE = "PvcBox";
	
	public PvcBoxLotQuerySection() {
		super();
	}
	private Boolean isCaseSensitive;
	
	protected ToolItem itemClear;
	protected ToolItem itemExport;
	public FormToolkit toolkit;
    protected Text txtSourceLot;
    protected Text txtSourceQty;
    protected CheckBoxFixEditorTableManager tableManager;
    protected NatTable natTable;
    
	
	protected void createSectionDesc(Section section) {}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClear(tBar);
		section.setTextClient(tBar);
	}

	private void createToolItemExport(ToolBar tBar) {
		itemExport = new ToolItem(tBar, 8);
		itemExport.setText(Message.getString("common.export"));
		itemExport.setImage(SWTResourceCache.getImage("export"));
		itemExport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				exportAdapter(tableManager);
			}

		});
	}
	
	protected void exportAdapter(CheckBoxFixEditorTableManager tableManager) {
		try {
			natTable = tableManager.getNatTable();
			new NatExporter(natTable.getShell()).exportSingleLayer(tableManager.getLayer(),
					natTable.getConfigRegistry());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@SuppressWarnings("unchecked")
	protected void deleteAdapter() {
		try {
			List<Lot> lotList = (List<Lot>)(List<? extends Object>) tableManager.getInput();
			List<Lot> lots = new ArrayList<Lot>();
			for (Lot lot : lotList) {
				lots.add(lot);
			}
			List<Object> os = tableManager.getCheckedObject();
			if(os.size() != 0) {
				for (Object object : os) {
					Lot l = (Lot) object;
					lots.remove(l);
					BigDecimal totalQty = new BigDecimal(txtSourceQty.getText().toString()).subtract(l.getSubQty());
					txtSourceQty.setText(totalQty.toString());
				}
			}
			tableManager.setInput(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}

	private void createToolItemClear(ToolBar tBar) {
		itemClear = new ToolItem(tBar, 8);
		itemClear.setText(Message.getString("common.clear"));
		itemClear.setImage(SWTResourceCache.getImage("clear"));
		itemClear.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				clearAdapter();
			}
		});
	}
	
	private void clearAdapter() {
		tableManager.setInput(null);
		txtSourceQty.setText("0");
	}

	@Override
	protected void createSectionContent(Composite client) {
		GridLayout layout = new GridLayout(1, true);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		client.setLayout(layout);
		client.setLayoutData(new GridData(GridData.FILL_BOTH));
		this.section.setText("箱包装列表");
		
		
		toolkit = new FormToolkit(client.getDisplay());
		Composite content = toolkit.createComposite(client, SWT.NONE);
		
		content.setLayout(layout);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createSourceTitle(content);
		createSourceTable(content);	
	}
	
	protected void createSourceTitle(Composite content) {
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(content);
		top.setLayout(new GridLayout(4, false));
		top.setLayoutData(gd);
		
		org.eclipse.swt.widgets.Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtSourceLot = toolkit.createText(top, "", SWT.BORDER);

		GridData gText = new GridData();
		gText.widthHint = 216;
		txtSourceLot.setLayoutData(gText);
		txtSourceLot.setTextLimit(32);
		txtSourceLot.forceFocus();	
		txtSourceLot.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		txtSourceLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					try {
						String lotId = tLotId.getText();
						if (!isLotIdCaseSensitive()) {
							lotId = lotId.toUpperCase();
						}
						LotManager lotManager = Framework.getService(LotManager.class);
						//当前输入的箱号找到箱 
						Lot currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
						if (currentLot == null || !(currentLot.getMainMatType().equals(PvcCode.MAIN_MAT_TYPE_BOX))) {
							UI.showWarning(Message.getString("pvc.box_not_found"));									
							tLotId.forceFocus();
							return;
						}
						addSource(currentLot);
						tLotId.forceFocus();
					} catch (Exception e) {
						 ExceptionHandlerManager.asyncHandleException(e);
				         return;
					}
					break;
				}
			}
		});
		org.eclipse.swt.widgets.Label labelQty = toolkit.createLabel(top, Message.getString("wip.lot_h_qty"));
		labelQty.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		txtSourceQty = toolkit.createText(top, "", SWT.READ_ONLY);

		GridData qtyText = new GridData();
		qtyText.widthHint = 216;
		txtSourceQty.setLayoutData(qtyText);
		txtSourceQty.setTextLimit(32);
		txtSourceQty.forceFocus();	
		txtSourceQty.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TEXT_BACKGRAOUND));
	}
	
	protected void createSourceTable(Composite content) {
	    try {
	    	ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
	    	ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_SOURCE);
	    	tableManager = new CheckBoxFixEditorTableManager(adTable);
	    	tableManager.setIndexFlag(true);
	    	tableManager.newViewer(content);
	    	tableManager.addDoubleClickListener(getSourceTableDoubleClickListener());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected IMouseAction getSourceTableDoubleClickListener() {
		return new AbstractMouseSelectionAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				Object ob = tableManager.getSelectedObject();
				if (null == ob) {
					UI.showError(Message.getString("common.select_object"));
					return;
				}
				List<Lot> lots = getLotList();
				lots.remove(ob);
				tableManager.setInput(lots);
		    }
		};
	}
	
	protected boolean addSource(ADBase adBase) {
		try {
			if (adBase instanceof Lot) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.addAll((List<Lot>) tableManager.getInput());
				
				// table 里面的lot
				Lot lot = (Lot) adBase;
				if (lots != null && lots.size() > 0) {
					// 判断界面有没有已存在的lot
					for (Lot oldLot : lots) {
						if (oldLot.getLotId().equals(lot.getLotId())) {
							UI.showError( Message.getString("wip.box_already_exists"));									
							return false;
						}
					}
					lots.add(lot);
				} else {
					lots.add(lot);
				}
				//需要计算箱的总数
				BigDecimal totalQty = BigDecimal.ZERO;
				for(Lot boxLot : lots) {
					totalQty = totalQty.add(boxLot.getSubQty());
				}
				txtSourceQty.setText(totalQty.toString());
				tableManager.setInput(lots);
			}
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return true;
		}
	}
	
	public List<Lot> getLotList() {
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll((List<Lot>) tableManager.getInput());
		return lots;
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
