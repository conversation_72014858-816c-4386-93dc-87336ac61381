package com.glory.mes.pvc.lot.batchship;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class PvcBatchShipDialog extends GlcBaseDialog { 

	private static final String FIELD_LOTID = "lotId";
	private static final String FIELD_LOTQUERY = "lotQuery";
	private static final String FIELD_LOTACTION = "lotAction";
	private static final String FIELD_ACTIONCODE = "actionCode";
	private static final String FIELD_ACTIONCOMMENT = "actionComment";
	
	private static final String BUTTON_REMOVE = "remove";

	protected TextField lotIdField;
	protected ListTableManagerField lotQueryField;
	protected EntityFormField lotActionField;
	protected RefTableField actionCodeField;
	protected TextField actionCommentField;
	
	protected List<Lot> lotList = Lists.newArrayList();

	public PvcBatchShipDialog(String adFormName, String authority, IEventBroker eventBroker, List<Lot> lotList) {
		super(adFormName, authority, eventBroker);
		this.lotList = lotList;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotIdField = form.getFieldByControlId(FIELD_LOTID, TextField.class);
		lotQueryField = form.getFieldByControlId(FIELD_LOTQUERY, ListTableManagerField.class);
		
		lotActionField = form.getFieldByControlId(FIELD_LOTACTION, EntityFormField.class);
		actionCodeField = lotActionField.getFieldByControlId(FIELD_ACTIONCODE, RefTableField.class);
		actionCommentField = lotActionField.getFieldByControlId(FIELD_ACTIONCOMMENT, TextField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
		
		init();
		
		changeLotIdEvent();
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void removeAdapter(Object object) {
		List<Object> allLots = (List<Object>) lotQueryField.getListTableManager().getInput();
		List<Object> selectLots = lotQueryField.getListTableManager().getCheckedObject();
		if (CollectionUtils.isNotEmpty(allLots) && CollectionUtils.isNotEmpty(selectLots)) {
			allLots.removeAll(selectLots);
			
			List<Lot> selectMLotList = (List) selectLots.stream().map(selectObj -> (Lot) selectObj)
					.collect(Collectors.toList());
			lotList.removeAll(selectMLotList);
			
			List<Lot> checkLots = (List) allLots.stream().map(selectObj -> (Lot) selectObj)
					.collect(Collectors.toList());
			lotQueryField.getListTableManager().setInput(checkLots);
		}
	}
	
	private void init() {
		lotQueryField.getListTableManager().setInput(lotList);
	}
	
	// 注册组件号控件enter事件
	protected void changeLotIdEvent() {
		lotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@SuppressWarnings("unchecked")
			@Override
			public void keyPressed(KeyEvent event) {
				Text lotIdText = ((Text) event.widget);
				lotIdText.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String lotId = lotIdText.getText();
					if (StringUtil.isEmpty(lotId)) {
						UI.showError(Message.getString("mm.please_enter_mlot_id"));
						return;
					} else {
						try {
							// 校验是否已经在列表中
							List<Lot> lots = (List<Lot>) (Object) lotQueryField.getListTableManager().getInput();
							Boolean flag = false;
							for (Lot lot : lots) {
								if (lot.getLotId().equals(lotId)) {
									flag = true;
									break;
								}
							}
							if (flag) {
								UI.showError(Message.getString("wip.box_already_exists"));
								lotIdField.setText("");
								lotIdField.getTextControl().forceFocus();
								return;
							}

							LotManager lotManager = Framework.getService(LotManager.class);
							Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, true);
							
							if (StringUtils.equals(lot.getState(), LotStateMachine.STATE_SHIP)) {
								UI.showError(Message.getString("pvc.current_lot_has_shipped"));
								lotIdField.setText("");
								lotIdField.getTextControl().forceFocus();
								return;
							}
							        
							lotList.add(lot);
							lotQueryField.getListTableManager().setInput(lotList);
							lotIdField.setText("");
							lotIdField.getTextControl().forceFocus();
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
					break;
				}
			}
		});
	}
	
	@SuppressWarnings({"unchecked"})
	protected void okPressed() {
		try {
			List<Lot> shipLotList = (List<Lot>) lotQueryField.getListTableManager().getInput();
			if (CollectionUtils.isEmpty(shipLotList)) {
				UI.showError(Message.getString("wip.ship_error_nolot_toship"));
				return;
			}
			if (Objects.isNull(actionCodeField.getValue())) {
				UI.showError(Message.getString("mm.ship_code_null"));
				return;
			}
			LotAction lotAction = new LotAction();
			lotAction.setActionCode(actionCodeField.getValue().toString());
			lotAction.setActionComment(Objects.isNull(actionCommentField.getValue()) ? "" : actionCommentField.getValue().toString());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			for (Lot lot : shipLotList) {
				lotManager.shipLot(lot, lotAction, Env.getSessionContext());
			}
			UI.showInfo(Message.getString("wip.ship_success"));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
}