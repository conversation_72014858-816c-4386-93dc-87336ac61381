package com.glory.mes.pvc.lot.box.cancel;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcPackManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class PvcBoxCancelEditor extends PvcGlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.lot.box.cancel.PvcBoxCancelEditor";

	private static final String CANCEL_BUTTON = "cancel";

	private static final String FIELD_QUERY = "search";

	private static final String FIELD_TEXT = "remark";

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		subscribeAndExecute(eventBroker, form.getFullTopic(CANCEL_BUTTON), this::cancelAdapter);
	}

	private void cancelAdapter(Object obj) {
		if (!UI.showConfirm(Message.getString("common.delete.not"))) {
			return;
		}
		QueryFormField formField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);
		List<Object> checkedList = formField.getCheckedObjects();

		if (CollectionUtils.isEmpty(checkedList)) {
			UI.showInfo(Message.getString("common.select_object"));
			return;
		}

		// 获取备注
		CustomField remarkField = form.getFieldByControlId(FIELD_TEXT, CustomField.class);
		String remark = (String) remarkField.getValue();

		try {
			PvcPackManager packManager = Framework.getService(PvcPackManager.class);
			List<Lot> lotList = new ArrayList<Lot>();
			for (Object check : checkedList) {
				Lot lot = (Lot) check;
				if (LotStateMachine.STATE_WAIT.equals(lot.getState())) {
					UI.showError("LotId:" + lot.getLotId() + "," + Message.getString("error.state_is_not_allow"));
					return;
				}
				lotList.add(lot);
			}

			// 创建批次动作
			LotAction lotAction = new LotAction();
			lotAction.setActionCode("PackageLotCancel");
			lotAction.setActionComment(remark);

			packManager.cancelBox(lotList, lotAction, Env.getSessionContext());

			UI.showInfo(Message.getString("pvc.cancel_seccess"));
			remarkField.setValue("");
			remarkField.refresh();
			formField.refresh();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
