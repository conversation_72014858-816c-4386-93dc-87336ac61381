package com.glory.mes.pvc.lot.batchunship;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class PvcBatchUnShipManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.lot.batchunship.PvcBatchUnShipManagerEditor";

	private static final String FIELD_LOTQUERY = "lotQuery";
	private static final String FIELD_LOTACTION = "lotAction";
	private static final String FIELD_ACTIONCODE = "actionCode";
	private static final String FIELD_ACTIONREASON = "actionReason";
	private static final String FIELD_ACTIONCOMMENT = "actionComment";

	private static final String BUTTON_UNSHIP = "unship";

	protected QueryFormField lotQueryField;
	protected EntityFormField lotActionField;
	protected RefTableField actionCodeField;
	protected TextField actionReasonField;
	protected TextField actionCommentField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotQueryField = form.getFieldByControlId(FIELD_LOTQUERY, QueryFormField.class);
		lotActionField = form.getFieldByControlId(FIELD_LOTACTION, EntityFormField.class);
		actionCodeField = lotActionField.getFieldByControlId(FIELD_ACTIONCODE, RefTableField.class);
		actionReasonField = lotActionField.getFieldByControlId(FIELD_ACTIONREASON, TextField.class);
		actionCommentField = lotActionField.getFieldByControlId(FIELD_ACTIONCOMMENT, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNSHIP), this::unshipAdapter);
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void unshipAdapter(Object object) {
		try {
			List<Object> objects = lotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showError(Message.getString("wip.unship_error_nolot_tounship"));
				return;
			}
			List<Lot> unshipLotList = (List) objects.stream().map(selectObj -> (Lot) selectObj)
					.collect(Collectors.toList());
			
			if (Objects.isNull(actionCodeField.getValue())) {
				UI.showError(Message.getString("mm.unship_code_null"));
				return;
			}
			LotAction lotAction = new LotAction();
			lotAction.setActionCode(actionCodeField.getValue().toString());
			lotAction.setActionReason(Objects.isNull(actionReasonField.getValue()) ? "" : actionReasonField.getValue().toString());
			lotAction.setActionComment(Objects.isNull(actionCommentField.getValue()) ? "" : actionCommentField.getValue().toString());
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.unShipLot(unshipLotList, lotAction, Env.getSessionContext());
			
			UI.showInfo(Message.getString("wip.unship_successed"));// 弹出提示框
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void refreshAdapter(Object object) {
		lotQueryField.getQueryForm().refresh();
		lotQueryField.refresh();
		actionCodeField.setValue(null);
		actionCodeField.refresh();
		actionReasonField.setValue(null);
		actionReasonField.refresh();
		actionCommentField.setValue(null);
		actionCommentField.refresh();
	}

}