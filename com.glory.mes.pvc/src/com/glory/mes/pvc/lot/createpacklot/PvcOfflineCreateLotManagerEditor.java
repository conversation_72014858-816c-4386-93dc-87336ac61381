package com.glory.mes.pvc.lot.createpacklot;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map.Entry;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.WorkStation;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcPackManager;
import com.glory.mes.pvc.model.BinBoxSet;
import com.glory.mes.pvc.model.BinBoxSetLine;
import com.glory.mes.pvc.model.LineConfig;
import com.google.common.collect.Lists;

public class PvcOfflineCreateLotManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.lot.createpacklot.PvcOfflineCreateLotManagerEditor";
	
	private static final String FORM_PRINT_INFO = "printInfo";

	private static final String FIELD_LINEID = "lineId";
	private static final String FIELD_UDF_BINBOXSETRRN = "udf.binBoxSetRrn";
	private static final String FIELD_PARTNAME = "partName";
	private static final String FIELD_UDF_BOXID = "udf.boxId";
	private static final String FIELD_MAINQTY = "mainQty";
	private static final String FIELD_COUNT = "count";

	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";

	protected RefTableField lineIdField;
	protected RefTableField binboxSetName;
	protected RefTableField partNameField;
	protected RefTableField boxId;
	protected TextField mainQtyField;
	protected TextField countField;
	
	private EntityForm entityForm;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		entityForm = (EntityForm) form.getSubFormById(FORM_PRINT_INFO);

		lineIdField = form.getFieldByControlId(FIELD_LINEID, RefTableField.class);
		binboxSetName = form.getFieldByControlId(FIELD_UDF_BINBOXSETRRN, RefTableField.class);
		partNameField = form.getFieldByControlId(FIELD_PARTNAME, RefTableField.class);
		boxId = form.getFieldByControlId(FIELD_UDF_BOXID, RefTableField.class);
		mainQtyField = form.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		countField = form.getFieldByControlId(FIELD_COUNT, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		
		lineIdField.addValueChangeListener(lineIdListener);
		binboxSetName.addValueChangeListener(binboxSetNameListener);
		boxId.addValueChangeListener(boxIdListener);
	}
	
	IValueChangeListener lineIdListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			String lineId = lineIdField.getValue().toString();
			if (lineId != null) {
				List<LineConfig> lineConfigs = adManager.getEntityList(Env.getOrgRrn(), LineConfig.class,
						Integer.MAX_VALUE, " lineId = '" + lineId + "'", "");
				if (CollectionUtils.isEmpty(lineConfigs)) {
					binboxSetName.setInput(Lists.newArrayList());
					binboxSetName.setValue(null);
					partNameField.setValue(null);
				} else {
					List<BinBoxSet> BinBoxSetList = adManager.getEntityList(Env.getOrgRrn(),
							BinBoxSet.class, Integer.MAX_VALUE,
							"objectRrn = '" + lineConfigs.get(0).getBinBoxSetRrn() + "'", "");
					binboxSetName.setInput(BinBoxSetList);
					binboxSetName.setValue(lineConfigs.get(0).getBinBoxSetName());
					partNameField.setValue(lineConfigs.get(0).getPartName());
				}
				binboxSetName.refresh();
				countField.setValue("1");
				countField.refresh();
			}
		}
	};
	
	IValueChangeListener binboxSetNameListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			BinBoxSet binSet = (BinBoxSet) binboxSetName.getData();
			if (binSet != null) {
				List<BinBoxSet> binBoxSetList = adManager.getEntityList(Env.getOrgRrn(), BinBoxSet.class,
						Integer.MAX_VALUE, "status = 'Active' and name = '" + binSet.getName() + "'", "");
				if (binBoxSetList.size() != 0) {
					List<BinBoxSetLine> binBoxSetLineList = adManager.getEntityList(Env.getOrgRrn(),
							BinBoxSetLine.class, Integer.MAX_VALUE,
							"setRrn = '" + binBoxSetList.get(0).getObjectRrn() + "'", "");
					if (CollectionUtils.isNotEmpty(binBoxSetLineList)) {
						boxId.setInput(binBoxSetLineList);
					}
				}
			} else {
				List<BinBoxSetLine> binBoxSetLineList = adManager.getEntityList(Env.getOrgRrn(),
						BinBoxSetLine.class, Integer.MAX_VALUE, "", "");
				if (CollectionUtils.isNotEmpty(binBoxSetLineList)) {
					boxId.setInput(binBoxSetLineList);
				}
			}
			boxId.refresh();
		}
	};
	
	IValueChangeListener boxIdListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			if (binboxSetName.getText() != null && boxId.getText() != null) {
				BinBoxSet binBoxSet = (BinBoxSet) binboxSetName.getData();
				List<BinBoxSetLine> binBoxSetLineList = adManager.getEntityList(Env.getOrgRrn(),
						BinBoxSetLine.class, Integer.MAX_VALUE,
						"setRrn = '" + binBoxSet.getObjectRrn() + "' and boxId = '" + boxId.getValue() + "'",
						"");
				if (CollectionUtils.isNotEmpty(binBoxSetLineList)) {
					mainQtyField.setValue(binBoxSetLineList.get(0).getPackQty().toString());
				}
			}
			mainQtyField.refresh();
		}
	};

	private void newAdapter(Object object) {
		try {
			entityForm.removeAllMessages();

			for (Entry<String, IField> adField : entityForm.getFields().entrySet()) {
				adField.getValue().setValue(null);
			}
			entityForm.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void saveAdapter(Object object) {
		try {
			form.getMessageManager().setAutoUpdate(true); // 设置为true时才会刷新界面出现icon
			form.getMessageManager().removeAllMessages();

			if (!entityForm.validate()) {
				return;
			}
			
			PvcPackManager packManager = Framework.getService(PvcPackManager.class);

			String lineId = lineIdField.getValue().toString();
			
			BinBoxSetLine binBoxSetLine = (BinBoxSetLine) boxId.getData();

			BigDecimal mainQty = new BigDecimal(mainQtyField.getValue().toString());

			String count = countField.getValue().toString();
			
			List<WorkStation> workStations = adManager.getEntityList(Env.getOrgRrn(), WorkStation.class, 1, " ipAddress = '" + Env.getClientIp() + "'", "");
			if (CollectionUtils.isEmpty(workStations)) {
				UI.showError(Message.getString("pvc.work_station_not_found"));
				return;
			}

			if (count != null) {
				int totalCount = Integer.valueOf(count);
				for (int i = 0; i < totalCount; i++) {
					packManager.packLotByOffline(lineId, binBoxSetLine, mainQty, 1, workStations.get(0).getName(), Env.getSessionContext());
				}				
			}
			UI.showInfo(Message.getString("common.operation_successed"));// 弹出提示框
			newAdapter(object);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			form.getMessageManager().setAutoUpdate(false);
		}
	}

}