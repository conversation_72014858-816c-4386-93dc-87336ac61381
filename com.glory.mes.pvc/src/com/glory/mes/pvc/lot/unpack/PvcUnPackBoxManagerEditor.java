package com.glory.mes.pvc.lot.unpack;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.PackManager;
import com.glory.mes.mm.model.PackageType;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

public class PvcUnPackBoxManagerEditor extends PvcGlcEditor { 
	
	private static final Logger logger = Logger.getLogger(PvcUnPackBoxManagerEditor.class);

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.lot.unpack.PvcUnPackBoxManagerEditor";
	
	private static final String BOX_MAIN_MAY_TYPE = "BOX";

	private static final String FIELD_LOTID = "lotId";
	private static final String FIELD_BOXINFOLIST = "boxInfoList";

	private static final String BUTTON_DELETE = "delete";
	
	private static final String PACKAGE_TYPE_NAME = "BOX";

	protected TextField lotIdField;
	protected ListTableManagerField boxInfoListField;
	
	public Lot packedLot;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotIdField = form.getFieldByControlId(FIELD_LOTID, TextField.class);
		boxInfoListField = form.getFieldByControlId(FIELD_BOXINFOLIST, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::unpackAdapter);
		
		lotIdKeyPressEvent();
	}
	
	private void lotIdKeyPressEvent() {
		lotIdField.getTextControl().forceFocus();
		lotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tTrayId = ((Text) event.widget);
				tTrayId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					try {
						List<Lot> lots = null;
						String lotId = tTrayId.getText();
						tTrayId.setText(lotId);
						LotManager lotManager = Framework.getService(LotManager.class);
						Lot currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
						if (Objects.isNull(currentLot)) {
							UI.showError(Message.getString("mm.package_id_not_exist"));
							tTrayId.setText("");
							tTrayId.forceFocus();
							return;
						}
						if (!StringUtils.equals(currentLot.getMainMatType(), BOX_MAIN_MAY_TYPE)) {
							UI.showError(Message.getString("mm.tray_plase_input_tray_code"));
							tTrayId.setText("");
							tTrayId.forceFocus();
							return;
						}
						lots = searchLots(currentLot);
						packedLot = currentLot;
						tTrayId.selectAll();
						
						boxInfoListField.getListTableManager().getInput().clear();
						if (CollectionUtils.isEmpty(lots)) {
							tTrayId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							UI.showInfo(Message.getString("mm.carrier_mlot_does_not_exist"));
							tTrayId.setText("");
							tTrayId.forceFocus();
							return;
						} else {
							boxInfoListField.getListTableManager().addList(lots);
						}
						break;
					}  catch (Exception e) {
						 ExceptionHandlerManager.asyncHandleException(e);
				         return;
					}
				}
			}
		});
	}
	
	public List<Lot> searchLots(Lot currentLot) {
		try {
			List<Lot> sourceLots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Integer.MAX_VALUE,
					"parentLotRrn = '" + currentLot.getObjectRrn() + "'", "");
			return sourceLots;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}

	private void unpackAdapter(Object object) {
		try {
			if (packedLot == null) {
				UI.showInfo(Message.getString("mm.pack_unpack_lot_is_null"));
				return;
			}
			
			boolean confirmUnPack = UI.showConfirm(Message.getString("mm.pack_unpack_confirm"));
			if (confirmUnPack) {
				// 拆包处理
				List<Lot> sourceLots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Integer.MAX_VALUE,
						"parentLotRrn = '" + packedLot.getObjectRrn() + "'", "");	
				
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				carrierLotManager.unpackLot(packedLot, sourceLots, getPackageType(packedLot), Env.getSessionContext());
				
				//清空
				UI.showInfo(Message.getString("common.operation_successed"));
				boxInfoListField.getListTableManager().setInput(Lists.newArrayList());
				lotIdField.setText(null);
				lotIdField.getTextControl().forceFocus();
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	public PackageType getPackageType(Lot lot) throws Exception {
		try {
			PackManager packManager = Framework.getService(PackManager.class);
			
			PackageType packageType = packManager.getPackageType(Env.getOrgRrn(), PACKAGE_TYPE_NAME, PackageType.OBJECT_TYPE_LOT);	
			
			return packageType;
		} catch (Exception e) {		
			ExceptionHandlerManager.asyncHandleException(e);
	        return null;
		}
	}

}