package com.glory.mes.pvc.lot.fastpack;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.common.label.model.Label;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.FMessage;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.custom.MessageConsoleCustomComposite;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.base.model.WorkStation;
import com.glory.mes.mm.client.PackManager;
import com.glory.mes.mm.model.PackageType;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcPackManager;
import com.glory.mes.pvc.model.PvcCode;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

import javazoom.jl.decoder.JavaLayerException;
import javazoom.jl.player.Player;

public class PvcPackBoxManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.lot.fastpack.PvcPackBoxManagerEditor";
	
	public static String MEDIA_TYPE_SCAN_FAIL = "ScanFail";
	public static String MEDIA_TYPE_SCAN_SUCCESS = "ScanSuccess";
	public static String MEDIA_TYPE_PACK_FAIL = "PackFail";
	public static String MEDIA_TYPE_PACK_SUCCESS = "PackSuccess";

	private static final String FIELD_LOTID = "lotId";
	private static final String FIELD_SMALLBOXINFOLIST = "smallBoxInfoList";
	private static final String FIELD_MESSAGECONSOLE = "messageConsole";

	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_REFRESH = "refresh";
	
	private static final String PACKAGE_TYPE_NAME = "BOX";

	protected TextField lotIdField;
	protected ListTableManagerField smallBoxInfoListField;
	protected CustomField messageConsoleField;
	
	protected MessageConsoleCustomComposite consoleComposite;
	protected ListTableManager tableManager;
	
	private Lot parentLot;
	private PackageType packageType;
	
	private Boolean isCaseSensitive;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotIdField = form.getFieldByControlId(FIELD_LOTID, TextField.class);
		smallBoxInfoListField = form.getFieldByControlId(FIELD_SMALLBOXINFOLIST, ListTableManagerField.class);
		tableManager = smallBoxInfoListField.getListTableManager();
		messageConsoleField = form.getFieldByControlId(FIELD_MESSAGECONSOLE, CustomField.class);
		consoleComposite = (MessageConsoleCustomComposite) messageConsoleField.getCustomComposite();

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::packAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		lotIdKeyPressEvent();
	}
	
	private void lotIdKeyPressEvent() {
		lotIdField.getTextControl().forceFocus();
		lotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor("Black"));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					try {
						String lotId = tLotId.getText();
						if (!isLotIdCaseSensitive()) {
							lotId = lotId.toUpperCase();
						}

						LotManager lotManager = Framework.getService(LotManager.class);
						//当前输入的lotId找到的Lot
						Lot currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
						if (currentLot == null) {
							buildConsoleMessage("Lot", Message.getString("wip.lot_not_found"), MsgType.MSG_ERROR);
							tLotId.forceFocus();
							mediaPlay(MEDIA_TYPE_SCAN_FAIL);
							return;
						} 
						if (!StringUtils.equals(PvcCode.MAIN_MAT_TYPE_SMALLBOX, currentLot.getMainMatType())) {
							buildConsoleMessage("Lot", Message.getString("mm.pack_mainmattype_not_allow"), MsgType.MSG_ERROR);
							tLotId.forceFocus();
							mediaPlay(MEDIA_TYPE_SCAN_FAIL);
							return;
						}
						if(BigDecimal.ZERO.compareTo(currentLot.getMainQty()) == 0) {
							buildConsoleMessage("Lot", Message.getString("wip.lot_qty_must_more_than.zero"), MsgType.MSG_ERROR);
							tLotId.forceFocus();
							mediaPlay(MEDIA_TYPE_SCAN_FAIL);
							return;
						} 
						if (currentLot.getParentLotRrn() != null) {
							buildConsoleMessage("Lot", Message.getString("mm.pack_lot_is_packed"), MsgType.MSG_ERROR);
							tLotId.forceFocus();
							mediaPlay(MEDIA_TYPE_SCAN_FAIL);
							return;
						}
						if(currentLot.getHoldState().equals("On")) {
							buildConsoleMessage("Lot", Message.getString("pvc.state_not_allow"), MsgType.MSG_ERROR);
							tLotId.forceFocus();
							mediaPlay(MEDIA_TYPE_SCAN_FAIL);
							return;
						}
						if(!LotStateMachine.STATE_FIN.equals(currentLot.getState())) {
							buildConsoleMessage("Lot", Message.getString("pvc.state_not_allow"), MsgType.MSG_ERROR);
							tLotId.forceFocus();
							mediaPlay(MEDIA_TYPE_SCAN_FAIL);
							return;
						}
						if (getPackageType() != null) {
							PackageType newPackageType = getPackageTypeByName();
							if (!getPackageType().getName().equals(newPackageType.getName())) {
								buildConsoleMessage("Lot", Message.getString("wip.lotId_packagin_are_inconsistent"), MsgType.MSG_ERROR);
								tLotId.forceFocus();
								mediaPlay(MEDIA_TYPE_SCAN_FAIL);
								return;
							}
						}
						
						if (parentLot == null) {
							PackageType newPackageType = getPackageTypeByName();
							if (newPackageType == null) {
								tLotId.forceFocus();
								mediaPlay(MEDIA_TYPE_SCAN_FAIL);
								return;
							}
							if (!StringUtil.isEmpty(newPackageType.getSourceMainMatType())) { //源包装类型未维护时，不检查此项
								if (!newPackageType.getSourceMainMatType().equals(currentLot.getMainMatType())) {
									buildConsoleMessage("Lot", Message.getString("mm.pack_mainmattype_not_allow"), MsgType.MSG_ERROR);
									tLotId.forceFocus();
									mediaPlay(MEDIA_TYPE_SCAN_FAIL);
									return;
								}
							}
							
							if (!StringUtil.isEmpty(newPackageType.getSourceLotState())) {
								if (!currentLot.getState().equals(newPackageType.getSourceLotState())) {
									buildConsoleMessage("Lot", Message.getString("error.lot_state_not_allow"), MsgType.MSG_ERROR);
									tLotId.forceFocus();
									mediaPlay(MEDIA_TYPE_SCAN_FAIL);
									return;
								}
							}

							if (newPackageType.getPackMainMatType().equals(currentLot.getMainMatType())) {
								buildConsoleMessage("Lot", Message.getString("mm.packed"), MsgType.MSG_ERROR);
								tLotId.forceFocus();
								mediaPlay(MEDIA_TYPE_SCAN_FAIL);
								return;
							}

							setPackageType(newPackageType);
							parentLot = currentLot;
						}		
						
						if (addSource(currentLot)) {
//							mediaPlay(MEDIA_TYPE_SCAN_SUCCESS);
						} else {
							//调用错误语音
							mediaPlay(MEDIA_TYPE_SCAN_FAIL);
						}
						
						//自动包装
						if (getPackageType().getSourceMainQty().compareTo(BigDecimal.valueOf(getLotList().size())) == 0) {
							if (packLot(getLotList())) {
								mediaPlay(MEDIA_TYPE_PACK_SUCCESS);
							} else {
								mediaPlay(MEDIA_TYPE_PACK_FAIL);
							}
						} 
						
						lotIdField.setValue(null);
						lotIdField.refresh();
						tLotId.forceFocus();
					} catch (Exception e) {
						 ExceptionHandlerManager.asyncHandleException(e);
				         return;
					}
					break;
				}
			}
		});
	}
	
	@SuppressWarnings("rawtypes")
	protected boolean packLot(List<Lot> lots) {
		try {
			String currentLabelAttributeValue = null;
			Map udf = lots.get(0).getUdf();
			if (udf != null && udf.size() > 0) {
				for (Object key : udf.keySet()) {
					if (PvcCode.EXTEND_WIP_LOT_CURRENT_LABEL_NAME.equals((String)key)) {
						currentLabelAttributeValue = (String)udf.get(key);
					} 
				}
			}
			
			if (currentLabelAttributeValue == null) {
				buildConsoleMessage("Lot", Message.getString("pvc.pack_lot_currentlayer_label_not_found"), MsgType.MSG_ERROR);
				return false;
			}
			
			List<Label> currentLabels = adManager.getEntityList(Env.getOrgRrn(), Label.class, Integer.MAX_VALUE, 
					" name = '" + currentLabelAttributeValue + "' and state = 'ACTIVE'", "");
			if (currentLabels == null || currentLabels.size() == 0) {	
				buildConsoleMessage("Lot", Message.getString("pvc.pack_lot_currentlayer_label_not_found"), MsgType.MSG_ERROR);
				return false;
			} 		
			
			List<Label> boxLabels = adManager.getEntityList(Env.getOrgRrn(), Label.class, Integer.MAX_VALUE, 
					" name = '" + currentLabels.get(0).getUdf().get(PvcCode.LABEL_PARENT_LABEL_NAME) + "' and state = 'ACTIVE'", "");
			if (boxLabels == null || boxLabels.size() == 0) {
				buildConsoleMessage("Lot", Message.getString("yj.pack_lot_pallet_label_not_found"), MsgType.MSG_ERROR);				
				return false;
			}
			
			Label boxLabel = (Label) adManager.getEntity(boxLabels.get(0));
			
			//一起会打标签
			List<WorkStation> workStations = adManager.getEntityList(Env.getOrgRrn(), WorkStation.class, 1, " ipAddress = '" + Env.getClientIp() + "'", "");
			if (workStations.size() == 0) {
				buildConsoleMessage("Lot", Message.getString("pvc.work_station_not_found"), MsgType.MSG_ERROR);				
				return false;
			}

			PvcPackManager packManager = Framework.getService(PvcPackManager.class);
			Lot packLot = packManager.packLot(lots, "", getPackageType(), boxLabel, workStations.get(0).getName(), Env.getSessionContext());
			
			//打印成功
			if(packLot != null) {
				consoleComposite.getfMessageConsole().clear();
				buildConsoleMessage("Lot", Message.getString("mm.pack_package_id") + packLot.getLotId() + ":" +
						Message.getString("common.operation_successed"), MsgType.MSG_INFORMATION);				
			}
			//清除TableManager与parentLot
			clearAdapter(); 
			return true;
		} catch (Exception e) {	
			ClientException e1=(ClientException) e;
			if(StringUtils.isNotEmpty(e1.getErrorCode())) {
				buildConsoleMessage("Lot", Message.getString(e1.getErrorCode()), MsgType.MSG_ERROR);	
			}else {
				buildConsoleMessage("Lot", Message.getString(e1.getMessage()), MsgType.MSG_ERROR);
			}
	        return false;
		}
	}
	
	protected void clearAdapter() {
		try {
			parentLot = null;
			setPackageType(null);
			tableManager.setInput(Lists.newArrayList());
			lotIdField.setValue(null);
			lotIdField.refresh();
			lotIdField.getTextControl().forceFocus();	
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}
	
	@SuppressWarnings("unchecked")
	protected boolean addSource(ADBase adBase) {
		try {
			if (adBase instanceof Lot) {
				//未包装的lot
				List<Lot> lots = new ArrayList<Lot>();
				lots.addAll((List<Lot>) tableManager.getInput());
				
				LotManager lotManager = Framework.getService(LotManager.class);
				
				// table 里面的lot
				Lot lot = (Lot) adBase;
				if (!isValid(lot)) {
					return false;
				}
				if (lots != null && lots.size() > 0) {
					// 判断界面有没有已存在的lot
					for (Lot oldLot : lots) {
						if (oldLot.getLotId().equals(lot.getLotId())) {
							buildConsoleMessage("Lot", Message.getString("wip.box_already_exists"), MsgType.MSG_ERROR);									
							return false;
						}
					}
					
					if (!PackageType.OBJECT_TYPE_LOT.equals(getPackageType().getObjectType())) {
						buildConsoleMessage("Lot", Message.getString("pvc.pack_object_type_mlot"), MsgType.MSG_ERROR);									
						return false;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceMainMatType())
							&& !getPackageType().getSourceMainMatType().equals(lot.getMainMatType())) {
						buildConsoleMessage("Lot", Message.getString("mm.pack_mainmattype_not_allow"), MsgType.MSG_ERROR);
						return false;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceSubMatType())
							&& !getPackageType().getSourceSubMatType().equals(lot.getSubMatType())) {
						buildConsoleMessage("Lot", Message.getString("mm.pack_submattype_not_allow"), MsgType.MSG_ERROR);
						return false;
					}
					if (!checkLotInfo(lots, lot)) {
						return false;
					}
					if (!StringUtil.isEmpty(getPackageType().getPackMergeRule())) {
						// 检查包装合批规则,如果不通过则加入rejectSourceTableManager
						List<Lot> childLots = new ArrayList<>();
						childLots.add(lot);
					
						MergeRuleResult pckResult = lotManager.checkLotMergeRule(parentLot, childLots,
								getPackageType().getPackMergeRule());

						if (!pckResult.isSuccess()) {
							buildConsoleMessage("Lot", Message.getString("mm.pack_mergerule_not_allow"), MsgType.MSG_ERROR);
							return false;
						} else {
							lots.add(lot);
						}
					}
				} else {
					lots.add(lot);
				}
				tableManager.setInput(lots);
			}
			return true;
		} catch (Exception e) {
			buildConsoleMessage("Lot", Message.getString(e.getMessage()), MsgType.MSG_ERROR);
	        return false;
		}
	}
	
	public boolean isValid(Lot lot) {
		try {
			Date vaildTime = lot.getScheduleTime();
			Date currentTime = new Date();
			if (vaildTime == null || (vaildTime != null && currentTime.before(vaildTime))) {
				return true;
			} else if (currentTime.after(vaildTime)) {
				buildConsoleMessage("Lot", String.format(Message.getString("pvc.lot_pack_valid_date_has_passed"), lot.getLotId()), MsgType.MSG_ERROR);
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return true;
	}
	
	/**
	 * 检查批次属性信息
	 * 颜色、效率、等级、开压等级、客户料号
	 * @param lotList
	 * @param newLot
	 * @return
	 */
	protected boolean checkLotInfo (List<Lot> lotList, Lot newLot) {
		String color = PvcCode.EXTEND_WIP_LOT_COLOUR;
		String efficiency = PvcCode.EXTEND_WIP_LOT_EFFICIENCY;
		String uoc = PvcCode.EXTEND_WIP_LOT_UOC;
		String gear=PvcCode.EXTEND_WIP_LOT_GEAR;
		String power=PvcCode.EXTEND_WIP_LOT_POWER;
		try {
			if (String.valueOf(lotList.get(0).getUdfValue(color)) != null && !String
					.valueOf(lotList.get(0).getUdfValue(color)).equals(String.valueOf(newLot.getUdfValue(color)))) {
				buildConsoleMessage("Lot", Message.getString("pvc.lot_color_not_same"), MsgType.MSG_ERROR);
				return false;
			}
			if (String.valueOf(lotList.get(0).getUdfValue(uoc)) != null && !String
					.valueOf(lotList.get(0).getUdfValue(uoc)).equals(String.valueOf(newLot.getUdfValue(uoc)))) {
				buildConsoleMessage("Lot", Message.getString("pvc.lot_uoc_not_same"), MsgType.MSG_ERROR);
				return false;
			}
			if (String.valueOf(lotList.get(0).getUdfValue(efficiency)) != null && !String
					.valueOf(lotList.get(0).getUdfValue(efficiency)).equals(String.valueOf(newLot.getUdfValue(efficiency)))) {
				buildConsoleMessage("Lot", Message.getString("pvc.lot_efficiency_not_same"), MsgType.MSG_ERROR);
				return false;
			}
			if (lotList.get(0).getGrade1() != null && !lotList.get(0).getGrade1().equals(newLot.getGrade1())) {
				buildConsoleMessage("Lot", Message.getString("pvc.lot_grade_not_same"), MsgType.MSG_ERROR);
				return false;
			}
			if (String.valueOf(lotList.get(0).getUdfValue(gear)) != null && !String
					.valueOf(lotList.get(0).getUdfValue(gear)).equals(String.valueOf(newLot.getUdfValue(gear)))) {
				buildConsoleMessage("Lot", Message.getString("pvc.lot_gear_not_same"), MsgType.MSG_ERROR);
				return false;
			}
			if (String.valueOf(lotList.get(0).getUdfValue(power)) != null && !String
					.valueOf(lotList.get(0).getUdfValue(power)).equals(String.valueOf(newLot.getUdfValue(power)))) {
				buildConsoleMessage("Lot", Message.getString("pvc.lot_power_not_same"), MsgType.MSG_ERROR);
				return false;
			}
			
			if (lotList.get(0).getCustomerPartId() != null && !lotList.get(0).getCustomerPartId().equals(newLot.getCustomerPartId())) {
				buildConsoleMessage("Lot", Message.getString("pvc.lot_customer_part_not_same"), MsgType.MSG_ERROR);
				return false;
			}
			if (!StringUtils.equals(packageType.getSourceMainMatType(), lotList.get(0).getMainMatType())) {
				buildConsoleMessage("Lot", Message.getString("mm.pack_type_not_same"), MsgType.MSG_ERROR);
				return false;
			}
		} catch (Exception e) {
			buildConsoleMessage("Lot", Message.getString(e.getMessage()), MsgType.MSG_ERROR);
	        return false;
		}
		return true;
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
	@SuppressWarnings("unchecked")
	public List<Lot> getLotList() {
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll((List<Lot>) tableManager.getInput());
		return lots;
	}
	
	private void packAdapter(Object object) {
		try {
			//点击包装按钮，包装所有Lot
			List<Lot> lots = new ArrayList<Lot>();
			lots.addAll((List<Lot>)getLotList());
			if (lots.size() == 0) {
				buildConsoleMessage("Lot", Message.getString("error.no_lot_input"), MsgType.MSG_WARNING);
				return;
			}
			if (packLot(lots)) {
				mediaPlay(MEDIA_TYPE_PACK_SUCCESS);
			} else {
				mediaPlay(MEDIA_TYPE_PACK_FAIL);
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@SuppressWarnings("unchecked")
	private void deleteAdapter(Object object) {
		try {
			List<Lot> lotList = (List<Lot>)(List<? extends Object>) tableManager.getInput();
			List<Lot> lots = new ArrayList<Lot>();
			for (Lot lot : lotList) {
				lots.add(lot);
			}
			List<Object> os = tableManager.getCheckedObject();
			if(os.size() != 0) {
				for (Object obj : os) {
					Lot l = (Lot) obj;
					lots.remove(l);
				}
			}
			tableManager.setInput(lots);
			tableManager.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}

	private void refreshAdapter(Object object) {
		try {
			parentLot = null;
			setPackageType(null);
			tableManager.setInput(Lists.newArrayList());
			consoleComposite.getfMessageConsole().clear();
			lotIdField.setValue(null);
			lotIdField.refresh();
			lotIdField.getTextControl().forceFocus();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}
	
	public PackageType getPackageTypeByName() {
		try {
			PackManager packManager = Framework.getService(PackManager.class);
			
			packageType = packManager.getPackageType(Env.getOrgRrn(), PACKAGE_TYPE_NAME, PackageType.OBJECT_TYPE_LOT);	
			
			return packageType;
		} catch (Exception e) {		
			buildConsoleMessage("Lot", Message.getString(e.getMessage()), MsgType.MSG_ERROR);
	        return null;
		}
	}
	
	public PackageType getPackageType() {
		return packageType;
	}

	public void setPackageType(PackageType packageType) {
		this.packageType = packageType;
	}

	/**
	 * 播放提示声
	 * @param isSuccess
	 * @throws JavaLayerException
	 */
	public void mediaPlay(String mediaType) throws JavaLayerException {
		String path = null;
		if (MEDIA_TYPE_SCAN_FAIL.equals(mediaType)) {
			path = "/media/fail.mp3";
		} else if (MEDIA_TYPE_PACK_FAIL.equals(mediaType)) {
			path = "/media/packfail.mp3";
		} else if (MEDIA_TYPE_PACK_SUCCESS.equals(mediaType)) {
			path = "/media/packsuccess.mp3";
		} else {
			path = "/media/success.mp3";
		}
		InputStream inputStream = this.getClass().getResourceAsStream(path);
		BufferedInputStream buffer = new BufferedInputStream(inputStream);
		Player player = new Player(buffer);
		Thread t = new Thread(new Runnable() {

			@Override
			public void run() {
				try {
					player.play();
				} catch (JavaLayerException e) {
					 ExceptionHandlerManager.asyncHandleException(e);
					 return;
				}
			}
		});
		t.start();
	}
	
	private void buildConsoleMessage(String code, String message, MsgType messageLevel) {
		FMessage fMessage = new FMessage(code, message, messageLevel, messageLevel.getIndex());
		consoleComposite.getfMessageConsole().put(fMessage);
	}

}