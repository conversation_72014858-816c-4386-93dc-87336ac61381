package com.glory.mes.pvc.lot.supplement;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.scrap.supplement.ScrapSupplementEntityForm;
import com.glory.mes.wip.lot.scrap.supplement.ScrapSupplementSection;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.LotScrap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 不良补片<br/>
 * 返工补片目前只支持补片到固定的返工流程，后续根据实际情况再做调整
 * <AUTHOR>
 *
 */
public class PvcSupplementSection extends ScrapSupplementSection {
	
	private static final String PROCEDURE_NAME = "REWORK-PROCEDURE";

	public PvcSupplementSection(ADTable adTable, String rightTitle, String leftTitle) {
		super(adTable, rightTitle, leftTitle);
	}
	
	@Override
	protected ScrapSupplementEntityForm getForm(Composite bottomFormBody) {
		return new PvcSupplementEntityForm(this, getPvcADManager(), bottomFormBody, SWT.NONE, null,
				getAdTable().getFields(), 1, getManagedForm().getMessageManager());
	}

	@Override
	protected void addAdapter() {
		try {
			getManagedForm().getMessageManager().removeAllMessages();
			if (getEntityForm().saveToObject()) {
				LotScrap lotScrap = getEntityForm().getScrapAction();

				if (scrapSupplementMap.size() == 0) {
					UI.showWarning(String.format(Message.getString("common.please_select"),
							ComponentUnit.class.getSimpleName()));
					return;
				}
				
				LotAction commentAction = new LotAction();
				commentAction.setActionComment(lotScrap.getActionComment());

				PvcSupplementEntityForm entityForm = (PvcSupplementEntityForm) getEntityForm();
				if (entityForm.isScrap()) {
					// 报废
					Map<String, String> unitPositionMap = Maps.newHashMap();
					Set<ComponentUnit> scrapUnits = scrapSupplementMap.keySet();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (ComponentUnit scrapUnit : scrapUnits) {
						LotAction lotAction = new LotAction();
						lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
						lotAction.setActionReason(DBUtil.toString(scrapUnit.getAttribute1()));
						lotAction.setLotRrn(scrapUnit.getParentUnitRrn());
						lotAction.setActionCode(scrapUnit.getActionCode());
						lotAction.setActionUnits(Lists.newArrayList(scrapUnit));
						lotAction.setActionComment(lotScrap.getActionComment());
						scrapLotActions.add(lotAction);

						// source wafer使用scrap wafer的position
						ComponentUnit sourceUnit = scrapSupplementMap.get(scrapUnit);
						unitPositionMap.put(sourceUnit.getComponentId(), scrapUnit.getPosition());
					}

					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.lotScrapSupplement(targetLot, scrapSupplementMap, scrapLotActions, commentAction,
							unitPositionMap, Env.getSessionContext());
				} else {
					PrdManager prdManager = Framework.getService(PrdManager.class);
					
					Procedure procedure = new Procedure();
					procedure.setName(PROCEDURE_NAME);
					procedure.setOrgRrn(Env.getOrgRrn());
					procedure = (Procedure)prdManager.getActivePrdBase(procedure);
					
					if (procedure == null) {
						UI.showError(String.format(
								Message.getString("pvc.create_rework_procedure_first"), PROCEDURE_NAME));
						return;
					}
					
					//返工
					Map<String, String> unitPositionMap = Maps.newHashMap();
					Set<ComponentUnit> reworkUnits = scrapSupplementMap.keySet();
					
					for (ComponentUnit reworkUnit : reworkUnits) {
						// source wafer使用scrap wafer的position
						ComponentUnit sourceUnit = scrapSupplementMap.get(reworkUnit);
						unitPositionMap.put(sourceUnit.getComponentId(), reworkUnit.getPosition());
					}
					
//					PvcManager pvcManager = Framework.getService(PvcManager.class);
//					pvcManager.lotReworkSupplement(targetLot, scrapSupplementMap, procedure, commentAction, unitPositionMap, Env.getSessionContext());
				}
				
				UI.showInfo(Message.getString("common.operation_successed"));
				refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public ADManager getPvcADManager() {
		try {
			ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			return adManager;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

}
