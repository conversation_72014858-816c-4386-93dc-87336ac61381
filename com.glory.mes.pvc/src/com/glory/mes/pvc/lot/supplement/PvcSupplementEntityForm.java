package com.glory.mes.pvc.lot.supplement;

import java.util.LinkedHashMap;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.client.PvcADManager;
import com.glory.mes.wip.lot.scrap.supplement.ScrapSupplementEntityForm;
import com.google.common.collect.Maps;

public class PvcSupplementEntityForm extends ScrapSupplementEntityForm {

	private static final Logger logger = Logger.getLogger(PvcSupplementEntityForm.class);
	
	protected static final String REWORK_CODE = "ReworkCode";
	
	private PvcSupplementSection supplementSection;
	
	protected IField fieldSupplementType;
	protected static final String SUPPLEMENTTYPE = "SupplementType";
	protected static final String SUPPLEMENTTYPE_ID = "supplementType";
	
	private static final String TYPE_SCRAP = "0";
	private static final String TYPE_REWORK = "1";
	
	private boolean scrap = true;

	public PvcSupplementEntityForm(PvcSupplementSection supplementSection, ADManager adManager, Composite parent, int style, Object object,
			List<ADField> adfields, int gridY, IMessageManager mmng) {
		super(adManager, parent, style, object, adfields, gridY, mmng);
		this.supplementSection = supplementSection;
	}

	@Override
	public void addFields() {
		try {
			ADField mandatoryADField = new ADField();
			mandatoryADField.setIsMandatory(true);
			LinkedHashMap<String, String> items = Maps.newLinkedHashMap();
			items.put(TYPE_SCRAP, Message.getString("wip.scrap"));
			items.put(TYPE_REWORK, Message.getString("wip.recovery"));
			fieldSupplementType = createRadioGroup(SUPPLEMENTTYPE_ID, Message.getString("pvc.supplement_type"), items, 0);
			
			fieldSupplementType.addValueChangeListener(new IValueChangeListener() {
				
				@Override
				public void valueChanged(Object sender, Object newValue) {
					try {
						ADManager adManager = Framework.getService(PvcADManager.class, "pvc");
			    		ADTable adTable  = adManager.getADTable(Env.getOrgRrn(), "ADURefListView");
			    		
			    		String referenceName = null;
			    		if (TYPE_SCRAP.equals(DBUtil.toString(newValue))) {
							// 报废
			    			referenceName = getScrapCode();
			    			setScrap(true);
						} else {
							// 返工
							referenceName = getReworkCode();
							setScrap(false);
						}
						
			    		String whereClause = " referenceName = '" + referenceName + "'";
						List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(),
								Env.getMaxResult(), whereClause, null);
						RefTableField field = (RefTableField) fieldDefectCode;
						field.setValue(null, true);
						field.setInput(list);
						supplementSection.refresh();
			    	} catch (Exception e){
						ExceptionHandlerManager.asyncHandleException(e);
					}
				}
			});

			fieldDefectCode = createUserRefList(DEFECTCODE_ID, Message.getString("pvc.bad_code") + "*",
					getScrapCode(), true);
			fieldDefectCode.setADField(mandatoryADField);

			fieldReasonCode = createText(REASONCODE, Message.getString("pvc.bad_reason") + "*", 64);
			fieldReasonCode.setADField(mandatoryADField);

			fieldComments = createText(COMMENTS_ID, Message.getString("wip.remark"), "", 128);

			addField(SUPPLEMENTTYPE, fieldSupplementType);
			addField(DEFECTCODE, fieldDefectCode);
			addField(REASONCODE, fieldReasonCode);
			addField(COMMENTS, fieldComments);
		} catch (Exception e) {
			logger.error("PvcSupplementEntityForm : addFields()", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public boolean isScrap() {
		return scrap;
	}

	public void setScrap(boolean scrap) {
		this.scrap = scrap;
	}

	protected String getReworkCode() {
		return REWORK_CODE;
	}
	
}
