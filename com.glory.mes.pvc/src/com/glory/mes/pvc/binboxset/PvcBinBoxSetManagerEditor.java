package com.glory.mes.pvc.binboxset;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.IToolItemListener;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.CopyFromDialog;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.excel.download.Download;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcPackManager;
import com.glory.mes.pvc.model.BinBoxSet;
import com.glory.mes.pvc.model.BinBoxSetLine;
import com.glory.mes.pvc.model.LineConfig;
import com.google.common.collect.Lists;

public class PvcBinBoxSetManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.binboxset.PvcBinBoxSetManagerEditor";
	
	public static String ADD_TABLE_NAME = "PvcBinBoxSetLineAdd";

	private static final String FIELD_LINES = "lines";
	private static final String FIELD_COPYFROM = "copyFrom";

	private static final String BUTTON_COPYFROM = "copyFrom";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_FROZEN = "frozen";
	private static final String BUTTON_ACTIVE = "active";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_EXPORT = "export";
	private static final String BUTTON_NAME_ADD = "add";
	private static final String BUTTON_NAME_REMOVE = "remove";

	protected ListTableManagerField linesField;
	protected RefTableField copyFromField;
	
	private EntityBlock entityBlock;
	private EntitySection entitySection;
	
	private ToolItem itemSave;
	private ToolItem itemFrozen;
	private ToolItem itemActive;
	private ToolItem itemDelete;
	
	private ListTableManager tableManager;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		linesField = form.getFieldByControlId(FIELD_LINES, ListTableManagerField.class);
		tableManager = linesField.getListTableManager();
		copyFromField = form.getFieldByControlId(FIELD_COPYFROM, RefTableField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_COPYFROM), this::copyFromAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_FROZEN), this::frozenAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ACTIVE), this::activeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(IToolItemListener.TYPE_IMPORT), this::importAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EXPORT), this::exportAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_ADD), this::addAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_REMOVE), this::delAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		init();
	}
	
	private void init() {
		entityBlock = (EntityBlock) form.getBlock();
		entitySection = entityBlock.getCurrentPage();
		
		itemSave = (ToolItem) form.getButtonByControl(null, BUTTON_SAVE);
		itemFrozen = (ToolItem) form.getButtonByControl(null, BUTTON_FROZEN);
		itemActive = (ToolItem) form.getButtonByControl(null, BUTTON_ACTIVE);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
	}

	private void copyFromAdapter(Object object) {
		List<ADField> fields = entitySection.getTable().getFields();
		for (ADField field : fields) {
			if ("copyFrom".equals(field.getName())) {
				CopyFromDialog dialog = new CopyFromDialog(Display.getCurrent().getActiveShell(), field);
				if (dialog.open() == Dialog.OK) {
					String objectRrn = dialog.getJobRrn();
					try {
						form.getMessageManager().removeAllMessages();
						if(objectRrn == null || "".equals(objectRrn.trim())) {
							return;
						}
						VersionControl copyFromObject = (VersionControl)entitySection.getAdObject();
						copyFromObject.setObjectRrn(new Long(objectRrn));
						copyFromObject = (VersionControl)adManager.getEntity(copyFromObject);
						if (copyFromObject != null) {
							VersionControl cloneObject = (VersionControl)copyFromObject.clone();
							cloneObject.setVersion(null);
							cloneObject.setStatus("");
							cloneObject.setActiveTime(null);
							cloneObject.setActiveUser(null);
							setAdObject(cloneObject);
							refresh();
							
							List<BinBoxSetLine> setLines = adManager.getEntityList(Env.getOrgRrn(), BinBoxSetLine.class, 
									Integer.MAX_VALUE, " setRrn = " + copyFromObject.getObjectRrn(), "");
							if (setLines != null) {
								for (BinBoxSetLine setLine : setLines) {
									setLine.setObjectRrn(null);
									setLine.setSetRrn(null);
								}
								tableManager.setInput(setLines);
							} else {
								tableManager.setInput(new ArrayList<BinBoxSetLine>());
							}				
						}
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
				
				break;
			}
		}
	}

	@SuppressWarnings("unchecked")
	private void saveAdapter(Object object) {
		try {
    		form.getMessageManager().removeAllMessages();
    		if (entitySection.getAdObject() != null) {
    			ADBase oldBase = entitySection.getAdObject();
    			
    			boolean saveFlag = true;
    			for (IForm detailForm : entitySection.getDetailForms()) {
    				if (!detailForm.saveToObject()) {
    					saveFlag = false;
    				}
    			}
    			if (saveFlag) {
    				for (IForm detailForm : entitySection.getDetailForms()) {
    					PropertyUtil.copyProperties(entitySection.getAdObject(), detailForm.getObject(),
    							detailForm.getCopyProperties());
    				}
    				
    				BinBoxSet binBoxSet = (BinBoxSet) entitySection.getAdObject();
    				if(checkBinBoxSetState(Env.getOrgRrn(), binBoxSet)) {
    					UI.showInfo(Message.getString("pvc.bin_box_set_is_used"));
    					return;
    				}
    				
    				List<BinBoxSetLine> lines = (List<BinBoxSetLine>)(List<? extends Object>)tableManager.getInput();
    				List<BinBoxSetLine> allLines = new ArrayList<BinBoxSetLine>();
    				for (BinBoxSetLine line : lines) {
    					line.setObjectRrn(null);
    					line.setOrgRrn(Env.getOrgRrn());
    					line.setSeqNo(Long.valueOf(line.getBoxId()));
    					allLines.add(line);
    				}										   				
    				binBoxSet.setLines(allLines);
    				
    				PvcPackManager packManager = Framework.getService(PvcPackManager.class);    				
    				binBoxSet = packManager.saveBinBoxSet(binBoxSet, Env.getSessionContext());
    				UI.showInfo(Message.getString("common.save_successed"));
    				setAdObject(adManager.getEntity(binBoxSet));
    				this.refresh();
    				ADBase newBase = entitySection.getAdObject();
    				if (oldBase.getObjectRrn() == null) {
    					entityBlock.refreshAdd(newBase);
    				} else {
    					entityBlock.refreshUpdate(newBase);
    				}
    			}
    		}
    		
    	} catch (Exception e) { 
    		ExceptionHandlerManager.asyncHandleException(e);
    		return;
    	}
	}
	
	/**
	 * 检查BIN盒方案是否已经绑定
	 * @param orgRrn
	 * @param binBoxSet
	 * @return
	 */
	public boolean checkBinBoxSetState(long orgRrn, BinBoxSet binBoxSet) {
		boolean isAttached = false;
		if (binBoxSet != null && binBoxSet.getObjectRrn() != null) {
			List<LineConfig> binBoxSets = adManager.getEntityList(orgRrn, LineConfig.class, Integer.MAX_VALUE,
					" binBoxSetRrn = '" + binBoxSet.getObjectRrn() + "'", "");
			if (binBoxSets.size() > 0) {
				isAttached = true;
			}
		}
		return isAttached;
	}

	private void frozenAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			if (entitySection.getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : entitySection.getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					ADBase obj;
					PvcPackManager pvcPackManager = Framework.getService(PvcPackManager.class);
					if (VersionControl.STATUS_UNFROZNE.equalsIgnoreCase(((VersionControl)entitySection.getAdObject()).getStatus())) {
						obj = pvcPackManager.statusChangeBinBoxSet((BinBoxSet) entitySection.getAdObject(), VersionControl.STATUS_FROZNE, Env.getSessionContext());
						UI.showInfo(Message.getString("common.frozen_success"));
					} else {
						obj = pvcPackManager.statusChangeBinBoxSet((BinBoxSet) entitySection.getAdObject(), VersionControl.STATUS_UNFROZNE, Env.getSessionContext());
						UI.showInfo(Message.getString("common.unfrozen_success"));
					}

					setAdObject(adManager.getEntity(obj));
					refresh();
					entityBlock.refreshUpdate(entitySection.getAdObject());
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void activeAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			if (entitySection.getAdObject() != null) {
				ADBase obj = null;
				PvcPackManager pvcPackManager = Framework.getService(PvcPackManager.class);
				BinBoxSet binBoxSet = (BinBoxSet) entitySection.getAdObject();
				if (VersionControl.STATUS_ACTIVE.equals(binBoxSet.getStatus())) {
					obj = pvcPackManager.statusChangeBinBoxSet(binBoxSet, VersionControl.STATUS_INACTIVE, Env.getSessionContext());
					UI.showInfo(Message.getString("common.inactive_success"));
				} else if (VersionControl.STATUS_INACTIVE.equals(binBoxSet.getStatus())
						|| VersionControl.STATUS_FROZNE.equals(binBoxSet.getStatus())) {
					obj = pvcPackManager.statusChangeBinBoxSet(binBoxSet, VersionControl.STATUS_ACTIVE, Env.getSessionContext());
					UI.showInfo(Message.getString("common.active_success"));
				}
				if (!Objects.isNull(obj)) {
					setAdObject(adManager.getEntity(obj));
				}
				entitySection.refresh();
				entityBlock.refreshUpdate(entitySection.getAdObject());
			}

		} catch (Exception var6) {
			ExceptionHandlerManager.asyncHandleException(var6);
		}
	}

	private void deleteAdapter(Object object) {
		if (entitySection.getAdObject() != null) {
    		try {
    			boolean confirmDelete = UI.showConfirm(Message.getString("common.confirm_delete"));
    			if (confirmDelete) {
    				PvcPackManager packManager = Framework.getService(PvcPackManager.class);
    				BinBoxSet binBoxSet = (BinBoxSet) entitySection.getAdObject();
    				if(checkBinBoxSetState(Env.getOrgRrn(), binBoxSet)) {
    					UI.showInfo(Message.getString("pvc.bin_box_set_is_used"));
    					return;
    				}
    				packManager.deleteBinBoxSet(binBoxSet, Env.getSessionContext());
    				UI.showInfo(Message.getString("common.delete_successed"));
    				setAdObject(entitySection.createAdObject());
    				entitySection.refresh();
    				entityBlock.refreshDelete(binBoxSet);
    			}
    		} catch (Exception e) {
    			ExceptionHandlerManager.asyncHandleException(e);
    			return;
    		}
    	}
	}

	private void importAdapter(Object object) {
		PvcBinBoxSetUpload upload = new PvcBinBoxSetUpload(entitySection.getTable().getAuthorityKey(), null , null);
		if (upload.getUploadProgress().init()) {
			if (upload.run()) {
				refresh();
			}
		}
	}

	private void exportAdapter(Object object) {
		List<BinBoxSet> binBoxSetList = new ArrayList<>();
		if (entityBlock.getTableManager().getTableManager().getMultiSelectedObjects() != null) {
			binBoxSetList = Lists.newArrayList(entityBlock.getTableManager().getTableManager().getMultiSelectedObjects())
					.stream().map(o -> ((BinBoxSet)o)).collect(Collectors.toList());
		}
		if (CollectionUtils.isNotEmpty(binBoxSetList)) {
			Download download = new Download(entitySection.getTable().getAuthorityKey(), null);
			if (download.getDownloadProgress().init()) {
				download.run(binBoxSetList);
			}
		} else {
			DefaultDownloadWriter.exportTemplate(entitySection.getTable().getAuthorityKey(), null);
		}
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected void addAdapter(Object object) {
    	try {
    		BinBoxSet binBoxSet = (BinBoxSet) entitySection.getAdObject();
    		if (binBoxSet != null && !(StringUtils.isEmpty(binBoxSet.getStatus()) || "UnFrozen".equals(binBoxSet.getStatus()))) {
    			return;
    		}
    		
    		ADTable adTable = adManager.getADTable(Env.getOrgRrn(), ADD_TABLE_NAME);
    		EntityDialog dialog = new PvcBinBoxSetAddDialog(adTable, new BinBoxSetLine());
    		if (dialog.open() == IDialogConstants.OK_ID) {
    			BinBoxSetLine binBoxSetLine = (BinBoxSetLine) dialog.getAdObject();
    			List<BinBoxSetLine> allLines = new ArrayList<BinBoxSetLine>();
    			List<BinBoxSetLine> lines = (List<BinBoxSetLine>)(List) tableManager.getInput();
    			allLines.addAll(lines);
    			allLines.add(binBoxSetLine);
    			tableManager.setInput(allLines);
    		}
    	} catch (Exception e) {
    		ExceptionHandlerManager.asyncHandleException(e);
    		return;
    	}
    }

    @SuppressWarnings("unchecked")
	protected void delAdapter(Object object) {
    	try {
    		BinBoxSet binBoxSet = (BinBoxSet) entitySection.getAdObject();
    		if (binBoxSet != null && !(StringUtils.isEmpty(binBoxSet.getStatus()) || "UnFrozen".equals(binBoxSet.getStatus()))) {
    			return;
    		}
    		
    		List<BinBoxSetLine> allLines = (List<BinBoxSetLine>)(List<? extends Object>)tableManager.getInput();
    		List<BinBoxSetLine> lines = new ArrayList<BinBoxSetLine>();
    		for(BinBoxSetLine allLine : allLines){
    			lines.add(allLine);
    		}				
    		List<Object> os = tableManager.getCheckedObject();
    		if (os.size() != 0) {
    			for (Object o : os) {
    				BinBoxSetLine line = (BinBoxSetLine) o;
    				lines.remove(line);
    			}
    		}
    		tableManager.setInput(lines);
    	} catch (Exception e) {
    		ExceptionHandlerManager.asyncHandleException(e);
    		return;
    	}
	}
	
	public void refresh() {
        if (entitySection.getAdObject() != null) {
        	BinBoxSet positionSet = (BinBoxSet) entitySection.getAdObject();
            if (positionSet.getLines() == null) {
                positionSet.setLines(new ArrayList<BinBoxSetLine>());
            }
            tableManager.setInput(positionSet.getLines());
        }
        for (IForm detailForm : entitySection.getDetailForms()) {
            detailForm.setObject(entitySection.getAdObject());
            detailForm.loadFromObject();
        }
        form.getMessageManager().removeAllMessages();
    }
	
	public void selectionChanged(Object object) {
		try {
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			Object label = selection.getFirstElement();
			try {
				if (label != null && ((ADBase)label).getObjectRrn() != null) {
					setAdObject(getADManger().getEntity((ADBase)label));
				} else if (label != null) {
					setAdObject((ADBase)label);
					setAdObject(entitySection.createAdObject());
				}
				entitySection.refresh();
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void setAdObject(ADBase adObject) {
		entitySection.setAdObject(adObject);
		BinBoxSet binBoxSet = (BinBoxSet) adObject;
		if (!Objects.isNull(binBoxSet)) {
			List<BinBoxSetLine> binBixSetLines = binBoxSet.getLines();
			if (CollectionUtils.isNotEmpty(binBixSetLines)) {
				tableManager.setInput(binBixSetLines);
				tableManager.refresh();
			}
		}
		VersionControl newObject = (VersionControl) entitySection.getAdObject();
		if (newObject != null) {
			statusChanged(newObject.getStatus());
		} else {
			statusChanged("");
		}
	}
	
	public void statusChanged(String newStatus) {
		if ("UnFrozen".equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
			itemFrozen.setText(Message.getString("common.frozen"));
			itemFrozen.setEnabled(true);
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString("common.active"));
			itemActive.setEnabled(false);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(true);	
			setEnable(true);
		} else if ("Frozen".equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
			itemFrozen.setText(Message.getString("common.unfrozen"));
			itemFrozen.setEnabled(true);
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString("common.active"));
			itemActive.setEnabled(true);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);	
			setEnable(false);
		} else if ("Active".equals(newStatus)) {
			itemActive.setImage(SWTResourceCache.getImage("inactive"));
			itemActive.setText(Message.getString("common.inactive"));
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(true);
			setEnable(false);
		} else if ("InActive".equals(newStatus)) {
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString("common.active"));
			itemActive.setEnabled(true);
			itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
			itemFrozen.setText(Message.getString("common.unfrozen"));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);		
			setEnable(false);
		} else {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(true);
			itemActive.setEnabled(false);
			setEnable(true);
		}
	}
	
	protected void setEnable(boolean enableFlag) {
		entityBlock.getCurrentPage().getDetailForms().get(0).setEnabled(enableFlag);
	}

}