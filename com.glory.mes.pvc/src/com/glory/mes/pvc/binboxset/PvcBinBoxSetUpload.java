package com.glory.mes.pvc.binboxset;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.glory.common.label.client.LabelManager;
import com.glory.common.label.model.Label;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pvc.client.PvcPackManager;
import com.glory.mes.pvc.model.BinBoxSet;

public class PvcBinBoxSetUpload extends Upload {

	public PvcBinBoxSetUpload(String authorityName, String buttonName, String tableName) {
		super(authorityName, buttonName , tableName);
	}

	@Override
	protected void cudEntityList() {
		try {
			List<ADBase> uploadList = progress.getUploadList();
			LabelManager labelManager = Framework.getService(LabelManager.class);
			PvcPackManager pvcPackManager = Framework.getService(PvcPackManager.class);
			if (CollectionUtils.isNotEmpty(uploadList)) {
				for (int i = 0; i < uploadList.size(); i++) {
					BinBoxSet upload = (BinBoxSet) uploadList.get(i);
					if(StringUtils.isNotEmpty(upload.getSmallLabel())) {
						Label label = labelManager.getLabelByName(Env.getOrgRrn(), upload.getSmallLabel(), false, true);
						upload.setSmallLabelRrn(label.getObjectRrn());
					}
					
					if(CollectionUtils.isNotEmpty(upload.getLines())) {
						upload.getLines().stream().filter( p -> StringUtils.isNotEmpty(p.getSmallLabel())).forEach(line ->{
							Label lineLabel = labelManager.getLabelByName(Env.getOrgRrn(), line.getSmallLabel(), false, true);
							line.setSmallLabelRrn(lineLabel.getObjectRrn());
						});
					}
					pvcPackManager.saveBinBoxSet(upload, Env.getSessionContext());
				}
				UI.showInfo(Message.getString("common.import_sucess"));// 弹出提示框
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
    		return;
		}
	}

}
