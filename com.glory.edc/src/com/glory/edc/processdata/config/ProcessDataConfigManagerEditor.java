package com.glory.edc.processdata.config;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.osgi.service.event.Event;

import com.glory.edc.client.EDCManager;
import com.glory.edc.exception.EDCExceptionBundle;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcProcessDataConfig;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.query.SearchMultiDialog;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.row.ListRowEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.google.common.collect.Lists;

public class ProcessDataConfigManagerEditor extends GlcEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.edc/com.glory.edc.processdata.config.ProcessDataConfigManagerEditor";

	public static final String FIELD_CONFIGQUERYFORM = "configQueryForm";
	public static final String FIELD_CONFIGEDITORFORM = "configEditorForm";
	public static final String FIELD_CONFIGNAMEFORM = "configNameForm";
	public static final String FIELD_CONFIGDETAILFORM = "configDetailForm";
	public static final String FIELD_EQPTYPEFORM = "eqpTypeForm";
	public static final String FIELD_STEPNAMEFORM = "stepNameForm";
	public static final String FIELD_EDCDATAITEMINFO = "edcDataItemInfo";

	public static final String BUTTON_CONFIGNAMEADD = "ConfigNameAdd";
	public static final String BUTTON_CONFIGNAMEDELETE = "ConfigNameDelete";
	public static final String BUTTON_EQPTYPEADD = "eqpTypeAdd";
	public static final String BUTTON_EQPTYPEADDFROM = "eqpTypeAddFrom";
	public static final String BUTTON_EQPTYPEDELETE = "eqpTypeDelete";
	public static final String BUTTON_STEPADD = "stepAdd";
	public static final String BUTTON_STEPADDFROM = "stepAddFrom";
	public static final String BUTTON_STEPDELETE = "stepDelete";
	public static final String BUTTON_DATAITEMADD = "dataItemAdd";
	public static final String BUTTON_DATAITEMDELETE = "dataItemDelete";

	protected GlcFormField configQueryFormField;
	protected GlcFormField configEditorFormField;
	protected ListTableManagerField configNameFormField;
	protected ListTableManagerField configDetailFormField;
	protected ListTableManagerField eqpTypeFormField;
	protected ListTableManagerField stepNameFormField;
	protected ListTableManagerField edcDataItemInfoField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		configQueryFormField = form.getFieldByControlId(FIELD_CONFIGQUERYFORM, GlcFormField.class);
		configNameFormField = configQueryFormField.getFieldByControlId(FIELD_CONFIGNAMEFORM, ListTableManagerField.class);
		configNameFormField.setValue(getADManger().getADURefList(Env.getOrgRrn(), "ProcessDataConfig"));
		configNameFormField.refresh();
		configDetailFormField = configQueryFormField.getFieldByControlId(FIELD_CONFIGDETAILFORM, ListTableManagerField.class);
		
		configEditorFormField = form.getFieldByControlId(FIELD_CONFIGEDITORFORM, GlcFormField.class);
		eqpTypeFormField = configEditorFormField.getFieldByControlId(FIELD_EQPTYPEFORM, ListTableManagerField.class);
		stepNameFormField = configEditorFormField.getFieldByControlId(FIELD_STEPNAMEFORM, ListTableManagerField.class);
		edcDataItemInfoField = configEditorFormField.getFieldByControlId(FIELD_EDCDATAITEMINFO, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, configNameFormField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::configNameFormSelectionChanged);
		subscribeAndExecute(eventBroker, configDetailFormField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::configDetailFormSelectionChanged);

		subscribeAndExecute(eventBroker, configQueryFormField.getFullTopic(BUTTON_CONFIGNAMEADD), this::ConfigNameAddAdapter);
		subscribeAndExecute(eventBroker, configQueryFormField.getFullTopic(BUTTON_CONFIGNAMEDELETE), this::ConfigNameDeleteAdapter);
		subscribeAndExecute(eventBroker, configQueryFormField.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, configQueryFormField.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, configQueryFormField.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_EQPTYPEADD), this::eqpTypeAddAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_EQPTYPEADDFROM), this::eqpTypeAddFromAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_EQPTYPEDELETE), this::eqpTypeDeleteAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_STEPADD), this::stepAddAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_STEPADDFROM), this::stepAddFromAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_STEPDELETE), this::stepDeleteAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_DATAITEMADD), this::dataItemAddAdapter);
		subscribeAndExecute(eventBroker, configEditorFormField.getFullTopic(BUTTON_DATAITEMDELETE), this::dataItemDeleteAdapter);
	}

	private void ConfigNameAddAdapter(Object object) {
		add((ListRowEditorTableManager)configNameFormField.getListTableManager(), false);
	}

	private void ConfigNameDeleteAdapter(Object object) {
		try {
			List<Object> os = configNameFormField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isNotEmpty(os)) {
				List<ADBase> deleteValues = Lists.newArrayList();
				List<ADURefList> values = os.stream().map(obj -> (ADURefList)obj).collect(Collectors.toList());
				EDCManager edcManager = Framework.getService(EDCManager.class);
				for (ADURefList value : values) {
					List<EdcProcessDataConfig> edcProcessDataConfigs = edcManager.getEdcProcessDataConfig(Env.getOrgRrn(), value.getKey(), false);
					if (CollectionUtils.isNotEmpty(edcProcessDataConfigs)) {
						deleteValues.addAll(edcProcessDataConfigs);
					}
					if (value.getObjectRrn() != null) {
						deleteValues.add(value);
					}
					((ListRowEditorTableManager)configNameFormField.getListTableManager()).removeEditorObjects(value);
				}
				
				if (CollectionUtils.isNotEmpty(deleteValues)) {
					getADManger().deleteEntityList(deleteValues, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				}
				configNameFormField.getListTableManager().removeList(values);
				configDetailFormField.setValue(null);
				configDetailFormField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    	return;
		}
	}

	private void newAdapter(Object object) {
		configDetailFormField.refresh();
		eqpTypeFormField.setValue(null);
		eqpTypeFormField.refresh();
		stepNameFormField.setValue(null);
		stepNameFormField.refresh();
		edcDataItemInfoField.setValue(null);
		edcDataItemInfoField.refresh();
	}

	private void saveAdapter(Object object) {
		try {
			ADURefList adURefList = (ADURefList) configNameFormField.getListTableManager().getSelectedObject();
			EdcProcessDataConfig edcProcessDataConfig = (EdcProcessDataConfig) configDetailFormField.getListTableManager().getSelectedObject();
			if (edcProcessDataConfig == null) {
				edcProcessDataConfig = new EdcProcessDataConfig();
				edcProcessDataConfig.setOrgRrn(Env.getOrgRrn());
			}
			
			Long configSeqNo = 0L;
			if (CollectionUtils.isNotEmpty(configDetailFormField.getListTableManager().getInput())) {
				configSeqNo = configDetailFormField.getListTableManager().getInput().
						stream().map(x -> ((EdcProcessDataConfig)x).getSeqNo()).max(Comparator.comparingLong(Long::valueOf)).get();
			}
			String eqpTypes = eqpTypeFormField.getListTableManager().getInput().stream().map(x -> ((ADURefList)x).getKey()).collect(Collectors.joining(";"));
			String stepNames = stepNameFormField.getListTableManager().getInput().stream().map(x -> ((Step)x).getName()).collect(Collectors.joining(";"));
			String itemNames = edcDataItemInfoField.getListTableManager().getInput().stream().map(x -> ((EdcItem)x).getName()).collect(Collectors.joining(";"));
			List<ADBase> abBases = Lists.newArrayList();
			if (!StringUtil.isEmpty(eqpTypes) || !StringUtil.isEmpty(stepNames) || !StringUtil.isEmpty(itemNames)) {
				if (adURefList == null) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
	                return;
				}
				edcProcessDataConfig.setEqpTypes(eqpTypes);
				edcProcessDataConfig.setStepNames(stepNames);
				edcProcessDataConfig.setItemNames(itemNames);
				edcProcessDataConfig.setUpdated(new Date());
				edcProcessDataConfig.setConfigName(adURefList.getKey());
				edcProcessDataConfig.setSeqNo(configSeqNo + 10);
				abBases.add(edcProcessDataConfig);
			} else if (adURefList != null && adURefList.getObjectRrn() != null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.ErrorDataException()));
                return;
			}
			
			List<Object> objects = ((ListRowEditorTableManager)configNameFormField.getListTableManager()).getEditorObject();
			Optional<ADURefList>  present = getADManger().getADURefList(Env.getOrgRrn(), "ProcessDataConfig").stream().max(Comparator.comparing(ADURefList::getSeqNo));
			Long seqNo = 1L;
			if (present.isPresent()) {
				seqNo = present.get().getSeqNo() + 10;
			}
			List<String> keys = Lists.newArrayList();
			for (Object obj : objects) {
				ADURefList value = (ADURefList)obj;
				value.setText(adURefList.getKey());
				value.setDescription(adURefList.getKey());
				value.setSeqNo(seqNo);
				value.setReferenceName("ProcessDataConfig");
				abBases.add(value);
				keys.add(value.getKey());
				seqNo = seqNo + 10;
			}
			
			if (CollectionUtils.isNotEmpty(abBases)) {
				getADManger().saveEntityList(abBases, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				for (Object obj : configNameFormField.getListTableManager().getInput()) {
					ADURefList value = (ADURefList)obj;
					if (keys.contains(value.getKey())) {
						((ListRowEditorTableManager)configNameFormField.getListTableManager()).removeEditorObjects(value);
					}
				}
				configNameFormField.setValue(getADManger().getADURefList(Env.getOrgRrn(), "ProcessDataConfig"));
				configNameFormField.refresh();
				
				configDetailFormField.setValue(null);;
				configDetailFormField.refresh();
				newAdapter(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    	return;
		}
	}
	
	private void deleteAdapter(Object object) {
		try {
			EdcProcessDataConfig edcProcessDataConfig = (EdcProcessDataConfig) configDetailFormField.getListTableManager().getSelectedObject();
			if (edcProcessDataConfig != null) {
				getADManger().deleteEntity(edcProcessDataConfig, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				EDCManager edcManager = Framework.getService(EDCManager.class);
				configDetailFormField.setValue(edcManager.getEdcProcessDataConfig(Env.getOrgRrn(), edcProcessDataConfig.getConfigName(), false));
				configDetailFormField.refresh();
			}
			newAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    	return;
		}
	}

	private void configNameFormSelectionChanged(Object object) {
		try {
			newAdapter(null);
			Event event = (Event) object;
			ADURefList adURefList = (ADURefList) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (adURefList != null) {
				EDCManager edcManager = Framework.getService(EDCManager.class);
				configDetailFormField.setValue(edcManager.getEdcProcessDataConfig(Env.getOrgRrn(), adURefList.getKey(), false));
				configDetailFormField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    	return;
		}
	}

	private void configDetailFormSelectionChanged(Object object) {
		try {
			Event event = (Event) object;
			EdcProcessDataConfig edcProcessDataConfig = (EdcProcessDataConfig) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (edcProcessDataConfig != null) {
				List<ADURefList> eqpTypeLists = Lists.newArrayList();
				if (!StringUtil.isEmpty(edcProcessDataConfig.getEqpTypes())) {
					String[] eqpTypes = edcProcessDataConfig.getEqpTypes().split(";");
					for (String eqpType : eqpTypes) {
						ADURefList adURefList = new ADURefList();
						adURefList.setKey(eqpType);
						eqpTypeLists.add(adURefList);
					}
				}
				eqpTypeFormField.getListTableManager().setInput(eqpTypeLists);
				
				List<Step> stepNameLists = Lists.newArrayList();
				if (!StringUtil.isEmpty(edcProcessDataConfig.getStepNames())) {
					String[] stepNames = edcProcessDataConfig.getStepNames().split(";");
					for (String stepName : stepNames) {
						Step step = new Step();
						step.setName(stepName);
						stepNameLists.add(step);
					}
				}
				stepNameFormField.getListTableManager().setInput(stepNameLists);
				
				List<EdcItem> itemNameLists = Lists.newArrayList();
				if (!StringUtil.isEmpty(edcProcessDataConfig.getItemNames())) {
					String[] itemNames = edcProcessDataConfig.getItemNames().split(";");
					for (String itemName : itemNames) {
						EdcItem item = new EdcItem();
						item.setName(itemName);
						itemNameLists.add(item);
					}
				}
				edcDataItemInfoField.getListTableManager().setInput(itemNameLists);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    	return;
		}
	}

	private void eqpTypeAddAdapter(Object object) {
		add((ListRowEditorTableManager)eqpTypeFormField.getListTableManager(), true);
	}

	private void eqpTypeAddFromAdapter(Object object) {
		try {
			ADURefList adURefList = (ADURefList) configNameFormField.getListTableManager().getSelectedObject();
			if (adURefList == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
			}
			//获取上次勾选内容
			String key = eqpTypeFormField.getListTableManager().getInput().stream().map(x -> ((ADURefList)x).getKey()).collect(Collectors.joining(";"));
			
			ADTable adTable = getADManger().getADTable(Env.getOrgRrn(), RefTableField.TABLENAME_USER_REFLIST);
			ListTableManager manager = new ListTableManager(adTable, true);
			SearchMultiDialog queryDialog = new SearchMultiDialog(manager, null, " referenceName = 'EquipmentType'", key, "key");
			queryDialog.setADManager(getADManger());
			if (queryDialog.open() == IDialogConstants.OK_ID) {
				eqpTypeFormField.getListTableManager().setInput(queryDialog.getSelectionList());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    	return;
		}
	}

	private void eqpTypeDeleteAdapter(Object object) {
		delete((ListRowEditorTableManager)eqpTypeFormField.getListTableManager());
	}

	private void stepAddAdapter(Object object) {
		add((ListRowEditorTableManager)stepNameFormField.getListTableManager(), true);
	}

	private void stepAddFromAdapter(Object object) {
		try {
			ADURefList adURefList = (ADURefList) configNameFormField.getListTableManager().getSelectedObject();
			if (adURefList == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
			}
			//获取上次勾选内容
			String key = stepNameFormField.getListTableManager().getInput().stream().map(x -> ((Step)x).getName()).collect(Collectors.joining(";"));
			
			ADTable adTable = getADManger().getADTable(Env.getOrgRrn(), "PRDActiveStep");
			ListTableManager manager = new ListTableManager(adTable, true);
			SearchMultiDialog queryDialog = new SearchMultiDialog(manager, null, null, key, "name");
			queryDialog.setADManager(getADManger());
			if (queryDialog.open() == IDialogConstants.OK_ID) {
				stepNameFormField.getListTableManager().setInput(queryDialog.getSelectionList());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	    	return;
		}
	}

	private void stepDeleteAdapter(Object object) {
		delete((ListRowEditorTableManager)stepNameFormField.getListTableManager());
	}

	private void dataItemAddAdapter(Object object) {
		add((ListRowEditorTableManager)edcDataItemInfoField.getListTableManager(), true);
	}

	private void dataItemDeleteAdapter(Object object) {
		delete((ListRowEditorTableManager)edcDataItemInfoField.getListTableManager());
	}

	public void add(ListRowEditorTableManager tableManager, boolean ischeck) {
		try {
			if (ischeck) {
				ADURefList adURefList = (ADURefList) configNameFormField.getListTableManager().getSelectedObject();
				if (adURefList == null) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
	                return;
				}
			}
			ADBase base = (ADBase)Class.forName(tableManager.getADTable().getModelClass()).getDeclaredConstructor().newInstance();
			base.setOrgRrn(Env.getOrgRrn());
			if (base != null) {
				List<Object> list = (List<Object>)tableManager.getInput();
				List<ADBase> adBases = Lists.newArrayList();
				if (list != null) {
					for (Object obj : list) {
						adBases.add((ADBase)obj);
					}
				}
				adBases.add(base);
				tableManager.addEditorObjects(base);
				tableManager.setInput(adBases);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	public void delete(ListRowEditorTableManager tableManager) {
		List<ADBase> list = (List<ADBase>)tableManager.getInput();
		List<Object> os = tableManager.getCheckedObject();
		List<ADBase> adBases = Lists.newArrayList();
		for (Object obj : list) {
			adBases.add((ADBase)obj);
		}
		for (Object o : os) {
			ADBase pe = (ADBase)o;
			adBases.remove(pe);	
			tableManager.removeEditorObjects(o);
		}
		tableManager.getCheckedObject().clear();
		tableManager.setInput(adBases);
	}
}