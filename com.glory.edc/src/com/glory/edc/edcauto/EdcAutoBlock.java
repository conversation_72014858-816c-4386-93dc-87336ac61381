package com.glory.edc.edcauto;

import org.apache.log4j.Logger;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.DetailsPart;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.exception.ExceptionBundle;


public class EdcAutoBlock extends EntityBlock{
	private static final Logger logger = Logger.getLogger(EdcAutoBlock.class);
	EdcAutoProperties page;

	public EdcAutoBlock(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class<?> klass = Class.forName(table.getModelClass());
			page = new EdcAutoProperties();
			page.setTable(table);
			page.setMasterParent(this);
			detailsPart.registerPage(klass, page);
		} catch (Exception e){
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((EdcAutoProperties)page).setFocus();
	}
	
	// 刷新前先把page的值改掉
	protected void createToolItemRefresh(ToolBar tBar) {
	      this.itemRefresh = new ToolItem(tBar, 8);
	      this.itemRefresh.setText(Message.getString(ExceptionBundle.bundle.CommonRefresh()));
	      this.itemRefresh.setImage(SWTResourceCache.getImage("refresh"));
	      this.itemRefresh.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					page.connectorField.setValue("");
				}
			});
	   }
}
