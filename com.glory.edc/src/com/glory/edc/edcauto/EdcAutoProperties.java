package com.glory.edc.edcauto;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.StackLayout;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.edc.client.EDCManager;
import com.glory.edc.model.auto.EdcAutoConfig;
import com.glory.edc.model.auto.EdcAutoConfigParser;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.core.exception.ExceptionBundle;


public class EdcAutoProperties extends EntityProperties {

	public CheckBoxFixEditorTableManager manager;
	public static String TABLE_NAME_EDCPARSER = "EdcAutoConfigParser";
	public static String TABLE_NAME_EDCCONFIG = "EdcAutoConfig";
	public static String TABLE_NAME_EDCCONFIG_REMODE = "EdcAutoConfigRemote";
	public static String TABLE_NAME_EDCCONFIG_FTP = "EdcAutoConfigUpload";
	public static String TABLE_NAME_EDCCONFIG_FILE = "EdcAutoConfigFile";
	public static String TABLE_NAME_EDCCONFIG_SOCKET = "EdcAutoConfigSocket";
	
	protected ToolItem itemImport;
	public Button add, remove;
	protected EdcAutoParserDialog eid;
	
	protected Composite connectorComp;
	
	protected IField connectorField, runModeField, runIntervalField;
	private StackLayout stackLayout;
	
	EntityForm remoteDirForm, uploadForm, fileForm, socketForm;
	
	
	
	public EdcAutoProperties() {
		super();
	}

	protected void createSectionContent(Composite client) {
		try {
			ADManager adManager = getADManger();
			final FormToolkit toolkit = form.getToolkit();
			mmng = form.getMessageManager();
			createBasicSection(client);
			createConnectorIdTypeField(this.getDetailForms().get(0));

			connectorComp = toolkit.createComposite(client, SWT.NONE);
			stackLayout = new StackLayout();
			connectorComp.setLayout(stackLayout);
			GridData connectorGrid = new GridData(GridData.FILL_BOTH);
			connectorComp.setLayoutData(connectorGrid);

			ADTable remoteAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_EDCCONFIG_REMODE);
			final Composite remoteDirComp = new Composite(connectorComp, SWT.NONE);// 远程共享文件夹的界面
			remoteDirComp.setLayout(new GridLayout());
			remoteDirComp.setLayoutData(new GridData(GridData.FILL_BOTH));

			remoteDirForm = new EntityForm(remoteDirComp, SWT.BORDER, new EdcAutoConfig(), remoteAdTable, mmng);
			GridData remoteFormGridData = new GridData(GridData.FILL_BOTH);
			remoteFormGridData.heightHint = 120;
			remoteDirForm.setLayoutData(remoteFormGridData);

			ADTable uploadAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_EDCCONFIG_FTP);
			final Composite uploadComp = new Composite(connectorComp, SWT.NONE);// FTP上传的界面
			uploadComp.setLayout(new GridLayout());
			uploadComp.setLayoutData(new GridData(GridData.FILL_BOTH));

			uploadForm = new EntityForm(uploadComp, SWT.BORDER, new EdcAutoConfig(), uploadAdTable, mmng);
			GridData uploadFormGridData = new GridData(GridData.FILL_BOTH);
			uploadFormGridData.heightHint = 300;
			uploadForm.setLayoutData(uploadFormGridData);

			ADTable fileAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_EDCCONFIG_FILE);
			final Composite fileComp = new Composite(connectorComp, SWT.NONE);// file的界面
			fileComp.setLayout(new GridLayout());
			fileComp.setLayoutData(new GridData(GridData.FILL_BOTH));

			fileForm = new EntityForm(fileComp, SWT.BORDER, new EdcAutoConfig(), fileAdTable, mmng);
			GridData fileFormGridData = new GridData(GridData.FILL_BOTH);
			fileFormGridData.heightHint = 300;
			fileForm.setLayoutData(fileFormGridData);

			ADTable socketAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_EDCCONFIG_SOCKET);
			final Composite socketComp = new Composite(connectorComp, SWT.NONE);// socket的界面
			socketComp.setLayout(new GridLayout());
			socketComp.setLayoutData(new GridData(GridData.FILL_BOTH));

			socketForm = new EntityForm(socketComp, SWT.BORDER, new EdcAutoConfig(), socketAdTable, mmng);
			GridData socketFormGridData = new GridData(GridData.FILL_BOTH);
			socketFormGridData.heightHint = 300;
			socketForm.setLayoutData(socketFormGridData);

			connectorField.addValueChangeListener(new IValueChangeListener() {

				@Override
				public void valueChanged(Object source, Object newValue) {

					String str = (String) newValue;
					if (str == null) {
						str = "";
					}
					switch (str) {
					case "RemoteFileConnector":
						stackLayout.topControl = remoteDirComp;
						connectorComp.layout();
						PropertyUtil.copyProperties(remoteDirForm.getObject(), getAdObject(),
								remoteDirForm.getCopyProperties());
						remoteDirForm.setObject(remoteDirForm.getObject());
						remoteDirForm.loadFromObject();
						break;
					case "FileUpload":
						stackLayout.topControl = uploadComp;
						connectorComp.layout();
						PropertyUtil.copyProperties(uploadForm.getObject(), getAdObject(),
								uploadForm.getCopyProperties());
						uploadForm.setObject(uploadForm.getObject());
						uploadForm.loadFromObject();
						break;

					case "ExcelFileConnector":
					case "TxtFileConnector":
					case "CsvFileConnector":
						stackLayout.topControl = fileComp;
						connectorComp.layout();
						PropertyUtil.copyProperties(fileForm.getObject(), getAdObject(), fileForm.getCopyProperties());
						fileForm.setObject(fileForm.getObject());
						fileForm.loadFromObject();
						break;

					case "SocketConnector":
						stackLayout.topControl = socketComp;
						connectorComp.layout();
						PropertyUtil.copyProperties(socketForm.getObject(), getAdObject(),
								socketForm.getCopyProperties());
						socketForm.setObject(socketForm.getObject());
						socketForm.loadFromObject();
						break;

					default:

						stackLayout.topControl = null;
						connectorComp.layout(false);

					}

				}

			});

			runModeField.addValueChangeListener(new IValueChangeListener() {

				@Override
				public void valueChanged(Object source, Object newValue) {
					if (newValue == null || "".equals(((String) newValue).trim()))
						return;

					String str = (String) newValue;
					switch (str) {
					case "M":
						runIntervalField.setValue("");
						((TextField) runIntervalField).setText("");
						runIntervalField.setEnabled(false);
						break;
					case "A":
						runIntervalField.setEnabled(true);
						break;

					default:

					}
				}

			});

			Section section = toolkit.createSection(client, Section.TITLE_BAR | Section.EXPANDED);
			section.setText("文件解析规则信息");
			GridData gd = new GridData(GridData.FILL_BOTH);
			section.setLayoutData(gd);
			section.setLayout(new GridLayout(1, true));

			Composite parameterComp = toolkit.createComposite(section, SWT.BORDER);
			GridLayout layout = new GridLayout(1, true);
			parameterComp.setLayout(layout);
			gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 280;
			parameterComp.setLayoutData(gd);
			final ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_EDCPARSER);
			manager = new CheckBoxFixEditorTableManager(adTable);
			manager.setIndexFlag(true);
			manager.newViewer(parameterComp);

			section.setClient(parameterComp);

			Composite btnComposite = toolkit.createComposite(client, SWT.NONE);
			GridLayout layoutBtn = new GridLayout(5, false);
			btnComposite.setLayout(layoutBtn);
			GridData gd1 = new GridData(GridData.FILL_BOTH);
			gd1.horizontalAlignment = SWT.RIGHT;
			btnComposite.setLayoutData(gd1);

			add = toolkit.createButton(btnComposite, "  " + Message.getString(ExceptionBundle.bundle.CommonAdd()) + "  ", SWT.NONE);
			add.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						addAdapter(event);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});

			remove = toolkit.createButton(btnComposite, "  " + Message.getString(ExceptionBundle.bundle.CommonDelete()) + "  ", SWT.NONE);
			remove.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						removeAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void refresh() {
		super.refresh();
		if (getAdObject() != null) {
			EdcAutoConfig edcConfig = (EdcAutoConfig) getAdObject();
			if (edcConfig != null && edcConfig.getParsers() == null) {
				edcConfig.setParsers(new ArrayList<EdcAutoConfigParser>());
				
			}
			manager.setInput(edcConfig.getParsers());
			if (edcConfig != null) {
				if (StringUtil.isEmpty(edcConfig.getConnectorId())) {
					return;
				}
				switch (edcConfig.getConnectorId()) {
				case "RemoteFileConnector":
					PropertyUtil.copyProperties(remoteDirForm.getObject(), getAdObject(),
							remoteDirForm.getCopyProperties());
					remoteDirForm.loadFromObject();
					break;
				case "FileUpload":
					PropertyUtil.copyProperties(uploadForm.getObject(), getAdObject(), uploadForm.getCopyProperties());
					uploadForm.loadFromObject();
					break;

				case "ExcelFileConnector":
				case "TxtFileConnector":
				case "CsvFileConnector":
					PropertyUtil.copyProperties(fileForm.getObject(), getAdObject(), fileForm.getCopyProperties());
					fileForm.loadFromObject();
					break;

				case "SocketConnector":
					PropertyUtil.copyProperties(socketForm.getObject(), getAdObject(), socketForm.getCopyProperties());
					socketForm.loadFromObject();
					break;

				default:

				}

			}

		} 
		for (IForm detailForm : getDetailForms()) {
			detailForm.setObject(getAdObject());
			detailForm.loadFromObject();
		}
		form.getMessageManager().removeAllMessages();
	}
	
	@SuppressWarnings("unchecked")
	protected void addAdapter(SelectionEvent event) {
		ADManager adManager = getADManger();
		EdcAutoConfigParser edcParser = new EdcAutoConfigParser();
		edcParser.setOrgRrn(Env.getOrgRrn());
		edcParser.setIsActive(true);
		final ADTable adTable = adManager.getADTableDeep(Env.getOrgRrn(), TABLE_NAME_EDCPARSER);
		EdcAutoParserDialog dialog = new EdcAutoParserDialog(adTable, edcParser);
		if (dialog.open() == Dialog.OK) {

		}
		EdcAutoConfig edcConfig = (EdcAutoConfig)getAdObject();
		if (edcConfig.getParsers() == null) {
			edcConfig.setParsers(new ArrayList<EdcAutoConfigParser>());
		}
		edcConfig.getParsers().add(edcParser);
		manager.setInput(edcConfig.getParsers());

	}
	
	
	@SuppressWarnings("unchecked")
	@Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();

				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
								detailForm.getCopyProperties());
					}

					EdcAutoConfig tempconfig = (EdcAutoConfig) getAdObject();

					switch (tempconfig.getConnectorId()) {
					case "RemoteFileConnector":
						remoteDirForm.saveToObject();
						PropertyUtil.copyProperties(getAdObject(), remoteDirForm.getObject(),
								remoteDirForm.getCopyProperties());
						break;
					case "FileUpload":
						uploadForm.saveToObject();
						PropertyUtil.copyProperties(getAdObject(), uploadForm.getObject(),
								uploadForm.getCopyProperties());
						break;

					case "ExcelFileConnector":
					case "TxtFileConnector":
					case "CsvFileConnector":
						fileForm.saveToObject();
						PropertyUtil.copyProperties(getAdObject(), fileForm.getObject(), fileForm.getCopyProperties());
						break;

					case "SocketConnector":
						socketForm.saveToObject();
						PropertyUtil.copyProperties(getAdObject(), socketForm.getObject(),
								socketForm.getCopyProperties());
						break;

					default:

					}

					List<EdcAutoConfigParser> parsers = (List<EdcAutoConfigParser>) (List<? extends Object>) manager
							.getInput();
					List<EdcAutoConfigParser> tempParsers = new ArrayList<EdcAutoConfigParser>();
					tempParsers.addAll(parsers);

					PropertyUtil.setProperty(getAdObject(), "parsers", tempParsers);

					EDCManager edcManager = Framework.getService(EDCManager.class);
					tempconfig = edcManager.saveEdcAutoConfig(tempconfig, Env.getSessionContext());

					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
					setAdObject(tempconfig);
					refresh();
					ADBase newBase = getAdObject();
					if (oldBase.getObjectRrn() == null) {
						getMasterParent().refreshAdd(newBase);
					} else {
						getMasterParent().refreshUpdate(newBase);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@SuppressWarnings("unchecked")
	protected void removeAdapter() {
		try {
			List<EdcAutoConfigParser> parsers = (List<EdcAutoConfigParser>)(List<? extends Object>)manager.getInput();
			List<EdcAutoConfigParser> tempParsers = new ArrayList<EdcAutoConfigParser>();
			for (EdcAutoConfigParser parser : parsers) {
				tempParsers.add(parser);
			}				
			List<Object> os = manager.getCheckedObject();
			if (os.size() != 0) {
				for (Object o : os) {
					EdcAutoConfigParser line = (EdcAutoConfigParser) o;
					tempParsers.remove(line);
				}
			}
			manager.setInput(tempParsers);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	private void createConnectorIdTypeField(IForm iForm){
		connectorField = this.getField("connectorId");
		
		runModeField = this.getField("runMode");
		
		runIntervalField = this.getField("runInterval");
		
	}
	
	protected void createToolItemNew(ToolBar tBar) {
	      this.itemNew = new ToolItem(tBar, 8);
	      this.itemNew.setText(Message.getString(ExceptionBundle.bundle.CommonNew()));
	      this.itemNew.setImage(SWTResourceCache.getImage("new"));
	      this.itemNew.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					form.getMessageManager().removeAllMessages();
					setAdObject(null);
					refreshAdapter();
					connectorField.setValue("");
					masterParent.refresh();
				}
			});
	   }
}
