package com.glory.edc.itemset.plan;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSubgroupPlan;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;

public class AttributePlanDialog extends BaseTitleDialog{
	protected FormToolkit toolkit;
	protected Button btnSampleNumber,btnSamplePercent,btnNumber,btnPercent;
	protected Text textSampleNumber,textSamplePercent,textQualified,
		textDefectLineCode,textSampleSize,textSubgroupSize,textNumber,textPercent;
	protected GridLayout layout;
	protected EdcItemSetLine line;
	protected String param [];
	protected Boolean isEdit = true;
	
	public AttributePlanDialog(Shell parentShell,EdcItemSetLine line,String []param) {
		super(parentShell);
		this.line = line;
		this.param = param;
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("operation-dialog"));
		setTitle(Message.getString("edc.sample_plan_attribute"));
		toolkit = new FormToolkit(parent.getDisplay());
		// 设置统一风格
		Composite composite = toolkit.createComposite(parent);
		layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		composite.setLayoutData(gd);
		
		createEdcPlanDefArea(composite);
		createEdcPlanArea(composite);
		createEdcPlanRuleArea(composite);
		//保存之后的查看
		if(line.getIsSubgroupPlan()){
			if(line.getSubgroupPlans()!=null&&line.getSubgroupPlans().size()>0){
				isEdit = false;
				setEditData(line.getSubgroupPlans(),isEdit);
			}
		}
		//未保存且添加了临时数据时，允许其进行修改
		if((!line.getIsSubgroupPlan()||line.getIsSubgroupPlan()==null)
				&&line.getSubgroupPlans()!=null&&line.getSubgroupPlans().size()>0){
			setEditData(line.getSubgroupPlans(),isEdit);
		}
		return parent;
	}
	
	protected void createEdcPlanDefArea(Composite composite){
	    Composite form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(4, false);
		form.setLayout(flayout);
		GridData gdForm = new GridData(GridData.FILL_BOTH);
		gdForm.verticalSpan = 3;
		form.setLayoutData(gdForm);
		
		Section section = toolkit.createSection(form, Section.TITLE_BAR | Section.DESCRIPTION);
		section.setText(Message.getString("edc.sample_plan_default"));
		section.setLayout(layout);
		GridData s = new GridData(GridData.FILL_HORIZONTAL);
		s.horizontalSpan = 6;
		section.setLayoutData(s);

		btnNumber = toolkit.createButton(form, Message.getString("edc.edcset_attribute_number"), SWT.RADIO);   
		textNumber = toolkit.createText(form, "", SWT.BORDER);	    
		GridData gdFix = new GridData(GridData.FILL_HORIZONTAL);
		gdFix.horizontalSpan = 3;
		textNumber.setLayoutData(gdFix);
		
		
		btnPercent = toolkit.createButton(form, Message.getString("edc.edcset_attribute_percent"), SWT.RADIO);     
		textPercent = toolkit.createText(form, "", SWT.BORDER);   
		GridData gdPercent = new GridData(GridData.FILL_HORIZONTAL);
		gdPercent.horizontalSpan = 3;
		textPercent.setLayoutData(gdPercent);
		
		if(param[1].equals(EdcSubgroupPlan.RULETYPE_NUMBER)){
			btnNumber.setSelection(true);
			textNumber.setText(param[0]);
		}else{
			btnPercent.setSelection(true);
			textPercent.setText(param[0]);
		}
		btnNumber.setEnabled(false);
		textNumber.setEnabled(false);
		btnPercent.setEnabled(false);
		textPercent.setEnabled(false);
	}
	
	protected void createEdcPlanArea(Composite composite){
	    Composite form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(4, false);
		form.setLayout(flayout);
		GridData gdForm = new GridData(GridData.FILL_BOTH);
		gdForm.verticalSpan = 3;
		form.setLayoutData(gdForm);
		
		Section section = toolkit.createSection(form, Section.TITLE_BAR | Section.DESCRIPTION);
		section.setText(Message.getString("edc.sample_plan_normal"));
		section.setLayout(layout);
		GridData s = new GridData(GridData.FILL_HORIZONTAL);
		s.horizontalSpan = 6;
		section.setLayoutData(s);
			
		
		btnSampleNumber = toolkit.createButton(form, Message.getString("edc.edcset_attribute_number"), SWT.RADIO);
		btnSampleNumber.setSelection(true);
		textSampleNumber = toolkit.createText(form, "", SWT.BORDER);	
		GridData gdFix = new GridData(GridData.FILL_HORIZONTAL);
		gdFix.horizontalSpan = 3;
		textSampleNumber.setLayoutData(gdFix);
		
		btnSamplePercent = toolkit.createButton(form, Message.getString("edc.edcset_attribute_percent"), SWT.RADIO);     
		textSamplePercent = toolkit.createText(form, "", SWT.BORDER);   
		GridData gdPercent = new GridData(GridData.FILL_HORIZONTAL);
		gdPercent.horizontalSpan = 3;
		textSamplePercent.setLayoutData(gdPercent);
		textSamplePercent.setEnabled(false);
		
		//把没选中的单选框对应的Text设为不可用
		btnSampleNumber.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				textSampleNumber.setEnabled(true);
				textSampleNumber.setFocus();
				textSamplePercent.setText("");		
				textSamplePercent.setEnabled(false);
			}
		});		
		btnSamplePercent.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				textSamplePercent.setEnabled(true);
				textSamplePercent.setFocus();
				textSampleNumber.setText("");
				textSampleNumber.setEnabled(false);
			}
		});
		
		
		//验证输入的是否是数字
		textSampleNumber.addFocusListener(new FocusListener() {	
			@Override
			public void focusGained(FocusEvent e) {

			}
			@Override
			public void focusLost(FocusEvent e) {
				try {
					if(!"".equals(textSampleNumber.getText())){
						Integer.parseInt(textSampleNumber.getText().trim());
						textSubgroupSize.setText(textSampleNumber.getText());
				    }
				} catch (NumberFormatException ex) {
					UI.showError(Message.getString("edc.edcset_attribute_format")); //你输入的数据格式不对！
					return;
				}
			}
		});
		textSamplePercent.addFocusListener(new FocusListener() {	
			@Override
			public void focusGained(FocusEvent e) {	

			}
			@Override
			public void focusLost(FocusEvent e) {
				try {
					if(!"".equals(textSamplePercent.getText())){
						Float.parseFloat(textSamplePercent.getText().trim());
						textSubgroupSize.setText(textSamplePercent.getText());
					}
				} catch (NumberFormatException ex) {
					UI.showError(Message.getString("edc.edcset_attribute_format"));
					return;
				}
			}
		});	
	}
	
	protected void createEdcPlanRuleArea(Composite composite){
		Composite form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(4, false);
		form.setLayout(flayout);
		GridData gdForm = new GridData(GridData.FILL_BOTH);
		gdForm.verticalSpan = 3;
		form.setLayoutData(gdForm);
		Label sLabel1 = new Label(form,SWT.SEPARATOR|SWT.HORIZONTAL|SWT.FULL_SELECTION);
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalSpan = 4;
		sLabel1.setLayoutData(gd);
		
		toolkit.createLabel(form, Message.getString("edc.sample_plan_exception"));
		textDefectLineCode = toolkit.createText(form, "", SWT.BORDER);   
		GridData gdDefectLine = new GridData(GridData.FILL_HORIZONTAL);
		gdDefectLine.horizontalSpan = 3;
		textDefectLineCode.setLayoutData(gdDefectLine);
		
		toolkit.createLabel(form, Message.getString("edc.sample_plan_qualified"));
		textQualified = toolkit.createText(form, "", SWT.BORDER);   
		GridData gdQualified = new GridData(GridData.FILL_HORIZONTAL);
		gdQualified.horizontalSpan = 3;
		textQualified.setLayoutData(gdQualified);
		
		Group group = new Group(composite, SWT.NULL);
		group.setLayout(new GridLayout(6, false));
		group.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		Label labSampleSize=toolkit.createLabel(group, "SampleSize");
		labSampleSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
		textSampleSize = toolkit.createText(group, "1", SWT.READ_ONLY);
		GridData gdSampleSize = new GridData(GridData.FILL_HORIZONTAL);
		gdSampleSize.horizontalSpan = 2;
		textSampleSize.setLayoutData(gdSampleSize);
		textSampleSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
		Label labSubgroupSize=toolkit.createLabel(group, "SubgroupSize");	
		labSubgroupSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
		textSubgroupSize = toolkit.createText(group, "", SWT.READ_ONLY);
		textSubgroupSize.setLayoutData(gdSampleSize); 
		textSubgroupSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
	}
	

	@Override
	public void okPressed() {
		if(isEdit){
			if("".equals(textSampleNumber.getText().trim())&&
					"".equals(textSamplePercent.getText().trim())){
				UI.showInfo(Message.getString("edc.sample_plan_chose"));
				return;
			}
			
			if("".equals(textQualified.getText().trim())||
					"".equals(textDefectLineCode.getText().trim())){
				UI.showInfo(Message.getString("edc.sample_plan_complete"));
				return;
			}
			
			String regex1 = "^\\d+(\\.\\d+)?$";
			String regex2 = "^[0-9]*$";
			Pattern pattern1 = Pattern.compile(regex1);    
			Pattern pattern2 = Pattern.compile(regex2);  
			Matcher qualified = pattern2.matcher(textQualified.getText().trim());  
			Matcher code = pattern1.matcher(textDefectLineCode.getText().trim());
			if(!qualified.matches()||!code.matches()){
				UI.showError(Message.getString("edc.edcset_attribute_format"));
				return;
			}
			
			if(textSamplePercent.getText().trim().length()>0){
				if(Float.parseFloat(textSamplePercent.getText().trim()) > 100){
					UI.showError(Message.getString("edc.edcset_attribute_pecent"));  //百分比不能大于100！
					return;
				}
			}
			line.setSubgroupPlans(createList());
			line.setSubgroupQualifyRate(Long.parseLong(textQualified.getText()));
			line.setSubgroupQualifyCount(0l);//初始化count
		}
		super.okPressed();
	}

	protected List<EdcSubgroupPlan> createList(){
		List<EdcSubgroupPlan> plans = new ArrayList<EdcSubgroupPlan>();
		
		EdcSubgroupPlan defaultPlan  = new EdcSubgroupPlan();
		defaultPlan.setOrgRrn(Env.getSessionContext().getOrgRrn());
		int i = 1;
		defaultPlan.setSubgroupSize(new BigDecimal(param[0]));
		defaultPlan.setRuleType(param[i++]);
		defaultPlan.setSampleSize(Long.parseLong(param[i++]));
		defaultPlan.setRuleLevel(EdcSubgroupPlan.RULELEVEL_DEFALUT);
		plans.add(defaultPlan);
		
		EdcSubgroupPlan normal = new EdcSubgroupPlan();
		normal.setOrgRrn(Env.getSessionContext().getOrgRrn());
		normal.setRuleType(btnSampleNumber.getSelection()?
				EdcSubgroupPlan.RULETYPE_NUMBER:EdcSubgroupPlan.RULETYPE_PERCENT);
		normal.setRuleLevel(EdcSubgroupPlan.RULELEVEL_ATTRIBUTE);
		normal.setSampleSize(Long.parseLong(textSampleSize.getText()));
		normal.setSubgroupSize(new BigDecimal(textSubgroupSize.getText()));
		normal.setCount(new BigDecimal(textDefectLineCode.getText()));
		plans.add(normal);
		return plans;
	}
	
	protected void setEditData(List<EdcSubgroupPlan> plans, Boolean isEdit){
		if(plans.size()>1){
			for(EdcSubgroupPlan plan : plans){
				if(plan.getRuleLevel().equals(EdcSubgroupPlan.RULELEVEL_DEFALUT)){
					if(plan.getRuleType().equals(EdcSubgroupPlan.RULETYPE_NUMBER)){
						btnNumber.setSelection(true);
						btnPercent.setSelection(false);
						textPercent.setText("");
						textNumber.setText(
								plan.getSubgroupSize()==null?"":plan.getSubgroupSize().toString());
					}else{
						btnPercent.setSelection(true);
						btnNumber.setSelection(false);
						textNumber.setText("");
						textPercent.setText(
								plan.getSubgroupSize()==null?"":plan.getSubgroupSize().toString());
					}
				}else{
					if(plan.getRuleType().equals(EdcSubgroupPlan.RULETYPE_NUMBER)){
						btnSampleNumber.setSelection(true);
						btnSamplePercent.setSelection(false);
						textSampleNumber.setText(
								plan.getSubgroupSize()==null?"":plan.getSubgroupSize().toString());
					}else{
						btnSamplePercent.setSelection(true);
						btnSampleNumber.setSelection(false);
						textSamplePercent.setText(
								plan.getSubgroupSize()==null?"":plan.getSubgroupSize().toString());
					}
					textSampleSize.setText(
							plan.getSampleSize()==null?"":plan.getSampleSize().toString());
					textSubgroupSize.setText(
							plan.getSubgroupSize()==null?"":plan.getSubgroupSize().toString());
					textDefectLineCode.setText(
							plan.getCount()==null?"":plan.getCount().toString());
					textQualified.setText(
							line.getSubgroupQualifyRate()==null?"":line.getSubgroupQualifyRate().toString());
					
				}
			}
			if(!isEdit){
				btnSampleNumber.setEnabled(false);
				btnSamplePercent.setEnabled(false);
				textSamplePercent.setEnabled(false);
				textSampleNumber.setEnabled(false);
				textDefectLineCode.setEnabled(false);
				textQualified.setEnabled(false);
			}
		}
	}
}
