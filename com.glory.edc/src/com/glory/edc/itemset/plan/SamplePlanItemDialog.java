package com.glory.edc.itemset.plan;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSubgroupPlan;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;

public class SamplePlanItemDialog  extends BaseTitleDialog{
	protected FormToolkit toolkit;
	protected Text textDefItem,textDefItemDesc;
	protected Text textSampleItem,textSampleItemDesc;
	protected Text textQualified,textPointfied;
	protected Text textSampleSize,textSubgroupSize;
	protected GridLayout layout;
	protected XCombo comboSampleDefectCode;
	protected String [] param;
	protected EdcItemSetLine line;
	protected Boolean isEdit = true; 
	
	public SamplePlanItemDialog(Shell parentShell,String param[],EdcItemSetLine line) {
		super(parentShell);
		this.param = param;
		this.line = line;
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("operation-dialog"));
		setTitle(Message.getString("edc.sample_plan_variable"));
		toolkit = new FormToolkit(parent.getDisplay());
		layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		createSpecArea(parent);
		createSampleSpecArea(parent);
		createEdcPlanRuleArea(parent);
		//保存之后的查看
		if(line.getIsSubgroupPlan()){
			if(line.getSubgroupPlans()!=null&&line.getSubgroupPlans().size()>0){
				isEdit = false;
				setEditData(line.getSubgroupPlans(),isEdit);
			}
		}
		//未保存且添加了临时数据时，允许其进行修改
		if((!line.getIsSubgroupPlan()||line.getIsSubgroupPlan()==null)
				&&line.getSubgroupPlans()!=null&&line.getSubgroupPlans().size()>0){
			setEditData(line.getSubgroupPlans(),isEdit);
		}
		
		return parent;
	}

	protected void createSpecArea(Composite composite){
		Composite form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(2,false);
		form.setLayout(flayout);
		GridData gdForm =new GridData(GridData.FILL_BOTH);
		form.setLayoutData(gdForm);
		
		Section section = toolkit.createSection(form, Section.TITLE_BAR | Section.DESCRIPTION);
		section.setText(Message.getString("edc.sample_plan_default"));
		section.setLayout(layout);
		GridData s = new GridData(GridData.FILL_HORIZONTAL);
		s.horizontalSpan = 2;
		section.setLayoutData(s);
		
		int i = 1;
		toolkit.createLabel(form, "Item*");
		textDefItem = toolkit.createText(form, "", SWT.BORDER);
		GridData gdTtem = new GridData(GridData.FILL_HORIZONTAL);
		textDefItem.setLayoutData(gdTtem);
		textDefItem.setText(param[0]);
		textDefItem.setEnabled(false);
		
		toolkit.createLabel(form, "ItemDesc");
		textDefItemDesc = toolkit.createText(form, "", SWT.BORDER);
		GridData TtemDesc = new GridData(GridData.FILL_HORIZONTAL);
		textDefItemDesc.setLayoutData(TtemDesc);
		textDefItemDesc.setText(param[i++]);
		textDefItemDesc.setEnabled(false);

	}
	
	protected void createSampleSpecArea(Composite composite){
		Composite form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(2,false);
		form.setLayout(flayout);
		GridData gdForm =new GridData(GridData.FILL_BOTH);
		form.setLayoutData(gdForm);
		
		Section section = toolkit.createSection(form, Section.TITLE_BAR | Section.DESCRIPTION);
		section.setText(Message.getString("edc.sample_plan_normal"));
		section.setLayout(layout);
		GridData s = new GridData(GridData.FILL_HORIZONTAL);
		s.horizontalSpan = 2;
		section.setLayoutData(s);

		toolkit.createLabel(form, "Item*");
		textSampleItem = toolkit.createText(form, "", SWT.BORDER);
		GridData gdTtem = new GridData(GridData.FILL_HORIZONTAL);
		textSampleItem.setLayoutData(gdTtem);
		textSampleItem.setText("1");
		
		toolkit.createLabel(form, "ItemDesc");
		textSampleItemDesc = toolkit.createText(form, "", SWT.BORDER);
		GridData TtemDesc = new GridData(GridData.FILL_HORIZONTAL);
		textSampleItemDesc.setLayoutData(TtemDesc);
	
		textSampleItem.addFocusListener(new FocusListener() {	
			@Override
			public void focusGained(FocusEvent e) {
			}
			@Override
			public void focusLost(FocusEvent e) {
				try {
					if(!"".equals(textSampleItem.getText())){
						Integer.parseInt(textSampleItem.getText().trim());
						textSubgroupSize.setText(textSampleItem.getText());
					}
				} catch (NumberFormatException ex) {
					textSampleItem.setText("");
					textSubgroupSize.setText("");
					UI.showError(Message.getString("edc.edcset_attribute_format"));
				}
			}
		});
	}
	
	protected void createEdcPlanRuleArea(Composite composite){
		Composite form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(4,false);
		form.setLayout(flayout);
		GridData gdForm =new GridData(GridData.FILL_BOTH);
		form.setLayoutData(gdForm);
		
		Label sLabel1 = new Label(form,SWT.SEPARATOR|SWT.HORIZONTAL|SWT.FULL_SELECTION);
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalSpan = 4;
		sLabel1.setLayoutData(gd);
		
		Composite form1 = toolkit.createComposite(composite);
		GridLayout flayout1 = new GridLayout(3,false);
		form1.setLayout(flayout1);
		GridData gdForm1 =new GridData(GridData.FILL_BOTH);
		form1.setLayoutData(gdForm1);
		
		toolkit.createLabel(form1, Message.getString("edc.sample_plan_exception_variable"));
		comboSampleDefectCode = RCPUtil.getSysRefListCombo(form1, "SamplePlanCode", Env.getOrgRrn());
		comboSampleDefectCode.setEditable(false);
		GridData gdDefectCode = new GridData(GridData.FILL_HORIZONTAL);
		gdDefectCode.horizontalSpan = 2;
		comboSampleDefectCode.setLayoutData(gdDefectCode);
		
		comboSampleDefectCode.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				widgetDefaultSelected(e);
			}
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				if(comboSampleDefectCode.getText().equals(EdcSubgroupPlan.RULELEVEL_OOS_COUNT)){
					textPointfied.setEnabled(true);
				}else{
					textPointfied.setEnabled(false);
				}
			}
		});
		
		toolkit.createLabel(form1, Message.getString("edc.sample_plan_qualified_count"));
		textPointfied = toolkit.createText(form1, "", SWT.BORDER);   
		textPointfied.setLayoutData(gdDefectCode);
		textPointfied.setEnabled(false);
		
		toolkit.createLabel(form1, Message.getString("edc.sample_plan_qualified"));
		textQualified = toolkit.createText(form1, "", SWT.BORDER);   
		textQualified.setLayoutData(gdDefectCode);
		
		Group group = new Group(composite, SWT.NULL);
		group.setLayout(new GridLayout(4, false));
		group.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		Label labSampleSize=toolkit.createLabel(group, "SampleSize");
		labSampleSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
		textSampleSize = toolkit.createText(group, "1", SWT.READ_ONLY);
		textSampleSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
		textSampleSize.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		Label labSubgroupSize=toolkit.createLabel(group, "SubgroupSize");	
		labSubgroupSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
		textSubgroupSize = toolkit.createText(group, "", SWT.READ_ONLY);
		textSubgroupSize.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		textSubgroupSize.setBackground(new Color(Display.getCurrent(), 236,233,216));
	}
	
	@Override
	public void okPressed() {
		if(isEdit){
			if("".equals(textSampleItem.getText().trim())||
						"".equals(comboSampleDefectCode.getText().trim())||
							"".equals(textQualified.getText().trim())){
				UI.showInfo(Message.getString("edc.sample_plan_complete"));
				return;
			}
			
			String regex = "^[0-9]*$";
			Pattern pattern = Pattern.compile(regex);   
			if(EdcSubgroupPlan.RULELEVEL_OOS_COUNT.equals(comboSampleDefectCode.getText().trim())&&
					"".equals(textPointfied.getText().trim())){
				UI.showInfo(Message.getString("edc.sample_plan_ooscount"));
				return;
			}else{
				Matcher code = pattern.matcher(textPointfied.getText().trim());
				if(!code.matches()&&!"".equals(textPointfied.getText().trim())){
					UI.showError(Message.getString("edc.sample_plan_variable_regex"));
					return;
				}
			}
			Matcher qualified = pattern.matcher(textQualified.getText().trim()); 
			if(!qualified.matches()){
				UI.showError(Message.getString("edc.sample_plan_variable_regex"));
				return;
			}
			
			line.setSubgroupQualifyRate(Long.parseLong(textQualified.getText()));
			line.setSubgroupQualifyCount(0l);//初始化count
			line.setSubgroupPlans(createList());
		}
		super.okPressed();
	}
	
	protected List<EdcSubgroupPlan> createList(){
		List<EdcSubgroupPlan> plans = new ArrayList<EdcSubgroupPlan>();
		EdcSubgroupPlan def = new EdcSubgroupPlan();
		int i = 1;
		def.setOrgRrn(Env.getSessionContext().getOrgRrn());
		def.setItem(Long.parseLong(param[0]));
		def.setItemDesc(param[i++].trim());
		def.setSampleSize(Long.parseLong(param[i++]));
		def.setSubgroupSize(new BigDecimal(param[i++]));
		def.setRuleLevel(EdcSubgroupPlan.RULELEVEL_DEFALUT);
		def.setCount(textPointfied.getText().equals("") ?
				null : new BigDecimal(textPointfied.getText()));
		plans.add(def);
		
		EdcSubgroupPlan normal = new EdcSubgroupPlan();
		normal.setOrgRrn(Env.getSessionContext().getOrgRrn());
		normal.setItem(Long.parseLong(textSampleItem.getText()));
		normal.setItemDesc(textSampleItemDesc.getText());
		normal.setRuleLevel(comboSampleDefectCode.getValue().toString());
		normal.setCount(textPointfied.getText().equals("") ?
				null : new BigDecimal(textPointfied.getText()));
		normal.setSampleSize(Long.parseLong(textSampleSize.getText()));
		normal.setSubgroupSize(new BigDecimal(textSubgroupSize.getText()));
		plans.add(normal);
		
		return plans;
	}
	
	protected void setEditData(List<EdcSubgroupPlan> plans, Boolean isEdit){
		if(plans.size()>1){
			for(EdcSubgroupPlan plan : plans){
				if(plan.getRuleLevel().equals(EdcSubgroupPlan.RULELEVEL_DEFALUT)){
					textDefItem.setText(plan.getItem().toString());
					textDefItemDesc.setText(plan.getItemDesc()==null?"":plan.getItemDesc());
				}else{
					textSampleItem.setText(plan.getItem().toString());
					textSampleItemDesc.setText(plan.getItemDesc()==null?"":plan.getItemDesc());
					comboSampleDefectCode.setText(plan.getRuleLevel());
					textPointfied.setText(plan.getCount()==null?"":plan.getCount().toString());
					textSampleSize.setText(plan.getSampleSize().toString());
					textSubgroupSize.setText(plan.getSubgroupSize().toString());
					textQualified.setText(
							line.getSubgroupQualifyRate()==null?"":line.getSubgroupQualifyRate().toString());
				}
			}
			if(!isEdit){
				textSampleItem.setEnabled(false);
				textSampleItemDesc.setEnabled(false);
				comboSampleDefectCode.setEnabled(false);
				textPointfied.setEnabled(false);
				textQualified.setEnabled(false);
			}else {
				if(comboSampleDefectCode.getText().equals(EdcSubgroupPlan.RULELEVEL_OOS_COUNT)){
					textPointfied.setEnabled(true);
				}
			}
		}
	}
}
