package com.glory.edc.itemset;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.edc.client.EDCManager;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcItemSetUploadTemp;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.excel.UploadErrorLog;
import com.glory.framework.base.excel.UploadException;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.glory.framework.core.exception.ExceptionBundle;

public class ItemSetUpload extends Upload {

	protected List<AbstractEdcSet> edcSets = Lists.newArrayList();
	
	private Map<String, EdcItem> itemMap = Maps.newHashMap();
	private Map<String, EdcItem> uploadItemMap = Maps.newHashMap();
	
	protected IEventBroker iEventBroker;

	public ItemSetUpload(String name) {
		super(name);
	}
	
	public ItemSetUpload(String name, IEventBroker iEventBroker) {
		super(name);
		this.iEventBroker = iEventBroker;
	}
	
	public ItemSetUpload(String authorityName, String buttonName, String tableName, IEventBroker iEventBroker) {
		super(authorityName, buttonName, tableName);
		this.iEventBroker = iEventBroker;
	}

	@Override
	protected void cudEntityList() {
		try {
			if (edcSets != null && edcSets.size() > 0) {
				EDCManager edcManager = Framework.getService(EDCManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				
				if (!uploadItemMap.isEmpty()) {
					List<EdcItem> edcItems = Lists.newArrayList();
					for (EdcItem edcItem : uploadItemMap.values()) {
						edcItems.add(edcItem);
					}
					edcItems = edcManager.saveEdcItems(edcItems, Env.getSessionContext());
					uploadItemMap = edcItems.stream().collect(Collectors.toMap(EdcItem::getName, Function.identity(), (v1, v2) -> v1));
				}
				
				//已存在的edcSet需要手动确认是否激活
				List<AbstractEdcSet> activeExistEdcSets = new ArrayList<AbstractEdcSet>();
				List<AbstractEdcSet> saveEdcSets = Lists.newArrayList();
				for(AbstractEdcSet abstractEdcSet : edcSets) {
					EdcItemSet edcItemSet = (EdcItemSet) abstractEdcSet;
					List<AbstractEdcSet> existEdcsets = adManager.getEntityList(Env.getOrgRrn(), AbstractEdcSet.class, 
							Integer.MAX_VALUE, "name = '"+edcItemSet.getName()+"'" , "");
					List<EdcItemSetLine> itemSetLines = Lists.newArrayList();
					for (EdcItemSetLine itemSetLine : edcItemSet.getItemSetLines()) {
						if (itemSetLine.getItemRrn() == null && uploadItemMap.containsKey(itemSetLine.getName())) {
							itemSetLine.setItemRrn(uploadItemMap.get(itemSetLine.getName()).getObjectRrn());
						}
						itemSetLine.setItemSet(edcItemSet);
						itemSetLines.add(itemSetLine);
					}
					saveEdcSets.add(edcItemSet);
					if (existEdcsets.size() > 0) {
						activeExistEdcSets.add(edcItemSet);
					}
				}
				if (activeExistEdcSets.size() > 0) {
					ItemSetUploadActiveDialog baseDialog = new ItemSetUploadActiveDialog("EDCItemSetUploadActiveForm", null, iEventBroker, activeExistEdcSets);
					if (Dialog.OK != baseDialog.open()) {
						return;
					}
				}
				
				List<AbstractEdcSet> abstractEdcSetedcSets = edcManager.saveEdcSets(saveEdcSets, Env.getSessionContext());
				//导入自动激活
				List<VersionControl> activeEdcSets = new ArrayList<>();
				for (AbstractEdcSet edcSet : abstractEdcSetedcSets) {
					edcSet = (AbstractEdcSet) adManager.getEntity(edcSet);
					activeEdcSets.add(edcSet);
				}
				edcManager.batchActive(activeEdcSets, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public boolean preCheck(List<ADBase> uploadList, List<ADBase> deleteList) throws UploadException {
		progress.setErrLogs(Lists.newArrayList());
		// 获取到导入数据,根据Set Name分组数据
		Map<String, Map<Integer, EdcItemSetUploadTemp>> setMap = Maps.newLinkedHashMap();
		if (uploadList != null && uploadList.size() > 0) {
			for (int i = 0; i < uploadList.size(); i++) {
				EdcItemSetUploadTemp edcItemSetUploadTemp = (EdcItemSetUploadTemp) uploadList.get(i);
				if (setMap.containsKey(edcItemSetUploadTemp.getItemSetName())) {
					setMap.get(edcItemSetUploadTemp.getItemSetName()).put(i + 2, edcItemSetUploadTemp);
				} else {
					Map<Integer, EdcItemSetUploadTemp> edcItemSetUploadTempIndex = Maps.newLinkedHashMap();
					edcItemSetUploadTempIndex.put(i + 2, edcItemSetUploadTemp);
					setMap.put(edcItemSetUploadTemp.getItemSetName(), edcItemSetUploadTempIndex);
				}
			}
		}
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<EdcItem> items = adManager.getEntityList(Env.getOrgRrn(), EdcItem.class, Env.getMaxResult());
			if (CollectionUtils.isNotEmpty(items)) {
				itemMap = items.stream().collect(Collectors.toMap(EdcItem::getName, Function.identity(), (v1, v2) -> v1));
			}
		} catch (Exception e) {
			e.printStackTrace();
			addErrorLog(null, null, "", e.getClass().getName());
		}

		// 组装并校验Set数据
		for (String setName : setMap.keySet()) {
			EdcItemSet itemSet = buildEdcItemSet(setMap.get(setName));
			edcSets.add(itemSet);
		}
		return progress.isSuccess();
	}

	private EdcItemSet buildEdcItemSet(Map<Integer, EdcItemSetUploadTemp> map) {
		try {
			EdcItemSet itemSet = new EdcItemSet();
			itemSet.setOrgRrn(Env.getOrgRrn());
			ADManager adManager = Framework.getService(ADManager.class);
			// 检查采集项名称是否存在、是否有重复
			List<EdcItemSetLine> setLines = Lists.newArrayList();
			List<String> itemNames = Lists.newArrayList();
			Long seqNo = 0L;
			for (Integer row : map.keySet()) {
				EdcItemSetUploadTemp temp = map.get(row);
				if (StringUtil.isEmpty(itemSet.getName())) {
					itemSet.setName(temp.getItemSetName());
					itemSet.setDescription(temp.getItemSetDescription());
				}
				// 校验name格式正确
				if (!match("^$|^[A-Za-z0-9-.-_]+$", temp.getItemSetName())) {
					addErrorLog(Message.getString("edc.set_collection_item_wrong_format"), row, "NAME", itemSet.getName());
				}

				if (itemNames.contains(temp.getItemName())) {
					addErrorLog(Message.getString("edc.set_collection_item_repetition"), row, "ITEM_NAME", itemSet.getName());
				} else {
					itemNames.add(temp.getItemName());
				}

				if (!match("^$|^[A-Za-z0-9-.-_]+$", temp.getItemName())) {
					addErrorLog(Message.getString("edc.set_collection_item_wrong_format"), row, "ITEM_NAME", temp.getItemName());
				} else {
					EdcItem edcItem = getEdcItem(row, temp);
					EdcItemSetLine line = new EdcItemSetLine();
					line.setOrgRrn(Env.getOrgRrn());
					line.setName(temp.getItemName());
					line.setItemRrn(edcItem.getObjectRrn());
					line.setDescription(edcItem.getDescription());
					line.setDataType(edcItem.getDataType());
					line.setSampleType(edcItem.getSampleType());
					line.setIsMandatory(DBUtil.toBoolean(temp.getIsMandatory()));
					line.setIsJudgeByManual(DBUtil.toBoolean(temp.getIsJudgeByManual()));
					line.setIsAutoDc(DBUtil.toBoolean(temp.getIsAutoDc()));
					line.setIsShowEquipment(DBUtil.toBoolean(temp.getIsShowEquipment()));
					line.setIsHoldLot(DBUtil.toBoolean(temp.getIsHoldLot()));
					seqNo = seqNo + 10;
					line.setSeqNo(seqNo);
					
					line.setUslString(temp.getUsl());
					line.setSlString(temp.getSl());
					line.setLslString(temp.getLsl());
					boolean flag = false;
					if (!StringUtil.isEmpty(line.getUslString())) {
						// 判断是否非负数
						if (!match("^\\d+(\\.\\d+)?$", line.getUslString())) {
							addErrorLog(Message.getString("edc.set_usl_non_negative_numbers"), row, "Usl", itemSet.getName(), temp.getItemName());
							flag = true;
						}
					}

					if (!StringUtil.isEmpty(line.getSlString())) {
						// 判断是否非负数
						if (!match("^\\d+(\\.\\d+)?$", line.getSlString())) {
							addErrorLog(Message.getString("edc.set_sl_non_negative_numbers"), row, "SL", itemSet.getName(), temp.getItemName());
							flag = true;
						}
					}

					if (!StringUtil.isEmpty(line.getLslString())) {
						// 判断是否非负数
						if (!match("^\\d+(\\.\\d+)?$", line.getLslString())) {
							addErrorLog(Message.getString("edc.set_lsl_non_negative_numbers"), row, "LSL", itemSet.getName(), temp.getItemName());
							flag = true;
						}
					}
					if (!flag) {
						String lslStr = StringUtil.isEmpty(line.getLslString()) ? "-" : line.getLslString();
						String slStr = StringUtil.isEmpty(line.getSlString()) ? "-" : line.getSlString();
						String uslStr = StringUtil.isEmpty(line.getUslString()) ? "-" : line.getUslString();
						line.setFormula(lslStr + "/" + slStr + "/" + uslStr);

						String lsl = line.getLslString();
						String sl = line.getSlString();
						String usl = line.getUslString();
						if (isTxtEmpty(usl, sl, lsl) == 2) {
							if (!StringUtil.isEmpty(usl) && !StringUtil.isEmpty(sl)) {
								if (Double.parseDouble(usl) <= Double.parseDouble(sl)) {
									addErrorLog(String.format(Message.getString("edc.set_collection_item"), itemSet.getName(),
											temp.getItemName()) + Message.getString("edc.usl_sl"),  row, "", new Object[] {});
								}
							}

							if (!StringUtil.isEmpty(usl) && !StringUtil.isEmpty(lsl)) {
								if (Double.parseDouble(usl) <= Double.parseDouble(lsl)) {
									addErrorLog(String.format(Message.getString("edc.set_collection_item"), itemSet.getName(),
											temp.getItemName()) + Message.getString("edc.usl_lsl"), row, "", new Object[] {});
								}
							}

							if (!StringUtil.isEmpty(sl) && !StringUtil.isEmpty(lsl)) {
								if (Double.parseDouble(sl) <= Double.parseDouble(lsl)) {
									addErrorLog(String.format(Message.getString("edc.set_collection_item"), itemSet.getName(),
											temp.getItemName()) + Message.getString("edc.sl_lsl"), row, "", new Object[] {});
								}
							}
						}
						if (isTxtEmpty(usl, sl, lsl) == 3) {
							if (Double.parseDouble(usl) < Double.parseDouble(sl)
									|| Double.parseDouble(usl) <= Double.parseDouble(lsl)
									|| Double.parseDouble(sl) < Double.parseDouble(lsl)) {
								addErrorLog(String.format(Message.getString("edc.set_collection_item"), itemSet.getName(),
										temp.getItemName()) + Message.getString("edc.usl_sl_lsl"), row, "", new Object[] {});
							}
						}

					}
					line.setSampleSize(1L);
					if (StringUtil.isEmpty(temp.getItem())) {
						temp.setItem("1");
					}

					if (EdcItemSetLine.DATA_TYPE_VARIABLE.equals(line.getDataType())) {
						if (StringUtil.isEmpty(temp.getItem()) || !match("^\\+?[1-9][0-9]*$", temp.getItem())) {
							addErrorLog(Message.getString("edc.set_item_non_zero_integer"), row, "ITEM", itemSet.getName(), temp.getItemName());
						} else {
							line.setItem(Long.valueOf(temp.getItem()));
							line.setItemDesc(temp.getItemDesc());
							line.setSubgroupSize(new BigDecimal(temp.getItem()));
						}
						
						if (line.getItem() != null && !StringUtil.isEmpty(temp.getItemDesc())) {
							String[] desces = new String[] { temp.getItemDesc() };
							if (temp.getItemDesc().contains(",") || temp.getItemDesc().contains(";")) {
								desces = temp.getItemDesc().split(",|;");
							}

							if (line.getItem().intValue() != desces.length) {
								addErrorLog(Message.getString("edc.set_item_and_itemdesc_mismatch"), row, "ITEM_DESC", itemSet.getName(), temp.getItemName());
							}
						}

						if (EdcItemSetLine.SAMPLELEVEL_COMP.equals(line.getSampleType())
								|| EdcItemSetLine.SAMPLELEVEL_SITE.equals(line.getSampleType())) {
							if (StringUtil.isEmpty(temp.getComp()) || !match("^\\+?[1-9][0-9]*$", temp.getComp())) {
								addErrorLog(Message.getString("edc.set_comp_non_zero_integer"), row, "COMP", itemSet.getName(), temp.getItemName());
							} else {
								line.setComp(Long.valueOf(temp.getComp()));
								line.setCompDesc(temp.getCompDesc());
								line.setSubgroupSize(
										new BigDecimal(temp.getItem()).multiply(new BigDecimal(temp.getComp())));
							}

							if (line.getComp() != null && !StringUtil.isEmpty(temp.getCompDesc())) {
								String[] desces = new String[] { temp.getCompDesc() };
								if (temp.getCompDesc().contains(",") || temp.getCompDesc().contains(";")) {
									desces = temp.getCompDesc().split(",|;");
								}

								if (line.getComp().intValue() != desces.length) {
									addErrorLog(Message.getString("edc.set_comp_and_compdesc_mismatch"), row, "COMP_DESC", itemSet.getName(), temp.getItemName());
								}
							}
						}

						if (EdcItemSetLine.SAMPLELEVEL_SITE.equals(line.getSampleType())) {
							if (StringUtil.isEmpty(temp.getSite()) || !match("^\\+?[1-9][0-9]*$", temp.getSite())) {
								addErrorLog(Message.getString("edc.set_site_non_zero_integer"), row, "SITE", itemSet.getName(), temp.getItemName());
							} else {
								line.setSite(Long.valueOf(temp.getSite()));
								line.setSiteDesc(temp.getSiteDesc());
								line.setSubgroupSize(
										new BigDecimal(temp.getItem()).multiply(new BigDecimal(temp.getComp()))
												.multiply(new BigDecimal(temp.getSite())));
							}

							if (line.getSite() != null && !StringUtil.isEmpty(temp.getSiteDesc())) {
								String[] desces = new String[] { temp.getSiteDesc() };
								if (temp.getSiteDesc().contains(",") || temp.getSiteDesc().contains(";")) {
									desces = temp.getSiteDesc().split(",|;");
								}

								if (line.getSite().intValue() != desces.length) {
									addErrorLog(Message.getString("edc.set_site_and_sitedesc_mismatch"), row, "SITE_DESC", itemSet.getName(), temp.getItemName());
								}
							}
						}

						if ((EdcItemSetLine.SAMPLELEVEL_COMP.equals(line.getSampleType())
								|| EdcItemSetLine.SAMPLELEVEL_SITE.equals(line.getSampleType()))) {
							if (!StringUtil.isEmpty(temp.getCompPosition())) {
								if (!match("\\d+(,\\d+)*", temp.getCompPosition())) {
									addErrorLog(Message.getString("edc.set_comp_position_incorrect_format"), row, "COMP_POSITION", itemSet.getName(), temp.getItemName());
								} else {
									String[] cps = new String[] { temp.getCompPosition() };
									if (temp.getCompPosition().contains(",") || temp.getCompPosition().contains(";")) {
										cps = temp.getCompPosition().split(",|;");
									}
									Set<String> cpSet = Sets.newHashSet(cps);

									// 校验Comp Position是否有重复
									if (cpSet.size() < cps.length) {
										addErrorLog(Message.getString("edc.set_comp_position_data_duplication"), row, "COMP_POSITION", itemSet.getName(), temp.getItemName());
									}

									// 校验Comp Position数据是否超过组件范围
									for (String cp : cps) {
										int CompPosition = Integer.parseInt(cp);
										if (CompPosition >= 26 || CompPosition <= 0) {
											addErrorLog(Message.getString("edc.set_comp_position_crossing"), row, "COMP_POSITION", itemSet.getName(), temp.getItemName());
										}
									}

									if (cpSet.size() != line.getItem().intValue()) {
										addErrorLog(String.format(Message.getString("edc.set_collection_item"), itemSet.getName(), temp.getItemName())
												+ Message.getString("edc.itemset_selectcomp_number_error"), row, "COMP_POSITION", new Object[] {});
									} else {
										line.setCompSLotSrc(temp.getCompPosition());
									}
								}
							}
						}
					} else {
						// 判断类型是否正确
						if (EdcItemSetLine.SPECTYPE_NUMBER.equals(temp.getSpecType())
								|| EdcItemSetLine.SPECTYPE_PERCENT.equals(temp.getSpecType())) {
							if (EdcItemSetLine.SAMPLELEVEL_NUMBER.equals(temp.getSampleLevel())
									|| EdcItemSetLine.SAMPLELEVEL_PERCENT.equals(temp.getSampleLevel())) {
								line.setSpecType(temp.getSpecType());
								line.setSampleLevel(temp.getSampleLevel());
							} else {
								addErrorLog(Message.getString("edc.set_sample_level_wrong_type"), row, "SAMPLE_LEVEL", itemSet.getName(), temp.getItemName());
							}
						} else {
							addErrorLog(Message.getString("edc.set_spec_type_wrong_type"), row, "SPEC_TYPE", itemSet.getName(), temp.getItemName());
						}

						if (EdcItemSetLine.SAMPLELEVEL_NUMBER.equals(line.getSampleLevel())) {
							// 判断是否非负整数
							if (!match("^\\+?[1-9][0-9]*$", temp.getAttribute())) {
								addErrorLog(Message.getString("edc.set_attribute_non_negative_integer"), row, "ATTRIBUTE", itemSet.getName(), temp.getItemName());
							} else {
								line.setSubgroupSize(new BigDecimal(temp.getAttribute()));
							}
						} else {
							// 判断是否非负数
							if (StringUtil.isEmpty(temp.getAttribute())
									|| !match("^\\d+(\\.\\d+)?$", temp.getAttribute())) {
								addErrorLog(Message.getString("edc.set_attribute_non_negative_numbers"), row, "ATTRIBUTE", itemSet.getName(), temp.getItemName());
							} else {
								BigDecimal value = new BigDecimal(temp.getAttribute());
								if (value.compareTo(new BigDecimal(100)) > 0) {
									addErrorLog(Message.getString("edc.set_attribute_cannot_be_greater_than_100"), row, "ATTRIBUTE", itemSet.getName(), temp.getItemName());
								} else {
									line.setSubgroupSize(new BigDecimal(temp.getAttribute()));
								}
							}
						}

						// 检查defectCode是否存在
						String defectCode = temp.getDefectCode();
						if (!StringUtil.isEmpty(defectCode)) {
							List<ADURefList> aduRefLists = adManager.getADURefList(Env.getOrgRrn(), defectCode);
							if (CollectionUtils.isEmpty(aduRefLists)) {
								addErrorLog(Message.getString("edc.set_defect_code_does_not_exist"), row, "DEFECT_CODE", itemSet.getName(), temp.getItemName());
							} else {
								line.setDefectCodeSrc(defectCode);
							}
						}
					}
					setLines.add(line);
				}
			}
			itemSet.setItemSetLines(setLines);
			return itemSet;
		} catch (Exception e) {
			e.printStackTrace();
			addErrorLog(null, null, "", e.getClass().getName());
		}
		return null;
	}

	private void addErrorLog(String messageKey, Integer row, String columnName, Object...messageParameter) {
		String message = Message.getString(messageKey);
		if (StringUtil.isEmpty(message)) {
			message = messageKey;
		}
		Long index = null;
		if (row != null) {
			index = Long.valueOf(row);
		}
		UploadErrorLog errorLog = null;
		if (messageParameter != null && messageParameter.length > 0) {
			errorLog = new UploadErrorLog(index, null, columnName, String.format(messageKey, messageParameter));
		} else {
			errorLog = new UploadErrorLog(index, null, columnName, messageKey);
		}
		progress.getErrLogs().add(errorLog);
	}

	private int isTxtEmpty(String usl, String sl, String lsl) {
		int result = 0;
		if (!StringUtil.isEmpty(sl)) {
			result = result + 1;
		}
		if (!StringUtil.isEmpty(usl)) {
			result = result + 1;
		}
		if (!StringUtil.isEmpty(lsl)) {
			result = result + 1;
		}
		return result;
	}

	/**
	 * @param regex
	 *            正则表达式字符串
	 * @param str
	 *            要匹配的字符串
	 * @return 如果str 符合 regex的正则表达式格式,返回true, 否则返回 false;
	 */
	private static boolean match(String regex, String str) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(str);
		return matcher.matches();
	}
	
	private EdcItem getEdcItem(Integer i, EdcItemSetUploadTemp itemSetUploadTemp) {
		EdcItem edcItem = null;
		if (!itemMap.containsKey(itemSetUploadTemp.getItemName())) {
			if (!uploadItemMap.containsKey(itemSetUploadTemp.getItemName())) {
				edcItem = itemSetUploadTemp.getEdcItem();
				edcItem.setName(itemSetUploadTemp.getItemName());
				uploadItemMap.put(itemSetUploadTemp.getItemName(), edcItem);
			} else {
				edcItem = uploadItemMap.get(itemSetUploadTemp.getItemName());
			}
		} else {
			edcItem = itemMap.get(itemSetUploadTemp.getItemName());
		}
		if (StringUtil.isEmpty(edcItem.getName())) {
			addErrorLog(edcItem.getName() + Message.getString(ExceptionBundle.bundle.CommonIsNull()), i, "EdcItemName");
		}
		
		//效验数据类型是否存在
		if (StringUtil.isEmpty(edcItem.getDataType())) {
			addErrorLog(String.format(edcItem.getDataType() + Message.getString(ExceptionBundle.bundle.CommonIsNull())), i, "EdcItemDataType");
		}
		
		//效验抽样类型是否存在
		if (StringUtil.isEmpty(edcItem.getSampleType())) {
			addErrorLog(String.format(edcItem.getSampleType() + Message.getString(ExceptionBundle.bundle.CommonIsNull())), i, "EdcItemSampleType");
		}
		return edcItem;
	}
	
	public static List<Object> buildEdcItemSetUploadTempList(List<EdcItemSet> itemSets) {
		List<Object> itemSetUploadTemps = Lists.newArrayList();
		for (EdcItemSet itemSet : itemSets) {
			for (EdcItemSetLine itemSetLine : itemSet.getItemSetLines()) {
				EdcItemSetUploadTemp edcItemSetUploadTemp = new EdcItemSetUploadTemp();
				edcItemSetUploadTemp.setItemSetName(itemSet.getName());
				edcItemSetUploadTemp.setItemSetDescription(itemSet.getDescription());
				edcItemSetUploadTemp.setItemName(itemSetLine.getEdcItem().getName());
				edcItemSetUploadTemp.setEdcItem(itemSetLine.getEdcItem());
				edcItemSetUploadTemp.setIsMandatory(DBUtil.toString(itemSetLine.getIsMandatory()));
				edcItemSetUploadTemp.setIsJudgeByManual(DBUtil.toString(itemSetLine.getIsJudgeByManual()));
				edcItemSetUploadTemp.setIsAutoDc(DBUtil.toString(itemSetLine.getIsAutoDc()));
				edcItemSetUploadTemp.setIsShowEquipment(DBUtil.toString(itemSetLine.getIsShowEquipment()));
				edcItemSetUploadTemp.setIsHoldLot(DBUtil.toString(itemSetLine.getIsHoldLot()));
				edcItemSetUploadTemp.setUsl(itemSetLine.getUslString());
				edcItemSetUploadTemp.setSl(itemSetLine.getSlString());
				edcItemSetUploadTemp.setLsl(itemSetLine.getLslString());
				edcItemSetUploadTemp.setItem(DBUtil.toString(itemSetLine.getItem()));
				edcItemSetUploadTemp.setItemDesc(itemSetLine.getItemDesc());
				edcItemSetUploadTemp.setComp(DBUtil.toString(itemSetLine.getComp()));
				edcItemSetUploadTemp.setCompDesc(itemSetLine.getCompDesc());
				edcItemSetUploadTemp.setSite(DBUtil.toString(itemSetLine.getSite()));
				edcItemSetUploadTemp.setSiteDesc(itemSetLine.getSiteDesc());
				edcItemSetUploadTemp.setCompPosition(itemSetLine.getCompSLotSrc());
				
				edcItemSetUploadTemp.setSpecType(itemSetLine.getSpecType());
				edcItemSetUploadTemp.setSampleLevel(itemSetLine.getSampleLevel());
				edcItemSetUploadTemp.setDefectCode(itemSetLine.getDefectCodeSrc());
				edcItemSetUploadTemp.setAttribute(DBUtil.toString(itemSetLine.getSubgroupSize()));
				itemSetUploadTemps.add(edcItemSetUploadTemp);
			}
		}
		return itemSetUploadTemps;
	}
	
}
