package com.glory.edc.itemset;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;

public class BaseInfoPage extends FlowWizardPage implements VerifyListener {

	private static String FIELDNAME_ITEM_NAME = "name";

	protected BaseInfoForm form;
	protected FormToolkit toolkit;
	protected Text textUSL, textSL, textLSL;
	protected Label labUSL, labSL, labLSL;
	
	private String usl, sl, lsl;
	protected SetItemWizard sw, siw;

	public BaseInfoPage() {
		super();
	}

	public BaseInfoPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	
	@Override
	public String doNext() {
		LinkedHashMap<String, IField> iFields = form.getFields();
		if (isOnlyData(iFields)) {
			if (isValidate()) {
				form.saveToObject();
				EdcItemSetLine line = (EdcItemSetLine) form.getObject();
				EdcItem item = (EdcItem) iFields.get(FIELDNAME_ITEM_NAME).getData();
				if (item != null) {
					line.setEdcItem(item);
				} else {
					UI.showError(Message.getString("edc.message_selectname"));
					return null;
				}

				line.setUslString(usl);
				line.setSlString(sl);
				line.setLslString(lsl);
				((SetItemWizard) this.getWizard()).setLine(line);  

				if(line != null && line.getDataType() != null) {
					if(EdcItem.DATATYPE_ATTRIBUTE.equals(line.getDataType())) {
						line.setSampleType(EdcItem.SAMPLETYPE_ITEM); 
						return "attributePlanItem";  
					} else {
						if (line != null && line.getSampleType() != null) {
							if (EdcItem.SAMPLETYPE_ITEM.equals(line.getSampleType())) {
								return "samplePlanItem";
							} else if (EdcItem.SAMPLETYPE_COMP.equals(line.getSampleType())) {
								return "samplePlanComp";
							} else if (EdcItem.SAMPLETYPE_SITE.equals(line.getSampleType())) {
								return "samplePlanSite";
							} else {
								UI.showError(Message.getString("edc.itemset_sampletype_unknown")); 
							}
						} else {
							UI.showError(Message.getString("edc.message_selectname")); 
						}	
					}
				}
			}
		} else {
			UI.showError(Message.getString("edc.message_nameolny"));  
			return null;
		}
		return null;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private boolean isOnlyData(LinkedHashMap<String, IField> iFields) {
		siw = (SetItemWizard) this.getWizard();
		if (SetItemWizard.ACTION_TYPE_ADD.equals(sw.getActionType())) {
			IField itemName = (IField) iFields.get("name");
			String sItemName = (String) itemName.getValue();
			List<EdcItemSetLine> lines = siw.getExistlines();
			if (lines == null) {
				lines = new LinkedList();
			}
			if (lines.size() > 0) {
				for (int i = 0; i < lines.size(); i++) {
					if (lines.get(i).getName().equals(sItemName)) {
						return false;
					}
				}
				return true;
			} else {
				return true;
			}
		} else {
			return true;
		}
	}

	private boolean isValidate() {
		usl = textUSL.getText().trim();
		sl = textSL.getText().trim();
		lsl = textLSL.getText().trim();
		
	    try {
				if(!"".equals(usl)) {
					if (!isRegex(usl)) {
						return false;
					}
					Double.parseDouble(usl) ;
				}
				if(!"".equals(sl)) {
					if (!isRegex(sl)) {
						return false;
					}
					Double.parseDouble(sl);
				}
				if(!"".equals(lsl)) {
					if (!isRegex(lsl)) {
						return false;
					}
					Double.parseDouble(lsl);
				}
			} catch (Exception e) {
				return true;
			}
			if (isTxtEmpty(usl, sl, lsl) == 2) {
				if (!"".equals(usl) && !"".equals(sl)) {
					if (Double.parseDouble(usl) > Double.parseDouble(sl))
						return true;
					UI.showError(Message.getString("edc.usl_sl"));
				}

				if (!"".equals(usl) && !"".equals(lsl)) {
					if (Double.parseDouble(usl) > Double.parseDouble(lsl))
						return true;
					UI.showError(Message.getString("edc.usl_lsl"));
				}

				if (!"".equals(sl) && !"".equals(lsl)) {
					if (Double.parseDouble(sl) > Double.parseDouble(lsl))
						return true;
					UI.showError(Message.getString("edc.sl_lsl"));
				}
			}
			if (isTxtEmpty(usl, sl, lsl) == 3) {
					if (Double.parseDouble(usl) >= Double.parseDouble(sl)
							&& Double.parseDouble(usl) > Double.parseDouble(lsl)
							&& Double.parseDouble(sl) >= Double.parseDouble(lsl))
						return true;
					UI.showError(Message.getString("edc.usl_sl_lsl"));
			}
			if (isTxtEmpty(usl, sl, lsl) < 2) {
				if (!"".equals(usl)) {
					Double.parseDouble(usl);
				}
				if (!"".equals(sl)) {
					Double.parseDouble(sl);
				}
				if (!"".equals(lsl)) {
					Double.parseDouble(lsl);
				}
				return true;
			}
		return false;
	}

	private int isTxtEmpty(String usl, String sl, String lsl) {
		int result = 0;
		try {

			if (!"".equals(usl.trim())) {
				result = result + 1;
			}
			if (!"".equals(sl.trim())) {
				result = result + 1;
			}
			if (!"".equals(lsl.trim())) {
				result = result + 1;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	
	private boolean isRegex(String str) {
		LinkedHashMap<String, IField> iFields = form.getFields();
		//设置使用isUseParameter参数true时，不检查表达式
		if(iFields != null && iFields.get("isUseParameter") != null && !(boolean) iFields.get("isUseParameter").getValue()) {
			//正则校验
			String regEx = "^(([1-9-]{1}\\d*)|(0{1}))(\\.\\d{0,4})?$";
			Pattern pattern = Pattern.compile(regEx);
			Matcher matcher = pattern.matcher(str.trim());
			if (!matcher.matches()) {
				UI.showError(Message.getString("wip.changemo_checking") + "->" + str);
				return false;
			}
		}
		return true;
	}

	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public void createControl(Composite parent) {
		toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_VERTICAL);
		composite.setLayoutData(gd);
		composite.setBackground(new Color(null, 236, 233, 216));
		sw = (SetItemWizard) this.getWizard();
		EdcItemSetLine line = sw.getLine();

		if (SetItemWizard.ACTION_TYPE_EDIT.equals(sw.getActionType())) {
			composite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
			// form页面的生成
			form = new BaseInfoForm(composite, SWT.NULL, line);
			form.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			Group groupDown = new Group(composite, SWT.NULL);
			groupDown.setText("Spec");
			groupDown.setLayout(new GridLayout(6, false));
			groupDown.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			labLSL = toolkit.createLabel(groupDown, "LSL");
			labLSL.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
			
			textLSL = toolkit.createText(groupDown, null == sw.getLine()
					.getLslString() ? "" : sw.getLine().getLslString()
					.toString(), SWT.BORDER);
			textLSL.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			textLSL.addVerifyListener(this);

			labSL = toolkit.createLabel(groupDown, "SL");
			labSL.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
			
			textSL = toolkit.createText(groupDown,
					null == sw.getLine().getSlString() ? "" : sw.getLine()
							.getSlString().toString(), SWT.BORDER);
			textSL.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			textSL.addVerifyListener(this);
			
			labUSL = toolkit.createLabel(groupDown, "USL");
			labUSL.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
			
			textUSL = toolkit.createText(groupDown, null == sw.getLine()
					.getUslString() ? "" : sw.getLine().getUslString()
					.toString(), SWT.BORDER);
			textUSL.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			textUSL.addVerifyListener(this);
			setControl(composite);
			setTitle(Message.getString("edc.edcset_changeitem"));
			setMessage(Message.getString("edc.edcset_changeitem_message"));
		} else if (SetItemWizard.ACTION_TYPE_ADD.equals(sw.getActionType())) {
			composite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));

			// form页面的生成
			form = new BaseInfoForm(composite, SWT.NULL, line);
			form.setLayoutData(new GridData(GridData.FILL_BOTH));

			Group groupDown = new Group(composite, SWT.NULL);
			groupDown.setText("Spec");
			groupDown.setLayout(new GridLayout(6, false));
			groupDown.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			labLSL = toolkit.createLabel(groupDown, "LSL");
			labLSL.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
			
			textLSL = toolkit.createText(groupDown, "", SWT.BORDER);
			textLSL.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			textLSL.addVerifyListener(this);
			
			labSL = toolkit.createLabel(groupDown, "SL");
			labSL.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
			
			textSL = toolkit.createText(groupDown, "", SWT.BORDER);
			textSL.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			textSL.addVerifyListener(this);
			labUSL = toolkit.createLabel(groupDown, "USL");
			labUSL.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
			
			textUSL = toolkit.createText(groupDown, "", SWT.BORDER);
			textUSL.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			textUSL.addVerifyListener(this);
			setControl(composite);
			setTitle(Message.getString("edc.edcset_additem"));
			setMessage(Message.getString("edc.edcset_additem_message"));
		}

	}

	@Override
	public void verifyText(VerifyEvent e) {
	}
}
