package com.glory.edc.itemset;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ExportToolItemGlc;
import org.eclipse.swt.widgets.ImportToolItemGlc;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IFormPart;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.context.client.ContextManager;
import com.glory.common.context.model.ContextValue;
import com.glory.edc.client.EDCManager;
import com.glory.edc.model.EdcContextValueEcn;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcItemSetLineAttr;
import com.glory.edc.model.EdcSubgroupPlan;
import com.glory.edc.model.calculation.FormulaVariable;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.dialog.CopyFromDialog;
import com.glory.framework.base.entitymanager.forms.VersionControlProperties;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.excel.download.Download;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class ItemSetProperties extends VersionControlProperties {

	protected IEventBroker eventBroker;
	
	public static final String KEY_UNFROZEN = "UnFrozen";
	public static final String KEY_INACTIVE = "InActive";

	private static String FIELDNAME_ITEMSETLINES = "itemSetLines";
	private static String TEMPLATE_FILE_NAME = "edcitemset_template.xlsx";
	protected ItemSetTableSelectField fItemSetLines;

	protected ToolItem itemInActive;
	protected ToolItem itemUnFrozen;
	
	protected EdcItemSetLine edcItemSetLine;
	protected boolean insertPlanFlag = false;
	
	protected ImportToolItemGlc itemImport;
	protected ExportToolItemGlc itemExport;

	public ItemSetProperties() {
		super();
	}
	
	public ItemSetProperties(IEventBroker eventBroker) {
		super();
		this.eventBroker = eventBroker;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		tBar.setEnabled(true);
		createToolItemNew(tBar);
		createToolItemCopyFrom(tBar);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemFrozen(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnFrozen(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemActive(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemInActive(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemInActive(ToolBar tBar) {
		itemInActive = new AuthorityToolItem(tBar, SWT.PUSH, getMasterParent().getTableManager()
				.getADTable().getAuthorityKey() + "." + KEY_INACTIVE);
		itemInActive.setImage(SWTResourceCache.getImage("inactive"));
		itemInActive.setText(Message.getString(ExceptionBundle.bundle.CommonInActive()));
		itemInActive.setEnabled(true);
		itemInActive.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				inActiveAdapter();
			}
		});
	}
	protected void createToolItemUnFrozen(ToolBar tBar) {
		itemUnFrozen = new AuthorityToolItem(tBar, SWT.PUSH, getMasterParent().getTableManager()
				.getADTable().getAuthorityKey() + "." + KEY_UNFROZEN);
		itemUnFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
		itemUnFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
		itemUnFrozen.setEnabled(true);
		itemUnFrozen.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unFrozenAdapter();
			}
		});
	}
	protected void createToolItemActive(ToolBar tBar) {
		itemActive = new AuthorityToolItem(tBar, SWT.PUSH, getMasterParent().getTableManager()
				.getADTable().getAuthorityKey() + "." + KEY_ACTIVE);
		itemActive.setImage(SWTResourceCache.getImage("active"));
		itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonActive()));
		itemActive.setEnabled(true);
		itemActive.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				activeAdapter();
			}
		});
	}
	
	protected void createToolItemImport(ToolBar tBar) {
		itemImport = new ImportToolItemGlc(tBar, null, getTable().getAuthorityKey(), null, null);
		itemImport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				ItemSetUpload upload = new ItemSetUpload(getTable().getAuthorityKey(), null, "EDCItemSetUploadTemp", eventBroker);
				if (upload.getUploadProgress().init()) {
					if (upload.run()) {
						getMasterParent().refresh();
					}
				}
			}
		});
	}
	
	protected void createToolItemExport(ToolBar tBar) {
		itemExport = new ExportToolItemGlc(tBar, null, getTable().getAuthorityKey(), null, null);
		itemExport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				try {
					List<Object> vObjects = getMasterParent().getTableManager().getCheckedObject();
					List<EdcItemSet> list = Lists.newArrayList();
					EDCManager edcManager = Framework.getService(EDCManager.class);
					if (CollectionUtils.isNotEmpty(vObjects)) {
						for (Object object : vObjects) {
							EdcItemSet itemSet =(EdcItemSet) object;
							itemSet = (EdcItemSet) edcManager.getEdcSet(Env.getOrgRrn(), itemSet.getName(), itemSet.getVersion(), true, true);
							list.add(itemSet);
						}
					}
					if (CollectionUtils.isNotEmpty(ItemSetUpload.buildEdcItemSetUploadTempList(list))) {
						Download download = new Download(getTable().getAuthorityKey(), null);
						if (download.getDownloadProgress().init()) {
							download.run(ItemSetUpload.buildEdcItemSetUploadTempList(list));
						}
					} else {
						DefaultDownloadWriter.exportTemplate(getTable().getAuthorityKey(), null);
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
			}
		});
	}
	
	protected void unFrozenAdapter() {
		try {
			List<VersionControl> edcItemSets = getCheckItemSet(VersionControl.STATUS_FROZNE);
			if(edcItemSets != null && edcItemSets.size() > 0) {
				EDCManager basManager = Framework.getService(EDCManager.class);
				basManager.batchUnFrozen(edcItemSets, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonUnFrozenSuccess()));
				setAdObject(new EdcItemSet());
				refreshAdapter();
				getMasterParent().refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void frozenAdapter() {
		try {
			List<VersionControl> edcItemSets = getCheckItemSet(VersionControl.STATUS_UNFROZNE);
			if(edcItemSets != null && edcItemSets.size() > 0) {
				EDCManager basManager = Framework.getService(EDCManager.class);
				basManager.batchFrozen(edcItemSets, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonFrozenSuccess()));
				setAdObject(new EdcItemSet());
				refreshAdapter();
				getMasterParent().refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void inActiveAdapter() {
		try {
			List<VersionControl> edcItemSets = getCheckItemSet(VersionControl.STATUS_INACTIVE);
			if(edcItemSets != null && edcItemSets.size() > 0) {
				for(VersionControl vObject : edcItemSets) {
					//可以将激活状态的对象转换为InActive状态
					//首先判断对象是否在使用中
					Map<String, String> map = new HashMap<String, String>();
					map.put(EdcContextValueEcn.CONTEXT_RESULT_EDC_SET_NAME, vObject.getName());
					
					ContextManager ctxManager = Framework.getService(ContextManager.class);
					List<ContextValue> values = ctxManager.getActiveContextValuesByResult(Env.getOrgRrn(), 
							EdcContextValueEcn.CONTEXT_NAME, map, false);
					if (values.size() >0) {
						UI.showWarning(Message.getString(ExceptionBundle.bundle.ConstraintViolation(EdcContextValueEcn.CONTEXT_NAME).getErrorCode()));
						return;
					}
				}
				EDCManager basManager = Framework.getService(EDCManager.class);
				basManager.batchInActive(edcItemSets, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
				setAdObject(new EdcItemSet());
				refreshAdapter();
				getMasterParent().refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void activeAdapter() {
		try {
			List<VersionControl> edcItemSets = getCheckItemSet(VersionControl.STATUS_ACTIVE);
			if(edcItemSets != null && edcItemSets.size() > 0) {
				EDCManager basManager = Framework.getService(EDCManager.class);
				basManager.batchActive(edcItemSets, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonActiveSuccess()));
				setAdObject(new EdcItemSet());
				refreshAdapter();
				getMasterParent().refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public boolean delete() {
		try {
			List<VersionControl> edcItemSets = getCheckItemSet(VersionControl.STATUS_DELETE);
			if(edcItemSets != null && edcItemSets.size() > 0) {
				boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
				if (confirmDelete) {
					EDCManager basManager = Framework.getService(EDCManager.class);
					basManager.batchDelete(edcItemSets, Env.getSessionContext());
					setAdObject(new EdcItemSet());
					refreshAdapter();
					getMasterParent().refresh();
					return true;
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		return false;
	}

	public List<VersionControl> getCheckItemSet(String status){
		List<Object> vObjects = getMasterParent().getTableManager().getCheckedObject();
		List<VersionControl> edcItemSets = new ArrayList<VersionControl>();
		List<String> s = new ArrayList<String>();
		for(Object object : vObjects) {
			Boolean checkStatus = true;
			Boolean checkName = true;
			VersionControl vObject = (VersionControl) object;
			
			if(VersionControl.STATUS_UNFROZNE.equals(status)) {//冻结操作
				if(VersionControl.STATUS_UNFROZNE.equals(vObject.getStatus())) {
					checkStatus = false;
				}
			}else if(VersionControl.STATUS_FROZNE.equals(status)) {//解冻操作
				if(VersionControl.STATUS_FROZNE.equals(vObject.getStatus()) || 
						VersionControl.STATUS_INACTIVE.equals(vObject.getStatus())) {
					checkStatus = false;
				}
			}else if(VersionControl.STATUS_ACTIVE.equals(status)) {//激活操作
				if(VersionControl.STATUS_FROZNE.equals(vObject.getStatus()) || 
						VersionControl.STATUS_INACTIVE.equals(vObject.getStatus())) {
					checkStatus = false;
				}
			}else if(VersionControl.STATUS_INACTIVE.equals(status)){//失效操作
				if(VersionControl.STATUS_ACTIVE.equals(vObject.getStatus())) {
					checkStatus = false;
				}
			}else if(VersionControl.STATUS_DELETE.equals(status)) {//删除操作
				if(VersionControl.STATUS_UNFROZNE.equals(vObject.getStatus())) {
					checkStatus = false;
					checkName = false;
				}
			}
			if(checkStatus) {
				UI.showError(vObject.getName() + ":" + String.format(Message.getString("bas.inconsistent_state"), vObject.getStatus()));
				return null;
			}
			
			if(checkName && s.contains(vObject.getName())) {
				UI.showError(Message.getString("bas.same_object") + vObject.getName());
				return null;
			}
			s.add(vObject.getName());
			edcItemSets.add(vObject);
		}
		return edcItemSets;
	}
	
	@Override
	public void statusChanged(String newStatus) {
		List<Object> vObjects = getMasterParent().getTableManager().getCheckedObject();
		List<String> statusList = vObjects.stream().map(object -> (VersionControl)object)
				.map(VersionControl :: getStatus).collect(Collectors.toList());
		
		Optional<String> isUnFrozenOrDelete = statusList.stream().
				filter(status -> !VersionControl.STATUS_UNFROZNE.equals(status)).findFirst();
		Optional<String> isActiveOrFrozen = statusList.stream().
				filter(status -> !(VersionControl.STATUS_FROZNE.equals(status) || VersionControl.STATUS_INACTIVE.equals(status))).
				findFirst();
		Optional<String> isInActive = statusList.stream().
				filter(status -> !VersionControl.STATUS_ACTIVE.equals(status)).findFirst();
		if (isUnFrozenOrDelete.isPresent()) {
			itemDelete.setEnabled(false);
			itemFrozen.setEnabled(false);
		} else {
			itemDelete.setEnabled(true);
			itemFrozen.setEnabled(true);
		}
		
		if (isActiveOrFrozen.isPresent()) {
			itemActive.setEnabled(false);
			itemUnFrozen.setEnabled(false);
		} else {
			itemActive.setEnabled(true);
			itemUnFrozen.setEnabled(true);
		}

		if (isInActive.isPresent()) {
			itemInActive.setEnabled(false);
		} else {
			itemInActive.setEnabled(true);
		}
	}
	
	@Override
	protected void newAdapter() {
		itemSave.setEnabled(true);
		super.newAdapter();
	}

	@Override
	public void createContents(Composite parent) {
       super.createContents(form, parent);
       fItemSetLines = (ItemSetTableSelectField)this.getField(FIELDNAME_ITEMSETLINES);
       fItemSetLines.setAddSelectionListener(new SelectionAdapter() {
    	   @Override
			public void widgetSelected(SelectionEvent event) {
				addAdapter(event);
			}
       });
       fItemSetLines.setDoubleClickListener(new AbstractMouseSelectionAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				editAdapter();
			}   
       });
       
       fItemSetLines.setFormulaClickListener(new SelectionAdapter() {
    	   @Override
			public void widgetSelected(SelectionEvent event) {
    		   addFormulaAdapter(event);
			}
       });
	}

    @SuppressWarnings("unchecked")
    protected void addAdapter(SelectionEvent event){
    	if (!saveToObject()) {
			return;
		}
    	
		EdcItemSetLine line = new EdcItemSetLine();
		line.setOrgRrn(Env.getOrgRrn());
		line.setIsActive(true);
		
		List<EdcItemSetLine> lines = (List<EdcItemSetLine>)fItemSetLines.getValue();
		
		SetItemWizard siw = new SetItemWizard(line, lines, SetItemWizard.ACTION_TYPE_ADD);
		SetItemDialog sid = new SetItemDialog(UI.getActiveShell(), siw);
		if (sid.open() == Dialog.OK) {
			line = siw.getLine();
			String lsl = "".equals(line.getLslString().trim()) ? "-" : line.getLslString();
			String sl = "".equals(line.getSlString().trim()) ? "-" : line.getSlString();
			String usl = "".equals(line.getUslString().trim()) ? "-" : line.getUslString();
			line.setFormula( lsl + "/" + sl + "/" + usl);
			if(lines==null){
				lines=new ArrayList<EdcItemSetLine>();
				line.setSeqNo(Long.valueOf(10));
			}
			line.setSeqNo(Long.valueOf(lines.size()+1)*10);
			lines.add(line);
			fItemSetLines.setValue(lines);
			fItemSetLines.refresh();
		}
	}
    
	@SuppressWarnings("unchecked")
	protected void addFormulaAdapter(SelectionEvent event) {
		if (!saveToObject()) {
			return;
		}

		EdcItemSetLine line = new EdcItemSetLine();
		line.setOrgRrn(Env.getOrgRrn());
		line.setIsActive(true);

		SetItemWizard siw = new SetItemWizard("EDCSetFormula", line, (EdcItemSet) getAdObject(),
				SetItemWizard.ACTION_TYPE_ADD);
		SetItemDialog sid = new SetItemDialog(UI.getActiveShell(), siw);
		if (sid.open() == Dialog.OK) {
			List<EdcItemSetLine> lines = (List<EdcItemSetLine>) fItemSetLines.getValue();
			line = siw.getLine();
			if (lines == null) {
				lines = new ArrayList<EdcItemSetLine>();
				line.setSeqNo(Long.valueOf(10));
			}
			line.setSeqNo(Long.valueOf(lines.size() + 1) * 10);
			lines.add(line);
			fItemSetLines.setValue(lines);
			fItemSetLines.refresh();
		}
	}
	
	
	@SuppressWarnings("unchecked")
    protected void editAdapter(){
		try {
			List<EdcItemSetLine> lines = (List<EdcItemSetLine>)fItemSetLines.getValue();
			
			edcItemSetLine = (EdcItemSetLine)fItemSetLines.getTableManager().getSelectedObject();
			
			if (!saveToObject()) {
				return;
			}
			
			if (EdcItemSetLine.DATA_TYPE_FORMULA.equals(edcItemSetLine.getDataType())) {
				if (edcItemSetLine.getObjectRrn() != null) {
					// 查询formula
					String whereClause = " edcSetLineRrn =" + edcItemSetLine.getObjectRrn();
					ADManager adManager = Framework.getService(ADManager.class);
					List<FormulaVariable> variables = adManager.getEntityList(
							Env.getOrgRrn(), FormulaVariable.class, Env.getMaxResult(), whereClause, null);
					edcItemSetLine.setFormulaVariables(variables);
				}
				
				SetItemWizard siw = new SetItemWizard("EDCSetFormula", edcItemSetLine, (EdcItemSet) getAdObject(), SetItemWizard.ACTION_TYPE_EDIT);
			
				SetItemDialog sid = new SetItemDialog(UI.getActiveShell(), siw);
				if (sid.open() == Dialog.OK) {
					List<EdcItemSetLine> newLines = new ArrayList<EdcItemSetLine>();
					for (EdcItemSetLine line : lines) {
			            newLines.add(line);
					}
					fItemSetLines.setValue(newLines);
					fItemSetLines.refresh();
				}
			} else {
				SetItemWizard siw = new SetItemWizard(edcItemSetLine, lines, SetItemWizard.ACTION_TYPE_EDIT);
				
				ADManager manager = Framework.getService(ADManager.class);
				EdcItem item = new EdcItem();
				item.setObjectRrn(edcItemSetLine.getItemRrn());
				item = (EdcItem)manager.getEntity(item);
				siw.getLine().setEdcItem(item);
				
				SetItemDialog sid = new SetItemDialog(UI.getActiveShell(), siw);
				if (sid.open() == Dialog.OK) {
					List<EdcItemSetLine> newLines = new ArrayList<EdcItemSetLine>();
					for (EdcItemSetLine line : lines) {
					    String lsl = (line.getLslString() == null || "".equals(line.getLslString().trim())) ? "-" : line.getLslString();
			            String sl = (line.getSlString() == null || "".equals(line.getSlString().trim())) ? "-" : line.getSlString();
			            String usl = (line.getUslString() == null || "".equals(line.getUslString().trim())) ? "-" : line.getUslString();
			            line.setFormula( lsl + "/" + sl + "/" + usl);
			            newLines.add(line);
					}
					fItemSetLines.setValue(newLines);
					fItemSetLines.refresh();
				}
			}
			
			
//			sid.open();
//			refreshAdapter();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void setEnable(boolean enableFlag) {
		for (IForm detailForm : getDetailForms()) {
			TableSelectField filed = (TableSelectField)((Form)detailForm).getFields().get(FIELDNAME_ITEMSETLINES);
			if (filed != null) {
				filed.setEnabled(enableFlag);
			} else {
				detailForm.setEnabled(enableFlag);
			}
		}
	}

	protected void copyFromAdapter() {
		List<ADField> fields = getTable().getFields();
		for (ADField field : fields) {
			if ("copyFrom".equals(field.getName())) {
				CopyFromDialog dialog = new CopyFromDialog(Display.getCurrent().getActiveShell(), field);
				if (dialog.open() == Dialog.OK) {
					form.getMessageManager().removeAllMessages();
					
					String setRrn = dialog.getJobRrn();
					try {
						if(setRrn == null || "".equals(setRrn.trim())) {
							return;
						}
						
						// 查询EDCSet详细数据
						ADManager adManager = Framework.getService(ADManager.class);
						EdcItemSet copyFromSet = (EdcItemSet) getAdObject();
						copyFromSet.setObjectRrn(new Long(setRrn));
						copyFromSet = (EdcItemSet) adManager.getEntity(copyFromSet);
						
						EDCManager edcManager = Framework.getService(EDCManager.class);
						copyFromSet = (EdcItemSet) edcManager.getEdcSetFull(
								Env.getOrgRrn(), copyFromSet.getName(), copyFromSet.getVersion(), true);
						
						// 处理Line
						List<EdcItemSetLine> cloneLines = Lists.newArrayList();
						
						List<EdcItemSetLine> copyFromLineList = copyFromSet.getItemSetLines();
						for (EdcItemSetLine copyFromLine : copyFromLineList) {
							// 处理Line Attribute
							List<EdcItemSetLineAttr> cloneAttrs = Lists.newArrayList();
							if (CollectionUtils.isNotEmpty(copyFromLine.getAttributes())) {
								for (EdcItemSetLineAttr copyFromAttr : copyFromLine.getAttributes()) {
									EdcItemSetLineAttr cloneAttr = (EdcItemSetLineAttr) copyFromAttr.clone();
									cloneAttrs.add(cloneAttr);
								}
							}
							
							// 处理Variables
							List<FormulaVariable> cloneVariables = Lists.newArrayList();
							if (CollectionUtils.isNotEmpty(copyFromLine.getFormulaVariables())) {
								for (FormulaVariable copyFromVariable : copyFromLine.getFormulaVariables()) {
									FormulaVariable cloneVariable = (FormulaVariable) copyFromVariable.clone();
									cloneVariables.add(cloneVariable);
								}
							}
							
							// 处理Subgroup Plan
							List<EdcSubgroupPlan> clonePlans = Lists.newArrayList();
							List<EdcSubgroupPlan> copyFromPlans = edcManager.getSubgroupPlanList(Env.getOrgRrn(),
									copyFromLine.getObjectRrn());
							if (CollectionUtils.isNotEmpty(copyFromPlans)) {
								for (EdcSubgroupPlan copyFromPlan : copyFromPlans) {
									EdcSubgroupPlan clonePlan = (EdcSubgroupPlan) copyFromPlan.clone();
									clonePlans.add(clonePlan);
								}
							}
							
							// 克隆Line
							EdcItemSetLine cloneLine = (EdcItemSetLine) copyFromLine.clone();
							
							// 设置line子信息
							if (CollectionUtils.isNotEmpty(cloneAttrs)) {
								cloneLine.setAttributes(cloneAttrs);
							}
							
							if (CollectionUtils.isNotEmpty(cloneVariables)) {
								cloneLine.setFormulaVariables(cloneVariables);
							}
							
							if (CollectionUtils.isNotEmpty(clonePlans)) {
								cloneLine.setSubgroupPlans(clonePlans);
							}
							
							if (copyFromPlans.size() > 1) {
								// 改变计划状态使其可编辑
								cloneLine.setIsSubgroupPlan(false);
								insertPlanFlag = false;
							}
							
							cloneLines.add(cloneLine);
						}
						
						// 克隆 Set
						EdcItemSet cloneItemSet = (EdcItemSet) copyFromSet.clone();
						cloneItemSet.setItemSetLines(cloneLines);
						cloneItemSet.setVersion(null);
						cloneItemSet.setStatus("");
						setAdObject(cloneItemSet);
						refresh();
						itemSave.setEnabled(true);
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			}
		}
	}
	
	private boolean saveToObject() {
		form.getMessageManager().removeAllMessages();
		if (getAdObject() != null) {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				for (IForm detailForm : getDetailForms()) {
					PropertyUtil.copyProperties(getAdObject(), detailForm
							.getObject(), detailForm.getCopyProperties());
				}
			}
			
			return saveFlag;
		}
		
		return false;
	}

	@Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();
				
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					
					EDCManager edcManager = Framework.getService(EDCManager.class);
					EdcItemSet set=(EdcItemSet)getAdObject();
					if(VersionControl.STATUS_UNFROZNE.equals(set.getStatus()) || StringUtil.isEmpty(set.getStatus())) {
						if(set.getItemSetLines() == null){
							UI.showError(Message.getString("common.line_is_not_null"));// 弹出错误提示框
							return;
						}
						EdcItemSet obj = (EdcItemSet)edcManager.saveEdcSet(set, Env.getSessionContext());
						
						ADManager adManager = Framework.getService(ADManager.class);
						setAdObject(adManager.getEntity(obj));
						//保存之后退出拷贝从状态
						insertPlanFlag = true;
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
						refresh();
						ADBase newBase = getAdObject();
						if (oldBase.getObjectRrn() == null) {
							getMasterParent().refreshAdd(newBase);
						} else {
							getMasterParent().refreshUpdate(newBase);
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public List<EdcSubgroupPlan> getSubGroupPlans(EdcItemSetLine line){
		List<EdcSubgroupPlan> list = null;
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			if(line ==null || line.getObjectRrn()==null){
				return new ArrayList<EdcSubgroupPlan>();
			}
			list = edcManager.getSubgroupPlanList(Env.getSessionContext().getOrgRrn(), 
					line.getObjectRrn());
			return list;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	@Override
	public void selectionChanged(IFormPart part, ISelection selection) {
		insertPlanFlag = true;
		super.selectionChanged(part, selection);
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		if ("EdcItemSetInfo".equals(tab.getName())) {
			return new ItemSetForm(composite, SWT.NONE, tab, mmng);
		}
		return super.getForm(composite, tab);
	}

	@Override
	public void refresh() {
		//如果是拷贝从就不需要在此添加数据
		if (insertPlanFlag){
			if (getAdObject() instanceof EdcItemSet) {
				//如果Set里面没有Line就不需要添加数据
				if(((EdcItemSet)getAdObject()).getItemSetLines()!=null){
					for (EdcItemSetLine line : ((EdcItemSet)getAdObject()).getItemSetLines()) {
						//如果Line里没有样本计划设置，则不需要添加
						if(getSubGroupPlans(line).size()>0){
							line.setSubgroupPlans(getSubGroupPlans(line));
						}
					}
				}
			}
		}
		super.refresh();
	}

	public ItemSetTableSelectField getfItemSetLines() {
		return fItemSetLines;
	}

	public void setfItemSetLines(ItemSetTableSelectField fItemSetLines) {
		this.fItemSetLines = fItemSetLines;
	}
	
	
}