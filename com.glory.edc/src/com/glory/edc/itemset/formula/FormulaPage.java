package com.glory.edc.itemset.formula;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.client.EDCManager;
import com.glory.edc.itemset.SetItemWizard;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.calculation.FormulaVariable;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.google.common.collect.Lists;

public class FormulaPage extends FlowWizardPage implements VerifyListener {

	private static final String FILED_EDC_SET = "edcSetRrn";
	private static final String FIELD_IS_PROCESS_FORMULA = "isProcessFormula";
	private static final String FILED_EDC_ITEM = "itemName";
	
	private String VARIABLE_TABLE_NAME = "EDCItemSetLineFormulaVariable";
	private String LINE_TABLE_NAME = "EDCItemSetAddFormulaLine";

	protected ListTableManager tableManager;
	protected Text txtFormula;

	private EntityForm itemForm;

	private SetItemWizard siw;

	public FormulaPage() {
		super();
	}

	@Override
	public void createControl(Composite parent) {
		siw = (SetItemWizard) this.getWizard();
		
		setTitle(Message.getString("edc.edc_formula_setup"));
		setMessage(Message.getString("edc.edc_formula_setup_message"));
		FormToolkit toolkit = new FFormToolKit(Display.getCurrent());
		configureBody(parent);
		parent.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		
		// Item部分
		Section section = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
		section.setText(Message.getString("edc.edc_formula_item_setup"));
		configureBody(section);
		
		Composite client = toolkit.createComposite(section);
		configureBody(client);
		
		ScrolledForm sForm = toolkit.createScrolledForm(client);
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		ManagedForm managedForm = new ManagedForm(toolkit, sForm);
		Composite body = sForm.getForm().getBody();
		configureBody(body);
		
		itemForm = new EntityForm(body, SWT.NONE, siw.getLine(), getTableByName(LINE_TABLE_NAME), managedForm.getMessageManager());
		configureBody(itemForm);
		section.setClient(client);
		
		// 公式部分
		Section formulaSection = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
		formulaSection.setText(Message.getString("edc.edc_formula"));
		configureBody(formulaSection);
		
		Composite formFormla = toolkit.createComposite(formulaSection);
		configureBody(formFormla);
		formFormla.setLayoutData(new GridData(GridData.FILL_VERTICAL));
		
		createVariableText(toolkit, formFormla);
		createVariableTableViewer(toolkit, formFormla);
		
		Composite formulaTextBody = toolkit.createComposite(formFormla);
		formulaTextBody.setLayout(new GridLayout(13, false));
		GridData gdFormulaText = new GridData(GridData.FILL_BOTH);
		gdFormulaText.heightHint = 180;
		formulaTextBody.setLayoutData(gdFormulaText);
		createFormulaText(toolkit, formulaTextBody);
		
		formulaSection.setClient(formFormla);
	}
	
	private void createVariableTableViewer(FormToolkit toolkit, Composite content) {
		try {
			Composite formulaTextBody = toolkit.createComposite(content);
			formulaTextBody.setLayout(new GridLayout(1, false));
			GridData gdFormulaText = new GridData(GridData.FILL_BOTH);
			gdFormulaText.heightHint = 180;
			formulaTextBody.setLayoutData(gdFormulaText);
			
			tableManager = new ListTableManager(getTableByName(VARIABLE_TABLE_NAME), true);
			tableManager.setInput(siw.getLine().getFormulaVariables());
			tableManager.newViewer(formulaTextBody);
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public void createVariableText(FormToolkit toolkit, Composite form2) {
		ScrolledForm sForm = toolkit.createScrolledForm(form2);
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		ManagedForm managedForm = new ManagedForm(toolkit, sForm);
		Composite body = sForm.getForm().getBody();
		configureBody(body);
		
		EntityForm variableForm = new EntityForm(body, SWT.NONE, new FormulaVariable(), getTableByName(VARIABLE_TABLE_NAME), managedForm.getMessageManager());
		configureBody(variableForm);
		Map<String, IField> fieldMap = variableForm.getFields();
		RefTableField refTableField = (RefTableField) fieldMap.get(FILED_EDC_ITEM);
		if (CollectionUtils.isNotEmpty(siw.getExistlines())) {
			List<EdcItemSetLine> lines = Lists.newArrayList(siw.getExistlines());
			lines.remove(siw.getLine());
			refTableField.setInput(lines);
		} else {
			refTableField.setInput(Lists.newArrayList());
		}
		
		RefTableField setField = (RefTableField) fieldMap.get(FILED_EDC_SET);
		
		EdcItemSetLine line = siw.getLine();
		if (line != null && line.getIsProcessFormula()) {
			setField.setEnabled(true);
		} else {
			setField.setEnabled(false);
		}
		
		setField.addValueChangeListener(new IValueChangeListener() {
			
			@Override
			public void valueChanged(Object sender, Object newValue) {
				if (newValue == null || StringUtil.isEmpty(DBUtil.toString(newValue))) {
					RefTableField refTableField = (RefTableField) fieldMap.get(FILED_EDC_ITEM);
					if (CollectionUtils.isNotEmpty(siw.getExistlines())) {
						List<EdcItemSetLine> lines = Lists.newArrayList(siw.getExistlines());
						lines.remove(siw.getLine());
						refTableField.setInput(lines);
					} else {
						refTableField.setInput(Lists.newArrayList());
					}
				}
			}
		});
		
		BooleanField isProcessFormulaField = (BooleanField) itemForm.getFields().get(FIELD_IS_PROCESS_FORMULA);
		isProcessFormulaField.addValueChangeListener(new IValueChangeListener() {
			
			@Override
			public void valueChanged(Object sender, Object newValue) {
				if (newValue != null) {
					RefTableField setField = (RefTableField) fieldMap.get(FILED_EDC_SET);
					boolean checked = DBUtil.toBoolean(newValue);
					if (checked) {
						setField.setEnabled(true);
					} else {
						setField.setValue(null);
						setField.setEnabled(false);
					}
					
					RefTableField refTableField = (RefTableField) fieldMap.get(FILED_EDC_ITEM);
					if (CollectionUtils.isNotEmpty(siw.getExistlines())) {
						List<EdcItemSetLine> lines = Lists.newArrayList(siw.getExistlines());
						lines.remove(siw.getLine());
						refTableField.setInput(lines);
					} else {
						refTableField.setInput(Lists.newArrayList());
					}
				}
			}
		});
		
		
		Composite buttonBar = toolkit.createComposite(body);
		buttonBar.setLayout(new GridLayout(2, false));
		
		SquareButton addToTable = UIControlsFactory.createButton(buttonBar, "ADD", UIControlsFactory.BUTTON_DEFAULT);
		addToTable.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				try {
					variableForm.removeAllMessages();
					if (variableForm.saveToObject()) {
						FormulaVariable variable = (FormulaVariable) variableForm.getObject();
						
						EdcItemSetLine setLine = (EdcItemSetLine) refTableField.getData();
						// 计算变量号
						variable = (FormulaVariable) variable.clone();
						
						if (StringUtil.isEmpty(variable.getProcessName())) {
							variable.setProcessName(siw.getItemSet().getName());
						}
						
						variable.checkVariable(setLine);
						
						String variableStr = buildVariableString(variable);
						
						int i = 1;
						List<FormulaVariable> formulaVariables = (List<FormulaVariable>) tableManager.getInput();
						for (FormulaVariable oldVariable : formulaVariables) {
							
							if (variableStr.equals(buildVariableString(oldVariable))) {
								UI.showInfo(Message.getString("edc.edc_formula_variable_repeat"));
								return;
							}
							
							oldVariable.setVariableName("V" + i);
							i++;
						}
						
						variable.setVariableName("V" + i);
						tableManager.add(variable);
					}
				} catch (Exception ex) {
					ExceptionHandlerManager.asyncHandleException(ex);
					return;
				}
			}
		});
		
		SquareButton delete = UIControlsFactory.createButton(buttonBar, "DELETE", UIControlsFactory.BUTTON_DEFAULT);
		delete.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				List<Object> os = tableManager.getCheckedObject();
				
				if (CollectionUtils.isNotEmpty(os)) {
					tableManager.removeList(os);
				}
				
				List<FormulaVariable> variables = (List<FormulaVariable>) tableManager.getInput();
				if (variables != null) {
					for (int i = 0; i < variables.size(); i++) {
						int j = i + 1;
						variables.get(i).setVariableName("V" + j);
					}
				}
			}
		});
	}
	
	private ADTable getTableByName(String tableName) {
		try {
		    ADManager entityManager = Framework.getService(ADManager.class);
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), tableName);
			return adTable;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	public void createTextArea(FormToolkit toolkit, Composite formFormla){
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.horizontalSpan = 20;
		txtFormula = toolkit.createText(formFormla,
				!StringUtil.isEmpty(siw.getLine().getFormula()) ? "" : siw.getLine().getFormula(),
				SWT.WRAP | SWT.MULTI | SWT.V_SCROLL);
		
		txtFormula.setLayoutData(gd);
		txtFormula.setText(DBUtil.toString(siw.getLine().getFormula()));
	}
	
	public void createFormulaText(FormToolkit toolkit, Composite formFormla) {
		
		toolkit.createLabel(formFormla, "Function");
		
		XCombo combo = RCPUtil.getSysRefListCombo(formFormla, "EDCFormulaOperators", Env.getOrgRrn());
		GridData gd = new GridData(GridData.FILL);
		gd.widthHint = 200;
		combo.setLayoutData(gd);
		
		Button button = toolkit.createButton(formFormla, "ADD", SWT.BUTTON3);
		
		toolkit.createLabel(formFormla, "  ");
		Button btnLeftBracket = toolkit.createButton(formFormla, "(", SWT.NONE);
		Button btnRightBracket = toolkit
				.createButton(formFormla, ")", SWT.NONE);

		toolkit.createLabel(formFormla, "  ");
		Button btnAdd = toolkit.createButton(formFormla, "+", SWT.NONE);
		Button btnSub = toolkit.createButton(formFormla, "-", SWT.NONE);
		Button btnMul = toolkit.createButton(formFormla, "*", SWT.NONE);
		Button btnDiv = toolkit.createButton(formFormla, "/", SWT.NONE);

		toolkit.createLabel(formFormla, "  ");
		Button btnClear = toolkit.createButton(formFormla, "CLEAR", SWT.NONE);
		
		createTextArea(toolkit, formFormla);
		
		button.addSelectionListener(new SelectionListener() {
			
			@Override
			public void widgetSelected(SelectionEvent e) {
				String text = combo.getText();
				if (StringUtil.isEmpty(text)) {
					return;
				}
				
				if (text.equals("VAVG")) {
					txtFormula.setText(txtFormula.getText() + "VAVG()");
				} else if (text.equals("VMEDIAN")) {
					txtFormula.setText(txtFormula.getText() + "VMEDIAN()");
				} else if (text.equals("VRANGE")) {
					txtFormula.setText(txtFormula.getText() + "VRANGE()");
				} else if (text.equals("VSIGMA")) {
					txtFormula.setText(txtFormula.getText() + "VSIGMA()");
				} else if (text.equals("VSUM")) {
					txtFormula.setText(txtFormula.getText() + "VSUM()");
				} else if (text.equals("VMIN")) {
					txtFormula.setText(txtFormula.getText() + "VMIN()");
				} else if (text.equals("VMAX")) {
					txtFormula.setText(txtFormula.getText() + "VMAX()");
				} else if (text.equals("MIN")) {
					txtFormula.setText(txtFormula.getText() + "MIN( , )");
				} else if (text.equals("MAX")) {
					txtFormula.setText(txtFormula.getText() + "MAX( , )");
				} else if (text.equals("OOSNUM")) {
					txtFormula.setText(txtFormula.getText() + "OOSNUM()");
				}
			}
			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
				
			}
		});
		
		btnClear.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText("");
			}
		});

		btnLeftBracket.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + "(");
			}
		});

		btnRightBracket.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + ")");
			}
		});

		btnMul.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + "*");
			}
		});

		btnDiv.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + "/");
			}
		});

		btnAdd.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + "+");
			}
		});

		btnSub.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				txtFormula.setText(txtFormula.getText() + "-");
			}
		});
	}

	public Text getTxtFormula() {
		return txtFormula;
	}

	public void setTxtFormula(Text txtFormula) {
		this.txtFormula = txtFormula;
	}
	
	@Override
	public void verifyText(VerifyEvent e) {
	}

	@Override
	public String doNext() {
		itemForm.removeAllMessages();
		
		if (!itemForm.saveToObject()) {
			return "";
		}
		
		EdcItemSetLine line = (EdcItemSetLine) itemForm.getObject();
		
		if (!isSpecValidate(line)) {
			UI.showInfo(Message.getString("edc.edc_spec_limits_illegal"));
			return "";
		}
		
		List<FormulaVariable> formulaVariables = (List<FormulaVariable>) Lists.newArrayList(tableManager.getInput());
		
		// 流程公式检查
		if (line.getIsProcessFormula()) {
			// 流程公式中必须要关联当前采集项集中的一个采集项
			boolean failed = true;
			String edcSetName = siw.getItemSet().getName();
			for (FormulaVariable variable : formulaVariables) {
				if (edcSetName.equals(variable.getProcessName())) {
					failed = false;
					break;
				}
			}
			
			if (failed) {
				UI.showInfo(Message.getString("edc.formula_process_associated_current"));
				return "";
			}
			
		} else {
			// 仅有在流程公式中可以存在多个数据采集项集
			boolean failed = false;
			String edcSetName = siw.getItemSet().getName();
			for (FormulaVariable variable : formulaVariables) {
				if (!edcSetName.equals(variable.getProcessName())) {
					failed = true;
					break;
				}
			}
			
			if (failed) {
				UI.showInfo(Message.getString("edc.formula_process_muilt_set"));
				return "";
			}
		}
		
		line.setFormulaVariables(formulaVariables);
		line.setIsFormula(true);
		line.setFormula(txtFormula.getText().trim());
		
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			edcManager.checkFormula(line);
		}catch (ClientException ce) {
			UI.showError(Message.getString(ce.getErrorCode()));
			return "";
		}catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return "";
		}
		
		line.setDataType(EdcItemSetLine.DATA_TYPE_FORMULA);
		
		siw.setLine(line);
		return getDefaultDirect();
	}
	
	
	private boolean isSpecValidate(EdcItemSetLine setLine) {
		String usl = setLine.getUslString();
		String sl = setLine.getSlString();
		String lsl = setLine.getLslString();

		try {
			if (!"".equals(usl))
				Double.parseDouble(usl);
			if (!"".equals(sl))
				Double.parseDouble(sl);
			if (!"".equals(lsl))
				Double.parseDouble(lsl);
		} catch (Exception e) {
			return true;
		}
		if (isTxtEmpty(usl, sl, lsl) == 2) {
			if (!"".equals(usl) && !"".equals(sl)) {
				if (Double.parseDouble(usl) > Double.parseDouble(sl))
					return true;
				UI.showError(Message.getString("edc.usl_sl"));
			}

			if (!"".equals(usl) && !"".equals(lsl)) {
				if (Double.parseDouble(usl) > Double.parseDouble(lsl))
					return true;
				UI.showError(Message.getString("edc.usl_lsl"));
			}

			if (!"".equals(sl) && !"".equals(lsl)) {
				if (Double.parseDouble(sl) > Double.parseDouble(lsl))
					return true;
				UI.showError(Message.getString("edc.sl_lsl"));
			}
		}
		if (isTxtEmpty(usl, sl, lsl) == 3) {
			if (Double.parseDouble(usl) > Double.parseDouble(sl) && Double.parseDouble(usl) > Double.parseDouble(lsl)
					&& Double.parseDouble(sl) > Double.parseDouble(lsl))
				return true;
			UI.showError(Message.getString("edc.usl_sl_lsl"));
		}
		if (isTxtEmpty(usl, sl, lsl) < 2) {
			if (!"".equals(usl)) {
				Double.parseDouble(usl);
			}
			if (!"".equals(sl)) {
				Double.parseDouble(sl);
			}
			if (!"".equals(lsl)) {
				Double.parseDouble(lsl);
			}
			return true;
		}
		return false;
	}

	private int isTxtEmpty(String usl, String sl, String lsl) {
		int result = 0;
		try {

			if (!"".equals(usl.trim())) {
				result = result + 1;
			}
			if (!"".equals(sl.trim())) {
				result = result + 1;
			}
			if (!"".equals(lsl.trim())) {
				result = result + 1;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	
	private String buildVariableString(FormulaVariable variable) {
		return variable.getProcessName() + variable.getItemName() + variable.getItemStr() + variable.getCompStr() + variable.getSiteStr();
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
