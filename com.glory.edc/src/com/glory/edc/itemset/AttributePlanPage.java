package com.glory.edc.itemset;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.edc.itemset.plan.AttributePlanDialog;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSubgroupPlan;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;

/**
 * 当采集项的数据类型为“Attribute”时，进入该页面
 * 
 * <AUTHOR>
 * @since 2011年9月22日 15:08:03 @
 * 
 */
public class AttributePlanPage extends FlowWizardPage implements VerifyListener {

	protected FormToolkit toolkit;
	protected Text textNumber, textPercent, textSampleSize, textSubgroupSize,
			textArea;
	protected Label labDefectCode, labSampleSize, labSubgroupSize, labSpecType;
	protected Button btnNumber, btnPercent, btnSpecNumber, btnSpecPercent;
	protected XCombo comboDefectCode;
	protected Combo comboSpecType;
	protected SetItemWizard sw;
	protected SquareButton plan, cancelPlan;
	protected Composite form;

	public AttributePlanPage() {
		super();
	}

	public AttributePlanPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	/*
	 * 点击“下一步”，执行的代码
	 */
	@Override
	public String doNext() {
		EdcItemSetLine line = ((SetItemWizard) this.getWizard()).getLine();

		if (btnSpecNumber.getSelection() == true) {
			line.setSpecType(EdcItemSetLine.SPECTYPE_NUMBER);
		} else {
			line.setSpecType(EdcItemSetLine.SPECTYPE_PERCENT);
		}

		if ((!"".equals(textNumber.getText()))
				|| (!"".equals(textPercent.getText()))) {
			if (!"".equals(textPercent.getText())) {
				if (Float.parseFloat(textPercent.getText().trim()) <= 100) {
					line.setSampleLevel(EdcItemSetLine.SAMPLELEVEL_PERCENT);
				} else {
					UI.showError(Message
							.getString("edc.edcset_attribute_pecent")); // 百分比不能大于100！
					return null;
				}
			} else {
				line.setSampleLevel(EdcItemSetLine.SAMPLELEVEL_NUMBER);
			}
			line.setDefectCodeSrc(comboDefectCode.getText());
			line.setSampleSize(Long.parseLong(textSampleSize.getText())); // 把SampleSize存入数据库，在数据收集里用到
			line.setSubgroupSize(new BigDecimal(textSubgroupSize.getText()));
			((SetItemWizard) this.getWizard()).setLine(line);
			return getDefaultDirect();
		} else {
			UI.showError(Message.getString("edc.edcset_attribute_haveto")); // 固定采样数与百分比不能同时为空！
			return null;
		}
	}

	/*
	 * 点击“上一步”，执行的代码
	 */
	@Override
	public String doPrevious() {
		return "basicInfo";
	}

	/*
	 * 创建页面的内容
	 */
	@Override
	public void createControl(Composite parent) {
		toolkit = new FormToolkit(parent.getDisplay());
		// 设置统一风格
		Composite composite = toolkit.createComposite(parent);
		GridLayout layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		composite.setLayoutData(gd);
		sw = (SetItemWizard) this.getWizard();
		// 当双击数据项时，表示进入修改页面
		if (SetItemWizard.ACTION_TYPE_EDIT.equals(sw.getActionType())) {
			// 设置页面上部分
			aboveArea(composite);

			if (sw.getLine().getDefectCodeSrc() == null) {
				comboDefectCode.setText("");
			} else {
				comboDefectCode.setText(sw.getLine().getDefectCodeSrc().trim());
			}
			if (sw.getLine().getSampleLevel().equals(
					EdcItemSetLine.SAMPLELEVEL_NUMBER)) {
				btnNumber.setSelection(true);
				textNumber.setText(sw.getLine().getSubgroupSize().toString());
				textPercent.setEnabled(false);
			} else {
				btnPercent.setSelection(true);
				textPercent.setText(sw.getLine().getSubgroupSize().toString());
				textNumber.setEnabled(false);
			}
			if (sw.getLine().getSpecType().equals(
					EdcItemSetLine.SPECTYPE_NUMBER)) {
				btnSpecNumber.setSelection(true);
			} else {
				btnSpecPercent.setSelection(true);
			}
			// doSamplePlan(form);
			// 页面下部分
			followArea(composite);
			textSubgroupSize.setText(sw.getLine().getSubgroupSize().toString());
			// 当单击“添加”按钮时，表示进入添加页面
		} else {
			// 设置页面上部分
			aboveArea(composite);
			btnSpecNumber.setSelection(true);
			btnNumber.setSelection(true);
			// doSamplePlan(form);
			// 页面下部分
			followArea(composite);
		}
		loadListeners();
	}

	protected void aboveArea(Composite composite) {
		form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(4, false);
		form.setLayout(flayout);
		GridData gdForm = new GridData(GridData.FILL_BOTH);
		gdForm.verticalSpan = 3;
		form.setLayoutData(gdForm);

		Group groupSpec = new Group(form, SWT.NULL);
		GridData gdGroupSpec = new GridData(GridData.FILL_HORIZONTAL);
		gdGroupSpec.horizontalSpan = 4;
		groupSpec.setLayoutData(gdGroupSpec);
		groupSpec.setText(Message.getString("edc.edcset_attribute_spectype")); // 规范类型
		groupSpec.setLayout(new GridLayout(2, false));

		btnSpecNumber = new Button(groupSpec, SWT.RADIO);
		btnSpecNumber.setText("NUMBER");
		btnSpecPercent = new Button(groupSpec, SWT.RADIO);
		btnSpecPercent.setText("PERCENT");

		btnNumber = toolkit.createButton(form, Message
				.getString("edc.edcset_attribute_number"), SWT.RADIO); // 固定采样数
		textNumber = toolkit.createText(form, "", SWT.BORDER);
		GridData gdFix = new GridData(GridData.FILL_HORIZONTAL);
		gdFix.horizontalSpan = 3;
		textNumber.setLayoutData(gdFix);
		textNumber.addVerifyListener(this);

		btnPercent = toolkit.createButton(form, Message
				.getString("edc.edcset_attribute_percent"), SWT.RADIO); // 百分比(%)
		textPercent = toolkit.createText(form, "", SWT.BORDER);
		GridData gdPercent = new GridData(GridData.FILL_HORIZONTAL);
		gdPercent.horizontalSpan = 3;
		textPercent.setLayoutData(gdPercent);
		textPercent.addVerifyListener(this);
		textPercent.setEnabled(false);

		labDefectCode = toolkit.createLabel(form, Message
				.getString("edc.edcset_attribute_defectcode")); // 缺陷码
		comboDefectCode = getUserRefListCombo(form, Env.getOrgRrn());
		GridData gdDefectCode = new GridData(GridData.FILL_HORIZONTAL);
		gdDefectCode.horizontalSpan = 3;
		comboDefectCode.setLayoutData(gdDefectCode);

	}

	protected void followArea(Composite composite) {
		Group group = new Group(composite, SWT.NULL);
		group.setLayout(new GridLayout(4, false));
		group.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		labSampleSize = toolkit.createLabel(group, "SampleSize");
//		labSampleSize.setBackground(new Color(Display.getCurrent(), 236, 233,
//				216));
		textSampleSize = toolkit.createText(group, "1", SWT.READ_ONLY);
//		textSampleSize.setBackground(new Color(Display.getCurrent(), 236, 233,
//				216));
		textSampleSize.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		labSubgroupSize = toolkit.createLabel(group, "SubgroupSize");
//		labSubgroupSize.setBackground(new Color(Display.getCurrent(), 236, 233,
//				216));
		textSubgroupSize = toolkit.createText(group, "", SWT.READ_ONLY);
		textSubgroupSize.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//		textSubgroupSize.setBackground(new Color(Display.getCurrent(), 236,
//				233, 216));
		setControl(composite);
		setTitle(Message.getString("edc.edcset_attribute_title")); // 设置Attribute类型的相关信息
		setMessage(Message.getString("edc.edcset_attribute_title_message"));
	}

	protected void loadListeners() {
		// 把没选中的单选框对应的Text设为不可用
		btnNumber.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				textNumber.setEnabled(true);
				textNumber.setFocus();
				textPercent.setText("");
				textPercent.setEnabled(false);
			}
		});
		btnPercent.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				textPercent.setEnabled(true);
				textPercent.setFocus();
				textNumber.setText("");
				textNumber.setEnabled(false);
			}
		});

		// 验证输入的是否是数字
		textNumber.addFocusListener(new FocusListener() {
			@Override
			public void focusGained(FocusEvent e) {

			}

			@Override
			public void focusLost(FocusEvent e) {
				try {
					if (!"".equals(textNumber.getText())) {
						Integer.parseInt(textNumber.getText().trim());
						textSubgroupSize.setText(textNumber.getText());
					}
				} catch (NumberFormatException ex) {
					UI.showError(Message
							.getString("edc.edcset_attribute_format"));
					return;
				}
			}
		});
		textPercent.addFocusListener(new FocusListener() {
			@Override
			public void focusGained(FocusEvent e) {
			}

			@Override
			public void focusLost(FocusEvent e) {
				try {
					if (!"".equals(textPercent.getText())) {
						Float.parseFloat(textPercent.getText().trim());
						textSubgroupSize.setText(textPercent.getText());
					}
				} catch (NumberFormatException ex) {
					UI.showError(Message
							.getString("edc.edcset_attribute_format"));
					return;
				}
			}
		});
	}

	protected void doSamplePlan(Composite form) {
		Group group2 = new Group(form, SWT.NULL);
		group2.setLayout(new GridLayout(10, false));
		group2.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		group2.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		GridData gd2 = new GridData(GridData.FILL_HORIZONTAL);
		group2.setLayoutData(gd2);

		toolkit.createLabel(group2, Message
				.getString("edc.sample_plan_lab_plan"));

		gd2.horizontalSpan = 7;
		textArea = toolkit.createText(group2, "", SWT.BORDER);
		textArea.setEditable(false);
		textArea.setLayoutData(gd2);

		if (sw.getLine().getSubgroupPlans() != null) {
			textArea.setText(sw.getLine().buildSubgroupPlan());
		}

		plan = UIControlsFactory.createButton(group2, Message
				.getString("edc.sample_plan_btn"), UIControlsFactory.BUTTON_DEFAULT);
		cancelPlan = UIControlsFactory.createButton(group2, Message
				.getString("edc.sample_plan_btn_cancel"), UIControlsFactory.BUTTON_DEFAULT);
		decorateButton(plan);
		decorateButton(cancelPlan);
		plan.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				widgetDefaultSelected(e);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				String value = btnNumber.getSelection() == true ? textNumber
						.getText() : textPercent.getText();
				if ("".equals(value.trim())) {
					UI.showInfo(Message
							.getString("edc.sample_plan_keyin_default"));
					return;
				}
				String param[] = new String[] {
						value,
						btnNumber.getSelection() == true ? EdcSubgroupPlan.RULETYPE_NUMBER
								: EdcSubgroupPlan.RULETYPE_PERCENT,
						textSampleSize.getText(), textSubgroupSize.getText() };
				AttributePlanDialog dialog = new AttributePlanDialog(UI
						.getActiveShell(), sw.getLine(), param);
				if (dialog.open() == IDialogConstants.OK_ID) {
					if (sw.getLine().getSubgroupPlans() != null) {
						textArea.setText(sw.getLine().buildSubgroupPlan());
						refresh();
					}
				}
			}
		});

		cancelPlan.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				widgetDefaultSelected(e);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				if (sw.getLine().getSubgroupPlans() != null) {
					if (UI.showConfirm(Message
							.getString("edc.sample_plan_lab_cancel"))) {
						if(sw.getLine().getIsSubgroupPlan()){
							plan.setEnabled(false);
						}
						sw.getLine().setSubgroupPlans(
								new ArrayList<EdcSubgroupPlan>());
						textArea.setText("");
						refresh();
					}
				} else {
					UI.showInfo(Message.getString("edc.sample_plan_no_plan"));
					return;
				}
			}
		});
	}

	protected void decorateButton(SquareButton button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		int widthHint = 60;  //IDialogConstants.BUTTON_WIDTH
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}

	/*
	 * 创建缺陷码的下拉框
	 */
	public static XCombo getUserRefListCombo(Composite parent, long orgRrn) {
		try {
			FormToolkit toolkit = new FormToolkit(Display.getCurrent()
					.getActiveShell().getDisplay());
			XCombo combo;
			ADManager adManager = (ADManager) Framework
					.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(),
					"ADVURefListName");
			ListTableManager tableManager = new ListTableManager(adTable);
			List<?> list = adManager.getEntityList(orgRrn, adTable
					.getObjectRrn().longValue(), Env.getMaxResult());
			tableManager.setInput(list);
			combo = new XCombo(parent, tableManager, "name",
					"name", 2048, true);
			toolkit.adapt(combo);
			toolkit.paintBordersFor(combo);
			return combo;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/*
	 * 点击“下一步”，完成
	 */
	public String getDefaultDirect() {
		return FINISH;
	};

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

	@Override
	public void verifyText(VerifyEvent e) {
		e.doit = false;
		char myChar = e.character;
		Text tData = (Text) e.widget;
		if (Character.isSpaceChar(myChar) || Character.isDigit(myChar)
				|| myChar == SWT.BS || (int) myChar == 46 || (int) myChar == 45
				|| "".equals(e.text) || (int) myChar == 0 || (int) myChar == 8) {
			if (tData.getText() == null || "".equals(e.text)
					|| (int) myChar == 0 || (int) myChar == 8
					|| (tData.getText().indexOf(".") < 0 && (int) myChar != 45)
					|| ((int) myChar != 46 && (int) myChar != 45)
					|| ((int) myChar == 45 && tData.getText().indexOf("-") < 0)) {
				e.doit = true;
				return;
			}
		}
	}
}
