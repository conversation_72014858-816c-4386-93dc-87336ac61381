package com.glory.edc.itemset;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.itemset.plan.SamplePlanCompDialog;
import com.glory.edc.itemset.plan.SelectComponentDialog;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcItemSetLineAttr;
import com.glory.edc.model.EdcSubgroupPlan;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.ExtendTableForm;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.core.exception.ExceptionBundle;

public class SamplePlanCompPage extends FlowWizardPage implements VerifyListener {

	private static String Comp_SampleType = "COMP";
	public static String TALBENAME_EDCSET_ATTRIBUTE = "EDCSetAttributeForCompAndSite";
	protected FormToolkit toolkit;
	protected Text textItem,textItemDesc,textComp,textSampleSize,textCompDesc,textSubgroupSize,textArea,selectCompTextArea;
	protected Label labItem,labItemDesc,labSampleSize,labSubgroupSize;
	protected SetItemWizard sw;
	protected ExtendTableForm attributeForm;
	protected List<EdcItemSetLineAttr> attributes = new ArrayList<EdcItemSetLineAttr>();
	protected SquareButton plan,cancelPlan;
	protected Composite form ;
	
	public SamplePlanCompPage() {
		super();
	}
	
	public SamplePlanCompPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public String doNext() {
		if(!"".equals(textItem.getText())&&!"".equals(textComp.getText())){
			if (selectCompTextArea.getText() != null && !"".equals(selectCompTextArea.getText())) {
				if (Long.parseLong(textItem.getText()) != selectCompTextArea.getText().split(",").length) {
					UI.showError(Message.getString("edc.itemset_selectcomp_number_error"));
					return null;
				}
			}
			EdcItemSetLine line = ((SetItemWizard)this.getWizard()).getLine();
			line.setItem(Long.parseLong(textItem.getText()));
			line.setItemDesc(textItemDesc.getText());
			line.setComp(Long.parseLong(textComp.getText()));
			line.setCompDesc(textCompDesc.getText());
			line.setSampleType(Comp_SampleType);
			line.setSampleSize(Long.parseLong(textSampleSize.getText()));
			line.setSubgroupSize(new BigDecimal(textSubgroupSize.getText()));
			if (attributeForm != null) {
				attributes = (List<EdcItemSetLineAttr>)attributeForm.getObject();
				if (attributes != null && attributes.size() > 0) {
					int m = 0;
					for (IField f : attributeForm.getFields().values()){					
						if (!(f instanceof SeparatorField)){
							int n = m/6;
							if (!f.getId().substring(0, f.getId().length()-1).equals("chk")) {
								PropertyUtil.setProperty(attributes.get(n), f.getId().substring(0, f.getId().length()-1), f.getValue());
								attributes.get(n).setOrgRrn(Env.getOrgRrn());
								attributes.get(n).setSeqNo((n+1) * 10L);
							}
							m++;
						}
					}
					for (int i=0; i<attributes.size(); i++) {
						for (int j=0; j<attributes.size(); j++) {
							if (i!=j && attributes.get(i).getAttributeName().equals(attributes.get(j).getAttributeName())) {
								UI.showError(Message.getString("edc.itemset_attribute_error"));
								return null;
							}
						}
					}
					line.setAttributes(attributes);
				}
			}
			
			((SetItemWizard)this.getWizard()).setLine(line);
			
			return getDefaultDirect();
		} else if("".equals(textItem.getText())){
			UI.showError(Message.getString("spc.Item_must_input"));
			return null;
		} else if ("".equals(textComp.getText())){
			UI.showError(Message.getString("spc.Comp_must_input"));
			return null;
		}
		return null;
	}

	@Override
	public String doPrevious() {
		return "basicInfo";
	}

	@Override
	public void createControl(Composite parent) {
		setTitle(Message.getString("edc.edcset_sampleplan"));
		setMessage(Message.getString("edc.edcset_sampleplan_message"));
		toolkit = new FFormToolKit(parent.getDisplay());
		//设置统一风格
		Composite composite = toolkit.createComposite(parent);
		GridLayout layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		composite.setLayoutData(gd);
		composite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));

		sw = (SetItemWizard) this.getWizard();
		if (SetItemWizard.ACTION_TYPE_EDIT.equals(sw.getActionType())){
			//设置页面上部分
			aboveArea(composite);
			textItem.setText(sw.getLine().getItem()==null?
					"":sw.getLine().getItem().toString());
			textItemDesc.setText(sw.getLine().getItemDesc()==null?
					"":sw.getLine().getItemDesc());
			textComp.setText(sw.getLine().getComp().toString()==null?
					"":sw.getLine().getComp().toString());
			textCompDesc.setText(sw.getLine().getCompDesc()==null?
					"":sw.getLine().getCompDesc());
			
			selectCompArea(form);
			//doSamplePlan(form);
			
			//中间部分(ComponentAttribute部分)
			//目前先暂时不支持(Tony)
			//middleArea(layout);
			//中间部分
			
			//页面下部分
			followArea(composite);
			textSubgroupSize.setText(sw.getLine().getSubgroupSize()==null?
					"":sw.getLine().getSubgroupSize().toString());
		} else {
			//设置页面上部分
			aboveArea(composite);
			
			selectCompArea(form);
			//doSamplePlan(form);
			
			//中间部分(ComponentAttribute部分)
			//目前先暂时不支持(Tony)
			//middleArea(layout);
			//中间部分
			//页面下部分
			followArea(composite);
		}
		loadListeners();
	}

	protected void aboveArea(Composite composite){
		form = toolkit.createComposite(composite);
		GridLayout flayout = new GridLayout(4,false);
		form.setLayout(flayout);
		GridData gdForm =new GridData(GridData.FILL_BOTH);
		form.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));

		gdForm.verticalSpan=3;
		form.setLayoutData(gdForm);
		labItem = toolkit.createLabel(form, "Item*");
		labItem.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		
		textItem = toolkit.createText(form, "", SWT.BORDER);
		GridData gdTtem =new GridData(GridData.FILL_HORIZONTAL); 
		gdTtem.widthHint = 100;
		textItem.setLayoutData(gdTtem);
		textItem.setText("1");
		textItem.addVerifyListener(this);
		Label lblComp = toolkit.createLabel(form, "Comp*");
		lblComp.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));

		textComp = toolkit.createText(form, "", SWT.BORDER);
		GridData gdTextComp = new GridData(GridData.FILL_HORIZONTAL); 
		gdTextComp.widthHint = 100;
		textComp.setLayoutData(gdTextComp);
		textComp.setFocus();
		textComp.addVerifyListener(this);
		labItemDesc = toolkit.createLabel(form, "ItemDesc");
		labItemDesc.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		textItemDesc = toolkit.createText(form, "", SWT.BORDER);
		textItemDesc.setLayoutData(gdTextComp);
		
		Label lblCompDesc = toolkit.createLabel(form, "CompDesc");
		lblCompDesc.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));

		textCompDesc = toolkit.createText(form, "", SWT.BORDER);
		textCompDesc.setLayoutData(gdTextComp);
		
	}
	
	protected void middleArea(GridLayout layout){
		try {
			Section section = toolkit.createSection(form, Section.NO_TITLE | FFormSection.FFORM);
			section.setText(Message.getString("edc.itemset_attribute"));
			section.setLayout(layout);
			GridData s = new GridData(GridData.FILL_HORIZONTAL);
			s.horizontalSpan = 6;
			section.setLayoutData(s);
			Composite comp = toolkit.createComposite(section);
			GridData gdf = new GridData(GridData.FILL_BOTH);
			comp.setLayout(layout);
			comp.setLayoutData(gdf);
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TALBENAME_EDCSET_ATTRIBUTE);
			if(SetItemWizard.ACTION_TYPE_EDIT.equals(sw.getActionType())){
				EdcItemSetLine line = sw.getLine();
				
				line = (EdcItemSetLine) adManager.getEntity(line);
				attributes = line.getAttributes();	
				attributeForm = new ExtendTableForm(comp, SWT.NONE, true, table, attributes, 6, null);
				attributeForm.setObject(attributes);
				attributeForm.reflow();
				attributeForm.loadFromObject();			
			}else{
				attributeForm = new ExtendTableForm(comp, SWT.NONE, true, table, attributes, 6, null);
			}
			Composite bottom = toolkit.createComposite(comp, SWT.NONE);
			bottom.setLayout(new GridLayout(2, false));
			bottom.setLayoutData(new GridData(GridData.HORIZONTAL_ALIGN_END));
			
			gdf = new GridData(GridData.FILL_BOTH);
			gdf.heightHint = 170;
			attributeForm.setLayoutData(gdf);
			
			SquareButton add = UIControlsFactory.createButton(bottom, Message
					.getString("wip.step_add"), UIControlsFactory.BUTTON_DEFAULT);
			SquareButton delete = UIControlsFactory.createButton(bottom, Message
					.getString("wip.step_delete"), UIControlsFactory.BUTTON_DEFAULT);
			add.addSelectionListener(new SelectionListener() {
	            public void widgetSelected(SelectionEvent e) {
	            	attributeForm.addLine();
	            }
	            public void widgetDefaultSelected(SelectionEvent e) {
	                widgetSelected(e);
	            }
	    	});
			delete.addSelectionListener(new SelectionListener() {
	            public void widgetSelected(SelectionEvent e) {
	            	attributeForm.removeLine();
	            }
	            public void widgetDefaultSelected(SelectionEvent e) {
	                widgetSelected(e);
	            }
	    	});
			section.setClient(comp);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void followArea(Composite composite){
		Group group = new Group(composite, SWT.NULL);
		group.setLayout(new GridLayout(4, false));
		group.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		labSampleSize=toolkit.createLabel(group, "SampleSize");
		labSampleSize.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		textSampleSize = toolkit.createText(group, "1", SWT.READ_ONLY);
		textSampleSize.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		textSampleSize.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		labSubgroupSize=toolkit.createLabel(group, "SubgroupSize");	
		labSubgroupSize.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		textSubgroupSize = toolkit.createText(group, "", SWT.READ_ONLY);
		textSubgroupSize.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		textSubgroupSize.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		setControl(composite);
	}
	
	protected void selectCompArea(Composite form) {
		Group group = new Group(form, SWT.NULL);
		group.setLayout(new GridLayout(10, false));
		group.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		group.setBackground(new Color(Display.getCurrent(), 255,255,255));
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		group.setLayoutData(gd);
		group.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		
		Label lblPosition = toolkit.createLabel(group, Message.getString("edc.itemset_comp_slot_src"));
		lblPosition.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));
		
		gd.horizontalSpan = 7;
		selectCompTextArea = toolkit.createText(group, sw.getLine().getCompSLotSrc(), SWT.BORDER);
		selectCompTextArea.setEditable(false);
		selectCompTextArea.setLayoutData(gd);
		
    	SquareButton select = UIControlsFactory.createButton(group, 
    			Message.getString(ExceptionBundle.bundle.CommonSelect()), UIControlsFactory.BUTTON_DEFAULT);
    	SquareButton clear = UIControlsFactory.createButton(group, 
    			Message.getString(ExceptionBundle.bundle.CommonClear()), UIControlsFactory.BUTTON_DEFAULT);
    	decorateButton(select);
    	decorateButton(clear);
    	
    	select.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				widgetDefaultSelected(e);
			}
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				sw.getLine().setCompSLotSrc(selectCompTextArea.getText());
				
				SelectComponentDialog dialog = new SelectComponentDialog(UI.getActiveShell(), sw.getLine());
				if (dialog.open() == IDialogConstants.OK_ID) {
					sw.getLine().setCompSLotSrc(dialog.getSlots());
					selectCompTextArea.setText(dialog.getSlots());
				}
			}
		});
    	
    	clear.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				widgetDefaultSelected(e);
			}
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				selectCompTextArea.setText("");
			}
		});
	}
	
	protected void doSamplePlan(Composite form){
		Group group2 = new Group(form, SWT.NULL);
		group2.setLayout(new GridLayout(10, false));
		group2.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		GridData gd2 = new GridData(GridData.FILL_HORIZONTAL);
		group2.setLayoutData(gd2);
		
		Label lblPlan = toolkit.createLabel(group2, Message.getString("edc.sample_plan_lab_plan"));
		lblPlan.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_CONTENT_BG));

		gd2.horizontalSpan = 7;
		textArea = toolkit.createText(group2, "", SWT.BORDER);
		textArea.setEditable(false);
		textArea.setLayoutData(gd2);
		
		if(sw.getLine().getSubgroupPlans()!=null){
			textArea.setText(sw.getLine().buildSubgroupPlan());
		}
		
		plan = UIControlsFactory.createButton(group2, Message
				.getString("edc.sample_plan_btn"), UIControlsFactory.BUTTON_DEFAULT);
		cancelPlan = UIControlsFactory.createButton(group2, Message
				.getString("edc.sample_plan_btn_cancel"), UIControlsFactory.BUTTON_DEFAULT);
		decorateButton(plan);
    	decorateButton(cancelPlan);
    	
    	plan.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				widgetDefaultSelected(e);
			}
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				if("".equals(textItem.getText().trim())||
						"".equals(textComp.getText().trim())){
					UI.showInfo(Message.getString("edc.sample_plan_keyin_default"));
					return ;
				}
				String param [] = new String []{
						textItem.getText(),
						textComp.getText(),
						textItemDesc.getText(),
						textCompDesc.getText(),
						textSampleSize.getText(),
						textSubgroupSize.getText()
				};
				SamplePlanCompDialog dialog = new SamplePlanCompDialog(
						UI.getActiveShell(),param,sw.getLine());
				if(dialog.open()==IDialogConstants.OK_ID){
					if(sw.getLine().getSubgroupPlans()!=null){
						textArea.setText(sw.getLine().buildSubgroupPlan());
						refresh();
					}
				}
			}
		});
    	
    	cancelPlan.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				widgetDefaultSelected(e);
			}
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				if(sw.getLine().getSubgroupPlans()!=null){
					if(UI.showConfirm(Message.getString("edc.sample_plan_lab_cancel"))){
						if(sw.getLine().getIsSubgroupPlan()){
							plan.setEnabled(false);
						}
						sw.getLine().setSubgroupPlans(new ArrayList<EdcSubgroupPlan>());
						textArea.setText("");
						refresh();
					}
				}else{
					UI.showInfo(Message.getString("edc.sample_plan_no_plan"));
					return;
				}
			}
		});
	}
	
	protected void loadListeners(){
		textItem.addFocusListener(new FocusListener() {	
			@Override
			public void focusGained(FocusEvent e) {
				
			}
			@Override
			public void focusLost(FocusEvent e) {
					try {
						if(!"".equals(textItem.getText())&&!"".equals(textComp.getText())){
						 textSubgroupSize.setText(String.valueOf(
								  Integer.parseInt(textItem.getText())
					                 *Integer.parseInt(textComp.getText())));
						}else if(!"".equals(textItem.getText())){
							 Integer.parseInt(textItem.getText());
						}else if(!"".equals(textComp.getText())){
							Integer.parseInt(textComp.getText());
						}
					} catch (NumberFormatException ex) {
						textItem.setText("");
						textSubgroupSize.setText("");
						UI.showError(Message.getString("edc.validate.message_double"));
					}
							
			}
		});
		
		textComp.addFocusListener(new FocusListener() {	
			@Override
			public void focusGained(FocusEvent e) {
				
			}
			@Override
			public void focusLost(FocusEvent e) {
					try {
						if(!"".equals(textItem.getText())&&!"".equals(textComp.getText())){
						 textSubgroupSize.setText(String.valueOf(
								  Integer.parseInt(textItem.getText())
					                 *Integer.parseInt(textComp.getText())));
						}else if(!"".equals(textItem.getText())){
							 Integer.parseInt(textItem.getText());
						}else if(!"".equals(textComp.getText())){
							Integer.parseInt(textComp.getText());
						}
					} catch (NumberFormatException ex) {
						textComp.setText("");
						textSubgroupSize.setText("");
						UI.showError(Message.getString("edc.validate.message_double"));
					}
							
			}
		});
	}
	
	public void decorateButton(SquareButton button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		int widthHint = 60;  //IDialogConstants.BUTTON_WIDTH
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}
	
	@Override
	public void verifyText(VerifyEvent e) {
		e.doit = false;
		char myChar = e.character;
		Text tData = (Text) e.widget;
		if (Character.isSpaceChar(myChar)
				|| Character.isDigit(myChar) || myChar == SWT.BS
				|| (int) myChar == 46 || (int) myChar == 45
				|| "".equals(e.text) || (int) myChar == 0
				|| (int) myChar == 8) {
			if (tData.getText() == null
					|| "".equals(e.text)
					|| (int) myChar == 0
					|| (int) myChar == 8
					|| (tData.getText().indexOf(".") < 0 && (int) myChar != 45)
					|| ((int) myChar != 46 && (int) myChar != 45)
					|| ((int) myChar == 45 && tData.getText()
							.indexOf("-") < 0)) {
				e.doit = true;
				return;
			}
		}		
	}
	
	public String getDefaultDirect() {
		return FINISH;
	};
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
}
