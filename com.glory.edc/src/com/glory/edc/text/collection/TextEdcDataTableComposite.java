package com.glory.edc.text.collection;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.edc.collection.EdcDataItem;
import com.glory.edc.collection.EdcUpload;
import com.glory.edc.collection.JudgeComposite;
import com.glory.edc.item.operation.ShowItemOperationDialog;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemOperation;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.edc.util.EDCUtil;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.FixEditorTableManager;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.Lot;

public class TextEdcDataTableComposite extends Composite {

	private static final Logger logger = Logger.getLogger(TextEdcDataTableComposite.class);

	public static final String TABLE_NAME = "WIPEdcDataItem";
	protected FormToolkit toolkit;

	protected Control ctlOperator;
	protected Control ctlEquipment;
	protected JudgeComposite judgeComposite;

	protected Lot lot;
	protected EdcTextSet edcTextSet;
	protected EdcSetCurrent edcCurrent;
	protected List<EdcData> currentEdcDatas;

	protected RowFixEditorTableManager tableManager;
	private TableViewer viewer;

	public TextEdcDataTableComposite(Composite parent, Object object, FormToolkit toolkit, EdcSetCurrent edcCurrent,
			Lot lot, List<EdcData> lastEdcDatas) {
		super(parent, SWT.NONE);
		this.edcTextSet = (EdcTextSet) object;
		this.toolkit = toolkit;
		this.edcCurrent = edcCurrent;
		this.lot = lot;
		this.currentEdcDatas = lastEdcDatas;
	}

	public void createForm() {
		GridLayout layout = new GridLayout(1, true);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		setLayoutData(new GridData(GridData.FILL_BOTH));
		createUpperComponent(this);
		createTableComponent(this);
		
		if (edcTextSet.getIsJudgeByManual()) {
			judgeComposite = new JudgeComposite(this, SWT.BORDER);
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			judgeComposite.setLayoutData(gd);
			judgeComposite.createForm();
		}
	}

	protected void createUpperComponent(final Composite group) {
		Composite composite = toolkit.createComposite(group, SWT.NONE);
		GridLayout layout = new GridLayout(4, true);
		layout.marginRight = 5;
		layout.marginLeft = 5;
		layout.marginHeight = 5;
		layout.verticalSpacing = 5;
		layout.makeColumnsEqualWidth = false;
		composite.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		composite.setLayoutData(gd);
		
		if (edcTextSet.getIsShowEquipment()) {
			//对多操作人处理
			toolkit.createLabel(composite, Message.getString("wip.operator") + "*");
			if (StringUtil.isEmpty(edcTextSet.getOwner())) {
				ctlOperator = toolkit.createText(composite, Env.getUserName(), SWT.BORDER);
				GridData gText = new GridData(GridData.FILL_HORIZONTAL);
				gText.widthHint = 40;
				ctlOperator.setLayoutData(gText);
			}else {
				List<String> groupNames = new ArrayList<>();
				for (String owner : edcTextSet.getOwner().split(";")) {
					groupNames.add(owner);
				}
				ctlOperator = RCPUtil.getADUserCombo(composite, groupNames, edcTextSet.getIsMultiOperator(), Env.getUserName());
				GridData gText = new GridData(GridData.FILL_HORIZONTAL);
				gText.widthHint = 40;
				ctlOperator.setLayoutData(gText);		
			}
			//对多设备处理		
			toolkit.createLabel(composite, Message.getString("ras.equipment") + ":");
			ctlEquipment = EDCUtil.getEdcEquipmentWidget(Env.getOrgRrn(), edcTextSet.getCapability(),
					edcTextSet.getIsMultiEquipment(),lot.getEquipmentId(), composite);
			GridData gCombo = new GridData(GridData.FILL_HORIZONTAL);
			gCombo.widthHint = 80;
			ctlEquipment.setLayoutData(gCombo);
		}
		
		if (edcTextSet.getIsShowOperation()) {
			Button btnShowOperation = toolkit.createButton(composite, Message.getString("edc.item_show_operation"), SWT.PUSH);
			btnShowOperation.setBounds(185, 53, 80, 20);
			gd = new GridData(GridData.FILL_HORIZONTAL);
			btnShowOperation.setLayoutData(gd);
			btnShowOperation.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					ShowItemOperationDialog operationDialog = new ShowItemOperationDialog(getShell(), edcTextSet.getTextSetLines().get(0), lot);
					operationDialog.open();
				}
			});
		}
	}

	public void createTableComponent(Composite composite) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			final ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			if (edcTextSet.getIsShowRemark()) {
				for (ADField field : adTable.getFields()) {
					if (EdcDataItem.FILED_REMARK.equals(field.getName())) {
						field.setIsDisplay(true);
						field.setIsMain(true);
						field.setIsEditable(true);
						break;
					}
				}
			}

			tableManager = new TextEdcDataTableManager(adTable);
			setViewer((TableViewer) tableManager.createViewer(composite, toolkit, 50));
			GridData gd = new GridData(GridData.FILL_BOTH);
			getViewer().getTable().setLayoutData(gd);
			setEdcDatas();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void upload(EdcUpload upload) {
		List<EdcData> edcDatas = getEdcData();
		edcDatas = upload.getEdcTextDataUpolad(edcTextSet, edcDatas);
		if (edcDatas != null) {
			currentEdcDatas = edcDatas;
			setEdcDatas();
		}
	}

	public void setEdcDatas() {
		List<EdcDataItem> dataItems = new ArrayList<EdcDataItem>();
		List<ADField> dataFileds = new ArrayList<ADField>();
		List<EdcTextSetLine> textSetLines = edcTextSet.getTextSetLines();

		try {
			ADManager adManager = Framework.getService(ADManager.class);
			for (EdcTextSetLine textLine : textSetLines) {
				EdcDataItem dataItem = new EdcDataItem();
				dataItem.setName(textLine.getName());
				dataItem.setDescription(textLine.getDescription());
				dataItem.setUsl(DBUtil.toDouble(textLine.getUslString()));
				dataItem.setSl(DBUtil.toDouble(textLine.getSlString()));
				dataItem.setLsl(DBUtil.toDouble(textLine.getLslString()));
				if (currentEdcDatas != null) {
					for (EdcData edcData : currentEdcDatas) {
						setMeasureEquipment(edcData.getMeasureEqp());
						setOperator(edcData.getOperator());
						if (edcData.getItemName().equals(textLine.getName())) {
							dataItem.setValue(edcData.getDcData());
							dataItem.setRemark(edcData.getDcRemark());
							break;
						}
					}
				}
				dataItems.add(dataItem);

				ADField adField = new ADField();
				if (EdcTextSetLine.DATATYPE_BOOLEAN.equals(textLine.getDataType())) {
					adField.setDisplayType(FieldType.BOOLEAN);
				} else if (EdcTextSetLine.DATATYPE_TEXT.equals(textLine.getDataType())) {
					adField.setDisplayType(FieldType.TEXT);
					adField.setMinValue(textLine.getLslString());
					adField.setMaxValue(textLine.getUslString());
					adField.setDataType("double");
				} else {
					adField.setDisplayType(FieldType.USERREFLIST);
					adField.setUreflistName(textLine.getSampleType());
				}
				dataFileds.add(adField);
			}
			tableManager.setInput(dataItems, dataFileds);
		} catch (Exception e) {
		}
	}

	public List<EdcData> getEdcData() {
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		List<EdcDataItem> dataItems = (List<EdcDataItem>) tableManager.getInput();
		if (dataItems == null || dataItems.size() == 0) {
			return edcDatas;
		}

		for (EdcDataItem dataItem : dataItems) {
			EdcData currentEdcData = new EdcData();
			currentEdcData.setOrgRrn(Env.getOrgRrn());
			currentEdcData.setOrgId(Env.getOrgName());
			currentEdcData.setMeasureTime(new Date());
			currentEdcData.setEdcSetRrn(edcTextSet.getObjectRrn());
			currentEdcData.setEdcSetName(edcTextSet.getName());
			currentEdcData.setEdcType(EdcData.EDCTYPE_TEXT);
			currentEdcData.setEdcSetVersion(edcTextSet.getVersion());
			currentEdcData.setItemName(dataItem.getName());
			currentEdcData.setDcName(dataItem.getName());
			currentEdcData.setDcData(dataItem.getValue());
			currentEdcData.setDcRemark(dataItem.getRemark());
			currentEdcData.setUsl(dataItem.getUsl());
			currentEdcData.setSl(dataItem.getSl());
			currentEdcData.setLsl(dataItem.getLsl());
			currentEdcData.setOperator(getCurOperator());
			if (lot != null) {
				currentEdcData.setLineId(lot.getLineId());
				currentEdcData.setLotRrn(lot.getObjectRrn());
				currentEdcData.setBatchId(lot.getBatchId());
				currentEdcData.setBatchLots("");
				currentEdcData.setLotCount(null);
				currentEdcData.setLotType(lot.getLotType());
				currentEdcData.setLotId(lot.getLotId());
				currentEdcData.setPartName(lot.getPartName());
				currentEdcData.setPartVersion(lot.getPartVersion());
				currentEdcData.setStepName(lot.getStepName());
				currentEdcData.setStepVersion(lot.getStepVersion());
				currentEdcData.setMaterialId(null);
				currentEdcData.setCustomer(lot.getCustomerCode());
				currentEdcData.setMeasureEqp(getMeasureEquipment());
				currentEdcData.setProcessEqp("");
			}
	
			edcDatas.add(currentEdcData);
		}
		
		if (edcTextSet.getIsJudgeByManual()) {
			EdcData currentEdcData = new EdcData();
			currentEdcData.setItemName(EdcTextSet.ITEM_JUDGE_BY_MANUAL);
			currentEdcData.setJudge1(judgeComposite.getJudge());
			edcDatas.add(currentEdcData);
		}
		
		return edcDatas;
	}

	public void setOperator(String operator) {

		if (ctlOperator != null) {
			if (ctlOperator instanceof Text) {
				((Text) ctlOperator).setText(operator);
			}
			if (ctlOperator instanceof XCombo) {
				((XCombo) ctlOperator).setText(operator);
			}
		} 
	}
	
	public void setMeasureEquipment(String measureEquipment) {

		if (ctlEquipment != null && measureEquipment != null) {
			if (ctlEquipment instanceof Text) {
				((Text) ctlEquipment).setText(measureEquipment);
			}
			if (ctlEquipment instanceof XCombo) {
				((XCombo) ctlEquipment).setText(measureEquipment);
			}
		}
	}

	public String getMeasureEquipment() {
		String measureEqp = null;
		if (ctlEquipment != null) {
			if (ctlEquipment instanceof Text) {
				measureEqp = (((Text) ctlEquipment).getText());
			}
			if (ctlEquipment instanceof XCombo) {
				measureEqp = (((XCombo) ctlEquipment).getText());
			}
		} else {
			measureEqp = lot.getEquipmentId();
		}
		return measureEqp;
	}
	
	public String getCurOperator() {
		String operator = null;
		if (ctlOperator  != null) {
			if (ctlOperator instanceof Text) {
				operator = ((Text) ctlOperator).getText();
			}
			if (ctlOperator instanceof XCombo) {
				operator = ((XCombo) ctlOperator).getText();
			}
		} else {
			operator = Env.getUserName();
		}
		return operator;
	}

	public boolean validate() {
		return true;
	}

	public EdcTextSet getEdcTextSet() {
		return edcTextSet;
	}

	public FixEditorTableManager getTableManager() {
		return tableManager;
	}

	public void setViewer(TableViewer viewer) {
		this.viewer = viewer;
	}

	public TableViewer getViewer() {
		return viewer;
	}
}
