package com.glory.edc.text.collection;

import java.util.List;

import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.TableEditor;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.KeyListener;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.TraverseEvent;
import org.eclipse.swt.events.TraverseListener;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.custom.XCombo1;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.FixEditorTableManager;
import com.glory.framework.base.ui.viewers.ListTableManager;
import com.glory.framework.runtime.Framework;

/**
 * 以列的形式显示显示对应的ADTable
 */
public class RowFixEditorTableManager extends FixEditorTableManager {
	
	public static final String FIELD_NAME = "name";
	public static final String FIELD_VALUE = "value";
	
	public RowFixEditorTableManager(ADTable adTable) {
		super(adTable);
	}

	public RowFixEditorTableManager(ADTable adTable, List<String> editorColumns) {
		super(adTable, editorColumns);
	}
	
	public void setInput(Object input, List<ADField> dataFileds) {
		//name和value栏位默认不参与Editor
    	this.getEditorColumns().remove(FIELD_NAME);
    	this.getEditorColumns().remove(FIELD_VALUE);
    	super.setInput(input);
    	
    	final Table table = ((TableViewer)this.viewer).getTable();
    	TableColumn [] columns = table.getColumns();
    	TableItem[] items = table.getItems();
    	for (int i = 0; i < columns.length; i++) {
    		TableColumn column = columns[i];
        	String columnId = (String)column.getData(COLUMN_ID);
        	if (columnId.equals(FIELD_VALUE)) {
        		//重置value栏位的编辑器
        		for (int j = 0; j < items.length; j++) {
        			TableItem item = items[j];
        			ADField field = dataFileds.get(j);
        			TableEditor editor = new TableEditor(table);
        			String editorType = field.getDisplayType();
        			
    				if (FieldType.BOOLEAN.equals(editorType)) {
        				Button button = new Button(table, SWT.CHECK);
        			    button.pack();
        			    editor.minimumWidth = button.getSize().x;
        			    editor.minimumHeight = rowHeight; 
        			    editor.horizontalAlignment = SWT.CENTER;
        			    item.setData(EDITOR_PREFIX + columnId, editor);
        			    button.setData(ROW_INDEX, j);
        			    button.setData(COLUMN_ID, columnId);
        			    button.setSelection("Y".equalsIgnoreCase(item.getText(i)));
        			    item.setText(i, "");
        			    editor.setEditor(button, item, i);
        			} else if (FieldType.USERREFLIST.equals(editorType)) {
        				try {
        					ADManager entityManager = Framework.getService(ADManager.class);
        					ADRefTable filedRefTable = new ADRefTable();
        					filedRefTable.setKeyField("key");
        					filedRefTable.setTextField("text");
        					ADTable adTable =  entityManager.getADTable(Env.getOrgRrn(), RefTableField.TABLENAME_USER_REFLIST);
        					filedRefTable.setAdTable(adTable);
        					
        					ListTableManager fieldTableManager = new ListTableManager(adTable); 
            				TableViewer fieldViewer = (TableViewer)fieldTableManager.createViewer(UI.getActiveShell(), new FormToolkit(Display.getCurrent()));
        					List<ADBase> fieldList = this.getFieldList(columnId, field);
        					fieldTableManager.setInput(fieldList);
            				int style = SWT.NONE;
            				if (field.getIsMandatory()) {
            					style = SWT.READ_ONLY;
            				}
            				fieldViewer.setInput(fieldList);
            				XCombo1 combo = new XCombo1(table, fieldViewer, filedRefTable.getKeyField(), filedRefTable.getTextField(), style, false);

//            				XCombo combo = new XCombo(table, fieldTableManager, filedRefTable.getKeyField(), filedRefTable.getTextField(), style, false);
            				if (item.getText(i) == null || item.getText(i).trim().length() == 0) {
    	        				if (field.getIsMandatory() && fieldList.size() > 0) { 
    	        					combo.select(0);
    	        				}
            				} else {
            					 combo.setText(item.getText(i));
            				}
            				combo.pack();
            				combo.setEditable(false);
            			    editor.minimumHeight = rowHeight; 
            			    editor.grabHorizontal = true;
            			    item.setData("EDITOR_" + columnId, editor);
            			    combo.setData(ROW_INDEX, j);
            			    combo.setData(COLUMN_ID, columnId);
            			    
            			    editor.setEditor(combo, item, i);
        				} catch (Exception e){
        					e.printStackTrace();
            			}
        			} else {
        				final Text text;
        				if (field.getIsReadonly()) {
        					text = new Text(table, SWT.CENTER | SWT.LEFT | SWT.BORDER | SWT.READ_ONLY);
        					text.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WIDGET_BACKGROUND));
        				} else {
        					text = new Text(table, SWT.CENTER | SWT.LEFT | SWT.BORDER);
        				}
	        			editor.setEditor(text, item, i);
	        			editor.minimumHeight = rowHeight; 
	        			editor.grabHorizontal = true;
	        			item.setData(EDITOR_PREFIX + columnId, editor);
	        			text.setData(ROW_INDEX, j);
	        			text.setData(COLUMN_ID, columnId);
	        			text.setData(COLUMN_FIELD, field);
	        			text.addModifyListener(new ModifyListener() {
							@Override
							public void modifyText(ModifyEvent e) {
								textModify((Text)e.widget);
							}
	        			});
	        			text.addVerifyListener(new VerifyListener() {
							@Override
							public void verifyText(VerifyEvent e) {
								e.doit = false; 
								if (validate(e)) {
									e.doit = true;
									return;
								}
							}
	        			});
	        			text.addKeyListener(new KeyListener() {
	        				@Override
	        				public void keyPressed(KeyEvent e) {
	        					//相应CRTL+B事件以支持从Excel粘贴功能
	        					//CRTL+V事件为text默认粘贴功能,目前暂未找到重载方法
	        					if (e.stateMask == SWT.CTRL) {
	        						if (e.keyCode == 98) {
	        							clipText((Text)e.widget);
	        							e.doit = false;
	        						}
	        					} 
	        				}

	        				@Override
	        				public void keyReleased(KeyEvent e) {
	        				}
	        			});
						text.setText(item.getText(i));
        			}
        				
    				editor.getEditor().addTraverseListener(new TraverseListener() {
						public void keyTraversed(TraverseEvent e) {
							if (e.detail != SWT.TRAVERSE_RETURN) {
								return;
							}
							int index = (Integer) e.widget.getData(ROW_INDEX);
							String columnId = (String) e.widget.getData(COLUMN_ID);
							if (index < table.getItemCount() - 1) {
								TableItem nextItem = table.getItem(index + 1);
								table.showItem(nextItem);
								
								TableEditor editor = (TableEditor) nextItem.getData(EDITOR_PREFIX + columnId);
								Control next = editor.getEditor();
								next.forceFocus();
								e.doit = false;
							}
						}
					});
    				editors.add(editor);
        		}
        	}
    	}
    }
	
	@Override
	public Object getInput() {
		this.getEditorColumns().add(FIELD_VALUE);
		return super.getInput();
	}
}
