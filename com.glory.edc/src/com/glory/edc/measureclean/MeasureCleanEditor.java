package com.glory.edc.measureclean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.osgi.service.event.Event;

import com.glory.edc.client.EDCManager;
import com.glory.edc.model.calculation.ProcessGroup;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADQuery;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class MeasureCleanEditor extends GlcEditor{
	public static final String EDITOR_ID = "bundleclass://com.glory.edc/com.glory.edc.measureclean.MeasureCleanEditor";

	private QueryFormField queryFormField;
	private EntityFormField entityFormField;

	private static final String FIELD_QUERY = "EDCMeasureCleanQuery";
	private static final String FIELD_FORM = "EDCMeasureCleanGlc";
	private static final String GROUP_ID = "GROUPID";
	private static final String TABLE_NAME_EXPORT = "EDCMeasureCleanQuery";

	@Override
	protected void createFormAction(GlcForm form) {

		super.createFormAction(form);

		queryFormField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);

		entityFormField = form.getFieldByControlId(FIELD_FORM, EntityFormField.class);

		// 查询事件
		subscribeAndExecute(eventBroker, queryFormField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);

		// 新建按钮事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);

		// 保存事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);

		// 选择事件
		subscribeAndExecute(eventBroker, queryFormField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);

		// 删除事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);

		// 刷新事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_REFRESH), this::refreshAdapter);

		// 导入事件
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_IMPORT), this::importAdapter);

	}

	public void queryAdapter(Object object) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			// 拿到Query查询条件
			LinkedHashMap<String, IField> fields = queryFormField.getQueryForm().getFields();
			StringBuffer where = new StringBuffer(" ");
			StringBuffer whereGroup = new StringBuffer(" groupId in ( ");
			List<ProcessGroup> queryResults = new ArrayList<ProcessGroup>();

			int num = 0;
			if(fields != null) {
				for(IField f : fields.values()) {
					Object value = f.getValue();
					if(value != null && !StringUtil.isEmpty(value.toString())) {
						if(num > 0) {
							where.append(" AND ");
						}
						where.append(f.getId() + " = '" + f.getValue() + "'");
						num ++;
					}
				}
			}


			int i = 0;
			int j = 0;

			List<ADQuery> querys = adManager.getEntityList(0L, ADQuery.class, 1, " name = 'MeasureCleanGroup'", "");
			if (querys.size() != 0) {
				ADQuery query = (ADQuery) querys.get(0);
				String queryText = query.getQueryText();
				List<Map> currentList = adManager.getEntityMapListByQueryText(queryText, getParameterMap(), 0, Env.getMaxResult(), where.toString(), "");
				if (currentList != null) {
					for (Map map : currentList) {
						HashMap<String, Object> hashMap = (HashMap<String, Object>) map;
						for (String key : hashMap.keySet()) {
							if((i + 1) == currentList.size()) {
								if (key.equalsIgnoreCase(GROUP_ID)) {
									whereGroup.append(map.get(key) + ") ");
									j++;
								}
							}else {
								if (key.equalsIgnoreCase(GROUP_ID)) {
									whereGroup.append(map.get(key) + ", ");
									j++;
								}
							}
						}
						i++;
					}
				}

			}
			List<ProcessGroup> groupList= new ArrayList<ProcessGroup>();
			if(j > 0) {
				groupList = adManager.getEntityList(Env.getOrgRrn(), ProcessGroup.class, Integer.MAX_VALUE, whereGroup.toString(), "");
			}

			if (CollectionUtils.isNotEmpty(groupList)) {

				Map<String, List<ProcessGroup>> processGroupMap = groupList.stream()
						.collect(Collectors.groupingBy(ProcessGroup::getGroupId));

				for (String groupId : processGroupMap.keySet()) {
					List<ProcessGroup> datas = processGroupMap.get(groupId);
					if (datas.size() == 2) {
						ProcessGroup processGroup1 = datas.get(0);
						ProcessGroup processGroup2 = datas.get(1);
						String processStepName = "";
						String measureStepName = "";

						if (ProcessGroup.PROCESSTYPE_CLEAN.equals(processGroup1.getProcessType())) {
							processStepName = processGroup1.getStepName();
							measureStepName = processGroup2.getStepName();
						} else if (ProcessGroup.PROCESSTYPE_MEASURE.equals(processGroup1.getProcessType())) {
							measureStepName = processGroup1.getStepName();
							processStepName = processGroup2.getStepName();
						}

						processGroup1.setProcessStepName(processStepName);
						processGroup1.setMeasureStepName(measureStepName);
						processGroup1.setProcessType("MC");

						queryResults.add(processGroup1);
					}
				}
			}
			queryFormField.getQueryForm().getTableManager().setInput(queryResults);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	public Map<String, Object> getParameterMap() {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("orgRrn", Env.getOrgRrn());
		return paramMap;
	}

	protected void newAdapter(Object object) {
		ProcessGroup newPlan = new ProcessGroup();
		entityFormField.setValue(newPlan);
		entityFormField.refresh();
		queryAdapter(new Object());
	}

	protected void saveAdapter(Object object) {
		form.getMessageManager().removeAllMessages();
		// 公共校验，动态表配置规则信息
		if (entityFormField.validate()) {
			ProcessGroup processGroup = (ProcessGroup) entityFormField.getValue();
			try {
				EDCManager edcManager = Framework.getService(EDCManager.class);
				if (processGroup.getProcessStepName().equals(processGroup.getMeasureStepName())) {
					UI.showInfo(Message.getString("edc.measure_clean_step_can_not_be_same"));// 弹出提示框
					return;
				}
				processGroup.setCategory(ProcessGroup.GROUPCATEGORY_MC);
				edcManager.saveProcessGroup(processGroup, Env.getSessionContext());
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
			refresh(processGroup);
		}
	}

	protected void refresh(ProcessGroup processGroup) {
		try {
			if(processGroup != null && processGroup.getGroupId() != null) {
				entityFormField.setValue(processGroup);
				entityFormField.refresh();
			} else {
				entityFormField.setValue(new ProcessGroup());
				entityFormField.refresh();
			}
			queryAdapter(new Object());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void selectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;

			ProcessGroup processGroup = (ProcessGroup) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (processGroup == null) {
				return;
			}

			//插入entityForm
			entityFormField.setValue(processGroup);
			entityFormField.refresh();

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected void deleteAdapter(Object object) {
		ProcessGroup processGroup = (ProcessGroup) entityFormField.getValue();
		if (processGroup != null && processGroup.getGroupId() != null) {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				try {

					EDCManager edcManager = Framework.getService(EDCManager.class);
					edcManager.removeProcessGroup(processGroup.getGroupId(), Env.getSessionContext());

				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				refresh(null);
			}
		}
	}

	protected void refreshAdapter(Object object) {
		refresh(null);
	}

	private void importAdapter(Object object) {
		MeasureCleanUpload upload = new MeasureCleanUpload(form.getAuthority(), null, TABLE_NAME_EXPORT);
		if (upload.getUploadProgress().init()) {
			if (upload.run()) {
				// 刷新
				refreshAdapter(object);
			}
		}
	}
}
