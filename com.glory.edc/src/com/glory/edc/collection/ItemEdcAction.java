package com.glory.edc.collection;

import java.util.List;

import org.eclipse.jface.dialogs.Dialog;

import com.glory.edc.attr.collection.component.AttrEdcDialogComponent;
import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.extensionpoints.IEdcAction;
import com.glory.edc.generaledc.GeneralEdcDialog;
import com.glory.edc.mlot.collection.MLotEdcDialog;
import com.glory.edc.mlot.collection.VirtualMLotEdcDialog;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.offlinelot.OffLineLotDialog;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class ItemEdcAction implements IEdcAction {

	@Override
	public Object doAction(EdcEvent event) {
		EdcDialog dialog = null;
		EdcItemSet itemSet = (EdcItemSet)event.getEdcSet();
		
		if (EdcData.EDCFROM_LOT.equals(event.getEdcFrom())
				|| EdcData.EDCFROM_OFFLINELOT.equals(event.getEdcFrom())) {
			//如果是按批次进行数据采集
			if (itemSet.getItemSetLines().size() == 1 && 
					EdcItem.DATATYPE_ATTRIBUTE.equals(itemSet.getItemSetLines().get(0).getDataType())) {
				//只有EDC中只有一个Attribute类型的数据采集时,才需要先判断是否按Component采集
				if (event.getLot().getSubUnitType().equals(ComponentUnit.getUnitType())
						&& itemSet.getIsByComponent()) {
					//如果按Component采集,需要先选择Component
					SelectComponentDialog4Edc d = new SelectComponentDialog4Edc(
							UI.getActiveShell(), event.getLot().getMainQty().intValue(), event.getLot(), event.getEdcSet());
					if (d.open() == Dialog.OK) {
						List<ComponentUnit> selectedComponents = d.getSelectedComponents();
						if (selectedComponents != null && selectedComponents.size() > 0){
							dialog = new AttrEdcDialogComponent(UI.getActiveShell(), event.getEdcCurrent(), itemSet, 
									event, selectedComponents);
							dialog.open();
							return dialog;
						} else {
							UI.showWarning(Message.getString("edc.bin_waferid_null"));
							return null;
						}
					}
				}
			}
			if (EdcData.EDCFROM_LOT.equals(event.getEdcFrom())) {
				dialog = new EdcDialog(UI.getActiveShell(), event.getEdcCurrent(), itemSet, event);
				dialog.open();
			} else {
				dialog = new OffLineLotDialog(UI.getActiveShell(), itemSet, event);
				dialog.open();
			} 
		} else if (EdcData.EDCFROM_GENERAL.equals(event.getEdcFrom())) {
			//一般数据采集
			dialog = new GeneralEdcDialog(UI.getActiveShell(), itemSet, event);
			dialog.open();
		} else if (EdcData.EDCFROM_MLOT.equals(event.getEdcFrom())) {
			// 原材料数据采集
			MLot mLot = event.getMLot();
			
			Lot lot = new Lot();
			lot.setObjectRrn(mLot.getObjectRrn());
			lot.setLotId(mLot.getmLotId());
			lot.setLotType(mLot.getmLotType());
			lot.setMainQty(mLot.getMainQty());
			lot.setSubQty(mLot.getSubQty());
			lot.setPartName(mLot.getMaterialName());
			lot.setPartVersion(mLot.getMaterialVersion());
			event.setLot(lot);
			
			dialog = new MLotEdcDialog(UI.getActiveShell(), event.getEdcCurrent(), itemSet, event);
			dialog.open();
		} else if (EdcData.EDCFROM_VIRTUAL_MLOT.equals(event.getEdcFrom())) {
			// 原材料数据采集
			MLot mLot = event.getMLot();
			
			Lot lot = new Lot();
			lot.setObjectRrn(mLot.getObjectRrn());
			lot.setLotId(mLot.getmLotId());
			lot.setLotType(mLot.getmLotType());
			lot.setMainQty(mLot.getMainQty());
			lot.setSubQty(mLot.getSubQty());
			lot.setPartName(mLot.getMaterialName());
			lot.setPartVersion(mLot.getMaterialVersion());
			event.setLot(lot);
			
			dialog = new VirtualMLotEdcDialog(UI.getActiveShell(), event.getEdcCurrent(), itemSet, event);
			dialog.open();
		}
		
		//显示结果
		if (dialog != null && dialog.getReturnCode() == Dialog.OK && !dialog.getIsTemp()) {
			ResultDialog spcDialog = new ResultDialog(UI.getActiveShell(), 
					event.getEdcFrom(), event.getEdcSet(), dialog.getDcDatas(), dialog.getDcResult());
			spcDialog.open();
		}
		return dialog.getReturnCode();
	}

}