package com.glory.edc.collection;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.custom.CTabItem;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;

import com.glory.edc.attr.collection.EdcDataTableCompositeAttr;
import com.glory.edc.extensionpoints.IEdcUnit;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.table.TableEdcDataTableComposite;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.wip.model.Lot;

public class ItemEdcUnit implements IEdcUnit {
	
	public Composite createUnit(Composite parent, Lot lot, Object object) {
		return createUnit(parent, lot, object, null);
	}
	
	public Composite createUnit(Composite parent, Lot lot, Object object, CTabFolder tabFolder) {
		EdcItemSetLine edcLine = (EdcItemSetLine)object;
		String dataType = edcLine.getDataType();
		if (EdcItem.DATATYPE_ATTRIBUTE.equals(dataType)) {
			Group group = new Group(parent, SWT.NONE);
			GridLayout layout = new GridLayout(1, true);
			layout.numColumns = 1;
			layout.marginRight = 1;
			layout.marginLeft = 1;
			layout.verticalSpacing = 3;
			group.setLayout(layout);
			group.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
			
			GridData gd = new GridData(GridData.FILL_VERTICAL);
			gd.widthHint = 350;
			group.setLayoutData(gd);//在数据采集项后面加上中文描述。
			group.setText(edcLine.getName() + "<"+edcLine.getEdcItemDesc()+">");
			
			//如果是ATTRIBUTE进入不同的TableComposite
			EdcDataTableCompositeAttr form = new EdcDataTableCompositeAttr(group, lot, edcLine);
			form.createForm();
			return form;
		} else {
			EdcDataTableComposite edcUnit = null;
			if (tabFolder == null) {
				Group group = new Group(parent, SWT.NONE);
				GridLayout layout = new GridLayout(1, true);
				layout.numColumns = 1;
				layout.marginRight = 1;
				layout.marginLeft = 1;
				layout.verticalSpacing = 3;
				group.setLayout(layout);
				group.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
				
				GridData gd = new GridData(GridData.FILL_VERTICAL);
				gd.widthHint = 380;
				group.setLayoutData(gd);//在数据采集项后面加上中文描述。
				group.setText(edcLine.getName() + "<"+edcLine.getEdcItemDesc()+">");
				edcUnit = new EdcDataTableComposite(group, lot, edcLine);
			} else {
				CTabItem item = new CTabItem(tabFolder, SWT.BORDER);
				item.setText(edcLine.getName() + "<"+edcLine.getEdcItemDesc()+">");
				edcUnit = new TableEdcDataTableComposite(tabFolder, lot, edcLine);
				item.setControl(edcUnit);
			}
			edcUnit.createForm();
			return edcUnit;
		}
	}

	@Override
	public void disConnect() {
		// TODO Auto-generated method stub
		
	}
	
}
