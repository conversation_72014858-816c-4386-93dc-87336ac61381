package com.glory.edc.collection;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.nebula.widgets.nattable.edit.command.EditCellCommand;
import org.eclipse.nebula.widgets.nattable.edit.editor.ICellEditor;
import org.eclipse.nebula.widgets.nattable.selection.SelectionLayer.MoveDirectionEnum;
import org.eclipse.nebula.widgets.nattable.selection.command.SelectCellCommand;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.events.DisposeEvent;
import org.eclipse.swt.events.DisposeListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.edc.attr.collection.EdcDataTableCompositeAttr;
import com.glory.edc.auto.camel.CamelMessage;
import com.glory.edc.auto.camel.CamelMessageData;
import com.glory.edc.bin.collection.BinEdcDataTableComposite;
import com.glory.edc.extensionpoints.EdcUnitExtensionPoint;
import com.glory.edc.extensionpoints.IEdcUnit;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.text.collection.TextEdcDataTableComposite;
import com.glory.framework.base.entitymanager.forms.ScorllFormComposite;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.model.Lot;

public class EdcScorllForm extends ScorllFormComposite {

	protected FormToolkit toolkit;
	protected EdcSetCurrent edcCurrent;
	protected Lot lot;
	protected List<EdcData> lastDcDatas;
	protected int style;
	
	protected CTabFolder tabFolder = null;
	
	protected IEdcUnit edcUnit = null;
	
	public EdcScorllForm(Composite parent, EdcSetCurrent edcCurrent, 
			Lot lot, List<?> objects, List<EdcData> lastDcDatas, FormToolkit toolkit) {
		super(parent, objects, toolkit);
		this.edcCurrent = edcCurrent;
		this.lot = lot;
		this.lastDcDatas = lastDcDatas;
		this.toolkit = toolkit;
		parent.addDisposeListener(new DisposeListener() {
			
			@Override
			public void widgetDisposed(DisposeEvent e) {
				EdcDataTableManagers.getTableManagers().clear();
			}
		});
	}
	
	@Override
	public void createContent() {
		if (toolkit == null) {
			toolkit = new FormToolkit(Display.getCurrent()); 
		}
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		int numColumns = this.objects.size() + 2;
		Object object = this.getObjects().get(0);
		if(object instanceof EdcItemSetLine) {
			EdcItemSetLine edcLine = (EdcItemSetLine) object;
			if (edcLine.getIsFormulaTable()) {
				numColumns = 1;
			}
		}
		
		Composite body = form.getBody();
		GridLayout layout = new GridLayout(numColumns, true);
		
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		for (int i = 0; i < this.getObjects().size(); i++) {
			getUnits().add(createUnit(body, this.getObjects().get(i)));
		}
		if (tabFolder != null) {
			tabFolder.setSelection(0);
		}
	}

	@Override
	public Composite createUnit(Composite parent, Object object) {
		EdcItemSetLine edcLine = (EdcItemSetLine)object;
		
		if (edcLine.getIsAutoDc()) {
			edcUnit = EdcUnitExtensionPoint.getEdcUnit(edcLine.getItemSet().getEdcType() + "-AUTO");
		}
		if (edcUnit == null) {
			edcUnit = new ItemEdcUnit();
		}
		
		Composite composite = null;
		if (!edcLine.getIsAutoDc() && edcLine.getIsFormulaTable()) {
			if (tabFolder == null) {
				tabFolder = new CTabFolder(parent, SWT.FLAT | SWT.TOP);
				toolkit.adapt(tabFolder, true, true);
				GridData gd = new GridData(GridData.FILL_BOTH);
				tabFolder.setLayoutData(gd);
			}
			composite = edcUnit.createUnit(parent, lot, object, tabFolder);
		} else {
			composite = edcUnit.createUnit(parent, lot, object);
		}
				
		EdcData currentEdcData = null;
		if (lastDcDatas != null) {
			for (EdcData data : lastDcDatas) {
				if (((EdcItemSetLine)edcLine).getName().equals(data.getItemName())) {
					currentEdcData = data;
				}
			}
		}
		
		if (composite instanceof EdcDataTableCompositeAttr) {
			((EdcDataTableCompositeAttr)composite).setLot(lot);
			((EdcDataTableCompositeAttr)composite).setEdcCurrent(edcCurrent);
			((EdcDataTableCompositeAttr)composite).setCurrentEdcData(currentEdcData);
			((EdcDataTableCompositeAttr)composite).setEdcDatas();
		} else if (composite instanceof EdcDataTableComposite) {
			((EdcDataTableComposite)composite).setLot(lot);
			((EdcDataTableComposite)composite).setEdcCurrent(edcCurrent);
			((EdcDataTableComposite)composite).setCurrentEdcData(currentEdcData);
			((EdcDataTableComposite)composite).setEdcDatas();
		}
		return composite;
	} 
	
	public void clearEditorTableData() {
		for (Composite unit : units) {
			if (unit instanceof EdcDataTableCompositeAttr) {
				EdcDataTableCompositeAttr form = (EdcDataTableCompositeAttr)unit;
				form.getTxtTotal().setText("0");
				form.getTxtBadNum().setText("0");
			} else if (unit instanceof EdcDataTableComposite) {
				EdcDataTableComposite form = (EdcDataTableComposite)unit;
				form.setEdcDatas();
				
				form.getTableManager().setCurrentCell(null);
			}
		}
	}
	
	public List<EdcData> getEdcDatas(){
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		for (Composite unit : units) {
			if (unit instanceof EdcDataTableCompositeAttr) {
				EdcDataTableCompositeAttr eTable = (EdcDataTableCompositeAttr)unit;
				EdcData data = eTable.getEdcData();
				if(data != null){
					edcDatas.add(data);
				}
			}else if (unit instanceof EdcDataTableComposite) {
				EdcDataTableComposite eTable = (EdcDataTableComposite)unit;
				EdcData data = eTable.getEdcData();
				if (data != null){
					edcDatas.add(data);
				}
			}
		}
		return edcDatas;
	}
	
	public void setSelectButtonEnable() {
		for (Composite unit : units) {
			EdcDataTableComposite composite = (EdcDataTableComposite)unit;
			composite.setSelectButtonEnable();
		}
	}
	
	public boolean validate() {
		for (Composite unit : units) {
			if (unit instanceof EdcDataTableCompositeAttr) {
				EdcDataTableCompositeAttr eTable = (EdcDataTableCompositeAttr)unit;
				if (!eTable.validate()) {
					return false;
				}
			}else if (unit instanceof EdcDataTableComposite) {
				EdcDataTableComposite eTable = (EdcDataTableComposite)unit;
				if (!eTable.validate()) {
					return false;
				}
			}
		}
		return true;
	}
	
	public void upload(EdcUpload upload) {
		for (Composite unit : units) {
			if (unit instanceof EdcDataTableCompositeAttr) {
				EdcDataTableCompositeAttr eTable = (EdcDataTableCompositeAttr)unit;
				eTable.upload(upload);
			}else if (unit instanceof EdcDataTableComposite) {
				EdcDataTableComposite eTable = (EdcDataTableComposite)unit;
				eTable.upload(upload);
			} else if (unit instanceof BinEdcDataTableComposite) {
				BinEdcDataTableComposite eTable = (BinEdcDataTableComposite) unit;
				eTable.upload(upload);
			} else if (unit instanceof TextEdcDataTableComposite) {
				TextEdcDataTableComposite eTable = (TextEdcDataTableComposite) unit;
				eTable.upload(upload);
			}
		}
	}
	
	public void disConnect() {
		if (edcUnit == null) {
			return;
		}
		edcUnit.disConnect();
	}
	
	public boolean loadAutoData(CamelMessage camelMessage) {
		if (CollectionUtils.isEmpty(getUnits())) {
			return true;
		}
		
		int i = 0;
		for (Composite unit : getUnits()) {
			EdcDataTableManager tableManager = null;
			String itemName = null;
			if (unit instanceof EdcDataTableComposite) {
				EdcDataTableComposite comp = (EdcDataTableComposite) unit;
				tableManager = comp.getTableManager();
				itemName = comp.getEdcSetLine().getName();
			}
			
			if (tableManager != null) {
				List<EdcDataItem> dataItems = (List<EdcDataItem>) tableManager.getInput();
				if (CollectionUtils.isNotEmpty(dataItems)) {
					String finalItemName = itemName;
					List<CamelMessageData> datas = camelMessage.getResultDatas();
					Optional<CamelMessageData> find = datas.stream().filter(d -> d.getName().equals(finalItemName)).findFirst();
					if (!find.isPresent()) {
						return false;
					}
					CamelMessageData messageData = find.get();
					EdcDataCellEditor currentCell = tableManager.getCurrentCell();
					
					Optional<EdcDataItem> itemFind = dataItems.stream().filter(d -> StringUtil.isEmpty(d.getValue())).findFirst();
					if (itemFind.isPresent() && currentCell == null) {
						// 从空栏位往下赋值
						for (String value : messageData.getValues()) {
							for (EdcDataItem dataItem : dataItems) {
								if (StringUtil.isEmpty(dataItem.getValue())) {
									dataItem.setValue(value);
									break;
								}
							}
						}
						
						tableManager.refresh();
					} else if (currentCell != null) {
						int index = currentCell.getRowPosition() - 1;
						
						// 从该Index往下赋值
						List<String> values = messageData.getValues();
						for (int k = index; k < dataItems.size(); k++) {
							if (k -index == values.size()) {
								break;
							}
							
							// 如果只有一列是可以编辑列，回车向下换行
							currentCell.commit(MoveDirectionEnum.DOWN);
							tableManager.getNatTable().doCommand(
									new EditCellCommand(
											tableManager.getNatTable(),
											tableManager.getNatTable().getConfigRegistry(),
											tableManager.getNatTable().getCellByPosition(currentCell.getColumnPosition(), currentCell.getRowPosition() + 1)));
							currentCell = tableManager.getCurrentCell();
							
							EdcDataItem dataItem = dataItems.get(k);
							dataItem.setValue(values.get(k - index));
						}
						
					} else {
						i++;
					}
					
					itemFind = dataItems.stream().filter(d -> StringUtil.isEmpty(d.getValue())).findFirst();
					if (!itemFind.isPresent()) {
						i++;
					}
				}
			}
		}
		
		boolean finishedAll = i == getUnits().size();
		return finishedAll;
	}
}
