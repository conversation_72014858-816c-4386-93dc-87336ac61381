package com.glory.edc.collection;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcTextSet;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.core.exception.ExceptionBundle;

public class EdcUpload {

	public static final String TEMPLATE_FILE_NAME = "edcdata_template.xlsx";
	
	public static final String BIN_TEMPLATE_FILE_NAME = "bindata_template.xlsx";

	public static final String HEADER_COMPONENT_ID = "COMPONENTID";
	
	public static final String HEADER_ID = "ID";
	
	public static final String HEADER_DATA = "DATA";
	
	public static final String HEADER_BIN_NAME = "BINNAME";
	
	public static final String HEADER_BIN_VALUE = "VALUE";
	
	public static final String HEADER_ITEM = "ITEM";
	
	private List<Map> uploadList;
	
	public EdcUpload(List<Map> uploadList) {
		this.uploadList = uploadList;
	}
	
	/**
	 * 获得ComponentId信息
	 * 
	 * @param List<String> 返回ComponentId信息
	 * 如果ComponentId重复,只记录一次ComponentId
	 * 如果未定义COMPONENTID栏位则返回null
	 */
	public List<String> getUploadComponents() {
		List<String> datas = new ArrayList<String>();
		for (Map upload : uploadList) {
			if (upload.containsKey(HEADER_COMPONENT_ID)) {
				Object value = upload.get(HEADER_COMPONENT_ID);
				if (value != null ) {
					String valueStr = String.valueOf(value).trim();
					if (valueStr.length() != 0 && !datas.contains(valueStr)) {
						datas.add(valueStr);
					}
				} 
			}
		}
		if (datas.size() == 0) {
			return null;
		}
		return datas;
	}
	
	
	/**
	 * 通过获得Id信息
	 * @param List<String> 返回Id信息
	 */
	public List<Map> getUploads(String header) {
		List<Map> maps = new ArrayList<Map>();
		for (Map upload : uploadList) {
			if(upload.get(HEADER_ITEM) != null) {
				if (upload.get(HEADER_ITEM).equals(header)) {
					maps.add(upload);
				}
			}
		}
		if (maps.size() == 0) {
			return null;
		}
		return maps;
	}
	
	/**
	 * 获得对应的数据
	 * 
	 * @param List<String> 如果对应位置数据为null,只记录为""
	 * 如果未定义header栏位则返回null
	 */
	public List<String> getUploadDatas(String header) {
		List<String> datas = new ArrayList<String>();
		header = header.toUpperCase();
		boolean isExist = false;
		for (Map upload : uploadList) {
			if (upload.containsKey(header)) {
				isExist = true;
				
				Object value = upload.get(header);
				if (value != null) {
					datas.add(String.valueOf(value));
				} else {
					datas.add("");
				}
			}
		}
		if (!isExist) {
			return null;
		}
		return datas;
	}
	
//	/**
//	 * 获得上传的EDC数据
//	 * @param edcLine 数据采集项
//	 * @param edcData 数据采集项对应的Edc数据
//	 * @param isComponent 是否需要采集Component信息
//	 * @param isCreateId 是否根据edcLine定义创建Id号,如果不创建则使用序号
//	 * @param itemNumber
//	 * 
//	 * @return 如果上传文件中包含了此EDC数据,则返回上传的数据,否则返回null
//	 */
//	public EdcData getEdcData(EdcItemSetLine edcLine, EdcData edcData, boolean isComponent, boolean isCreateId, int itemNumber) {
//		if (edcData == null) {
//			edcData = new EdcData();
//		}
//		if (EdcItem.DATATYPE_VARIABLE.equals(edcLine.getDataType())) {
//			List<String> componentIds = null;
//			if (isComponent) {
//				//需要输入ComponentID
//				componentIds = getUploadComponents();
//				if (componentIds != null) {
//					String componentStr = null;
//					for (String componentId : componentIds) {
//						if (componentStr == null) {
//							componentStr = componentId;
//						} else {
//							componentStr = componentStr + ";" + componentId;
//						}
//					}
//					edcData.setComponentList(componentStr);
//				}
//			}
//			List<String> datas = getUploadDatas(edcLine.getName());
//			if (datas == null) {
//				return null;
//			}
//			
//			String[] itemDescs;
//			String name = null;
//			String value = null;
//			if (isCreateId) {
//				itemDescs = EdcDataTableComposite.createIds(edcLine, componentIds, isComponent, itemNumber);
//				for (int i = 0; i < itemDescs.length; i++) {
//					if (name == null) {
//						name = DBUtil.toString(itemDescs[i]);
//						if (datas.size() > i) {
//							value = DBUtil.toString(datas.get(i));
//						} else {
//							value = "";
//						}
//					} else {
//						name += ";" + DBUtil.toString(itemDescs[i]);
//						if (datas.size() > i) {
//							value += ";" + DBUtil.toString(datas.get(i));
//						} else {
//							value += ";" + "";
//						}
//					}
//				}
//			} else {
//				//移除List尾部为空的数据
//				ListIterator<String> listIterator = datas.listIterator(datas.size() - 1);
//				while (listIterator.hasPrevious()) {
//					if (StringUtil.isEmpty(listIterator.previous())) {
//						listIterator.remove();
//					} else {
//						//只要尾部发现一个不为空的就返回
//						break;
//					}
//				}
//				
//				for (int i = 0; i < datas.size(); i++) {
//					if (name == null) {
//						name = DBUtil.toString(i + 1);
//						if (datas.size() > i) {
//							value = DBUtil.toString(datas.get(i));
//						} else {
//							value = "";
//						}
//					} else {
//						name += ";" + DBUtil.toString(i + 1);
//						if (datas.size() > i) {
//							value += ";" + DBUtil.toString(datas.get(i));
//						} else {
//							value += ";" + "";
//						}
//					}
//				}
//			}
//			edcData.setDcName(name); 
//			edcData.setDcData(value); 
//			
//			return edcData;
//		}
//		
//		return null;
//	}
	
	
	/**
	 * 获得上传的EDC数据
	 * @param edcLine 数据采集项
	 * @param edcData 数据采集项对应的Edc数据
	 * @param isComponent 是否需要采集Component信息
	 * @param isCreateId 是否根据edcLine定义创建Id号,如果不创建则使用序号
	 * @param itemNumber
	 * 
	 * @return 如果上传文件中包含了此EDC数据,则返回上传的数据,否则返回null
	 */
	public EdcData getEdcDataUpload(EdcItemSetLine edcLine, EdcData edcData) {
		if (edcData == null) {
			edcData = new EdcData();
		}
		if (EdcItem.DATATYPE_VARIABLE.equals(edcLine.getDataType())) {
			String name = null;
			String value = null;
			List<Map> maps = getUploads(edcLine.getName());
			if(CollectionUtils.isNotEmpty(maps)) {
				for(Map map : maps) {
					if (name == null) {
						if(map.get(HEADER_ID) != null && !StringUtil.isEmpty((String)map.get(HEADER_ID))) {
							name = DBUtil.toString(map.get(HEADER_ID));
						}
						value = DBUtil.toString(map.get(HEADER_DATA));
					} else {
						if(map.get(HEADER_ID) != null && !StringUtil.isEmpty((String)map.get(HEADER_ID))) {
							name += ";" + DBUtil.toString(map.get(HEADER_ID));
						}
						value += ";" + DBUtil.toString(map.get(HEADER_DATA));
					}
				}
			}
			edcData.setDcName(name); 
			edcData.setDcData(value); 
			
			return edcData;
		}
		
		return null;
	}

//	/**
//	 * 获得上传的BIN EDC数据
//	 * @param edcBinSet 数据采集级名称
//	 * @param edcData 数据采集项对应的Edc数据
//	 * 
//	 * @return 如果上传文件中包含了此EDC数据,则返回上传的数据
//	 */
//	public EdcData getEdcBinData(EdcBinSet edcBinSet, EdcData edcData) {
//		if (uploadList == null) {
//			return null;
//		}
//		
//		// 将导入的数据转化为Map<Bin名称,Bin值>形式
//		Map<String, String> binValueMap = new HashMap<String, String>();
//		for (Map map : uploadList) {
//			String binName = null;
//			String binValue = null;
//			for (Object key : map.keySet()) {
//	            String keyStr = (String) key;
//	            if (HEADER_BIN_NAME.equals(keyStr)) {
//	            	binName = (String) map.get(keyStr);;
//	            } else {
//	            	binValue = (String) map.get(keyStr);;
//	            }
//	        }
//			binValueMap.put(binName, binValue);
//		}
//		
//		// 通过传入的DcName数据获取对应的值
//		// 注意：TOTAL和Group类型的数据不导入，此时需要处理
//		String names[] = edcData.getDcName().split(";");
//		String values[] = edcData.getDcData().split(";", edcData.getDcData().length());
//
//		StringBuffer dcValueSb = new StringBuffer("");
//
//		for (int i=0; i < names.length; i++) {
//			if (binValueMap.get(names[i]) != null) {
//				dcValueSb.append(binValueMap.get(names[i]));
//			} else {
//				dcValueSb.append(values[i]);
//			}
//			dcValueSb.append(";");
//		}
//
//		// 只需要更新DcData，不需要更新DcName
//		edcData.setDcData(dcValueSb.toString()); 
//		return edcData;
//	}
	
	/**
	 * 获得上传的BIN EDC数据
	 * @param edcBinSet 数据采集级名称
	 * @param edcData 数据采集项对应的Edc数据
	 * 
	 * @return 如果上传文件中包含了此EDC数据,则返回上传的数据
	 */
	public EdcData getEdcBinDataUpload(EdcBinSet edcBinSet, EdcData edcData) {
		if (uploadList == null) {
			return null;
		}
		
		// 将导入的数据转化为Map<Bin名称,Bin值>形式
		Map<String, String> binValueMap = new HashMap<String, String>();
		for (Map map : uploadList) {
			String binName = null;
			String binValue = null;
			//如果是批次Component收集则判断等于ComponentID如果是设备则是单个与采集项集名称匹配
			if(map.get(HEADER_ID) != null && (map.get(HEADER_ID).equals(edcData.getComponentList()) || map.get(HEADER_ID).equals(edcData.getEdcSetName()))) {
				for (Object key : map.keySet()) {
		            String keyStr = (String) key;
		            if (HEADER_BIN_NAME.equals(keyStr)) {
		            	binName = (String) map.get(keyStr);;
		            } else if(HEADER_BIN_VALUE.equals(keyStr)) {
		            	binValue = (String) map.get(keyStr);;
		            }
		        }
				binValueMap.put(binName, binValue);
			}
		}
		
		// 通过传入的DcName数据获取对应的值
		// 注意：TOTAL和Group类型的数据不导入，此时需要处理
		String names[] = edcData.getDcName().split(";");
		String values[] = edcData.getDcData().split(";", edcData.getDcData().length());

		StringBuffer dcValueSb = new StringBuffer("");

		for (int i=0; i < names.length; i++) {
			if (binValueMap.get(names[i]) != null) {
				dcValueSb.append(binValueMap.get(names[i]));
			} else {
				dcValueSb.append(values[i]);
			}
			dcValueSb.append(";");
		}

		// 只需要更新DcData，不需要更新DcName
		edcData.setDcData(dcValueSb.toString()); 
		return edcData;
	}
	
	
	/**
	 * 获得上传的BIN EDC数据
	 * @param edcBinSet 数据采集级名称
	 * @param edcData 数据采集项对应的Edc数据
	 * 
	 * @return 如果上传文件中包含了此EDC数据,则返回上传的数据
	 */
	public List<EdcData> getEdcTextDataUpolad(EdcTextSet edcTextSet, List<EdcData> edcDatas) {
		if (uploadList == null) {
			return null;
		}
		
		// 将导入的数据转化为Map<Bin名称,Bin值>形式
		Map<String, String> binValueMap = new HashMap<String, String>();
		if(CollectionUtils.isNotEmpty(edcDatas)) {
			for(EdcData edcData : edcDatas) {
				for (Map map : uploadList) {
					if(map.get(HEADER_ITEM).equals(edcData.getDcName())) {
						edcData.setDcData(DBUtil.toString(map.get(HEADER_DATA)));
					}
				}
			}
		}
		
		// 通过传入的DcName数据获取对应的值
		return edcDatas;
	}
}
