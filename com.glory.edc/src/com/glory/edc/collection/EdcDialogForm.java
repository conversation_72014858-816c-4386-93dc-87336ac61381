package com.glory.edc.collection;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.edc.auto.AutoEventBroker;
import com.glory.edc.auto.camel.CamelMessage;
import com.glory.edc.auto.camel.CamelMessageUtil;
import com.glory.edc.bin.collection.BinEdcScrollForm;
import com.glory.edc.bin.collection.component.BinEdcScrollFormComponent;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.text.collection.TextEdcScrollForm;
import com.glory.edc.text.collection.component.TextEdcScrollFormComponent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.WorkStation;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.google.gson.Gson;

public class EdcDialogForm {
	
	/**
	 * 发送rest消息后等待返回
	 */
	public static final String TYPE_REST = "Rest";
	
	/**
	 * 串口方式，发送rest消息后等待返回
	 */
	public static final String TYPE_SERIAL_PORT = "SerialPort";

	protected Composite parent;
	protected FormToolkit toolkit;
	protected EdcScorllForm edcForm;
	protected EdcSetCurrent edcCurrent;
	protected AbstractEdcSet edcSet;
	protected Lot lot;
	protected List<EdcData> lastDcDatas;
	
	boolean connetFlag = false;
	
	protected String autoEdcType;
	
	public EdcDialogForm(Composite parent, EdcSetCurrent edcCurrent,
			Lot lot, AbstractEdcSet edcSet, List<EdcData> lastDcDatas, FormToolkit toolkit) {
		this.parent = parent;
		this.edcCurrent = edcCurrent;
		this.edcSet = edcSet;
		sortItemSetLine();
		this.lot = lot;
		this.lastDcDatas = lastDcDatas;
		this.toolkit = toolkit;
		AutoEventBroker.subscribe(connectHandler);
	}
	
	private void sortItemSetLine() {
		// 按采集项名称排序
		if (edcSet instanceof EdcItemSet) {
			List<EdcItemSetLine> lines = ((EdcItemSet) edcSet).getItemSetLines();
			Optional<EdcItemSetLine> optional = lines.stream().filter(l -> l.getSeqNo() == null).findFirst();
			if(optional.isPresent()) {
				lines = lines.stream().sorted(Comparator.comparing(EdcItemSetLine::getName)).collect(Collectors.toList());
			} else {
				lines = lines.stream().sorted(Comparator.comparing(EdcItemSetLine::getSeqNo)).collect(Collectors.toList());
			}
			((EdcItemSet) edcSet).setItemSetLines(lines);
		}
	}

	public void createForm() {
		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		parent.setLayout(layout);
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
		createBasicInfoFrom(parent, toolkit);
		createAutoFrom(parent, toolkit);
		createEdcScorllForm(parent, toolkit);
	}
	
	
	protected void createAutoFrom(Composite parent, FormToolkit toolkit) {
		
		if (lot != null && edcSet != null) {
			String equipmentId = lot.getEquipmentId();
			if (StringUtil.isEmpty(equipmentId)) {
				// 离线数据收集设备栏位
				equipmentId = DBUtil.toString(lot.getAttribute1());
			}
			
			if (StringUtil.isEmpty(equipmentId)) {
				// 没有设备ID不能做自动数据收集
				return;
			}
			
			try {
				String whereClause = " name = '" + equipmentId + "'";
				ADManager adManager = Framework.getService(ADManager.class);
				List<WorkStation> stations = adManager.getEntityList(Env.getOrgRrn(), WorkStation.class, 1, whereClause, null);
				if (CollectionUtils.isNotEmpty(stations)) {
					setAutoEdcType(stations.get(0).getLabelConfigName());
					
					// 说明可以做自动数据收集
					Composite com = toolkit.createComposite(parent, SWT.NONE);
					GridLayout layout = new GridLayout(2, false);
					layout.verticalSpacing = 0;
					layout.horizontalSpacing = 5;
					layout.marginWidth = 5;
					layout.marginHeight = 0;
					com.setLayout(layout);
					
					if (TYPE_REST.equals(getAutoEdcType())) {
						SquareButton auto = UIControlsFactory.createButton(com, "Default");
						auto.setText(Message.getString("wip.autodcop"));
						String finalEquipmentId = equipmentId;
						auto.addSelectionListener(new SelectionAdapter() {
							public void widgetSelected(SelectionEvent event) {
								try {
									AutoEventBroker.subscribe(eventHandler);
									CamelMessageUtil.autoEdc(edcSet.getName(), finalEquipmentId);
								} catch (Exception e) {
									ExceptionHandlerManager.asyncHandleException(e);
								}
							}
						});
					} else {
						Label label = toolkit.createLabel(com, "     ");
						label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BUTTON_GRAY_BG));
						SquareButton auto = UIControlsFactory.createButton(com, "Default");
						auto.setText(Message.getString("edc.auto_connect"));
						auto.setData(true);
						String finalEquipmentId = equipmentId;
						auto.addSelectionListener(new SelectionAdapter() {
							public void widgetSelected(SelectionEvent event) {
								try {
									if (DBUtil.toBoolean(auto.getData())) {
										CamelMessageUtil.autoEdc(edcSet.getName(), finalEquipmentId, CamelMessageUtil.getAutoEdcResponseUrl());
										if (connetFlag) {
											AutoEventBroker.subscribe(eventHandler);
											auto.setText(Message.getString("edc.auto_disconnect"));
											label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_GREEN));
											auto.setData(false);
										} 
										
									} else {
										CamelMessageUtil.autoEdcFinish(edcSet.getName(), finalEquipmentId);
										AutoEventBroker.unsubscribe(eventHandler);
										label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BUTTON_GRAY_BG));
										auto.setText(Message.getString("edc.auto_connect"));
										auto.setData(true);
										connetFlag = false;
									}
									
								} catch (Exception e) {
									ExceptionHandlerManager.asyncHandleException(e);
								}
							}
						});
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}

	protected EventHandler eventHandler = new EventHandler() {
		@Override
		public void handleEvent(Event event) {
			Object obj = event.getProperty(IEventBroker.DATA);
			Gson gson = new Gson();
			CamelMessage message = gson.fromJson(obj.toString(), CamelMessage.class);
			if (message.isSuccess()) {
				Display.getDefault().syncExec(new Runnable() {
					public void run() {
						edcForm.loadAutoData(message);
					}
				});
			}
		}
	};
	
	protected EventHandler connectHandler = new EventHandler() {
		@Override
		public void handleEvent(Event event) {
			Object obj = event.getProperty(IEventBroker.DATA);
			Gson gson = new Gson();
			CamelMessage message = gson.fromJson(obj.toString(), CamelMessage.class);
			connetFlag = message.isSuccess();
		}
	};
	
	protected void createBasicInfoFrom(Composite parent, FormToolkit toolkit) {
		Composite com = toolkit.createComposite(parent, SWT.NONE);
		GridLayout layout = new GridLayout(2, false);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 5;
		layout.marginWidth = 5;
		layout.marginHeight = 0;
		com.setLayout(layout);
		com.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		EdcBaseInfoForm form = new EdcBaseInfoForm(com, SWT.BORDER, lot);
		GridData gd = new GridData(GridData.FILL_BOTH);
		form.setLayoutData(gd);
	}
	
	protected  void createEdcScorllForm(Composite parent, FormToolkit toolkit){
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		GridLayout gd = new GridLayout(1, true);
		gd.marginTop = -10;
		gd.marginLeft = -5;
		composite.setLayout(gd); 
		composite.setLayoutData(new GridData(GridData.FILL_BOTH)); 
		
		List<ComponentUnit> componentUnits = new ArrayList<ComponentUnit>();
		try {
			if (edcSet.getIsByComponent()) {	
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				for (EdcData data : lastDcDatas) {
					ComponentUnit componentUnit = componentManager.getComponentByComponentId(Env.getOrgRrn(), data.getComponentList());
					if (!componentUnits.contains(componentUnit)) {
						componentUnits.add(componentUnit);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}	
		
		if (edcSet instanceof EdcItemSet) {
			List<EdcItemSetLine> itemSetLines = ((EdcItemSet) edcSet).getItemSetLines().stream()
					.filter(l -> !EdcItemSetLine.DATA_TYPE_FORMULA.equals(l.getDataType()))
					.collect(Collectors.toList());
			
			edcForm = new EdcScorllForm(composite,
					 edcCurrent, lot, itemSetLines, lastDcDatas, toolkit);
		} else if (edcSet instanceof EdcBinSet) {
			List<EdcBinSet> objects = new ArrayList<EdcBinSet>();
			objects.add((EdcBinSet) edcSet);
			if (edcSet.getIsByComponent()) {			
				edcForm = new BinEdcScrollFormComponent(composite,
						 edcCurrent, lot, componentUnits, lastDcDatas, toolkit, (EdcBinSet)edcSet);
			} else {
				edcForm = new BinEdcScrollForm(composite,
						 edcCurrent, lot, objects, lastDcDatas, toolkit);
			}
			
		} else if (edcSet instanceof EdcTextSet) {
			List<EdcTextSet> objects = new ArrayList<EdcTextSet>();
			objects.add((EdcTextSet) edcSet);
			if (edcSet.getIsByComponent()) {
				edcForm = new TextEdcScrollFormComponent(composite,
						 edcCurrent, lot, componentUnits, lastDcDatas, toolkit, (EdcTextSet)edcSet);
			} else {
				edcForm = new TextEdcScrollForm(composite,
					 edcCurrent, lot, objects, lastDcDatas, toolkit);
			}
		}	
		edcForm.createContent();
	}
	
	public List<EdcData>  getEdcDatas() {
		return edcForm.getEdcDatas();
	}
	
	public void clear() {
		edcForm.clearEditorTableData();
	}
	
	public void setSelectButtonEnable() {
		edcForm.setSelectButtonEnable();
	}
	
	public boolean validate() {
		return edcForm.validate();
	}
	
	public void upload(EdcUpload upload) {
		if(edcForm != null) {
			edcForm.upload(upload);
		}
	}
	
	public String getAutoEdcType() {
		return autoEdcType;
	}

	public void setAutoEdcType(String autoEdcType) {
		this.autoEdcType = autoEdcType;
	}

	public void disConnect() {
		if (edcForm instanceof EdcScorllForm) {
			edcForm.disConnect();
			
			IEventBroker eventBroker = AutoEventBroker.getiEventBroker();
			if (eventBroker != null) {
				eventBroker.unsubscribe(eventHandler);
				eventBroker.unsubscribe(connectHandler);
			}
		}
	}
}
