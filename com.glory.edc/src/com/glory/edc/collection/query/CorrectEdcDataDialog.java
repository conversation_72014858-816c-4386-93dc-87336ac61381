package com.glory.edc.collection.query;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.client.EDCManager;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcResult;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class CorrectEdcDataDialog extends BaseDialog{
	
	protected CorrectEdcDataDialogForm correctForm;
	protected FFormToolKit toolkit;
	protected Section section;
	protected EdcData edcData;
	protected EdcItemSet edcSet;
	protected EdcResult dcResult;
	protected Lot lot;
	
	protected ToolItem itemSave;
	protected ToolItem itemClose;
	
	protected boolean isTemp;
	
	private static int MIN_DIALOG_WIDTH = 840;
	private static int MIN_DIALOG_HEIGHT = 600;
	
	protected CorrectEdcDataDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public CorrectEdcDataDialog(Shell parent, Lot lot, EdcItemSet edcSet, EdcData edcData) {
		this(parent);
		this.edcData = edcData;
		this.edcSet = edcSet;
		this.lot = lot;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		toolkit = new FFormToolKit(parent.getDisplay());
		GridLayout layout = new GridLayout(1, true);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		layout.horizontalSpacing = 0;
		parent.setLayout(layout);
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
		
	    createToolBarComposite(parent) ;
		setInitData();
		createDialogForm(parent);
		return parent;
	}
	
	protected void setInitData() {
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			//抓取历史数据
			if(this.edcSet instanceof EdcItemSet) {
				edcSet = (EdcItemSet)edcManager.getActualEdcSet(edcData.getEdcSetRrn(), null, null);
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	protected void createDialogForm(Composite parent) {
		correctForm = new CorrectEdcDataDialogForm(parent, lot, edcSet, edcData, toolkit);
		correctForm.createForm();
	}
	
	protected void createToolBarComposite(Composite parent) {
	    section = toolkit.createSection(parent, Section.DESCRIPTION | Section.NO_TITLE | FFormSection.FFORM);
		section.setText(Message.getString("edc.edc_shellTitle"));
		section.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		GridLayout gridLayout = new GridLayout();
		section.setLayout(gridLayout);
		
		Composite client = toolkit.createComposite(section);
		
	    createToolBar(section);
	    gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		client.setLayout(gridLayout);
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		client.setLayoutData(gd);
		section.setClient(client);	

	}
	
	protected void createToolBar(Composite parent) {
		ToolBar tBar = new ToolBar(parent, SWT.HORIZONTAL | SWT.FLAT);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClose(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}
	
	protected void createToolItemClose(ToolBar tBar) {
		itemClose = new ToolItem(tBar, SWT.PUSH);
		itemClose.setText(Message.getString(ExceptionBundle.bundle.CommonExit()));
		itemClose.setImage(SWTResourceCache.getImage("delete"));
		itemClose.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmExit()))) {
					setReturnCode(CANCEL);
					close();
				}
			}
		});
	}
	
	protected void saveAdapter() {
		try {
			if (!correctForm.validate()) {
				return;
			}
			List<EdcData> dcDatas = correctForm.getEdcDatas();
			//检查强制输入
			if (!checkMandatory(dcDatas)) {
				return;
			}
			if (dcDatas == null || dcDatas.size() < 1){
				//不允许所有采集项采集数据为空
				UI.showError(Message.getString("edc.cannot_save"), Message.getString("edc.alert_message_title"));
				return;
			}
			if (UI.showConfirm(Message.getString("edc.data_cannot_change"))){
				saveData(dcDatas);
				super.okPressed();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void saveData(List<EdcData> datas) throws Exception {
		EDCManager edcManager = Framework.getService(EDCManager.class);
		ADManager adManager = Framework.getService(ADManager.class);
		for (EdcData data : datas) {
			//获取数据未拆分时数据库完整数据
			EdcData newdata = (EdcData)adManager.getEntity(edcData);
			//拿到dcdata与dcname键值
			List<String> olddataList = Arrays.asList(newdata.getDcData().split(";",-1));
			List<String> olddataNameList = Arrays.asList(newdata.getDcName().split(";",-1));
			
			//需要修改的dcdata与dcname键值
			List<String> dataList = Arrays.asList(data.getDcData().split(";",-1));
			List<String> dataNameList = Arrays.asList(data.getDcName().split(";",-1));
			
			//循环因为dcdata与dcname两两对应，循环替换dcname键值相同的dcdata下标数据
			for(int e = 0; e < dataNameList.size(); e++) {
				for(int i = 0; i < olddataNameList.size(); i++) {
					if(olddataNameList.get(i).equals(dataNameList.get(e))) {
						olddataList.set(i, dataList.get(e));
					}
				}
			}
			
			//将替换好的数据写入，如果不是拆分数据也无妨按照键值下标匹配的
			if(olddataList.size() > 0) {
				newdata.setDcData(olddataList.stream().map(String :: valueOf).collect(Collectors.joining(";")));
			}
			edcManager.correctEdcData(newdata,Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
		}
	}
	
	/**
	 * 检查是否强制输入，如果强制输入
	 * 1，Variable类型数据必须每个都输入值
	 * 2，Attribute、BIN类型的数据Total必须有值且不能为0
	 * 3.所有选择了选择设备的数据采集都必须选择设备
	 * 4.文本数据采集设置了采集项为必填的检查强制输入
	 */
	public boolean checkMandatory(List<EdcData> dcDatas){

		if (edcSet instanceof EdcItemSet){
			for (EdcItemSetLine line : ((EdcItemSet) edcSet).getItemSetLines()) {
				if (line.getIsShowEquipment()) {
					for (EdcData edcData : dcDatas) {
						if (edcData.getMeasureEqp().equals("") || edcData.getMeasureEqp() == null) {
							return false;
						}
					}
				}
				//如果为必须数据采集
				if (line.getIsMandatory()){
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if(line.getDataType().equals(EdcItem.DATATYPE_ATTRIBUTE)){
								//检查Attribute类型,Total必须有值且不能为0
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								for (int i = 0; i < edcIds.length; i++) {
									if (EdcData.ATTRIBUTE_TOTAL.equals(edcIds[i])) {
										if (i < edcDatas.length) {
											String totalData = edcDatas[i];
											if (totalData.trim().length() > 0 
													&& !"0".equals(totalData.trim())) {
												return true;
											}
										}
									}
								}
								UI.showError(Message.getString("edc.data_mandatory_fail"));
								return false;
							} else {
								//检查Variable类型
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								//检查长度是否相同
								if (!(edcIds.length == edcDatas.length)) {
									UI.showError(Message.getString("edc.data_mandatory_fail"));
									return false;
								}
								//检查每个栏位是否有值
								for (String edcData : edcDatas) {
									if (edcData.trim().length() == 0) {
										UI.showError(Message.getString("edc.data_mandatory_fail"));
										return false;
									}
								}
							}
						}
					}
				}
			}
		}
		return true;
	}
	
	public EdcResult getDcResult() {
		return dcResult;
	}

	public void setDcResult(EdcResult dcResult) {
		this.dcResult = dcResult;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		
	}

}
