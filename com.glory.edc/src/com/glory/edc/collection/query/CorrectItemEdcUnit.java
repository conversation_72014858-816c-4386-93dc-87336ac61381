package com.glory.edc.collection.query;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.edc.attr.collection.EdcDataTableCompositeAttr;
import com.glory.edc.collection.correct.CorrectEdcDataTableComposite;
import com.glory.edc.extensionpoints.IEdcUnit;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.mes.wip.model.Lot;

public class CorrectItemEdcUnit implements IEdcUnit {

	public Composite createCorrectItemEdcUnit(Composite parent,EdcData edcData) {
//		Group group = new Group(parent, SWT.NONE);
//		GridLayout layout = new GridLayout(1, true);
//		layout.numColumns = 1;
//		layout.marginRight = 1;
//		layout.marginLeft = 1;
//		layout.verticalSpacing = 3;
//		group.setLayout(layout);
//		
////		EdcItemSetLine edcLine = (EdcItemSetLine)object;
//
//		GridData gd = new GridData(GridData.FILL_VERTICAL);
//		gd.widthHint = 350;
//		group.setLayoutData(gd);//在数据采集项后面加上中文描述。
//		group.setText(edcLine.getName() + "<"+edcLine.getEdcItemDesc()+">");
//		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		//如果是ATTRIBUTE进入不同的TableComposite
//		if (EdcItem.DATATYPE_ATTRIBUTE.equals(((EdcItemSetLine)edcLine).getDataType())){
//			EdcDataTableCompositeAttr form = new EdcDataTableCompositeAttr(group, lot, edcLine);
//			form.createForm();
//			return form;
//		}
		CorrectEdcDataTableComposite edcUnit = new CorrectEdcDataTableComposite(parent,edcData);
		edcUnit.createForm();
		return edcUnit;
	}
	public Composite createUnit(Composite parent, Lot lot, Object object) {
		return parent;
//		Group group = new Group(parent, SWT.NONE);
//		GridLayout layout = new GridLayout(1, true);
//		layout.numColumns = 1;
//		layout.marginRight = 1;
//		layout.marginLeft = 1;
//		layout.verticalSpacing = 3;
//		group.setLayout(layout);
//		
//		EdcItemSetLine edcLine = (EdcItemSetLine)object;
//		
//		GridData gd = new GridData(GridData.FILL_VERTICAL);
//		gd.widthHint = 350;
//		group.setLayoutData(gd);//在数据采集项后面加上中文描述。
//		group.setText(edcLine.getName() + "<"+edcLine.getEdcItemDesc()+">");
//		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
//		
//		//如果是ATTRIBUTE进入不同的TableComposite
//		if (EdcItem.DATATYPE_ATTRIBUTE.equals(((EdcItemSetLine)edcLine).getDataType())){
//			EdcDataTableCompositeAttr form = new EdcDataTableCompositeAttr(group, lot, edcLine);
//			form.createForm();
//			return form;
//		}
//		CorrectEdcDataTableComposite edcUnit = new CorrectEdcDataTableComposite(group, lot, edcLine);
//		edcUnit.createForm();
//		return edcUnit;
	}

	@Override
	public void disConnect() {
		// TODO Auto-generated method stub
		
	}
	@Override
	public Composite createUnit(Composite parent, Lot lot, Object object, CTabFolder lastTabFolder) {
		// TODO Auto-generated method stub
		return null;
	}
	
}
