package com.glory.edc.collection.query.adapter;

import com.glory.edc.model.EdcData;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;

public class EdcGeneralDataAdapter extends ListItemAdapter<EdcData> {

	public static final String DATA_PREFIX = "DATA_";
	public static final String ATTR_PREFIX = "ATTR_";

	@Override
	public String getText(Object object, String id) {
		if (object != null && id != null) {
			if (id.startsWith(DATA_PREFIX)) {
				try {
					int index = Integer.parseInt(id.substring(
							DATA_PREFIX.length(), id.length()));
					EdcData edcData = (EdcData) object;
					String[] rawDataString = edcData.getRawDataString();
					if (rawDataString.length >= index) {
						// DCData中第一个为Total,从第二个开始取
						return rawDataString[index - 1];
					} else {
						return "";
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			if (id.startsWith(ATTR_PREFIX)) {
				try {
					int index = Integer.parseInt(id.substring(ATTR_PREFIX.length(), id.length()));
					EdcData edcData = (EdcData) object;
					String[] rawDataString = edcData.getRawDataString();
					if (rawDataString.length > index ) {
						//DCData中第一个为Total,从第二个开始取
						return rawDataString[index];
					} else {
						return "";
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			} 
		}
		return super.getText(object, id);
	}
}
