package com.glory.edc.collection.query;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.collection.query.adapter.EdcGeneralDataAdapter;
import com.glory.edc.collection.query.adapter.EdcGeneralDataTableManager;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.EntityQueryProgress;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.entitymanager.forms.ViewQueryProgress;
import com.glory.framework.base.ui.dialog.QueryProgressMonitorDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class DataQuerySection extends QueryEntityListSection implements
		IRefresh {

	private static final Logger logger = Logger.getLogger(DataQuerySection.class);

	public static final String FIELD_DATASETNAME = "edcSetRrn";
	public static final String FIELD_DATAITEMNAME = "itemName";

	protected ADTable adTable;
	protected EdcData edcData;
	protected EdcItemSet edcSet = new EdcItemSet();

	String whereClause;
	
	protected ToolItem itemImport;
	
	protected ToolItem itemLotEdc;
	protected ToolItem itemEqpEdc;

	public DataQuerySection(ADTable adTable,
			EdcGeneralDataTableManager tableManager) {
		super(tableManager);
		this.adTable = adTable;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemEdit(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemLotEdc(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemEqpEdc(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void queryAdapter() {
		try {
			refreshAdapter(true, null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createToolItemEdit(ToolBar tBar) {
		itemEdit = new AuthorityToolItem(tBar, 8, getADTable().getAuthorityKey() + "." + "Edit");
		itemEdit.setText(Message.getString(ExceptionBundle.bundle.CommonEdit()));                                      
		itemEdit.setImage(SWTResourceCache.getImage("edit"));                                    
		itemEdit.setEnabled(true);                                    
		itemEdit.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				editAdapter();
			}
		});
	}
	
	@Override
	protected void editAdapter() {                                                                       
		try {   
			if (tableManager.getSelectedObject() == null) {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			edcData = (EdcData)tableManager.getSelectedObject();
			Long objectRrn = edcData.getObjectRrn();
			
			ADManager adManager = Framework.getService(ADManager.class);
			edcSet.setObjectRrn(((EdcData)getSelectedObject()).getEdcSetRrn());
			edcSet = (EdcItemSet) adManager.getEntity(edcSet);
			edcData = (EdcData)getSelectedObject();
			edcData.setObjectRrn(objectRrn);
			if (getSelectedObject() != null) {   
				EdcData edcData = (EdcData) getSelectedObject();
				edcData = (EdcData) adManager.getEntity(edcData);
				CorrectEdcDataDialog dialog = new CorrectEdcDataDialog(UI.getActiveShell(), getLot(), edcSet, edcData);
				if (dialog.open() == 0) {                                                                      
					this.queryAdapter();
					refresh();
				}                                                                                              
			} else {                                                                                         
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));                                     
			}     
		} catch (Exception e1) {                                                                           
			ExceptionHandlerManager.asyncHandleException(e1);                                                
		}                                                                                                  
	}
	
	protected void createToolItemLotEdc(ToolBar tBar) {
		itemLotEdc = new ToolItem(tBar, 8);
		itemLotEdc.setText(Message.getString("common.lotedc"));                                      
		itemLotEdc.setImage(SWTResourceCache.getImage("reserve"));                                    
		itemLotEdc.setEnabled(true);                                    
		itemLotEdc.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				lotEdcAdapter();
			}
		});
	}
	
	protected void lotEdcAdapter() {
		try {
			String edcFrom = "OFFLINELOT', 'LOT";
			refreshAdapter(true, edcFrom);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}                                                                    

	
	protected void createToolItemEqpEdc(ToolBar tBar) {
		itemEqpEdc = new ToolItem(tBar, 8);
		itemEqpEdc.setText(Message.getString("common.eqpedc"));                                      
		itemEqpEdc.setImage(SWTResourceCache.getImage("reserve"));                                    
		itemEqpEdc.setEnabled(true);                                    
		itemEqpEdc.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				eqpEdcAdapter();
			}
		});
	}
	
	protected void eqpEdcAdapter() {
		try {
			refreshAdapter(true, EdcData.EDCFROM_GENERAL);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}                                                                    

	
	private Lot getLot() {
		Lot lot;
		try {
			EdcData edcData = (EdcData) getSelectedObject();
			LotManager lotmanager = Framework.getService(LotManager.class);
			lot = lotmanager.getLot(edcData.getLotRrn());
		} catch (ClientException e) {
			return null;
		} catch (Exception e) {
			return null;
		}
		return lot;
	}

	@Override
	public void refresh() {
		try {		
			long count = getEntityNumber();
			List<Object> adList = new ArrayList<Object>();
			if (count > getMonitorThreshold()) {
				EntityQueryProgress progress;
				if (tableManager.getADTable().getIsView()) {
					progress = new ViewQueryProgress(getADManger(), count, getProcessThreshold(), tableManager.getADTable(), getWhereClause(), "", getParameterMap());
				} else {
					progress = new EntityQueryProgress(getADManger(), count, getProcessThreshold(), tableManager.getADTable(), getWhereClause(), "", getParameterMap());
				}
				QueryProgressMonitorDialog progressDiglog = new QueryProgressMonitorDialog(UI.getActiveShell(), "");
				progressDiglog.run(true, true, progress);
				adList = progress.getAdList();
			} else {
				ADManager manager = getADManger();
				if (tableManager.getADTable().getIsView()) {
					List<Map> currentList = manager.getEntityMapListByColumn(Env.getOrgRrn(), tableManager.getADTable().getObjectRrn(), 
							0, Env.getMaxResult(), getWhereClause(), "", false);
					adList.addAll(currentList);
				} else {
					if (tableManager.getADTable().isContainMainAttribute()) {
						List<ADBase> currentList = manager.getEntityList(Env.getOrgRrn(), tableManager.getADTable().getObjectRrn(), 
								0, Env.getMaxResult(), getWhereClause(), "", true, tableManager.getADTable().getMainAttributes(), getParameterMap());
						adList.addAll(currentList);
					} else {
						List<ADBase> currentList = manager.getEntityList(Env.getOrgRrn(), tableManager.getADTable().getObjectRrn(), 0, 
								Env.getMaxResult(), getWhereClause(), "", getParameterMap());
						adList.addAll(currentList);
					}
				}
			}
			showNumber = adList.size();
			createSectionDesc(section);
			List<EdcData> datas = processList(adList);
			tableManager.setInput(datas);
		} catch(Exception e) {
			logger.error("Error at Refresh ", e);
		}
	}
	
	/*
	 * 特殊处理EdcData数据，实际测量的数据超过设置的EdcSet时：
	 * 例如：设置测量点数384点，测量片数1片，实际测量片数2片 
	 * 数据存储为：ComponentList(片1；片2) DcData(384 * 2) 界面查询数据显示wafer两片，data数只显示片1数据。
	 * 数据显示异常，做如下处理：
	 * 1.当DcData数能被SubgroupSize整除且结果超过1时，将EdcData数据一分为多
	 * 2.不满足以上条件保留当前数据显示
	*/
	private List<EdcData> processList(List<Object> adList){
		List<EdcData> newEdcDatas = new ArrayList<>();
		try {
			for(Object object : adList) {
				EdcData edcData = (EdcData) object;
				String [] data = edcData.getDcData().split(";",-1);
				String [] dataName = edcData.getDcName().split(";",-1);
				List<String> dataList = Arrays.asList(data);
				List<String> dataNameList = Arrays.asList(dataName);
				List<String> currList = null;
				List<String> dcNameCurrList = null;
				if(edcData.getSubgroupSize() != null && edcData.getSubgroupSize() != 0 
						&& data.length % edcData.getSubgroupSize() == 0 && edcData.getComponentList() != null) {
					int subgroupSize = edcData.getDcData().split(";",-1).length / edcData.getComponentList().split(";",-1).length;
					edcData.setSubgroupSize((long)subgroupSize);
					int length = (int) (data.length / edcData.getSubgroupSize());
					String [] component = edcData.getComponentList().split(";",-1);
					if(length > 1 && component.length == length) {
						for(int i = 0; i < length; i++) {
							EdcData cloneData = (EdcData) edcData.clone();
							currList = dataList.subList(i * edcData.getSubgroupSize().intValue(), (i + 1) * edcData.getSubgroupSize().intValue());
							dcNameCurrList = dataNameList.subList(i * edcData.getSubgroupSize().intValue(), (i + 1) * edcData.getSubgroupSize().intValue());
							cloneData.setComponentList(component[i]);
							cloneData.setDcData(currList.stream().map(String :: valueOf).collect(Collectors.joining(";")));
							cloneData.setDcName(dcNameCurrList.stream().map(String :: valueOf).collect(Collectors.joining(";")));
							cloneData.setObjectRrn(edcData.getObjectRrn());
							newEdcDatas.add(cloneData);
						}
					}else {
						newEdcDatas.add(edcData);
					}
				} else {
					newEdcDatas.add(edcData);
				}
			}
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
		}
		return newEdcDatas;
	}
	
	protected void refreshAdapter(boolean byDcName, String edcFrom) {
		managedForm.getMessageManager().removeAllMessages();
		if (!getQueryForm().validate()) {
			return;
		}
		Object edcSetRrn = getQueryForm().createConditionMap().get(FIELD_DATASETNAME);
		Object itemName = getQueryForm().createConditionMap().get(FIELD_DATAITEMNAME);

		try {
			EdcItemSet itemSet = new EdcItemSet();
			itemSet.setObjectRrn(Long.valueOf(edcSetRrn.toString()));
			ADManager adManager = Framework.getService(ADManager.class);
			itemSet = (EdcItemSet) adManager.getEntity(itemSet);
			ADTable cloneTable = (ADTable) adTable.clone();
			cloneTable.setObjectRrn(adTable.getObjectRrn());
		
			
			List<ADField> cloneFields = new ArrayList<ADField>();
			for (ADField adField : adTable.getFields()) {
				cloneFields.add(adField);
			}
			int count = 1;
			String condition = "edcSetName='" + itemSet.getName()
					+ "' and itemName='" + String.valueOf(itemName).trim() +  "'";
			if (edcFrom != null) {
				condition = condition.substring(0, condition.length() - 1);
				condition += "' and edcFrom in ('"+ edcFrom + "')" ;
			}
			
			// 取最大的SubgroupSize的一条记录
			List<EdcData> datas = null;
			if (byDcName) {
				datas = adManager.getEntityList(
						Env.getOrgRrn(), EdcData.class, 1,
						condition, " subgroupSize DESC, measureTime DESC");
			} else {
				datas = adManager.getEntityList(
						Env.getOrgRrn(), EdcData.class, 1,
						condition, " subgroupSize DESC");
			}

			if (datas.size() == 0 || datas == null) {
				count = 0;
			} else {
				EdcData data = datas.get(0);
				if (EdcItem.DATATYPE_VARIABLE.equals(data.getDataType()) || EdcItemSetLine.DATA_TYPE_FORMULA.equals(data.getDataType())) {
					// VARIABLE类型subgroupSize一定会有值
					if(EdcItemSetLine.DATA_TYPE_FORMULA.equals(data.getDataType())) {
						count = data.getDcData().split(";",-1).length / data.getComponentList().split(";",-1).length;
						data.setSubgroupSize((long)count);
					}else {
						count = data.getSubgroupSize().intValue();
					}
					
					String[] dcName = data.getDcName().split(";");
					
					for (int i = 1; i <= dcName.length; i++) {
						ADField itemField = new ADField();
						itemField.setName(EdcGeneralDataAdapter.DATA_PREFIX + i);
						itemField.setIsDisplay(true);
						itemField.setIsMain(true);
						itemField.setDisplayLength(24L);
						if (byDcName) {
							itemField.setLabel(dcName[i - 1]);
							itemField.setLabel_zh(dcName[i - 1]);
						} else {
							itemField.setLabel(itemField.getName());
							itemField.setLabel_zh(itemField.getName());
						}
						cloneFields.add(itemField);
					}
				}
				if (EdcItem.DATATYPE_ATTRIBUTE.equals(data.getDataType())) {
					String[] row = data.getDcName().split(";",-1);
					count = row.length;
					
					for (int i = 1; i < count; i++) {
						ADField itemField = new ADField();
						itemField.setName(EdcGeneralDataAdapter.ATTR_PREFIX + i);
						itemField.setIsDisplay(true);
						itemField.setIsMain(true);
						itemField.setDisplayLength(24L);
						itemField.setLabel(row[i]);
						itemField.setLabel_zh(row[i]);
						cloneFields.add(itemField);
					}
				}
			}

			cloneTable.setFields(cloneFields);
			EdcGeneralDataTableManager binTableManager = (EdcGeneralDataTableManager) tableManager;
			binTableManager.setADTable(cloneTable);
			binTableManager.refresh();

			String whereClause = " 1 = 1 " + getQueryForm().createWhereClause();
			whereClause = StringUtil.relpaceWildcardCondition(whereClause);
			setWhereClause(whereClause);
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
