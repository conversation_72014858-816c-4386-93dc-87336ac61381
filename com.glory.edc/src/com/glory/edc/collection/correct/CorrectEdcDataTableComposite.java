package com.glory.edc.collection.correct;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.edc.collection.EdcDataItem;
import com.glory.edc.collection.EdcDataTableManager;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.runtime.Framework;

/**
 * 只用于修改数据,不进行任何相关检查,也不传递给SPC
 */
public class CorrectEdcDataTableComposite extends Composite {

	private FixEditorTableManager tableManager;
	private EdcData edcData;
	
	public CorrectEdcDataTableComposite(Composite parent, EdcData edcData) {
		super(parent, SWT.NONE);
		this.edcData = edcData;
	}

	public void createForm() {
		GridLayout layout = new GridLayout(1, true);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		
		setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createUpperComponent(this);
		if (EdcItem.DATATYPE_VARIABLE.equals(edcData.getDataType())) {
			createTableComponentVariale(this);
		} else {
			createTableComponentAttribute(this);
		}
	}
	
	protected Composite createUpperComponent(final Composite group) {
		return null;
	}
	
	public void createTableComponentVariale(Composite composite) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "WIPEdcDataItem");
			tableManager = new EdcDataTableManager(adTable, edcData.getUsl(), edcData.getLsl());
			tableManager.newViewer(composite);
			
			List<EdcDataItem> dataItems = new ArrayList<EdcDataItem>();
			if (edcData != null) {
				//从历史数据中抓取
				String[] dcDatas = null;
				if (edcData.getDcData() != null) {
					dcDatas = edcData.getDcData().split(";");
				}
				String[] dcNames = null;
				if (edcData.getDcName() != null) {
					dcNames = edcData.getDcName().split(";");
				}
				String[] dcRemarks = null;
				if (edcData.getDcRemark() != null) {
					dcRemarks = edcData.getDcRemark().split(";");
				}
				if (dcNames != null) {
					for(int i = 0; i < dcNames.length; i++) {
						EdcDataItem dataItem = new EdcDataItem();
						dataItem.setName(dcNames[i]);
						if (dcDatas != null && dcDatas.length > i) {
							dataItem.setValue(dcDatas[i]);
						}
						if (dcRemarks != null && dcRemarks.length > i) {
							dataItem.setRemark(dcRemarks[i]);
						}
						dataItems.add(dataItem);
					}
				}
			}
			tableManager.setInput(dataItems);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void createTableComponentAttribute(Composite composite) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "WIPEdcDataAttributItem");
			tableManager = new EdcDataTableManager(adTable);
			tableManager.newViewer(composite);
			
			List<EdcDataItem> dataItems = new ArrayList<EdcDataItem>();
			if (edcData != null) {
				//从历史数据中抓取
				String[] dcDatas = edcData.getDcData().split(";");
				String[] dcNames = edcData.getDcName().split(";");
				String[] dcRemarks = edcData.getDcRemark().split(";");
				for(int i = 0; i < dcDatas.length; i++){
					EdcDataItem dataItem = new EdcDataItem();
					dataItem.setValue(dcDatas[i]);
					if (dcNames.length > i) {
						dataItem.setName(dcNames[i]);
					}
					if (dcRemarks.length > i) {
						dataItem.setRemark(dcRemarks[i]);
					}
					dataItems.add(dataItem);
				}
			}
			tableManager.setInput(dataItems);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public EdcData saveEdcData() {
		List<EdcDataItem> dataItems = (List)tableManager.getInput();
		String value = null;
		String remark = null;
		for (EdcDataItem dataItem : dataItems) {
			if (value == null) {
				value = DBUtil.toString(dataItem.getValue());		
				remark = DBUtil.toString(dataItem.getRemark());
			} else {
				value += ";" + DBUtil.toString(dataItem.getValue());
				remark += ";" + DBUtil.toString(dataItem.getRemark());
			}
		}
		edcData.setDcData(value);
		edcData.setDcRemark(remark);
		return edcData;
	}

	public FixEditorTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(FixEditorTableManager tableManager) {
		this.tableManager = tableManager;
	}

	public void setEdcData(EdcData edcData) {
		this.edcData = edcData;
	}
	
	public EdcData getEdcData() {
		edcData.setOrgRrn(Env.getOrgRrn());
		edcData.setOrgId(Env.getOrgName());
		List<EdcDataItem> dataItems =(List<EdcDataItem>)tableManager.getInput();
		if(dataItems == null || dataItems.size() == 0){
			return null;
		}
		
		String name = null;
		String value = null;
		String remark = null;
		for (EdcDataItem dataItem : dataItems) {
			if (name == null) {
				name = DBUtil.toString(dataItem.getName());
				value = DBUtil.toString(dataItem.getValue());
				remark = DBUtil.toString(dataItem.getRemark());
			} else {
				name += ";" + DBUtil.toString(dataItem.getName());
				value += ";" + DBUtil.toString(dataItem.getValue());
				remark += ";" + DBUtil.toString(dataItem.getRemark());
			}
		}
		edcData.setDcName(name); 
		edcData.setDcData(value); 
		edcData.setDcRemark(remark);
		
		edcData.setDcDataAvg(DBUtil.toDouble(edcData.buildDcDataAvg()));
		edcData.setOosList(edcData.buildOosList());
		return edcData;
	}
	
}
