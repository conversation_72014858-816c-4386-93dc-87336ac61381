package com.glory.edc.collection;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;

import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class SelectComponentDialog4Edc extends SelectComponentDialog {

	private static final String TABLENAME_COMPONENT = "EDCComponentUnit";
	 
	protected AbstractEdcSet edcSet;
	protected List<EdcData> lastDcDatas = new ArrayList<EdcData>();
	
	public SelectComponentDialog4Edc(Shell parent, int waferNumber, Lot lot, AbstractEdcSet edcSet) {
		super(parent, waferNumber, lot, null, null);
		this.edcSet = edcSet;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		setTitle(Message.getString("edc.waferId_list_shellTitle"));
		
		parent = CustomCompsite.configureBody(parent);
		parent.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));

		createComponentTable(parent, getComponents());
		return parent;
	}
	
	protected void createComponentTable(Composite parent, List<ComponentUnit> units) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLENAME_COMPONENT);
			tableManager = new CheckBoxFixEditorTableManager(adTable);
			
			List<String> waferIdDone = new ArrayList<String>();
			List<String> waferIdTemp = new ArrayList<String>();
			List<Object> tempComponents = new ArrayList<Object>();
			List<ComponentUnit> doneComponents = new ArrayList<ComponentUnit>();
			
			//取出EDCData用于判断当前ComponentEdc的状态
			StringBuffer whereClause = new StringBuffer(" lotRrn = ");
			whereClause.append(currentLot.getObjectRrn());
			whereClause.append("AND edcSetRrn = '");
			whereClause.append(edcSet.getObjectRrn());
			whereClause.append("' AND CASE componentList WHEN '' THEN NULL ELSE componentList END IS NOT NULL");
			String orderBy = " seqNo ASC";
			ADManager adManager = Framework.getService(ADManager.class);
			List<EdcData> lastDcDatas = adManager.getEntityList(Env.getOrgRrn(), 
					EdcData.class, Integer.MAX_VALUE, whereClause.toString(), orderBy);
			
			for(EdcData lastDcData : lastDcDatas){
				if(lastDcData.getIsTemp()){
					waferIdTemp.add(lastDcData.getComponentList());
				} else {
					waferIdDone.add(lastDcData.getComponentList());
				}
			}
			for (ComponentUnit unit : units) {
				if (waferIdDone.contains(unit.getComponentId())){
					unit.setState(EdcSetCurrent.FLAG_DONE);
					doneComponents.add(unit);
				} else if (waferIdTemp.contains(unit.getComponentId())){
					unit.setState(EdcSetCurrent.FLAG_TEMP);
					tempComponents.add(unit);
				} else {
					unit.setState("");
				}
			}
			tableManager.setInput(units);
			tableManager.setCheckedObject(tempComponents);
			
			tableManager.addICheckChangedListener(new ICheckChangedListener() {
				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if (CollectionUtils.isEmpty(eventObjects)) {
						return;
					}
					List<Object> removeList = new ArrayList<Object>();
					for (Object object : eventObjects) {
						if (checked) {
							ComponentUnit unit = (ComponentUnit)object;
							if (!EdcSetCurrent.FLAG_TEMP.equals(unit.getState())) {
								removeList.add(object);
							}
						}
					}
					if (CollectionUtils.isEmpty(removeList)) {
						tableManager.getCheckedObject().removeAll(removeList);
					}
				}			
			});
			
			tableManager.newViewer(parent);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}  
	
	protected void okButtonAdapter() {
		selectedComponents = new ArrayList<ComponentUnit>();
		selectedComponentIds = new ArrayList<String>();
		selectedComponentRrns = new ArrayList<String>();
		
		List<Object> selectObjs = tableManager.getCheckedObject();
		if (waferNumber < selectObjs.size()) {
			UI.showError(Message.getString("edc_waferId_toomuch")
					+ waferNumber, Message.getString("edc.alert_message_title"));
			return;
		}
		for (Object selectObj : selectObjs) {
			selectedComponents.add(((ComponentUnit)selectObj));
			selectedComponentIds.add(((ComponentUnit)selectObj).getComponentId());
			selectedComponentRrns.add(String.valueOf(((ComponentUnit)selectObj).getObjectRrn()));
		}
		super.okPressed();
	}
}
