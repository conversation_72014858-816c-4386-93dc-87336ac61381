package com.glory.edc.collection;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.edc.client.EDCManager;
import com.glory.edc.item.operation.ShowItemOperationDialog;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTecn;
import com.glory.edc.util.EDCUtil;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class EdcDataTableComposite  extends Composite {
	
	private static final Logger logger = Logger.getLogger(EdcDataTableComposite.class);

	protected FormToolkit toolkit;
	
	protected Label lblSamplePlan;
	protected Label lblUnit;
	protected Label lblSpec;
	
	protected int itemNumber;

	protected Label lblComponent;
	protected SquareButton btnSelectComp;
	
	protected EdcDataTableManager tableManager;
	
	protected Control ctlOperator;
	protected Control ctlEquipment;
	protected JudgeComposite judgeComposite;

	protected EdcItemSetLine edcSetLine;

	protected Lot lot;
	protected EdcSetCurrent edcCurrent;
	protected EdcData currentEdcData;
	
	protected List<String> selectedComponentIds;
	protected List<String> selectedComponentRrns;
  
	public EdcDataTableComposite(Composite parent, Lot lot,  EdcItemSetLine edcSetLine) {
		super(parent, SWT.NONE);
		this.edcSetLine = edcSetLine;
		this.lot = lot;
		this.toolkit = new FFormToolKit(parent.getDisplay());
		
		 itemNumber = edcSetLine.getItem().intValue();
		 if (edcSetLine.getIsItemUsePercent()) {
			 //使用百分比
			 itemNumber = EdcItemSetLine.getActualItemNumber(lot.getMainQty(), new BigDecimal(itemNumber));
			 if (itemNumber < 1) {
				 //不允许小于1
				 itemNumber = 1;
			 }
		 }
	}
	
	public void createForm() {
		GridLayout layout = new GridLayout(1, true);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		
		setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createUpperComponent(this);
		createTableComponent(this);
		
		if (edcSetLine.getIsJudgeByManual()) {
			judgeComposite = new JudgeComposite(this, SWT.BORDER);
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			judgeComposite.setLayoutData(gd);
			judgeComposite.createForm();
		}
	}
	
	public void createTableComponent(Composite composite) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "WIPEdcDataItem");
			tableManager = new EdcDataTableManager(adTable, edcSetLine.getUsl(), edcSetLine.getLsl());
			tableManager.newViewer(composite);
			EdcDataTableManagers.add(tableManager);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public List<EdcDataItem> generateEdcDataItems() {
		List<EdcDataItem> dataItems = new ArrayList<EdcDataItem>();
		String[] dcDatas = null;
		String[] dcDataRemarks = null;
		if (currentEdcData != null) {
			//从历史数据中抓取
			dcDatas = currentEdcData.getDcData().split(";", -1);
			dcDataRemarks = currentEdcData.getDcRemark().split(";", -1);
			if (edcCurrent != null && 
					(edcCurrent.getHistorySeq()!= null || EdcSetCurrent.FLAG_TEMP.equals(edcCurrent.getEdcFlag()))) {
				//使用历史SPEC
				if (edcCurrent.getHistorySeq()!= null) {
					tableManager.setUsl(currentEdcData.getUsl());
					tableManager.setLsl(currentEdcData.getLsl());
					lblSamplePlan.setText(currentEdcData.getSamplePlan() == null ? "" : currentEdcData.getSamplePlan());
					lblUnit.setText(DBUtil.toString(edcSetLine.getItemUnit()));
					lblSpec.setText(currentEdcData.getSpec());
				}
				//设置以保存的操作人和设备
				setMeasureEquipment(this.currentEdcData.getMeasureEqp());
				setOperator(this.currentEdcData.getOperator());
				//检查是否有Component信息
				String components = currentEdcData.getComponentList();
				String componentRrns = currentEdcData.getComponentRrnList();
				if (components != null && components.trim().length() > 0) {
					selectedComponentIds = new ArrayList<String>();
					String[] s1 = components.split(";");
					for (String s : s1) {
						if (s.trim().length() > 0) {
							selectedComponentIds.add(s);
						}
					}
					if (componentRrns != null && componentRrns.trim().length() > 0) {
						selectedComponentRrns = new ArrayList<String>();
						String[] s2 = componentRrns.split(";");
						for (String s : s2) {
							if (s.trim().length() > 0) {
								selectedComponentRrns.add(s);
							}
						}
					}
				} 
			}
			
			if (currentEdcData.getDcName() != null && currentEdcData.getDcName().trim().length() > 0) {
				//如果有DCName
				String[] dcNames = currentEdcData.getDcName().split(";");
				if (dcDatas.length <= dcNames.length) {
					for(int i = 0; i < dcNames.length; i++){
						EdcDataItem dataItem = new EdcDataItem();
						dataItem.setName(dcNames[i]);
						if (i < dcDatas.length) {
							dataItem.setValue(dcDatas[i]);
							dataItem.setRemark(dcDataRemarks[i]);
						} 
						dataItems.add(dataItem);
					}
				}
			}	
		}
		
		if (lblComponent != null) {
			if (selectedComponentIds != null) {
				lblComponent.setText(String.valueOf(selectedComponentIds.size()));	
			} else {
				lblComponent.setText("0");
			}
		}
		
		if (dataItems.size() == 0) {
			boolean isComponnet = isComponentUnitType() && !EdcItem.SAMPLETYPE_ITEM.equals(edcSetLine.getSampleType());
			String[] itemDescs = EdcDataTableComposite.createIds(edcSetLine, selectedComponentIds, isComponnet, itemNumber);
			if (dcDatas != null && dcDatas.length == itemDescs.length) {
				for(int i = 0; i < itemDescs.length; i++){
					EdcDataItem dataItem = new EdcDataItem();
					dataItem.setName(itemDescs[i]);
					dataItem.setValue(dcDatas[i]);
					dataItems.add(dataItem);
				}
			} else {
				for(int i = 0; i < itemDescs.length; i++){
					EdcDataItem dataItem = new EdcDataItem();
					dataItem.setName(itemDescs[i]);
					dataItem.setValue("");
					dataItems.add(dataItem);
				}
			}
		}
		return dataItems;
	}
	
	public void setEdcDatas() {
		tableManager.setInput(generateEdcDataItems());
	}
	
	protected Composite createUpperComponent(final Composite group) {
		Composite composite = toolkit.createComposite(group, SWT.NONE);
		GridLayout layout = new GridLayout(4, false);
		layout.marginRight = 5;
		layout.marginLeft = 5;
		layout.marginHeight = 5;
		layout.verticalSpacing = 5;
		composite.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.LEFT;
		composite.setLayoutData(gd);

		toolkit.createLabel(composite, Message.getString("edc.bylotedc_sampling_plan"));
		lblSamplePlan = toolkit.createLabel(composite, edcSetLine.getSamplePlan(new Long(itemNumber)));
		toolkit.createLabel(composite, Message.getString("edc.bylotedc_unit"));
		lblUnit = toolkit.createLabel(composite, DBUtil.toString(edcSetLine.getItemUnit()));

		toolkit.createLabel(composite, Message.getString("edc.bylotedc_specifications"));
		lblSpec = toolkit.createLabel(composite, edcSetLine.getSpec());			
		toolkit.createLabel(composite, "");
		toolkit.createLabel(composite, "");
		
		if (isComponentUnitType() && !EdcItem.SAMPLETYPE_ITEM.equals(edcSetLine.getSampleType())) {
			toolkit.createLabel(composite, Message.getString("edc.bylotedc_wafer_collection_number"));
			
			lblComponent = toolkit.createLabel(composite, String.valueOf(itemNumber));
			gd = new GridData(80, 20);
			gd.horizontalSpan = 2;
			lblComponent.setLayoutData(gd);
			toolkit.createLabel(composite, "");
			btnSelectComp = UIControlsFactory.createButton(composite, Message
					.getString("common.select"), UIControlsFactory.BUTTON_DEFAULT);
			btnSelectComp.setBounds(185, 53, 60, 29);
			btnSelectComp.setLayoutData(gd);
			btnSelectComp.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					List<ComponentUnit> checkComponentUnits = null;
					try {
						if (edcCurrent != null) {
							ADManager adManager = Framework.getService(ADManager.class);
							AbstractEdcSet edcSet = new AbstractEdcSet();
							edcSet.setObjectRrn(edcCurrent.getItemSetRrn());
							edcSet = (AbstractEdcSet) adManager.getEntity(edcSet);
							if (EdcSetCurrent.TYPE_TECN.equals(edcCurrent.getEdcType())) {		
								//如果为TECN													
								EDCManager edcManager = Framework.getService(EDCManager.class);													
								if (edcSetLine instanceof EdcItemSetLine) {
									List<EdcTecn> lotTecns = edcManager.getTecnByLotCurrentStep(lot.getOrgRrn(), lot.getLotId(), lot.getStepName());
									for (EdcTecn lotTecn : lotTecns) {
										if ((StringUtils.equals(edcSet.getName(), lotTecn.getEdcName()) || StringUtil.isEmpty(lotTecn.getEdcName()))
												&& (StringUtils.equals(edcSetLine.getName(), lotTecn.getEdcItemName()) || StringUtil.isEmpty(lotTecn.getEdcItemName()))) {
											if (!StringUtil.isEmpty(lotTecn.getComponentList())) {
												List<String> componentIds = new ArrayList<String>();
												for (String componentId : lotTecn.getComponentList().split(";")) {
													componentIds.add(componentId);
												}
												ComponentManager componentManager = Framework.getService(ComponentManager.class);
												checkComponentUnits = componentManager.getComponentsByComponentId(Env.getOrgRrn(), componentIds, true);
												break;
											}
											if (!StringUtil.isEmpty(lotTecn.getCompSLotSrc())) {
												Set<String> sLotSrc = new HashSet<String>();
												for (String sLot : lotTecn.getCompSLotSrc().split(";")) {
													sLotSrc.add(sLot);
												}								
												Map<String, Object> fieldMap = new HashMap<String, Object>();
												fieldMap.put("sLotSrc", sLotSrc);
												checkComponentUnits = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, Integer.MIN_VALUE, Integer.MAX_VALUE, " parentUnitRrn = " + lot.getObjectRrn() + " and position in (:sLotSrc) ", " position ", fieldMap);
												if (checkComponentUnits.size() != sLotSrc.size()) {
													if (!UI.showConfirm("TecnName:" + lotTecn.getName() + ", " + Message.getString("edc.tecn_position_component_not_exist"))) {
														return;
													}
												}
											}
										}
									}
									
									List<EdcTecn> contextTecns = edcManager.getTecnByContext(lot, new HashMap<String, String>());
									for (EdcTecn contextTecn : contextTecns) {
										if ((StringUtils.equals(edcSet.getName(), contextTecn.getEdcName()) || StringUtil.isEmpty(contextTecn.getEdcName()))
												&& (StringUtils.equals(edcSetLine.getName(), contextTecn.getEdcItemName()) || StringUtil.isEmpty(contextTecn.getEdcItemName()))) {
											if (!StringUtil.isEmpty(contextTecn.getCompSLotSrc())) {
												Set<String> sLotSrc = new HashSet<String>();
												for (String sLot : contextTecn.getCompSLotSrc().split(";")) {
													sLotSrc.add(sLot);
												}								
												Map<String, Object> fieldMap = new HashMap<String, Object>();
												fieldMap.put("sLotSrc", sLotSrc);
												checkComponentUnits = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, Integer.MIN_VALUE, Integer.MAX_VALUE, " parentUnitRrn = " + lot.getObjectRrn() + " and position in (:sLotSrc) ", " position ", fieldMap);
												if (checkComponentUnits.size() != sLotSrc.size()) {
													if (!UI.showConfirm("TecnName:" + contextTecn.getName() + ", " + Message.getString("edc.tecn_position_component_not_exist"))) {
														return;
													}
												}
											}
										}
									}
								}
							} else if (EdcSetCurrent.TYPE_NORMAL.equals(edcCurrent.getEdcType())) {
								//是否存在抽样
								if (edcSetLine instanceof EdcItemSetLine) {
									LotManager lotManager = Framework.getService(LotManager.class);
									String componentSamplingPlan = null;
									//取第一个有值componentSamplingPlan
			    					if (componentSamplingPlan == null && !StringUtil.isEmpty(edcSet.getComponentSamplingPlan())) {
			    						componentSamplingPlan = edcSet.getComponentSamplingPlan();
			    					}
									List<String> samplingComponentIds = lotManager.getLotSamplingComponent(lot, componentSamplingPlan, Env.getSessionContext());
									if (CollectionUtils.isNotEmpty(samplingComponentIds)) {
										ComponentManager componentManager = Framework.getService(ComponentManager.class);
										checkComponentUnits = componentManager.getComponentsByComponentId(Env.getOrgRrn(), samplingComponentIds, true);
									}
								}
								if (CollectionUtils.isEmpty(checkComponentUnits) && !StringUtil.isEmpty(edcSetLine.getCompSLotSrc())) {
									Set<String> sLotSrc = new HashSet<String>();
									for (String sLot : edcSetLine.getCompSLotSrc().split(",")) {
										sLotSrc.add(sLot);
									}								
									Map<String, Object> fieldMap = new HashMap<String, Object>();
									fieldMap.put("sLotSrc", sLotSrc);
									checkComponentUnits = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, Integer.MIN_VALUE, Integer.MAX_VALUE, " parentUnitRrn = " + lot.getObjectRrn() + " and position in (:sLotSrc) ", " position ", fieldMap);
									if (checkComponentUnits.size() != sLotSrc.size()) {
										if (!UI.showConfirm("EDCSetName:" + edcSet.getName() + ", " + Message.getString("edc.edcsetline_position_component_not_exist"))) {
											return;
										}
									}
								}
							}
							if (CollectionUtils.isNotEmpty(checkComponentUnits)) {
								itemNumber = checkComponentUnits.size();
							}
						}
					} catch (Exception e1) {
						e1.printStackTrace();
					}
					SelectComponentDialog d = new SelectComponentDialog(
							UI.getActiveShell(), itemNumber, lot, selectedComponentIds, selectedComponentRrns, checkComponentUnits);
					if (d.open() == Dialog.OK) {
						if (d.getReturnCode() == IDialogConstants.OK_ID) {
							selectedComponentIds = d.getSelectedComponentIds();
							selectedComponentRrns = d.getSelectedComponentRrns();
							currentEdcData = null;
							setEdcDatas();
						}
					}
				}
			});
			toolkit.createLabel(composite, "");
			toolkit.createLabel(composite, "");
		}

		if (edcSetLine.getIsShowEquipment()) {
			//对多操作人处理
			toolkit.createLabel(composite, Message.getString("wip.operator") + "*");
			if (StringUtil.isEmpty(edcSetLine.getItemSet().getOwner())) {
				ctlOperator = toolkit.createText(composite, Env.getUserName(), SWT.BORDER);
				GridData gText = new GridData(GridData.FILL_HORIZONTAL);		
				gText.horizontalSpan = 3;
				ctlOperator.setLayoutData(gText);
			} else {
				List<String> groupNames = new ArrayList<>();
				for (String owner : edcSetLine.getItemSet().getOwner().split(";")) {
					groupNames.add(owner);
				}
				ctlOperator = RCPUtil.getADUserCombo(composite, groupNames, edcSetLine.getItemSet().getIsMultiOperator(), Env.getUserName());
				GridData gText = new GridData(GridData.FILL_HORIZONTAL);		
				gText.horizontalSpan = 3;
				ctlOperator.setLayoutData(gText);		
			}
			
			//对多设备人处理		
			toolkit.createLabel(composite, Message.getString("ras.equipment") + ":");
			if (edcSetLine.getItemSet().getCapability() == null) {
				GridData gText = new GridData(GridData.FILL_HORIZONTAL);
				ctlEquipment = toolkit.createText(composite, "", SWT.BORDER);				
				gText.horizontalSpan = 3;
				ctlEquipment.setLayoutData(gText);
			
			} else {
				ctlEquipment = EDCUtil.getEdcEquipmentWidget(Env.getOrgRrn(), edcSetLine.getItemSet().getCapability(),
						edcSetLine.getItemSet().getIsMultiEquipment(), lot.getEquipmentId(), composite);
				GridData gCombo = new GridData(GridData.FILL_HORIZONTAL);
				gCombo.horizontalSpan = 3;
				ctlEquipment.setLayoutData(gCombo);
			}
		}
		
		if (edcSetLine.getIsShowOperation()) {
			Button btnShowOperation = toolkit.createButton(composite, Message.getString("edc.item_show_operation"), SWT.PUSH);
			btnShowOperation.setBounds(185, 53, 80, 20);
			gd = new GridData(GridData.FILL_HORIZONTAL);
			btnShowOperation.setLayoutData(gd);
			btnShowOperation.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					ShowItemOperationDialog operationDialog = new ShowItemOperationDialog(UI.getActiveShell(), edcSetLine, lot);
					operationDialog.open();
				}
			});
		}
		
		return composite;
	}
	
	protected boolean isComponentUnitType() {
		if (lot != null) {
			if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				return true;
			} else {
				return false;
			}
		}
		return false;
	}
	 
	public EdcData getEdcData() {
		List<EdcDataItem> dataItems = (List<EdcDataItem>)tableManager.getInput();
		return getEdcData(dataItems);
	}
	
	public EdcData getEdcData(List<EdcDataItem> dataItems) {
		if(dataItems == null || dataItems.size() == 0){
			return null;
		}
		
		if (currentEdcData == null) {
			currentEdcData = new EdcData();
			currentEdcData.setOrgRrn(Env.getOrgRrn());
			currentEdcData.setOrgId(Env.getOrgName());
		}
		
		String name = null;
		String value = null;
		String remark = null;
		for (EdcDataItem dataItem : dataItems) {
			if (name == null) {
				name = DBUtil.toString(dataItem.getName());
				value = DBUtil.toString(dataItem.getValue());
				remark = DBUtil.toString(dataItem.getRemark());
			} else {
				name += ";" + DBUtil.toString(dataItem.getName());
				value += ";" + DBUtil.toString(dataItem.getValue());
				remark += ";" + DBUtil.toString(dataItem.getRemark());
			}
		}
		currentEdcData.setDcName(name); 
		currentEdcData.setDcData(value); 
		currentEdcData.setDcRemark(remark);
		
		currentEdcData.setItemName(edcSetLine.getName());
		
		if (selectedComponentIds != null && selectedComponentIds.size() > 0) {
			String componentStr = null;
			String componentRrns = null;
			for (String componentId : selectedComponentIds) {
				if (componentStr == null) {
					componentStr = componentId;
				} else {
					componentStr = componentStr + ";" + componentId;
				}
			}
			for (String componentRrn : selectedComponentRrns) {
				if (componentRrns == null) {
					componentRrns = componentRrn;
				} else {
					componentRrns = componentRrns + ";" + componentRrn;
				}
			}
			currentEdcData.setComponentList(componentStr);
			currentEdcData.setComponentRrnList(componentRrns);	
		}
	
		currentEdcData.setMeasureTime(new Date());
		currentEdcData.setEdcType(EdcData.EDCTYPE_ITEM);
		currentEdcData.setEdcSetRrn(edcSetLine.getEdcSetRrn());
		currentEdcData.setEdcSetName(edcSetLine.getItemSet().getName());
		currentEdcData.setEdcSetVersion(edcSetLine.getItemSet().getVersion());
		currentEdcData.setItemName(edcSetLine.getName());
		currentEdcData.setSeqNo(edcSetLine.getSeqNo());
		currentEdcData.setUsl(DBUtil.toDouble(edcSetLine.getUsl()));
		currentEdcData.setLsl(DBUtil.toDouble(edcSetLine.getLsl()));
		currentEdcData.setSl(DBUtil.toDouble(edcSetLine.getSl()));
		currentEdcData.setSubgroupSize(edcSetLine.getSubgroupSize() !=null ? edcSetLine.getSubgroupSize().longValue() : null);
		currentEdcData.setSampleSize(edcSetLine.getSampleSize());
		currentEdcData.setSamplePlan(edcSetLine.getSamplePlan(new Long(itemNumber)));
		currentEdcData.setDataType(edcSetLine.getEdcItem().getDataType());
		currentEdcData.setSampleType(edcSetLine.getSampleType());
		if (lot != null) {
			currentEdcData.setLineId(lot.getLineId());
			currentEdcData.setLotRrn(lot.getObjectRrn());
			currentEdcData.setBatchId(lot.getBatchId());
			currentEdcData.setBatchLots("");
			currentEdcData.setLotCount(null);
			currentEdcData.setLotType(lot.getLotType());
			currentEdcData.setLotId(lot.getLotId());
			currentEdcData.setPartName(lot.getPartName());
			currentEdcData.setPartVersion(lot.getPartVersion());
			currentEdcData.setStepName(lot.getStepName());
			currentEdcData.setStepVersion(lot.getStepVersion());
			currentEdcData.setMaterialId(null);
			currentEdcData.setCustomer(lot.getCustomerCode());
			currentEdcData.setMeasureEqp(getMeasureEquipment());
			currentEdcData.setOperator(getCurOperator());
			currentEdcData.setProcessEqp("");
			currentEdcData.setLineId(lot.getLineId());
		}
		currentEdcData.setTeamId(Env.getTeam());
		
		String[] valueStrings = currentEdcData.getDcData().split(";");
		if (valueStrings.length > 0) {
			currentEdcData.setDcDataAvg(DBUtil.toDouble(currentEdcData.buildDcDataAvg()));
			currentEdcData.setOosList(currentEdcData.buildOosList());
		}
		
		if (edcSetLine.getIsJudgeByManual()) {
			currentEdcData.setJudge1(judgeComposite.getJudge());
		}
		
		return currentEdcData;
	}
	
	public String getCurOperator() {
		String operator = null;
		if (ctlOperator  != null) {
			if (ctlOperator instanceof Text) {
				operator = ((Text) ctlOperator).getText();
			}
			if (ctlOperator instanceof XCombo) {
				operator = ((XCombo) ctlOperator).getText();
			}
		} else {
			operator = Env.getUserName();
		}
		return operator;
	}

	public String getMeasureEquipment() {
		String measureEqp = null;
		if (ctlEquipment  != null) {
			if (ctlEquipment instanceof Text) {
				measureEqp = (((Text) ctlEquipment).getText());
			}
			if (ctlEquipment instanceof XCombo) {
				measureEqp = (((XCombo) ctlEquipment).getText());
			}
		}
		if(measureEqp == null || measureEqp.equals("")){
			measureEqp = lot.getEquipmentId();
		}
		return measureEqp;
	}
	
	public void setOperator(String operator) {

		if (ctlOperator != null) {
			if (ctlOperator instanceof Text) {
				((Text) ctlOperator).setText(operator);
			}
			if (ctlOperator instanceof XCombo) {
				((XCombo) ctlOperator).setText(operator);
			}
		} 
	}
	
	public void setMeasureEquipment(String measureEquipment) {

		if (this.ctlEquipment != null && measureEquipment != null) {
			if (ctlEquipment instanceof Text) {
				((Text) this.ctlEquipment).setText(measureEquipment);
			}
			if (ctlEquipment instanceof XCombo) {
				((XCombo) ctlEquipment).setText(measureEquipment);
			}
		} 
	}

	public boolean validate() {
		return true;
	}
	
	public EdcItemSetLine getEdcSetLine() {
		return edcSetLine;
	}

	public void setSelectButtonEnable() {
		if (btnSelectComp != null){
			btnSelectComp.setEnabled(false);
		}
	}
	
	public EdcDataTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(EdcDataTableManager tableManager) {
		this.tableManager = tableManager;
	}
	
	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public EdcSetCurrent getEdcCurrent() {
		return edcCurrent;
	}

	public void setEdcCurrent(EdcSetCurrent edcCurrent) {
		this.edcCurrent = edcCurrent;
	}

	public EdcData getCurrentEdcData() {
		return currentEdcData;
	}

	public void setCurrentEdcData(EdcData currentEdcData) {
		this.currentEdcData = currentEdcData;
	}
	
	public void upload(EdcUpload upload) {
		boolean isComponent = isComponentUnitType() && !EdcItem.SAMPLETYPE_ITEM.equals(edcSetLine.getSampleType());
		EdcData edcData = getEdcData();
		//edcData = upload.getEdcData(edcSetLine, edcData, isComponent, true, itemNumber);
		edcData = upload.getEdcDataUpload(edcSetLine, edcData);
		if (edcData != null && edcData.getDcData() != null) {
			currentEdcData = edcData;
			setEdcDatas();
		}
	}
	
	public static String[] createIds(EdcItemSetLine edcLine, List<String> selectedComponentIds, boolean isComponent, int itemNumber) {
		 String[] itemIds = null;
		 
		 if (isComponent) {
			 if (selectedComponentIds != null && selectedComponentIds.size() > 0) {
				 int i = 0;
				 itemIds = new String[selectedComponentIds.size()];
				 for (String componentId : selectedComponentIds) {
					 itemIds[i] = componentId;
					 i++;
			 	 }
			 }
		 } else {
			itemIds = new String[itemNumber];
			String imString = edcLine.getItemDesc();
			if (imString != null && !"".equals(imString.trim())) {
				String imStr[] = imString.split(";");
				for (int n = 0; n < itemNumber; n++) {
					if (imStr.length > n) {
						itemIds[n] = imStr[n];
					} else {
						itemIds[n] = imStr[(n % imStr.length)];
					}
				}
			} else {
				for (int n = 0; n < itemNumber; n++) {
					itemIds[n] = n + 1 + "";
				}
			}
		 }
		if (EdcItem.SAMPLETYPE_ITEM.equals(edcLine.getSampleType())) {
			return itemIds;
		}
		
		/**GET IDS OF THE COMP
		 */
		if (itemIds == null) {
			return new String[]{};
		}
		int compNumber = edcLine.getComp().intValue();		
		String compIds[] = new String [compNumber];
		String compString = edcLine.getCompDesc();
		if (compString != null && !"".equals(compString.trim())) {
			String compStr[] = compString.split(";");
			for (int n = 0; n < compNumber; n++) {
				if (compStr.length > n)
					compIds[n] = compStr[n];
				else
					compIds[n] = compStr[(n % compStr.length)];
			}
		} else {
			for (int n = 0; n < compNumber; n++) {
				compIds[n] = n + 1 + "";
			}
		}
		
		String comDescs[] = new String [itemIds.length * compIds.length];
		if(EdcItem.SAMPLETYPE_COMP.equals(edcLine.getSampleType())){
			int idIndex = 0;
			for (int i = 0; i < itemIds.length; i++) {
				String str = itemIds[i];
				for (int j = 0; j < compIds.length; j++) {
					comDescs[idIndex] = str + "-" + compIds[j];
					idIndex++;
				}
			}
			return comDescs;
		}
		
		/**GET IDS OF THE SITE
		 */
		int siteNumber = edcLine.getSite().intValue();	
		String siteIds[] = new String[siteNumber];
		String siteString = edcLine.getSiteDesc();
		if (siteString != null && !"".equals(siteString.trim())) {
			String siteStr[] = siteString.split(";");
			for (int n = 0; n < siteNumber; n++) {
				if (siteStr.length > n)
					siteIds[n] = siteStr[n];
				else
					siteIds[n] = siteStr[(n % siteStr.length)];
			}
		} else {
			for (int n = 0; n < siteNumber; n++) {
				siteIds[n] = n + 1 + "";
			}
		}
		String siteDescs[] = new String [itemIds.length * compIds.length * siteIds.length];
		int idIndex = 0;
		for (int i = 0; i < itemIds.length; i++) {
			String str = itemIds[i];
			for (int j = 0; j < compIds.length; j++) {
				String str2 = str + "-" + compIds[j];
				for (int m = 0; m < siteIds.length; m++) {
					siteDescs[idIndex] = str2 + "-" + siteIds[m];
					idIndex++;
				}
			}
		}
		return siteDescs;
	}
}
