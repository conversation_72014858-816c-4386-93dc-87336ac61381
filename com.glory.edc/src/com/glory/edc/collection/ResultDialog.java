package com.glory.edc.collection;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormText;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcResult;
import com.glory.edc.model.spc.SpcResultChart;
import com.glory.edc.model.spc.SpcResultJob;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.framework.core.exception.ExceptionBundle;

public class ResultDialog extends BaseTitleDialog {

	protected Section section;
	protected List<EdcData> dcDatas;
	protected EdcResult dcResult;
	protected String edcFrom;
	protected AbstractEdcSet edcSet;
	
	protected static final String SPC_JOB_NAME_LIST = "spcJobNameList";
	
	// Chart图，Class名称路径
	protected static final String CHART_CLASS_NAME = "com.glory.spc.chart.SpcSimpleChartDialog";
	
	public ResultDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public ResultDialog(Shell parentShell, String edcFrom, AbstractEdcSet edcSet,
			List<EdcData> dcDatas, EdcResult dcResult) {
		super(parentShell);
		this.edcFrom = edcFrom;
		this.edcSet = edcSet;
		this.dcDatas = dcDatas;
		this.dcResult = dcResult;
	}
	
	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(600, 200, 700, 600);
		getShell().setMinimumSize(700, 600);
		getShell().setBackground(new Color(Display.getCurrent(), 188, 201, 228));
		getShell().setText(Message.getString("edc.edc_shellTitle"));
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {				
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonClose()), false, UIControlsFactory.BUTTON_GRAY);
		
		//如果已完成则不显示按钮
		if(dcResult != null && CollectionUtils.isNotEmpty(dcResult.getResultJobs())) {
			SquareButton chart = createSquareButton(parent, 100,
					Message.getString("spc.spcjob_chart_show"), false, UIControlsFactory.BUTTON_DEFAULT);
			chart.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					showChart();
				}
			});
			
			FormData ch = new FormData();
			ch.width = 90;
			ch.height = 35;
			ch.top = new FormAttachment(0, 15);
			ch.right = new FormAttachment((Control) cancel, -12, 16384);
			ch.bottom = new FormAttachment(100, -15);
			chart.setLayoutData(ch);
		}
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
	}

	@Override
	protected Control buildView(Composite parent) {
	    setTitle(Message.getString("edc.edc_collection_task_list"));
		FormToolkit toolkit = new FormToolkit(parent.getDisplay());	
		createFormText(parent, toolkit);	
		return parent;
	}

	protected void createFormText(Composite parent, FormToolkit toolkit) {
		ScrolledComposite sc = new ScrolledComposite(parent, SWT.V_SCROLL);
		sc.setLayout(new GridLayout(1, false));
		sc.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		sc.setExpandHorizontal(true);
		sc.setExpandVertical(true);
		sc.setMinWidth(700);	
		sc.setMinHeight(20000);	
		
		Composite topComposite = new Composite(sc, SWT.NONE);
		sc.setContent(topComposite);
		topComposite.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		topComposite.setLayout(new GridLayout(1, true));
		GridData data = new GridData(GridData.FILL_VERTICAL);
		topComposite.setLayoutData(data);

		try {
			ADManager adManager = Framework.getService(ADManager.class);
			dcResult = (EdcResult)adManager.getEntity(dcResult);
			List<String> spcJobName = new ArrayList<String>();
			
			if (dcResult == null || dcResult.getResultJobs() == null
					|| dcResult.getResultJobs().size() == 0) {
				//如果没有SPC则直接显示数据采集信息
				dcDatas = adManager.getEntityList(Env.getOrgRrn(), EdcData.class, Integer.MAX_VALUE, "hisSeq = '" + dcResult.getHisSeq() + "'", " seqNo ");
				for (EdcData dcData : dcDatas) {
					dcData = (EdcData)adManager.getEntity(dcData);
					Section section = toolkit.createSection(topComposite, Section.EXPANDED | Section.DESCRIPTION);
					if (dcData.getEdcType().equals(EdcData.EDCTYPE_ITEM)){
						section.setText(Message.getString("edc.spcresult_item_name") + ":"   
								+ dcData.getItemName());
					} else if (dcData.getEdcType().equals(EdcData.EDCTYPE_BIN)){
						if (edcSet.getIsByComponent()) {
							section.setText(Message.getString("edc.spcresult_item_name") + ":" + dcData.getEdcSetName() + 
									"-->" + Message.getString("wip.component_id") + ":" + dcData.getComponentList());
						} else {
							section.setText(Message.getString("edc.spcresult_item_name") + ":"  
								+ dcData.getEdcSetName());
						}
					} else if (dcData.getEdcType().equals(EdcData.EDCTYPE_TEXT)) {
						if (edcSet.getIsByComponent()) {
							section.setText(Message.getString("edc.spcresult_item_name") + ":" + dcData.getEdcSetName() + "<" + dcData.getItemName() + ">" +
									"-->" + Message.getString("wip.component_id") + ":" + dcData.getComponentList());
						} else {
							section.setText(Message.getString("edc.spcresult_item_name") + ":" + dcData.getEdcSetName() + "<" + dcData.getItemName() + ">");
						}						
					}
					toolkit.createCompositeSeparator(section);
					Composite composite = toolkit.createComposite(section, SWT.NONE);
					composite.setLayout(new GridLayout(1, true));
					composite.setLayoutData(new GridData(GridData.FILL_BOTH));
					
					FormText text = toolkit.createFormText(composite, true);
					text.marginWidth = 20;
					StringBuffer sb = new StringBuffer();
					sb.append("<form>");
					sb.append(Message.getString("edc.data_collection_success"));
					sb.append("</form>");
					text.setText(sb.toString(), true, false);
					
					if (dcData.getIsOos()) {
						//如果有OOS则显示OOS信息及其Action
						text = toolkit.createFormText(composite, true);
						text.setColor("red", SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						text.marginWidth = 20;
						sb = new StringBuffer();
						sb.append("<form>");
						sb.append("<li><span color=\"red\">ViolateRule:OOS");
						sb.append("</span></li>");
						if (dcData.getIsHoldLot() || dcData.getIsHoldEqp()) {
							sb.append("<li><span color=\"red\">Action:");
							if (dcData.getIsHoldLot() && dcData.getIsHoldEqp()) {
								sb.append("HoldLot/HoldEqp");
							} else if (dcData.getIsHoldLot()) {
								sb.append("HoldLot");
							} else {
								sb.append("HoldEqp");
							}
							sb.append("</span></li>");
						}
						sb.append("</form>");
						text.setText(sb.toString(), true, false);
					}
					section.setClient(composite);
				}
			} else {
				Section sectionSuc = toolkit.createSection(topComposite,
						Section.EXPANDED | Section.DESCRIPTION);
				sectionSuc.setText(Message.getString("edc.data_collection_success"));
				toolkit.createCompositeSeparator(sectionSuc);
				Section spcSuc = toolkit.createSection(topComposite, Section.EXPANDED | Section.DESCRIPTION);
				spcSuc.setText(Message.getString("edc.spccheck_success"));
				toolkit.createCompositeSeparator(spcSuc);
				
				for (SpcResultJob result : dcResult.getResultJobs()) {
					result = (SpcResultJob)adManager.getEntity(result);
	
					Section section = toolkit.createSection(topComposite, Section.EXPANDED);
					section.setText(result.getJobName());
					toolkit.createCompositeSeparator(section);
					Composite composite = toolkit.createComposite(section, SWT.NONE);
					composite.setLayout(new GridLayout(1, true));
					composite.setLayoutData(new GridData(GridData.FILL_BOTH));
					String jobDatas = result.getJobDatas();
					FormText text = toolkit.createFormText(composite, true);
					text.marginWidth = 20;
					StringBuffer sb = new StringBuffer();
					sb.append("<form>");
					sb.append("<li><b>JobDatas:</b>");
					sb.append(jobDatas);
					sb.append("</li></form>");
					text.setText(sb.toString(), true, false);
					StringBuffer violateText = new StringBuffer();
					
					if (result.getIsOoc()) {
						violateText.append("OOC");
					}
					if (result.getIsOos()) {
						if (violateText.length() > 0)
							violateText.append("/OOS");
						else
							violateText.append("OOS");
					}
					
					// 只要有OOS或者OOC则弹出SpcChar
					if (result.getIsOoc() || result.getIsOos()) {
						spcJobName.add(result.getJobName());
					}
					
					if (violateText != null && violateText.length() > 0) {
						FormText textRule = toolkit.createFormText(composite, true);
						textRule.setColor("red", SWTResourceCache.getColor("Red"));
						textRule.marginWidth = 20;
						StringBuffer vsb = new StringBuffer();
						vsb.append("<form>");
						vsb.append("<li><span color=\"red\">ViolateRule:"
								+ violateText);
						vsb.append("</span></li></form>");
						textRule.setText(vsb.toString(), true, false);
					}
	
					StringBuffer chartType = new StringBuffer(
							"<form><li><b>Chart:  </b>");
					StringBuffer value = new StringBuffer("<b>Value：   </b>");
					StringBuffer acton = new StringBuffer(
							"<form><li><span color=\"red\">Action:");
					StringBuffer vioChartRule = new StringBuffer();
					for (int i = 0; i < result.getResultCharts().size(); i++) {
						SpcResultChart chart = result.getResultCharts().get(i);
						if (i == result.getResultCharts().size() - 1) {
							chartType.append(chart.getChartType()).append(";  ");
							value.append((double)(Math.round(chart.getValue()*1000))/1000).append(";");
	
						} else {
							value.append((double)(Math.round(chart.getValue()*1000))/1000).append("/");
							chartType.append(chart.getChartType()).append("/");
						}
						if (chart.getOosList() != null
								&& chart.getOosList().length() > 0) {
							if(vioChartRule!=null && vioChartRule.length()>0)vioChartRule.append(
											result.getResultCharts().get(i)
													.getOosList()).append("/");
							else vioChartRule.append("<span color=\"red\">ViolateRule:").append(
									result.getResultCharts().get(i)
									.getOosList()).append("/");
							
						} else if (chart.getOocList() != null
								&& chart.getOocList().length() > 0) {
							if(vioChartRule!=null && vioChartRule.length()>0)
									vioChartRule.append(chart.getOocList()).append("/");
							else vioChartRule.append("<span color=\"red\">ViolateRule:").append(chart.getOocList()).append("/");
						}
					}
					FormText chartText = toolkit.createFormText(composite, true);
					chartText.marginWidth = 20;
					chartText.setColor("red", SWTResourceCache.getColor("Red"));
					if (vioChartRule != null && vioChartRule.length() > 0) {
						chartType.append(value).append(
								vioChartRule.subSequence(0,
										vioChartRule.length() - 1)).append(
								"</span></li></form>").toString();
						chartText.setText(chartType.toString(), true, false);
					} else {
						chartType.append(value).append("</li></form>").toString();
						chartText.setText(chartType.toString(), true, false);
					}
					boolean disAction = false;
					if(result.getIsHoldLot()&&result.getIsHoldEqp()){
						acton.append("HoldLot/HoldEqps").append("</span></li></form>");
						disAction = true;
					}else	if (result.getIsHoldEqp()&&!result.getIsHoldLot()) {
						acton.append("HoldEQPS:").append(result.getHoldEqps()).append(
								"</span></li></form>");
						disAction = true;
					} else if (result.getIsHoldLot()&&!result.getIsHoldEqp()) {
						acton.append("HoldLot</span></li></form>");
						disAction = true;
					}
//					if(edcFrom.equals(EdcData.EDCFROM_GENERAL)||edcFrom.equals(EdcData.EDCFROM_OFFLINELOT)){
//						disAction = false;
//					}
					if (disAction) {
						FormText actionText = toolkit.createFormText(composite,
								true);
						actionText.setColor("red", new Color(null, 255, 0, 0));
						actionText.marginWidth = 20;
						actionText.setText(acton.toString(), true, false);
					}
					section.setClient(composite);
				}
				//判断系统参数是否自动打开Chart弹窗
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				//效验否有OOC或OOS任一，判断系统参数自动打开chart页面是否开启
				if (CollectionUtils.isEmpty(spcJobName) || !MesCfMod.isShowChartByResults(Env.getOrgRrn(), sysParamManager)) {
					return;
				}
				//打开弹窗
				open(spcJobName);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 按钮手动显示Chart图
	 */
	protected void showChart() {
		try {
			if (dcResult != null && CollectionUtils.isNotEmpty(dcResult.getResultJobs())) {
				List<String> spcJobName = dcResult.getResultJobs().stream().map(o -> o.getJobName()).collect(Collectors.toList());
				//打开弹窗
				open(spcJobName);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * 通过扩展点获取dialog页面
	 * @param object 弹窗路径
	 */
	public static BaseDialog getDialog(String object) {
		try {
			IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint("com.glory.framework.base.dialogs");
			IExtension[] extensions = extensionPoint.getExtensions();
			for (int i = 0; i < extensions.length; ++i) {
				IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
				for (int j = 0; j < configElements.length; ++j) {
					if (object.equals(configElements[j].getAttribute("object"))) {
						BaseDialog dialog = (BaseDialog) configElements[j].createExecutableExtension("class");
						return dialog;
					}
				}
			}
		} catch (Exception var7) {
			ExceptionHandlerManager.asyncHandleException(var7);
		}
		return null;
	}

	/**
	 * 打开弹窗
	 * @param spcjobName  SPCJob名称（参数）
	 */
	protected void open(List<String> spcjobName) throws Exception {
		try {
			Object obj = getDialog(CHART_CLASS_NAME);
			if (obj != null) {
				BaseDialog dialog = (BaseDialog) obj;
				PropertyUtil.setProperty(dialog, SPC_JOB_NAME_LIST, spcjobName);
				dialog.open();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
