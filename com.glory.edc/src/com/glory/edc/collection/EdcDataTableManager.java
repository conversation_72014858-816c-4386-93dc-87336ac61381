package com.glory.edc.collection;

import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.config.CellConfigAttributes;
import org.eclipse.nebula.widgets.nattable.config.IConfigRegistry;
import org.eclipse.nebula.widgets.nattable.config.IEditableRule;
import org.eclipse.nebula.widgets.nattable.data.validate.DataValidator;
import org.eclipse.nebula.widgets.nattable.data.validate.IDataValidator;
import org.eclipse.nebula.widgets.nattable.edit.EditConfigAttributes;
import org.eclipse.nebula.widgets.nattable.edit.command.EditCellCommand;
import org.eclipse.nebula.widgets.nattable.painter.cell.TextPainter;
import org.eclipse.nebula.widgets.nattable.painter.cell.decorator.LineBorderDecorator;
import org.eclipse.nebula.widgets.nattable.painter.cell.decorator.PaddingDecorator;
import org.eclipse.nebula.widgets.nattable.style.CellStyleAttributes;
import org.eclipse.nebula.widgets.nattable.style.DisplayMode;
import org.eclipse.nebula.widgets.nattable.style.HorizontalAlignmentEnum;
import org.eclipse.nebula.widgets.nattable.style.Style;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.nattable.editor.FixTextCellEditor;
import com.glory.framework.core.util.StringUtil;

public class EdcDataTableManager extends FixEditorTableManager {
	
	protected EdcDataTableManager next;
	
	protected EdcDataCellEditor currentCell;
	
	protected Double usl, lsl;
	
	public EdcDataTableManager(ADTable adTable) {
		super(adTable);
	}

	public EdcDataTableManager(ADTable adTable, Double usl, Double lsl) {
		super(adTable);
		this.usl = usl;
		this.lsl = lsl;
	}
	
	@Override
	public void registerEditor(IConfigRegistry configRegistry) {
		super.registerEditor(configRegistry);
		configRegistry.registerConfigAttribute(EditConfigAttributes.DATA_VALIDATOR, getSpecValidator(), DisplayMode.EDIT);
	}
	
	public void registerText(IConfigRegistry configRegistry) {
		if (getADTable() != null) {
			int i = firstColumn;
			for (final ADField field : getADTable().getFields()){
        		if (field.getIsMain() && field.getIsDisplay()){
        			if (FieldType.TEXT.equals(field.getDisplayType())) {
        				if (field.getIsEditable()) {
        					editorColumnPosition.add(i);

            				columnLabelAccumulator.registerColumnOverrides(i, COLUMN_LABEL_PREFIX + i);
            				configRegistry.registerConfigAttribute(CellConfigAttributes.CELL_PAINTER, 
            							new PaddingDecorator(
            								new LineBorderDecorator(
            										createTextPaint(field.getName()), textBorderStyle), 1), DisplayMode.NORMAL, COLUMN_LABEL_PREFIX + i);
            				
            				EdcDataCellEditor cellEditor = new EdcDataCellEditor(this);
            				cellEditor.registerVerifyListener(new VerifyListener() {
								@Override
								public void verifyText(VerifyEvent e) {
									e.doit = false; 
									e.data = field;
									if (validate(e)) {
										e.doit = true;
										return;
									}
								}
		        			});
            				cellEditor.registerModifyListener(new ModifyListener() {
								@Override
								public void modifyText(ModifyEvent e) {
									textModify((Text)e.widget);
								}
		        			});
            				
            				cellEditor.registerCRListener(this);
            				
            				Style cellStyle = new Style();
            				cellStyle.setAttributeValue(CellStyleAttributes.HORIZONTAL_ALIGNMENT, HorizontalAlignmentEnum.RIGHT);
            				configRegistry.registerConfigAttribute(CellConfigAttributes.CELL_STYLE, cellStyle, DisplayMode.NORMAL, COLUMN_LABEL_PREFIX + i);
            				
            				configRegistry.registerConfigAttribute(EditConfigAttributes.CELL_EDITOR, cellEditor, DisplayMode.EDIT, COLUMN_LABEL_PREFIX + i);
        					configRegistry.registerConfigAttribute(EditConfigAttributes.CELL_EDITABLE_RULE, IEditableRule.ALWAYS_EDITABLE, DisplayMode.EDIT, COLUMN_LABEL_PREFIX + i);
        				}
        			}
        			i++;
        		}
        	}
		}
	} 
	
	public IDataValidator getSpecValidator() {
		return new DataValidator() {
			public boolean validate(int columnIndex, int rowIndex, Object newValue) {
//				String fieldName = getColumnProperty(columnIndex);
//				if (EdcDataItem.FILED_VALUE.equals(fieldName)) {
//					if (newValue == null || String.valueOf(newValue).trim().length() == 0) {
//						return true;
//					}
//					try {
//						Double value = Double.valueOf((String)newValue);
//						if ((usl == null || value <= usl)
//							&& (lsl == null || value >= lsl)){
//							return true;
//						}
//					} catch (Exception e) {
//						return false;
//					}
//					return false;
//				}
				return true;
			}
		};
	}
	
	@Override
	public TextPainter createTextPaint(String fieldName) {
		if (EdcDataItem.FILED_VALUE.equals(fieldName)) {
			return new SpecTextPainter(usl, lsl);
		}
		return super.createTextPaint(fieldName);
	}
	
	public void inputChange(String data) {
		if (StringUtil.isEmpty(data)) {
			return;
		}
		String[] datas = data.split(";");
		EdcDataItem curDataItem = (EdcDataItem)getSelectedObject();
		
		int idxStart = 0, idxEnd = 0;
		if (curDataItem != null) {
			idxStart = getInput().indexOf(curDataItem);
			if (idxStart == -1) {
				idxStart = 0;
			}
		}
		
		List<EdcDataItem> dataItems = (List<EdcDataItem>)getInput();
		int dataSize = datas.length;
		int dataIdx = 0;
		idxEnd = (idxStart + dataSize) <= dataItems.size() ? idxStart + dataSize : dataItems.size(); 
		for (int i = idxStart ; i < idxEnd; i++) {
			EdcDataItem item = dataItems.get(i);
			item.setValue(datas[dataIdx]);
			update(item);
			dataIdx++;
			if (idxStart == idxEnd - 1) {
				if (i == dataItems.size() - 1) {
					setSelectedObject(dataItems.get(i));
				} else {
					setSelectedObject(dataItems.get(i + 1));
				}
			}
		}
		
		//设置焦点落在下一个Text
		int columnPosition = getColumnPosition(EdcDataItem.FILED_VALUE);
		setFocus(columnPosition, idxEnd + 1);
	}
	
	
	public void setUsl(Double usl) {
		this.usl = usl;
	}

	public Double getUsl() {
		return usl;
	}

	public void setLsl(Double lsl) {
		this.lsl = lsl;
	}

	public Double getLsl() {
		return lsl;
	}
	
	@Override
	public void carriageReturn(FixTextCellEditor cellEditor) {
		super.carriageReturn(cellEditor);
		EdcDataTableManager tableManager = getNext();
		if (tableManager != null) {
			NatTable nextNatTable = tableManager.getNatTable();
			if (nextNatTable.getRowCount() > 1 && cellEditor.getRowIndex() >= natTable.getRowCount() - 2) {
				nextNatTable.doCommand(
						new EditCellCommand(
								nextNatTable,
								nextNatTable.getConfigRegistry(),
								nextNatTable.getCellByPosition(1, 1)));
			}
		}
	}
	
	public EdcDataTableManager getNext() {
		return next;
	}

	public void setNext(EdcDataTableManager next) {
		this.next = next;
	}

	public EdcDataCellEditor getCurrentCell() {
		return currentCell;
	}

	public void setCurrentCell(EdcDataCellEditor currentCell) {
		this.currentCell = currentCell;
	}

}
