package com.glory.edc.collection;

import java.io.Serializable;

import com.glory.edc.model.AbstractEdcSetLine;

public class EdcDataItem implements Serializable {
	
	private static final long serialVersionUID = 1L;

	public static String FILED_VALUE = "value";
	public static String FILED_REMARK = "remark";
	
	private String name;
	private String description;
	private String value;
	private String flag;
	private String remark;
	private String specType;
	private String group;
	private Double usl;
	private Double sl;
	private Double lsl;
	private String reserved1;
	private String reserved2;
	private String reserved3;
	private String reserved4;
	private String reserved5;
	
	public void setName(String name) {
		this.name = name;
	}
	
	public String getName() {
		return name;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}

	public String getDescription() {
		return description;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getValue() {
		return value;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public void setSpecType(String specType) {
		this.specType = specType;
	}

	public String getSpecType() {
		return specType;
	}
	
	public String getGroup() {
		return group;
	}

	public void setGroup(String group) {
		this.group = group;
	}
	
	public void setUsl(Double usl) {
		this.usl = usl;
	}

	public Double getUsl() {
		return usl;
	}

	public void setSl(Double sl) {
		this.sl = sl;
	}

	public Double getSl() {
		return sl;
	}

	public void setLsl(Double lsl) {
		this.lsl = lsl;
	}

	public Double getLsl() {
		return lsl;
	}
	
	public String getReserved1() {
		return reserved1;
	}

	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}

	public String getReserved2() {
		return reserved2;
	}

	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}

	public String getReserved3() {
		return reserved3;
	}

	public void setReserved3(String reserved3) {
		this.reserved3 = reserved3;
	}

	public String getReserved4() {
		return reserved4;
	}

	public void setReserved4(String reserved4) {
		this.reserved4 = reserved4;
	}

	public String getReserved5() {
		return reserved5;
	}

	public void setReserved5(String reserved5) {
		this.reserved5 = reserved5;
	}

	/*
	 * 检查SPEC,如果符合SPEC则返回true,否则返回false
	 */
	public boolean checkSpec(Double total) {
		if (value != null && value.trim().length() > 0) {
			if (specType == null || specType.trim().length() == 0
					|| AbstractEdcSetLine.SPECTYPE_NUMBER.equals(specType)) {
				if (usl != null && !Double.isNaN(usl)) {
					if (Double.valueOf(value) > usl) {
						return false;
					}
				}
				if (lsl != null && !Double.isNaN(lsl)) {
					if (Double.valueOf(value) < lsl) {
						return false;
					}
				}
			} else if (AbstractEdcSetLine.SPECTYPE_PERCENT.equals(specType)){
				if (total != null && !Double.isNaN(total) && total > 0) {
					double percent = Double.valueOf(value) * 100 / total;
					if (usl != null && !Double.isNaN(usl)) {
						if (percent > usl) {
							return false;
						}
					}
					if (lsl != null && !Double.isNaN(lsl)) {
						if (percent < lsl) {
							return false;
						}
					}
				}
			}
		}
		return true;
	}

	


}
