package com.glory.edc.collection;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.events.HyperlinkEvent;
import org.eclipse.ui.forms.events.IHyperlinkListener;
import org.eclipse.ui.forms.widgets.FormText;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Hyperlink;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.EdcEntry;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;

public class EdcSetCurrentDialog extends ResultDialog {

	private IManagedForm form;
	protected Lot lot;
	protected List<EdcSetCurrent> currents;
	protected FormToolkit toolkit;
	
	public EdcSetCurrentDialog(Shell parentShell) {
		super(parentShell);
	}

	public EdcSetCurrentDialog(Shell parentShell, IManagedForm form, Lot lot, List<EdcSetCurrent> currents) {
		super(parentShell);
		this.form = form;
		this.lot = lot;
		this.currents = currents;
	}
	
	public EdcSetCurrentDialog(Shell parentShell, Lot lot, List<EdcSetCurrent> currents) {
		super(parentShell);
		this.lot = lot;
		this.currents = currents;
	}
	
	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(600, 150, 700, 800);
		getShell().setMinimumSize(700, 800);
		getShell().setBackground(new Color(Display.getCurrent(), 188, 201, 228));
		getShell().setText(Message.getString("edc.edc_shellTitle"));
	}
	
	protected void createFormText(Composite parent, FormToolkit toolkit) {
		ScrolledComposite sc = new ScrolledComposite(parent, SWT.V_SCROLL);
		sc.setLayout(new GridLayout(10, false));
		sc.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		sc.setExpandHorizontal(true);
		sc.setExpandVertical(true);
		sc.setMinWidth(700);
		if (currents != null && currents.size() > 0) {
			sc.setMinHeight(currents.size() * 100);
		} else {
			sc.setMinHeight(500);
		}
		Composite topComposite = new Composite(sc, SWT.NONE);
		sc.setContent(topComposite);
		topComposite.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));// White color
		GridLayout gd = new GridLayout(1, true);
		topComposite.setLayout(gd);
		GridData data = new GridData(GridData.FILL_VERTICAL);
		topComposite.setLayoutData(data);
		
		for (final EdcSetCurrent edcSetCurrent : currents){
			ADManager adManager;
			AbstractEdcSet itemSet = null;
			try{
				adManager = Framework.getService(ADManager.class);
				itemSet = new AbstractEdcSet();
				itemSet.setObjectRrn(edcSetCurrent.getItemSetRrn());
				itemSet = (AbstractEdcSet) adManager.getEntity(itemSet);
			}catch(Exception e){
				e.printStackTrace();
			}
			
			Section sectionSuc = toolkit.createSection(topComposite,
						Section.EXPANDED | Section.DESCRIPTION);
			String nameText = itemSet != null ? (Message.getString("edc.edc_collection_set") 
					+ itemSet.getName()  + " (" + (itemSet.getDescription() != null ? itemSet.getDescription() : " ") 
					+ ")") : "";
			sectionSuc.setText(nameText);
			toolkit.createCompositeSeparator(sectionSuc);
			
			Composite composite = toolkit.createComposite(topComposite, SWT.NONE);
			composite.setLayout(new GridLayout(2, true));
			GridLayout gd1 = new GridLayout(2, true);
			gd1.marginHeight = 10;
			composite.setLayout(gd1);
			composite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
			if (EdcSetCurrent.FLAG_TEMP.equals(edcSetCurrent.getEdcFlag())){
				FormText text = toolkit.createFormText(composite, true);
				text.setColor("red", new Color(null, 255, 0, 0));
				text.marginWidth = 20;
				StringBuffer sb = new StringBuffer();
				sb.append("<form>");
				sb.append("<li><b>"+Message.getString("edc.edc_temp_data_collection_status")+"</b>");
				sb.append(Message.getString("edc.edc_completed"));
				sb.append("</li></form>");
				text.setText(sb.toString(), true, false);				
				Label labIsTempSave = toolkit.createLabel(composite, "", SWT.NONE);	
				
				FormText text1 = toolkit.createFormText(composite, true);
				text1.setColor("red", new Color(null, 255, 0, 0));
				text1.marginWidth = 20;
				StringBuffer sb1 = new StringBuffer();
				sb1.append("<form>");
				sb1.append("<li><b>"+Message.getString("edc.edc_save_data_collection_status")+"</b>");
				sb1.append(Message.getString("edc.edc_unfinished"));
				sb1.append("</li></form>");
				text1.setText(sb1.toString(), true, false);				
				Hyperlink labIsSave = toolkit.createHyperlink(composite, Message.getString("edc.edc_modify_save_collected_data"), SWT.NONE);
				labIsSave.addHyperlinkListener(new IHyperlinkListener() {
			        public void linkEntered(HyperlinkEvent e) {				          
			        }
			        public void linkExited(HyperlinkEvent e) {		       
			        }
			        public void linkActivated(HyperlinkEvent e) {
			        	edcDialog(edcSetCurrent);
			        }
			    });				
			} else if(EdcSetCurrent.FLAG_DONE.equals(edcSetCurrent.getEdcFlag())){
				FormText text = toolkit.createFormText(composite, true);
				text.setColor("red", new Color(null, 255, 0, 0));
				text.marginWidth = 20;
				StringBuffer sb = new StringBuffer();
				sb.append("<form>");
				sb.append("<li><b>"+Message.getString("edc.edc_temp_data_collection_status")+"</b>");
				sb.append(Message.getString("edc.edc_completed"));
				sb.append("</li></form>");
				text.setText(sb.toString(), true, false);				
				Label labIsTempSave = toolkit.createLabel(composite, "", SWT.NONE);	
				
				FormText text1 = toolkit.createFormText(composite, true);
				text1.setColor("red", new Color(null, 255, 0, 0));
				text1.marginWidth = 20;
				StringBuffer sb1 = new StringBuffer();
				sb1.append("<form>");
				sb1.append("<li><b>"+Message.getString("edc.edc_save_data_collection_status")+"</b>");
				sb1.append(Message.getString("edc.edc_completed"));
				sb1.append("</li></form>");
				text1.setText(sb1.toString(), true, false);
				
				//判断是否可进行重测
				if (itemSet.getIsRepeatable() == true){
					edcSetCurrent.setTestType(EdcSetCurrent.TEST_TYPE_RETEST);
					
					Hyperlink labIsSave = toolkit.createHyperlink(composite, Message.getString("edc.edc_repeat_data_collection"), SWT.NONE);
					labIsSave.addHyperlinkListener(new IHyperlinkListener() {
				        public void linkEntered(HyperlinkEvent e) {				          
				        }
				        public void linkExited(HyperlinkEvent e) {		       
				        }
				        public void linkActivated(HyperlinkEvent e) {
				        	edcDialog(edcSetCurrent);
				        }
				    });
				} else {
					Label labIsSave = toolkit.createLabel(composite, "");
				}	
			}else if("".equals(edcSetCurrent.getEdcFlag()) || edcSetCurrent.getEdcFlag()==null){
				FormText text = toolkit.createFormText(composite, true);
				text.setColor("red", new Color(null, 255, 0, 0));
				text.marginWidth = 20;
				StringBuffer sb = new StringBuffer();
				sb.append("<form>");
				sb.append("<li><b>"+Message.getString("edc.edc_temp_data_collection_status")+"</b>");
				sb.append(Message.getString("edc.edc_unfinished"));
				sb.append("</li></form>");
				text.setText(sb.toString(), true, false);				
				Label labIsTempSave = toolkit.createLabel(composite, "", SWT.NONE);	
				
				FormText text1 = toolkit.createFormText(composite, true);
				text1.setColor("red", new Color(null, 255, 0, 0));
				text1.marginWidth = 20;
				StringBuffer sb1 = new StringBuffer();
				sb1.append("<form>");
				sb1.append("<li><b>"+Message.getString("edc.edc_save_data_collection_status")+"</b>");
				sb1.append(Message.getString("edc.edc_unfinished"));
				sb1.append("</li></form>");
				text1.setText(sb1.toString(), true, false);				
				Hyperlink labIsSave = toolkit.createHyperlink(composite, Message.getString("edc.edc_shellTitle"), SWT.NONE);
				labIsSave.addHyperlinkListener(new IHyperlinkListener() {
					public void linkEntered(HyperlinkEvent e) {				          
				    }
				    public void linkExited(HyperlinkEvent e) {		       
				    }
				    public void linkActivated(HyperlinkEvent e) {
				       edcDialog(edcSetCurrent);
				    }
				});					
			}	
		}
	}

	public void edcDialog(EdcSetCurrent edcSetCurrent){
		try {
			close();
			setReturnCode(EdcEntry.open(EdcData.EDCFROM_LOT, edcSetCurrent, null, lot));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}


}
