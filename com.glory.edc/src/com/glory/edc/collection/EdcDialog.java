package com.glory.edc.collection;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFComment;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.eclipse.nebula.widgets.nattable.export.FileOutputStreamProvider;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.ExportToolItemGlc;
import org.eclipse.swt.widgets.ImportToolItemGlc;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.excel.upload.MapUpload;
import com.glory.edc.client.EDCManager;
import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcResult;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADImpExp;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class EdcDialog extends BaseDialog {
	
	public static final String DEFAULT_EXPORT_FILE_NAME = "table_export.xlsx";
	public static final String[] DEFAULT_EXPORT_FILE_TYPE = new String[] { "Excel Workbook (*.xls)", "Excel Workbook (*.xlsx)" };
	public static final String[] DEFAULT_EXPORT_FILE_EXT = new String[] { "*.xls", "*.xlsx" };
	
	public static final String UPLOAD_BIN = "Edc.EdcBinSetUpload";
	
	public static final String UPLOAD_TEXT = "Edc.EdcTextSetUpload";

	private static int MIN_DIALOG_WIDTH = 840;
	private static int MIN_DIALOG_HEIGHT = 600;
	
	protected EdcEvent event;
	protected Lot lot;
	protected EdcSetCurrent edcCurrent;
	protected static AbstractEdcSet edcSet;
	protected List<EdcData> lastDcDatas =new ArrayList<EdcData>();
	protected List<EdcData> dcDatas;
	protected EdcResult dcResult;
	
	protected FormToolkit toolkit;
	protected Section section;
	protected ToolItem itemSave;
	protected ToolItem itemTempSave;
	protected ToolItem itemImport;
	protected ToolItem itemClear;
	protected ToolItem itemClose;
	protected ExportToolItemGlc itemExport;
	protected EdcDialogForm edcForm;
	protected boolean isTemp;
//	protected boolean isFull = true;
//	protected boolean isEmpty = false;
	
	public EdcDialog(Shell parentShell) {
		super(parentShell);
		super.setButtonBar(false);
	}

	public EdcDialog(Shell parent, EdcSetCurrent edcCurrent, AbstractEdcSet edcSet, EdcEvent event) {
		this(parent);
		this.edcCurrent = edcCurrent;
		this.edcSet = edcSet;
		this.event = event;
		this.lot = event.getLot();
		super.setButtonBar(false);
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}

	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText(Message.getString("edc.edc_shellTitle"));
	}
	
	@Override
	protected Control createButtonBar(Composite parent) {
		return parent;
	}

	@Override
	protected Control buildView(Composite parent) {
		toolkit = new FFormToolKit(parent.getDisplay());
		GridLayout layout = new GridLayout(1, true);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		layout.horizontalSpacing = 0;
		parent.setLayout(layout);
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createToolBarComposite(parent) ;
		setInitData();
		createDialogForm(parent);
		return parent;
	}

	protected void createToolBarComposite(Composite parent) {
	    section = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
		section.setText(Message.getString("edc.edc_shellTitle"));
		section.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		GridLayout gridLayout = new GridLayout();
		section.setLayout(gridLayout);
		
		Composite client = toolkit.createComposite(section);
		
	    createToolBar(section);
	    setButtonEnable();
	    
	    gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		client.setLayout(gridLayout);
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		client.setLayoutData(gd);
		section.setClient(client);	
	}
	
	protected void createDialogForm(Composite parent) {
		edcForm = new EdcDialogForm(parent, edcCurrent, lot, edcSet, lastDcDatas, toolkit);
		edcForm.createForm();
	}
	
	protected void createToolBar(Composite parent) {
		ToolBar tBar = new ToolBar(parent, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemTempSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClear(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClose(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}
	
	protected void createToolItemTempSave(ToolBar tBar) {
		itemTempSave = new ToolItem(tBar, SWT.PUSH);
		itemTempSave.setText(Message.getString("edc.temp.save"));
		itemTempSave.setImage(SWTResourceCache.getImage("save"));
		itemTempSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				tempSaveAdapter();
			}
		});
	}

	protected void createToolItemImport(ToolBar tBar) {
		if (edcSet instanceof EdcBinSet) {
			itemImport = new ImportToolItemGlc(tBar, null, "Edc.EdcBinSetUpload", null, null);
		} else {
			itemImport = new ImportToolItemGlc(tBar, null, "Edc.EdcSetUpload", null, null);
		}
		itemImport.addSelectionListener(new SelectionAdapter()  {
			@Override
			public void widgetSelected(SelectionEvent event) {
				MapUpload upload = new MapUpload();
				List<Map> dataMap = upload.run();
				EdcUpload edcUpload = new EdcUpload(dataMap);
				
				edcForm.upload(edcUpload);
			}
		});
	}
	
	protected void createToolItemExport(ToolBar tBar) {
		itemExport = new ExportToolItemGlc(tBar, null, "", null, null);
		itemExport.setImage(SWTResourceCache.getImage("export"));
		itemExport.setText(Message.getString(ExceptionBundle.bundle.CommonExportTemplate()));
		itemExport.setEnabled(true);
		itemExport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				exportAdapter();
			}
		});
	}
	
	protected void createToolItemClear(ToolBar tBar) {
		itemClear = new ToolItem(tBar, SWT.PUSH);
		itemClear.setText(Message.getString(ExceptionBundle.bundle.CommonClear()));
		itemClear.setImage(SWTResourceCache.getImage("clear"));
		itemClear.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				clearAdapter();
			}
		});
	}

	protected void createToolItemClose(ToolBar tBar) {
		itemClose = new ToolItem(tBar, SWT.PUSH);
		itemClose.setText(Message.getString(ExceptionBundle.bundle.CommonExit()));
		itemClose.setImage(SWTResourceCache.getImage("delete"));
		itemClose.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmExit()))) {
					setReturnCode(CANCEL);
					close();
				}
			}
		});
	}
	
	protected void setButtonEnable() {
	}
	
	protected void exportAdapter() {
		try {
			if (edcSet instanceof EdcBinSet) {
				exportTemplate("Edc.EdcBinSetUpload", null);
			}else if (edcSet instanceof EdcTextSet) {
				exportTemplate("Edc.EdcTextSetUpload", null);
			} else {
				List<EdcData> datas = edcForm.getEdcDatas();
				if(CollectionUtils.isNotEmpty(datas)) {
					exportTemplate("Edc.EdcSetUpload", null);
				}else {
					UI.showWarning(Message.getString("edc.select_sample_component_export"));
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	
	/**
	 * 导出模版
	 * 
	 * @param adAuthorityName
	 */
	public void exportTemplate(String authorityName, String buttonName) {
		BufferedOutputStream bufferedOutputStream = null;
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			String fileName = StringUtil.isEmpty(authorityName) ? DEFAULT_EXPORT_FILE_NAME : authorityName.replace(".", "") + ".xlsx";
			FileOutputStreamProvider provider = new FileOutputStreamProvider(fileName, DEFAULT_EXPORT_FILE_TYPE, DEFAULT_EXPORT_FILE_EXT);
			OutputStream outputStream = provider.getOutputStream(UI.getActiveShell());
			if (outputStream == null) {
				return;
			}
			bufferedOutputStream = new BufferedOutputStream(outputStream);

			XSSFSheet sheet = workbook.createSheet();

			// 从ADImpExpFieldMap表中导出
			ADManager adManager = Framework.getService(ADManager.class);
			ADImpExp adImpExp = adManager.getADImpExpByAuthorityName(Env.getOrgRrn(), authorityName, buttonName, true);
			if (adImpExp != null) {
				List<ADField> exportFields = adManager.buildADFieldByADImpExp(adImpExp, true, Env.getSessionContext());
				if (CollectionUtils.isNotEmpty(exportFields)) {
					XSSFDrawing draw = sheet.createDrawingPatriarch();
					XSSFComment comment = null;
					XSSFRow firstRow = sheet.createRow(0);// 第一行表头
					int i = 0;
					for (ADField exportField : exportFields) {
						XSSFCell cell = firstRow.createCell(i);
						if (!StringUtil.isEmpty(exportField.getColumnName())) {
							cell.setCellValue(exportField.getColumnName());
						} else {
							cell.setCellValue(exportField.getName().toUpperCase());
						}
						comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
						comment.setString(DefaultDownloadWriter.getCellComment(exportField));
						cell.setCellComment(comment);
						i++;
					}
					// 写入ID
					int rum = 1;
					List<EdcData> datas = edcForm.getEdcDatas();
					if (CollectionUtils.isNotEmpty(datas)) {
						// 写入设置的采集项采样计划
						for (EdcData data : datas) {
							String[] dcName = data.getDcName().split(";");
							String[] dcData = null;
							if(data.getDcData() != null) {
								dcData = data.getDcData().split(";");
							}
							if (dcName != null && dcName.length > 0) {
								for (int b = 0; b < dcName.length; b++) {
									XSSFRow row = sheet.createRow(rum);
									if (authorityName.equals(UPLOAD_BIN)) {// Bin类型
										if (StringUtil.isEmpty(data.getComponentList())) {// 设备采集无批次写入采集项集名称
											row.createCell(0).setCellValue(data.getEdcSetName());// 写入行
										} else {// 批次BIN类型写入Component
											row.createCell(0).setCellValue(data.getComponentList());// 写入行
										}
										row.createCell(1).setCellValue(dcName[b]);// 写入行
										if (dcData != null && dcData.length > 0 && dcData.length >= dcName.length) {
											row.createCell(2).setCellValue(dcData[b]);// 写入行
										}
									} else if (authorityName.equals(UPLOAD_TEXT)) {//text写入
										row.createCell(0).setCellValue(dcName[b]);// 写入行
										if (dcData != null && dcData.length > 0 && dcData.length >= dcName.length) {
											row.createCell(1).setCellValue(dcData[b]);// 写入行
										}
									} else {
										row.createCell(0).setCellValue(dcName[b]);// edcset
										row.createCell(1).setCellValue(data.getItemName());// 写入行
										if (dcData != null && dcData.length > 0 && dcData.length >= dcName.length) {
											row.createCell(2).setCellValue(dcData[b]);// 写入行
										}
									}
									rum++;
								}
							}
						}
					}
				}
				// 设置列宽自适应
				int columnNum = 0;
				for (ADField adField : exportFields) {
					sheet.autoSizeColumn(columnNum, true);
					columnNum++;
				}
			}

			workbook.write(bufferedOutputStream);
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonExportSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			try {
				if (bufferedOutputStream != null) {
					bufferedOutputStream.close();
				}
				workbook.close();
			} catch (IOException e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	protected void clearAdapter() {
		edcForm.clear();
	}
	
	protected void setInitData() {
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			if (edcCurrent.getObjectRrn() != null) {
				//抓取临时保存数据或当站数据
				if (EdcSetCurrent.FLAG_TEMP.equals(edcCurrent.getEdcFlag())){
					String whereClause="";
					String orderBy = " seqNo ASC";
					if (edcCurrent.getBatchId() != null) {
						whereClause = " batchId = '" + edcCurrent.getBatchId() + "'";
					} else {
						whereClause = " lotRrn = '" + edcCurrent.getLotRrn() + "'";
					}
					whereClause = whereClause + " AND edcSetRrn = '" + edcCurrent.getItemSetRrn() + "' AND isTemp ='Y'";
					lastDcDatas = adManager.getEntityList(edcCurrent.getOrgRrn(), EdcData.class, Integer.MAX_VALUE, whereClause, orderBy);
				} 
			} else if (edcCurrent.getHistorySeq() != null) {
				//抓取历史数据
				if(this.edcSet instanceof EdcItemSet) {
					edcSet = (EdcItemSet)edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), null, null);
				}else if(this.edcSet instanceof EdcBinSet) {
					edcSet = (EdcBinSet)edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), null, null);
				}
				String condition = " hisSeq = '" + edcCurrent.getHistorySeq() + "'";
				lastDcDatas = adManager.getEntityList(Env.getOrgRrn(), EdcData.class, Env.getMaxResult(),
						condition, " seqNo ASC");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void saveAdapter() {
		try {
			isTemp = false;
			
			if (!edcForm.validate()) {
				return;
			}
			
			List<EdcData> datas = edcForm.getEdcDatas();
			//检查强制输入
			if (!checkMandatory(datas)) {
				return;
			}
			
			if((edcSet instanceof EdcItemSet)){
				this.dcDatas = removeEmptyEdcData(datas);
			} else {
				this.dcDatas = datas;
			}
		
			if (dcDatas == null || dcDatas.size() < 1){
				//不允许所有采集项采集数据为空
				UI.showError(Message.getString("edc.cannot_save"), Message.getString("edc.alert_message_title"));
				return;
			}

			if (UI.showConfirm(Message.getString("edc.data_cannot_change"))){
				for (EdcData data: dcDatas) {
					data.setIsTemp(false);
					data.setObjectRrn(null);
					if (edcCurrent != null) {
						if (EdcSetCurrent.TEST_TYPE_RETEST.equals(edcCurrent.getTestType())) {
							data.setIsRetest(EdcSetCurrent.TEST_TYPE_RETEST);
						}
					} else {
						data.setIsRetest(EdcSetCurrent.TEST_TYPE_NORMAL);
					}
				}  
				saveData(dcDatas);
				super.okPressed();
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void tempSaveAdapter() {
		try {
			isTemp = true;
			if (edcSet instanceof EdcItemSet){
				this.dcDatas = removeEmptyEdcData(edcForm.getEdcDatas());
			} else{
				this.dcDatas = edcForm.getEdcDatas();
			}
			for (EdcData data: dcDatas) {
				data.setIsTemp(true);
				data.setObjectRrn(null);
			}  
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.edcLotDataTemp(dcDatas, EdcData.EDCFROM_LOT, Env.getSessionContext());
			UI.showInfo(Message.getString("edc.data_temp_save_success"), Message.getString("edc.showInfo_informations"));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * 在多个数据采集项时
	 * 不保存没有输入数据的采集结果
	 */
	public List<EdcData> removeEmptyEdcData(List<EdcData> datas){
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		for (EdcData data : datas) {
			if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())) {
				edcDatas.add(data);
			} else if (data.getDcData() != null) {
				String dcDatas[] = data.getDcData().split(";");
				if (!data.getDcData().equals("") 
						&& dcDatas != null && dcDatas.length != 0){
					edcDatas.add(data);
				}
			}
		}
		return edcDatas;
	}
	
	/**
	 * 检查是否强制输入，如果强制输入
	 * 1，Variable类型数据必须每个都输入值
	 * 2，Attribute、BIN类型的数据Total必须有值且不能为0
	 * 3.所有选择了选择设备的数据采集都必须选择设备
	 * 4.文本数据采集设置了采集项为必填的检查强制输入
	 */
	public boolean checkMandatory(List<EdcData> dcDatas){

		if (edcSet instanceof EdcItemSet){
			for (EdcItemSetLine line : ((EdcItemSet) edcSet).getItemSetLines()) {
				if (line.getIsShowEquipment()) {
					for (EdcData edcData : dcDatas) {
						if (edcData.getMeasureEqp() == null || edcData.getMeasureEqp().equals("")) {
							return false;
						}
					}
				}
				//如果为必须数据采集
				if (line.getIsMandatory()){
					//判断是否有生成dcDatas
					boolean check = false;
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							check = true;
							if(line.getDataType().equals(EdcItem.DATATYPE_ATTRIBUTE)){
								//检查Attribute类型,Total必须有值且不能为0
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								for (int i = 0; i < edcIds.length; i++) {
									if (EdcData.ATTRIBUTE_TOTAL.equals(edcIds[i])) {
										if (i < edcDatas.length) {
											String totalData = edcDatas[i];
											if (totalData.trim().length() > 0 
													&& !"0".equals(totalData.trim())) {
												return true;
											}
										}
									}
								}
								UI.showError(Message.getString("edc.data_mandatory_fail"));
								return false;
							} else {
								//检查Variable类型
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								//检查长度是否相同
								if (!(edcIds.length == edcDatas.length)) {
									UI.showError(Message.getString("edc.data_mandatory_fail"));
									return false;
								}
								//检查每个栏位是否有值
								for (String edcData : edcDatas) {
									if (edcData.trim().length() == 0) {
										UI.showError(Message.getString("edc.data_mandatory_fail"));
										return false;
									}
								}
							}
						}
					}
					//如果设置了必输，但未生成dcDatas未录入值，则也进行卡控
					if(!check) {
						UI.showError(Message.getString("edc.data_mandatory_fail"));
						return false;
					}
				}
				
				if (line.getIsJudgeByManual()) {
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (StringUtil.isEmpty(data.getJudge1())) {
								UI.showError(Message.getString("edc.data_judge_must_input"));
								return false;
							}
						}
					}
				}
			}
		} else if (edcSet instanceof EdcBinSet){
			//如果是BIN数据采集
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (StringUtil.isEmpty(data.getJudge1())) {
						UI.showError(Message.getString("edc.data_judge_must_input"));
						return false;
					}
				}
			}
		} else if (edcSet instanceof EdcTextSet) {
			for (EdcTextSetLine line : ((EdcTextSet) edcSet).getTextSetLines()) {
				if (edcSet.getIsShowEquipment()) {
					for (EdcData edcData : dcDatas) {
						if (edcData.getMeasureEqp().equals("") || edcData.getMeasureEqp() == null) {
							return false;
						}
					}
				}
				//如果为必须数据采集
				if (line.getIsMandatory()){
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (data.getDcData().equals("") || data.getDcData() == null) {
								return false;
							}
						}
					}
				}
			}	
			
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())){
						if (StringUtil.isEmpty(data.getJudge1())) {
							UI.showError(Message.getString("edc.data_judge_must_input"));
							return false;
						}
					}
				}
			}
		}
		
		
		return true;
	}
	
	public void saveData(List<EdcData> datas) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);

		List<Lot> edcLots = new ArrayList<Lot>();
		String lotIds = "";
		edcLots.add(lot);
		EdcData edcData = datas.get(0);

		if (edcData.getBatchId() != null) {
			List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), edcData.getBatchId());
			for (Lot batchLot : lots) {
				lotIds += batchLot.getLotId() + ";";
				// 记录每个批次EDC历史
				if (!lot.getLotId().equals(batchLot.getLotId())) {
					edcLots.add(batchLot);
				}
			}
		}
		
		for (EdcData data : datas) {
			data.setBatchLots(StringUtils.isNotBlank(lotIds) ? lotIds.substring(0, lotIds.length() - 1) : null);
			data.setLineId(lot.getLineId());
			data.setTeamId(Env.getSessionContext().getTeam());
		}

		dcResult = lotManager.edcLotData(edcLots, datas, EdcData.EDCFROM_LOT, true, Env.getSessionContext());
	}
	
	public void setDcResult(EdcResult dcResult) {
		this.dcResult = dcResult;
	}

	public EdcResult getDcResult() {
		return dcResult;
	}
	
	public List<EdcData> getDcDatas() {
		return dcDatas;
	}
	
	public void setSelectButtonEnable() {
		edcForm.setSelectButtonEnable();
	}
	
	public String formatList(List<String> strs){
		StringBuffer returnStr = new StringBuffer("");
		for(int i=0;i<strs.size();i++){
			if(i < strs.size()-1){
				returnStr.append(strs.get(i)).append(";");
			}else {
				returnStr.append(strs.get(i));
			}
		}
		return returnStr.toString();
	}

	public void setIsTemp(Boolean isTemp) {
		this.isTemp = isTemp;
	}

	public Boolean getIsTemp() {
		return isTemp;
	}
	
	@Override
	public boolean close() {
		//关闭窗口的时候要关闭自动采集的连接
		((EdcDialogForm) edcForm).disConnect();
		return super.close();
	}

	public List<EdcData> getLastDcDatas() {
		return lastDcDatas;
	}

	public void setLastDcDatas(List<EdcData> lastDcDatas) {
		this.lastDcDatas = lastDcDatas;
	}

}


