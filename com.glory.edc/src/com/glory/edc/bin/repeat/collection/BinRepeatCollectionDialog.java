package com.glory.edc.bin.repeat.collection;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.edc.bin.query.adapter.EdcDataBinAdapter;
import com.glory.edc.bin.query.adapter.EdcDataBinTableManager;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcDataItem;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.AbstractEdcSetLine;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSpec;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.PrdSpecValue;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class BinRepeatCollectionDialog extends BaseTitleDialog {
	
	public static final String TABLE_NAME = "EDCBinQuery";
	
	public EdcDataBinTableManager tableManager;
	
	public List<Button> btns = new ArrayList<Button>();
	public Text contextText;
	public BinRepeatEdcDataTableComposite form;
	
	public List<EdcBinSetLine> edcBinSetLineList;
	
	protected Map<String, String> valueMap = null;
	protected Lot lot;
	protected List<EdcData> edcDatas;
	protected String edcFrom;
	
	protected BinRepeatCollectionDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public BinRepeatCollectionDialog(Shell parentShell, Lot lot, List<EdcData> edcDatas, String edcFrom) {
		this(parentShell);
		this.lot = lot;
		this.edcFrom = edcFrom;	
		this.edcDatas = edcDatas;
	}	
		
//	@Override
//	protected void constrainShellSize() {
//		super.constrainShellSize();
////		getShell().setBounds(200, 90, 300, 595);
//		getShell().setMinimumSize(1000, 600);
//		getShell().setBackground(new Color(Display.getCurrent(), 188, 201, 228));
//	}
	
	@Override
	protected Point getInitialSize() {
	    return new Point(1500, 950);
	}
	
	@Override
	protected Control buildView(Composite parent) {
		setTitle(Message.getString("edc.lot_bin_repeat_collection"));
	    setMessage(Message.getString("edc.lot_select_bin_repeat_collection"));
	        
		Composite content = new Composite(parent, SWT.BORDER);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setBackground(new Color(Display.getCurrent(), 255, 255, 255));	

		Group binTableComposite = new Group(content, SWT.BORDER);
		binTableComposite.setLayout(new GridLayout(1, false));
		GridData binTableGridData = new GridData(GridData.FILL_BOTH);
		binTableGridData.heightHint = 300;
		binTableComposite.setLayoutData(binTableGridData);
		binTableComposite.setText(Message.getString("edc.lot_last_bin_data"));
		try {				
			ADManager entityManager = (ADManager)Framework.getService(ADManager.class);	
			edcBinSetLineList = entityManager.getEntityList(Env.getOrgRrn(), EdcBinSetLine.class, Env.getMaxResult(), 
					" edcSetRrn = " + edcDatas.get(0).getEdcSetRrn(), " seqNo ");
//			List<EdcBinSetLine> edcBinSetLines = entityManager.getEntityList(Env.getOrgRrn(), EdcBinSetLine.class, Env.getMaxResult(), 
//					"binType !='G' and edcSetRrn = " + edcDatas.get(0).getEdcSetRrn(), " seqNo ");
//			
			EdcBinSet edcBinSet = new EdcBinSet();
			edcBinSet.setObjectRrn(edcDatas.get(0).getEdcSetRrn());
//			edcBinSet = (EdcBinSet) entityManager.getEntity(edcBinSet);
			edcBinSet = getBinSetSpec(edcBinSet);
			
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ADTable cloneTable = (ADTable) adTable.clone();
			cloneTable.setObjectRrn(adTable.getObjectRrn());
			List<ADField> cloneFields = new ArrayList<ADField>();
			for (ADField adField : adTable.getFields()) {
				cloneFields.add(adField);
			}
			int i = 0;
			for (EdcBinSetLine line : edcBinSetLineList) {
				ADField binField = new ADField();
				binField.setName(EdcDataBinAdapter.BIN_PREFIX + i);
				if (EdcBinSetLine.BINTYPE_GROUP.equals(line.getBinType())) {
					binField.setIsMain(false);
				} else {
					binField.setIsMain(true);
				}
				binField.setIsDisplay(true);
				binField.setDisplayLength(24L);
				binField.setLabel(line.getName());
				binField.setLabel_zh(line.getName());
				cloneFields.add(binField);
				i++;
			}	
			cloneTable.setFields(cloneFields);
			
			tableManager = new EdcDataBinTableManager(cloneTable);
			tableManager.newViewer(binTableComposite);
			tableManager.setInput(edcDatas);
			tableManager.addSelectionChangedListener(new org.eclipse.jface.viewers.ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					IStructuredSelection ss = event.getStructuredSelection();
					EdcData edcData = (EdcData) ss.getFirstElement();
					valueMap = new HashMap<String, String>();
					if (edcData != null) {
						String[] rawDataString = edcData.getDcData().split(";");
						String[] rawNameString = edcData.getDcName().split(";");
						for (int key = 0 ; key < rawNameString.length; key++) {
							try {
								valueMap.put(rawNameString[key], StringUtil.isEmpty(rawDataString[key]) ? "0"  : rawDataString[key]);
							} catch (Exception e) {//最后采集项为空时会报越界异常
								valueMap.put(rawNameString[key], "0");
							}			
						}
					}	
					for (Button btn : btns) {
						btn.setSelection(false);
					}
					contextText.setText("");
					Map<String, String> tableValueMap = new HashMap<String, String>();
					tableValueMap.put(EdcData.ATTRIBUTE_TOTAL, "0");
					form.setEdcDatas(tableValueMap);
				}
			});
			
			Composite binComposite = new Composite(content, SWT.NONE);
			binComposite.setLayout(new GridLayout(2, false));
			binComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			Group binSelectGroup = new Group(binComposite, SWT.BORDER);
			binSelectGroup.setLayout(new GridLayout(1, false));
			binSelectGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
			binSelectGroup.setText(Message.getString("edc.bin_for_repeat"));
			
			Composite binSelectComposite = new Composite(binSelectGroup, SWT.NONE);
			GridLayout gridLayout = new GridLayout(6, false);
			binSelectComposite.setLayout(gridLayout);
			binSelectComposite.setLayoutData(new GridData(GridData.FILL_BOTH));	
			final FormToolkit toolkit = new FormToolkit(binSelectComposite.getDisplay());
			for (EdcBinSetLine line : edcBinSetLineList) {
				if (!EdcBinSetLine.BINTYPE_GROUP.equals(line.getBinType()) &&
						!EdcBinSetLine.BINTYPE_OTHER.equals(line.getBinType())) {
				Button checkBtn = toolkit.createButton(binSelectComposite, line.getName(), SWT.CHECK);
				checkBtn.addSelectionListener(new org.eclipse.swt.events.SelectionListener( ) {

					@Override
					public void widgetSelected(SelectionEvent e) {
						EdcData edcData = (EdcData) tableManager.getSelectedObject();
						if (edcData == null) {
							UI.showError(Message.getString("edc.data_no_selected"));
							Button b = (Button) e.getSource();
							b.setSelection(false);
							return;
						}
						
						Map<String, String> tableValueMap = new HashMap<String, String>();
						
						StringBuffer context = new StringBuffer();
						BigDecimal totalQty = BigDecimal.ZERO;
						for (Button btn : btns) {
							if (btn.getSelection()) {	
								String data = valueMap.get(btn.getText());
								if (StringUtil.isEmpty(data)) {
									context.append(btn.getText() + ":0  ");
								} else {
									context.append(btn.getText() + ":" + data + " ");
									totalQty = totalQty.add(BigDecimal.valueOf(Long.valueOf(data)));
								}
								tableValueMap.put(btn.getText(), data);
							}
						}
						context.append("Total:" + totalQty + " ");
						contextText.setText(context.toString());
						tableValueMap.put(EdcData.ATTRIBUTE_TOTAL, totalQty.toString());
						form.setEdcDatas(tableValueMap);
					}

					@Override
					public void widgetDefaultSelected(SelectionEvent e) {}
					
				});
				btns.add(checkBtn);
			}
			}
			GridData multiTexGridDatat = new GridData();
			multiTexGridDatat.horizontalAlignment = SWT.FILL;
			multiTexGridDatat.grabExcessHorizontalSpace = true;
			multiTexGridDatat.heightHint = 100;
			contextText = toolkit.createText(binSelectGroup, "",  SWT.MULTI | SWT.WRAP | SWT.H_SCROLL | SWT.V_SCROLL);
			contextText.setLayoutData(multiTexGridDatat);
			
			Group binInputGroup = new Group(binComposite, SWT.BORDER);
			binInputGroup.setLayout(new GridLayout(1, false));
			binInputGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
			binInputGroup.setText(Message.getString("edc.lot_select_bin_repeat"));
			
			form = new BinRepeatEdcDataTableComposite(binInputGroup, (EdcBinSet)edcBinSet, toolkit, null, lot, null);
			form.createForm();
			
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
		}
		return content;
	}
		
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		SquareButton ok = createSquareButton(parent, IDialogConstants.OK_ID,
				Message.getString(ExceptionBundle.bundle.CommonSave()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
	}
	
	@Override
	public void okPressed() {
		try {
			EdcData edcData = (EdcData) tableManager.getSelectedObject();
			if (edcData == null) {
				UI.showError(Message.getString("edc.data_no_selected"));
				return;
			}
			List<String> binNames = new ArrayList<String>();
			for (Button btn : btns) {
				if (btn.getSelection()) {
					binNames.add(btn.getText());			
				}
			}
			if (binNames.size() <= 0) {
				UI.showError(Message.getString("edc.repeat_bin_no_selected"));
				return;
			}
			Map<String, String> subValueMap = new HashMap<String, String>();
			EdcData subEdcData = form.getEdcData();
			String[] rawDataString = subEdcData.getDcData().split(";");
			String[] rawNameString = subEdcData.getDcName().split(";");
			
			Long inputTotal = 0l;
			Long initTotal = 0l;
			for (int key = 0; key < rawNameString.length; key++) {
				if (!EdcData.ATTRIBUTE_TOTAL.equals(rawNameString[key])) {
					try {
						subValueMap.put(rawNameString[key],
								StringUtil.isEmpty(rawDataString[key]) ? "0" : rawDataString[key]);
						boolean isGroupType = true;
						for (EdcBinSetLine line : edcBinSetLineList) {
							if (line.getName().equals(rawNameString[key])
									&& !EdcBinSetLine.BINTYPE_GROUP.equals(line.getBinType())
									&& !EdcBinSetLine.BINTYPE_OTHER.equals(line.getBinType())) {
								isGroupType = false;
								break;
							}
						}
						if (!isGroupType) {
							inputTotal = inputTotal
									+ Long.valueOf(StringUtil.isEmpty(rawDataString[key]) ? "0" : rawDataString[key]);
						}

					} catch (Exception e) {
						subValueMap.put(rawNameString[key], "0");
					}
				} else {
					initTotal = Long.valueOf(StringUtil.isEmpty(rawDataString[key]) ? "0" : rawDataString[key]);
				}
			}
			//
//			if (inputTotal != initTotal) {
//				UI.showError(Message.getString("edc.input_data_greater_than_init_data"));
//				return;
//			}
			//是否检查总数
			ADManager adManager = Framework.getService(ADManager.class);
			List<EdcBinSet> binSetList = adManager.getEntityList(Env.getOrgRrn(), EdcBinSet.class, 1,
					" objectRrn = "+edcData.getEdcSetRrn()+"", "");
			if (binSetList.get(0).getIsCheckTotal() && !inputTotal.equals(initTotal) ) {
				UI.showError(Message.getString("edc.input_data_greater_than_init_data"));
				return;
			}
			
			for (String key : valueMap.keySet()) {
				if (!EdcData.ATTRIBUTE_TOTAL.equals(key)) {
					if (binNames.contains(key)) {
						valueMap.put(key, subValueMap.get(key));
					} else {
						valueMap.put(key, String.valueOf(Long.valueOf(valueMap.get(key)) + Long.valueOf(subValueMap.get(key))));
					}
				}
			}
			for (String key : valueMap.keySet()) {
				boolean isGroupType = false;
				for (EdcBinSetLine line : edcBinSetLineList) {
					if (line.getName().equals(key) && EdcBinSetLine.BINTYPE_GROUP.equals(line.getBinType())) {
						isGroupType = true;
						break;
					}
				}
				if (isGroupType) {
					BigDecimal total = BigDecimal.ZERO;
					for (String groupName : valueMap.keySet()) {
						for (EdcBinSetLine line : edcBinSetLineList) {
							if (line.getName().equals(groupName) && key.equals(line.getBinGroup())) {
								total = total.add(new BigDecimal(valueMap.get(groupName)));
							}
						}
					}
					valueMap.put(key, total.toString());
				}
			}
			EdcData cloneEdcData = (EdcData) edcData.clone();
			String[] binNameStr = subEdcData.getDcName().split(";");
			StringBuffer binDatas = new StringBuffer();
			BigDecimal badQty = BigDecimal.ZERO;
			for (int key = 0 ; key < binNameStr.length; key++) {
				if (key == binNameStr.length - 1) {
					binDatas.append(valueMap.get(binNameStr[key]));
				} else {
					binDatas.append(valueMap.get(binNameStr[key]) + ";");
				}
				if (!StringUtil.isEmpty(valueMap.get(binNameStr[key]))) {
					for (EdcBinSetLine line : edcBinSetLineList) {
						if (line.getName().equals(binNameStr[key]) && EdcBinSetLine.BINTYPE_FAIL.equals(line.getBinType())) {
							badQty = badQty.add(new BigDecimal(valueMap.get(binNameStr[key])));
						}
					}
				}
			}
			cloneEdcData.setDcData(binDatas.toString());
			cloneEdcData.setBadQty(badQty);
			EdcBinSet edcBinSet = new EdcBinSet();
			edcBinSet.setObjectRrn(edcBinSetLineList.get(0).getEdcSetRrn());
			edcBinSet = (EdcBinSet) adManager.getEntity(edcBinSet);
			String oosList = "";
			int i = 1;
			//添加一个空对象用于第一次计算总数良率
			edcBinSetLineList.add(0, new EdcBinSetLine());
			for (EdcBinSetLine binLine : edcBinSetLineList) {
				if (i == 1) {
					EdcDataItem totalItem = new EdcDataItem();
					totalItem.setName(EdcData.ATTRIBUTE_TOTAL);
					totalItem.setDescription(EdcData.ATTRIBUTE_TOTAL);
					totalItem.setFlag("");
					totalItem.setValue(String.valueOf(cloneEdcData.getTotalQty().subtract(badQty)));
					totalItem.setSpecType(AbstractEdcSetLine.SPECTYPE_PERCENT);
					totalItem.setUsl(edcBinSet.getUslString()!=null?Double.parseDouble(edcBinSet.getUslString()):null);
					totalItem.setLsl(edcBinSet.getLslString()!=null?Double.parseDouble(edcBinSet.getLslString()):null);
					// 整体良率OOS记入oosList的第一个栏位
					if (!totalItem.checkSpec(cloneEdcData.getTotalQty().doubleValue())) {
						oosList += i + ";";
					}
				} else {
					EdcDataItem dataItem = new EdcDataItem();
					dataItem.setName(binLine.getName());      
					dataItem.setDescription(binLine.getDescription());
					dataItem.setSpecType(binLine.getSpecType());
					dataItem.setGroup(binLine.getBinGroup());
					dataItem.setUsl(binLine.getUslString()!=null?Double.parseDouble(binLine.getUslString()):null);
					dataItem.setSl(binLine.getSlString()!=null?Double.parseDouble(binLine.getSlString()):null);
					dataItem.setLsl(binLine.getLslString()!=null?Double.parseDouble(binLine.getLslString()):null);
					dataItem.setFlag(binLine.getBinType());
					dataItem.setValue(valueMap.get(binLine.getName()));
					if (!dataItem.checkSpec(cloneEdcData.getTotalQty().doubleValue())) {
						oosList += i + ";";
					}
				}
				i++;
			}
			cloneEdcData.setOosList(oosList);
			
			List<EdcData> summaryBinDatas = new ArrayList<EdcData>();
			summaryBinDatas.add(cloneEdcData);
			
			List<EdcData> repeatBinDatas = new ArrayList<EdcData>();
			repeatBinDatas.add(subEdcData);	
			
			LotManager lotManager = Framework.getService(LotManager.class);	
			List<Lot> lots = new ArrayList<Lot>();
			lot = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
			lots.add(lot);
			lotManager.edcLotDataByBinRepeat(lots, binNames, repeatBinDatas, summaryBinDatas, edcFrom, false, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		close();
	}
	
	/**
	 * 获取BINSet SPEC
	 * 借鉴EdcEntry类中的逻辑
	 * */
	private EdcBinSet getBinSetSpec(EdcBinSet binSet) {
		try {
			Map<String, EdcSpec> specMap = null;
			Map<String, Object> paramMap = null;
			
			EDCManager edcManager = Framework.getService(EDCManager.class);
			AbstractEdcSet actualEdcSet = edcManager.getActualEdcSet(binSet.getObjectRrn(), null, null);
			//默认BINSet
			if (actualEdcSet instanceof EdcBinSet) {
				binSet = (EdcBinSet)actualEdcSet;
			}
			for (EdcBinSetLine line : binSet.getBinSetLines()) {
				if (line.getIsUsePartSpec()) {
					specMap = new HashMap<String, EdcSpec>();
				} else if (line.getIsUseParameter()) { 
					paramMap = new HashMap<String, Object>();
				}
			}
			PrdManager prdManager = Framework.getService(PrdManager.class);
			if (specMap != null) {
				List<PrdSpecValue> specValues = prdManager.getSpecValues(Env.getOrgRrn(), null, lot.getStepName(), lot);
				for (PrdSpecValue specValue : specValues) {
					EdcSpec edcSepc = new EdcSpec();
					edcSepc.setName(specValue.getName());
					edcSepc.setUslString(specValue.getMaxValue());
					edcSepc.setSlString(specValue.getDefaultValue());
					edcSepc.setLslString(specValue.getMinValue());
					specMap.put(specValue.getName(), edcSepc);
				}
			}
			if (paramMap != null) {
				if (lot.getProcessInstanceRrn() != null) {
					paramMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
				} 
			}
			if ((specMap != null && !specMap.isEmpty()) 
					|| (paramMap != null && !paramMap.isEmpty())) {
				binSet = (EdcBinSet)edcManager.getActualEdcSet(binSet.getObjectRrn(), specMap, paramMap);
			}
			return binSet;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	
}
