package com.glory.edc.offlinelot;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.edc.aql.collection.AQLEdcDialogForm;
import com.glory.edc.bin.collection.BinEdcDialogForm;
import com.glory.edc.collection.EdcDialog;
import com.glory.edc.collection.EdcDialogForm;
import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcAQLSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.EdcContext;

public class OffLineLotDialog extends EdcDialog {

	public OffLineLotDialog(Shell parent, AbstractEdcSet itemSet, EdcEvent event) {
		super(parent);
		if (itemSet instanceof EdcItemSet){
			this.edcSet = (EdcItemSet)itemSet;
		} else if(itemSet instanceof EdcBinSet){
			this.edcSet = (EdcBinSet)itemSet;
		} else if(itemSet instanceof EdcAQLSet){
            this.edcSet = (EdcAQLSet)itemSet;
        }
		this.event = event;
		this.lot = event.getLot();
	}
	
	@Override
	protected void setInitData() {	
	}
	
	@Override
	public void saveData(List<EdcData> datas) throws Exception {
		try {
			if (edcForm.validate()) {
				edcCurrent = new EdcSetCurrent();
				edcCurrent.setItemSetRrn(edcSet.getObjectRrn());
				if (edcSet instanceof EdcItemSet){
					for (EdcData data : datas) {
						data.setMeasureEqp(String.valueOf(lot.getAttribute1()));
						data.setLineId(lot.getLineId());
						data.setTeamId(Env.getTeam());
						data.setPartName(lot.getPartName());
						data.setPartVersion(lot.getPartVersion());
					}
				} else if(edcSet instanceof EdcBinSet){
					for (EdcData data : datas) {
						//非ByLot，所以设为空
						data.setMeasureEqp(String.valueOf(lot.getAttribute1()));
						data.setLineId(lot.getLineId());
						data.setTeamId(Env.getTeam());
						data.setPartName(lot.getPartName());
						data.setPartVersion(lot.getPartVersion());
					}
				}
				List<Lot> lots = new ArrayList<Lot>();
				lot.setOperator1(Env.getUserName());
				lots.add(lot);
				
				LotManager lotManager = Framework.getService(LotManager.class);
				EdcContext context = new EdcContext();
				if (event.isOfflineFirstInspect()) {
					context.setOfflineFirstInspect(true);
				}
				context.setLotAction(event.getLotAction());
				dcResult = lotManager.edcLotData(lots, datas, EdcData.EDCFROM_OFFLINELOT, true, context, Env.getSessionContext());
				Display.getCurrent().getActiveShell().dispose();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void createDialogForm(Composite com) {
		if (edcSet instanceof EdcItemSet) {
			edcForm = new EdcDialogForm(com, edcCurrent, lot, edcSet, lastDcDatas, toolkit);
			edcForm.createForm();
		} else if(edcSet instanceof EdcBinSet){
			edcForm = new BinEdcDialogForm(com, edcCurrent, lot, edcSet, lastDcDatas, toolkit);
			edcForm.createForm();
		} else if(edcSet instanceof EdcAQLSet){
		    edcForm = new AQLEdcDialogForm(com, edcCurrent, lot, edcSet, lastDcDatas, toolkit);
            edcForm.createForm();
		}
	}

	@Override
	protected void createToolBar(Composite parent) {
		ToolBar tBar = new ToolBar(parent, SWT.HORIZONTAL | SWT.FLAT);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClear(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClose(tBar);
		section.setTextClient(tBar);
	}
	
}
