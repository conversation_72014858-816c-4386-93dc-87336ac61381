package com.glory.edc.generaledc;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.bin.query.adapter.EdcDataBinAdapter;
import com.glory.edc.bin.query.adapter.EdcDataBinTableManager;
import com.glory.edc.collection.query.adapter.EdcGeneralDataAdapter;
import com.glory.edc.collection.query.adapter.EdcGeneralDataTableManager;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.AbstractEdcSetLine;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;

/**
 * 显示处理批次信息,有上下两个部分组成
 * 上部分根据不同的动作显示不同的输入表单
 * 下部分为选中的批次信息,包含不能处理的批次,不能处理的批次也显示,但显示红色且不能选中
 */
public class EDCLineImportDialog extends BaseDialog {
	
	private static int MIN_DIALOG_WIDTH = 750;
	private static int MIN_DIALOG_HEIGHT = 300;
	
	private EdcGeneralDataTableManager tableManager;
	
	private EdcDataBinTableManager binTableManager;
	
	private ListTableManager textTableManager;
	
	private List<EdcData> edcDatas;
	
	private AbstractEdcSet edcSet;
	
	private AbstractEdcSetLine edcSetLine;
	
	public EDCLineImportDialog(List<EdcData> edcDatas, AbstractEdcSet itemSet, AbstractEdcSetLine edcSetLine) {
		super();
		this.edcDatas = edcDatas;
		this.edcSet = itemSet;
		this.edcSetLine = edcSetLine;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			BASManager basManager = Framework.getService(BASManager.class);
			parent.setLayout(new GridLayout(1, true));
			parent.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			FFormToolKit toolkit = new FFormToolKit(parent.getDisplay());
			
			Section section = toolkit.createSection(parent, Section.NO_TITLE | FFormSection.FFORM);
			section.setText(Message.getString("wip.edc_data_info"));
			section.marginWidth = 0;
			section.marginHeight = 0;
			
			GridData gd = new GridData(GridData.FILL_BOTH);
			section.setLayoutData(gd);
			section.setLayout(new GridLayout(1, true));
			
			Composite parameterComp = toolkit.createComposite(section);
			GridLayout layout = new GridLayout(1, true);
			parameterComp.setLayout(layout);
			gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 380;
			parameterComp.setLayoutData(gd);			
			
			if(edcDatas.get(0).getEdcType().equals(EdcData.EDCTYPE_BIN)) {
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "EDCBinQuery");
				
				binTableManager = new EdcDataBinTableManager(adTable);
				//binTableManager.setAutoSizeFlag(true);
				binTableManager.newViewer(parameterComp);
				
				EdcBinSet binSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcBinSet.class, String.valueOf(edcSet.getName()).trim());
				binSet = (EdcBinSet) adManager.getEntity(binSet);
				
				ADTable cloneTable = (ADTable) adTable.clone();
				cloneTable.setObjectRrn(adTable.getObjectRrn());
				List<ADField> cloneFields = new ArrayList<ADField>();
				for (ADField adField : adTable.getFields()) {
					cloneFields.add(adField);
				}
				int i = 0;
				for (EdcBinSetLine line : binSet.getBinSetLines()) {
					ADField binField = new ADField();
					binField.setName(EdcDataBinAdapter.BIN_PREFIX + i);
					binField.setIsDisplay(true);
					binField.setIsMain(true);
					binField.setDisplayLength(24L);
					binField.setLabel(line.getName());
					binField.setLabel_zh(line.getName());
					cloneFields.add(binField);
					i++;
				}
				
				cloneTable.setFields(cloneFields);
				EdcDataBinTableManager tableManager = (EdcDataBinTableManager) binTableManager;
				tableManager.setADTable(cloneTable);
				tableManager.refresh();
				
				tableManager.setInput(edcDatas);
			}else if (edcDatas.get(0).getEdcType().equals(EdcData.EDCTYPE_ITEM)) {
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "EDCDataQuery");
				
				tableManager = new EdcGeneralDataTableManager(adTable);
				//tableManager.setAutoSizeFlag(true);
				tableManager.newViewer(parameterComp);
				
				EdcItemSet itemSet = new EdcItemSet();
				itemSet.setObjectRrn(Long.valueOf(edcSet.getObjectRrn().toString()));
				itemSet = (EdcItemSet) adManager.getEntity(itemSet);
				ADTable cloneTable = (ADTable) adTable.clone();
				cloneTable.setObjectRrn(adTable.getObjectRrn());
			
				
				List<ADField> cloneFields = new ArrayList<ADField>();
				for (ADField adField : adTable.getFields()) {
					cloneFields.add(adField);
				}
				int count = 1;
				
				// 取最大的SubgroupSize的一条记录

					EdcData data = edcDatas.get(0);
					if (EdcItem.DATATYPE_VARIABLE.equals(data.getDataType()) || EdcItemSetLine.DATA_TYPE_FORMULA.equals(data.getDataType())) {
						// VARIABLE类型subgroupSize一定会有值
						if(EdcItemSetLine.DATA_TYPE_FORMULA.equals(data.getDataType())) {
							count = data.getDcData().split(";",-1).length / data.getComponentList().split(";",-1).length;
							data.setSubgroupSize((long)count);
						}else {
							count = data.getSubgroupSize().intValue();
						}
						
						for (int i = 1; i <= count; i++) {
							ADField itemField = new ADField();
							itemField.setName(EdcGeneralDataAdapter.DATA_PREFIX + i);
							itemField.setIsDisplay(true);
							itemField.setIsMain(true);
							itemField.setDisplayLength(24L);
							itemField.setLabel(itemField.getName());
							itemField.setLabel_zh(itemField.getName());
							cloneFields.add(itemField);
						}
					}
					if (EdcItem.DATATYPE_ATTRIBUTE.equals(data.getDataType())) {
						String[] row = data.getDcName().split(";",-1);
						count = row.length;
						
						for (int i = 1; i < count; i++) {
							ADField itemField = new ADField();
							itemField.setName(EdcGeneralDataAdapter.ATTR_PREFIX + i);
							itemField.setIsDisplay(true);
							itemField.setIsMain(true);
							itemField.setDisplayLength(24L);
							itemField.setLabel(row[i]);
							itemField.setLabel_zh(row[i]);
							cloneFields.add(itemField);
						}
					}

				cloneTable.setFields(cloneFields);
				EdcGeneralDataTableManager binTableManager = (EdcGeneralDataTableManager) tableManager;
				binTableManager.setADTable(cloneTable);
				binTableManager.refresh();
				
				binTableManager.setInput(edcDatas);
			}else if (edcDatas.get(0).getEdcType().equals(EdcData.EDCTYPE_TEXT)) {
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "EDCTextQuery");

				textTableManager = new ListTableManager(adTable);
				//textTableManager.setAutoSizeFlag(true);
				textTableManager.newViewer(parameterComp);
				textTableManager.setInput(edcDatas);
			}
			
			toolkit.paintBordersFor(section);
			section.setClient(parameterComp);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return parent;
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		int width = MIN_DIALOG_WIDTH;
		int height = MIN_DIALOG_HEIGHT;
		return new Point(Math.max(convertHorizontalDLUsToPixels(width), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(height), shellSize.y));
	}
}
