package com.glory.edc.generaledc;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFComment;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.export.FileOutputStreamProvider;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.IToolItemListener;

import com.glory.common.excel.upload.MapUpload;
import com.glory.edc.EdcEntry;
import com.glory.edc.collection.EdcDataTableComposite;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.AbstractEdcSetLine;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADImpExp;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.excel.UploadErrorDialog;
import com.glory.framework.base.excel.download.DefaultDownloadWriter;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

@SuppressWarnings("restriction")
public class GeneralEdcGlcEditor extends GlcEditor { 
	public static final String DEFAULT_EXPORT_FILE_NAME = "table_export.xlsx";
	public static final String[] DEFAULT_EXPORT_FILE_TYPE = new String[] { "Excel Workbook (*.xls)", "Excel Workbook (*.xlsx)" };
	public static final String[] DEFAULT_EXPORT_FILE_EXT = new String[] { "*.xls", "*.xlsx" };

	public static final String EDITOR_ID = "bundleclass://com.glory.edc/com.glory.edc.generaledc.GeneralEdcGlcEditor";

	private static final String FIELD_EQUIPMENTID = "equipmentId";
	private static final String FIELD_EQUIPMENINFO = "equipmenInfo";
	private static final String FIELD_ITEMSETNAME = "itemSetName";
	private static final String FIELD_ITEMSET = "itemSet";

	private static final String BUTTON_EXPORT = "export";
	private static final String BUTTON_DCOP = "dcop";
	private static final String BUTTON_REFRESH = "refresh";
	
	protected static final String UPLOAD_TABLE = "BatchGeneralEdcDataUpload";

	protected SearchField equipmentIdField;
	protected EntityFormField equipmenInfoField;
	protected SearchField itemSetNameField;
	protected ListTableManagerField itemSetField;
	
	private Lot lot;
	private Equipment eqp;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		equipmentIdField = form.getFieldByControlId(FIELD_EQUIPMENTID, SearchField.class);
		equipmenInfoField = form.getFieldByControlId(FIELD_EQUIPMENINFO, EntityFormField.class);
		itemSetNameField = form.getFieldByControlId(FIELD_ITEMSETNAME, SearchField.class);
		itemSetField = form.getFieldByControlId(FIELD_ITEMSET, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(IToolItemListener.TYPE_IMPORT), this::importAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EXPORT), this::exportAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DCOP), this::dcopAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		init();
		
		equipmentIdField.addValueChangeListener(queryEquipmentListener);
		itemSetNameField.addValueChangeListener(queryItemSetListener);
	}
	
	// 设定SearchField栏位宽度
	private void init() {
		GridData gText = new GridData();
		gText.horizontalIndent = DPIUtil.autoScaleUpUsingNativeDPI(21);
		gText.widthHint = DPIUtil.autoScaleUpUsingNativeDPI(200);
		equipmentIdField.getControls()[0].setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		equipmentIdField.getControls()[1].setLayoutData(gText);
		equipmentIdField.refresh();
		
		GridData gText1 = new GridData();
		gText1.widthHint = DPIUtil.autoScaleUpUsingNativeDPI(200);
		itemSetNameField.getControls()[0].setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		itemSetNameField.getControls()[1].setLayoutData(gText1);
		itemSetNameField.refresh();
	}
	
	IValueChangeListener queryEquipmentListener = new IValueChangeListener() {
		public void valueChanged(Object arg0, Object arg1) {
			if (equipmentIdField.getValue() != null
					&& ((String) equipmentIdField.getValue()).trim()
							.length() > 0) {
				try {
					RASManager rasManager = Framework.getService(RASManager.class);
					Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(),
							(String) equipmentIdField.getValue());
					equipmenInfoField.setValue(equipment);
					equipmenInfoField.refresh();
				}catch(Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	};
	
	IValueChangeListener queryItemSetListener = new IValueChangeListener() {
		public void valueChanged(Object arg0, Object arg1) {
			if (itemSetNameField.getValue() != null
					&& ((String) itemSetNameField.getValue()).trim()
							.length() > 0) {
				try {
					AbstractEdcSet ItemSet = (AbstractEdcSet) itemSetNameField.getData();
					StringBuffer whereClause = new StringBuffer(" 1 = 1 and edcSetRrn = '" + ItemSet.getObjectRrn() +"'");
					
					ADManager adManager = Framework.getService(ADManager.class);
					List<AbstractEdcSetLine> itemSetLines = adManager.getEntityList(Env.getOrgRrn(), AbstractEdcSetLine.class,
							Integer.MIN_VALUE, Integer.MAX_VALUE, whereClause.toString(), null);
					for (AbstractEdcSetLine line : itemSetLines) {
					    String lsl = (line.getLslString() == null || "".equals(line.getLslString().trim())) ? "-" : line.getLslString();
			            String sl = (line.getSlString() == null || "".equals(line.getSlString().trim())) ? "-" : line.getSlString();
			            String usl = (line.getUslString() == null || "".equals(line.getUslString().trim())) ? "-" : line.getUslString();
			            if (StringUtil.isEmpty(line.getLslString()) && StringUtil.isEmpty(line.getUslString()))
			            	continue;
			            
			            line.setFormula( lsl + "/" + sl + "/" + usl);
					}
					itemSetField.getListTableManager().setInput(itemSetLines);
				}catch(Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		}
	};

	private void importAdapter(Object object) {
		if(equipmentIdField.getValue() != null && itemSetNameField.getData() != null) {
			String equipment = ((String)this.equipmentIdField.getValue()).trim().toUpperCase();
			AbstractEdcSet itemSet = (AbstractEdcSet) itemSetNameField.getData();
			
			//获取采集项
			AbstractEdcSetLine edcSetLine = (AbstractEdcSetLine) itemSetField.getListTableManager().getSelectedObject();
			if(itemSet instanceof EdcItemSet && edcSetLine == null) {
				UI.showError(Message.getString("wip.line_is_not_select"));
				return;
			}
			
			//获取导入数据map
			MapUpload mapUpload = new MapUpload();
			List<Map> valueMap = mapUpload.run();
			GeneralEdcDataUpload upload = new GeneralEdcDataUpload(form.getAuthority(), UPLOAD_TABLE, itemSet, equipment, edcSetLine);
			if(upload.preCheck(valueMap)) {
				if(CollectionUtils.isNotEmpty(upload.uploadTemps)) {
					List<EdcData> edcDatas = processList(upload.uploadTemps);
					//弹出显示框
					EDCLineImportDialog dialog = new EDCLineImportDialog(edcDatas, itemSet, edcSetLine);
					if (Dialog.OK == dialog.open()) {
						upload.cudEntityList();
					}
				}
			}else {
				UploadErrorDialog dialog = new UploadErrorDialog(upload.progress.getErrLogs());
				if(upload.progress.getErrLogs() != null && upload.progress.getErrLogs().size() > 0) {
		        	dialog.open();
	        	}
			}
		}
	}

	private void exportAdapter(Object object) {
		try {
			AbstractEdcSet itemSet = null;
			BASManager basManager = Framework.getService(BASManager.class);
			AbstractEdcSet abstractEdcSet = (AbstractEdcSet) itemSetNameField.getData();
			AbstractEdcSetLine edcSetLine = (AbstractEdcSetLine) itemSetField.getListTableManager().getSelectedObject();
			
			int itemNumber = 0;// 采集片数
			String type = "";// 类型
			
			if (abstractEdcSet instanceof EdcItemSet) {// item类型
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcItemSet.class,abstractEdcSet.getName());
				if(edcSetLine == null) {
					UI.showError(Message.getString("wip.line_is_not_select"));
					return;
				}
				// 获取采集项
				EdcItemSetLine line = (EdcItemSetLine) edcSetLine;
				// 类型
				type = EdcData.EDCTYPE_ITEM;
				itemNumber = line.getItem().intValue();
			} else if (abstractEdcSet instanceof EdcTextSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcTextSet.class,abstractEdcSet.getName());
				// 类型
				type = EdcData.EDCTYPE_TEXT;
				
			} else if (abstractEdcSet instanceof EdcBinSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcBinSet.class,abstractEdcSet.getName());
				// 类型
				type = EdcData.EDCTYPE_BIN;

				itemNumber = Integer.MAX_VALUE;
				
			}
			
			if (itemSet == null) {
				UI.showError(Message.getString("edc.data_set_cannot found"), Message.getString("edc.alert_message_title"));
			} else {
				exportTemplate(form.getAuthority(), type, itemSet, edcSetLine, itemNumber);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 导出模版
	 * authorityName 菜单名称
	 * itemSet 采集项集
	 * edcSetLine 采集项
	 * selectedComponentIds 选择组件号
	 * isComponnet 是否组件
	 * itemNumber item数
	 * @param adAuthorityName
	 */
	public void exportTemplate(String authorityName,String type, AbstractEdcSet itemSet, AbstractEdcSetLine edcSetLine, int itemNumber) {
		BufferedOutputStream bufferedOutputStream = null;
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			String fileName = StringUtil.isEmpty(authorityName) ? DEFAULT_EXPORT_FILE_NAME : authorityName.replace(".", "") + ".xlsx";
			FileOutputStreamProvider provider = new FileOutputStreamProvider(fileName, DEFAULT_EXPORT_FILE_TYPE, DEFAULT_EXPORT_FILE_EXT);
			OutputStream outputStream = provider.getOutputStream(UI.getActiveShell());
			if (outputStream == null) {
				return;
			}
			bufferedOutputStream = new BufferedOutputStream(outputStream);

			XSSFSheet sheet = workbook.createSheet();
			DataFormat format = workbook.createDataFormat();
			XSSFCellStyle commonStyle = workbook.createCellStyle();
			commonStyle.setDataFormat(format.getFormat("@"));

			// 从ADImpExpFieldMap表中导出
			ADManager adManager = Framework.getService(ADManager.class);
			ADImpExp adImpExp = adManager.getADImpExpByAuthorityName(Env.getOrgRrn(), authorityName, null, true);
			if (adImpExp != null) {
				List<ADField> exportFields = adManager.buildADFieldByADImpExp(adImpExp, true, Env.getSessionContext());
				if (CollectionUtils.isNotEmpty(exportFields)) {
					XSSFDrawing draw = sheet.createDrawingPatriarch();
					XSSFComment comment = null;
					XSSFRow firstRow = sheet.createRow(0);// 第一行表头
					int i = 0;
					for (ADField exportField : exportFields) {
						XSSFCell cell = firstRow.createCell(i);
						if (!StringUtil.isEmpty(exportField.getColumnName())) {
							cell.setCellValue(exportField.getColumnName());
						} else {
							cell.setCellValue(exportField.getName().toUpperCase());
						}
						comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
						comment.setString(DefaultDownloadWriter.getCellComment(exportField));
						cell.setCellComment(comment);
						i++;
					}
					
					int v = 3;
					//根据采集点位生成列
					if(type.equals(EdcData.EDCTYPE_ITEM)) {
						EdcItemSetLine line = (EdcItemSetLine)edcSetLine;
						String[] itemDescs = EdcDataTableComposite.createIds(line, null, false,itemNumber);
						if(itemDescs != null && itemDescs.length > 0) {//插入列
							for(int c = 0; c < itemDescs.length; c++) {
								XSSFCell cell = firstRow.createCell(i);
								cell.setCellValue(itemDescs[c].toUpperCase());
								comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
								comment.setString(itemDescs[c].toUpperCase());
								cell.setCellComment(comment);
								sheet.autoSizeColumn(i, true);
								v++;
								i++;
							}
						}
					}else if(type.equals(EdcData.EDCTYPE_BIN)) {
						List<EdcBinSetLine> binSetLines = (List<EdcBinSetLine>) itemSetField.getListTableManager().getInput();
						for (EdcBinSetLine binSetLine : binSetLines) {
							XSSFCell cell = firstRow.createCell(i);
							cell.setCellValue((itemSet.getName() + "-" + binSetLine.getName()).toUpperCase());
							comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
							comment.setString((itemSet.getName() + "-" + binSetLine.getName()).toUpperCase());
							cell.setCellComment(comment);
							sheet.autoSizeColumn(i, true);
							v++;
							i++;
						}
					}else if(type.equals(EdcData.EDCTYPE_TEXT)) {
						List<EdcTextSetLine> textSetLines = (List<EdcTextSetLine>) itemSetField.getListTableManager().getInput();
						for (EdcTextSetLine textSetLine : textSetLines) {
							XSSFCell cell = firstRow.createCell(i);
							cell.setCellValue((itemSet.getName() + "-" + textSetLine.getName()).toUpperCase());
							comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, 3, 2, 5, 4));
							comment.setString((itemSet.getName() + "-" + textSetLine.getName()).toUpperCase());
							cell.setCellComment(comment);
							sheet.autoSizeColumn(i, true);
							v++;
							i++;
						}
					}
					
					//写入导出模板值
					XSSFRow row = sheet.createRow(1);
					
					row.createCell(0).setCellValue(((String)this.equipmentIdField.getValue()).trim().toUpperCase());
					
					row.createCell(1).setCellValue(itemSet.getName());
					
					row.createCell(2).setCellValue(itemSet.getEdcType());
					
					if(type.equals(EdcData.EDCTYPE_BIN) || type.equals(EdcData.EDCTYPE_TEXT)) {
						row.createCell(3).setCellValue("");
					}else {
						row.createCell(3).setCellValue(edcSetLine.getName());
					}
					
					//写入默认空的值，方法拿不到空值列
					for(int k = 4; k < v; k++) {
						row.createCell(k).setCellValue("");
					}
				}
				// 设置列宽自适应
				int columnNum = 0;
				for (ADField adField : exportFields) {
					sheet.autoSizeColumn(columnNum, true);
					columnNum++;
				}
			}

			workbook.write(bufferedOutputStream);
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonExportSuccessed()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			try {
				if (bufferedOutputStream != null) {
					bufferedOutputStream.close();
				}
				workbook.close();
			} catch (IOException e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}

	private void dcopAdapter(Object object) {
		try {
			if (this.equipmentIdField.getValue() == null || "".equals(((String)this.equipmentIdField.getValue()).trim())) {
				UI.showError(Message.getString("edc.equipmentNumber_cannot_null"), Message.getString("edc.alert_message_title"));
				return;
			} else if (this.itemSetNameField.getValue() == null || "".equals(((String)this.itemSetNameField.getValue()).trim())) {
				UI.showError(Message.getString("edc.data_set_cannot_null"), Message.getString("edc.alert_message_title"));
				return;
			}
			RASManager rasManager = Framework.getService(RASManager.class);
			try {
				eqp = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), ((String)this.equipmentIdField.getValue()).toUpperCase());
				if (eqp == null) {
					UI.showError(Message.getString("edc.eqp_not_exist"), Message.getString("edc.alert_message_title"));
				}
			} catch (Exception e) {
				UI.showError(Message.getString("edc.eqp_not_exist"), Message.getString("edc.alert_message_title"));
				return;
			}

			AbstractEdcSet abstractEdcSet = (AbstractEdcSet) itemSetNameField.getData();
			AbstractEdcSet itemSet = null;
			BASManager basManager = Framework.getService(BASManager.class);
			if (abstractEdcSet instanceof EdcItemSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcItemSet.class, abstractEdcSet.getName());
			} else if (abstractEdcSet instanceof EdcTextSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcTextSet.class, abstractEdcSet.getName());
			} else if (abstractEdcSet instanceof EdcBinSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcBinSet.class, abstractEdcSet.getName());
			}

			if (itemSet == null) {
				UI.showError(Message.getString("edc.data_set_cannot found"), Message.getString("edc.alert_message_title"));
			} else {
				EdcSetCurrent edcCurrent = new EdcSetCurrent(); 
				edcCurrent.setItemSetRrn(itemSet.getObjectRrn());
				lot = new Lot();
				lot.setEquipmentId(eqp.getEquipmentId());
				lot.setMainQty(BigDecimal.ZERO);
				EdcEntry.open(EdcData.EDCFROM_GENERAL, edcCurrent, null, lot);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void refreshAdapter(Object object) {
		equipmentIdField.setValue(null);
		itemSetNameField.setValue(null);
	}
	
	
	/*
	 * 特殊处理EdcData数据，实际测量的数据超过设置的EdcSet时：
	 * 例如：设置测量点数384点，测量片数1片，实际测量片数2片 
	 * 数据存储为：ComponentList(片1；片2) DcData(384 * 2) 界面查询数据显示wafer两片，data数只显示片1数据。
	 * 数据显示异常，做如下处理：
	 * 1.当DcData数能被SubgroupSize整除且结果超过1时，将EdcData数据一分为多
	 * 2.不满足以上条件保留当前数据显示
	*/
	private List<EdcData> processList(List<EdcData> adList){
		List<EdcData> newEdcDatas = new ArrayList<>();
		try {
			for(EdcData object : adList) {
				EdcData edcData = (EdcData) object;
				String [] data = edcData.getDcData().split(";",-1);
				String [] dataName = edcData.getDcName().split(";",-1);
				List<String> dataList = Arrays.asList(data);
				List<String> dataNameList = Arrays.asList(dataName);
				List<String> currList = null;
				List<String> dcNameCurrList = null;
				if(edcData.getSubgroupSize() != null && edcData.getSubgroupSize() != 0 
						&& data.length % edcData.getSubgroupSize() == 0 && edcData.getComponentList() != null) {
					int subgroupSize = edcData.getDcData().split(";",-1).length / edcData.getComponentList().split(";",-1).length;
					edcData.setSubgroupSize((long)subgroupSize);
					int length = (int) (data.length / edcData.getSubgroupSize());
					String [] component = edcData.getComponentList().split(";",-1);
					if(length > 1 && component.length == length) {
						for(int i = 0; i < length; i++) {
							EdcData cloneData = (EdcData) edcData.clone();
							currList = dataList.subList(i * edcData.getSubgroupSize().intValue(), (i + 1) * edcData.getSubgroupSize().intValue());
							dcNameCurrList = dataNameList.subList(i * edcData.getSubgroupSize().intValue(), (i + 1) * edcData.getSubgroupSize().intValue());
							cloneData.setComponentList(component[i]);
							cloneData.setDcData(currList.stream().map(String :: valueOf).collect(Collectors.joining(";")));
							cloneData.setDcName(dcNameCurrList.stream().map(String :: valueOf).collect(Collectors.joining(";")));
							cloneData.setObjectRrn(edcData.getObjectRrn());
							newEdcDatas.add(cloneData);
						}
					}else {
						newEdcDatas.add(edcData);
					}
				} else {
					newEdcDatas.add(edcData);
				}
			}
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
		}
		return newEdcDatas;
	}
}