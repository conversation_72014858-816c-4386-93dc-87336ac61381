package com.glory.edc.generaledc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;

import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcDataItem;
import com.glory.edc.collection.EdcDataTableComposite;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.AbstractEdcSetLine;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADImpExp;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.excel.UploadErrorLog;
import com.glory.framework.base.excel.UploadException;
import com.glory.framework.base.excel.UploadProgress;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class GeneralEdcDataUpload {
	
	public String authorityName;
	
	public String euipmentId = "";//设备
	
	public List<Lot> edcLots = new ArrayList<Lot>();
	
	public Lot lot = new Lot();
	
	public List<EdcData> uploadTemps = new ArrayList<EdcData>();
	
	public AbstractEdcSet itemSet = new AbstractEdcSet(); 
	
	public int row = 2;
	
	public AbstractEdcSetLine edcSetLine;
	
	public List<String> fields = new ArrayList<String>(); //配置表字段，用于筛选出动态增加的data列
	
	public UploadProgress progress ;
	
	public static final String HEADER_LOTID = "LOTID";
	
	public static final String HEADER_EQUIPMENT = "EQUIPMENT";
	
	public static final String HEADER_EDCSETNAME = "EDCSETNAME";
	
	public static final String HEADER_EDCTYPE = "EDCTYPE";
	
	public static final String HEADER_ITEMNAME = "ITEMNAME";
	
	public static final String COMPONENT = "COMPONENT";
	
	public GeneralEdcDataUpload(String authorityName, String tableName , AbstractEdcSet itemSet, String euipmentId, AbstractEdcSetLine edcSetLine) {
		this.itemSet = itemSet;
		this.euipmentId = euipmentId;
		this.edcSetLine = edcSetLine;
		this.authorityName = authorityName;
		this.lot.setEquipmentId(euipmentId);
		progress = new UploadProgress(authorityName, null, tableName);
	}

	protected void cudEntityList() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			if (CollectionUtils.isNotEmpty(uploadTemps)) {
				for (EdcData data : uploadTemps) {
					if (itemSet instanceof EdcItemSet) {
						data.setMeasureEqp(lot.getEquipmentId());
						data.setLineId(lot.getLineId());
						data.setTeamId(Env.getTeam());
						data.setOperator(Env.getUserName());
						data.setPartName(lot.getPartName());
						data.setPartVersion(lot.getPartVersion());
					} else if (itemSet instanceof EdcBinSet) {
						data.setMeasureEqp(lot.getEquipmentId());
						data.setLineId(lot.getLineId());
						data.setTeamId(Env.getTeam());
						data.setOperator(Env.getUserName());
						data.setPartName(lot.getPartName());
						data.setPartVersion(lot.getPartVersion());
					} else if (itemSet instanceof EdcTextSet) {
						data.setMeasureEqp(lot.getEquipmentId());
						data.setLineId(lot.getLineId());
						data.setTeamId(Env.getTeam());
						data.setOperator(Env.getUserName());
						data.setPartName(lot.getPartName());
						data.setPartVersion(lot.getPartVersion());
					}
				}
				lotManager.edcGeneralData(uploadTemps, EdcData.EDCFROM_GENERAL, false, Env.getSessionContext());
				// 更新修改时间
				UI.showInfo(Message.getString("com.import_success"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean preCheck(List<Map> valueMap) throws UploadException {
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			if(itemSet instanceof EdcItemSet) {
				itemSet = (AbstractEdcSet)edcManager.getEdcSet(Env.getOrgRrn(), itemSet.getName(), itemSet.getVersion(), true, false);
				if(CollectionUtils.isNotEmpty(itemSet.getEdcSetLine())) {
					for(AbstractEdcSetLine line : itemSet.getEdcSetLine()) {
						if(line.getName().equals(edcSetLine.getName())) {
							edcSetLine = line;
						}
					}
				}
			}else {
				ADManager adManager = Framework.getService(ADManager.class);
				itemSet = (AbstractEdcSet)adManager.getEntity(itemSet);
			}
			
			//获取信息
			getInfo();
			
			if(CollectionUtils.isNotEmpty(valueMap)) {
				for (int i = 0; i < valueMap.size(); i++) {
					if(valueMap.get(i).get(HEADER_EQUIPMENT) == null || !valueMap.get(i).get(HEADER_EQUIPMENT).equals(euipmentId)) {
						addErrorLog(Message.getString("wip.equipment_id_is_not_same"), row, "EQUIPMENT_ID");
					}
					
					if(itemSet instanceof EdcItemSet && (valueMap.get(i).get(HEADER_EDCTYPE) == null || !valueMap.get(i).get(HEADER_EDCTYPE).equals(EdcData.EDCTYPE_ITEM))) {
						addErrorLog(Message.getString("wip.type_not_same"), row, "TYPE");
					}else if(itemSet instanceof EdcBinSet && (valueMap.get(i).get(HEADER_EDCTYPE) == null || !valueMap.get(i).get(HEADER_EDCTYPE).equals(EdcData.EDCTYPE_BIN))) {
						addErrorLog(Message.getString("wip.type_not_same"), row, "TYPE");
					}else if(itemSet instanceof EdcTextSet && (valueMap.get(i).get(HEADER_EDCTYPE) == null || !valueMap.get(i).get(HEADER_EDCTYPE).equals(EdcData.EDCTYPE_TEXT))) {
						addErrorLog(Message.getString("wip.type_not_same"), row, "TYPE");
					}
					
                    TreeMap<String, String> map = new TreeMap<String, String>(valueMap.get(i));
                    List<EdcData> edcdats = new ArrayList<EdcData>();
                    if(valueMap.get(i).get(HEADER_EDCTYPE).equals("ITEM")) {
                    	edcdats = getItemEdcData(map);
                    }else if (valueMap.get(i).get(HEADER_EDCTYPE).equals("BIN")) {
                    	edcdats = getBinEdcData(map);
					}else if (valueMap.get(i).get(HEADER_EDCTYPE).equals("TEXT")) {
                    	edcdats = getTextEdcData(map);
					}
					
					if(CollectionUtils.isNotEmpty(edcdats)) {
						//检查强制输入
						checkMandatory(edcdats, itemSet);
						uploadTemps.addAll(edcdats);
					}
					
					row++;
				}
			}else {
				return false;
			}
		} catch (Exception e) {
			UI.showError(Message.getString(e.toString()));
			e.printStackTrace();

		}
		return progress.isSuccess();
	}
	
	/**
	 * 获取信息
	 */
	protected void getInfo() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			//获取配置表字段
			ADImpExp adImpExp = adManager.getADImpExpByAuthorityName(Env.getOrgRrn(), authorityName, null, true);
			if(adImpExp != null) {
				List<ADField> exportFields = adManager.buildADFieldByADImpExp(adImpExp, true, Env.getSessionContext());
				if(CollectionUtils.isNotEmpty(exportFields)) {
					for(ADField adField : exportFields) {
						//用于筛选动态增加的采集Value值字段
						if (!StringUtil.isEmpty(adField.getColumnName())) {
							fields.add(adField.getColumnName().toUpperCase());
						} else {
							fields.add(adField.getName().toUpperCase());
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 获取组装Text类型datas
	 */
	protected List<EdcData> getTextEdcData(TreeMap<String, String> map) {
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		try {
			EdcTextSet edcTextSet = (EdcTextSet) itemSet;
			List<EdcTextSetLine> lines = edcTextSet.getTextSetLines();
			//效验生成是否正确
			if(verifyTextcolumn(map, lines)) {
				for (EdcTextSetLine line : lines) {
					EdcData edcData = new EdcData();
					edcData.setOrgRrn(Env.getOrgRrn());
					edcData.setOrgId(Env.getOrgName());
					edcData.setMeasureTime(new Date());
					edcData.setEdcSetRrn(itemSet.getObjectRrn());
					edcData.setEdcSetName(itemSet.getName());
					edcData.setEdcType(EdcData.EDCTYPE_TEXT);
					edcData.setEdcSetVersion(itemSet.getVersion());
					edcData.setItemName(line.getName());
					edcData.setDcName(line.getName());
					
					//匹配数据类型
					String value = map.get(edcTextSet.getName() + "-" + line.getName());
                    if(EdcTextSetLine.DATATYPE_BOOLEAN.equals(line.getDataType())) {
						if(!(value.equals("Y") || value.equals("N"))) {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).toUpperCase());
						}
					}else if (EdcTextSetLine.DATATYPE_DOUBLE.equals(line.getDataType())) {
						if(!match("^\\d+(\\.\\d+)?$", value.toString())) {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).toUpperCase());
						}
					}else if (EdcTextSetLine.DATATYPE_INTEGER.equals(line.getDataType())) {
						if(!match("^[1-9]\\d*$", value.toString())) {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).toUpperCase());
						}
					}else if (EdcTextSetLine.DATATYPE_ADFIELD.equals(line.getDataType())){
						ADManager adManager = Framework.getService(ADManager.class);
						String whereClause = " referenceName = '" + line.getSampleType() + "'";
						List<ADURefList> list = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, Env.getMaxResult(), whereClause, "seqNo");
						if(CollectionUtils.isNotEmpty(list)) {
							Boolean isVerf = false;
							for(ADURefList uList : list) {
								if(uList.getText().equals(value)) {
									value = uList.getKey();
									isVerf = true;
								}
							}
							if(!isVerf) {
								value = "";
								addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).trim());
							}
						}else {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).trim());
						}
					}
					edcData.setDcData(value);
					edcData.setUsl(DBUtil.toDouble(line.getUslString()));
					edcData.setLsl(DBUtil.toDouble(line.getLslString()));
					edcData.setSl(DBUtil.toDouble(line.getSlString()));
					edcData.setOperator(Env.getUserName());
					if (lot != null) {
						edcData.setLineId(lot.getLineId());
						edcData.setLotRrn(lot.getObjectRrn());
						edcData.setBatchId(lot.getBatchId());
						edcData.setBatchLots("");
						edcData.setLotCount(null);
						edcData.setLotType(lot.getLotType());
						edcData.setLotId(lot.getLotId());
						edcData.setPartName(lot.getPartName());
						edcData.setPartVersion(lot.getPartVersion());
						edcData.setStepName(lot.getStepName());
						edcData.setStepVersion(lot.getStepVersion());
						edcData.setMaterialId(null);
						edcData.setCustomer(lot.getCustomerCode());
						edcData.setMeasureEqp(map.get(HEADER_EQUIPMENT));
						edcData.setProcessEqp("");
					}
					edcDatas.add(edcData);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return edcDatas;
	}
	
	/**
	 * 获取组装Bin类型datas
	 */
	protected List<EdcData> getBinEdcData(TreeMap<String, String> map) {
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		try {
			if (itemSet instanceof EdcBinSet) {
				EdcBinSet edcBinSet = (EdcBinSet) itemSet;
				List<EdcBinSetLine> lines = edcBinSet.getBinSetLines();

				// 效验列
				if (verifyBincolumn(map, lines)) {
					EdcData edcData = new EdcData();
					edcData.setOrgRrn(Env.getOrgRrn());
					edcData.setOrgId(Env.getOrgName());

					String name = null;
					String value = null;
					String oosList = "";

					// 计算出总数
					BigDecimal totalQty = BigDecimal.ZERO;
					BigDecimal badQty = BigDecimal.ZERO;
					for (EdcBinSetLine line : lines) {
						if (EdcBinSetLine.BINTYPE_GROUP.equals(line.getBinType())
								|| EdcBinSetLine.BINTYPE_OTHER.equals(line.getBinType())) {
							continue;
						}
						if (EdcData.ATTRIBUTE_TOTAL.equals(line.getName())) {
							continue;
						} else if (map.get(itemSet.getName() + "-" + line.getName()) != null
								&& map.get(itemSet.getName() + "-" + line.getName()).trim().length() > 0) {
							if (EdcBinSetLine.BINTYPE_FAIL.equals(line.getBinType())) {
								badQty = badQty.add(new BigDecimal((map.get(itemSet.getName() + "-" + line.getName()).trim())));
							}
							totalQty = totalQty.add(new BigDecimal((map.get(itemSet.getName() + "-" + line.getName()).trim())));
						}
					}

					// 循环写入Value
					int num = 2;
					for (EdcBinSetLine line : lines) {
						if (name == null) {
							name = DBUtil.toString(line.getName());
							value = DBUtil.toString(map.get(itemSet.getName() + "-" + line.getName()));
							// 整体良率OOS记入oosList的第一个栏位
							EdcDataItem dataItem = new EdcDataItem();
							if(map.get(itemSet.getName() + "-" + line.getName()) != null && !StringUtil.isEmpty(map.get(itemSet.getName() + "-" + line.getName()))) {
								dataItem.setValue(String.valueOf(map.get(itemSet.getName() + "-" + line.getName())));
							}
							dataItem.setDescription(line.getDescription());
							dataItem.setSpecType(line.getSpecType());
							dataItem.setGroup(line.getBinGroup());
							if (!StringUtil.isEmpty(line.getUslString())) {
								dataItem.setUsl(Double.valueOf(line.getUslString()));
							}
							if (!StringUtil.isEmpty(line.getSlString())) {
								dataItem.setSl(Double.valueOf(line.getSlString()));
							}
							if (!StringUtil.isEmpty(line.getLslString())) {
								dataItem.setLsl(Double.valueOf(line.getLslString()));
							}
							dataItem.setFlag(line.getBinType());

							if (!dataItem.checkSpec(totalQty.doubleValue())) {
								oosList += num + ";";
							}
						} else {
							name += ";" + DBUtil.toString(line.getName());
							value += ";" + DBUtil.toString(map.get(itemSet.getName() + "-" + line.getName()));
							// 整体良率OOS记入oosList的第一个栏位
							EdcDataItem dataItem = new EdcDataItem();
							if(map.get(itemSet.getName() + "-" + line.getName()) != null && !StringUtil.isEmpty(map.get(itemSet.getName() + "-" + line.getName()))) {
								dataItem.setValue(String.valueOf(map.get(itemSet.getName() + "-" + line.getName())));
							}
							dataItem.setDescription(line.getDescription());
							dataItem.setSpecType(line.getSpecType());
							dataItem.setGroup(line.getBinGroup());
							if (!StringUtil.isEmpty(line.getUslString())) {
								dataItem.setUsl(Double.valueOf(line.getUslString()));
							}
							if (!StringUtil.isEmpty(line.getSlString())) {
								dataItem.setSl(Double.valueOf(line.getSlString()));
							}
							if (!StringUtil.isEmpty(line.getLslString())) {
								dataItem.setLsl(Double.valueOf(line.getLslString()));
							}
							dataItem.setFlag(line.getBinType());
							if(!dataItem.checkSpec(totalQty.doubleValue())) {
								oosList += num + ";";
							}
						}
						num++;
					}

					Double doubleUsl = DBUtil.toDouble(edcBinSet.getUslString());
					Double doubleSl = DBUtil.toDouble(edcBinSet.getSlString());
					Double doubleLsl = DBUtil.toDouble(edcBinSet.getLslString());

					edcData.setDcName("TOTAL;" + name);
					edcData.setDcData(totalQty + ";" + value);

					EdcDataItem totalItem = new EdcDataItem();
					totalItem.setValue(String.valueOf(totalQty.doubleValue()));
					totalItem.setSpecType(AbstractEdcSetLine.SPECTYPE_PERCENT);
					totalItem.setUsl(doubleUsl);
					totalItem.setSl(doubleSl);
					totalItem.setLsl(doubleLsl);
					if (!totalItem.checkSpec(totalQty.doubleValue())) {
						edcData.setOosList("1;" + oosList);
					} else {
						edcData.setOosList(oosList);
					}

					edcData.setTotalQty(totalQty);
					edcData.setBadQty(badQty);
					edcData.setMeasureTime(new Date());
					edcData.setEdcSetRrn(edcBinSet.getObjectRrn());
					edcData.setEdcSetName(edcBinSet.getName());
					edcData.setEdcType(EdcData.EDCTYPE_BIN);
					edcData.setEdcSetVersion(edcBinSet.getVersion());
					edcData.setSeqNo(10L);
					edcData.setUsl(doubleUsl);
					edcData.setSl(doubleSl);
					edcData.setLsl(doubleLsl);
					edcData.setOperator(Env.getUserName());
					if (lot != null) {
						edcData.setLineId(lot.getLineId());
						edcData.setLotRrn(lot.getObjectRrn());
						edcData.setBatchId(lot.getBatchId());
						edcData.setBatchLots("");
						edcData.setLotCount(null);
						edcData.setLotType(lot.getLotType());
						edcData.setLotId(lot.getLotId());
						edcData.setPartName(lot.getPartName());
						edcData.setPartVersion(lot.getPartVersion());
						edcData.setStepName(lot.getStepName());
						edcData.setStepVersion(lot.getStepVersion());
						edcData.setMaterialId(null);
						edcData.setCustomer(lot.getCustomerCode());
						edcData.setMeasureEqp(map.get(HEADER_EQUIPMENT));
						edcData.setProcessEqp("");
					}

					edcDatas.add(edcData);
				}
			}
			return edcDatas;
		} catch (Exception e) {
			UI.showError(e.toString());
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * Bin类型效验生成字段
	 */
	protected Boolean verifyBincolumn(TreeMap<String, String> map, List<EdcBinSetLine> lines) {
		Boolean verify = true;
		String columnName = "";
		for (EdcBinSetLine line : lines) {
			// 拼接生成列
			if (StringUtil.isEmpty(columnName)) {
				columnName = itemSet.getName() + "-" + line.getName();
			} else {
				columnName += ";" + itemSet.getName() + "-" + line.getName();
			}
		}
		String[] itemDescs = columnName.split(";");
		// 判断列是否存在
		for (String key : map.keySet()) {
			if (!fields.contains(key)) {
				if (!Arrays.asList(itemDescs).contains(key)) {
					addErrorLog(Message.getString("wip.import_field_not_exist"), row, key.toUpperCase());
					verify = false;
				}
			}
		}
		
		// 判断导入列是否少
		for (String column : Arrays.asList(itemDescs)) {
			if (!map.keySet().contains(column)) {
				addErrorLog(Message.getString("wip.import_field_not_exist"), row, column.toUpperCase());
				verify = false;
			}
		}
		return verify;
	}
	
	/**
	 * Text类型效验生成字段
	 */
	protected Boolean verifyTextcolumn(TreeMap<String, String> map, List<EdcTextSetLine> lines) {
		Boolean verify = true;
		String columnName = "";
		for (EdcTextSetLine line : lines) {
			// 拼接生成列
			if (StringUtil.isEmpty(columnName)) {
				columnName = itemSet.getName() + "-" + line.getName();
			} else {
				columnName += ";" + itemSet.getName() + "-" + line.getName();
			}
		}
		String[] itemDescs = columnName.split(";");
		
		// 判断导入列是否多出
		for (String key : map.keySet()) {
			if (!fields.contains(key)) {
				if (!Arrays.asList(itemDescs).contains(key)) {
					addErrorLog(Message.getString("wip.import_field_oversupply"), row, key.toUpperCase());
					verify = false;
				}
			}
		}
		
		// 判断导入列是否少
		for(String column : Arrays.asList(itemDescs)) {
			if(!map.keySet().contains(column)) {
				addErrorLog(Message.getString("wip.import_field_not_exist"), row, column.toUpperCase());
				verify = false;
			}
		}
		
		return verify;
	}
	
	/**
	 * 获取组装Item类型datas
	 */
	protected List<EdcData> getItemEdcData(TreeMap<String, String> map) {
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		EdcData edcData = new EdcData();
		try {
			EdcItemSetLine line = (EdcItemSetLine)edcSetLine;
			int itemNumber = line.getItem().intValue();
			
			//生成应该生成的列信息
			String[] itemDescs = EdcDataTableComposite.createIds(line, null, false, itemNumber);
			List<String> itemDescNames = new ArrayList<String>();
			if(itemDescs.length > 0) {
				for(int i = 0; i < itemDescs.length; i ++) {
					itemDescNames.add(itemDescs[i].toUpperCase());
				}
			}
			
			
			edcData.setOrgRrn(Env.getOrgRrn());
			edcData.setOrgId(Env.getOrgName());

			String name = null;
			String value = null;

			// 循环写入Value
			for (String key : map.keySet()) {
				if (!fields.contains(key)) {
					//判断列是否存在
					if(!itemDescNames.contains(key)) {
						addErrorLog(Message.getString("wip.import_field_not_exist"), row, key.toUpperCase());
					}
					if (name == null) {
						name = DBUtil.toString(key);
						value = DBUtil.toString(map.get(key));
					} else {
						name += ";" + DBUtil.toString(key);
						value += ";" + DBUtil.toString(map.get(key));
					}
				}
			}

			edcData.setDcName(name);
			edcData.setDcData(value);
			edcData.setItemName(line.getName());

			edcData.setMeasureTime(new Date());
			edcData.setEdcType(EdcData.EDCTYPE_ITEM);
			edcData.setEdcSetRrn(line.getEdcSetRrn());
			edcData.setEdcSetName(line.getItemSet().getName());
			edcData.setEdcSetVersion(line.getItemSet().getVersion());
			edcData.setItemName(line.getName());
			edcData.setSeqNo(line.getSeqNo());
			edcData.setUsl(DBUtil.toDouble(line.getUslString()));
			edcData.setLsl(DBUtil.toDouble(line.getLslString()));
			edcData.setSl(DBUtil.toDouble(line.getSlString()));
			edcData.setSubgroupSize(line.getSubgroupSize() != null ? line.getSubgroupSize().longValue() : null);
			edcData.setSampleSize(line.getSampleSize());
			edcData.setSamplePlan(line.getSamplePlan(new Long(itemNumber)));
			edcData.setDataType(line.getEdcItem().getDataType());
			edcData.setSampleType(line.getSampleType());
			if (lot != null) {
				edcData.setLineId(lot.getLineId());
				edcData.setLotRrn(lot.getObjectRrn());
				edcData.setBatchId(lot.getBatchId());
				edcData.setBatchLots("");
				edcData.setLotCount(null);
				edcData.setLotType(lot.getLotType());
				edcData.setLotId(lot.getLotId());
				edcData.setPartName(lot.getPartName());
				edcData.setPartVersion(lot.getPartVersion());
				edcData.setStepName(lot.getStepName());
				edcData.setStepVersion(lot.getStepVersion());
				edcData.setMaterialId(null);
				edcData.setCustomer(lot.getCustomerCode());
				edcData.setMeasureEqp(map.get(HEADER_EQUIPMENT));
				edcData.setOperator(Env.getUserName());
				edcData.setProcessEqp("");
				edcData.setLineId(lot.getLineId());
			}
			edcData.setTeamId(Env.getTeam());
			String[] valueStrings = edcData.getDcData().split(";");
			if (valueStrings.length > 0) {
				edcData.setDcDataAvg(DBUtil.toDouble(edcData.buildDcDataAvg()));
				edcData.setOosList(edcData.buildOosList());
			}

			edcData.setIsTemp(false);
			edcData.setObjectRrn(null);
			if (itemSet.getIsRepeatable()) {
				edcData.setIsRetest(EdcSetCurrent.TEST_TYPE_RETEST);
			}

			lot.setOperator1(Env.getUserName());
			edcDatas.add(edcData);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return edcDatas;
	}
	
	
	/**
	 * 在多个数据采集项时
	 * 不保存没有输入数据的采集结果
	 */
	public List<EdcData> removeEmptyEdcData(List<EdcData> datas){
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		for (EdcData data : datas) {
			if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())) {
				edcDatas.add(data);
			} else if (data.getDcData() != null) {
				String dcDatas[] = data.getDcData().split(";");
				if (!data.getDcData().equals("") 
						&& dcDatas != null && dcDatas.length != 0){
					edcDatas.add(data);
				}
			}
		}
		return edcDatas;
	}
	
	/**
	 * 检查是否强制输入，如果强制输入
	 * 1，Variable类型数据必须每个都输入值
	 * 2，Attribute、BIN类型的数据Total必须有值且不能为0
	 * 3.所有选择了选择设备的数据采集都必须选择设备
	 * 4.文本数据采集设置了采集项为必填的检查强制输入
	 */
	public void checkMandatory(List<EdcData> dcDatas, AbstractEdcSet edcSet){

		if (edcSet instanceof EdcItemSet){
			for (EdcItemSetLine line : ((EdcItemSet) edcSet).getItemSetLines()) {
				//如果为必须数据采集
				if (line.getIsMandatory()){
					//判断是否有生成dcDatas
					boolean check = false;
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							check = true;
							if(line.getDataType().equals(EdcItem.DATATYPE_ATTRIBUTE)){
								//检查Attribute类型,Total必须有值且不能为0
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								for (int i = 0; i < edcIds.length; i++) {
									if (EdcData.ATTRIBUTE_TOTAL.equals(edcIds[i])) {
										if (i < edcDatas.length) {
											String totalData = edcDatas[i];
											if (totalData.trim().length() > 0 && !"0".equals(totalData.trim())) {
												return;
											}
										}
									}
								}
								addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
							} else {
								//检查Variable类型
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								//检查长度是否相同
								if (!(edcIds.length == edcDatas.length)) {
									addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
								}
								//检查每个栏位是否有值
								for (String edcData : edcDatas) {
									if (edcData.trim().length() == 0) {
										addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
									}
								}
							}
						}
					}
					//如果设置了必输，但未生成dcDatas未录入值，则也进行卡控
					if(!check) {
						addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
					}
				}
				
				if (line.getIsJudgeByManual()) {
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (StringUtil.isEmpty(data.getJudge1())) {
								addErrorLog(Message.getString("edc.data_judge_must_input"), row, "DATA");
							}
						}
					}
				}
			}
		} else if (edcSet instanceof EdcBinSet){
			//如果是BIN数据采集
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (StringUtil.isEmpty(data.getJudge1())) {
						addErrorLog(Message.getString("edc.data_judge_must_input"), row, "DATA");
					}
				}
			}
		} else if (edcSet instanceof EdcTextSet) {
			for (EdcTextSetLine line : ((EdcTextSet) edcSet).getTextSetLines()) {
				if (edcSet.getIsShowEquipment()) {
					for (EdcData edcData : dcDatas) {
						if (edcData.getMeasureEqp().equals("") || edcData.getMeasureEqp() == null) {
							addErrorLog(Message.getString("common.is_require"), row, "DATA");
						}
					}
				}
				//如果为必须数据采集
				if (line.getIsMandatory()){
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (data.getDcData().equals("") || data.getDcData() == null) {
								addErrorLog(Message.getString("common.is_require"), row, "DATA");
							}
						}
					}
				}
			}	
			
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())){
						if (StringUtil.isEmpty(data.getJudge1())) {
							addErrorLog(Message.getString("edc.data_judge_must_input"), row, "DATA");
						}
					}
				}
			}
		}
	}
	
	/**
	 * @param 批次是否Component
	 */
	protected boolean isComponentUnitType() {
		if (lot != null) {
			if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				return true;
			} else {
				return false;
			}
		}
		return false;
	}
	
	/**
	 * @param regex 正则表达式字符串
	 * @param str   要匹配的字符串
	 * @return 如果str 符合 regex的正则表达式格式,返回true, 否则返回 false;
	 */
	private static boolean match(String regex, String str) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(str);
		return matcher.matches();
	}
	
	/**
	 * @param message          massage
	 * @param row              行
	 * @param columnName       单元格
	 * @param messageParameter 异常信息;
	 */
	private void addErrorLog(String message, Integer row, String columnName, Object... messageParameter) {
		Long index = null;
		if (row != null) {
			index = Long.valueOf(row);
		}
		UploadErrorLog errorLog = null;
		if (messageParameter != null && messageParameter.length > 0) {
			errorLog = new UploadErrorLog(index, null, columnName, String.format(message, messageParameter));
		} else {
			errorLog = new UploadErrorLog(index, null, columnName, message);
		}
		progress.getErrLogs().add(errorLog);
	}
}
