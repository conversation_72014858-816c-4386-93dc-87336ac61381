package com.glory.edc.generaledc;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.EdcEntry;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.EquipmentLine;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class GeneralEdcSection extends EntitySection {
	
	private static final Logger logger = Logger.getLogger(EntitySection.class);
	protected ToolItem itemDcop;
	protected ToolItem refersh;
//	protected Text txtEquipment;
	protected SearchField edcSetField;
	private Equipment eqp;
	private Lot lot;
	protected ADField eqpNameField;
	protected SearchField searchField;
	private static final String FIELD_ID_EQUIPMENT = "equipmentId";
	private static final String FIELD_DISPLAY_TYPE = "reftable";
	private static final String REFTABLE_ID = "EDCAbstractEdcSet";
	public GeneralEdcSection(ADTable table) {
		super(table);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		
		GridLayout gl = new GridLayout(2, true);
		gl.horizontalSpacing = 20;
		gl.marginWidth = 0;
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.CENTER;
		gd.horizontalAlignment = SWT.CENTER;
		gd.horizontalSpan = 1;
		
		Composite top = toolkit.createComposite(client);
		top.setLayout(gl);
		top.setLayoutData(new GridData(GridData.CENTER));
		
		Label lblEqupment = toolkit.createLabel(top, Message.getString("edc.generalEdc_equipment_id")+ "*");
		searchField = createSearchFieldByLines(top, toolkit);//new SearchField("", adTable, refTable, "", mStyle);
		lblEqupment.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		lblEqupment.setLayoutData(new GridData(100, 21));

		Label lblOperator = toolkit.createLabel(top, Message
				.getString("edc.generalEdc_data_set")+ "*");//参数集名称
		lblOperator.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		lblOperator.setLayoutData(new GridData(100, 21));
		
		edcSetField = createEdcSetField(top,toolkit);//toolkit.createText(top, "", SWT.BORDER);
//		txtEdcSet.setLayoutData(new GridData(200, 13));
//		txtEdcSet.addFocusListener(new FocusListener() {
//			public void focusGained(FocusEvent e) {
//			}
//
//			public void focusLost(FocusEvent e) {
//				Text tLotId = ((Text) e.widget);
//				tLotId.setText(tLotId.getText().toUpperCase());
//			}
//		});
	}
		
	private Equipment searchEquipment(Long objectRrn) {
		try {
			if (objectRrn != null) {
				Equipment eqp = new Equipment();
				eqp.setObjectRrn(objectRrn);
				ADManager entityManager = Framework.getService(ADManager.class);
				return (Equipment) entityManager.getEntity(eqp);
			}
		} catch (Exception e) {
			logger.error("LogEventSection searchLotEntity(): Equipment isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
	}

	protected void createSectionContent(Composite client) {

	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		section.setText(Message.getString("edc.general_collection_title"));
		createToolItemDcop(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void createToolItemRefresh(ToolBar tBar) {
		refersh = new ToolItem(tBar, SWT.PUSH);
		refersh.setText(Message.getString(ExceptionBundle.bundle.CommonRefresh()));
		refersh.setImage(SWTResourceCache.getImage("refresh"));
		refersh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				edcSetField.setValue(null);
				searchField.setValue(null);
			}
		});

	}
	
	protected void createToolItemDcop(ToolBar tBar) {
		itemDcop = new ToolItem(tBar, SWT.PUSH);
		itemDcop.setText(Message.getString("wip.dcop"));
		itemDcop.setImage(SWTResourceCache.getImage("dcop"));
		itemDcop.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				dcopAdapter(event);
			}
		});
	}
	
	protected void dcopAdapter(SelectionEvent event) {
		try {
			if (this.searchField.getText() == null || "".equals(this.searchField.getText().trim())) {
				UI.showError(Message.getString("edc.equipmentNumber_cannot_null"), Message.getString("edc.alert_message_title"));
				return;
			} else if (this.edcSetField.getText() == null || "".equals(this.edcSetField.getText().trim())) {
				UI.showError(Message.getString("edc.data_set_cannot_null"), Message.getString("edc.alert_message_title"));
				return;
			}
			RASManager rasManager = Framework.getService(RASManager.class);
			try {
				eqp = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), this.searchField.getText().toUpperCase());
				if (eqp == null) {
					UI.showError(Message.getString("edc.eqp_not_exist"), Message.getString("edc.alert_message_title"));
				}
			} catch (Exception e) {
				UI.showError(Message.getString("edc.eqp_not_exist"), Message.getString("edc.alert_message_title"));
				return;
			}

			AbstractEdcSet abstractEdcSet = (AbstractEdcSet) edcSetField.getData();
			AbstractEdcSet itemSet = null;
			BASManager basManager = Framework.getService(BASManager.class);
			if (abstractEdcSet instanceof EdcItemSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcItemSet.class, abstractEdcSet.getName());
			} else if (abstractEdcSet instanceof EdcTextSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcTextSet.class, abstractEdcSet.getName());
			} else if (abstractEdcSet instanceof EdcBinSet) {
				itemSet = basManager.getActiveVersionControl(Env.getOrgRrn(), EdcBinSet.class, abstractEdcSet.getName());
			}

			if (itemSet == null) {
				UI.showError(Message.getString("edc.data_set_cannot found"), Message.getString("edc.alert_message_title"));
			} else {
				EdcSetCurrent edcCurrent = new EdcSetCurrent(); 
				edcCurrent.setItemSetRrn(itemSet.getObjectRrn());
				lot = new Lot();
				lot.setEquipmentId(eqp.getEquipmentId());
				EdcEntry.open(EdcData.EDCFROM_GENERAL, edcCurrent, null, lot);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public SearchField createSearchFieldByLines(Composite top, FormToolkit toolkit) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			ADRefTable refTable = new ADRefTable();
			for (ADField adField : table.getFields()) {
				if (FIELD_ID_EQUIPMENT.equals(adField.getName()) && FIELD_DISPLAY_TYPE.equals(adField.getDisplayType())) {
					eqpNameField = adField;
				}
			}

			refTable.setObjectRrn(eqpNameField.getReftableRrn());
			refTable = (ADRefTable) entityManager.getEntity(refTable);
			if (Env.isUseLine()) {
				List<EquipmentLine> eLines = new ArrayList<EquipmentLine>();
				List<EquipmentLine> list = entityManager.getEntityList(Env.getOrgRrn(), EquipmentLine.class, Env.getMaxResult(),
						" lineId " + Env.getLineClause(), "");
				eLines.addAll(list);
				String condition = "";
				for (EquipmentLine el : eLines) {
					condition = condition + " '" + el.getEquipmentRrn() + "',";
				}
				if (condition.length() > 0) {
					condition = condition.substring(0, condition.length() - 1);
					condition = " objectRrn in(" + condition + ")";
					refTable.setWhereClause(condition);
				}
			}

			ADTable adTable = entityManager.getADTable(refTable.getTableRrn());
			int mStyle = SWT.BORDER;
			searchField = new SearchField("", adTable, refTable, "", mStyle);
			searchField.setADManager(getADManger());
			searchField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					if (searchField.getValue() != null && ((String) searchField.getValue()).trim().length() > 0) {
						setAdObject(searchEquipment(Long.parseLong((String) searchField.getValue())));
						refresh();
					}
				}
			});
			searchField.createContent(top, toolkit);
		} catch (Exception e1) {
		}
		return searchField;
	}
	
	public SearchField createEdcSetField(Composite top , FormToolkit toolkit){
		try{
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable =adManager.getADTable(Env.getOrgRrn(), REFTABLE_ID);
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("name");
			adRefTable.setTextField("name");
			adRefTable.setWhereClause(" status='Active' ");
			edcSetField = new SearchField("", adTable, adRefTable, 	"", SWT.BORDER);
			edcSetField.setADManager(adManager);
			edcSetField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					if (edcSetField.getValue() != null && ((String) edcSetField.getValue()).trim().length() > 0) {
						refresh();
					}
				}
			});
	
		}catch(Exception e){
			
		}
		edcSetField.createContent(top, toolkit);
		return edcSetField;
	}
}
