package com.glory.edc.generaledc;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;

import com.glory.edc.collection.EdcDialog;
import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcAQLSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class GeneralEdcDialog extends EdcDialog  {

	public GeneralEdcDialog(Shell parentShell) {
		super(parentShell);
	}

	public GeneralEdcDialog(Shell parent, AbstractEdcSet edcSet, EdcEvent event) {
		this(parent);
		if (edcSet instanceof EdcItemSet){
			this.edcSet = (EdcItemSet)edcSet;
		} else if(edcSet instanceof EdcBinSet){
			this.edcSet = (EdcBinSet)edcSet;
		} else if(edcSet instanceof EdcAQLSet){
            this.edcSet = (EdcAQLSet)edcSet;
        } else if(edcSet instanceof EdcTextSet){
            this.edcSet = (EdcTextSet)edcSet;
        }
		this.event = event;
		this.lot = event.getLot();
	}
	
	@Override
	protected void setInitData() {	
	}
	
	@Override
	protected void createDialogForm(Composite com) {
		edcForm = new GeneralEdcForm(com, edcCurrent, lot, edcSet, null, toolkit);
		edcForm.createForm();
	}
	
	@Override
	public void saveData(List<EdcData> datas) throws Exception {
		if (!edcForm.validate()) {
			return;
		}
		
		//检查强制输入
		if (!checkMandatory(datas)) {
			return;
		}
		
		edcCurrent = new EdcSetCurrent();
		edcCurrent.setItemSetRrn(edcSet.getObjectRrn());
		if (edcSet instanceof EdcItemSet){
			for (EdcData data : datas) {
				data.setMeasureEqp(lot.getEquipmentId());
				data.setLineId(lot.getLineId());
				data.setTeamId(Env.getTeam());
				data.setOperator(Env.getUserName());
				data.setPartName(lot.getPartName());
				data.setPartVersion(lot.getPartVersion());
			}
		} else if(edcSet instanceof EdcBinSet){
			for (EdcData data : datas) {
				data.setMeasureEqp(lot.getEquipmentId());
				data.setLineId(lot.getLineId());
				data.setTeamId(Env.getTeam());
				data.setOperator(Env.getUserName());
				data.setPartName(lot.getPartName());
				data.setPartVersion(lot.getPartVersion());
			}
		} else if(edcSet instanceof EdcTextSet){
			for (EdcData data : datas) {
				data.setMeasureEqp(lot.getEquipmentId());
				data.setLineId(lot.getLineId());
				data.setTeamId(Env.getTeam());
				data.setOperator(Env.getUserName());
				data.setPartName(lot.getPartName());
				data.setPartVersion(lot.getPartVersion());
			}
		}
		LotManager lotManager = Framework.getService(LotManager.class);
		dcResult = lotManager.edcGeneralData(datas, 
				EdcData.EDCFROM_GENERAL, false, Env.getSessionContext());
	}
	
	protected void createToolBar(Composite parent) {
		ToolBar tBar = new ToolBar(parent, SWT.HORIZONTAL | SWT.FLAT);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClear(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClose(tBar);
		section.setTextClient(tBar);
	}

}
