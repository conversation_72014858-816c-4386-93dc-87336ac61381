package com.glory.edc.table;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.edc.collection.EdcDataItem;
import com.glory.edc.collection.EdcDataTableComposite;
import com.glory.edc.collection.JudgeComposite;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

public class TableEdcDataTableComposite extends EdcDataTableComposite {

	protected TableEdcFixEditorTableManager tableManager;
	protected int dataSize;
	protected EdcItemSetLine edcSetLine;
	
	public TableEdcDataTableComposite(Composite parent, Lot lot, EdcItemSetLine edcSetLine) {
		super(parent, lot, edcSetLine);
		this.edcSetLine = edcSetLine;
		this.dataSize = getDataSize();
	}

	public void createForm() {
		GridLayout layout = new GridLayout(1, true);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		
		setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createUpperComponent(this);
		createTableComponent(this);
		
		if (edcSetLine.getIsJudgeByManual()) {
			judgeComposite = new JudgeComposite(this, SWT.BORDER);
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			judgeComposite.setLayoutData(gd);
			judgeComposite.createForm();
		}
	}
	
	public void createTableComponent(Composite composite) {
		try {
			tableManager = new TableEdcFixEditorTableManager(getADTable(), edcSetLine, dataSize);
			tableManager.newViewer(composite);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 查询、或者创建动态表
	 * @return
	 */
	private ADTable getADTable() throws Exception {
		String tableName = edcSetLine.getAdTableName();
		ADTable adTable = null;
		if (!StringUtil.isEmpty(tableName)) {
			try {
				ADManager adManager = Framework.getService(ADManager.class);
				adTable = adManager.getADTable(Env.getOrgRrn(), tableName);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		if (adTable == null) {
			// 创建动态表
			adTable = new ADTable();
			adTable.setName("adTable");
			adTable.setModelClass(TableDataItem.class.getName());
			adTable.setModelName(TableDataItem.class.getSimpleName());
			
			// 创建数据Field
			List<ADField> adFields = Lists.newArrayList();
			
			long seq = 1;
			ADField nameField = new ADField();
			nameField.setName("name");
			nameField.setDisplayType(FieldType.TEXT);
			nameField.setDataType(DataType.STRING);
			nameField.setSeqNo(seq++);
			nameField.setIsDisplay(true);
			nameField.setIsMain(true);
			nameField.setLabel(Message.getString("edc.collection_table_name"));
			nameField.setLabel_zh(Message.getString("edc.collection_table_name"));
			adFields.add(nameField);
			
//			ADField descField = new ADField();
//			descField.setName("description");
//			descField.setDisplayType(FieldType.TEXT);
//			descField.setDataType(DataType.STRING);
//			descField.setSeqNo(seq++);
//			descField.setIsDisplay(true);
//			descField.setIsMain(true);
//			descField.setLabel(Message.getString("edc.collection_table_desc"));
//			descField.setLabel_zh(Message.getString("edc.collection_table_desc"));
//			adFields.add(descField);
			
			for (int i = 0; i < dataSize; i++) {
				ADField adField = new ADField();
				adField.setName(TableDataItem.DATA_PREFIX + i);
				adField.setDisplayType(FieldType.TEXT);
				adField.setDataType(DataType.STRING);
				adField.setSeqNo(seq++);
				adField.setIsEditable(true);
				adField.setIsDisplay(true);
				adField.setIsMain(true);
				
				String label = getColumnLabelByIndex(i);
				if (label != null) {
					adField.setLabel(label);
					adField.setLabel_zh(label);
				} else {
					adField.setLabel(Message.getString("edc.collection_table_label") + (i + 1));
					adField.setLabel_zh(Message.getString("edc.collection_table_label") + (i + 1));
				}
				adFields.add(adField);
			}
			
			// 创建
			if (edcSetLine.getIsJudgeByManual()) {
				ADField judeField = new ADField();
				judeField.setName("flag");
				judeField.setDisplayType(FieldType.BOOLEAN);
				judeField.setSeqNo(seq++);
				judeField.setIsEditable(true);
				judeField.setIsDisplay(true);
				judeField.setIsMain(true);
				judeField.setLabel("OK/NG");
				judeField.setLabel_zh("OK/NG");
				adFields.add(judeField);
			}
			
			// 创建平均值Field
			ADField avgField = new ADField();
			avgField.setName("reserved1");
			avgField.setDisplayType("formula");
			avgField.setDataType(DataType.DOUBLE);
			avgField.setSeqNo(seq++);
			avgField.setIsDisplay(true);
			avgField.setIsMain(true);
			avgField.setLabel(Message.getString("edc.collection_table_avg"));
			avgField.setLabel_zh(Message.getString("edc.collection_table_avg"));
			avgField.setReferenceRule(getAvgFormula());
			adFields.add(avgField);
			
			// 创建求和Field
			ADField sumField = new ADField();
			sumField.setName("reserved2");
			sumField.setDisplayType("formula");
			sumField.setDataType(DataType.DOUBLE);
			sumField.setSeqNo(seq++);
			sumField.setIsDisplay(true);
			sumField.setIsMain(true);
			sumField.setLabel(Message.getString("edc.collection_table_sum"));
			sumField.setLabel_zh(Message.getString("edc.collection_table_sum"));
			sumField.setReferenceRule(getSumFormula());
			adFields.add(sumField);
			
			adTable.setFields(adFields);
		}
		
		return adTable;
	}
	/**
	 * 获取求和公式
	 * @return
	 */
	private String getSumFormula() {
		int num = Integer.valueOf(dataSize);
		return "=SUM(B$ROW:" + excelColIndexToStr(num) + "$ROW)";
	}
	
	/**
	 * 获取平均值公式
	 * @return
	 */
	private String getAvgFormula() {
		int num = Integer.valueOf(dataSize);
		return "=AVERAGE(B$ROW:" + excelColIndexToStr(num) + "$ROW)";
	}
	
	/**
	 * 数字转换为Excel列名，从0开始
	 * @param columnIndex
	 * @return
	 */
	private String excelColIndexToStr(int columnIndex) {
		if (columnIndex == 0) {
			return null;
		}
		String columnStr = "";
		do {
			if (columnStr.length() > 0) {
				columnIndex--;
			}
			columnStr = ((char) (columnIndex % 26 + (int) 'A')) + columnStr;
			columnIndex = (int) ((columnIndex - columnIndex % 26) / 26);
		} while (columnIndex > 0);
		return columnStr;
	}
	
	/**
	 * 根据索引获取列名
	 * @param i
	 * @return
	 */
	private String getColumnLabelByIndex(int i) {
		if (EdcItem.SAMPLETYPE_ITEM.equals(edcSetLine.getSampleType())) {
			return null;
		}
		
		boolean isComponnet = isComponentUnitType() && !EdcItem.SAMPLETYPE_ITEM.equals(edcSetLine.getSampleType());
		String[] itemDescs = EdcDataTableComposite.createIds(edcSetLine, selectedComponentIds, isComponnet, itemNumber);
		
		if (itemDescs == null || itemDescs.length <= i) {
			return null;
		}
		
		String label = itemDescs[i];
		
		if (label.indexOf("-") > 0) {
			label = label.substring(label.indexOf("-") + 1);
		}
		return label;
	}
	
	/**
	 * TableDataItem List转换为EdcData
	 */
	public EdcData getEdcData() {
		List<TableDataItem> tableItems =(List<TableDataItem>)tableManager.getInput();
		if(tableItems == null || tableItems.size() == 0){
			return null;
		}
		List<EdcDataItem> dataItems = new ArrayList<EdcDataItem>();
		for (TableDataItem tableItem : tableItems) {
			dataItems.addAll(TableDataItem.getDataItems(tableItem, dataSize));
		}
		return super.getEdcData(dataItems);
	}
	
	/**
	 * EdcDataItem List转换为TableDataItem List，并在表格中显示
	 */
	public void setEdcDatas() {
		List<EdcDataItem> dataItems = generateEdcDataItems();
		List<TableDataItem> tableItems = TableDataItem.getTableItems(dataItems, dataSize);
		tableManager.setInput(tableItems);
	}

	/**
	 * 获取数据长度，决定一行有几个输入栏位
	 * @return
	 */
	public int getDataSize() {
		if (EdcItem.DATATYPE_VARIABLE.equals(edcSetLine.getDataType())) {
			if (EdcItem.SAMPLETYPE_ITEM.equals(edcSetLine.getSampleType())) {
				return 1;
			}
			return edcSetLine.getSubgroupSize().intValue() / edcSetLine.getItem().intValue();
		}
		return 1;
	}
	
}
