package com.glory.edc.table;

import java.util.HashSet;
import java.util.Set;

import org.eclipse.nebula.widgets.nattable.painter.cell.TextPainter;

import com.glory.edc.collection.SpecTextPainter;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;

public class TableEdcFixEditorTableManager extends FixEditorTableManager {

	protected EdcItemSetLine edcSetLine;
	protected int dataSize;

	public TableEdcFixEditorTableManager(ADTable adTable, EdcItemSetLine edcSetLine, int dataSize) {
		super(adTable);
		this.edcSetLine = edcSetLine;
		this.dataSize = dataSize;
		resetADTableByData();
		this.setFormulaFlag(true);
	}
	
	@Override
	public TextPainter createTextPaint(String fieldName) {
		if (fieldName.startsWith(TableDataItem.DATA_PREFIX)) {
			return new SpecTextPainter(edcSetLine.getUsl(), edcSetLine.getLsl());
		}
		return super.createTextPaint(fieldName);
	}
	

	
	public Set<String> getDataFileds(int size) {
		Set<String> datas = new HashSet<String>();
		for (int i = 0; i < size; i++) {
			datas.add(TableDataItem.DATA_PREFIX + i);
		}
		return datas;
	}
	
	/**
	 * 重置ADTable,根据采样计划确定显示栏位
	 */
	public void resetADTableByData() {
		ADTable adTable = this.getADTable();
		Set<String> datas = getDataFileds(dataSize);
		
		for (ADField adField : adTable.getFields()) {
			if (adField.getName().startsWith(TableDataItem.DATA_PREFIX) 
					&& !datas.contains(adField.getName())) {
				adField.setIsDisplay(false);
			}
		}
	}
	
	
}
