package com.glory.edc.util;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.custom.XMultiCombo;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;

public class EDCUtil {

	public static Control getEdcEquipmentWidget(long orgRrn, Long capability, String parentEquipmentId, Composite parent) {
		return getEdcEquipmentWidget(orgRrn, capability, false, parentEquipmentId, parent);
	}
	
	public static Control getEdcEquipmentWidget(long orgRrn, Long capability, boolean isMulti, String parentEquipmentId, Composite parent) {
		try {
			FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());

			if (capability == null) {
				Text text = toolkit.createText(parent, "", SWT.BORDER);
				return text;
			} else {
				//指定了Capability,需要选择设备
				RASManager rasManager = Framework.getService(RASManager.class);
				List<Equipment> availEqps = new ArrayList<Equipment>();
				List<Equipment> capaEqps = rasManager.getEquipmentsByCapa(capability);
				if (capaEqps.size() > 0) {
					List<Equipment> subEqps = null;
					if (parentEquipmentId != null) {
						subEqps = rasManager.getSubEquipments(Env.getOrgRrn(), parentEquipmentId);
					}
	
					if (subEqps == null || subEqps.size() == 0) {
						//没有子设备则返回所有Capa设备
						availEqps.addAll(capaEqps);
					} else {
						for (Equipment capaEqp : capaEqps) {
							for (Equipment subEqp : subEqps) {
								if (capaEqp.getEquipmentId().equals(subEqp.getEquipmentId())) {
									availEqps.add(capaEqp);
								}
							}
						}
					}
				}
				//使用下拉框形式
				ADManager adManager = Framework.getService(ADManager.class);
				ADTable adTable  = adManager.getADTable(Env.getOrgRrn(), "RASEquipment");  
				if (isMulti) {
					ListTableManager tableManager = new ListTableManager(adTable, true);
					tableManager.setInput(availEqps);
					XMultiCombo combo = new XMultiCombo(parent, tableManager, "equipmentId", "equipmentId", SWT.BORDER | SWT.READ_ONLY, false);
					toolkit.adapt(combo);
			        toolkit.paintBordersFor(combo);
			        return combo;
				} else {
					ListTableManager tableManager = new ListTableManager(adTable);
					tableManager.setInput(availEqps);
					XCombo combo = new XCombo(parent, tableManager, "equipmentId", "equipmentId", SWT.BORDER | SWT.READ_ONLY, false);
					toolkit.adapt(combo);
			        toolkit.paintBordersFor(combo);
			        return combo;
				}
			}
		} catch(Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
}
