package com.glory.edc.custom;

import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormText;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.edc.collection.EdcDataItem;
import com.glory.edc.collection.JudgeComposite;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.custom.richtext.RichTextViewer;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.editor.FixTextCellEditor;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.google.common.collect.Maps;

/**
 * EDC数据收集控件
 * 
 * textViewer文本设置
 * 参考HTML风格或com.glory.common.richtext下readme文档
 */
public class EdcCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(EdcCustomComposite.class);

	/**
	 * Title显示方式：
	 * Text:采用RichTextViewer方式显示
	 * Entity:采用EntityForm方式显示
	 */
	public static final String TITLE_TYPE_TEXT = "Text";
	public static final String TITLE_TYPE_ENTITY = "Entity";

	public static final String ATTRIBUTE_TITLE_TYPE = "TitleType";
	public static final String ATTRIBUTE_TITLE_TABLE_NAME = "EntityTable";
	
	public static final String ATTRIBUTE_EDC_TABLE_NAME = "EdcTable";
	public static final String ATTRIBUTE_IS_JUDGE = "IsJudge";

	public static final String ATTRIBUTE_TEXT_HEIGTH_HINT = "textHightHint";

	public static final String EVENT_FOCUS = "focus";
	public static final String EVENT_DATARECEIVE = "dataReceive";

	protected String titleType;
	protected String entityTable;
	protected String edcTable;

	protected boolean isJudge = false;
	
	protected int textHeightHint = -1;

	protected RichTextViewer textViewer;
	protected EdcCustomTableManager tableManager;
	protected JudgeComposite judgeComposite;

	protected EventHandler focusHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	if (tableManager != null) {
        		int columnPosition = tableManager.getColumnPosition(EdcDataItem.FILED_VALUE);
        		if (columnPosition != -1) {
        			tableManager.setFocus(columnPosition, 0);
        		}
    		}
        }
	};
	
	/**
	 * 自动接收外部传递的数据
	 */
	protected EventHandler receiveDataHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	Object obj = event.getProperty(GlcEvent.PROPERTY_DATA);
        	if (obj != null && !StringUtil.isEmpty((String)obj) && tableManager != null) {
        		//数据间用“;”分隔
        		String[] datas = ((String)data).split(";");
        		FixTextCellEditor oldCellEditor = null;
        		for (String data : datas) {
	        		FixTextCellEditor cellEditor = (FixTextCellEditor)tableManager.getNatTable().getActiveCellEditor();
	        		cellEditor.setEditorValue(data);
	        		if (cellEditor != null && !cellEditor.equals(oldCellEditor)) {
	        			tableManager.carriageReturn(cellEditor);
	        			oldCellEditor = cellEditor;
	        		} else {
	        			//如果为空,或者已经到了最后一个输入栏位,则不再处理
	        			break;
	        		}
        		}
    		}
        }
	};
		
	@Override
	public void initSubscribeEvent() {
		subscribeEvent(null, EVENT_FOCUS, focusHandler);
		subscribeEvent(null, EVENT_DATARECEIVE, receiveDataHandler);
	}
	
	@Override
	public void preDestory() {
		super.preDestory();
		unsubscribeEvent(focusHandler);
		unsubscribeEvent(receiveDataHandler);
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite container = toolkit.createComposite(parent, SWT.NONE);
		container = CustomCompsite.configureBody(container);

		if (!StringUtil.isEmpty(titleType)) {
			createTitleComposite(toolkit, container);
		}
		createTableComponent(toolkit, container);
		
		if (isJudge) {
			judgeComposite = new JudgeComposite(parent, SWT.BORDER);
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			judgeComposite.setLayoutData(gd);
			judgeComposite.createForm();
		}
		
		return container;
	}
	
	protected void createTitleComposite(FormToolkit toolkit, Composite parent) {
		try {
			if (TITLE_TYPE_TEXT.equals(titleType)) {
				textViewer = new RichTextViewer(parent, SWT.BORDER | SWT.WRAP);
				textViewer.setWordSplitRegex("\\s|\\-");//wrap after whitespace characters and delimiter

		        GridData gd = new GridData();
				gd.horizontalAlignment = SWT.FILL;
		        gd.grabExcessHorizontalSpace = true;
		        if (textHeightHint != -1) {
			        gd.heightHint = textHeightHint;
		        }
		        textViewer.setLayoutData(gd);
			} else {
				ADManager adManager = Framework.getService(ADManager.class);
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), entityTable); 
				EntityForm entityForm = new EntityForm(parent, SWT.NONE, adTable, null, false);
				entityForm.createForm();
			}
		} catch (Exception e) {
			logger.error(e);
		}
	}
	
	public void createTableComponent(FormToolkit toolkit, Composite parent) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), edcTable); 
			tableManager = new EdcCustomTableManager(adTable);
			tableManager.newViewer(parent);
		} catch (Exception e) {
			logger.error(e);
		}
	}

	public void setSpec(Double usl, Double lsl) {
		tableManager.setUsl(usl);
		tableManager.setLsl(lsl);
	}
	
	@Override
	public void refresh() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void setValue(Object value) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Object getValue() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		for (ADFormAttribute fieldAttribute : attributes) {
			switch (fieldAttribute.getAttributeName()) {
			case ATTRIBUTE_TEXT_HEIGTH_HINT:
				textHeightHint = fieldAttribute.getIntValue() == null ? textHeightHint : fieldAttribute.getIntValue();
				break;
			}
		}
	}
	
	public RichTextViewer getTextViewer() {
		return textViewer;
	}

	public void setTextViewer(RichTextViewer textViewer) {
		this.textViewer = textViewer;
	}

	public EdcCustomTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(EdcCustomTableManager tableManager) {
		this.tableManager = tableManager;
	}

	public JudgeComposite getJudgeComposite() {
		return judgeComposite;
	}

	public void setJudgeComposite(JudgeComposite judgeComposite) {
		this.judgeComposite = judgeComposite;
	}

}
