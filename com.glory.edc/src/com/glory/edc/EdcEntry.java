package com.glory.edc;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.dialogs.Dialog;

import com.glory.edc.client.EDCManager;
import com.glory.edc.extensionpoints.EdcActionExtensionPoint;
import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.extensionpoints.IEdcAction;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcSpec;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.PrdSpecValue;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class EdcEntry {
	
	public static int open(String edcFrom, EdcSetCurrent edcCurrent, 
			AbstractEdcSet edcSet, Lot lot) throws Exception {
		return EdcEntry.open(edcFrom, edcCurrent, edcSet, lot, null);
	}
	
	public static int open(String edcFrom, EdcSetCurrent edcCurrent, 
			AbstractEdcSet edcSet, Lot lot, EdcEvent event) throws Exception {
		if (edcSet == null && edcCurrent != null) {
			if (EdcData.EDCFROM_GENERAL.equals(edcFrom)) {
				EDCManager edcManager = Framework.getService(EDCManager.class);
				edcSet = edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), null, null);
			} else {
				LotManager lotManager = Framework.getService(LotManager.class);
				edcSet = lotManager.getLotEdcSet(lot, edcCurrent, true);
			}			
			
//			EDCManager edcManager = Framework.getService(EDCManager.class);
//			edcSet = edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), null, null);
//
//			Map<String, EdcSpec> specMap = null;
//			Map<String, Object> paramMap = null;
//			if (edcSet instanceof EdcItemSet) {
//				EdcItemSet itemSet = (EdcItemSet)edcSet;
//				for (EdcItemSetLine line : itemSet.getItemSetLines()) {
//					if (line.getIsUsePartSpec()) {
//						specMap = new HashMap<String, EdcSpec>();
//					} else if (line.getIsUseParameter()) { 
//						paramMap = new HashMap<String, Object>();
//					} 
//				}
//			} else if (edcSet instanceof EdcBinSet) {
//				EdcBinSet binSet = (EdcBinSet)edcSet;
//				binSet.setSpec(paramMap);
//				for (EdcBinSetLine line : binSet.getBinSetLines()) {
//					if (line.getIsUsePartSpec()) {
//						specMap = new HashMap<String, EdcSpec>();
//					} else if (line.getIsUseParameter()) { 
//						paramMap = new HashMap<String, Object>();
//					}
//				}
//			}
//			PrdManager prdManager = Framework.getService(PrdManager.class);
//			if (specMap != null) {
//				List<PrdSpecValue> specValues = prdManager.getSpecValues(Env.getOrgRrn(), null, lot.getStepName(), lot);
//				for (PrdSpecValue specValue : specValues) {
//					EdcSpec edcSepc = new EdcSpec();
//					edcSepc.setName(specValue.getName());
//					edcSepc.setUslString(specValue.getMaxValue());
//					edcSepc.setSlString(specValue.getDefaultValue());
//					edcSepc.setLslString(specValue.getMinValue());
//					specMap.put(specValue.getName(), edcSepc);
//				}
//			}
//			if (paramMap != null) {
//				if (lot.getProcessInstanceRrn() != null) {
//					paramMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
//				} 
//			}
//			if ((specMap != null && !specMap.isEmpty()) 
//					|| (paramMap != null && !paramMap.isEmpty())) {
//				edcSet = edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), specMap, paramMap);
//			}
		}
		if (edcSet == null) {
			throw new ClientException("edc.dcop_edcset_not_found");
		}
		
		ComponentUnit componentUnit = null;
		if (!StringUtil.isEmpty(edcCurrent.getComponentUnitId())) {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			componentUnit = componentManager.getComponentByComponentId(Env.getOrgRrn(), edcCurrent.getComponentUnitId());
		}
		
		String edcType = edcSet.getEdcType();
		if (!StringUtil.isEmpty(edcSet.getCategory1())) {
			edcType = edcType + "-" + edcSet.getCategory1();
		}
		IEdcAction action = EdcActionExtensionPoint.getEdcAction(edcType);
		if (action == null) {
			//如果edcType + category1 获取的Action为空,再会按edcType去查一次Action
			action = EdcActionExtensionPoint.getEdcAction(edcSet.getEdcType());
		}
		if (action != null) {
			if (event == null) {
				event = new EdcEvent();
			}
			event.setEdcFrom(edcFrom);
			event.setEdcCurrent(edcCurrent);
			event.setEdcSet(edcSet);
			event.setLot(lot);
			event.setComponentUnit(componentUnit);
			return Integer.parseInt(String.valueOf(action.doAction(event)));
		}
		return Dialog.CANCEL;
	}
	
	public static int open(String edcFrom, EdcSetCurrent edcCurrent, 
			AbstractEdcSet edcSet, MLot lot) throws Exception {
		if (edcSet == null && edcCurrent != null) {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			edcSet = edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), null, null);
		}
		
		if (edcSet == null) {
			throw new ClientException("edc.dcop_edcset_not_found");
		}
		
		String edcType = edcSet.getEdcType();
		if (!StringUtil.isEmpty(edcSet.getCategory1())) {
			edcType = edcType + "-" + edcSet.getCategory1();
		}
		IEdcAction action = EdcActionExtensionPoint.getEdcAction(edcType);
		if (action == null) {
			//如果edcType + category1 获取的Action为空,再会按edcType去查一次Action
			action = EdcActionExtensionPoint.getEdcAction(edcSet.getEdcType());
		}
		if (action != null) {
			EdcEvent event = new EdcEvent();
			event.setEdcFrom(edcFrom);
			event.setEdcCurrent(edcCurrent);
			event.setEdcSet(edcSet);
			event.setMLot(lot);
			
			return Integer.parseInt(String.valueOf(action.doAction(event)));
		}
		return Dialog.CANCEL;
	}
}
