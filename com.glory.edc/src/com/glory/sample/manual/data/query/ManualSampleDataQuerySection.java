package com.glory.sample.manual.data.query;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.edc.collection.query.adapter.EdcGeneralDataAdapter;
import com.glory.edc.collection.query.adapter.EdcGeneralDataTableManager;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.sampling.ManualSamplingData;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class ManualSampleDataQuerySection extends QueryEntityListSection implements IRefresh {
	
	private EdcGeneralDataTableManager lineTableManager;
	private ADTable adTable;

	public ManualSampleDataQuerySection(ListTableManager tableManager) {
		super(tableManager);
	}
	
	protected void createNewViewer(Composite client, final IManagedForm form){
		tableManager.setIndexFlag(true);
		tableManager.newViewer(client);
		natTable = tableManager.getNatTable();
		
		tableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection ss = (StructuredSelection)event.getSelection();
				if (ss != null && ss.getFirstElement() != null) {
					ManualSamplingData samplingData = (ManualSamplingData) ss.getFirstElement();
					selectChangedAdapter(samplingData);
					tableManager.getTableManager().setSelectedObject(samplingData);
				} else {
					tableManager.getTableManager().setSelectedObject(null);
				}
			}
		});
		tableManager.addDoubleClickListener(new AbstractMouseSelectionAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				doubleClick();
			}
		});
		
		FormToolkit toolkit = form.getToolkit();
		toolkit.createLabel(client, "Data:");
		
		adTable = getADManger().getADTable(Env.getOrgRrn(), "EDCDataQuery");
		
		lineTableManager = new EdcGeneralDataTableManager(adTable);
		lineTableManager.setIndexFlag(true);
		lineTableManager.newViewer(client);
	}
	
	protected void selectChangedAdapter(ManualSamplingData samplingData) {
		try {
			if (samplingData == null) {
				if (lineTableManager != null) {
					lineTableManager.getInput().clear();
				}
			} else {
				ADTable cloneTable = (ADTable) adTable.clone();
				cloneTable.setObjectRrn(adTable.getObjectRrn());
				List<ADField> cloneFields = new ArrayList<ADField>();
				for (ADField adField : adTable.getFields()) {
					cloneFields.add(adField);
				}
				
				String condition = "hisSeq ='" + samplingData.getEdcDataHisSeq() + "'";
				List<EdcData> datas = adManager.getEntityList(
						Env.getOrgRrn(), EdcData.class, Env.getMaxResult(), condition, null);
				int count = 1;
				if (datas.size() == 0 || datas == null) {
					count = 0;
				} else {
					// 取得显示列数
					List<EdcData> varDatas = datas.stream().filter(
							d -> EdcItem.DATATYPE_VARIABLE.equals(d.getDataType())).collect(Collectors.toList());
					List<EdcData> attDatas = datas.stream().filter(
							d -> EdcItem.DATATYPE_ATTRIBUTE.equals(d.getDataType())).collect(Collectors.toList());
					if (CollectionUtils.isNotEmpty(varDatas)) {
						EdcData data = varDatas.stream().collect(
								Collectors.maxBy(Comparator.comparingLong(EdcData::getSubgroupSize))).get();
						count = data.getSubgroupSize().intValue();
					}
					
					if (CollectionUtils.isNotEmpty(attDatas)) {
						count = attDatas.stream()
								.map(d -> d.getDcName().split(";").length)
								.collect(Collectors.maxBy(Comparator.comparingInt(d -> d))).get();
					}
					
					for (int i = 1; i <= count; i++) {
						ADField itemField = new ADField();
						itemField.setName(EdcGeneralDataAdapter.DATA_PREFIX + i);
						itemField.setIsDisplay(true);
						itemField.setIsMain(true);
						itemField.setDisplayLength(24L);
						itemField.setLabel(itemField.getName());
						itemField.setLabel_zh(itemField.getName());
						cloneFields.add(itemField);
					}
				}
				
				cloneTable.setFields(cloneFields);
				lineTableManager.setADTable(cloneTable);
				lineTableManager.setInput(datas);
				lineTableManager.refresh();
				statusChange(samplingData.getDocStatus());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void statusChange(String docStatus) {
		
	}
	
}
