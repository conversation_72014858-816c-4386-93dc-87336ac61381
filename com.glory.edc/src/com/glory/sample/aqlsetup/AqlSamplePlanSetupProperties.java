package com.glory.sample.aqlsetup;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.client.EDCManager;
import com.glory.edc.model.EdcAQLSet;
import com.glory.edc.model.sampling.AqlSamplingPlan;
import com.glory.edc.model.sampling.SamplingPlan;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.VersionControlProperties;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class AqlSamplePlanSetupProperties extends VersionControlProperties {

    public static final String KEY_FROZEN = "Frozen";
    public static final String KEY_ACTIVE = "Active";
    public static final String KEY_INACTIVE = "InActive";

    protected ToolItem itemCopyFrom;
    protected ToolItem itemFrozen;
    protected ToolItem itemActive;
    protected ToolItem itemInActive;

    public AqlSamplePlanSetupProperties() {
        super();
    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemNew(tBar);
        createToolItemSave(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemFrozen(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemActive(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemInActive(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemDelete(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    protected void createToolItemFrozen(ToolBar tBar) {
        itemFrozen = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "."
                + KEY_FROZEN);
        itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
        itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
        itemFrozen.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                frozenAdapter();
            }
        });
    }

    protected void createToolItemActive(ToolBar tBar) {
        itemActive = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "."
                + KEY_ACTIVE);
        itemActive.setImage(SWTResourceCache.getImage("active"));
        itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonActive()));
        itemActive.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                activeAdapter();
            }
        });
    }

    protected void createToolItemInActive(ToolBar tBar) {
        itemInActive = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "."
                + KEY_INACTIVE);
        itemInActive.setImage(SWTResourceCache.getImage("inactive"));
        itemInActive.setText(Message.getString(ExceptionBundle.bundle.CommonInActive()));
        itemInActive.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                inActiveAdapter();
            }
        });
    }

    protected void frozenAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    AqlSamplingPlan edcSamplingPlan = (AqlSamplingPlan) getAdObject();

                    EDCManager edcManager = Framework.getService(EDCManager.class);
                    if (VersionControl.STATUS_UNFROZNE.equalsIgnoreCase(edcSamplingPlan.getStatus())) {
                        edcSamplingPlan = (AqlSamplingPlan) edcManager.saveEdcSamplingPlan((SamplingPlan)edcSamplingPlan, VersionControl.STATUS_FROZNE,
                                Env.getSessionContext());
                        UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonFrozenSuccess()));
                    } else if (VersionControl.STATUS_ACTIVE.equalsIgnoreCase(edcSamplingPlan.getStatus())) {
                        edcSamplingPlan = (AqlSamplingPlan) edcManager.saveEdcSamplingPlan((SamplingPlan)edcSamplingPlan, VersionControl.STATUS_FROZNE,
                                Env.getSessionContext());
                        UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonFrozenSuccess()));
                    } else {
                        edcSamplingPlan = (AqlSamplingPlan) edcManager.saveEdcSamplingPlan((SamplingPlan)edcSamplingPlan, VersionControl.STATUS_UNFROZNE,
                                Env.getSessionContext());
                        UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonUnFrozenSuccess()));
                    }
                    ADManager adManager = Framework.getService(ADManager.class);
                    setAdObject(adManager.getEntity(edcSamplingPlan));
                    refresh();

                    getMasterParent().refreshUpdate(adManager.getEntity(edcSamplingPlan));
                }
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    protected void activeAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                AqlSamplingPlan edcSamplingPlan = (AqlSamplingPlan) getAdObject();

                EDCManager edcManager = Framework.getService(EDCManager.class);
                edcSamplingPlan = (AqlSamplingPlan) edcManager.saveEdcSamplingPlan((SamplingPlan)edcSamplingPlan, VersionControl.STATUS_ACTIVE,
                        Env.getSessionContext());

                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonActiveSuccess()));

                ADManager adManager = Framework.getService(ADManager.class);
                setAdObject(adManager.getEntity(edcSamplingPlan));
                refresh();

                getMasterParent().refreshUpdate(adManager.getEntity(edcSamplingPlan));
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    protected void inActiveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                AqlSamplingPlan edcSamplingPlan = (AqlSamplingPlan) getAdObject();

                EDCManager edcManager = Framework.getService(EDCManager.class);
                edcSamplingPlan = (AqlSamplingPlan) edcManager.saveEdcSamplingPlan((SamplingPlan)edcSamplingPlan, VersionControl.STATUS_INACTIVE,
                        Env.getSessionContext());

                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));

                ADManager adManager = Framework.getService(ADManager.class);
                setAdObject(adManager.getEntity(edcSamplingPlan));
                refresh();

                getMasterParent().refreshUpdate(adManager.getEntity(edcSamplingPlan));
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
    public void saveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                AqlSamplingPlan old = (AqlSamplingPlan) getAdObject();
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    for (IForm detailForm : getDetailForms()) {
                        PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
                                detailForm.getCopyProperties());
                    }
                    AqlSamplingPlan edcSamplingPlan = (AqlSamplingPlan) getAdObject();
                    ADManager adManager = Framework.getService(ADManager.class);
                    if (edcSamplingPlan.getVersion() == null) {
                        List<AqlSamplingPlan> edcSamplingPlans = adManager.getEntityList( Env.getOrgRrn(), AqlSamplingPlan.class, Integer.MAX_VALUE,
                                " name = '" + edcSamplingPlan.getName() + "'", "version desc");
                        if (edcSamplingPlans.size() > 0) {
                            boolean flag = UI.showConfirm(edcSamplingPlan.getName()
                                    + Message.getString("edc.bin_name_is_existed")
                                    + String.valueOf(edcSamplingPlans.get(0).getVersion() + 1));
                            if (!flag) {
                                return;
                            }
                        }
                    }
                    EDCManager edcManager = Framework.getService(EDCManager.class);
                    edcSamplingPlan = (AqlSamplingPlan) edcManager.saveEdcSamplingPlan((SamplingPlan)edcSamplingPlan, VersionControl.STATUS_UNFROZNE, 
                            Env.getSessionContext());
                    edcSamplingPlan = (AqlSamplingPlan) adManager.getEntity(edcSamplingPlan);
                    if (old.getObjectRrn() == null) {
                        getMasterParent().refreshAdd(edcSamplingPlan);
                    } else {
                        getMasterParent().refreshUpdate(edcSamplingPlan);
                    }
                    setAdObject(edcSamplingPlan);
                    UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// 弹出提示框
                    refresh();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    @Override
    public void deleteAdapter() {
        try {
            boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
            if (confirmDelete) {
                form.getMessageManager().setAutoUpdate(false);
                form.getMessageManager().removeAllMessages();
                if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
                    AqlSamplingPlan edcSamplingPlan = (AqlSamplingPlan) getAdObject();
                    //抽样计划是否被EdcAQLSet抽测使用，使用则无法删除
                    ADManager adManager = Framework.getService(ADManager.class);
                    List<EdcAQLSet> edcSamplingPlans = adManager.getEntityList(Env.getOrgRrn(), EdcAQLSet.class, Env.getMaxResult(), 
                            "samplePlanName = '" + edcSamplingPlan.getName() +"'", null);
                    if (edcSamplingPlans != null && edcSamplingPlans.size() > 0) {
                        UI.showError(Message.getString("edc.sample_plan_delete_error"));
                        return;
                    } else {
                        adManager.deleteEntity(edcSamplingPlan, Env.getSessionContext());
                        UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
                        setAdObject(createAdObject());
                        refresh();
                        getMasterParent().refresh();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    public void statusChanged(String newStatus) {
        buildTitle(newStatus);
        if (VersionControl.STATUS_UNFROZNE.equals(newStatus)) {
            itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
            itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
            itemFrozen.setEnabled(true);
            itemSave.setEnabled(true);
            itemDelete.setEnabled(true);
            itemActive.setEnabled(false);
            itemInActive.setEnabled(false);
            setEnable(true);
        } else if (VersionControl.STATUS_FROZNE.equals(newStatus)) {
            itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
            itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
            itemFrozen.setEnabled(true);
            itemSave.setEnabled(false);
            itemDelete.setEnabled(false);
            itemActive.setEnabled(true);
            itemInActive.setEnabled(false);
            setEnable(false);
        } else if (VersionControl.STATUS_ACTIVE.equals(newStatus)) {
            itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
            itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
            itemFrozen.setEnabled(false);
            itemSave.setEnabled(false);
            itemDelete.setEnabled(false);
            itemActive.setEnabled(false);
            itemInActive.setEnabled(true);
            setEnable(false);
        } else if (VersionControl.STATUS_INACTIVE.equals(newStatus)) {
            itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
            itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
            itemFrozen.setEnabled(true);
            itemSave.setEnabled(false);
            itemDelete.setEnabled(false);
            itemActive.setEnabled(true);
            itemInActive.setEnabled(false);
            setEnable(false);
        } else {
            itemFrozen.setEnabled(false);
            itemSave.setEnabled(true);
            itemDelete.setEnabled(true);
            itemActive.setEnabled(false);
            itemInActive.setEnabled(false);
            setEnable(true);
        }
    }   

    @Override
    public void dispose() {
        if (label != null && !label.isDisposed()) {
            label.dispose();
        }
        if (itemCopyFrom != null && !itemCopyFrom.isDisposed()) {
            itemCopyFrom.dispose();
        }
        if (itemFrozen != null && !itemFrozen.isDisposed()) {
            itemFrozen.dispose();
        }
        if (itemActive != null && !itemActive.isDisposed()) {
            itemActive.dispose();
        }
        super.dispose();
    }
}
